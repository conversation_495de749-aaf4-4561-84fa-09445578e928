<?xml version="1.0" encoding="UTF-8"?>
<settings xmlns="http://maven.apache.org/SETTINGS/1.0.0"
          xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
          xsi:schemaLocation="http://maven.apache.org/SETTINGS/1.0.0 http://maven.apache.org/xsd/settings-1.0.0.xsd">
    <!--本地仓库,该值表示构建系统本地仓库的路径,不同操作系统，这个路径不一样，根据需要修正-->
    <localRepository>${user.home}/.m2/repository</localRepository>

    <pluginGroups>

    </pluginGroups>

    <proxies>
    </proxies>
    <servers>
        <server>
            <id>CFPAMF</id>
            <!--每一个开发者的用户名和密码应该是不一样的，用于deploy的管理-->
            <username>cfpamfrdc</username>
            <password>cfpamf@l0gin#11</password>
        </server>
        <server>
            <id>public</id>
            <!--每一个开发者的用户名和密码应该是不一样的，用于包的下载-->
            <username>cfpamfrdc</username>
            <password>cfpamf@l0gin#11</password>
        </server>
        <server>
            <id>mycfpamf</id>
            <!--每一个开发者的用户名和密码应该是不一样的，用于包的下载-->
            <username>cfpamfrdc</username>
            <password>cfpamf@l0gin#11</password>
        </server>
        <server>
            <id>release</id>
            <!--每一个开发者的用户名和密码应该是不一样的，用于包的下载-->
            <username>cfpamfrdc</username>
            <password>cfpamf@l0gin#11</password>
        </server>
        <server>
            <id>snapshot</id>
            <!--每一个开发者的用户名和密码应该是不一样的，用于包的下载-->
            <username>cfpamfrdc</username>
            <password>cfpamf@l0gin#11</password>
        </server>
        <server>
            <id>central</id>
            <!--每一个开发者的用户名和密码应该是不一样的，用于包的下载-->
            <username>cfpamfrdc</username>
            <password>cfpamf@l0gin#11</password>
        </server>
    </servers>

    <!--为仓库列表配置的下载镜像列表-->
    <mirrors>
        <!--给定仓库的下载镜像,优先使用私服的-->
        <mirror>
            <id>alimaven</id>
            <mirrorOf>central</mirrorOf>
            <name>aliyun maven</name>
            <url>https://maven.aliyun.com/nexus/content/groups/public/</url>
        </mirror>
        <mirror>
            <id>public</id>
            <name>aliyun maven</name>
            <url>http://nexus.pub.cfpamf.com/repository/public/</url>
            <mirrorOf>public</mirrorOf>
        </mirror>
    </mirrors>
    <profiles>
        <profile>
            <id>mycfpamf</id>
            <repositories>
                <repository>
                    <id>release</id>
                    <name>release</name>
                    <snapshots>
                        <enabled>false</enabled>
                    </snapshots>
                    <url>http://nexus.pub.cfpamf.com/repository/releases/</url>
                </repository>
                <repository>
                    <!-- 总是拉取最新快照 -->
                    <id>snapshot</id>
                    <name>snapshot</name>
                    <snapshots>
                        <enabled>true</enabled>
                        <updatePolicy>always</updatePolicy>
                    </snapshots>
                    <url>http://nexus.pub.cfpamf.com/repository/snapshots/</url>
                </repository>
            </repositories>
        </profile>
    </profiles>

    <!-- 确认使用的Profiles -->
    <activeProfiles>
        <activeProfile>mycfpamf</activeProfile>
    </activeProfiles>
</settings>
