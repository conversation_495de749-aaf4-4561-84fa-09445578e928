
package groovy.activity

import com.google.common.collect.Lists
import com.google.common.collect.Maps


List<Map<String, Object>> execute(String params) {
    Map<String,Long> sumMap = summaryAchievement();
    log.warn("统计数据查询：{}",sumMap)
    String sql = """ 
        	select 	 
						sum(premium) as premium,
						orderId,
						policyNo,
						idCard,
						planId,
						riskId,
						termNum,
						userId
				from (
            select
                        t.conversion_amount as premium,
                        t.order_id as orderId,
                        t.policy_no as policyNo, 
                        t.insured_id_number as idCard,
                        t.plan_id as planId,
                        t.risk_id as riskId,
                        t.term_num as termNum,
                        t.policy_status as policyStatus,
                        t.commission_user_id as userId
            from sm_commission_detail t
            left join sm_plan c on t.plan_id=c.id
            where c.productId in (454,434)
            and t.account_time > str_to_date('2022-05-01', '%Y-%m-%d')
            and t.account_time < str_to_date('2022-07-01', '%Y-%m-%d') 
            and SUBSTRING_INDEX(order_id,'_',1) 
            in (select  `fhOrderId` 
                from sm_order b 
                left join sm_commission_detail c on b.fhOrderId=c.order_id
                where b.productId in (454,434)
                and b.`payStatus` = 2
                and case when b.paymentTime is null then c.create_time else b.paymentTime end > str_to_date('2022-05-01', '%Y-%m-%d')
                and case when b.paymentTime is null then c.create_time else b.paymentTime end < str_to_date('2022-07-01', '%Y-%m-%d')
                and INSTR (b.fhOrderId ,'_') =0
            )
        ) t 
				group by orderId, policyNo, idCard, planId, riskId,  termNum, t.userId  
				having premium >0 
            """
    List<Map<String,Object>> itemMap = msgStatService.selectMaps(sql)
    log.warn("数据查询：{}",itemMap)
    List<Map<String, Object>> result = Lists.newArrayList()
    for (Map<String, Object> map : itemMap) {
        Map<String,Object> resMap =new HashMap()
        String orderId  =  (String)map.get("orderId")
        String policyNo = (String)map.get("policyNo")
        String idCard   =   (String)map.get("idCard")
        String planId   =   (String)map.get("planId")
        String riskId   =   (String)map.get("riskId")
        String termNum  =  (String)map.get("termNum")
        String userId   =   (String)map.get("userId")
        String policyStatus = (String)map.get("policyStatus")
        Long prop = sumMap.get(userId);
        log.warn("匹配佣金:{}",prop)
        if(prop!=null){
            String dataId = orderId+'|'+policyNo+'|'+idCard+'|'+planId+'|'+riskId+'|' +termNum+'|' +policyStatus
            resMap.put("dataId",dataId)
            resMap.put("uuid",dataId)
            resMap.put("proportion",prop)
            result.add(resMap)
        }
    }
    return result
}

private Map<String,Long> summaryAchievement(){
    String sql = '''
        	select 
		sum(premium) as premium,
		commission_user_id as userId
from( select
				case when t.conversion_amount is null and t.policy_status=4 then t.amount*-1  
				when t.conversion_amount is null and t.policy_status=1 then t.amount
				else t.conversion_amount end as premium,
				t.commission_user_id
		from sm_commission_detail t
		left join sm_plan c on t.plan_id=c.id
		where c.productId in (454,434)
		and t.account_time > str_to_date('2022-05-01', '%Y-%m-%d')
		and t.account_time < str_to_date('2022-07-01', '%Y-%m-%d') 
		and SUBSTRING_INDEX(order_id,'_',1) 
		in (
            select  `fhOrderId` 
            from sm_order b 
            left join sm_commission_detail c on b.fhOrderId=c.order_id
            where b.productId in (454,434)
            and b.`payStatus` = 2
            and case when b.paymentTime is null then c.create_time else b.paymentTime end > str_to_date('2022-05-01', '%Y-%m-%d')
            and case when b.paymentTime is null then c.create_time else b.paymentTime end < str_to_date('2022-07-01', '%Y-%m-%d')
            and INSTR (b.fhOrderId ,'_') =0 
			)  
 ) a group by commission_user_id 
        '''
    List<Map> list = msgStatService.selectMaps(sql)
    Map<String,Long> awardMap = new HashMap()
    for(Map<String,Object> map:list){
        Long premium = (Long)map.get("premium")
        String userId =(String)map.get("userId");
        if(premium<=5000){
            awardMap.put(userId,2)
        }else if(premium>5000&&premium<=10000){
            awardMap.put(userId,3)
        }else if(premium>10000&&premium<=50000){
            awardMap.put(userId,4)
        }else if(premium>50000){
            awardMap.put(userId,6)
        }
    }
    return awardMap;
}
