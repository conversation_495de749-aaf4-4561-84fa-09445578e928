import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.JSONObject
import com.cfpamf.ms.insur.operation.msg.pojo.dto.OpMessageRuleGroovyDTO
import com.cfpamf.ms.insur.operation.msg.util.Html2ImageUtils

import java.time.LocalDate
import java.time.YearMonth
import com.cfpamf.ms.insur.operation.base.constant.BaseConstants

import java.time.temporal.ChronoUnit
import java.time.temporal.TemporalUnit

String execute(OpMessageRuleGroovyDTO rule) {
    log.info("脚本入参:{}", ((JSONObject)JSONObject.toJSON(rule)).toJSONString());
    Map<String, Object> result = getDetailData(rule);
    if(result==null){
        return null;
    }
    return Html2ImageUtils.processTemplateInfoString(rule.getMessageTemplate(), result);
}

/**
 * 弹窗机构数据
 * @param params
 * @return
 */
Map<String, Object> getOrgPopUpData(OpMessageRuleGroovyDTO rule) {
    LocalDate endDate = rule.getEndDate()
    Long restDays = null;
    if(endDate!=null){
        restDays = LocalDate.now().until(endDate,ChronoUnit.DAYS)+1;
    }
    String lastDay = LocalDate.now().minusDays(1).format(BaseConstants.FMT_DATE);
    def userParam = rule.getReceiver().otherParams;
    String orgName = userParam.orgName;
    String regionName = userParam.regionName;
    String userId = userParam.mainJobNumber;
    String sqlPerson = """ 
            select 
                temp.area_name,
                temp.bch_name,
                temp.recommend_emp_id,
                temp.recommend_emp_name,
                temp.pt,
                temp.district_name,
                case when temp.is_jan_sm_manage = 1 then '是' else '否' end as is_jan_sm_manage,
                (temp.total_insured_jul - temp.total_surrender_jul) total_insured_jul,
                (temp.total_insured_aug - temp.total_surrender_aug) total_insured_aug,
                (temp.total_insured_sept - temp.total_surrender_sept) total_insured_sept,
                (temp.total_insured - temp.total_surrender) total_insured,
                total_insured - total_surrender - 100   as gap
            from phoenix.temp_safes_stats_new_year_2024 temp 
            where 
                temp.pt in ('${lastDay}') 
                and temp.area_name = '${regionName}'
                and temp.bch_name = '${orgName}' 
                and temp.recommend_emp_id = '${userId}' 
                order by temp.pt limit 1
        """;

    def personCountResult = msgStatService.selectSafesPgMap(sqlPerson);
    if(personCountResult == null){
        return null;
    }
    if(personCountResult["total_insured"]==null || personCountResult["total_insured"]==0){
        return null;
    }

    if(restDays != null){
        personCountResult.put("restdays",restDays);
    }
    log.info("push 参数 结果 {} {}", userParam, personCountResult)
    def ftlParam=["datasJson": JSON.toJSONString([personCountResult])]
    ftlParam["restdays"] = restDays
    return ftlParam;

}


Map<String, Object> getDetailData(OpMessageRuleGroovyDTO rule) {

    Map<String, Object> temp = getOrgPopUpData(rule);
    if(temp == null){
        return null;
    }
    return temp;
}

