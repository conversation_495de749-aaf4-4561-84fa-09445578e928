import com.alibaba.fastjson.JSON
import com.cfpamf.ms.insur.operation.base.constant.BaseConstants
import com.cfpamf.ms.insur.operation.msg.pojo.dto.OpMessageRuleGroovyDTO
import com.cfpamf.ms.insur.operation.msg.util.Html2ImageUtils
import org.apache.commons.collections4.CollectionUtils

import java.time.LocalDate
import java.time.LocalDateTime
import java.time.LocalTime
import java.time.format.DateTimeFormatter

Map execute(OpMessageRuleGroovyDTO rule) {
    def pt = LocalDate.now().minusDays(1L).format(BaseConstants.FMT_DATE)
    def queryResult =["policyList":[]]
    def times = [
            (LocalDate.now().atTime(LocalTime.parse("00:00:00", DateTimeFormatter.ofPattern("HH:mm:ss")))) : (LocalDate.now().atTime(LocalTime.parse("08:18:00", DateTimeFormatter.ofPattern("HH:mm:ss")))),
            (LocalDate.now().atTime(LocalTime.parse("08:18:00", DateTimeFormatter.ofPattern("HH:mm:ss")))) : (LocalDate.now().atTime(LocalTime.parse("09:18:00", DateTimeFormatter.ofPattern("HH:mm:ss")))),
            (LocalDate.now().atTime(LocalTime.parse("09:18:00", DateTimeFormatter.ofPattern("HH:mm:ss")))) : (LocalDate.now().atTime(LocalTime.parse("10:18:00", DateTimeFormatter.ofPattern("HH:mm:ss")))),
            (LocalDate.now().atTime(LocalTime.parse("10:18:00", DateTimeFormatter.ofPattern("HH:mm:ss")))) : (LocalDate.now().atTime(LocalTime.parse("11:18:00", DateTimeFormatter.ofPattern("HH:mm:ss"))))
    ]
    def currentTime = LocalDateTime.now().minusHours(1)
    //def currentTime = LocalDateTime.parse("2024-11-17 10:20:00",DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")).minusHours(1)
    def startTime = "";
    def endTime = "";
    for (time in times) {
        if (currentTime.isAfter(time.getKey()) && currentTime.isBefore(time.getValue())) {
            startTime = time.getKey().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))
            endTime = time.getValue().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))
            break
        }
    }
    if(startTime == ""){
        return queryResult
    }
    def resultMap = [:]  // 使用 Map 保存结果
    def sql = """
                       select b.`policy_no`,
                              a.`paymentTime`,
                              a.`recommendMainJobNumber`,
                              au.username,
                              au.organizationname,
                              au.regionname,
                              a.totalAmount
                      from `sm_order` a 
                            join `sm_order_policy` b on a.`fhOrderId` =b.`fh_order_id` 
                            LEFT JOIN `auth_user` au on au.userid = a.`recommendMainJobNumber` 
                      where a.`paymentTime` > '${startTime}'
                        and a.`paymentTime` <= '${endTime}'
                        and a.`recommendId` is not null
                      and EXISTS (select 1 from `sm_order_insured` i where i.`fhOrderId` = `fhOrderId` and i.`appStatus` <> 2)
                      order by a.`paymentTime` desc ,a.`id` desc 
                      limit 10
                    """

    queryResult["policyList"] = msgStatService.selectMaps(sql);

    log.info(JSON.toJSONString(queryResult))
    return queryResult
}