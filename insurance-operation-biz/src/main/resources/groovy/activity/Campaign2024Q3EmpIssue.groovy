import com.alibaba.fastjson.JSON
import com.cfpamf.ms.insur.operation.base.constant.BaseConstants
import com.cfpamf.ms.insur.operation.msg.pojo.dto.OpMessageRuleGroovyDTO
import com.cfpamf.ms.insur.operation.msg.util.Html2ImageUtils
import org.apache.commons.collections4.CollectionUtils

import java.time.LocalDate


Map execute(OpMessageRuleGroovyDTO rule) {
    log.info("==========================start1================================")
    def pt = LocalDate.now().minusDays(1L).format(BaseConstants.FMT_DATE)
    def areaCodeListString = rule.areaCodeListString
    log.info("==========================start================================"+areaCodeListString)
    def resultMap = [:]  // 使用 Map 保存结果
    def sql = """
    select max(t.bch_name) as branch_name,max(t.recommend_emp_name) as emp_name,max(t.insurance_product_name) as product_name, sum(t.insurance_amt) as insurance_money, max(t.policy_no) as policy_no from phoenix.temp_safes_policy_new_year_2024 t 
    where t.pt ='${pt}'
    and t.trade_time >= current_date - interval '1 day'
    and t.area_code in (${areaCodeListString})
	GROUP BY t.policy_no ORDER BY min(t.trade_time) asc
    """
    log.info("==========================start================================"+sql)
    def queryResult = msgStatService.selectSafesPgMaps(sql);
    queryResult.forEach {
        def policyNo = it["policy_no"]
        def insuranceMoney = it["insurance_money"]
        if(insuranceMoney <= 0){
            log.info("surrender policyNo= "+policyNo+"; insuranceMoney= "+insuranceMoney)
            return true
        }
        def recommendEmpId = it["recommend_emp_id"]
        log.info("test================"+JSON.toJSONString(it));
        def sqlSafes = """
                            select * from op_message_push where `biz_code` = '${policyNo}'  and `rule_code` = '${rule.ruleCode}' and receiver = '${rule.receiver.receiver}' limit 1
                           """
        def queryResultSafes = msgStatService.selectMaps(sqlSafes);
        if (CollectionUtils.isEmpty(queryResultSafes)){
            def dingInfos = wecatService.getDingTalkInfo([recommendEmpId])
            def we = dingInfos.find { dingInfo -> Objects.equals(dingInfo.wechatUserId, recommendEmpId) }
            if (Objects.nonNull(we)){
                it["avatar"] = we.avatar
            }
            def htmlString = Html2ImageUtils.processTemplateInfoString(rule.getMessageTemplate(), it)
            log.info("test================"+htmlString);
            resultMap.put(policyNo, htmlString)
        }
    }
    return resultMap
}
