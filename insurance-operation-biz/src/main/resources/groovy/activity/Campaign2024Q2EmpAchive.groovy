import com.alibaba.fastjson.JSON
import com.cfpamf.ms.insur.operation.base.constant.BaseConstants
import com.cfpamf.ms.insur.operation.msg.pojo.dto.OpMessageRuleGroovyDTO
import com.cfpamf.ms.insur.operation.msg.util.Html2ImageUtils
import org.apache.commons.collections4.CollectionUtils

import java.time.LocalDate

Map execute(OpMessageRuleGroovyDTO rule) {
    def pt = LocalDate.now().minusDays(1L).format(BaseConstants.FMT_DATE)
    def resultMap = [:]  // 使用 Map 保存结果
    def sql = """select a.*, RANK() OVER (ORDER BY a.pt asc,a.total_insured desc ) rank from phoenix.temp_safes_stats_new_year_2024 a join 
    (select t.bch_code , t.recommend_emp_id , t.recommend_emp_name , min(t.pt) actime from phoenix.temp_safes_stats_new_year_2024 t 
    where ((t.total_insured - t.total_surrender) >= 100 or (t.total_insured_apr - t.total_surrender_apr) >= 50
        or (t.total_insured_may - t.total_surrender_may ) >= 50 or (t.total_insured_jun- t.total_surrender_jun) >= 50 )
    GROUP BY t.bch_code , t.recommend_emp_id , t.recommend_emp_name ) temp
on a.bch_code = temp.bch_code and a.recommend_emp_id = temp.recommend_emp_id and a.recommend_emp_name  = temp.recommend_emp_name and a.pt= temp.actime
and pt = '${pt}'"""
    def queryResult = msgStatService.selectSafesPgMaps(sql);

    queryResult.forEach {
        def recommendEmpId = it["recommend_emp_id"]
        //补充接受对象过滤
        def sqlSafes = """
                            select * from op_message_push where `biz_code` = '${recommendEmpId}'  and `rule_code` = '${rule.ruleCode}' and receiver = '${rule.receiver.receiver}' limit 1
                           """
        def queryResultSafes = msgStatService.selectMaps(sqlSafes);
        if (CollectionUtils.isEmpty(queryResultSafes)){
            def dingInfos = wecatService.getDingTalkInfo([recommendEmpId])

            def we = dingInfos.find { dingInfo -> Objects.equals(dingInfo.wechatUserId, recommendEmpId) }
            if (Objects.nonNull(we)){
                it["avatar"] = we.avatar
            }
            log.info(JSON.toJSONString(it));
            def htmlString = Html2ImageUtils.processTemplateInfoString(rule.getMessageTemplate(), it)
            log.info(htmlString);
            resultMap.put(recommendEmpId, htmlString)
        }
    }
    return resultMap
}