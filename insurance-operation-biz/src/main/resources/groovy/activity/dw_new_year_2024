CREATE TABLE IF NOT EXISTS temp_safes_stats_order_area_new_year_2024
(
    area_name        STRING COMMENT '区域名称',
    policy_count_avg DOUBLE  COMMENT '人均保单数'
)
COMMENT '开门红区域排名'
PARTITIONED BY
(
    pt               STRING
)
LIFECYCLE 500;

CREATE TABLE IF NOT EXISTS temp_safes_stats_order_bch_new_year_2024
(
    area_name        STRING COMMENT '区域名称',
    bch_name         STRING COMMENT '区域名称',
    bch_code         STRING COMMENT '区域编码',
    policy_count_avg DOUBLE COMMENT '人均保单数'
)
COMMENT '开门红区域排名'
PARTITIONED BY
(
    pt               STRING
)
LIFECYCLE 500;