import com.cfpamf.ms.insur.operation.msg.pojo.dto.OpMessageRuleGroovyDTO
import com.cfpamf.ms.insur.operation.msg.util.Html2ImageUtils
import org.apache.commons.collections4.CollectionUtils

Map execute(OpMessageRuleGroovyDTO rule) {
    def resultMap = [:]  // 使用 Map 保存结果
    def sql = """select a.*, RANK() OVER (ORDER BY a.pt asc,a.total_insured desc ) rank from phoenix.temp_safes_stats_new_year_2024 a join 
    (select t.bch_code , t.recommend_emp_id , t.recommend_emp_name , min(t.pt) actime from phoenix.temp_safes_stats_new_year_2024 t 
    where ((t.total_insured - t.total_surrender) >= 100 or (t.total_insured_jan - t.total_surrender_jan) >= 50
        or (t.total_insured_feb - t.total_surrender_feb ) >= 50 or (t.total_insured_mar- t.total_surrender_mar) >= 50 )
        and t.recommend_emp_id not in ('ZHNX37602','LNKP0004')
    GROUP BY t.bch_code , t.recommend_emp_id , t.recommend_emp_name ) temp
on a.bch_code = temp.bch_code and a.recommend_emp_id = temp.recommend_emp_id and a.recommend_emp_name  = temp.recommend_emp_name and a.pt= temp.actime"""
    def queryResult = msgStatService.selectSafesPgMaps(sql);

    queryResult.forEach {
        def recommendEmpId = it["recommend_emp_id"]
        //补充接受对象过滤
        def sqlSafes = """
                            select * from op_message_push where `biz_code` = '${recommendEmpId}'  and `rule_code` = '${rule.ruleCode}' and receiver = '${rule.receiver.receiver}' limit 1
                           """
        def queryResultSafes = msgStatService.selectMaps(sqlSafes);
        if (CollectionUtils.isEmpty(queryResultSafes)){
            def dingInfos = wecatService.getDingTalkInfo([recommendEmpId])

            def we = dingInfos.find { dingInfo -> Objects.equals(dingInfo.wechatUserId, recommendEmpId) }
            if (Objects.nonNull(we)){
                it["avatar"] = we.avatar
            }
            def htmlString = Html2ImageUtils.processTemplateInfoString(rule.getMessageTemplate(), it)
            resultMap.put(recommendEmpId, htmlString)
        }
    }
    return resultMap
}