import com.alibaba.fastjson.JSON
import com.cfpamf.ms.insur.operation.base.constant.BaseConstants
import com.cfpamf.ms.insur.operation.msg.pojo.dto.OpMessageRuleGroovyDTO
import com.cfpamf.ms.insur.operation.msg.util.Html2ImageUtils
import org.apache.commons.collections4.CollectionUtils

import java.time.LocalDate
import java.time.temporal.ChronoUnit

String execute(OpMessageRuleGroovyDTO rule) {
    LocalDate endDate = rule.getEndDate()
    Long restDays = null;
    if(endDate!=null){
        restDays = LocalDate.now().until(endDate, ChronoUnit.DAYS);
    }

    def pt = LocalDate.now().minusDays(1L).format(BaseConstants.FMT_DATE)
    def sql = """
        select 
            area_code,
            area_name,
            district_code,
            district_name,
            round(q4_offline_loan_insurance_rate,2) q4_offline_loan_insurance_rate,
            q4_offline_loan_insurance_rate_gt70_rank rank,
            case when q4_offline_loan_insurance_rate_gt70_rank >= 1 and q4_offline_loan_insurance_rate_gt70_rank <= 5 then '映山红水晶5箱'
            when q4_offline_loan_insurance_rate_gt70_rank >= 6 and q4_offline_loan_insurance_rate_gt70_rank <= 15 then '映山红水晶3箱'
            else '映山红水晶2箱' 
            end remark,
            case when q4_offline_loan_insurance_rate_gt70_rank >= 1 and q4_offline_loan_insurance_rate_gt70_rank <= 5 then '或尚品/臻陈18箱'
            when q4_offline_loan_insurance_rate_gt70_rank >= 6 and q4_offline_loan_insurance_rate_gt70_rank <= 15 then '或尚品/臻陈10箱'
            else '或尚品/臻陈7箱' 
            end remark2
        from report.ads_marketing_lifesrv_4q_district_static_dfp amlqdsd 
        where pt = '${pt}' and q4_offline_loan_insurance_rate_gt70_rank <=20
        order by q4_offline_loan_insurance_rate_gt70_rank 
    """
    def datas = msgStatService.selectSafesPgMaps(sql);
    if (CollectionUtils.isEmpty(datas)) {
        log.warn("ads_marketing_lifesrv_4q_district_static_dfp {}没数据", pt)
        return
    }
    def ftlMap = ["datasJson": JSON.toJSONString(datas)]

    ftlMap.put("restdays",restDays);
    log.info("push params {}", ftlMap)
    return Html2ImageUtils.processTemplateInfoString(rule.getMessageTemplate(), ftlMap)
}
