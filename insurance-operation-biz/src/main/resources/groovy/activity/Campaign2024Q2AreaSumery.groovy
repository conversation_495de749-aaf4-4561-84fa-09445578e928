import com.alibaba.fastjson.JSON
import com.cfpamf.ms.insur.operation.base.constant.BaseConstants
import com.cfpamf.ms.insur.operation.msg.pojo.dto.OpMessageRuleGroovyDTO
import com.cfpamf.ms.insur.operation.msg.util.Html2ImageUtils

import java.time.LocalDate
import java.time.temporal.ChronoUnit

String execute(OpMessageRuleGroovyDTO rule) {
    LocalDate endDate = rule.getEndDate()
    Long restDays = null;
    if(endDate!=null){
        restDays = LocalDate.now().until(endDate, ChronoUnit.DAYS);
    }

    def userParam = rule.getReceiver().otherParams;
    def pt = LocalDate.now().minusDays(1L).format(BaseConstants.FMT_DATE)
    def sql = """
                        select area_name
                        , sum(total_insured) - sum(total_surrender)   as total_insured
                        , round(max(area_manage_avg_cnt), 1) as area_manage_avg_cnt 
                        , round(max(area_manage_avg_cnt) * 27, 1)  as target_policies
                        , coalesce(SUM(activity1_total_insured) - SUM(activity1_total_surrender) + sum(zhongan_cancer_insured_jun)-sum(zhongan_cancer_surrender_jun) + sum(guotai_cancer_insured_jun)-sum(guotai_cancer_surrender_jun) + sum(jingxi_zhongan_hejiahuan_insured_jun)-sum(jingxi_zhongan_hejiahuan_surrender_jun),0) as sanxing_quanjiafu_insured
                        , round(coalesce(SUM(activity1_total_insured) - SUM(activity1_total_surrender) + sum(zhongan_cancer_insured_jun)-sum(zhongan_cancer_surrender_jun) + sum(guotai_cancer_insured_jun)-sum(guotai_cancer_surrender_jun) + sum(jingxi_zhongan_hejiahuan_insured_jun)-sum(jingxi_zhongan_hejiahuan_surrender_jun),0)/max(area_manage_avg_cnt), 1) AS san_xing_quanjiafu_insured_avg
                        , round(coalesce(SUM(activity1_total_insured) - SUM(activity1_total_surrender) + sum(zhongan_cancer_insured_jun)-sum(zhongan_cancer_surrender_jun) + sum(guotai_cancer_insured_jun)-sum(guotai_cancer_surrender_jun) + sum(jingxi_zhongan_hejiahuan_insured_jun)-sum(jingxi_zhongan_hejiahuan_surrender_jun),0)/max(area_manage_avg_cnt), 1) - 21 AS san_xing_quanjiafu_insured_avg_diff
                        , round((sum(total_insured) - sum(total_surrender)) / max(area_manage_avg_cnt),1) as avg_policies
                        , round((sum(total_insured) - sum(total_surrender)) / (max(area_manage_avg_cnt) * 27) * 100, 1) as pre_policies
                        from phoenix.temp_safes_stats_new_year_2024
                         where pt = '${pt}' and area_name = '${userParam.regionName}'
                        group by area_name
                    """
    def areaData = msgStatService.selectSafesPgMap(sql);
    if (areaData == null) {
        log.error("new_year_area_summary.groovy {} {}没数据", userParam.regionName, pt)
        return
    }
    areaData["gap"] =  areaData.total_insured - areaData.target_policies
    areaData["restdays"] = restDays;
    def resultData = [areaData]
    def ftlparam = ["datasJson": JSON.toJSONString(resultData)]
    ftlparam["restdays"] = restDays
    log.info("push params {} {}", userParam, ftlparam)
    return Html2ImageUtils.processTemplateInfoString(rule.getMessageTemplate(), ftlparam)
}
