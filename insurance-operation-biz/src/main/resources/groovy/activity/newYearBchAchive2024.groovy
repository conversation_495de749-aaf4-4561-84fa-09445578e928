import com.alibaba.fastjson.JSON
import com.cfpamf.ms.insur.operation.msg.pojo.dto.OpMessageRuleGroovyDTO
import com.cfpamf.ms.insur.operation.msg.util.Html2ImageUtils
import org.apache.commons.collections4.CollectionUtils


Map execute(OpMessageRuleGroovyDTO rule) {
    def resultMap = [:]  // 使用 Map 保存结果
    def sql = """select t1.*, RANK() OVER ( ORDER BY t1.pt asc,t1.bch_name asc,t1.policy_count_avg desc) rank
                        from phoenix.temp_safes_stats_order_bch_new_year_2024 t1 join (
                                SELECT area_name,bch_name,bch_code,min(pt) actime FROM phoenix.temp_safes_stats_order_bch_new_year_2024 
                                 group by area_name,bch_name,bch_code) t2 
                        on t1.area_name=t2.area_name and t1.bch_name=t2.bch_name and t1.bch_code=t2.bch_code and t1.pt=t2.actime
                        order by t1.pt asc,t1.bch_name asc,t1.policy_count_avg desc"""
    def queryResult = msgStatService.selectSafesPgMaps(sql);
    def bchCodes = ['LNKP','Z1608743','YNCN','YNCX','HBWD','HBCM','HBXS','HBXX','HBST','SXYX','HBBY','HBLR','NMAH','HBLY','Z1558321','HBFX','SXXY','SXFY']
    queryResult.forEach {
        def bchCode = it["bch_code"]
        def sqlSafes = """
                            select * from op_message_push where `biz_code` = '${bchCode}'  and `rule_code` = '${rule.ruleCode}' and receiver = '${rule.receiver.receiver}' limit 1
                           """
        def queryResultSafes = msgStatService.selectMaps(sqlSafes);
        if (CollectionUtils.isEmpty(queryResultSafes) && !bchCodes.contains(bchCode) ){
            def htmlString = Html2ImageUtils.processTemplateInfoString(rule.getMessageTemplate(), it)
            resultMap.put(bchCode, htmlString)
        }
    }
    return resultMap
}
