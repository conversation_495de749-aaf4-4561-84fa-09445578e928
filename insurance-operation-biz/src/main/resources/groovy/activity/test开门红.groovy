package groovy.activity

import com.cfpamf.ms.insur.operation.msg.service.MsgStatService
import org.apache.commons.lang3.StringUtils

List<Map<String, Object>> execute(String params) {

    MsgStatService s = msgStatService;
    String sql = """
set session group_concat_max_len  = 1024 * 1024;
with t as (select order_id,
                  id,
                  insured_id_number,
                  conversion_amount,
                  policy_status,
                  lead(t.conversion_amount)
                       over ( partition by SUBSTRING_INDEX(order_id, '_', 1),insured_id_number order by
                           cast(if(instr(t.order_id, '_') = 0, 0,
                                   SUBSTRING(t.order_id, instr(t.order_id, '_') + 1)) as signed))
                                  sub_amt,
                  lead(concat_ws('|', order_id, policy_no, insured_id_number, plan_id, policy_status)) over w
                                  sub_data_id,
                  lead(id) over w sub_id
           from sm_commission_detail t
           where t.account_time >= str_to_date('2023-03-01', '%Y-%m-%d')
             and t.account_time < str_to_date('2023-05-01', '%Y-%m-%d')
               window w as (
                   -- 根据原始订单和证件号分组
                   partition by SUBSTRING_INDEX(order_id, '_', 1),insured_id_number order by
                       cast(if(instr(t.order_id, '_') = 0, 0,
                               SUBSTRING(t.order_id, instr(t.order_id, '_') + 1)) as signed)))
select order_id,                                                                        -- 订单号
       sum(t.conversion_amount)                                            all_amt,     -- 原始金额
       sum(t.sub_amt)                                                      sub_amt,     -- 批减金额
       sum((t.conversion_amount + ifnull(t.sub_amt, 0)))                   cal_amt,     -- 实际计算金额
       group_concat(id)                                                    add_id,      -- 原始所有新增id
       group_concat(sub_id)                                                sub_id,      -- 批减数据id
       group_concat(sub_data_id)                                           sub_data_id, -- 批减数据标记
       if(sum((t.conversion_amount + ifnull(t.sub_amt, 0))) > 10000, 5, 3) proportion
from t
where t.policy_status = '1'
group by order_id
having cal_amt >= 5000"""
    def maps = s.selectMaps(sql)
    def activitId = 100
    def res = []
    maps.forEach {

        def subDataIds = StringUtils.split(it.sub_data_id as String, ",")
        res.addAll(subDataIds.collect { ['proportion': it.proportion, 'dataId': it, 'uuid': it] })
        def addIds = StringUtils.split(it.add_data_id as String, ",")
        res.add(addIds.collect { ['proportion': it.proportion, 'dataId': it, 'uuid': it] })
    }

    return []
}
