package groovy.activity

import com.google.common.collect.Lists
import com.google.common.collect.Maps


List<Map<String, Object>> execute(String params) {
    String sql = """  SELECT
 ( CASE WHEN a.totalAmount >= 10000 THEN 5 ELSE 2 END ) AS proportion,
 a.fhOrderIdList AS dataId 
 FROM
 (
 SELECT
 t1.IdNumber,
 t2.customerAdminMainJobNumber,
 sum( CASE WHEN t3.app_status = 1 THEN t3.total_amount ELSE t3.total_amount * - 1 END ) AS totalAmount,
 group_concat( t2.fhOrderId ) AS fhOrderIdList 
 FROM
 sm_order_applicant t1
 LEFT JOIN sm_order t2 ON t1.fhOrderId = t2.fhOrderId
 LEFT JOIN sm_order_item t3 ON t3.fh_order_id = t2.fhOrderId 
 WHERE
 t2.create_time >= '2022-01-01' 
 AND t2.productId IN ( 251, 169, 180, 219 ) 
 AND t2.`orderType` = 0 
 GROUP BY
 t1.IdN<PERSON>ber,
 t2.customerAdminMainJobNumber 
) a"""
    List<Map<String,Object>> mapList = msgStatService.selectMaps(sql)

    List<Map<String, Object>> result = Lists.newArrayList()
    for (Map<String, Object> map : mapList) {
        String fhOrderIdList = (String)map.get("dataId")
        Long proportion = (Long)map.get("proportion")
        for (String orderId : fhOrderIdList.split(",")) {
            Map<String, Object> resultMap = Maps.newHashMap();
            resultMap.put("proportion",proportion);
            resultMap.put("dataId",orderId);
            resultMap.put("uuid","ORDER_ID-"+orderId);
            result.add(resultMap);
        }
    }
    return result

}
