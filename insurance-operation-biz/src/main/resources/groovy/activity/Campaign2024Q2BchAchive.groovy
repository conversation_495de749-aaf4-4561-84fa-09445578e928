import com.alibaba.fastjson.JSON
import com.cfpamf.ms.insur.operation.base.constant.BaseConstants
import com.cfpamf.ms.insur.operation.msg.pojo.dto.OpMessageRuleGroovyDTO
import com.cfpamf.ms.insur.operation.msg.util.Html2ImageUtils
import org.apache.commons.collections4.CollectionUtils

import java.time.LocalDate


Map execute(OpMessageRuleGroovyDTO rule) {
    def pt = LocalDate.now().minusDays(1L).format(BaseConstants.FMT_DATE)
    def resultMap = [:]  // 使用 Map 保存结果
    def sql = """select * from (select t.area_name,t.bch_name,t.bch_code,t.pt,
                        case when max(t.bch_manage_avg_cnt)>0 then sum(t.total_insured -t.total_surrender)/max(t.bch_manage_avg_cnt) 
                             ELSE 0
                        end as avg_policis 
                        from phoenix.temp_safes_stats_new_year_2024 t 
                        where t.pt ='${pt}'
                        GROUP BY t.area_name,t.bch_name,t.bch_code ,t.pt ) a where a.avg_policis>27"""
    def queryResult = msgStatService.selectSafesPgMaps(sql);
    def bchCodes = []
    queryResult.forEach {
        def bchCode = it["bch_code"]
        def sqlSafes = """
                            select * from op_message_push where `biz_code` = '${bchCode}'  and `rule_code` = '${rule.ruleCode}' and receiver = '${rule.receiver.receiver}' limit 1
                           """
        def queryResultSafes = msgStatService.selectMaps(sqlSafes);
        if (CollectionUtils.isEmpty(queryResultSafes) && !bchCodes.contains(bchCode) ){
            def htmlString = Html2ImageUtils.processTemplateInfoString(rule.getMessageTemplate(), it)
            resultMap.put(bchCode, htmlString)
        }
    }
    return resultMap
}
