import com.alibaba.fastjson.JSON
import com.cfpamf.ms.insur.operation.base.constant.BaseConstants
import com.cfpamf.ms.insur.operation.msg.pojo.dto.OpMessageRuleGroovyDTO
import com.cfpamf.ms.insur.operation.msg.util.Html2ImageUtils
import org.apache.commons.collections4.CollectionUtils

import java.time.LocalDate
import java.time.temporal.ChronoUnit


Map execute(OpMessageRuleGroovyDTO rule) {
    def pt = LocalDate.now().minusDays(1L).format(BaseConstants.FMT_DATE)
    def resultMap = [:]  // 使用 Map 保存结果
    LocalDate endDate = rule.getEndDate()
    Long restDays = null;
    if(endDate!=null){
        restDays = LocalDate.now().until(endDate, ChronoUnit.DAYS);
        log.info("剩余天数：{}", restDays)
    }
    def sql = """select 
                            t1.area_code,
                            area_name,
                            round(sy_wine_revenue_amt/10000,0)::text sy_wine_revenue_amt,
                            round(sq_acm_wine_revenue_amt_ratio*100,2) sq_acm_wine_revenue_amt_ratio,
                            RANK() OVER ( ORDER BY t1.pt asc,round(sq_acm_wine_revenue_amt_ratio*100,2) desc) rank
                        from report.ads_marketing_lifesrv_4q_area_wine_achieve_rank_static_dfp t1 
                        join (
                            SELECT 
                            area_code,min(pt) actime 
                            FROM report.ads_marketing_lifesrv_4q_area_wine_achieve_rank_static_dfp 
                            group by area_code
                        ) t2 
                        on t1.area_code=t2.area_code and t1.pt=t2.actime
                        order by t1.pt asc,t1.sq_acm_wine_revenue_amt_ratio desc
                    """
    def queryResult = msgStatService.selectSafesPgMaps(sql);
    log.info("区域酒水数据：{}", queryResult)
    queryResult.forEach {
        def areaCode = it["area_code"]
        it["restDays"] = restDays
        def rank = it["rank"]
        if (rank==1) {
            it["rankMark"] = "首个"
        } else {
            it["rankMark"] = "第${rank}个"
        }
        log.info("it：{}", it)
        def sqlSafes = """
                            select * from op_message_push where `biz_code` = '${areaCode}'  and `rule_code` = '${rule.ruleCode}' and receiver = '${rule.receiver.receiver}' limit 1
                           """
        def queryResultSafes = msgStatService.selectMaps(sqlSafes);
        if (CollectionUtils.isEmpty(queryResultSafes)){
            def htmlString = Html2ImageUtils.processTemplateInfoString(rule.getMessageTemplate(), it)
            log.info("区域推送HTML：{}", htmlString)
            resultMap.put(areaCode, htmlString)
        }
    }
    return resultMap
}
