import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.JSONObject
import com.cfpamf.ms.insur.operation.msg.pojo.dto.OpMessageRuleGroovyDTO
import com.cfpamf.ms.insur.operation.msg.util.Html2ImageUtils

import java.time.LocalDate
import java.time.YearMonth
import com.cfpamf.ms.insur.operation.base.constant.BaseConstants

import java.time.temporal.ChronoUnit
import java.time.temporal.TemporalUnit

String execute(OpMessageRuleGroovyDTO rule) {
    log.info("脚本入参:{}", ((JSONObject)JSONObject.toJSON(rule)).toJSONString());
    Map<String, Object> result = getDetailData(rule);
    if(result==null){
        return null;
    }
    return Html2ImageUtils.processTemplateInfoString(rule.getMessageTemplate(), result);
}

/**
 * 弹窗机构数据
 * @param params
 * @return
 */
Map<String, Object> getOrgPopUpData(OpMessageRuleGroovyDTO rule) {
    LocalDate endDate = rule.getEndDate()
    Long restDays = null;
    if(endDate!=null){
        restDays = LocalDate.now().until(endDate,ChronoUnit.DAYS);
    }
    int currentMonth = YearMonth.now().getMonthValue();
    String lastDay = LocalDate.now().minusDays(1).format(BaseConstants.FMT_DATE);
    def userParam = rule.getReceiver().otherParams;
    String orgName = userParam.orgName;
    String regionName = userParam.regionName;
    String userId = userParam.mainJobNumber;
    String sql
    String sqlPerson = """ 
        select temp.recommend_emp_id,
               temp.recommend_emp_name,
               temp.total_insured-temp.total_surrender activitypolicycount,
               temp.pt,
               temp.district_name districtname,
               temp.is_jan_sm_manage isjanmanage,
               (temp.total_insured_jan - temp.total_surrender_jan) personcount01,
               (temp.total_insured_feb - temp.total_surrender_feb) personcount02,
               (temp.total_insured_mar - temp.total_surrender_mar) personcount03,
               (temp.total_insured - temp.total_surrender) personcounttotal
        from phoenix.temp_safes_stats_new_year_2024 temp where temp.pt in ('${lastDay}') and temp.area_name = '${regionName}'
               and temp.bch_name = '${orgName}' and temp.recommend_emp_id = '${userId}' order by temp.pt limit 1
    """;

    personCountResult = msgStatService.selectSafesPgMap(sqlPerson);
    if(personCountResult == null){
        return new HashMap();
    }
    sql = """ 
        select 
               temp.area_name regionname,
               temp.bch_name orgname,
               temp.recommend_emp_id recommendempid,
               temp.recommend_emp_name recommendempname,
               temp.total_insured-temp.total_surrender activitypolicycount,
               temp.pt
        from phoenix.temp_safes_stats_new_year_2024 temp where temp.pt in ('${lastDay}') and temp.area_name = '${regionName}'
               and temp.bch_name = '${orgName}' and temp.recommend_emp_id = '${userId}' order by temp.pt
        """;
    Map<String, Object> result = msgStatService.selectSafesPgMap(sql);
    if(result == null){
          return null;
//        result = new HashMap();
//        result.put("activitypolicycount",0);
//        result.put("pt",lastDay);
//        result.put("recommendempname",userParam.userName);
//        result.put("recommendempid",userId);
//        result.put("orgname",orgName);
//        result.put("regionname",regionName);
    }
    if(result.get("activitypolicycount",0)==0){
        return null;
    }
    if(restDays!=null){
        result.put("restdays",restDays);
    }
    result.put("recommendempname",userParam.userName);
    result.put("recommendempid",userId);
    result.put("personcount01",personCountResult["personcount01"]);
    result.put("personcount02",personCountResult["personcount02"]);
    result.put("personcount03",personCountResult["personcount03"]);
    result.put("personcounttotal",personCountResult["personcounttotal"]);
    result.put("districtname",personCountResult["districtname"]);
    result.put("isjanmanage",personCountResult["isjanmanage"]);
    return result;

}


Map<String, Object> getDetailData(OpMessageRuleGroovyDTO rule) {

    Map<String, Object> temp = getOrgPopUpData(rule);
    if(temp == null){
        return null;
    }

    Map<String, Object> result = new HashMap<>();
    result.put("orgPopData", temp);

    return result;
}



