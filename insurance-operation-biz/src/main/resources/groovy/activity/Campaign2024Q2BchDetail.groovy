import com.alibaba.fastjson.JSON
import com.cfpamf.ms.insur.operation.base.constant.BaseConstants
import com.cfpamf.ms.insur.operation.msg.pojo.dto.OpMessageRuleGroovyDTO
import com.cfpamf.ms.insur.operation.msg.util.Html2ImageUtils
import org.apache.commons.collections4.CollectionUtils

import java.time.LocalDate
import java.time.temporal.ChronoUnit

String execute(OpMessageRuleGroovyDTO rule) {
    LocalDate endDate = rule.getEndDate()
    Long restDays = null;
    if(endDate!=null){
        restDays = LocalDate.now().until(endDate, ChronoUnit.DAYS);
    }

    def userParam = rule.getReceiver().otherParams;
    def pt = LocalDate.now().minusDays(1L).format(BaseConstants.FMT_DATE)
    def sql = """
select area_name
     , district_name
     , bch_name
     , bch_code
     , recommend_emp_name
     , recommend_emp_id
     , case when is_jan_sm_manage = 1 then '是' else '否' end as is_jan_sm_manage
     , total_insured - total_surrender         as total_insured
     , total_insured - total_surrender - 100   as gap
     , total_insured_apr - total_surrender_apr as total_insured_apr
     , total_insured_may - total_surrender_may as total_insured_may
     , total_insured_jun - total_surrender_jun as total_insured_jun
     , activity1_total_insured - activity1_total_surrender + zhongan_cancer_insured_jun-zhongan_cancer_surrender_jun + guotai_cancer_insured_jun-guotai_cancer_surrender_jun + jingxi_zhongan_hejiahuan_insured_jun-jingxi_zhongan_hejiahuan_surrender_jun as sanxing_quanjiafu_insured
     , jingxi_sanxing_quanjiafu_insured_apr - jingxi_sanxing_quanjiafu_surrender_apr AS sanxing_quanjiafu_insured_apr
     , jingxi_sanxing_quanjiafu_insured_may - jingxi_sanxing_quanjiafu_surrender_may AS sanxing_quanjiafu_insured_may
     , jingxi_sanxing_quanjiafu_insured_jun - jingxi_sanxing_quanjiafu_surrender_jun + zhongan_cancer_insured_jun-zhongan_cancer_surrender_jun + guotai_cancer_insured_jun-guotai_cancer_surrender_jun + jingxi_zhongan_hejiahuan_insured_jun-jingxi_zhongan_hejiahuan_surrender_jun + zhongan_cancer_xb_insured_jun - zhongan_cancer_xb_surrender_jun + jingxi_renbao_quanjiafu_insured_jun - jingxi_renbao_quanjiafu_surrender_jun AS sanxing_quanjiafu_insured_jun
from phoenix.temp_safes_stats_new_year_2024
where pt = '${pt}'
  and area_name = '${userParam.regionName}'
  and bch_code = '${userParam.orgCode}'
    """
    def allPt = [:]
    def datas = msgStatService.selectSafesPgMaps(sql);
    if (CollectionUtils.isEmpty(datas)) {
        log.warn("new_year_bch_detail {} {}没数据", userParam.orgCode, pt)
        return
    }
    datas.forEach { it.jan_sm_manage = it.is_jan_sm_manage == 1 ? "是" : "否" }
    def ftlMap = ["datasJson": JSON.toJSONString(datas)]

    ftlMap.put("restdays",restDays);
    log.info("push params {} {}", userParam, ftlMap)
    return Html2ImageUtils.processTemplateInfoString(rule.getMessageTemplate(), ftlMap)
}
