import java.time.LocalDate
import java.time.YearMonth
import com.cfpamf.ms.insur.operation.base.constant.BaseConstants
import org.apache.commons.lang3.StringUtils

Object execute(String params) {

    Map<String, Object> allParams = jacksonObjectMapper.readValue(params, Map.class);

    return getDetailData(allParams);
}

/**
 * 弹窗机构数据
 * @param params
 * @return
 */
Map<String, Object> getOrgPopUpData(Map<String, Object> params) {
    int currentMonth = YearMonth.now().getMonthValue();
    String lastDay = LocalDate.now().minusDays(1).format(BaseConstants.FMT_DATE);
    String orgName = params.get("orgName");
    String regionName = params.get("regionName");
    String userId = params.get("userId");
    Map<String, Object> mockData = new HashMap<>();
    String sql
    if (params.roleType == 1) {
//        mockData.put("orgName",orgName);
//        mockData.put("activityPolicyCount",100);
//        mockData.put("activityPolicyTarget",110);
//        mockData.put("activityPolicyCountAvg",20);
//        mockData.put("activityPolicyAvgTarget",20);

        sql = """ 
            select a.area_name regionName,
                   a.bch_name orgName, 
                   a.activityPolicyCount, 
                   a.activityPolicyTarget,
                   round(a.activityPolicyCount/personCount,1) activityPolicyCountAvg,
                   a.personCount,
                   21 activityPolicyAvgTarget
            FROM  (select area_name,
                  temp.bch_name,
                  sum(temp.total_insured-temp.total_surrender) activityPolicyCount,
                  max(temp.bch_jan_emp_num*21) activityPolicyTarget,
                  max(temp.bch_jan_emp_num) personCount
            from phoenix.temp_safes_stats_new_year_2024 temp where temp.pt = '${lastDay}' GROUP by temp.area_name,temp.bch_name) a where a.bch_name='${orgName}'
            """;
        Map<String, Object> result = msgStatService.selectSafesPgMap(sql);
        if (result == null){
            result = new HashMap<>();
            result.put("regionname",regionName);
            result.put("orgname",orgName);
            result.put("activitypolicycount",0);
            result.put("activitypolicytarget",0);
            result.put("activitypolicycountavg",0);
            result.put("activitypolicyavgtarget",21);
            result.put("personcount",0);

        }

        return result
    }
    if(params.roleType == 2){
//        mockData.put("regionName",regionName);
//        mockData.put("activityPolicyCount",100);
//        mockData.put("activityPolicyTarget",110);
//        mockData.put("activityPolicyCountAvg",20);
//        mockData.put("activityPolicyAvgTarget",20);
        //return mockData;
        sql = """
            select a.area_name regionName, 
                   a.activityPolicyCount, 
                   a.activityPolicyTarget,
                   round(a.activityPolicyCount/personCount,1) activityPolicyCountAvg,
                   a.personCount,
                   21 activityPolicyAvgTarget
            FROM  (select area_name,
                  sum(temp.total_insured-temp.total_surrender) activityPolicyCount,
                  max(temp.area_jan_emp_num)*21 activityPolicyTarget,
                  max(temp.area_jan_emp_num) personCount
            from phoenix.temp_safes_stats_new_year_2024 temp where temp.pt = '${lastDay}' GROUP by area_name) a where a.area_name='${regionName}'
            """;
        Map<String, Object> result = msgStatService.selectSafesPgMap(sql);
        if (result == null){
            result = new HashMap<>();
            result.put("regionname",regionName);
            result.put("activitypolicycount",0);
            result.put("activitypolicytarget",0);
            result.put("activitypolicycountavg",0);
            result.put("activitypolicyavgtarget",21);
            result.put("personcount",0);
        }
        return result;
    }

    if (params.roleType == 3) {
        Map<String, Object> result = new HashMap<>();
//        mockData.put("orgName",orgName);
//        mockData.put("activityPolicyCount",100);
//        mockData.put("activityPolicyTarget",110);
//        mockData.put("activityPolicyCountAvg",20);
//        mockData.put("activityPolicyAvgTarget",20);
//
//        mockData.put("personCount01",30);
//        mockData.put("personCount02",40);
//        mockData.put("personCount03",0);
//        return mockData;
        String sqlPerson = """ 
        select temp.recommend_emp_id,
               temp.recommend_emp_name,
               temp.total_insured-temp.total_surrender activityPolicyCount,
               temp.pt,
               temp.district_name districtname,
               (temp.total_insured_jan - temp.total_surrender_jan) personcount01,
               (temp.total_insured_feb - temp.total_surrender_feb) personcount02,
               (temp.total_insured_mar - temp.total_surrender_mar) personcount03,
               (temp.total_insured - temp.total_surrender) personcounttotal
        from phoenix.temp_safes_stats_new_year_2024 temp where temp.pt in ('${lastDay}') and temp.area_name = '${regionName}'
               and temp.bch_name = '${orgName}' and temp.recommend_emp_id = '${userId}' order by temp.pt limit 1
    """;

        Map<String, Object> personCountResult = msgStatService.selectSafesPgMap(sqlPerson);


        sql = """ 
            select a.area_name regionName,
                   a.bch_name orgName, 
                   a.activityPolicyCount, 
                   a.activityPolicyTarget,
                   round(a.activityPolicyCount/personCount,1) activityPolicyCountAvg,
                   a.personCount,
                   21 activityPolicyAvgTarget
            FROM  (select area_name,
                  temp.bch_name,
                  sum(temp.total_insured-temp.total_surrender) activityPolicyCount,
                  max(temp.bch_jan_emp_num)*21 activityPolicyTarget,
                  max(temp.bch_jan_emp_num) personCount
            from phoenix.temp_safes_stats_new_year_2024 temp where temp.pt = '${lastDay}' GROUP by temp.area_name,temp.bch_name) a where a.bch_name='${orgName}'
            """;
        result = msgStatService.selectSafesPgMap(sql);
        if (result == null){
            result = new HashMap<>();
            result.put("regionname",regionName);
            result.put("orgname",orgName);
            result.put("activitypolicycount",0);
            result.put("activitypolicytarget",0);
            result.put("activitypolicycountavg",0);
            result.put("activitypolicyavgtarget",21);
            result.put("personcount",0);

        }
        //计算每月数据
        if(personCountResult == null){
            result.put("personcount01",0);
            result.put("personcount02",0);
            result.put("personcount03",0);
            result.put("personcounttotal",0);
            return result;
        }
        result.put("personcount01",personCountResult["personcount01"]);
        result.put("personcount02",personCountResult["personcount02"]);
        result.put("personcount03",personCountResult["personcount03"]);
        result.put("personcounttotal",personCountResult["personcounttotal"]);
        result.put("districtname",personCountResult["districtname"]);
        return result;
    }

}


Map<String, Object> getDetailData(Map<String, Object> params) {

    Map<String, Object> result = new HashMap<>();

    result.put("orgPopData", getOrgPopUpData(params));

    return result;
}



