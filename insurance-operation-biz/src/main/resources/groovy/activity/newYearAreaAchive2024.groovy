import com.cfpamf.ms.insur.operation.msg.pojo.dto.OpMessageRuleGroovyDTO
import com.cfpamf.ms.insur.operation.msg.util.Html2ImageUtils
import org.apache.commons.collections4.CollectionUtils

Map execute(OpMessageRuleGroovyDTO rule) {
    def resultMap = [:]  // 使用 Map 保存结果
    def sql = """select t1.*, RANK() OVER ( ORDER BY t1.pt asc,t1.policy_count_avg desc) rank
from phoenix.temp_safes_stats_order_area_new_year_2024 t1 join (
SELECT area_name,min(pt) actime FROM phoenix.temp_safes_stats_order_area_new_year_2024  group by area_name) t2 
on t1.area_name=t2.area_name and t1.pt=t2.actime
order by t1.pt asc,t1.policy_count_avg desc"""
    def queryResult = msgStatService.selectSafesPgMaps(sql);

    queryResult.forEach {
        def areaName = it["area_name"]
        def sqlSafes = """
                            select * from op_message_push where `biz_code` = '${areaName}'  and `rule_code` = '${rule.ruleCode}' and receiver = '${rule.receiver.receiver}' limit 1
                           """
        def queryResultSafes = msgStatService.selectMaps(sqlSafes);
        if (CollectionUtils.isEmpty(queryResultSafes)){
            def htmlString = Html2ImageUtils.processTemplateInfoString(rule.getMessageTemplate(), it)
            resultMap.put(areaName, htmlString)
        }
    }
    return resultMap
}