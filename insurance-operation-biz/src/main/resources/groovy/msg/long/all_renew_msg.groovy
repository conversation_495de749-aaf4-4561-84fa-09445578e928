import com.alibaba.excel.EasyExcel
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy
import com.alibaba.fastjson.JSON
import com.cfpamf.ms.insur.operation.base.util.AliYunOssUtil
import com.cfpamf.ms.insur.operation.msg.pojo.dto.OpMessageRuleGroovyDTO
import com.cfpamf.ms.insur.operation.msg.service.MsgStatService
import org.apache.commons.collections4.CollectionUtils

import java.time.LocalDate
import java.time.format.DateTimeFormatter

Map execute(OpMessageRuleGroovyDTO rule) {
    MsgStatService msgStatService = msgStatService;

    def ftlParam = [:]

    LocalDate currentDate = LocalDate.now()
    String start = LocalDate.of(currentDate.getYear(), 1, 1).toString()
    String end = currentDate.plusDays(60).toString()
    ftlParam["pt"] = LocalDate.now().minusDays(1L).format(DateTimeFormatter.ofPattern("M.d"))
    //  查询推广费
    def sql = """
SELECT regionName area_name,
       count(distinct t1.policy_no)                                 AS un_cts,
       sum(renewal_amount)                                       as un_amt,
       count(distinct case when `result` = 1 then t1.policy_no end) AS follow_1,
       count(distinct case when `result` = 2 then t1.policy_no end) AS follow_2,
       count(distinct case when `result` = 3 then t1.policy_no end) AS follow_3,
       count(distinct case when result is null then t1.policy_no end) AS follow_null
      FROM sm_order_renewal_term t1
               left join sm_order t2 on t2.fhOrderId = t1.order_id
               LEFT JOIN auth_user t6 ON t2.customerAdminId = t6.userId
          AND t6.enabled_flag = 0
               LEFT JOIN sm_order_renewal_term_notify_log t8 on t8.policy_no = t1.policy_no
          and t8.term_num = t1.term_num
          and t8.newest = 1
      WHERE due_time >= '${start}'
        and due_time < '${end}'
        and t6.regionName is not NULL
        and t1.renewal_status = 0 and t1.enabled_flag = 0
GROUP BY regionName
"""
    def statData= msgStatService.selectMaps(sql)
    if (CollectionUtils.isEmpty(statData as Collection<?>)) {
        return null
    }
    // 使用 BigDecimal 初始化累加结果
    def totals = statData.inject([un_cts:0, un_amt:new BigDecimal(0), follow_1:0, follow_2:0, follow_3:0, follow_null:0]) { total, current ->
        total.un_cts += current.un_cts as int
        total.un_amt += (current.un_amt as BigDecimal ?: new BigDecimal(0))
        total.follow_1 += current.follow_1 as int
        total.follow_2 += current.follow_2 as int
        total.follow_3 += current.follow_3 as int
        total.follow_null += current.follow_null as int
        return total
    }
    // 添加区域名称到累加结果中
    totals.area_name = '合计'

    // 将累加结果添加到结果集中
    statData << (totals as Map<String, Object>)
    def detailSql = """


SELECT t9.userName as `PCO`,
       t6.regionName as `区域`,
       t6.organizationName as `机构`,
       t6.userName as `管护经理`,
       t7.companyName as `保司名称`,
       t3.productName as `投保产品`,
       t1.policy_no as `保单号`,
       t4.personName as `投保人`,
       group_concat(distinct t5.personName) as `被保人`,
       t1.renewal_amount as `应收保费`,
       DATE_FORMAT(t1.due_time, '%Y-%m-%d') as `应缴日期`,
       t12.bank_name as `缴费银行`,
       t12.account_no as `银行账号`,
       MONTH(t1.due_time) as `应收月份`,
       t1.term_num as `交次`,
       t11.pay_period as `交费期限`,
       DATE_FORMAT(DATE_ADD(t1.due_time, INTERVAL grace_days DAY), '%Y-%m-%d') as `对应宽限期末`,
       CASE
           WHEN DATEDIFF(CURRENT_DATE, due_time) < 0 THEN
               CONCAT('应缴日期剩余', ABS(DATEDIFF(CURRENT_DATE, due_time)), '天')
           ELSE
               CASE
                   WHEN grace_days IS NOT NULL THEN
                       CONCAT('宽限期剩余', (grace_days - DATEDIFF(CURRENT_DATE, due_time)),
                              '天')
                   ELSE ''
                   END
           END                              as                                 `截止提示`,
       CASE
           WHEN t1.renewal_status = 0 THEN '未实收'
           WHEN t1.renewal_status = 2 THEN '已失效'
           ELSE '其他'
           END as `状态`,
       CASE
           WHEN t8.result = 1 THEN '愿意续期'
           WHEN t8.result = 2 THEN '不愿意续期'
           WHEN t8.result = 3 THEN '意愿不明'
           ELSE '未跟进'
           END as `跟进情况`,
       remark as `备注`,
       CASE
           WHEN t6.status = 8 THEN '离职'
           ELSE '在职'
           END as `是否在职`
FROM sm_order_renewal_term t1
         left join sm_order t2 on t2.fhOrderId = t1.order_id
         left join sm_product t3 on t2.productId = t3.id
         left join sm_company t7 on t3.companyId = t7.id
         LEFT JOIN sm_order_applicant t4 ON t2.fhOrderId = t4.fhOrderId
         LEFT JOIN sm_order_insured t5 ON t2.fhOrderId = t5.fhOrderId
         LEFT JOIN auth_user t6 ON t2.customerAdminId = t6.userId
    AND t6.enabled_flag = 0
         left join user_post t10 on t10.org_code = t6.orgCode
    and t10.post_code = '2357'
    and t10.service_type = 1
    and t10.service_status = 0
         LEFT JOIN auth_user t9 ON t10.main_job_number = t9.userId AND t9.enabled_flag = 0
         LEFT JOIN sm_order_renewal_term_notify_log t8 on t8.policy_no = t1.policy_no
    and t8.term_num = t1.term_num
    and t8.newest = 1
         left join sm_order_policy t11 on t1.order_id = t11.fh_order_id
         left join sm_order_renew_bind_info t12 on t1.order_id = t12.fh_order_id
WHERE due_time >= '${start}'
        and due_time < '${end}'
  and t6.regionName is not NULL
  and t1.renewal_status = 0  and t1.enabled_flag = 0 group by t1.order_id
"""

    def details = msgStatService.selectMaps(detailSql);
    if (CollectionUtils.isEmpty(details)) {
        return ftlParam
    }
    ftlParam["datasJson"] = JSON.toJSONString(statData)

    log.info("json is {}",ftlParam["datasJson"])
    def head = details[0]
    def heads = head.keySet().collect {[it]}
    def datas = details.collect {
        heads.collect({hit -> it.get(hit.get(0))})
    }
    ByteArrayOutputStream os = new ByteArrayOutputStream()
    EasyExcel.write(os).autoCloseStream(true)
            .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
            .sheet( "待续期明细").head(heads).doWrite(datas)

    log.info("开始上传数据到 oss ", details.size())
    def detailUrl = AliYunOssUtil.uploadByBytes(os.toByteArray(), "groovy/long_renewal/detail/" + UUID.randomUUID().toString().replaceAll("-", "") + "/" + System.currentTimeMillis() + ".xlsx")
    ftlParam["detailUrl"] = detailUrl
    return ftlParam
}
