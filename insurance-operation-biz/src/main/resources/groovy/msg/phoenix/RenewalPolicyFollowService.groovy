import com.alibaba.excel.EasyExcel
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy
import com.alibaba.fastjson.JSON
import com.cfpamf.ms.insur.operation.base.util.AliYunOssUtil
import com.cfpamf.ms.insur.operation.msg.pojo.dto.OpMessageRuleGroovyDTO
import com.cfpamf.ms.insur.operation.msg.service.MsgStatService
import org.apache.commons.collections4.CollectionUtils
import java.time.LocalDate
Map execute(OpMessageRuleGroovyDTO rule) {
    MsgStatService msgStatService = msgStatService;
    def ftlParam = [:]
    //  查询推广费
    def sql = """
         
select 
            a.customer_manager_id,
            a.customer_manager_name,
            a.supervisor_job_number,
            a.supervisor_name,
            area_code, 
            area_name,
            org_code,
            org_name,
            renewal_policy_count,
            cur_month_expire_count,
            premium,
            renewaled_count,
            no_follow_count,
            (cur_month_expire_count-renewaled_count) as wait_renewal_count,
            case when cur_month_expire_count=0 then 0 else round((cur_month_expire_count-no_follow_count)*100/cur_month_expire_count) end as follow_rate,
            case when cur_month_expire_count =0 then 0 else round(renewaled_count*100/cur_month_expire_count) end as renewal_rate
from (
        SELECT 
            a.customer_manager_id,
            a.customer_manager_name,
            a.supervisor_job_number,
            a.supervisor_name,
            area_code, 
            area_name,
            org_code,
            org_name,
            SUM(CASE WHEN renewal_flag = 'waited' THEN 1 ELSE 0 END) AS renewal_policy_count,
            SUM(CASE WHEN substr(a.renewal_before_date,1,7) = to_char(current_date,'yyyy-mm-dd') THEN 1 ELSE 0 END) AS cur_month_expire_count,
            SUM(CASE WHEN substr(a.renewal_before_date,1,7) = to_char(current_date,'yyyy-mm-dd') THEN CAST(premium AS NUMERIC) ELSE 0 END) AS premium,
            SUM(CASE WHEN renewal_flag = 'renewed' THEN 1 ELSE 0 END) AS renewaled_count,
            SUM(CASE WHEN substr(a.renewal_before_date,1,7) = to_char(current_date,'yyyy-mm-dd') AND a.renewal_follow_info = '未跟进' THEN 1 ELSE 0 END) AS no_follow_count
        FROM "phoenix"."dwd_renewal_policy_info"  a 
        GROUP BY 
        customer_manager_id,
        customer_manager_name,
        area_code,
        area_name,
        org_code,
        org_name,
        a.supervisor_job_number,
        supervisor_name
  ) a;

    """
    def dataList = msgStatService.selectSafesPgMaps(sql)
    log.info("获取数据集：{},{}",sql,JSON.toJSONString(dataList))
    if(CollectionUtils.isEmpty(dataList)){
        return null
    }
    def renewal_policy_count=0
    def cur_month_expire_count=0
    def premium=0
    def renewaled_count=0
    def wait_renewal_count=0
    def no_follow_count =0
    for(Map entry:dataList){
        renewal_policy_count+=entry.get("renewal_policy_count")
        cur_month_expire_count+=entry.get("cur_month_expire_count")
        premium+=entry.get("premium")
        renewaled_count+=entry.get("renewaled_count")
        wait_renewal_count+=entry.get("wait_renewal_count")
        no_follow_count+=entry.get("no_follow_count")
    }
    def renewal_rate = BigDecimal.ZERO
    def follow_rate = BigDecimal.ZERO

    if(cur_month_expire_count!=0) {
        renewal_rate = BigDecimal.valueOf(renewaled_count).multiply(BigDecimal.valueOf(100)).divide(new BigDecimal(cur_month_expire_count), 2, BigDecimal.ROUND_HALF_UP)
        follow_rate =(new BigDecimal(cur_month_expire_count).subtract(new BigDecimal(no_follow_count))).multiply(BigDecimal.valueOf(100)).divide(new BigDecimal(cur_month_expire_count), 2, BigDecimal.ROUND_HALF_UP)
    }
    Map lastTotal = [:]
    lastTotal["customer_manager_name"]= "分支合计"
    lastTotal["renewal_policy_count"]= renewal_policy_count
    lastTotal["premium"]= premium
    lastTotal["renewaled_count"]= renewaled_count
    lastTotal["wait_renewal_count"]= wait_renewal_count
    lastTotal["renewal_rate"]= renewal_rate
    lastTotal["follow_rate"]= follow_rate
    dataList.add(lastTotal)
    ftlParam["jsonDatas"] = JSON.toJSONString(dataList)

    def head = dataList[0]
    def heads = head.keySet().collect {[it]}
    def datas = dataList.collect {
        heads.collect({hit -> it.get(hit.get(0))})
    }
    ByteArrayOutputStream os = new ByteArrayOutputStream()
    EasyExcel.write(os).autoCloseStream(true)
            .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
            .sheet( "待续保明细").head(heads).doWrite(datas)

    log.info("开始上传数据到 oss ", dataList.size())
    def detailUrl = AliYunOssUtil.uploadByBytes(os.toByteArray(), "groovy/long_renewal/detail/" + UUID.randomUUID().toString().replaceAll("-", "") + "/" + System.currentTimeMillis() + ".xlsx")
    ftlParam["detailUrl"] = detailUrl
    ftlParam["month"] = LocalDate.now().getMonth().value;
    log.info("文件生成完成:{}", detailUrl)
     return ftlParam
}