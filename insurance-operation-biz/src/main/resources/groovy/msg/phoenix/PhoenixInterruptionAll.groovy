import com.cfpamf.ms.insur.operation.msg.pojo.dto.OpMessageRuleGroovyDTO
import com.cfpamf.ms.insur.operation.msg.service.MsgStatService
import com.cfpamf.ms.insur.operation.msg.util.Html2ImageUtils
import org.apache.commons.collections4.CollectionUtils

import java.time.LocalDate
import java.time.format.DateTimeFormatter

String execute(OpMessageRuleGroovyDTO rule) {
    MsgStatService msgStatService = msgStatService;
    def ftlParam = [:]
    //  查询推广费
    def sql = """
select *
from phoenix.dwa_safes_customer_interruption_area_statistics
    where un_renew_total>0
order by (case
              when regionname = '合计' then 2
              else 1 end),regionname
"""
    ftlParam["datas"] = msgStatService.selectSafesPgMaps(sql)


    if (CollectionUtils.isEmpty(ftlParam["datas"] as Collection<?>)) {
        return null
    }
    //最大值
    def notTotalDatas = ftlParam["datas"].findAll { it["regionname"] != '合计' }
    def max_sum_year = notTotalDatas.collect { it["sum_year"] }.max()
    def max_sum_month = notTotalDatas.collect { it["sum_month"] }.max()
    def max_renew_month = notTotalDatas.collect { it["renew_month"] }.max()
    def max_renew_year = notTotalDatas.collect { it["renew_year"] }.max()

    ftlParam["datas"].each {
        if (it["regionname"] != '合计') {

            if (max_sum_year != null && max_sum_year > 0) {
                it["sum_year_pre"] = Math.round(it["sum_year"] / max_sum_year * 100)
            }

            if (max_sum_month != null && max_sum_month > 0) {
                it["sum_month_pre"] = Math.round(it["sum_month"] / max_sum_month * 100)
            }

            if (max_renew_month != null && max_renew_month > 0) {
                it["renew_month_pre"] = Math.round(it["renew_month"] / max_renew_month * 100)
            }

            if (max_renew_year != null && max_renew_year > 0) {
                it["renew_year_pre"] = Math.round(it["renew_year"] / max_renew_year * 100)
            }
        }
        it["sum_year"] = Math.round(it["sum_year"] / 10000.0 * 100) / 100.0
        it["un_renew_total"] = Math.round(it["un_renew_total"] / 10000.0 * 100) / 100.0
    }

    ftlParam["rptDate"] = DateTimeFormatter.ofPattern("MM月dd日").format(LocalDate.now().minusDays(1L))
    return Html2ImageUtils.processTemplateInfoString(rule.getMessageTemplate(), ftlParam);
}
