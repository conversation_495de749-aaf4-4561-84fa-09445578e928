import com.alibaba.fastjson.JSON
import com.cfpamf.ms.insur.operation.msg.pojo.dto.OpMessageRuleGroovyDTO
import com.cfpamf.ms.insur.operation.msg.util.Html2ImageUtils
import org.apache.commons.collections4.CollectionUtils


Map execute(OpMessageRuleGroovyDTO rule) {
    def ftlParam = [:]

    def sql = """
    select t.*
    FROM (
    SELECT t2.regionName, t2.organizationName, t2.userName, t2.userId, SUM(IF(t.policy_status='4', t.conversion_amount*-1, t.conversion_amount)) AS amounts
        FROM sm_commission_detail t 
        LEFT JOIN sm_order t1 ON t.order_id = t1.fhOrderId
        LEFT JOIN auth_user t2 ON t1.recommendId = t2.userId
        WHERE t.account_time > CURDATE() and t.account_time < DATE_ADD(CURDATE(), INTERVAL 1 DAY) 
        and t1.recommendId is not null
        GROUP BY t1.recommendId
        HAVING amounts > 20000) t
    left join op_message_push t1 on t.userId=t1.biz_code and t1.message_rule_id=122
    where t1.id is null
    """

    ftlParam["datas"] = msgStatService.selectMaps(sql)

    if (CollectionUtils.isEmpty(ftlParam["datas"] as Collection<?>)) {
        return null
    }

    def resultMap = [:]  // 使用 Map 保存结果

    ftlParam["datas"].each { data ->
        def userId = data["userId"]
        def ftlMap = ["datasJson": JSON.toJSONString(data)]

        // 存储 userId 对应的数据行
        resultMap[userId] = Html2ImageUtils.processTemplateInfoString(rule.getMessageTemplate(), ftlMap)
    }

    return resultMap
}

