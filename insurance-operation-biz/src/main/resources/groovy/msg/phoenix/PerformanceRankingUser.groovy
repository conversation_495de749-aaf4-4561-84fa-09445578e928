package groovy.msg.phoenix

import com.alibaba.fastjson.JSON
import com.cfpamf.ms.insur.operation.msg.pojo.dto.OpMessageRuleGroovyDTO
import com.cfpamf.ms.insur.operation.msg.service.MsgStatService
import com.cfpamf.ms.insur.operation.msg.util.Html2ImageUtils
import org.apache.commons.collections4.CollectionUtils

import java.time.LocalDate
import java.time.format.DateTimeFormatter

String execute(OpMessageRuleGroovyDTO rule) {
    MsgStatService msgStatService = msgStatService;
    def userParam = rule.getReceiver().otherParams;

    def ftlParam = [:]
    //  查询员工待办统计数据
    def sql = """

SELECT * FROM (
SELECT  
    l7d_actual_receipt_performance, 
    l7d_norm_insurance_amt,
    sm_actual_receipt_performance, 
    sy_actual_receipt_performance, 
    sy_norm_insurance_amt,
     sm_norm_insurance_amt
 from phoenix.ads_insurance_emp_index_progress_dfp 
 where pt =TO_CHAR(CURRENT_DATE - INTERVAL '1 days', 'YYYYMMDD') and emp_id='${userParam.mainJobNumber}') t1 
 LEFT JOIN (
    SELECT
   max(l7d_actual_receipt_performance) as first_l7d_actual_receipt_performance
  ,max(sm_actual_receipt_performance) as first_sm_actual_receipt_performance
  ,max(sy_actual_receipt_performance) as first_sy_actual_receipt_performance
  ,max(l7d_norm_insurance_amt) as first_l7d_norm_insurance_amt
  ,max(sm_norm_insurance_amt) as first_sm_norm_insurance_amt
  ,max(sy_norm_insurance_amt) as first_sy_norm_insurance_amt
  FROM phoenix.ads_insurance_emp_index_progress_dfp where pt =TO_CHAR(CURRENT_DATE - INTERVAL '1 days', 'YYYYMMDD')
 ) t2 on 1=1
 left JOIN (
  SELECT * FROM (
  SELECT emp_id,
  rank() OVER (partition by pt order by l7d_actual_receipt_performance desc) as rank_l7d_actual_receipt_performance,
    rank() OVER (partition by pt order by l7d_norm_insurance_amt desc) rank_l7d_norm_insurance_amt
    ,rank() OVER (partition by pt order by sm_actual_receipt_performance desc) rank_sm_actual_receipt_performance
    ,rank() OVER (partition by pt order by sy_actual_receipt_performance desc) rank_sy_actual_receipt_performance
    ,rank() OVER (partition by pt order by sm_norm_insurance_amt desc) rank_sm_norm_insurance_amt
    ,rank() OVER (partition by pt order by sy_norm_insurance_amt desc) rank_sy_norm_insurance_amt
  from phoenix.ads_insurance_emp_index_progress_dfp where pt =TO_CHAR(CURRENT_DATE - INTERVAL '1 days', 'YYYYMMDD') ) t  WHERE t.emp_id='${userParam.mainJobNumber}'
 ) t3 on 1=1
 left JOIN (
  SELECT * FROM (
  SELECT 
  emp_id,
  rank() OVER (partition by pt order by l7d_actual_receipt_performance desc) as last_rank_ld7_actual_receipt_performance,
    rank() OVER (partition by pt order by l7d_norm_insurance_amt desc) last_rank_ld7_norm_insurance_amt
    ,rank() OVER (partition by pt order by sm_actual_receipt_performance desc) last_rank_sm_actual_receipt_performance
    ,rank() OVER (partition by pt order by sy_actual_receipt_performance desc) last_rank_sy_actual_receipt_performance
    ,rank() OVER (partition by pt order by sm_norm_insurance_amt desc) last_rank_sm_norm_insurance_amt
    ,rank() OVER (partition by pt order by sy_norm_insurance_amt desc) last_rank_sy_norm_insurance_amt
  from phoenix.ads_insurance_emp_index_progress_dfp where pt =TO_CHAR(CURRENT_DATE - INTERVAL '8 days', 'YYYYMMDD')) t
  WHERE t.emp_id='${userParam.mainJobNumber}'
  ) t4 on 1=1
 ;
"""
    ftlParam["datas"] = msgStatService.selectSafesPgMaps(sql)
    if(CollectionUtils.isEmpty(ftlParam["datas"] as Collection<?>)){
        return null
    }
    ftlParam["datasJson"] = JSON.toJSONString(ftlParam["datas"] )
    ftlParam["rptDate"] = DateTimeFormatter.ofPattern("MM月dd日").format(LocalDate.now().minusDays(1L))
    log.info("push params {} {}", rule.getRuleName(), ftlParam)
    return Html2ImageUtils.processTemplateInfoString(rule.getMessageTemplate(), ftlParam)
}