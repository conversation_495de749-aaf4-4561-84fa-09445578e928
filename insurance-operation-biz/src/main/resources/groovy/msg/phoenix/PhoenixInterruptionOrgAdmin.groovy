import com.alibaba.fastjson.JSON
import com.cfpamf.ms.insur.operation.msg.pojo.dto.OpMessageRuleGroovyDTO
import com.cfpamf.ms.insur.operation.msg.service.MsgStatService
import com.cfpamf.ms.insur.operation.msg.util.Html2ImageUtils
import org.apache.commons.collections4.CollectionUtils

import java.time.LocalDate
import java.time.format.DateTimeFormatter

String execute(OpMessageRuleGroovyDTO rule) {
    MsgStatService msgStatService = msgStatService;
    def userParam = rule.getReceiver().otherParams;
    def ftlParam = [:]
    //  查询推广费
    def sql = """
select *
from phoenix.dwa_safes_customer_interruption_user_statistics
where regionname = '${userParam.regionName}'  and un_renew_total>0
  and org = '${userParam.orgName}'
order by (case
              when un = '合计' then 2
              else 1 end),un

"""
    def datas = msgStatService.selectSafesPgMaps(sql)
    if(CollectionUtils.isEmpty(datas)){
        return null
    }
    ftlParam["titleName"] = userParam.orgName
    ftlParam["rptDate"] = DateTimeFormatter.ofPattern("MM月dd日").format(LocalDate.now().minusDays(1L))
    ftlParam["jsonDatas"] = JSON.toJSONString(datas)

    def res = Html2ImageUtils.processTemplateInfoString(rule.getMessageTemplate(), ftlParam)
    return res
}
