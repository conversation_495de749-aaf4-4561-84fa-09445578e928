package groovy.msg.honor

import com.alibaba.fastjson.JSON
import com.cfpamf.ms.insur.operation.base.constant.BaseConstants
import com.cfpamf.ms.insur.operation.msg.pojo.dto.OpMessageRuleGroovyDTO
import com.cfpamf.ms.insur.operation.msg.util.Html2ImageUtils
import org.apache.commons.collections4.CollectionUtils

import java.time.LocalDate
import java.time.YearMonth
import java.time.format.DateTimeFormatter
import java.time.temporal.IsoFields
import java.time.temporal.TemporalAdjusters
import java.util.stream.Collectors

Map execute(OpMessageRuleGroovyDTO rule) {

    def ftlParam = [:]
    def map = [1:"一",2:"二",3:"三",4:"四"]

    LocalDate lastDayOfPrevQuarter = getLastDayOfPreviousQuarter(LocalDate.now());
    // 获取上季度最后一天
    def pt = lastDayOfPrevQuarter.format(BaseConstants.FMT_DATE)
    // 获取历史季度
    List<LocalDate> quarterEndDates = getQuarterEndDatesOfYear().stream().filter({ date -> date.isBefore(lastDayOfPrevQuarter) }).collect(Collectors.toList());

    //  查询推广费
    def sql = """
select *
from (select
          pt,
        emp_code emp_id,
        emp_name,
          bch_name,
          area_name,
          round(sq_access_convert_insurance_amt /10000,1)                                    rank_value,
          rank() over (partition by pt order by sq_access_convert_insurance_amt  desc) rank_num
      from report.ads_insurance_emp_marketing_progress_dfp  
      where pt = '${pt}'
        and sq_access_convert_insurance_amt > 0
        and area_name like '%区域') t
where rank_num <= 20
"""
    ftlParam["datas"] = msgStatService.selectSafesPgMaps(sql)
    if (CollectionUtils.isEmpty(ftlParam["datas"] as Collection<?>)) {
        return null
    }
    def top3 = (ftlParam["datas"] as Collection<?>).stream().filter { it.rank_num <= 3 }.collect(Collectors.toList())

    def top3Emp = top3*.emp_id.collect { "'${it}'" }.join(",")
    def monthEndPts = quarterEndDates.collect { it.format(BaseConstants.FMT_DATE) }.collect { "'${it}'" }.join(",")
    def top3BeforeRanks = new ArrayList()
    if (!monthEndPts.isEmpty()) {
        def top3Sql = """select *
from (select
      pt,
    emp_code emp_id,
    emp_name,
      bch_name,
      area_name,
      round(sq_access_convert_insurance_amt /10000,1)                                    rank_value,
          rank() over (partition by pt order by sq_access_convert_insurance_amt  desc) rank_num
  from report.ads_insurance_emp_marketing_progress_dfp  
  where pt in (${monthEndPts})
    and sq_access_convert_insurance_amt > 0
    and area_name like '%区域') t
where  emp_id in (${top3Emp})"""
        top3BeforeRanks = msgStatService.selectSafesPgMaps(top3Sql) as Collection<?>
    }
    log.info('top3BeforeRanks {}', top3BeforeRanks)
    (ftlParam["datas"] as Collection<?>).stream().filter { it.rank_num <= 3 }.forEach {
        def sit = it
        sit["rankTimes"] = 1
        def empData = top3BeforeRanks.stream().filter { top3Data -> Objects.equals(sit.emp_id, top3Data.emp_id) }.collect(Collectors.toList())
        log.info('empData {}', empData)
        for (LocalDate yearMonth : quarterEndDates) {
            def prePt = yearMonth.format(BaseConstants.FMT_DATE)
            def preData = empData.find { Objects.equals(prePt, it.pt) }
            if (Objects.nonNull(preData) && preData.rank_num <= 20) {
                sit["rankTimes"] = sit["rankTimes"] + 1
            }
        }
    }
    def dingInfos = wecatService.getDingTalkInfo(top3*.emp_id)
    (ftlParam["datas"] as Collection<?>).stream().filter { it.rank_num <= 3 }.forEach {
        def we = dingInfos.find { dingInfo -> Objects.equals(dingInfo.wechatUserId, it.emp_id) }
        if (Objects.nonNull(we)){
            it["avatar"] = we.avatar
        }
    }
    def ftlMap = ["datasJson": JSON.toJSONString(ftlParam)]
    ftlMap["quarter"] = map.get(lastDayOfPrevQuarter.get(IsoFields.QUARTER_OF_YEAR))
    ftlMap["firstDay"] = getQuarterFirstDay(lastDayOfPrevQuarter)
    ftlMap["lastDay"] = lastDayOfPrevQuarter.format(DateTimeFormatter.ofPattern("yyyy.MM.dd"))
    def top3List = (ftlParam["datas"] as Collection<?>).stream().filter { it.rank_num <= 3 }.collect(Collectors.toList())
    ftlMap["datas"] = top3List.collect {obj->
        obj["emp"] = obj.area_name.replaceAll("区域", "")+""+obj.bch_name+"-"+obj.emp_name
        return obj
    }
    log.info("RankEmpAmt json is {}", ftlMap.datasJson)
    return ftlMap
}

LocalDate getLastDayOfPreviousQuarter(LocalDate date) {
    // 获取当前季度
    int currentQuarter = date.get(IsoFields.QUARTER_OF_YEAR);

    // 计算上个季度的最后一个月份
    int endMonthOfPrevQuarter = (currentQuarter - 1) * 3;
    if (endMonthOfPrevQuarter <= 0) { // 如果是第一季度，需要调整到去年的第四季度
        endMonthOfPrevQuarter += 12;
        date = date.minusYears(1); // 年份减一
    }

    // 设置为上个季度最后一个月的第一天，然后调整到该月的最后一天
    LocalDate lastDay = date.withMonth(endMonthOfPrevQuarter).with(TemporalAdjusters.lastDayOfMonth());

    return lastDay;
}

List<LocalDate> getQuarterEndDatesOfYear() {
    int year = LocalDate.now().minusDays(1).getYear();
    List<LocalDate> endDates = new ArrayList<>();

    // 第一季度末
    LocalDate q1End = LocalDate.of(year, 3, 31);
    endDates.add(q1End);

    // 第二季度末
    LocalDate q2End = LocalDate.of(year, 6, 30);
    endDates.add(q2End);

    // 第三季度末
    LocalDate q3End = LocalDate.of(year, 9, 30);
    endDates.add(q3End);

    // 第四季度末
    LocalDate q4End = LocalDate.of(year, 12, 31);
    endDates.add(q4End);

    return endDates;
}

String getQuarterFirstDay(LocalDate date) {
    int quarter = date.get(IsoFields.QUARTER_OF_YEAR);
    int month = (quarter - 1) * 3 + 1;
    return LocalDate.of(date.getYear(), month, 1).format(DateTimeFormatter.ofPattern("yyyy.MM.dd"));
}
