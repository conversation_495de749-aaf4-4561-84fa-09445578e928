package groovy.msg.honor

import com.alibaba.fastjson.JSON
import com.cfpamf.ms.insur.operation.base.constant.BaseConstants
import com.cfpamf.ms.insur.operation.msg.pojo.dto.OpMessageRuleGroovyDTO
import java.time.format.DateTimeFormatter
import org.apache.commons.collections4.CollectionUtils

import java.time.LocalDate
import java.time.temporal.IsoFields
import java.time.temporal.TemporalAdjusters
import java.util.stream.Collectors

Map execute(OpMessageRuleGroovyDTO rule) {

    def ftlParam = [:]

    def map = [1:"一",2:"二",3:"三",4:"四"]

    LocalDate lastDayOfPrevQuarter = getLastDayOfPreviousQuarter(LocalDate.now());
    // 获取上季度最后一天
    def pt = lastDayOfPrevQuarter.format(BaseConstants.FMT_DATE)
    //  查询推广费
    def sql = """
select *
from (select
          pt,
          bch_name,
          area_name,
          round(sq_access_convert_insurance_amt,1)                                    sq_access_convert_insurance_amt,
          cast(sq_assess_convert_insurance_amt_avg as int)                                    sq_assess_convert_insurance_amt_avg,
          rank() over (partition by pt order by sq_assess_convert_insurance_amt_avg desc) rank_num
      from report.ads_insurance_bch_marketing_progress_dfp  
      where pt = '${pt}'
        and sq_assess_convert_insurance_amt_avg > 0 and area_name like '%区域' and ms_emp_cnt_gt_3 = 1) t
where rank_num <= 20 order by sq_assess_convert_insurance_amt_avg desc,sq_access_convert_insurance_amt desc
"""
    ftlParam["datas"] = msgStatService.selectSafesPgMaps(sql)
    if (CollectionUtils.isEmpty(ftlParam["datas"] as Collection<?>)) {
        return null
    }
    def areaNameGroup = ftlParam["datas"]*.area_name.groupBy { it }.collect { key, value -> [key, value.size()] }.sort { -it[1] }
    def ftlMap = ["datasJson": JSON.toJSONString(ftlParam)]


    ftlMap["maxAreaName"] = areaNameGroup[0][0]
    ftlMap["maxAreaPre"] = areaNameGroup[0][1]
    ftlMap["maxAreaRate"] = areaNameGroup[0][1] ? Math.round((areaNameGroup[0][1]/ftlParam["datas"].size() ) * 100) : 0
    ftlMap["areaNames"] = ftlParam["datas"].collect { it.area_name.replaceAll("区域", "") }.unique().join("、")
    ftlMap["bchSize"] = (ftlParam["datas"] as Collection<?>).size()

    def top1 = (ftlParam["datas"] as Collection<?>).stream().filter { it.rank_num <= 1 }.collect { "${it.area_name}-${it.bch_name}" }
    def top1BchNames = top1.join(",")
    ftlMap["quarter"] = map.get(lastDayOfPrevQuarter.get(IsoFields.QUARTER_OF_YEAR))
    ftlMap["top1BchNames"] = top1BchNames
    ftlMap["top1Amt"] = (ftlParam["datas"] as Collection<?>).stream().filter { it.rank_num <= 1 }.collect { it.sq_assess_convert_insurance_amt_avg }[0]
    ftlMap["firstDay"] = getQuarterFirstDay(lastDayOfPrevQuarter)
    ftlMap["lastDay"] = lastDayOfPrevQuarter.format(DateTimeFormatter.ofPattern("yyyy.MM.dd"))
    log.info("HonorBchAmt json is {}", ftlMap)
    return ftlMap
}

LocalDate getLastDayOfPreviousQuarter(LocalDate date) {
    // 获取当前季度
    int currentQuarter = date.get(IsoFields.QUARTER_OF_YEAR);

    // 计算上个季度的最后一个月份
    int endMonthOfPrevQuarter = (currentQuarter - 1) * 3;
    if (endMonthOfPrevQuarter <= 0) { // 如果是第一季度，需要调整到去年的第四季度
        endMonthOfPrevQuarter += 12;
        date = date.minusYears(1); // 年份减一
    }

    // 设置为上个季度最后一个月的第一天，然后调整到该月的最后一天
    LocalDate lastDay = date.withMonth(endMonthOfPrevQuarter).with(TemporalAdjusters.lastDayOfMonth());

    return lastDay;
}

String getQuarterFirstDay(LocalDate date) {
    int quarter = date.get(IsoFields.QUARTER_OF_YEAR);
    int month = (quarter - 1) * 3 + 1;
    return LocalDate.of(date.getYear(), month, 1).format(DateTimeFormatter.ofPattern("yyyy.MM.dd"));
}