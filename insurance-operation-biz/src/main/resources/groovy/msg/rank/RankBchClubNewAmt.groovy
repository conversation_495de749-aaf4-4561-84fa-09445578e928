import com.alibaba.fastjson.JSON
import com.cfpamf.ms.insur.operation.base.constant.BaseConstants
import com.cfpamf.ms.insur.operation.msg.pojo.dto.OpMessageRuleGroovyDTO
import com.cfpamf.ms.insur.operation.msg.util.Html2ImageUtils
import org.apache.commons.collections4.CollectionUtils

import java.time.LocalDate
import java.time.YearMonth
import java.util.stream.Collectors

String execute(OpMessageRuleGroovyDTO rule) {

    def ftlParam = [:]

    def yesterday = LocalDate.now().minusDays(1L)
    def yearMonth = YearMonth.from(yesterday)
    def pt = yesterday.format(BaseConstants.FMT_DATE)
    //  新秀骑士 2023年1月1日后入职的新员工	年度标准业绩≥5万入围，排名前10名
    def sql = """
select *
from (select
          pt,
          bch_name,
          area_name,
 emp_name,
          round(sy_norm_insurance_amt/10000,1)                                    rank_value,
          rank() over (partition by pt order by sy_norm_insurance_amt desc) rank_num
      from phoenix.ads_insurance_emp_index_progress_dfp
      where pt = '${pt}'
        and sy_norm_insurance_amt >= 50000 and join_date>='2023-01-01' ) t
where rank_num <= 10 
"""
    ftlParam["datas"] = msgStatService.selectSafesPgMaps(sql)
    if (CollectionUtils.isEmpty(ftlParam["datas"] as Collection<?>)) {
       log.warn(" 新秀骑士 2023年1月1日后入职的新员工 年度标准业绩≥10万入围 没有符合要求的数据")
        return null
    }
    def areaNameGroup = ftlParam["datas"]*.area_name.groupBy { it }.collect { key, value -> [key, value.size()] }.sort { -it[1] }
    ftlParam["maxAreaName"] = areaNameGroup[0][0]
    ftlParam["maxAreaPre"] = areaNameGroup[0][1]
    ftlParam["areaNames"] = ftlParam["datas"]*.area_name.stream().distinct().collect(Collectors.toList())
    ftlParam["yearMonth"] = yearMonth
    ftlParam["pt"] = yesterday
    def ftlMap = ["datasJson": JSON.toJSONString(ftlParam)]
    log.info("RankBchClubNewAmt json is {}", ftlMap.datasJson)
    return Html2ImageUtils.processTemplateInfoString(rule.getMessageTemplate(), ftlMap)
}
