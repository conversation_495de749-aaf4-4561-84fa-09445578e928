import com.alibaba.fastjson.JSON
import com.cfpamf.ms.insur.operation.base.constant.BaseConstants
import com.cfpamf.ms.insur.operation.msg.pojo.dto.OpMessageRuleGroovyDTO
import com.cfpamf.ms.insur.operation.msg.util.Html2ImageUtils
import org.apache.commons.collections4.CollectionUtils

import java.time.YearMonth
import java.util.stream.Collectors

String execute(OpMessageRuleGroovyDTO rule) {

    def ftlParam = [:]

    def lastMonth = YearMonth.now().minusMonths(1L);
    def pt = lastMonth.atEndOfMonth().format(BaseConstants.FMT_DATE)
    //  查询推广费
    def sql = """
select *
from (select
          pt,
          bch_name,
          area_name,
          sm_land_customer_cnt                                    rank_value,
          rank() over (partition by pt order by sm_land_customer_cnt desc) rank_num
      from phoenix.ads_insurance_bch_index_progress_dfp
      where pt = '${pt}'
        and sm_land_customer_cnt > 0 and area_name like '%区域') t
where rank_num <= 30
"""
    ftlParam["datas"] = msgStatService.selectSafesPgMaps(sql)
    if (CollectionUtils.isEmpty(ftlParam["datas"] as Collection<?>)) {
        return null
    }
    def areaNameGroup = ftlParam["datas"]*.area_name.groupBy { it }.collect { key, value -> [key, value.size()] }.sort { -it[1] }
    ftlParam["maxAreaName"] = areaNameGroup[0][0]
    ftlParam["maxAreaPre"] = areaNameGroup[0][1]
    ftlParam["areaNames"] = ftlParam["datas"]*.area_name.stream().distinct().collect(Collectors.toList())
    ftlParam["yearMonth"] = lastMonth
    def ftlMap = ["datasJson": JSON.toJSONString(ftlParam)]
    log.info("RankBchCust json is {}", ftlMap.datasJson)
    return Html2ImageUtils.processTemplateInfoString(rule.getMessageTemplate(), ftlMap)
}
