import com.alibaba.fastjson.JSON
import com.cfpamf.ms.insur.operation.base.constant.BaseConstants
import com.cfpamf.ms.insur.operation.msg.pojo.dto.OpMessageRuleGroovyDTO
import com.cfpamf.ms.insur.operation.msg.util.Html2ImageUtils
import org.apache.commons.collections4.CollectionUtils

import java.time.LocalDate
import java.time.Month
import java.time.YearMonth
import java.time.format.DateTimeFormatter
import java.util.stream.Collectors

Object execute(OpMessageRuleGroovyDTO rule) {

    def ftlParam = [:]

    def yesterday = LocalDate.now().minusDays(1L)
    def yearMonth = YearMonth.from(yesterday)
    def pt = yesterday.format(BaseConstants.FMT_DATE)
    def firstDay = getQuarterFirstDay(yesterday)
    def lastDay = yesterday.format(DateTimeFormatter.ofPattern("yyyy.MM.dd"))

    def sql = """
                    select area_name,
                    area_code ,
                    bch_name,
                    bch_code,
                    round(sq_assess_convert_insurance_amt_avg,0) sq_assess_convert_insurance_amt_avg,
                    round(sq_access_convert_insurance_amt/10000,1) sq_access_convert_insurance_amt,
                    rank
                    from (SELECT t.area_name ,t.area_code,t.bch_name ,t.bch_code  ,t.sq_assess_convert_insurance_amt_avg,t.sq_access_convert_insurance_amt,
                        rank() over(order by t.sq_assess_convert_insurance_amt_avg desc) rank
                    FROM report."ads_insurance_bch_marketing_progress_dfp" t where t.pt ='${pt}') a
                    where a.rank <=30 order by a.rank,a.sq_access_convert_insurance_amt desc
                    """
    ftlParam["datas"] = msgStatService.selectSafesPgMaps(sql)
    if (CollectionUtils.isEmpty(ftlParam["datas"] as Collection<?>)) {
        return null
    }
    def areaNameGroup = ftlParam["datas"]*.area_name.groupBy { it }.collect { key, value -> [key, value.size()] }.sort { -it[1] }
    ftlParam["maxAreaName"] = areaNameGroup[0][0]
    ftlParam["maxAreaPre"] = areaNameGroup[0][1]
    ftlParam["areaNames"] = ftlParam["datas"]*.area_name.stream().distinct().collect(Collectors.toList())
    ftlParam["yearMonth"] = yearMonth
    def ftlMap = ["datasJson": JSON.toJSONString(ftlParam)]
    ftlMap["quarter"] = getQuarter(yesterday)
    ftlMap["maxAreaName"] = areaNameGroup[0][0]
    ftlMap["maxAreaPre"] = areaNameGroup[0][1]
    ftlMap["firstDay"] = firstDay
    ftlMap["lastDay"] = lastDay
    ftlMap["firstBch"] = ftlParam["datas"].findAll({ it.rank == 1 })
    log.info("HonerBchPre json is {}", ftlMap.datasJson)
    return ftlMap
}
String getQuarter(LocalDate date) {
    Month month = date.getMonth();
    if (month.getValue() >= 1 && month.getValue() <= 3) {
        return "一"; // 第一季度
    } else if (month.getValue() >= 4 && month.getValue() <= 6) {
        return "二"; // 第二季度
    } else if (month.getValue() >= 7 && month.getValue() <= 9) {
        return "三"; // 第三季度
    } else {
        return "四"; // 第四季度
    }
}

String getQuarterFirstDay(LocalDate date) {
    int quarter = getQuarterValue(date);
    int month = (quarter - 1) * 3 + 1;
    return LocalDate.of(date.getYear(), month, 1).format(DateTimeFormatter.ofPattern("yyyy.MM.dd"));
}

/**
 * 获取指定日期所在季度的最后一天。
 * @param date LocalDate对象
 * @return 该季度的最后一天
 */
String getQuarterLastDay(LocalDate date) {
    int quarter = getQuarterValue(date);
    int month = (quarter * 3) % 12 == 0 ? 12 : (quarter * 3) % 12;
    int year = month == 12 ? date.getYear() + 1 : date.getYear();
    return LocalDate.of(year, month, month == 12 ? 31 : (month == 2 ? 28 : 30)).format(DateTimeFormatter.ofPattern("yyyy.MM.dd"));
}

int getQuarterValue(LocalDate date) {
    Month month = date.getMonth();
    if (month.getValue() <= 3) return 1;
    if (month.getValue() <= 6) return 2;
    if (month.getValue() <= 9) return 3;
    return 4;
}