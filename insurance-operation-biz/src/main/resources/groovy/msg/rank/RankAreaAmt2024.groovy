package groovy.msg.honor

import com.alibaba.fastjson.JSON
import com.cfpamf.ms.insur.operation.base.constant.BaseConstants
import com.cfpamf.ms.insur.operation.msg.pojo.dto.OpMessageRuleGroovyDTO
import org.apache.commons.collections4.CollectionUtils

import java.time.LocalDate
import java.time.format.DateTimeFormatter
import java.time.temporal.IsoFields
import java.time.temporal.TemporalAdjusters
import java.util.stream.Collectors
import java.lang.Character

Map execute(OpMessageRuleGroovyDTO rule) {

    def map = [1:"一",2:"二",3:"三",4:"四"]

    def ftlParam = []

    def id = 17

    LocalDate lastDayOfPrevQuarter = getLastDayOfPreviousQuarter(LocalDate.now());
    // 获取上季度最后一天
    def pt = lastDayOfPrevQuarter.format(BaseConstants.FMT_DATE)
    // 获取历史季度
    List<LocalDate> quarterEndDates = getQuarterEndDatesOfYear().stream().filter({ date -> date.isBefore(lastDayOfPrevQuarter) }).collect(Collectors.toList());
    def historyRank = getHistoryAvgRank(quarterEndDates)

    def importResultSafes = getImportResult(id);

    log.info("importResultSafes========== {}",JSON.toJSONString(importResultSafes))

    if(Objects.isNull(id) || org.springframework.util.CollectionUtils.isEmpty(importResultSafes)){
        def sql ="""SELECT
                          RANK() OVER (order by a1.sq_assess_convert_insurance_amt_avg desc) ranks,
                          a1.area_code,
                          a1.area_name,
                          cast(a1.sq_assess_convert_insurance_amt_avg as int) sq_assess_convert_insurance_amt_avg,
                          round(a1.sq_access_convert_insurance_amt/10000,1) sq_assess_convert_insurance_amt
                        FROM
                          report.ads_insurance_area_marketing_progress_dfp a1
                          JOIN (
                            SELECT MIN-- 获取每月第三名的标准保费值
                            ( A.sq_assess_convert_insurance_amt_avg ) sq_assess_convert_insurance_amt_avg,
                            A.pt 
                          FROM
                            (
                            SELECT
                              sq_assess_convert_insurance_amt_avg,
                              t1.pt 
                            FROM
                              report.ads_insurance_area_marketing_progress_dfp t1 
                            WHERE
                              t1.pt = '${pt}' and t1.ms_emp_cnt_gt_3 = 1
                            ORDER BY
                              sq_assess_convert_insurance_amt_avg DESC 
                             LIMIT 5 
                            ) A 
                          GROUP BY
                            A.pt 
                          ) a2 ON a1.pt = a2.pt 
                          AND a1.sq_assess_convert_insurance_amt_avg >= a2.sq_assess_convert_insurance_amt_avg 
                          and a1.ms_emp_cnt_gt_3 = 1
                        ORDER BY
                          a1.sq_assess_convert_insurance_amt_avg desc,a1.sq_access_convert_insurance_amt DESC
                        """

        ftlParam = msgStatService.selectSafesPgMaps(sql)
        if (CollectionUtils.isEmpty(ftlParam)) {
            log.info("区域风云榜{}日期没数据",pt )
            return null
        }
    }else{
        ftlParam = importResultSafes
        log.info("importResultSafes 2222 ftlParam========== {}",JSON.toJSONString(ftlParam))
    }

    log.info("importResultSafes ftlParam========== {}",JSON.toJSONString(ftlParam))

    def top1 = (ftlParam as Collection<?>).stream().filter { it.ranks == 1 }.collect(Collectors.toList())
    firstRank(top1,historyRank) //计算霸榜次数
    qualifyTimes(ftlParam,historyRank) //计算入围次数
    def ftlMap = ["datasJson": JSON.toJSONString(ftlParam)]
    ftlMap["quarter"] = map.get(lastDayOfPrevQuarter.get(IsoFields.QUARTER_OF_YEAR))
    ftlMap["firstDay"] = getQuarterFirstDay(lastDayOfPrevQuarter)
    ftlMap["lastDay"] = lastDayOfPrevQuarter.format(DateTimeFormatter.ofPattern("yyyy.MM.dd"))
    ftlMap["datas"] = ftlParam
    log.info("RankAreaAmt json is {}", ftlMap)
    return ftlMap
}

void firstRank(def firstAreas,def historyRank){
    for (def firstArea in firstAreas) {
        def areaCode = firstArea["area_code"]
        int times = 1
        for (def pt in historyRank.keySet()){
            historyFirstRank = historyRank[pt][0]
            if(historyFirstRank["area_code"]!= areaCode){
                break
            }else{
                times++
            }
        }
        firstArea["firstTime"]=times
    }

}

void qualifyTimes(def ftlParam,def historyRank){
    for(int i = 0 ;i<ftlParam.size();i++){
        def areaCode = ftlParam[i]["area_code"]
        int times = 1
        for (def pt in historyRank.keySet()){
            historyFirstRank = historyRank[pt].each {
                if(it["area_code"]== areaCode){
                    times ++
                }
            }
        }
        ftlParam[i]["qualifyTimes"] = times;
    }
}

LinkedHashMap getHistoryAvgRank(List<LocalDate> lastQuarterEndDates){
    def ftlParam = [:]
    def hisPt = [];
    for (LocalDate date:lastQuarterEndDates){
        def pt = date.format(BaseConstants.FMT_DATE)
        def sql ="""SELECT
                          RANK() OVER (order by a1.sq_assess_convert_insurance_amt_avg desc) ranks,
                          a1.area_code,
                          a1.area_name,
                          trim(to_char(a1.sq_assess_convert_insurance_amt_avg,'99999999999D99')) sq_assess_convert_insurance_amt_avg,
                          trim(to_char(a1.sq_access_convert_insurance_amt/10000,'9999999999D99')) sq_assess_convert_insurance_amt
                        FROM
                          report.ads_insurance_area_marketing_progress_dfp a1
                          JOIN (
                            SELECT MIN-- 获取每月第三名的标准保费值
                            ( A.sq_assess_convert_insurance_amt_avg ) sq_assess_convert_insurance_amt_avg,
                            A.pt 
                          FROM
                            (
                            SELECT
                              sq_assess_convert_insurance_amt_avg,
                              t1.pt 
                            FROM
                              report.ads_insurance_area_marketing_progress_dfp t1 
                            WHERE
                              t1.pt = '${pt}' and t1.ms_emp_cnt_gt_3 = 1
                            ORDER BY
                              sq_assess_convert_insurance_amt_avg DESC 
                             LIMIT 5 
                            ) A 
                          GROUP BY
                            A.pt 
                          ) a2 ON a1.pt = a2.pt 
                          AND a1.sq_assess_convert_insurance_amt_avg >= a2.sq_assess_convert_insurance_amt_avg 
                          and a1.ms_emp_cnt_gt_3 = 1
                        ORDER BY
                          a1.sq_assess_convert_insurance_amt_avg DESC"""
        ftlParam[pt] = msgStatService.selectSafesPgMaps(sql)
    }
    log.info("ftlParam json is {}", JSON.toJSONString(ftlParam))
    return  ftlParam
}

LocalDate getLastDayOfPreviousQuarter(LocalDate date) {
    // 获取当前季度
    int currentQuarter = date.get(IsoFields.QUARTER_OF_YEAR);

    // 计算上个季度的最后一个月份
    int endMonthOfPrevQuarter = (currentQuarter - 1) * 3;
    if (endMonthOfPrevQuarter <= 0) { // 如果是第一季度，需要调整到去年的第四季度
        endMonthOfPrevQuarter += 12;
        date = date.minusYears(1); // 年份减一
    }

    // 设置为上个季度最后一个月的第一天，然后调整到该月的最后一天
    LocalDate lastDay = date.withMonth(endMonthOfPrevQuarter).with(TemporalAdjusters.lastDayOfMonth());

    return lastDay;
}

List<LocalDate> getQuarterEndDatesOfYear() {
    int year = LocalDate.now().minusDays(1).getYear();
    List<LocalDate> endDates = new ArrayList<>();

    // 第一季度末
    LocalDate q1End = LocalDate.of(year, 3, 31);
    endDates.add(q1End);

    // 第二季度末
    LocalDate q2End = LocalDate.of(year, 6, 30);
    endDates.add(q2End);

    // 第三季度末
    LocalDate q3End = LocalDate.of(year, 9, 30);
    endDates.add(q3End);

    // 第四季度末
    LocalDate q4End = LocalDate.of(year, 12, 31);
    endDates.add(q4End);

    return endDates;
}

String getQuarterFirstDay(LocalDate date) {
    int quarter = date.get(IsoFields.QUARTER_OF_YEAR);
    int month = (quarter - 1) * 3 + 1;
    return LocalDate.of(date.getYear(), month, 1).format(DateTimeFormatter.ofPattern("yyyy.MM.dd"));
}

def getImportResult(def id){
    if(Objects.isNull(id)){
        return []
    }
    def sqlSafes = """
        select
            t1.rank_num ranks
            ,t1.area_code
            ,t1.area_name
            ,TRUNCATE(t1.sq_assess_convert_insurance_amt_avg,0) sq_assess_convert_insurance_amt_avg
            ,round(t1.sq_access_convert_insurance_amt/10000,1) sq_assess_convert_insurance_amt
        from honors_selection_results t1
        where t1.honors_rules_configurations_id = ${id} and t1.enabled_flag = 0
        order by rank_num asc,t1.id asc;
   """
    log.info("==========================sqlSafes================================"+sqlSafes)
    def importResultSafes = msgStatService.selectMaps(sqlSafes);
    return importResultSafes
}