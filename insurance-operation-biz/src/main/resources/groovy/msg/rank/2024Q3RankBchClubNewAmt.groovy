import com.alibaba.fastjson.JSON
import com.cfpamf.ms.insur.operation.base.constant.BaseConstants
import com.cfpamf.ms.insur.operation.msg.pojo.dto.OpMessageRuleGroovyDTO
import com.cfpamf.ms.insur.operation.msg.util.Html2ImageUtils
import org.apache.commons.collections4.CollectionUtils

import java.time.LocalDate
import java.time.YearMonth
import java.time.format.DateTimeFormatter

Map execute(OpMessageRuleGroovyDTO rule) {

    def ftlParam = [:]

    def yesterday = LocalDate.now().minusDays(1L)
    def yearMonth = YearMonth.from(yesterday)
    def pt = yesterday.format(BaseConstants.FMT_DATE)
    DateTimeFormatter yyyyMMdd = DateTimeFormatter.ofPattern("yyyy.MM.dd")
    def endDate = yesterday.format(yyyyMMdd)
    //  骑士俱乐部	黄金骑士	员工	年度标准业绩≥11万入围
    def fee = 50000
    def sql = """
	select tmp.* from (
		select
		a.area_name,
		a.emp_code,
        a.emp_name,
	  a.bch_name,
	  a.area_name,
		sum(a.sy_assess_convert_insurance_amt) sy_norm_insurance_amt,
	  round(sum(a.sy_assess_convert_insurance_amt)/10000,1)                                    rank_value,
	  rank() over(order by sum(a.sy_assess_convert_insurance_amt)  desc nulls LAST )  rank_num
	from report.ads_insurance_emp_marketing_progress_dfp a
    left join phoenix.ads_insurance_emp_index_progress_dfp b on a.emp_code = b.emp_id
	where a.pt = '${pt}'
    and b.pt = '${pt}'
    and a.bch_name is not null
	and b.join_date>='2024-01-01'
	group by a.area_code,a.area_name,a.bch_code,a.bch_name,a.emp_name,a.emp_code
	) tmp where tmp.sy_norm_insurance_amt>=${fee}
"""
    log.info("sql=============== {}",sql)
    def queryResult = msgStatService.selectSafesPgMaps(sql);
    queryResult.forEach {
        def recommendEmpId = it["emp_code"]
        def dingInfos = wecatService.getDingTalkInfo([recommendEmpId])
        def we = dingInfos.find { dingInfo -> Objects.equals(dingInfo.wechatUserId, recommendEmpId) }
        if (Objects.nonNull(we)) {
            it["avatar"] = we.avatar
        }
    }
    ftlParam["data"] = queryResult
    if (CollectionUtils.isEmpty(ftlParam["data"] as Collection<?>)) {
        return null
    }
    // 列表小于3时候不发送
    if (ftlParam["data"].size() < 3) {
        return null
    }
    log.info("ftlParam============= {}",JSON.toJSONString(ftlParam))
    def ftlMap = ["datas": JSON.toJSONString(ftlParam["data"])]
    ftlMap["time"] = endDate
    log.info("ftlMap ============ {}", JSON.toJSONString(ftlMap))
    return ftlMap
}
