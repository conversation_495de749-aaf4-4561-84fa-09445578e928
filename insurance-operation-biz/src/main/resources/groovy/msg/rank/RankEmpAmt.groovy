import com.alibaba.fastjson.JSON
import com.cfpamf.ms.insur.operation.base.constant.BaseConstants
import com.cfpamf.ms.insur.operation.msg.pojo.dto.OpMessageRuleGroovyDTO
import com.cfpamf.ms.insur.operation.msg.util.Html2ImageUtils
import org.apache.commons.collections4.CollectionUtils

import java.time.YearMonth
import java.util.stream.Collectors

String execute(OpMessageRuleGroovyDTO rule) {

    def ftlParam = [:]

    def lastMonth = YearMonth.now().minusMonths(1L);
    def pt = lastMonth.atEndOfMonth().format(BaseConstants.FMT_DATE)
    //  查询推广费
    def sql = """
select *
from (select
          pt,
        emp_id,
        emp_name,
          bch_name,
          area_name,
          round(sm_norm_insurance_amt /10000,1)                                    rank_value,
          rank() over (partition by pt order by sm_norm_insurance_amt  desc) rank_num
      from phoenix.ads_insurance_emp_index_progress_dfp
      where pt = '${pt}'
        and sm_norm_insurance_amt > 0 and is_branch = 1 ) t
where rank_num <= 20
"""
    ftlParam["datas"] = msgStatService.selectSafesPgMaps(sql)
    if (CollectionUtils.isEmpty(ftlParam["datas"] as Collection<?>)) {
        return null
    }
    def top3 = (ftlParam["datas"] as Collection<?>).stream().filter { it.rank_num <= 3 }.collect(Collectors.toList())

    if (lastMonth.getMonthValue() > 1) {
        def top3Emp = top3*.emp_id.collect { "'${it}'" }.join(",")
        List<YearMonth> yearMonths = []
        YearMonth currentYearMonth = lastMonth

        (lastMonth.getMonthValue() - 1..1).each { month ->
            yearMonths << YearMonth.of(currentYearMonth.getYear(), month)
        }
        def monthEndPts = yearMonths.collect { it.atEndOfMonth().format(BaseConstants.FMT_DATE) }.collect { "'${it}'" }.join(",")
        def top3Sql = """select *
from (select pt,
             emp_id,
             emp_name,
             bch_name,
             area_name,
             round(sm_norm_insurance_amt / 10000, 1)                           rank_value,
             rank() over (partition by pt order by sm_norm_insurance_amt desc nulls last) rank_num
      from phoenix.ads_insurance_emp_index_progress_dfp
      where  pt in (${monthEndPts})   and sm_norm_insurance_amt>0
        and is_branch = 1) t
where  emp_id in (${top3Emp})"""
        def top3BeforeRanks = msgStatService.selectSafesPgMaps(top3Sql) as Collection<?>
        log.info('top3BeforeRanks {}', top3BeforeRanks)
        (ftlParam["datas"] as Collection<?>).stream().filter { it.rank_num <= 3 }.forEach {
            def sit = it
            sit["rankTimes"] = 1
            def empData = top3BeforeRanks.stream().filter { top3Data -> Objects.equals(sit.emp_id, top3Data.emp_id) }.collect(Collectors.toList())
            log.info('empData {}', empData)
            for (YearMonth yearMonth : yearMonths) {
                def prePt = yearMonth.atEndOfMonth().format(BaseConstants.FMT_DATE)
                def preData = empData.find { Objects.equals(prePt, it.pt) }
                if (Objects.nonNull(preData) && preData.rank_num <= 30) {
                    sit["rankTimes"] = sit["rankTimes"] + 1
                }
            }
        }
    }
    def dingInfos = wecatService.getDingTalkInfo(top3*.emp_id)
    (ftlParam["datas"] as Collection<?>).stream().filter { it.rank_num <= 3 }.forEach {
        def we = dingInfos.find { dingInfo -> Objects.equals(dingInfo.wechatUserId, it.emp_id) }
        if (Objects.nonNull(we)){
            it["avatar"] = we.avatar
        }
    }
    ftlParam["yearMonth"] = lastMonth
    def ftlMap = ["datasJson": JSON.toJSONString(ftlParam)]
    log.info("RankEmpAmt json is {}", ftlMap.datasJson)
    return Html2ImageUtils.processTemplateInfoString(rule.getMessageTemplate(), ftlMap)
}

