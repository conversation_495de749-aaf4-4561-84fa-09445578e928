import com.alibaba.fastjson.JSON
import com.cfpamf.ms.insur.operation.base.constant.BaseConstants
import com.cfpamf.ms.insur.operation.msg.pojo.dto.OpMessageRuleGroovyDTO
import com.cfpamf.ms.insur.operation.msg.util.Html2ImageUtils
import org.apache.commons.collections4.CollectionUtils

import java.time.YearMonth

String execute(OpMessageRuleGroovyDTO rule) {

    def ftlParam = []

    def lastMonth = YearMonth.now().minusMonths(1L);
    def pt = lastMonth.atEndOfMonth().format(BaseConstants.FMT_DATE)
    def lastMonthValue = lastMonth.getMonthValue();
    def hisPt = [];
    for (int i = 0; i<lastMonthValue; i++){
        hisPt.add(lastMonth.minusMonths(lastMonthValue-i).getMonthValue())
    }
    def historyRank = getHistoryAvgRank(lastMonthValue, lastMonth)
    def sql ="""SELECT
                          RANK() OVER (order by a1.sm_norm_insurance_amt / a1.ms_emp_cnt desc) rank,
                          a1.area_code,
                          a1.area_name,
                          trim(to_char(a1.sm_norm_insurance_amt / a1.ms_emp_cnt,'99999999999D99')) sm_norm_insurance_amt_avg,
                          trim(to_char(a1.sm_norm_insurance_amt/10000,'9999999999D99')) sm_norm_insurance_amt
                        FROM
                          phoenix.ads_insurance_area_index_progress_dfp a1
                          JOIN (
                            SELECT MIN-- 获取每月第三名的标准保费值
                            ( A.sm_norm_insurance_amt_avg ) sm_norm_insurance_amt_avg,
                            A.pt 
                          FROM
                            (
                            SELECT
                              t1.sm_norm_insurance_amt / t1.ms_emp_cnt sm_norm_insurance_amt_avg,
                              t1.pt 
                            FROM
                              phoenix.ads_insurance_area_index_progress_dfp t1 
                            WHERE
                              t1.pt = '${pt}' 
                            ORDER BY
                              t1.sm_norm_insurance_amt / t1.ms_emp_cnt DESC 
                             LIMIT 3 
                            ) A 
                          GROUP BY
                            A.pt 
                          ) a2 ON a1.pt = a2.pt 
                          AND a1.sm_norm_insurance_amt / a1.ms_emp_cnt >= a2.sm_norm_insurance_amt_avg 
                        ORDER BY
                          a1.sm_norm_insurance_amt / a1.ms_emp_cnt DESC 
                        """

    ftlParam = msgStatService.selectSafesPgMaps(sql)
    if (CollectionUtils.isEmpty(ftlParam)) {
        return null
    }
    firstRank(ftlParam[0],historyRank) //计算霸榜次数
    qualifyTimes(ftlParam,historyRank) //计算入围次数
    def ftlMap = ["datasJson": JSON.toJSONString(ftlParam)]
    ftlMap.put("month",lastMonthValue)
    log.info("RankAreaAmt json is {}", ftlMap)
    return Html2ImageUtils.processTemplateInfoString(rule.getMessageTemplate(), ftlMap)
}

void firstRank(def firstArea,def historyRank){
    def areaCode = firstArea["area_code"]
    int times = 1
    for (def pt in historyRank.keySet()){
        historyFirstRank = historyRank[pt][0]
        if(historyFirstRank["area_code"]!= areaCode){
            break
        }else{
            times++
        }
    }
    firstArea["firstTime"]=times

}

void qualifyTimes(def ftlParam,def historyRank){
    for(int i = 0 ;i<ftlParam.size();i++){
        def areaCode = ftlParam[i]["area_code"]
        int times = 1
        for (def pt in historyRank.keySet()){
            historyFirstRank = historyRank[pt].each {
                if(it["area_code"]== areaCode){
                    times ++
                }
            }
        }
        ftlParam[i]["qualifyTimes"] = times;
    }
}

LinkedHashMap getHistoryAvgRank(int lastMonthValue,YearMonth lastMonth){
    def ftlParam = [:]
    def hisPt = [];
    for (int i = 1; i<lastMonthValue; i++){
        def pt = lastMonth.minusMonths(i).atEndOfMonth().format(BaseConstants.FMT_DATE)
        def sql ="""SELECT
                          a1.area_code,
                          a1.area_name,
                          a1.sm_norm_insurance_amt / a1.ms_emp_cnt sm_norm_insurance_amt_avg 
                        FROM
                          phoenix.ads_insurance_area_index_progress_dfp a1
                          JOIN (
                            SELECT MIN-- 获取每月第三名的标准保费值
                            ( A.sm_norm_insurance_amt_avg ) sm_norm_insurance_amt_avg,
                            A.pt 
                          FROM
                            (
                            SELECT
                              t1.sm_norm_insurance_amt / t1.ms_emp_cnt sm_norm_insurance_amt_avg,
                              t1.pt 
                            FROM
                              phoenix.ads_insurance_area_index_progress_dfp t1 
                            WHERE
                              t1.pt = '${pt}' 
                            ORDER BY
                              t1.sm_norm_insurance_amt / t1.ms_emp_cnt DESC 
                             LIMIT 3 
                            ) A 
                          GROUP BY
                            A.pt 
                          ) a2 ON a1.pt = a2.pt 
                          AND a1.sm_norm_insurance_amt / a1.ms_emp_cnt >= a2.sm_norm_insurance_amt_avg 
                        ORDER BY
                          a1.sm_norm_insurance_amt / a1.ms_emp_cnt DESC 
                          LIMIT 3"""
        ftlParam[pt] = msgStatService.selectSafesPgMaps(sql)
    }
    log.info("ftlParam json is {}", JSON.toJSONString(ftlParam))
    return  ftlParam
}
