import com.alibaba.fastjson.JSON
import com.cfpamf.ms.insur.operation.base.constant.BaseConstants
import com.cfpamf.ms.insur.operation.msg.pojo.dto.OpMessageRuleGroovyDTO
import com.cfpamf.ms.insur.operation.msg.util.Html2ImageUtils
import org.apache.commons.collections4.CollectionUtils

import java.time.LocalDate
import java.time.YearMonth
import java.time.format.DateTimeFormatter

Map execute(OpMessageRuleGroovyDTO rule) {

    def ftlParam = [:]

    def yesterday = LocalDate.now().minusDays(1L)
    def yearMonth = YearMonth.from(yesterday)
    def pt = yesterday.format(BaseConstants.FMT_DATE)
    DateTimeFormatter yyyyMMdd = DateTimeFormatter.ofPattern("yyyy.MM.dd")
    def endDate = yesterday.format(yyyyMMdd)
    //  骑士俱乐部	黄金骑士	员工	年度标准业绩≥11万入围
    def sql = """
	select tmp.* from (
		select
		emp_name,
		emp_code,
	  bch_name,
	  area_name,
		sum(sy_assess_convert_insurance_amt) sy_norm_insurance_amt,
	  round(sum(sy_assess_convert_insurance_amt)/10000,1)                                    rank_value,
	  rank() over(order by sum(sy_assess_convert_insurance_amt)  desc nulls LAST )  rank_num
	from report.ads_insurance_emp_marketing_progress_dfp
	where pt = '${pt}'
    and bch_name is not null
	group by area_code,area_name,bch_code,bch_name,emp_name,emp_code
	) tmp where tmp.rank_num <= 80
"""
    log.info("sql=============== {}",sql)
    def queryResult = msgStatService.selectSafesPgMaps(sql);
    def picSize = 3
    def i = 0
    queryResult.forEach {
        if(i >= picSize){
            return true
        }
        def recommendEmpId = it["emp_code"]
        def dingInfos = wecatService.getDingTalkInfo([recommendEmpId])
        def we = dingInfos.find { dingInfo -> Objects.equals(dingInfo.wechatUserId, recommendEmpId) }
        if (Objects.nonNull(we)) {
            it["avatar"] = we.avatar
        }
        i++
    }
    ftlParam["data"] = queryResult
    if (CollectionUtils.isEmpty(ftlParam["data"] as Collection<?>)) {
        return null
    }
    log.info("ftlParam============= {}",JSON.toJSONString(ftlParam))
    def ftlMap = ["datas": JSON.toJSONString(ftlParam["data"])]
    ftlMap["time"] = endDate
    log.info("ftlMap ============ {}", JSON.toJSONString(ftlMap))
    return ftlMap
}
