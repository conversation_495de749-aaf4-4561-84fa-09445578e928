import com.alibaba.fastjson.JSON
import com.cfpamf.ms.insur.operation.base.constant.BaseConstants
import com.cfpamf.ms.insur.operation.msg.pojo.dto.OpMessageRuleGroovyDTO
import com.cfpamf.ms.insur.operation.msg.util.Html2ImageUtils
import org.apache.commons.collections4.CollectionUtils

import java.time.LocalDate
import java.time.YearMonth
import java.util.stream.Collectors

String execute(OpMessageRuleGroovyDTO rule) {

    def ftlParam = [:]

    def yesterday = LocalDate.now().minusDays(1L)
    def yearMonth = YearMonth.from(yesterday)
    def pt = yesterday.format(BaseConstants.FMT_DATE)
    //  百万俱乐部 查询推广费
    def sql = """
select
          pt,
          bch_name,
          area_name,
          round(sy_norm_insurance_amt /10000,1)                                    rank_value,
          rank() over (partition by pt order by sy_norm_insurance_amt desc) rank_num
      from phoenix.ads_insurance_bch_index_progress_dfp
      where pt = '${pt}'
        and sy_norm_insurance_amt >= 800000  and bch_name is not null
"""
    ftlParam["datas"] = msgStatService.selectSafesPgMaps(sql)
    if (CollectionUtils.isEmpty(ftlParam["datas"] as Collection<?>)) {
        return null
    }
    def areaNameGroup = ftlParam["datas"]*.area_name.groupBy { it }.collect { key, value -> [key, value.size()] }.sort { -it[1] }
    ftlParam["maxAreaName"] = areaNameGroup[0][0]
    ftlParam["maxAreaPre"] = areaNameGroup[0][1]
    ftlParam["areaNames"] = ftlParam["datas"]*.area_name.stream().distinct().collect(Collectors.toList())
    ftlParam["yearMonth"] = yearMonth
    ftlParam["pt"] = yesterday
    def ftlMap = ["datasJson": JSON.toJSONString(ftlParam)]
    log.info("RankBchClubMillion json is {}", ftlMap.datasJson)
    return Html2ImageUtils.processTemplateInfoString(rule.getMessageTemplate(), ftlMap)
}
