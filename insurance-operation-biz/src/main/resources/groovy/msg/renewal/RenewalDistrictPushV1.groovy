import com.alibaba.excel.EasyExcel
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy
import com.alibaba.fastjson.JSON
import com.cfpamf.ms.insur.operation.base.util.AliYunOssUtil
import com.cfpamf.ms.insur.operation.msg.pojo.dto.OpMessageRuleGroovyDTO
import com.cfpamf.ms.insur.operation.msg.service.MsgStatService
import org.apache.commons.collections4.CollectionUtils

import java.math.RoundingMode


// 续保推送-片区信息推送
Map execute(OpMessageRuleGroovyDTO rule) {
    MsgStatService msgStatService = msgStatService;
    def ftlParam = [:]
    // 1. 打印上下文参数
    log.info("上下文参数:{}",JSON.toJSONString(rule))
    def sql = """
        select 
            * 
        from phoenix.ads_bch_insurance_renewal_policy_dfp
        where district_code='%s'
     """

    def districtCode = "ZZFZZX"
    def districtName = "人才发展中心"
    def finalSql = String.format(sql,districtCode)
    log.info("开始查询片区信息:{},{},{}",districtCode,districtName,finalSql)
    def dataList = msgStatService.selectSafesPgMaps(finalSql)
    if(CollectionUtils.isEmpty(dataList)){
        return null
    }
    log.info("系统查询到分支员工信息:{}",dataList.size())
    // 计算未续保单数
    for(def data:dataList){
        def now_wait_follow_policy_cnt = data.get("now_wait_follow_policy_cnt")
        def now_renewal_policy_cnt = data.get("now_renewal_policy_cnt")
        if(now_renewal_policy_cnt!=null && now_wait_follow_policy_cnt!=null){
            def now_no_renewal_policy_cnt = now_wait_follow_policy_cnt - now_renewal_policy_cnt
            data.put("now_no_renewal_policy_cnt",now_no_renewal_policy_cnt)
        }
    }
    summary(dataList)
    ftlParam["jsonDatas"] = JSON.toJSONString(dataList)
    def detailUrl = uploadUrl(dataList)

    ftlParam["detailUrl"] = detailUrl
    ftlParam["districtName"] = districtName;
    log.info("OSS文件生成完成:{}", detailUrl)
    return ftlParam
}

def summary(def paramList){
    def sum_mon_wait_follow_policy_cnt = 0
    def sum_mon_wait_follow_policy_amt = 0
    def sum_mon_wait_follow_loan_policy_cnt = 0
    def sum_mon_wait_follow_unloan_policy_cnt = 0
    def sum_now_wait_follow_policy_cnt = 0
    def sum_now_wait_follow_policy_amt = 0
    def sum_now_wait_follow_loan_policy_cnt = 0
    def sum_now_wait_follow_unloan_policy_cnt = 0
    def sum_now_follow_policy_cnt = 0
    def sum_now_follow_loan_policy_cnt = 0
    def sum_now_follow_unloan_policy_cnt = 0
    def sum_now_renewal_policy_cnt = 0
    def sum_now_renewal_loan_policy_cnt = 0
    def sum_now_renewal_unloan_policy_cnt = 0
    def sum_now_renewal_policy_amt = 0
    def sum_now_no_renewal_policy_cnt = 0
    def summary=new LinkedHashMap()
    for(def data:paramList){
        def mon_wait_follow_policy_cnt = data.get("mon_wait_follow_policy_cnt")
        if(mon_wait_follow_policy_cnt!=null){
            sum_mon_wait_follow_policy_cnt += mon_wait_follow_policy_cnt
        }
        def mon_wait_follow_policy_amt = data.get("mon_wait_follow_policy_amt")
        if(mon_wait_follow_policy_amt!=null){
            mon_wait_follow_policy_amt = new BigDecimal(mon_wait_follow_policy_amt).setScale(2, RoundingMode.HALF_UP)
            data.put("mon_wait_follow_policy_amt",mon_wait_follow_policy_amt)
            sum_mon_wait_follow_policy_amt += mon_wait_follow_policy_amt
        }
        def mon_wait_follow_loan_policy_cnt = data.get("mon_wait_follow_loan_policy_cnt")
        if(mon_wait_follow_loan_policy_cnt!=null){
            sum_mon_wait_follow_loan_policy_cnt += mon_wait_follow_loan_policy_cnt
        }
        def mon_wait_follow_unloan_policy_cnt = data.get("mon_wait_follow_unloan_policy_cnt")
        if(mon_wait_follow_unloan_policy_cnt!=null){
            sum_mon_wait_follow_unloan_policy_cnt += mon_wait_follow_unloan_policy_cnt
        }
        def now_wait_follow_policy_cnt = data.get("now_wait_follow_policy_cnt")
        if(now_wait_follow_policy_cnt!=null){
            sum_now_wait_follow_policy_cnt += now_wait_follow_policy_cnt
        }
        def now_wait_follow_policy_amt = data.get("now_wait_follow_policy_amt")
        if(now_wait_follow_policy_amt!=null){
            now_wait_follow_policy_amt = new BigDecimal(now_wait_follow_policy_amt).setScale(2, RoundingMode.HALF_UP)
            data.put("now_wait_follow_policy_amt",now_wait_follow_policy_amt)
            sum_now_wait_follow_policy_amt += now_wait_follow_policy_amt
        }
        def now_wait_follow_loan_policy_cnt = data.get("now_wait_follow_loan_policy_cnt")
        if(now_wait_follow_loan_policy_cnt!=null){
            sum_now_wait_follow_loan_policy_cnt += now_wait_follow_loan_policy_cnt
        }
        def now_wait_follow_unloan_policy_cnt = data.get("now_wait_follow_unloan_policy_cnt")
        if(now_wait_follow_unloan_policy_cnt!=null){
            sum_now_wait_follow_unloan_policy_cnt += now_wait_follow_unloan_policy_cnt
        }
        def now_follow_policy_cnt = data.get("now_follow_policy_cnt")
        if(now_follow_policy_cnt!=null){
            sum_now_follow_policy_cnt += now_follow_policy_cnt
        }
        def now_follow_loan_policy_cnt = data.get("now_follow_loan_policy_cnt")
        if(now_follow_loan_policy_cnt!=null){
            sum_now_follow_loan_policy_cnt += now_follow_loan_policy_cnt
        }
        def now_follow_unloan_policy_cnt = data.get("now_follow_unloan_policy_cnt")
        if(now_follow_unloan_policy_cnt!=null){
            sum_now_follow_unloan_policy_cnt += now_follow_unloan_policy_cnt
        }
        def now_renewal_policy_cnt = data.get("now_renewal_policy_cnt")
        if(now_renewal_policy_cnt!=null){
            sum_now_renewal_policy_cnt += now_renewal_policy_cnt
        }
        def now_renewal_loan_policy_cnt = data.get("now_renewal_loan_policy_cnt")
        if(now_renewal_loan_policy_cnt!=null){
            sum_now_renewal_loan_policy_cnt += now_renewal_loan_policy_cnt
        }
        def now_renewal_unloan_policy_cnt = data.get("now_renewal_unloan_policy_cnt")
        if(now_renewal_unloan_policy_cnt!=null){
            sum_now_renewal_unloan_policy_cnt += now_renewal_unloan_policy_cnt
        }
        def now_renewal_policy_amt = data.get("now_renewal_policy_amt")
        if(now_renewal_policy_amt!=null){
            now_renewal_policy_amt = new BigDecimal(now_renewal_policy_amt).setScale(2, RoundingMode.HALF_UP)
            data.put("now_renewal_policy_amt",now_renewal_policy_amt)
            sum_now_renewal_policy_amt += now_renewal_policy_amt
        }
        def now_no_renewal_policy_cnt = data.get("now_no_renewal_policy_cnt")
        if(now_no_renewal_policy_cnt!=null){
            sum_now_no_renewal_policy_cnt += now_no_renewal_policy_cnt
        }

    }
    summary.put("mon_wait_follow_policy_cnt",sum_mon_wait_follow_policy_cnt)
    summary.put("mon_wait_follow_policy_amt",sum_mon_wait_follow_policy_amt)
    summary.put("mon_wait_follow_loan_policy_cnt",sum_mon_wait_follow_loan_policy_cnt)
    summary.put("mon_wait_follow_unloan_policy_cnt",sum_mon_wait_follow_unloan_policy_cnt)
    summary.put("now_wait_follow_policy_cnt",sum_now_wait_follow_policy_cnt)
    summary.put("now_wait_follow_policy_amt",sum_now_wait_follow_policy_amt)
    summary.put("now_wait_follow_loan_policy_cnt",sum_now_wait_follow_loan_policy_cnt)
    summary.put("now_wait_follow_unloan_policy_cnt",sum_now_wait_follow_unloan_policy_cnt)
    summary.put("now_follow_policy_cnt",sum_now_follow_policy_cnt)
    summary.put("now_follow_loan_policy_cnt",sum_now_follow_loan_policy_cnt)
    summary.put("now_follow_unloan_policy_cnt",sum_now_follow_unloan_policy_cnt)
    summary.put("now_no_renewal_policy_cnt",sum_now_no_renewal_policy_cnt)
    BigDecimal b1 = new BigDecimal(sum_now_wait_follow_policy_cnt)
    if(b1.compareTo(BigDecimal.ZERO)!=0) {
        def follow_rate = new BigDecimal(sum_now_follow_policy_cnt).divide(new BigDecimal(sum_now_wait_follow_policy_cnt), 4, BigDecimal.ROUND_HALF_UP)
        def loan_follow_rate = new BigDecimal(sum_now_follow_loan_policy_cnt).divide(new BigDecimal(sum_now_wait_follow_policy_cnt), 4, BigDecimal.ROUND_HALF_UP)
        def un_loan_follow_rate = new BigDecimal(sum_now_follow_unloan_policy_cnt).divide(new BigDecimal(sum_now_wait_follow_policy_cnt), 4, BigDecimal.ROUND_HALF_UP)
        summary.put("follow_rate",follow_rate)
        summary.put("loan_follow_rate",loan_follow_rate)
        summary.put("unloan_follow_rate",un_loan_follow_rate)

        def renewal_rate = new BigDecimal(sum_now_renewal_policy_cnt).divide(new BigDecimal(sum_now_wait_follow_policy_cnt), 4, BigDecimal.ROUND_HALF_UP)
        summary.put("renewal_rate",renewal_rate)
    }
    // 主营-续保率
    BigDecimal b2 = new BigDecimal(sum_now_wait_follow_loan_policy_cnt)
    if(b2.compareTo(BigDecimal.ZERO)!=0){
        def loan_renewal_rate = new BigDecimal(sum_now_renewal_loan_policy_cnt).divide(b2, 4, BigDecimal.ROUND_HALF_UP)
        summary.put("loan_renewal_rate",loan_renewal_rate)
    }
    // 主营-续保率
    BigDecimal b3 = new BigDecimal(sum_now_wait_follow_unloan_policy_cnt)
    if(b3.compareTo(BigDecimal.ZERO)!=0){
        def unloan_renewal_rate = new BigDecimal(sum_now_renewal_unloan_policy_cnt).divide(b3, 4, BigDecimal.ROUND_HALF_UP)
        summary.put("unloan_renewal_rate",unloan_renewal_rate)
    }
    summary.put("now_renewal_policy_cnt",sum_now_renewal_policy_cnt)
    summary.put("now_renewal_loan_policy_cnt",sum_now_renewal_loan_policy_cnt)
    summary.put("now_renewal_unloan_policy_cnt",sum_now_renewal_unloan_policy_cnt)
    summary.put("now_renewal_policy_amt",sum_now_renewal_policy_amt)
    summary.put("bch_name","总计")
    summary.put("area_name","-")
    summary.put("district_name","-")
    paramList.add(summary)
}

def uploadUrl(def paramList) {
    def dataList=[]
    def summary = new LinkedHashMap()
    for(def data:paramList){
        def entry= new LinkedHashMap()
        entry.put("区域",data.get("area_name"))
        entry.put("片区",data.get("district_name"))
        entry.put("机构",data.get("bch_name"))
        entry.put("月度待跟进保单数",data.get("mon_wait_follow_policy_cnt"))
        entry.put("月度待跟进保费",data.get("mon_wait_follow_policy_amt"))
        entry.put("当前待跟进保单数",data.get("now_wait_follow_policy_cnt"))
        entry.put("当前待跟进保费",data.get("now_wait_follow_policy_amt"))
        entry.put("当前待跟进主营保单数",data.get("now_wait_follow_unloan_policy_cnt"))
        //跟进率
        def followRate = data.get("follow_rate")
        if(followRate!=null){
            BigDecimal a = new BigDecimal(followRate).multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP)
            entry.put("跟进率",a+"%" )
        }else{
            entry.put("跟进率","0%")
        }
        //主营业务跟进率
        def loanFollowRate = data.get("loan_follow_rate")
        if(loanFollowRate!=null){
            BigDecimal a = new BigDecimal(loanFollowRate).multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP)
            entry.put("主营业务跟进率",a+"%"  )
        }else{
            entry.put("主营业务跟进率","0%")
        }

        //非主营业务跟进率
        def unloanFollowRate = data.get("unloan_follow_rate")
        if(unloanFollowRate!=null){
            BigDecimal a = new BigDecimal(unloanFollowRate).multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP)
            entry.put("非主营业务跟进率",a+"%"  )
        }else{
            entry.put("非主营业务跟进率","0%")
        }
        entry.put("当前已续保保单数",data.get("now_renewal_policy_cnt"))
        entry.put("当前已续保保费",data.get("now_renewal_policy_amt"))

        // 未续保单数
        def now_wait_follow_policy_cnt = data.get("now_wait_follow_policy_cnt")
        def now_renewal_policy_cnt = data.get("now_renewal_policy_cnt")
        if(now_renewal_policy_cnt!=null && now_wait_follow_policy_cnt!=null){
            def now_no_renewal_policy_cnt = now_wait_follow_policy_cnt - now_renewal_policy_cnt
            entry.put("未续保单数",now_no_renewal_policy_cnt)
        }

        entry.put("当前已续保主营保单数",data.get("now_renewal_loan_policy_cnt"))
        entry.put("当前已续保非主营保单数",data.get("now_renewal_unloan_policy_cnt"))
        entry.put("当前已续保保费",data.get("now_renewal_policy_amt"))
        // 主营业务续保率
        def renewalRate = data.get("renewal_rate")
        if(renewalRate!=null){
            BigDecimal a = new BigDecimal(renewalRate).multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP)
            entry.put("续保率",a+"%" )
        }else{
            entry.put("续保率","0%")
        }
        // 主营业务续保率
        def loan_renewal_rate = data.get("loan_renewal_rate")
        if(loan_renewal_rate!=null){
            BigDecimal a = new BigDecimal(loan_renewal_rate).multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP)
            entry.put("主营业务续保率",a+"%" )
        }else{
            entry.put("主营业务续保率","0%")
        }
        // 非主营业务续保率
        def unloan_renewal_rate = data.get("unloan_renewal_rate")
        if(unloan_renewal_rate!=null){
            BigDecimal a = new BigDecimal(unloan_renewal_rate).multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP)
            entry.put("非主营业务续保率",a+"%" )
        }else{
            entry.put("非主营业务续保率","0%")
        }
        dataList.add(entry)
    }
    def head = dataList[0]
    def heads = head.keySet().collect {[it]}
    def datas = dataList.collect {
        heads.collect({hit -> it.get(hit.get(0))})
    }
    ByteArrayOutputStream os = new ByteArrayOutputStream()
    EasyExcel.write(os).autoCloseStream(true)
            .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
            .sheet( "片区续保跟进信息")
            .head(heads)
            .doWrite(datas)

    log.info("开始上传数据到 oss ", dataList.size())
    def detailUrl = AliYunOssUtil.uploadByBytes(os.toByteArray(), "groovy/show_renewal/detail/" + UUID.randomUUID().toString().replaceAll("-", "") + "/" + System.currentTimeMillis() + ".xlsx")
    return detailUrl
}