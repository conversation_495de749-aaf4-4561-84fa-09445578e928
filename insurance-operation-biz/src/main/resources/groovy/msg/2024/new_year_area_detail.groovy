import com.alibaba.fastjson.JSON
import com.cfpamf.ms.insur.operation.base.constant.BaseConstants
import com.cfpamf.ms.insur.operation.msg.pojo.dto.OpMessageRuleGroovyDTO
import com.cfpamf.ms.insur.operation.msg.util.Html2ImageUtils
import org.apache.commons.collections4.CollectionUtils

import java.time.LocalDate
import java.time.temporal.ChronoUnit

String execute(OpMessageRuleGroovyDTO rule) {

    LocalDate endDate = rule.getEndDate()
    Long restDays = null;
    if(endDate!=null){
        restDays = LocalDate.now().until(endDate, ChronoUnit.DAYS);
    }
    def userParam = rule.getReceiver().otherParams;
    def pt = LocalDate.now().minusDays(1L).format(BaseConstants.FMT_DATE)
    def sql = """

select t1.area_name
     , t1.district_name
     , t1.bch_name
     , t1.bch_code
     , coalesce(sum(total_insured),0) - coalesce(sum(total_surrender)       ,0)                                                       as total_insured
     , max(t1.sm_manage_cnt)                                                                                  as jan_emp_num
     , max(t1.sm_manage_cnt) * 21                                                                             as target_policies
     , coalesce(round((sum(total_insured) - sum(total_surrender)) / max(bch_jan_emp_num), 1),
                0)                                                                                            as avg_policies
     , coalesce(round((sum(total_insured) - sum(total_surrender)) / (max(bch_jan_emp_num) * 21) * 100, 2),
                0)                                                                                            as pre_policies
from phoenix.ads_insurance_bch_index_progress_dfp t1
         left join
     phoenix.temp_safes_stats_new_year_2024 t2 on t1.bch_code = t2.bch_code
         and t2.pt = '${pt}'
where t1.pt = '20240101'
  and t1.area_name = '${userParam.regionName}'
  and t1.sm_manage_cnt > 0
group by t1.area_name, t1.district_name, t1.bch_name, t1.bch_code
    """
    def areaDatas = msgStatService.selectSafesPgMaps(sql);
    if (CollectionUtils.isEmpty(areaDatas)) {
        log.error("new_year_area_detail.groovy {} {}没数据", userParam.regionName, pt)
        return
    }
    //计算差值
    areaDatas.forEach {it["gap"] = it.total_insured - it.target_policies}
    def ftlMap = ["datasJson": JSON.toJSONString(areaDatas)]
    ftlMap.put("restdays",restDays);
    log.info("push params {} {}", userParam, ftlMap)
    return Html2ImageUtils.processTemplateInfoString(rule.getMessageTemplate(), ftlMap)
}
