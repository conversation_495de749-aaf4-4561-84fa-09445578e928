import com.cfpamf.ms.insur.operation.base.constant.BaseConstants
import com.cfpamf.ms.insur.operation.msg.pojo.dto.OpMessageRuleGroovyDTO
import com.cfpamf.ms.insur.operation.msg.util.Html2ImageUtils

import java.time.LocalDate
import java.time.temporal.ChronoUnit

String execute(OpMessageRuleGroovyDTO rule) {
    LocalDate endDate = rule.getEndDate()
    Long restDays = null;
    if(endDate!=null){
        restDays = LocalDate.now().until(endDate, ChronoUnit.DAYS);
    }

    def userParam = rule.getReceiver().otherParams;
    def pt = LocalDate.now().minusDays(1L).format(BaseConstants.FMT_DATE)
    def sql = """
select area_name, district_name, bch_name, bch_code
     , sum(total_insured) - sum(total_surrender)                             as total_insured
     , max(bch_jan_emp_num) as jan_emp_num 
     , max(bch_jan_emp_num) * 21                                                 as target_policies
     , round((sum(total_insured) - sum(total_surrender)) / max(bch_jan_emp_num),1)        as avg_policies
     , round((sum(total_insured) - sum(total_surrender)) / (max(bch_jan_emp_num) * 21) * 100, 2) as pre_policies
from phoenix.temp_safes_stats_new_year_2024
 where pt = '${pt}' and area_name = '${userParam.regionName}'
 and bch_code = '${userParam.orgCode}'
group by area_name, district_name, bch_name, bch_code
    """
    def areaData = msgStatService.selectSafesPgMap(sql);
    if (areaData == null) {
        log.warn("new_year_bch_summary.groovy {} {}没数据", userParam.orgCode, pt)
        return
    }
    areaData["gap"] =  areaData.total_insured - areaData.target_policies
    areaData["restdays"] = restDays
    log.info("push params {} {}", userParam, areaData)
    return Html2ImageUtils.processTemplateInfoString(rule.getMessageTemplate(), areaData)
}
