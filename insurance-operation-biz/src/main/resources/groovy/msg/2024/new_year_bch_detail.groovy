import com.alibaba.fastjson.JSON
import com.cfpamf.ms.insur.operation.base.constant.BaseConstants
import com.cfpamf.ms.insur.operation.msg.pojo.dto.OpMessageRuleGroovyDTO
import com.cfpamf.ms.insur.operation.msg.util.Html2ImageUtils
import org.apache.commons.collections4.CollectionUtils

import java.time.LocalDate
import java.time.temporal.ChronoUnit

String execute(OpMessageRuleGroovyDTO rule) {
    LocalDate endDate = rule.getEndDate()
    Long restDays = null;
    if(endDate!=null){
        restDays = LocalDate.now().until(endDate, ChronoUnit.DAYS);
    }

    def userParam = rule.getReceiver().otherParams;
    def pt = LocalDate.now().minusDays(1L).format(BaseConstants.FMT_DATE)
    def sql = """
select area_name
     , district_name
     , bch_name
     , bch_code
     , recommend_emp_name
     , recommend_emp_id
     , is_jan_sm_manage
     , total_insured - total_surrender         as total_insured
     , total_insured - total_surrender - 100   as gap
     , total_insured_jan - total_surrender_jan as total_insured_jan
     , total_insured_feb - total_surrender_feb as total_insured_feb
     , total_insured_mar - total_surrender_mar as total_insured_mar
from phoenix.temp_safes_stats_new_year_2024
where pt = '${pt}'
  and area_name = '${userParam.regionName}'
  and bch_code = '${userParam.orgCode}'
    """
    def allPt = [:]
    def datas = msgStatService.selectSafesPgMaps(sql);
    if (CollectionUtils.isEmpty(datas)) {
        log.warn("new_year_bch_detail {} {}没数据", userParam.orgCode, pt)
        return
    }
    datas.forEach { it.jan_sm_manage = it.is_jan_sm_manage == 1 ? "是" : "否" }
    def ftlMap = ["datasJson": JSON.toJSONString(datas)]

    ftlMap.put("restdays",restDays);
    log.info("push params {} {}", userParam, ftlMap)
    return Html2ImageUtils.processTemplateInfoString(rule.getMessageTemplate(), ftlMap)
}
