import com.cfpamf.ms.insur.operation.msg.pojo.po.OpMessageRule
import com.cfpamf.ms.insur.operation.msg.util.Html2ImageUtils

import java.time.LocalDate

String execute(OpMessageRule rule) {
    Map<String, Object> allParams = jacksonObjectMapper.readValue(rule.getParams(), Map.class)
    String rank = """
SELECT
temp.*,
au.userName AS userName,
au.regionName AS userRegion,
au.organizationFullName AS userOrganization 
FROM
(
SELECT
userId,
COUNT( 1 ) AS lotteryCount,
sum( totalAmount ) AS totalAmount 
FROM
(
(
SELECT
t1.customerAdminId AS userId,
ROUND( t1.qty * t1.unitPrice, 0 ) AS totalAmount 
FROM
sm_order t1
LEFT JOIN sm_order_insured t2 ON t1.fhOrderId = t2.fhOrderId
LEFT JOIN sm_order_policy t3 ON t2.policyNo = t3.policy_no 
AND t2.fhOrderId = t3.fh_order_id 
WHERE
t1.paymentTime >= '2021-11-01 00:00:00' 
AND t1.paymentTime <= '2021-11-30 23:59:59' 
AND t2.appStatus = '1' 
AND t1.productId IN ( 187, 178, 214, 252 ) 
AND TIMESTAMPDIFF( DAY, t1.paymentTime, t3.visit_time ) <= 5 
GROUP BY
t2.idNumber,
t1.productId 
) UNION ALL
(
SELECT
t1.customerAdminId,
ROUND( t7.total_amount, 0 ) AS totalAmount 
FROM
sm_order t1
LEFT JOIN sm_order_applicant t2 ON t1.fhOrderId = t2.fhOrderId
LEFT JOIN sm_order_insured t4 ON t1.fhOrderId = t4.fhOrderId
LEFT JOIN sm_order_item t7 ON t4.fhOrderId = t7.fh_order_id 
AND t4.idNumber = t7.id_number 
AND t4.appStatus = t7.app_status 
WHERE
t1.paymentTime >= '2021-11-01 00:00:00' 
AND t1.paymentTime <= '2021-11-30 23:59:59' 
AND t4.appStatus = '1' 
AND t1.productId IN ( 219, 180 ) 
GROUP BY
t2.idNumber,
t1.productId 
)) a 
GROUP BY
a.userId 
) temp
LEFT JOIN auth_user au ON temp.userId = au.`userId ` 
AND au.enabled_flag = 0 
ORDER BY
temp.lotteryCount DESC,
temp.totalAmount DESC
    limit   ${allParams.rank}
"""
    System.err.println('rank')
    def maps =     msgStatService.selectMaps(rank)
    allParams.put("rptDate", LocalDate.now().getDayOfMonth())
    allParams.put("datas", maps)
    return Html2ImageUtils.processTemplateInfoString(rule.getMessageTemplate(), allParams)
}
