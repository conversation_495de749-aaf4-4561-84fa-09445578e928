import com.alibaba.excel.EasyExcel
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy
import com.alibaba.fastjson.JSON
import com.cfpamf.ms.insur.operation.base.util.AliYunOssUtil
import com.cfpamf.ms.insur.operation.msg.pojo.dto.OpMessageRuleGroovyDTO
import com.cfpamf.ms.insur.operation.msg.service.MsgStatService
import org.apache.commons.collections4.CollectionUtils
import java.time.LocalDate
Map execute(OpMessageRuleGroovyDTO rule) {
    MsgStatService msgStatService = msgStatService;
    def ftlParam = [:]
    //  查询推广费
    log.info("上下文参数:{}",JSON.toJSONString(rule))
    def sql = """
         
select 
            a.customer_manager_id as 客户经理工号,
            a.customer_manager_name as 客户经理,
            a.supervisor_job_number as 督导工号,
            a.supervisor_name as 督导,
            area_code as 区域编码, 
            area_name as 区域名称,
            org_code as 机构编码,
            org_name as 机构名称,
            renewal_policy_count as 遗留待续保单数,
            cur_month_expire_count as 当月到期保单数,
            premium as 保费,
            renewaled_count as 已续或已转投保单数,
            renewaled_premium as 已续保费,
            no_follow_count as 未跟进保单数据,
            (cur_month_expire_count-renewaled_count) as 当月待续投保单数,
            case when cur_month_expire_count=0 then 0 else round((cur_month_expire_count-no_follow_count)*100/cur_month_expire_count) end as 跟进率,
            case when cur_month_expire_count =0 then 0 else round(renewaled_count*100/cur_month_expire_count) end as 续保率 
from (
        SELECT 
            a.customer_manager_id,
            a.customer_manager_name,
            a.supervisor_job_number,
            a.supervisor_name,
            area_code, 
            area_name,
            org_code,
            org_name,
            SUM(CASE WHEN renewal_flag = 'waited' and substr(a.renewal_before_date,1,7) < to_char(CURRENT_DATE,'yyyy-MM') THEN 1 ELSE 0 END) AS renewal_policy_count,
            SUM(CASE WHEN substr(a.renewal_before_date,1,7) = to_char(current_date,'yyyy-mm') THEN 1 ELSE 0 END) AS cur_month_expire_count,
            SUM(CASE WHEN substr(a.renewal_before_date,1,7) = to_char(current_date,'yyyy-mm') THEN CAST(premium AS NUMERIC) ELSE 0 END) AS premium,
            SUM(CASE WHEN substr(a.renewal_before_date,1,7) = to_char(current_date,'yyyy-mm') and (renewal_flag = 'renewed' or transf_flag=1 ) THEN 1 ELSE 0 END) AS renewaled_count,
            sum(case when substr(a.renewal_before_date,1,7) = to_char(current_date,'yyyy-mm') then (coalesce(renewaled_premium,0)+coalesce(transf_premium,0)) else 0 end) as renewaled_premium,
            SUM(CASE WHEN substr(a.renewal_before_date,1,7) = to_char(current_date,'yyyy-mm') AND a.renewal_follow_info = '未跟进' THEN 1 ELSE 0 END) AS no_follow_count
        FROM "phoenix"."dwd_renewal_policy_info"  a 
        where customer_manager_id is not null and customer_manager_id!=''
        GROUP BY 
        customer_manager_id,
        customer_manager_name,
        area_code,
        area_name,
        org_code,
        org_name,
        a.supervisor_job_number,
        supervisor_name
  ) a where area_name='%s' and org_code='%s' 
    """
    def areaname=rule.receiver.otherParams.regionName;
    def orgcode=rule.receiver.otherParams.orgCode;
    def finalSql = String.format(sql,areaname,orgcode)
    def dataList = msgStatService.selectSafesPgMaps(finalSql)
    log.info("获取数据集：{},{}",finalSql,JSON.toJSONString(dataList))
    if(CollectionUtils.isEmpty(dataList)){
        return null
    }
    def renewal_policy_count=0
    def renewaled_premium=0
    def cur_month_expire_count=0
    def premium=0
    def renewaled_count=0
    def wait_renewal_count=0
    def no_follow_count =0
    for(Map entry:dataList){
        renewal_policy_count+=entry.get("遗留待续保单数")
        cur_month_expire_count+=entry.get("当月到期保单数")
        premium+=entry.get("保费")
        renewaled_count+=entry.get("已续或已转投保单数")
        renewaled_premium+=entry.get("已续保费")
        wait_renewal_count+=entry.get("当月待续投保单数")
        no_follow_count+=entry.get("未跟进保单数据")
    }
    def renewal_rate = BigDecimal.ZERO
    def follow_rate = BigDecimal.ZERO

    if(cur_month_expire_count!=0) {
        renewal_rate = BigDecimal.valueOf(renewaled_count).multiply(BigDecimal.valueOf(100)).divide(new BigDecimal(cur_month_expire_count), 2, BigDecimal.ROUND_HALF_UP)
        follow_rate =(new BigDecimal(cur_month_expire_count).subtract(new BigDecimal(no_follow_count))).multiply(BigDecimal.valueOf(100)).divide(new BigDecimal(cur_month_expire_count), 2, BigDecimal.ROUND_HALF_UP)
    }
    Map lastTotal = [:]
    lastTotal["客户经理"]= "分支合计"
    lastTotal["当月到期保单数"]= cur_month_expire_count
    lastTotal["遗留待续保单数"]= renewal_policy_count
    lastTotal["保费"]= premium
    lastTotal["已续或已转投保单数"]= renewaled_count
    lastTotal["已续保费"]= renewaled_premium
    lastTotal["当月待续投保单数"]= wait_renewal_count
    lastTotal["续保率"]= renewal_rate
    lastTotal["跟进率"]= follow_rate
    dataList.add(lastTotal)
    ftlParam["jsonDatas"] = JSON.toJSONString(dataList)
    def detailUrl = uploadUrl(orgcode, areaname)

    ftlParam["detailUrl"] = detailUrl
    ftlParam["month"] = LocalDate.now().getMonth().value;
    log.info("OSS文件生成完成:{}", detailUrl)
    return ftlParam
}

def uploadUrl(def orgCode,def areaName) {
    def sql = """
        select 
        a.policy_no                 as 保单号
        ,policy_product_name        as 投保产品
        ,a.policy_product_type      as 类型
        ,a.premium                  as 保费
        ,a.invalid_time             as 失效时间
        ,a.renewal_remaining_day    as 状态
        ,a.self_insurance           as 自保件
        ,is_loan_flag               as 主营相关保单
        ,a.applicant_holder_name    as 投保人姓名
        ,a.insured_name             as 被保人姓名
        ,same_product_policy        as 同类产品是否有其他待生效或生效中保单
        ,policy_count               as 累计保单数
        ,claim_report_flag          as 被保人是否报案
        ,claim_payed_flag           as 是否赔付
        ,round(cast(claim_payed_money as NUMERIC),2)          as 赔付金额
        ,recommend_name             as 推荐人姓名
        ,customer_manager_name      as 管护客户经理
        ,area_name                  as 区域
        ,org_name                   as 机构
        ,ai_call_flag               as 是否AI外呼
        ,ai_call_result             as AI外呼结果
        ,ai_call_intention          as AI外呼意向
        ,renewal_intention_eval     as 续保可能性预估
        ,renewal_type               as 支持类型
        ,renewal_product_name       as 产品名称
        ,renewal_follow_info        as 跟进情况 
        from phoenix.dwd_renewal_policy_info a
        where a.customer_manager_id is not null and customer_manager_id !=''
        and area_name='%s' and org_code='%s' 
         
    """
    def finalSql = String.format(sql,areaName,orgCode)
    def dataList = msgStatService.selectSafesPgMaps(finalSql)
    log.info("获取续保明细数据集：{},{}",finalSql,JSON.toJSONString(dataList))
    def head = dataList[0]
    def heads = head.keySet().collect {[it]}
    def datas = dataList.collect {
        heads.collect({hit -> it.get(hit.get(0))})
    }
    ByteArrayOutputStream os = new ByteArrayOutputStream()
    EasyExcel.write(os).autoCloseStream(true)
            .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
            .sheet( "待续保明细").head(heads).doWrite(datas)

    log.info("开始上传数据到 oss ", dataList.size())
    def detailUrl = AliYunOssUtil.uploadByBytes(os.toByteArray(), "groovy/show_renewal/detail/" + UUID.randomUUID().toString().replaceAll("-", "") + "/" + System.currentTimeMillis() + ".xlsx")
    return detailUrl
}