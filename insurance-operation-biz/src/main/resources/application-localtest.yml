insurance-admin:
  url: http://insurance-admin.tsg.cfpamf.com/
wecat-service:
  url: http://wecat-service.tsg.cfpamf.com

insurance-image:
  url: http://insurance-image.tsg.cfpamf.com/
dynamics-image:
  url: https://node-canvas-scrm-ming-robot-jbjpsqdvwz.cn-hangzhou.fcapp.run


ms-service-customer:
  url:http://ms-customer.tsg.cfpamf.com/

log-gateway:
  url: https://log-gateway-test.cdfinance.com.cn

scrm-service:
  url: http://scrm-service.tsg.cfpamf.com
ims-public:
  url: http://ims-public-api.tsg.xiaowhale.com

bms:
  api:
    url: http://bms-service.tsg.cfpamf.com/

dingtalk:
  robot:
    app-key: dingebjxuactrt6zmvee
    agent-id: 15524002
    app-secret: 0bZbkzAzCGIcrKOW3V5M-oJW9AHqsOmjH2C4vCCG7Gm-lbqY2a3oV2snLt1tTchv

phantom:
  phantom-path: /Users/<USER>/gitwork/zhnx/html-render/phantomjs
  js-path: /Users/<USER>/gitwork/zhnx/html-render/rasterize.js
logging:
  level:
    org.springframework.jdbc.core.JdbcTemplate: debug
    com.cfpamf.ms.insur.operation.fegin.bms.facede: debug
    com.cfpamf.ms.insur.operation.fegin.image.facade: debug
    com.cfpamf.ms.insur.operation.phoenix.api: debug
    com.cfpamf.ms.insur.operation.fegin.hr: debug
    com.cfpamf.ms.insur.operation.pk.safepgdao: debug
    com.cfpamf.ms.insur.operation.pk.dao: debug
    com.cfpamf.ms.insur.operation.activity.dao: debug
    com.cfpamf.ms.insur.operation.honor.dao: debug
    com.cfpamf.ms.insur.operation.reward.addcommission.dao: debug

#whale:
#  domain: https://api-channel.xiaowhale.com/public-api
#  aid: ZHNX20220329095958425358

policy-info:
  exchange: insurance-biz.exchange.policy.info-notify
  routing-key: insurance-biz.queue.policy.info-notify.key
  queue: insurance-biz.queue.policy.info-notify


##
hrms-biz.url: http://hrms-biz.tsg.cfpamf.com

# 微信公众号参数配置
#wechat:
#  appId: wx22cd68f2c1046f97
#  appSecret: ENC(ZWE1MjI3MjMtOTkzYS00ZmEyLWJhYzItMjFmNzEyNDczMDRiQjdNTlBsZklySVRhVEcwUmpKVHZ5YXNSVWdxbEpDWExBQUFBQUFBQUFBQjc2WUdCR2Y2WVZsL3VaN0lCdjZkQldLeVBCd0FPTFk0Q0RNYzdDWm8wSDJ1R012SXdGcG5tWXdtNGpxVldmbU45TjMzbDJsazV4L09ZbFZBPQ==)
#  temp-message-channel-common: _LfaZ4unfORMKQl3clzQ1uwYFvN4RBwCnHFwolL6FdQ
#  todo-interruption-url: https://open.weixin.qq.com/connect/oauth2/authorize?appid=${wechat.app-id}&redirect_uri=https%3A%2F%2Fbms.cfpamf.org.cn%2Fapi%2Finsurance%2Fmicro%2Fwx%2Findex%2Fauthorize_redirect%3Frdl%3DaHR0cHM6Ly9oNS54aWFvd2hhbGUuY29tL3d4LyMvYnJlYWtJbnNUb2RvTGlzdA%3D%3D&response_type=code&scope=snsapi_userinfo&state=state#wechat_redirect
wechat:
  app-id: wxffdc81bf67a9f8bd
  app-secret: ********************************
  temp-message-channel-common: 6v6i-T9P6cqKmqBlFNE4xiF-Q1tFdfZl5YzbFSvwJfU
  temp-message-AI-follow: 1cOlGdjZ-Dj1kSheRqnIPHY-Ay9LWglpYV__gv9OtnE
  todo-interruption-url: https://open.weixin.qq.com/connect/oauth2/authorize?appid=${wechat.app-id}&redirect_uri=https%3A%2F%2Fbmstest.cdfinance.com.cn%2Fapi%2Finsurance%2Fmicro%2Fwx%2Findex%2Fauthorize_redirect%3Frdl%3DaHR0cHM6Ly9oNS10ZXN0LnhpYW93aGFsZS5jb20vIy9icmVha0luc1RvZG9MaXN0&response_type=code&scope=snsapi_userinfo&state=state#wechat_redirect
  front-domain: https://h5-test.xiaowhale.com
  customer-detail-url: ${wechat.front-domain}/#/breakInsCustomerDetail/%s/insur?type=break
  front-customer-detail-url: https://open.weixin.qq.com/connect/oauth2/authorize?appid=${wechat.app-id}&redirect_uri=%s&response_type=code&scope=snsapi_userinfo&state=%s#wechat_redirect
  car-promotion-fee: ${wechat.front-domain}/#/carPromotionFee?productId=%s&regionName=%s
  follow-message: 1cOlGdjZ-Dj1kSheRqnIPHY-Ay9LWglpYV__gv9OtnE
  api-domain: https://bmstest.cdfinance.com.cn/api/insurance/micro
  renew-detail-url: ${wechat.front-domain}/#/renewDetail?productAttrCode=%s&policyNo=%s&fhOrderId=%s
  #生产:_LfaZ4unfORMKQl3clzQ1uwYFvN4RBwCnHFwolL6FdQ
  material-change-message: 6v6i-T9P6cqKmqBlFNE4xiF-Q1tFdfZl5YzbFSvwJfU
  #生产：https://h5.xiaowhale.com/wx/#/assistant/onlinePromotionPage?fromWay=notice&id=
  front-material-detail-url: https://open.weixin.qq.com/connect/oauth2/authorize?appid=${wechat.app-id}&redirect_uri=%s&response_type=code&scope=snsapi_userinfo&state=%s#wechat_redirect
  material-detail-url: https://bmstest.cdfinance.com.cn/api/insurance/micro/wx/index/materialDetail?response_type=code&state=state&materialId=s%
