<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>断保客户跟进情况-分支</title>
    <style>
        body {
            margin: 0;
        }

        .list-table {
            /* width: 1150px; */
            background-color: #fff;
            padding: 25px 10px;
            border-radius: 2px;
        }


        table {
            border-collapse: collapse;
            border-spacing: 0;
            table-layout: fixed;
            border-radius: 10px;
            background: #fff;
            max-width: none;
            min-width: 100%;
        }

        table tr {
            width: 100%;
        }

        table tr > td {
            font-size: 14px;
            text-align: center;
        }

        table tr > td:not(:first-child) {
            text-align: right;
        }

        table tr > th {
            font-size: 14px;
            text-align: center;
        }

        table tr > td,
        th {
            padding: 3px;
            border: 1px solid #000;
        }

        .table-title {
            font-size: 22px;
        }

        .pink-bg {
            background: rgb(241, 214, 214);
        }


        .green-bg {
            background: rgb(236, 241, 224) !important;
        }

        .blue-tr > th {
            background: rgb(217, 229, 240);
        }

        .w75 {
            width: 80px;
        }

        .fs-16 {
            font-size: 16px;
        }

        .progress-bar {
            position: relative;
            width: 100%;
            height: 20px;
        }

        .progress {
            position: absolute;
            top: 0;
            left: 0;
            width: 0;
            height: 100%;
            text-align: right;
        }

        .breakIns-gradient {
            background: linear-gradient(to right, #0A4D68, #92b4c2);
        }

        .follow-gradient {
            background: linear-gradient(to right, #088395, #98ced8);
        }

        .renew-gradient {
            background: linear-gradient(to right, #05BFDB, #a8d5dc);
        }

        .sum-gradient {
            background: linear-gradient(to right, #00FFCA, #77b0a5);
        }

        .progress-text {
            position: relative;
            z-index: 99;
        }

        .green {
            background-color: #8ddd90;
        }

        .deep-green {
            background-color: #32b738;
        }

        .yellow {
            background-color: #FFEB3B;
        }

        .red {
            background-color: #fa494b;
        }
    </style>
</head>


<script>
    // 表格数据
    const tableData = [{"regionname":"河北区域","dn":"保定北片区","org":"新城","un":"毕建利","un_renew_day":0,"un_renew_week":0,"un_renew_month":0,"un_renew_year":0,"renew_day":0,"renew_week":0,"renew_month":0,"renew_year":0,"policy_day":0,"policy_week":0,"policy_month":0,"policy_year":0,"sum_day":0.0,"sum_week":0.0,"sum_month":0.0,"sum_year":0.0,"follow_count_day":0,"lose_contact_count_day":0,"high_count_day":0,"median_count_day":0,"low_count_day":0,"none_count_day":0,"follow_count_week":0,"lose_contact_count_week":0,"high_count_week":0,"median_count_week":0,"low_count_week":0,"none_count_week":0,"follow_count_month":0,"lose_contact_count_month":0,"high_count_month":0,"median_count_month":0,"low_count_month":0,"none_count_month":0,"follow_count_year":0,"lose_contact_count_year":0,"high_count_year":0,"median_count_year":0,"low_count_year":0,"none_count_year":0,"un_renew_total":292},{"regionname":"河北区域","dn":"保定北片区","org":"新城","un":"王星","un_renew_day":0,"un_renew_week":3,"un_renew_month":0,"un_renew_year":69,"renew_day":0,"renew_week":0,"renew_month":0,"renew_year":0,"policy_day":0,"policy_week":0,"policy_month":0,"policy_year":0,"sum_day":0.0,"sum_week":0.0,"sum_month":0.0,"sum_year":0.0,"follow_count_day":0,"lose_contact_count_day":0,"high_count_day":0,"median_count_day":0,"low_count_day":0,"none_count_day":0,"follow_count_week":0,"lose_contact_count_week":0,"high_count_week":0,"median_count_week":0,"low_count_week":0,"none_count_week":0,"follow_count_month":0,"lose_contact_count_month":0,"high_count_month":0,"median_count_month":0,"low_count_month":0,"none_count_month":0,"follow_count_year":0,"lose_contact_count_year":0,"high_count_year":0,"median_count_year":0,"low_count_year":0,"none_count_year":0,"un_renew_total":167},{"regionname":"河北区域","dn":"保定北片区","org":"新城","un":"王猛","un_renew_day":0,"un_renew_week":0,"un_renew_month":0,"un_renew_year":1,"renew_day":0,"renew_week":0,"renew_month":0,"renew_year":2,"policy_day":0,"policy_week":0,"policy_month":0,"policy_year":2,"sum_day":0.0,"sum_week":0.0,"sum_month":0.0,"sum_year":200.0,"follow_count_day":0,"lose_contact_count_day":0,"high_count_day":0,"median_count_day":0,"low_count_day":0,"none_count_day":0,"follow_count_week":0,"lose_contact_count_week":0,"high_count_week":0,"median_count_week":0,"low_count_week":0,"none_count_week":0,"follow_count_month":0,"lose_contact_count_month":0,"high_count_month":0,"median_count_month":0,"low_count_month":0,"none_count_month":0,"follow_count_year":0,"lose_contact_count_year":0,"high_count_year":0,"median_count_year":0,"low_count_year":0,"none_count_year":0,"un_renew_total":1},{"regionname":"河北区域","dn":"保定北片区","org":"新城","un":"合计","un_renew_day":0,"un_renew_week":0,"un_renew_month":0,"un_renew_year":0,"renew_day":0,"renew_week":0,"renew_month":0,"renew_year":0,"policy_day":0,"policy_week":0,"policy_month":0,"policy_year":0,"sum_day":0.0,"sum_week":0.0,"sum_month":0.0,"sum_year":0.0,"follow_count_day":0,"lose_contact_count_day":0,"high_count_day":0,"median_count_day":0,"low_count_day":0,"none_count_day":0,"follow_count_week":0,"lose_contact_count_week":0,"high_count_week":0,"median_count_week":0,"low_count_week":0,"none_count_week":0,"follow_count_month":0,"lose_contact_count_month":0,"high_count_month":0,"median_count_month":0,"low_count_month":0,"none_count_month":0,"follow_count_year":0,"lose_contact_count_year":0,"high_count_year":0,"median_count_year":0,"low_count_year":0,"none_count_year":0,"un_renew_total":54}]

    tableData.forEach(s=>{
        for (const sKey in s) {
            let val = s[sKey]
            if (!isNaN(val) && sKey !== 'sum_year' && sKey !== 'un_renew_total') {
                s[sKey] = Math.round(val)
            }
        }
    })
    const allData = tableData.filter(b => b["un"] !== '合计')

    function genPre(col, arr) {
        let maxVal = Math.max(...arr.map(a => a[col]))
        arr.forEach(item => {

            if (maxVal > 0) {
                item[col + '_pre'] = Math.round(item[col] / maxVal * 100)
            }
        })
    }

    let unNumKey = ["regionname", "dn", "un", "org", "userid", "orgcode"]
    for (const allDataKey in tableData[0]) {
        if (!isNaN(tableData[0][allDataKey])) {
            genPre(allDataKey, allData)
        }
    }

    const tableBody = document.getElementById("table-body");

    const tableConfig = [
        {title: '员工', field: 'un'},
        {title: '当日', field: 'un_renew_day', type: 'progress', belong: 'breakIns'},
        {title: '当月', field: 'un_renew_month', type: 'progress', belong: 'breakIns'},
        {title: '当年', field: 'un_renew_year', type: 'level', belong: 'breakIns'},
        {title: '累计', field: 'un_renew_total', type: 'level', belong: 'breakIns'},
        {title: '当日', field: 'follow_count_day', type: 'progress', belong: 'follow'},
        {title: '当月', field: 'follow_count_month', type: 'progress', belong: 'follow'},
        {title: '当年', field: 'follow_count_year', type: 'level', belong: 'follow'},
        {title: '当日', field: 'renew_day', type: 'progress', belong: 'renew'},
        {title: '当月', field: 'renew_month', type: 'progress', belong: 'renew'},
        {title: '当年', field: 'renew_year', type: 'level', belong: 'renew'},
        {title: '当日', field: 'sum_day', type: 'progress', belong: 'sum'},
        {title: '当月', field: 'sum_month', type: 'progress', belong: 'sum'},
        {title: '当年', field: 'sum_year', type: 'level', belong: 'sum'},
        {title: '高', field: 'high_count_year'},
        {title: '中', field: 'median_count_year'},
        {title: '低', field: 'low_count_year'},
        {title: '无意向', field: 'none_count_year'},
        {title: '联系不上', field: 'lose_contact_count_year'},
    ]


    // 获取比例值颜色
    function getColorLevel(num) {
        if (num >= 75) {
            return 'red';
        } else if (num >= 50) {
            return 'yellow';
        } else if (num >= 25) {
            return 'deep-green';
        } else {
            return 'green';
        }
    }

    // 获取table元素
    let table = document.getElementById('myTable');

    for (let i = 0; i < tableData.length; i++) {
        // 创建行
        let row = table.insertRow();

        for (let j = 0; j < tableConfig.length; j++) {
            const currentConfig = tableConfig[j];
            const type = currentConfig.type;

            // 创建单元格
            let cell = row.insertCell();
            const curValue = tableData[i][currentConfig.field];
            const curRatio = tableData[i][`${currentConfig.field}_pre`];

            // 添加代码到单元格中
            cell.innerHTML = curValue;

            if (i !== tableData.length - 1) {
                if (type === 'progress') {
                    const belong = currentConfig.belong

                    cell.innerHTML = `
                        <div class="progress-bar">
                            <div class="progress ${belong}-gradient" style="width: ${curRatio}%"></div>
                            <span class="progress-text">${curValue}</span>
                        </div>`;
                } else if (type === 'level') {
                    const tdColor = getColorLevel(curRatio);
                    cell.classList.add(tdColor);
                }
            }
        }
    }
</script>
<body>
<div class="list-table">
    <!-- 区域/分支 table -->
    <table id="myTable">
        <thead>
        <tr>
            <th colspan="19" class="table-title pink-bg">新城断保客户跟进情况 <span
                    class="fs-16">（截止统计日期：06月07日）</span>
            </th>
        </tr>
        <tr class="blue-tr">
            <th class="w75" rowspan="2">区域</th>
            <th colspan="4" class="green-bg">断保客户数</th>
            <th colspan="3">跟进客户数</th>
            <th class="green-bg" colspan="3">激活客户数</th>
            <th colspan="3">激活保费</th>
            <th class="green-bg" colspan="5">跟进情况（当年）</th>
        </tr>
        <tr class="blue-tr">
            <th class="green-bg">当日</th>
            <th class="green-bg">当月</th>
            <th class="green-bg">当年</th>
            <th class="green-bg">累计</th>
            <th>当日</th>
            <th>当月</th>
            <th>当年</th>
            <th class="green-bg">当日</th>
            <th class="green-bg">当月</th>
            <th class="green-bg">当年</th>
            <th>当日</th>
            <th>当月</th>
            <th>当年(万元)</th>
            <th class="green-bg">高意向</th>
            <th class="green-bg">中意向</th>
            <th class="green-bg">低意向</th>
            <th class="green-bg">无意向</th>
            <th class="green-bg">联系不上</th>
        </tr>
        </thead>
        <tbody id="table-body">

        </tbody>
    </table>
</div>

</body>

</html>