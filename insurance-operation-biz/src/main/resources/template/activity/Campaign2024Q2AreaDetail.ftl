<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <title>分支进度</title>
  <style>
    * {
      margin: 0;
      padding: 0;
    }

    ul {
      list-style-type: none;
    }

    p,
    ul,
    li {
      margin: 0;
      padding: 0;
      border: none;
    }

    #root {
      width: 1500px;
      background: linear-gradient(to bottom,#29834a,#576D18);
    }

    .bg {
      width: 100%;
      height: 100%;
      display: block;
    }

    .header-bg {
      width: 100%;
      height: 840px;
    }

    .title-bg {
      display: block;
      margin: -220px auto 0;
      width: 872px;
      height: 85px;
    }

    .push-countdown {
      margin: 40px 20px 0 0;
      text-align: right;
      font-size: 50px;
      color: #FFF7B7;
    }

    .highlight {
      display: inline-block;
      margin-right: 10px;
      padding: 8px 35px;
      border-radius: 24px;
      background-color: #FCF7D1;
      color: #307113;
      font-size: 60px;
      font-weight: bold;
    }

    .activity {
      margin: -10px 20px 0;
    }

    .activity-title {
      width: 295px;
      height: 112px;
      line-height: 100px;
      padding-left: 20px;
      background: url('https://safesfiles.oss-cn-beijing.aliyuncs.com/safes/ZHNX33656/Rectangle 560_202404241610.png') no-repeat;
      background-size: 100%;
      color: #FFF7B8;
      font-size: 40px;
      font-weight: 600;
    }

    .activity-content {
      margin-top: -25px;
      padding: 0 10px 20px;
      border-radius: 24px;
      background: #EDECEC;
    }

    .activity-content-title {
      display: inline-block;
      margin: 0 auto;
      width: 640px;
      height: 100px;
    }

    table {
      width: 100%;
      margin-top: 10px;
      border-collapse: collapse;
      border-spacing: 0;
      table-layout: fixed;
      border-radius: 10px;
      background: #ffffff;
      max-width: none;
      min-width: 100%;
    }

    table tr {
      width: 100%;
    }

    table tr>td {
      font-size: 33px;
      text-align: center;
      word-wrap: break-word;
      font-weight: 400;
    }

    table tr>th {
      color: #307113;
      font-size: 33px;
      text-align: center;
    }

    table tr>td,
    th {
      padding: 6px 3px;
      border: 3px solid #EDECEC;
    }

    .remark {
      margin: 20px 20px;
      color: #FFF7B9;
      font-size: 30px;
    }

    .remark-item {
      margin-top: 5px;
    }

    .footer-bg {
      width: 100%;
      height: 215px;
    }

    .center {
      text-align: center;
    }
    .mt-40 {
      margin-top: 40px;
    }
  </style>
</head>

<body>
  <div id="root">
    <img class="header-bg" src="https://safesfiles.oss-cn-beijing.aliyuncs.com/safes/ZHNX33656/q2ActivityHeaderBg2_202404241726.png" >
    <img class="title-bg" src="https://safesfiles.oss-cn-beijing.aliyuncs.com/safes/ZHNX33656/bch_202404241720.png" alt="">

    <div class="push-countdown">
      活动剩余：<div class="highlight">${restdays!}</div>天
    </div>

    <!-- 活动一：鲸耕细作 三星送福 -->
    <div class="activity">
      <div class="activity-title">活动一：</div>
      <div class="activity-content">
        <div class="center">
          <img class="activity-content-title"
            src="https://safesfiles.oss-cn-beijing.aliyuncs.com/safes/ZHNX33656/q2Activity1Title_202404241533.png"
            alt="">
        </div>
        <table>
          <thead>
            <tr>
              <th>区域</th>
              <th>片区</th>
              <th>分支</th>
              <th>4-6月月均客户经理数</th>
              <th>完成单量</th>
              <th>分支人均单量</th>
              <th>21单差值</th>
            </tr>
          </thead>
          <tbody id="table1-body">

          </tbody>
        </table>
      </div>
    </div>

    <!-- 活动二：农保安民 保障齐全 -->
    <div class="activity mt-40">
      <div class="activity-title">活动二：</div>
      <div class="activity-content">
        <div class="center">
          <img class="activity-content-title"
            src="https://safesfiles.oss-cn-beijing.aliyuncs.com/safes/ZHNX33656/q2Activity2Title_202404241536.png"
            alt="">
        </div>
        <table>
          <thead>
            <tr>
              <th>区域</th>
              <th>片区</th>
              <th>分支</th>
              <th>4-6月月均客户经理数</th>
              <th>机构目标单量</th>
              <th>总单量</th>
              <th>完成率</th>
              <th>差值</th>
              <th>机构人均单量</th>
            </tr>
          </thead>
          <tbody id="table2-body">

          </tbody>
        </table>
      </div>
    </div>

    <div class="remark">
      <p>备注：</p>
      <ul class="remark-item">
        <li>1、非主营业务客户单量统计口径：</li>
        <li>（1）主营业务客户：即个贷、随心取放款的主借人、共同借款人、担保人。</li>
        <li>（2）非在贷客户购买保单且购买后15天内无放款记录的保单计入活动保单。</li>
      </ul>
      <ul class="remark-item">
        <li>2、机构人均活动单量统计口径：</li>
        <li>（1）区域及分支人均统计口径为客户经理，不含后台；</li>
        <li>（2）人均计算规则为2024年4月、5月、6月初机构客户经理人力均值；</li>
        <li>（3）蓝医保、鲸喜中银全家福产品单量统计口径仅针对主险单量，附加险不计算单量。</li>
      </ul>
      <ul class="remark-item">
        <li>3、奖励每人仅限一份，团队与个人奖励不重复获得；活动一与活动二均达成奖励不重复获得，自选其一兑现。</li>
      </ul>
    </div>

    <img class="footer-bg"
      src="https://safesfiles.oss-cn-beijing.aliyuncs.com/safes/ZHNX33656/q2AcitivityFooter_202404241523.png" alt="">
  </div>
</body>

<script>
  // 活动一数据，数组结构（TODO：此处需替换）
  const datas1 = ${datasJson}
  // 活动二数据
  const datas2 = ${datasJson}

  <#noparse>
  // 替换字段正则
  const reg = new RegExp("\\[([^\\[\\]]*?)\\]", 'gm')

  // 活动一表格字段，字段从左到右替换即可（TODO：此处需替换）
  const table1Template = `
      <tr>
        <td>[area_name]</td>
        <td>[district_name]</td>
        <td>[bch_name]</td>
        <td>[jan_emp_num]</td>
        <td>[san_xing_quanjiafu_insured]</td>
        <td>[san_xing_quanjiafu_insured_avg]</td>
        <td>[san_xing_quanjiafu_insured_avg_diff]</td>
      </tr>
  `;
  // 活动二表格字段，字段从左到右替换即可（TODO：此处需替换）
  const table2Template = `
      <tr>
        <td>[area_name]</td>
        <td>[district_name]</td>
        <td>[bch_name]</td>
        <td>[jan_emp_num]</td>
        <td>[target_policies]</td>
        <td>[total_insured]</td>
        <td>[pre_policies]</td>
        <td>[total_insured_diff]</td>
        <td>[avg_policies]</td>
      </tr>
  `;

  let replaceTable1String = ''
  let replaceTable2String = ''
  datas1.forEach(row => replaceTable1String += table1Template.replace(reg, function (node, key) { return row[key]; }))
  datas2.forEach(row => replaceTable2String += table2Template.replace(reg, function (node, key) { return row[key]; }))

  document.querySelector('#table1-body').innerHTML = replaceTable1String
  document.querySelector('#table2-body').innerHTML = replaceTable2String
  </#noparse>
</script>

</html>