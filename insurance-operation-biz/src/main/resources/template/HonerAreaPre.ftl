<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>区域风云榜-欲获</title>
  <style>
    *{
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }
    li{
      list-style: none;
    }
    #root{
      position: relative;
      display: flex;
      flex-direction: column;
      align-items: center;
      width: 750px;
      padding: 20px 0;
      background: url("https://safesfiles-test.oss-cn-beijing.aliyuncs.com/safes/ZHNX13223/背景_202406261756.jpg");
      background-size: 100% 100%;
    }
    .header-bg{
      width: 650px;
      height: auto;
    }
    #quarter-bg {
      position: absolute;
      top: 112px;
      right: 184px;
      width: 56px;
      height: auto;
    }
    .estimate-title {
      position: absolute;
      top: 173px;
      right: 42px;
      width: 60px;
      height: auto;
    }
    .estimate-bg {
      width: 300px;
      height: auto;
      margin: 10px 0;
    }
    .time{
      width: 100%;
      padding: 0 60px;
      font-weight: 600;
      text-align: start;
      font-size: 30px;
      background: linear-gradient(to bottom,#fcedd4,#fcd196);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      /* 兼容非webkit内核的浏览器 */
      background-clip: text;
      color: transparent;
    }
    ul li{
      position: relative;
      width: 555px;
      margin: 20px auto;
      border-radius: 10px;
      background-size: 100% auto;
      background-color: #e60d22;
    }
    ul li{
      margin:60px;
      padding:10px;
      font-size: 30px;
      font-weight: 700;
      color: rgb(254, 253, 217);
    }
    .oddLeft{
      display: inline;
      height: 200px;
      overflow: hidden;
    }
    .oddLeft .medalImg{
      position: absolute;
      top: -46px;
      left: -65px;
      width: 290px;
      margin-bottom: 100px;
    }
    .oddLeft .numImg{
      z-index: 1;
      position: absolute;
      top: -60px;
      left: -50px;
      width: 150px;
    }
    .oddLeft h1,.evenLeft h1 {
      position: absolute;
      width: 200px;
      text-align: center;
      color:#e80d2d;
      font-size: 55px;
    }
    .oddLeft h1{
      top: 43px;
      left: -42px;
      font-weight:bold;
    }

    .oddRight{
      display: inline-block;
      width:460px;
      margin-left: 76px;
      padding: 12px 0 12px 80px;
      border-radius: 10px;
      border: 2px solid #ebc246;
    }
    .evenLeft{
      display: inline;
      height: 200px;
      overflow: hidden;
    }
    .evenLeft .medalImg{
      position: absolute;
      top: -48px;
      right: -116px;
      width: 290px;
    }
    .evenLeft .numImg{
      z-index: 1;
      position: absolute;
      top: -65px;
      right: -50px;
      width: 150px;
    }
    .evenLeft h1{
      position: absolute;
      top: 43px;
      right: -45px;
      color:#e80d2d;
    }
    .evenRight{
      padding: 12px;
      padding-left: 20px;
      border-radius: 10px;
      border: 2px solid #ebc246;
    }
    .avg-performance {
      display: flex;
      align-items: center;
      margin-bottom: 10px;
    }

   ul li .personScore{
    text-align: center;
    display: inline-block;
    margin-right: 13px;
    font-size: 25px;
   }
   ul li h2{
     display: inline;
   }
   .footer{
      color: #fafdf4;
      font-size:26px;
      text-align: center;
    }
    .yellowBorder{
      position: relative;
      background-color: pink;
    }
    .fs-26 {
      font-size: 25px;
    }
  </style>
</head>
<body>
   <div id="root">
      <img class="header-bg" src="https://safesfiles.oss-cn-beijing.aliyuncs.com/safes/ZHNX33656/区域-标题_202406272025.png" alt="">
      <img id="quarter-bg" src="https://safesfiles.oss-cn-beijing.aliyuncs.com/safes/ZHNX33656/1季度_202406272027.png" alt="季度">
      <img class="estimate-title" src="https://safesfiles.oss-cn-beijing.aliyuncs.com/safes/ZHNX33656/预获_202406271041.png" alt="">
      <img class="estimate-bg" src="https://safesfiles.oss-cn-beijing.aliyuncs.com/safes/ZHNX33656/预获2_202406271041.png" alt="">
      <div class="time">
        统计口径：人均标准业绩/元
        <br>
        统计时间：${firstDay}-${lastDay}
      </div>
      <div class="content">
        <ul>

        </ul>
      </div>
      <div class="footer">
        再/接/再/厉&nbsp&nbsp&nbsp&nbsp再/创/佳/绩
      </div>
  </div>
</body>
</html>
<script>
  const dataList = ${datasJson};
  const quarter = "${quarter}"

<#noparse>
// 季度图更换
  const quarterDom = document.getElementById('quarter-bg');
  // 此处替换季度字段 ！！！！
  quarterDom.src = `https://safesfiles.oss-cn-beijing.aliyuncs.com/safes/ZHNX33656/${quarter}季度_202406272043.png`;

// 移除特定字符
  const removeWords = function (text,removeword='区域') {
    if(!text){
      return text
    }
    return text.replace(removeword, '');
  }

  // 获取<ul>元素
  const ulElement = document.querySelector('#root .content ul');

  // 遍历创建新的li元素
  dataList.forEach((data,index) => {
    if((index+1) %2 != 0){
      // 奇数
      const evenLiElement = document.createElement('li');
          // 根据数据动态生成内容
      evenLiElement.innerHTML = `
        <div class="oddLeft">
          <img class="numImg" src="https://safesfiles-test.oss-cn-beijing.aliyuncs.com/safes/ZHNX13223/NO${data.rank}_202406261756.png" alt="">
          <img class="medalImg" src="https://safesfiles-test.oss-cn-beijing.aliyuncs.com/safes/ZHNX13223/月区域风云榜1_202406261756.png" alt="">
          <h1>${removeWords(data.area_name)}</h1>
        </div>
        <div class="oddRight">
          <div class="avg-performance">
            <div class="personScore">
              <p>${quarter}季度人均</p>
              <p>标准业绩</p>
            </div>
            <h2>${data.sq_assess_convert_insurance_amt_avg}&nbsp;元</h2>
          </div>
          <p class="fs-26">${quarter}季度标准业绩：${data.sq_access_convert_insurance_amt}万元</p>
        </div>
      `;
      // 添加到<ul>的末尾
      ulElement.appendChild(evenLiElement);
    }else{
      // 偶数
      const oddLiElement = document.createElement('li');
      oddLiElement.innerHTML = `
       <div class="evenLeft">
                <img class="numImg" src="https://safesfiles-test.oss-cn-beijing.aliyuncs.com/safes/ZHNX13223/NO${data.rank}_202406261756.png" alt="">
                <img class="medalImg" src="https://safesfiles-test.oss-cn-beijing.aliyuncs.com/safes/ZHNX13223/月区域风云榜1_202406261756.png" alt="">
                <h1>${removeWords(data.area_name)}</h1>
              </div>
              <div class="evenRight">
                <div class="avg-performance">
                  <div class="personScore">
                    <p>${quarter}季度人均</p>
                    <p>标准业绩</p>
                  </div>
                  <h2>${data.sq_assess_convert_insurance_amt_avg}&nbsp;元</h2>
                </div>
                <p class="fs-26">${quarter}季度标准业绩：${data.sq_access_convert_insurance_amt}万元</p>
              </div>
      `
        // 添加到<ul>的末尾
          ulElement.appendChild(oddLiElement);
    }

  })

 </#noparse>
</script>