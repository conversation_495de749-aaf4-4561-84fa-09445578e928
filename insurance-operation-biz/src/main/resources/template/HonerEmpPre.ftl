<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>个人风云榜</title>
  <style>
    * {
      margin: 0;
      padding: 0;
    }

    body {
      font-family: Arial, sans-serif;
      margin: 0;
      padding: 0;
    }

    .copywriting {
      padding: 10px;
      font-size: 14px;
      background-color: #fff;
    }

    .poster {
      position: relative;
      width: 750px;
      margin: 0 auto;
      background: url('https://safesfiles.oss-cn-beijing.aliyuncs.com/safes/ZHNX33656/月个人风云榜背景_202309211150.png') no-repeat;
      background-size: 100% 100%;
      overflow: hidden;
    }

    .poster-header img {
      display: block;
      width: 650px;
      height: auto;
      margin: 20px auto;
    }
    .estimate-title {
      position: absolute;
      top: 173px;
      right: 42px;
      width: 60px;
      height: auto;
    }
    .estimate-bg {
      display: block;
      width: 300px;
      height: auto;
      margin: 10px auto 5px;
    }

    .poster-month {
      position: absolute;
      top: 56px;
      right: 53px;
      width: 80px;
      text-align: center;
      font-size: 40px;
      font-family: math;
      color: #f6d993;
    }

    .poster-info{
      color: #f6d993;
      font-size: 30px;
    }

    .poster-info {
      padding: 0 40px 0;
      text-align: right;
    }

    .poster-bottom {
      margin: 10px 0 30px;
      text-align: center;
      color: #fff;
      font-size: 26px;
    }

    .poster-organization {
      margin: 10px 40px;
      background-color: #9f1515;
      border-radius: 16px;
      overflow: hidden;
    }

    .poster-organization table {
      width: 100%;
      border-collapse: collapse;
      color: #eeeeeef0;
    }

    .poster-organization table thead th{
      padding: 6px;
      font-size: 30px;
      background: linear-gradient(to bottom,#f2d9b1,#e0b369,#ead0a4); /* 表头背景色 */
      color: #af1e1c;
    }
    table tbody tr{
      border-bottom: 1px solid #af1e1c;
    }

    .poster-organization table tbody td {
      padding: 6px 0;
      font-size: 26px;
      text-align: center;
    }

    .high-light {
      color: #dd3737;
      font-weight: bolder;
    }

    .title-bold {
      color: green;
      font-weight: bolder;
    }

    .ml-20 {
      margin-left: 40px;
    }

    .bold {
      font-weight: bolder;
    }

    footer{
      padding: 0 20px 10px;
      background-color: #fff;
      font-size: 28px;
    }

    .poster-top{
      display: flex;
      justify-content: center;
    }
    .poster-top-item{
      display: flex;
      flex-direction: column;
      align-items: center;
      color: #e0d1b8;
      font-size: 26px;
    }
    .poster-top-item:not(:nth-last-child(1)){
      margin-right: 24px;
    }
    .poster-top-item:nth-child(1){
      order: 2;
    }
    .poster-top-item:nth-child(2){
      order: 1;
      margin-top: 30px;
    }
    .poster-top-item:nth-child(3){
      order: 3;
      margin-top: 50px;
    }
    .poster-top-item__avatar{
      display: flex;
      justify-content: center;
      align-items: center;
      margin-bottom: -30px;
      width: 176px;
      height: 200px;
      background: url('https://safesfiles.oss-cn-beijing.aliyuncs.com/safes/ZHNX33656/金_202309211845.png') no-repeat;
      background-size: contain;
    }
    .avatar{
      width: 132px;
      height: 132px;
      margin-top: 24px;
      border-radius: 50%;
    }
    .poster-top-item__name{
      position: relative;
      padding: 0 18px;
      background: linear-gradient(to right,#dfb267,#eaddc6,#dfb267);
      color: #af1e1c;
      font-size: 30px;
      font-weight: bold;
      border-radius: 32px;
    }
    .rank-img{
      position: absolute;
      top: -24px;
      left: 20px;
      width: 50px;
      height: auto;
    }
    .fs-20 {
      font-size: 20px;
    }

  </style>
</head>
https://safesfiles.oss-cn-beijing.aliyuncs.com/safes/ZHNX33656/${quarter}季度_202406272043.png
<body>
  <div class="poster">
    <div class="poster-header">
      <img src="https://safesfiles.oss-cn-beijing.aliyuncs.com/safes/ZHNX33656/个人-标题_202406271039.png" alt="">
    </div>
    <img class="estimate-title" src="https://safesfiles.oss-cn-beijing.aliyuncs.com/safes/ZHNX33656/预获_202406271041.png" alt="">
    <img class="estimate-bg" src="https://safesfiles.oss-cn-beijing.aliyuncs.com/safes/ZHNX33656/预获2_202406271041.png" alt="">
    <div class="poster-info">
      <span class="ml-20">统计时间：${firstDay}-${lastDay}</span>
    </div>
    <div class="poster-top" id="top-three">

    </div>
    <div class="poster-organization" id="organization-rank">
      <table border="0">
        <thead>
          <tr>
            <th>排名</th>
            <th>区域</th>
            <th>分支</th>
            <th>姓名</th>
            <th><span class="fs-20">${quarter}季度标准业绩</span></th>
          </tr>
        </thead>
        <tbody id="table-tbody">

        </tbody>
      </table>
    </div>

    <div class="poster-bottom">
      <span>再/接/再/厉</span>
      <span class="ml-20">再/创/佳/绩</span>
    </div>
  </div>
</body>

<script>
  const doms = {
    areaDom : document.getElementById('table-tbody'),
    copywritingDom:document.getElementById('copywriting-rank'),
    topThreeDom : document.getElementById('top-three')
  }

  const data = ${datasJson}
   const quarter = "${quarter}"
  const { datas,maxAreaName,yearMonth,areaNames,maxAreaPre,time } = data
<#noparse>

  // 时间字段



  // 头像展示前三个，并列第三的话取默认前面的
  const topThreeList = datas && datas.slice(0,3)
  doms.topThreeDom.innerHTML = topThreeList && topThreeList.map((item,index)=>{
    // 设置默认头像
    const avatarImg = item.avatar || 'https://safesfiles.oss-cn-beijing.aliyuncs.com/safes/ZHNX33656/默认头像_202309211954.png'
    const rankImg = {
      1:'https://safesfiles.oss-cn-beijing.aliyuncs.com/safes/ZHNX33656/NO1_202309211853.png',
      2:'https://safesfiles.oss-cn-beijing.aliyuncs.com/safes/ZHNX33656/NO2_202309211958.png',
      3:'https://safesfiles.oss-cn-beijing.aliyuncs.com/safes/ZHNX33656/NO3_202309211958.png',
    }
    // 排名头像背景
    const rankBg = {
      1: 'https://safesfiles.oss-cn-beijing.aliyuncs.com/safes/ZHNX33656/金_202309211845.png',
      2:'https://safesfiles.oss-cn-beijing.aliyuncs.com/safes/ZHNX33656/银_202309211955.png',
      3:'https://safesfiles.oss-cn-beijing.aliyuncs.com/safes/ZHNX33656/铜_202309211957.png',
    }
    const name = item.bch_name ? `${item.bch_name}-${item.emp_name}` : item.emp_name
    const rank = item.rank
    return `
    <div class="poster-top-item">
        <div class="poster-top-item__avatar" style="background-image: url(${rankBg[rank]})">
          <img src="${avatarImg}" alt="" class="avatar">
        </div>
        <div class="poster-top-item__name">
          <img src="${rankImg[rank]}" alt="No1" class="rank-img">
          <span>${name}</span>
        </div>
        <p>${quarter}季度标准保费</p>
        <span>${item.sq_access_convert_insurance_amt}万</span>
      </div>
    `
  }).join("")

  const otherDatas = datas && datas.slice(3)

  // 填充列表模块
  doms.areaDom.innerHTML = otherDatas && otherDatas.map((item,index) => {
    return `
        <tr>
          <td>${formatText(item.rank)}</td>
          <td>${formatText(item.area_name)}</td>
          <td>${formatText(item.bch_name)}</td>
          <td>${formatText(item.emp_name)}</td>
          <td>${formatText(item.sq_access_convert_insurance_amt)}万</td>
        </tr>
    `
  }).join("")

  function formatText(text){
    return text == null ? '-' : text
  }
</script>
 </#noparse>
</html>