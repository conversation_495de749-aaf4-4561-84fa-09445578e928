<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cfpamf.ms.insur.operation.msg.dwdao.DwGoalMapper">
    <select id="listRegionGoalByTime" resultType="com.cfpamf.ms.insur.operation.msg.pojo.dto.RegionGoalDTO">
        select
               rpt_date rptDate,
               area_name regionName,
               term ,
               product_class productClass,
               goal ,
               insure_amount amt,
        cast((case when COALESCE(goal,0)=0 then 0 else COALESCE(insure_amount,0.0000)/COALESCE(goal,0) end ) *100 as decimal (10,2))  as rate
        from rpt.insurance_930_trace_dfp
        where 1=1

        <if test="startDate!=null">
            and rpt_date >= #{startDate}
        </if>
        <if test="endDate!=null">
            <![CDATA[  and rpt_date <= #{endDate} ]]>
        </if>
        <if test="productClass!=null and productClass!=''">
            and product_class=#{productClass}
        </if>
        <if test="term!=null and term!=''">
            and term=#{term}
        </if>
        <if test="queryDateList!=null and queryDateList.size>0">
            AND rpt_date IN
            <foreach collection="queryDateList" item="monthEndDay" open="(" close=")" index="index" separator=",">
                to_date(#{monthEndDay,jdbcType=VARCHAR},'yyyy-MM-dd')
            </foreach>
        </if>
        order by rpt_date desc,rate desc

    </select>
</mapper>
