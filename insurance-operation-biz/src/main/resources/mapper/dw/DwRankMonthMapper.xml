<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cfpamf.ms.insur.operation.msg.dwdao.DwRankMonthMapper">
    <select id="listRankArea" resultType="com.cfpamf.ms.insur.operation.msg.pojo.vo.StatRankVO">
        select area_name region_name,
               insured_amt_year amts,
               CAST((CASE
                         WHEN COALESCE(emp_cnt_year, 0) = 0 THEN 0
                         ELSE COALESCE(insured_amt_year, 0.000) /
                              emp_cnt_year END) AS DECIMAL(10, 2)) AS avg_people
        from rpt.insurance_order_area_stat_dfp
        where rpt_date = #{startDate}
        order by avg_people desc,
                 insured_amt_year desc
        limit #{maxRows}
    </select>
    <select id="listRankBch" resultType="com.cfpamf.ms.insur.operation.msg.pojo.vo.StatRankVO">

        select area_name region_name,
               bch_name org_name,
               insured_amt_year amts,
               CAST((CASE
                         WHEN COALESCE(emp_cnt_year
                                  , 0) = 0 THEN 0
                         ELSE COALESCE(insured_amt_year, 0.000) /
                              emp_cnt_year END) AS DECIMAL(10, 2)) AS avg_people,
               emp_cnt_year,
               emp_cnt_month
        from rpt.insurance_order_bch_stat_dfp
        where rpt_date = #{startDate}
         and operation_status='正常'
        order by avg_people desc,
                 insured_amt_year desc
        limit #{maxRows}
    </select>

    <select id="listRankEmp" resultType="com.cfpamf.ms.insur.operation.msg.pojo.vo.StatRankVO">
        select area_name        region_name,
               bch_name         org_name,
               emp_name         user_name,
               insured_amt_year amts
        from rpt.insurance_order_emp_stat_dfp
        where rpt_date = #{startDate}
        order by insured_amt_year desc,
                 insured_cnt_year
            desc
        limit #{maxRows}
    </select>


</mapper>
