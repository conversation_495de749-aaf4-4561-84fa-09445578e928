<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cfpamf.ms.insur.operation.honor.safespgdao.AdsInsuranceMarketingProgressDao">
    <select id="resultList" resultType="com.cfpamf.ms.insur.operation.honor.dto.HonorsCalculateResults">
        select *
        from (
            select
                ${honorsRulesConfigurationsId} as honorsRulesConfigurationsId,
                #{level} as level,
                #{period} as period,
                pt,
                ${fields},
                ${honorNormCode},
                rank() over (order by ${orderSql}) rank_num,
                'system' as create_by,
                'system' as update_by
            from report.${tableName}
            where
                pt = #{pt}
                and area_name like concat('%','区域')
            <if test="msEmpCntGt3Sql != null and msEmpCntGt3Sql != ''">
                ${msEmpCntGt3Sql}
            </if>
        ) t
        where
        <![CDATA[
             rank_num <= ${rankNum}
        ]]>
        order by ${orderSql}
    </select>

    <select id="resultListByPeople" resultType="com.cfpamf.ms.insur.operation.honor.dto.HonorsCalculateResults">
        select *
        from (
            select
            ${honorsRulesConfigurationsId} as honorsRulesConfigurationsId,
            #{level} as level,
            #{period} as period,
            pt,
            ${fields},
            ${honorNormCode},
            rank() over (order by ${orderSql}) rank_num,
            'system' as create_by,
            'system' as update_by
            from report.${tableName}
            where
                pt = #{pt}
        ) t
        where 1=1
        <if test="codesConditionSql != null and codesConditionSql != ''">
            ${codesConditionSql}
        </if>
        order by ${orderSql}
    </select>
</mapper>