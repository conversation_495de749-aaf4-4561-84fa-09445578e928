<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.cfpamf.ms.insur.operation.assistant.dao.safespg.AdsInsuranceBchMarketingProgressDfpMapper">

    <select id="selectOneObject" resultType="com.cfpamf.ms.insur.operation.assistant.entity.safespg.AdsInsuranceBchMarketingProgressDfp">
        select * from report.ads_insurance_bch_marketing_progress_dfp a
        where a.bch_code = #{bchCode}
        and a.pt = #{pt}
    </select>

    <select id="selectAllBch" resultType="com.cfpamf.ms.insur.operation.assistant.entity.safespg.AdsInsuranceBchMarketingProgressDfp">
        SELECT bch_code FROM "ads_insurance_bch_marketing_progress_dfp" where pt =  #{pt}
    </select>
</mapper>