<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cfpamf.ms.insur.operation.pk.safepgdao.AdsInsuranceIndexProgressDao">
    <select id="selectMonthEmpOrg" resultType="com.cfpamf.ms.insur.operation.pk.pojo.dto.MonthEmpCntDto">
        select bch_code code,avg(ms_emp_cnt)emp_cnt
        from (select bch_code, min(ms_emp_cnt) ms_emp_cnt
        from phoenix.ads_insurance_bch_index_progress_dfp
        where pt between #{start} and #{end}
        and bch_code in
        <foreach collection="codeList" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        group by bch_code, substr(pt, 1, 6)) t
        group by bch_code
    </select>
    <select id="selectMonthEmpArea" resultType="com.cfpamf.ms.insur.operation.pk.pojo.dto.MonthEmpCntDto">
        select area_code code,avg(ms_emp_cnt) emp_cnt
        from (select area_code, min(ms_emp_cnt) ms_emp_cnt
        from phoenix.ads_insurance_area_index_progress_dfp
        where pt between #{start} and #{end}
        and area_code in
        <foreach collection="codeList" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        group by area_code, substr(pt, 1, 6)) t
        group by area_code
    </select>

    <select id="selectEmpHistoryIndicators"
            resultType="com.cfpamf.ms.insur.operation.pk.pojo.dto.HistoryIndicatorsDto">

        select emp_id as code,
        emp_name as name,
        max(manage_cust_cnt) manage_cust_cnt,
        sum(sd_land_customer_cnt) land_customer_cnt,
        sum(cd_norm_insurance_amt) norm_insurance_amt,
        sum(cd_actual_receipt_performance) actual_receipt_performance,
        max(job_months) job_months
        from phoenix.ads_insurance_emp_index_progress_dfp
        where emp_id in
        <foreach collection="codeList" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        and pt between #{start} and #{end}
        group by emp_id, emp_name
    </select>

    <select id="selectOrgHistoryIndicators"
            resultType="com.cfpamf.ms.insur.operation.pk.pojo.dto.HistoryIndicatorsDto">
        select bch_code as code,
        bch_name as name,
        max(ms_emp_cnt) ms_emp_cnt,
        max(manage_cust_cnt) manage_cust_cnt,
        sum(sd_land_customer_cnt) land_customer_cnt,
        sum(cd_norm_insurance_amt) norm_insurance_amt,
        sum(cd_actual_receipt_performance) actual_receipt_performance,
        max(set_months_15rule) set_months_15rule
        from phoenix.ads_insurance_bch_index_progress_dfp
        where bch_code in
        <foreach collection="codeList" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        and pt between #{start} and #{end}
        group by bch_code, bch_name
    </select>

    <select id="selectAreaHistoryIndicators"
            resultType="com.cfpamf.ms.insur.operation.pk.pojo.dto.HistoryIndicatorsDto">
        select area_code as code,
        area_name as name,
        max(ms_emp_cnt) ms_emp_cnt,
        max(manage_cust_cnt) manage_cust_cnt,
        sum(sd_land_customer_cnt) land_customer_cnt,
        sum(cd_norm_insurance_amt) norm_insurance_amt,
        sum(cd_actual_receipt_performance) actual_receipt_performance
        from phoenix.ads_insurance_area_index_progress_dfp
        where area_code in
        <foreach collection="codeList" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        and pt between #{start} and #{end}
        group by area_code, area_name
    </select>
    <select id="selectEmpHistoryIndicatorsMathch"
            resultType="com.cfpamf.ms.insur.operation.pk.pojo.dto.PkMatchOpponentsDto">
        select area_code as region_code,
        area_name as region_name,bch_code as branch_code,
        bch_name as branch_name,emp_id as user_code,emp_id as user_code,
        emp_name as user_name,
        sum(${pkColumn}) as value
        from phoenix.ads_insurance_emp_index_progress_dfp
        where
        pt between  #{startDateStr} and #{endDateStr}
        and emp_id not in
        <foreach collection="blackUserCodeList" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
          <if test="matchName!=null and matchName!=''">
              and emp_name like concat(#{matchName},'%')
          </if>
        and ${pkColumn} is not null
        group by emp_id, emp_name,bch_code, bch_name,area_code, area_name
        <if test="indicatorsMaxValue != null or indicatorsMinValue!=null">
            having  <if test="indicatorsMinValue!=null"> <![CDATA[    sum(${pkColumn})/#{matchValue} >=#{indicatorsMinValue} ]]> </if>
            <if test="indicatorsMinValue!=null and  indicatorsMaxValue!=null"> and </if>
            <if test="indicatorsMaxValue != null"> <![CDATA[ sum(${pkColumn})/#{matchValue}  <= #{indicatorsMaxValue} ]]> </if>
        </if>
    </select>

    <select id="selectOrgHistoryIndicatorsMatch"
            resultType="com.cfpamf.ms.insur.operation.pk.pojo.dto.PkMatchOpponentsDto">

        with t_leader as (
        select bch_code, bch_leader_id, bch_leader_name
        from phoenix.ads_insurance_bch_index_progress_dfp
        where pt = (select max(pt) from phoenix.ads_insurance_bch_index_progress_dfp)
        )
        <if test="avg == true">
            , t_emp_month as (select bch_code, avg(ms_emp_cnt) emp_cnt
            from (select bch_code, min(ms_emp_cnt) ms_emp_cnt
            from phoenix.ads_insurance_bch_index_progress_dfp
            where pt between #{startDateStr} and #{endDateStr}
            group by bch_code, substr(pt, 1, 6)) t
            group by bch_code)
        </if>
        select t1.area_code as region_code,
        t1.area_name as region_name,t1.bch_code as branch_code,
        t1.bch_name as branch_name,
        t3.bch_leader_id as user_code,t3.bch_leader_name as user_name,
        sum(${pkColumn})<if test="avg == true">/ min(t2.emp_cnt)</if> as value

        from phoenix.ads_insurance_bch_index_progress_dfp t1
        left join t_leader t3 on t1.bch_code = t3.bch_code
        <if test="avg == true">
            left join t_emp_month t2 on t1.bch_code = t2.bch_code
        </if>
        where pt between #{startDateStr} and #{endDateStr}
      and t1.bch_code not in
          <foreach collection="blackUserCodeList" item="item" open="(" close=")" separator=",">
        #{item}
    </foreach>
        <if test="matchName!=null and matchName!=''">
            and bch_name like concat(#{matchName},'%')
        </if>
        and ${pkColumn} is not null
        group by t1.bch_code, bch_name,area_code, area_name,t3.bch_leader_id, t3.bch_leader_name
        <if test="indicatorsMaxValue != null or indicatorsMinValue!=null">
            having
            <if test="indicatorsMinValue!=null">sum(${pkColumn})<if test="avg == true">/ min(t2.emp_cnt)</if> /#{matchValue}
                <![CDATA[  >= ]]> #{indicatorsMinValue}
            </if>
            <if test="indicatorsMinValue!=null and  indicatorsMaxValue!=null">and</if>
            <if test="indicatorsMaxValue != null">sum(${pkColumn}) <if test="avg == true">/ min(t2.emp_cnt)</if>/#{matchValue}
                <![CDATA[  <=  ]]> #{indicatorsMaxValue}
            </if>
        </if>
    </select>
    <select id="selectAreaHistoryIndicatorsMatch"
            resultType="com.cfpamf.ms.insur.operation.pk.pojo.dto.PkMatchOpponentsDto">
        <if test="avg == true">
            with t_emp_month as (select area_code, avg(ms_emp_cnt) emp_cnt
            from (select bch_code, min(ms_emp_cnt) ms_emp_cnt
            from phoenix.ads_insurance_area_index_progress_dfp
            where pt between  #{startDateStr} and #{endDateStr}
            group by area_code, substr(pt, 1, 6)) t
            group by area_code)
        </if>
        select t1.area_code as region_code,
        t1.area_name as region_name,
        sum(${pkColumn})<if test="avg == true"> / min(t2.emp_cnt)</if> as value
        from phoenix.ads_insurance_area_index_progress_dfp t1
        <if test="avg == true">
            left join t_emp_month t2 on t1.area_code = t2.area_code
        </if>
        where pt between #{startDateStr} and #{endDateStr}

       and t1.area_code not in
        <foreach collection="blackUserCodeList" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        <if test="matchName!=null and matchName!=''">
            and area_name like concat(#{matchName},'%')
        </if>
        and ${pkColumn} is not null
        group by t1.area_code, area_name
        <if test="indicatorsMaxValue != null or indicatorsMinValue!=null">
            having
            <if test="indicatorsMinValue!=null"> sum(${pkColumn})<if test="avg == true">/ min(t2.emp_cnt) </if>/#{matchValue} <![CDATA[  >= ]]> #{indicatorsMinValue}</if>
            <if test="indicatorsMinValue!=null and  indicatorsMaxValue!=null">and</if>
            <if test="indicatorsMaxValue != null"> sum(${pkColumn}) <if test="avg == true">/ min(t2.emp_cnt) </if>/#{matchValue}<![CDATA[  <=  ]]> #{indicatorsMaxValue}</if>
        </if>
    </select>

    <select id="selectEmpHistoryIndicatorsMe"
            resultType="com.cfpamf.ms.insur.operation.pk.pojo.dto.PkMatchOpponentsDto">
        select area_code as region_code,
               area_name as region_name,bch_code as branch_code,
               bch_name as branch_name,emp_id as user_code,
        emp_name as user_name,
        sum(${pkColumn}) as value
        from phoenix.ads_insurance_emp_index_progress_dfp
        where
        <![CDATA[ emp_id = #{userCode}]]>
        and pt between  #{startDateStr} and #{endDateStr}
        and ${pkColumn} is not null
        group by emp_id, emp_name,bch_code, bch_name,area_code, area_name

    </select>

    <select id="selectOrgHistoryIndicatorsMe"
            resultType="com.cfpamf.ms.insur.operation.pk.pojo.dto.PkMatchOpponentsDto">
        <if test="avg == true">
            with t_emp_month as (select bch_code, avg(ms_emp_cnt) emp_cnt
            from (select bch_code, min(ms_emp_cnt) ms_emp_cnt
            from phoenix.ads_insurance_bch_index_progress_dfp
            where pt between  #{startDateStr} and #{endDateStr}
            group by bch_code, substr(pt, 1, 6)) t
            group by bch_code)
        </if>
        select t1.area_code as region_code,
        t1.area_name as region_name,t1.bch_code as branch_code,
        t1.bch_name as branch_name,
        sum(${pkColumn})<if test="avg == true"> / min(t2.emp_cnt)</if> as value
        from phoenix.ads_insurance_bch_index_progress_dfp t1
        <if test="avg == true">
            left join t_emp_month t2 on t1.bch_code = t2.bch_code
        </if>
        where pt between #{startDateStr} and #{endDateStr}
       and t1.bch_code = #{branchCode}
         and ${pkColumn} is not null
        group by t1.bch_code, bch_name,area_code, area_name
    </select>
    <select id="selectAreaHistoryIndicatorsMe"
            resultType="com.cfpamf.ms.insur.operation.pk.pojo.dto.PkMatchOpponentsDto">
        <if test="avg == true">
            with t_emp_month as (select area_code, avg(ms_emp_cnt) emp_cnt
            from (select bch_code, min(ms_emp_cnt) ms_emp_cnt
            from phoenix.ads_insurance_area_index_progress_dfp
            where pt between  #{startDateStr} and #{endDateStr}
            group by area_code, substr(pt, 1, 6)) t
            group by area_code)
        </if>
        select t1.area_code as region_code,
        t1.area_name as region_name,
        sum(${pkColumn})<if test="avg == true"> / min(t2.emp_cnt)</if> as value
        from phoenix.ads_insurance_area_index_progress_dfp t1
        <if test="avg == true">
            left join t_emp_month t2 on t1.area_code = t2.area_code
        </if>
        where pt between #{startDateStr} and #{endDateStr}
       and t1.area_code = #{regionCode}
        and ${pkColumn} is not null
        group by t1.area_code, area_name
    </select>
</mapper>