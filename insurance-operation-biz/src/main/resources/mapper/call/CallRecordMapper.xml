<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.insur.operation.call.dao.CallRecordMapper">

    <resultMap id="BaseResultMap" type="com.cfpamf.ms.insur.operation.call.entity.CallRecordPo">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="policy_no" property="policyNo" jdbcType="VARCHAR"/>
        <result column="customer_phone" property="customerPhone" jdbcType="VARCHAR"/>
        <result column="employee_id" property="employeeId" jdbcType="VARCHAR"/>
        <result column="employee_name" property="employeeName" jdbcType="VARCHAR"/>
        <result column="employee_phone" property="employeePhone" jdbcType="VARCHAR"/>
        <result column="client_id" property="clientId" jdbcType="VARCHAR"/>
        <result column="client_name" property="clientName" jdbcType="VARCHAR"/>
        <result column="call_sid" property="callSid" jdbcType="VARCHAR"/>
        <result column="call_status" property="callStatus" jdbcType="INTEGER"/>
        <result column="begin_call_time" property="beginCallTime" jdbcType="TIMESTAMP"/>
        <result column="end_call_time" property="endCallTime" jdbcType="TIMESTAMP"/>
        <result column="duration" property="duration" jdbcType="INTEGER"/>
        <result column="record_url" property="recordUrl" jdbcType="VARCHAR"/>
        <result column="record_duration" property="recordDuration" jdbcType="VARCHAR"/>
        <result column="record_status" property="recordStatus" jdbcType="INTEGER"/>
        <result column="record_time" property="recordTime" jdbcType="TIMESTAMP"/>
        <result column="follow_match_status" property="followMatchStatus" jdbcType="INTEGER"/>
        <result column="follow_record_id" property="followRecordId" jdbcType="BIGINT"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
        <result column="fail_reason" property="failReason" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="enabled_flag" property="enabledFlag" jdbcType="INTEGER"/>
    </resultMap>

    <select id="selectByPolicyNo" resultMap="BaseResultMap">
        SELECT * FROM call_record 
        WHERE policy_no = #{policyNo} 
        AND enabled_flag = 0 
        ORDER BY create_time DESC
    </select>

    <select id="selectUnRecordedCalls" resultMap="BaseResultMap">
        SELECT * FROM call_record 
        WHERE record_status = 0 
        AND call_status IN (1, 2) 
        AND call_sid IS NOT NULL 
        AND enabled_flag = 0 
        ORDER BY create_time ASC 
        LIMIT #{limit}
    </select>

    <select id="selectUnMatchedFollowCalls" resultMap="BaseResultMap">
        SELECT * FROM call_record 
        WHERE record_status = 1 
        AND follow_match_status = 0 
        AND record_url IS NOT NULL 
        AND enabled_flag = 0 
        ORDER BY create_time ASC 
        LIMIT #{limit}
    </select>

    <select id="selectByCallSid" resultMap="BaseResultMap">
        SELECT * FROM call_record 
        WHERE call_sid = #{callSid} 
        AND enabled_flag = 0 
        LIMIT 1
    </select>

    <update id="updateRecordInfo">
        UPDATE call_record 
        SET record_url = #{recordUrl},
            record_duration = #{recordDuration},
            record_status = #{recordStatus},
            record_time = NOW(),
            update_time = NOW()
        WHERE id = #{id}
    </update>

    <update id="updateFollowMatchInfo">
        UPDATE call_record 
        SET follow_record_id = #{followRecordId},
            follow_match_status = #{followMatchStatus},
            update_time = NOW()
        WHERE id = #{id}
    </update>

</mapper>
