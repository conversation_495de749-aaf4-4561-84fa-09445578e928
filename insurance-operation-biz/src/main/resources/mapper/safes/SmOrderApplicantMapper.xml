<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.insur.operation.order.dao.SmOrderApplicantMapper">
    <select id="getIdNumberByOrderId" resultType="com.cfpamf.ms.insur.operation.customer.dto.CustomerDto">
        select max(t1.id) customerId,t.idNumber
        from sm_order_applicant t
        left join customer t1 on t.idNumber = t1.idNumber
        where fhOrderId = #{orderId} and t.enabled_flag=0
        group by t.idNumber
    </select>
</mapper>
