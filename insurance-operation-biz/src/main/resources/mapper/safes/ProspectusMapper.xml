<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.insur.operation.prospectus.dao.ProspectusMapper">

    <select id="getById" resultType="com.cfpamf.ms.insur.operation.prospectus.entity.Prospectus">
        select *
        from operation_prospectus
        where id = #{id}
          and enabled_flag = 0
    </select>

    <select id="getProspectusVoById" resultType="com.cfpamf.ms.insur.operation.prospectus.vo.ProspectusVo">
        select t1.*,
               t2.explain_video_file_path,
               t2.recommendation_reason,
               t3.userName   as customerManager,
               t3.userMobile as customerManagerMobile
        from operation_prospectus t1
                 left join operation_prospectus_config t2
                           on t1.product_id = t2.product_id and t2.enabled_flag = 0
                 left join auth_user t3
                           on t3.userId = t1.create_by and t3.enabled_flag = 0
        where t1.id = #{id}
          and t1.enabled_flag = 0
    </select>

    <select id="search" resultType="com.cfpamf.ms.insur.operation.prospectus.vo.ProspectusVo">
        select t1.* ,t2.explain_video_file_path,t2.recommendation_reason
        from operation_prospectus t1
        left join operation_prospectus_config t2
        on t1.product_id = t2.product_id and t2.enabled_flag = 0
        where t1.enabled_flag = 0
        <if test="name !=  null and name != ''">
            AND t1.name like CONCAT(#{name},'%')
        </if>
        <if test="userId !=  null and userId != ''">
            AND t1.create_by =#{userId}
        </if>
        order by t1.update_time DESC
    </select>

    <select id="getCountByUserIdCreated" resultType="java.lang.Integer">
        select count(1)
        from operation_prospectus
        where create_by = #{userId}
          and enabled_flag = 0
    </select>

    <select id="getThirtyDaysCountByUserIdCreated" resultType="java.lang.Integer">
        select count(1)
        from operation_prospectus
        where create_by = #{userId}
          and enabled_flag = 0
          and create_time >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
    </select>
    <select id="getCountByProductId" resultType="java.lang.Integer">
        select count(1)
        from operation_prospectus
        where product_id = #{productId}
          and enabled_flag = 0
    </select>
</mapper>