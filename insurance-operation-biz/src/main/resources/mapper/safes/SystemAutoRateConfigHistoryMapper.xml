<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.insur.operation.auto.dao.SystemAutoRateConfigHistoryMapper">
    <select id="selectByProductId" resultType="com.cfpamf.ms.insur.operation.auto.dto.SystemAutoRateConfigListDto">
        select t.version,t1.userName as updateBy,t.update_Time,t.product_id
        from system_auto_rate_config_history t
        left join auth_user t1 on t.update_by = t1.userId and t.enabled_flag = 0
        where t.product_id = #{productId} and t.enabled_flag = 0
        group by t.product_id,t.version
        order by t.version desc
    </select>

    <select id="selectByProductIdAndVersion" resultType="com.cfpamf.ms.insur.operation.auto.dto.SystemAutoRateConfigDto">
        select
            t.product_id as productId,
            t1.productName,
            t1.channel,
            t.remark as remark,
            t3.userName as updateBy,
            t.update_time as updateTime,
            t.id,
            t.version
        from system_auto_rate_config_history t
        left join sm_product t1 on t.product_id = t1.id and t1.enabled_flag = 0
        left join auth_user t3 on t3.userId = t.update_by and t3.enabled_flag = 0
        where t.product_id = #{productId} and t.version = #{version}
    </select>
</mapper>