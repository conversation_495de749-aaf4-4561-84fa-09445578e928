<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cfpamf.ms.insur.operation.activity.dao.SmCommissionDetailMapper">
    <select id="selectActivityOrder"
            resultType="com.cfpamf.ms.insur.operation.activity.dto.SimpleCommissionDetailOrderDTO">
        select so.fhOrderId, so.productId, so.paymentTime, so.recommendId,au.organizationName,au.regionName,
               au.regionCode
        from sm_order so
        left join auth_user au on so.recommendId = au.userId and au.enabled_flag = 0
        where so.paymentTime between #{start} and #{end}
        <if test="productIds != null and productIds.size()>0">
            and productId in
            <foreach collection="productIds" item="productId" open="(" close=")" separator=",">
                #{productId}
            </foreach>
        </if>
        <if test="regionCodes != null and regionCodes.size()>0">
            and au.regionCode in
            <foreach collection="regionCodes" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        and payStatus = '2'
        and au.id is not null
        and fhOrderId not in (select order_id
        from sm_add_commission_const_cache
        where sa_id = #{saId}
        <![CDATA[ and create_time<#{statTime} ]]>)
    </select>

    <select id="selectByOrderId" resultType="com.cfpamf.ms.insur.operation.activity.dto.RedEnvelopCommissionDetailDTO">
        select amount totalAmount,
               order_Id orderId,
               case when INSTR (order_Id ,'_') =0 then 0 else SUBSTRING_INDEX(order_Id,'_',-1) end orderIdIndex,
               insured_id_number idNumber,
               create_time createTime,
               policy_status policyStatus
        from sm_commission_detail
        where order_id like CONCAT('%', SUBSTRING_INDEX(#{orderId},'_',1), '%')
        <![CDATA[
          and account_time >='2023-03-01 00:00:00' and account_time <='2023-04-30 23:59:59'
        ]]>
    </select>

    <select id="sumConversionAmtByUser" resultType="com.cfpamf.ms.insur.operation.customer.dto.CustomerConversionDto">
        select commission_user_id emp_code,t1.userName empName,sum(if(`conversion_amount`,`conversion_amount`,0)) conversionAmt
        from sm_commission_detail t
        left join auth_user t1 on t.commission_user_id = t1.userId and t1.enabled_flag = 0
        where
        <![CDATA[
        `account_time` >= #{query.startDate} and account_time <= #{query.endDate}
        ]]>
        and `commission_user_id` in
        <foreach collection="query.empCodes" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        group by commission_user_id
    </select>

    <select id="getPolicyListForConversionAmt" resultType="com.cfpamf.ms.insur.operation.customer.dto.PolicyConversionAmtDto">
        with t_conversion_amount as (
        select t.`order_id` ,t.commission_user_id emp_code, sum(if(`conversion_amount`,`conversion_amount`,0)) conversion_amount from `sm_commission_detail` t
        where t.`account_time` <![CDATA[ >= ]]> #{startDate} and t.account_time <![CDATA[ < ]]> #{endDate}
        <if test="employeeIdList != null and employeeIdList.size()>0">
            and t.commission_user_id in
            <foreach collection="employeeIdList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        GROUP BY  t.`order_id` ,t.commission_user_id
        )
        select o.`fhOrderId` order_id ,
               co.emp_code, co.conversion_amount,
               cd.`insured_id_number` ,
               oa.`idNumber` id_number
        from `sm_order` o join `sm_product` p on o.`productId` =p.`id`
                          join t_conversion_amount co on co.order_id = o.`fhOrderId`
                          join `sm_commission_detail` cd on cd.`order_id` = o.`fhOrderId`
                          join `sm_order_applicant` oa on oa.`fhOrderId` = o.`fhOrderId`
        where p.`productAttrCode` = 'person'
        UNION
        select o.`fhOrderId` order_id ,
               co.emp_code,
               co.conversion_amount,
               null,
               oa.`idNumber` id_number
        from `sm_order` o join `sm_product` p on o.`productId` =p.`id`
                          join t_conversion_amount co on co.order_id = o.`fhOrderId`
                          join `sm_order_applicant` oa on oa.`fhOrderId` = o.`fhOrderId`
        where p.`productAttrCode` <![CDATA[ <> ]]> 'person'
    </select>

</mapper>
