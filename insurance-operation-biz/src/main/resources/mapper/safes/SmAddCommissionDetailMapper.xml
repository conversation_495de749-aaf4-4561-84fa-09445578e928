<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.insur.operation.reward.addcommission.dao.SmAddCommissionDetailMapper">

    <sql id="Base_Column_List">
        id
        , order_id, policy_no, insured_id_number, plan_id, risk_id, term_num, proportion,
    enabled_flag, create_time, update_time
    </sql>

    <update id="batchUpdate">
        insert into
        sm_add_commission_detail(order_id,policy_no,insured_id_number,plan_id,risk_id,term_num,proportion,enabled_flag,uuid,status)
        values
        <foreach collection="list" item="det" separator=",">
            (#{det.orderId},#{det.policyNo},#{det.insuredIdNumber},#{det.planId},#{det.riskId},#{det.termNum},#{det.proportion},#{det.enabledFlag},#{det.uuid},#{det.status}
            )
        </foreach>
        ON DUPLICATE KEY UPDATE
        proportion = values(proportion)
    </update>
    <update id="updateAmount">
        update sm_add_commission_detail
        set amount = #{amount} , add_commission_amount = #{addCommissionAmount}
        where uuid = #{uuid}
    </update>

    <update id="updateTotalAmount">
        insert into sm_add_commission_detail_sum(order_id,policy_no,insured_id_number,plan_id,term_num,proportion,add_commission_amount)
        select * from (select t.order_id, t.`policy_no` , t.`insured_id_number` , t.`plan_id` , t.`term_num`,t.proportion
                ,sum( `add_commission_amount`) as add_commission_amount
        from sm_add_commission_detail t
        where t.order_id = #{orderId}
        and t.insured_id_number = #{insuredIdNumber}
        and t.term_num = #{termNum}
        and t.enabled_flag = 0
        group by  t.order_id, t.`policy_no` , t.`insured_id_number` , t.`term_num`) t1
        ON DUPLICATE KEY UPDATE
        sm_add_commission_detail_sum.add_commission_amount = t1.add_commission_amount,
        sm_add_commission_detail_sum.proportion = t1.proportion
    </update>

    <select id="getDirtyData" resultType="com.cfpamf.ms.insur.operation.reward.addcommission.entity.SmAddCommissionDetail">
        select t.*
        from sm_add_commission_detail t
                 left join sm_commission_detail t1 on
                    t.policy_no = t1.policy_no and t1.insured_id_number = t.insured_id_number
                and t.order_id = t1.order_id
        where t1.id is null;
    </select>

    <insert id="insertAddCommissionDetailBackups">
        insert into sm_add_commission_detail_backups
        (detail_id,order_id,policy_no,insured_id_number,plan_id,risk_id,term_num,proportion,enabled_flag
        ,detail_create_time,detail_update_time,amount,add_commission_amount,uuid,status)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.id}, #{item.orderId}, #{item.policyNo}, #{item.insuredIdNumber}, #{item.planId}, #{item.riskId},#{item.termNum}
             ,#{item.proportion},#{item.enabledFlag},#{item.createTime},#{item.updateTime},#{item.amount},#{item.addCommissionAmount}
             ,#{item.uuid},#{item.status})
        </foreach>
    </insert>

    <delete id="deleteAddCommission">
        delete from sm_add_commission_detail
        where id in
        <foreach collection="list" open="(" close=")" separator="," item="item">
            #{item.id}
        </foreach>

    </delete>

    <select id="listAddCommission" resultType="com.cfpamf.ms.insur.operation.reward.addcommission.dto.SmAddCommissionListDto">
        select t3.productName,
               t1.order_id fhOrderId,
               t7.create_time,
               t2.account_time,
               t2.policy_no,
               t1.insured_id_number,
               t6.personName insuredPersonName,
               t8.regionName  AS recommendRegionName,
               t8.`organizationFullName` AS recommendOrganizationName,
               t7.recommendId,
               t8.`userName` recommendUserName,
               soa.idNumber,
               soa.personName applicantPersonName,
               t3.productType,
               t7.productId,
               t7.planId,
               tp.planName,
               if(t2.policy_status='4',round(t2.amount*-1,2),t2.amount) totalAmount,
               t2.policy_status appStatus,
               t1.proportion addCommissionProportion,
               ROUND(case
                         when (t2.policy_status = '4' and ifnull(t1.add_commission_amount, 0) > 0 and ifnull(t2.original_amount, 0) > 0) then -1*t2.original_amount*t1.proportion/100
                         when (t2.policy_status = '4' and ifnull(t1.add_commission_amount, 0) > 0 and ifnull(t2.original_amount, 0) = 0) then -1*t2.amount*t1.proportion/100
                         when (t2.policy_status = '1' and 0 > ifnull(t1.add_commission_amount, 0)) then -1*t1.add_commission_amount
                         else t1.add_commission_amount end
                   ,2) AS addCommissionAmount,
               t9.activity_code,t9.product_activity_code as villageActivity,t9.type,t7.orderType
        from sm_add_commission_detail_sum t1
        left join  sm_commission_detail t2 force index (idx_commission_detail_accounttime)
            ON t1.order_id = t2.order_id AND t1.insured_id_number = t2.insured_id_number AND t1.policy_no = t2.policy_no AND t1.plan_id = t2.plan_id and t1.term_num = t2.term_num
        LEFT JOIN sm_order t7
            ON t7.fhOrderId = t1.order_id
        LEFT JOIN auth_user t8
            ON t7.recommendId = t8.userId AND t8.enabled_flag = 0
        LEFT JOIN sm_product t3
            ON t7.productId = t3.id
        LEFT JOIN sm_order_insured t6
            on t6.fhOrderId = t2.order_id and t2.insured_id_number= t6.idNumber and t2.policy_status = t6.appStatus
        left join sm_order_applicant soa on soa.fhOrderId = t7.fhOrderId
        LEFT JOIN sm_plan tp ON t7.planId = tp.id
        left join sm_order_village_activity t9 on t7.fhOrderId=t9.fh_order_id and t9.type=100
        where t1.proportion > 0
        <if test="longInsurance!=null">
            and t3.long_insurance = #{longInsurance}
        </if>

        <if test='createDateStart != null'>
            <![CDATA[ AND t7.create_time>=#{createDateStart} ]]>
        </if>
        <if test='createDateEnd != null'>
            <![CDATA[ AND t7.create_time<=#{createDateEnd} ]]>
        </if>
        <if test='accountDateStart != null'>
            <![CDATA[ AND t2.account_time>=#{accountDateStart}  ]]>
        </if>
        <if test='accountDateEnd != null'>
            <![CDATA[ AND t2.account_time<=#{accountDateEnd}  ]]>
        </if>
        <if test='orderNo != null'>
            <if test="fullMatch">
                AND t1.order_id = #{orderNo}
            </if>
            <if test="!fullMatch">
                AND t1.order_id LIKE CONCAT(#{orderNo},'%')
            </if>
        </if>
        <if test='policyNo !=  null'>
            AND t1.policy_no like CONCAT(#{policyNo},'%')
        </if>
        <if test='productType != null'>
            and t3.productType = #{productType}
        </if>
        <if test='applicantName != null'>
            AND
            <if test="applicantType == 1">
                soa.personName LIKE CONCAT(#{applicantName},'%')
            </if>
            <if test="applicantType == 2">
                soa.idNumber LIKE CONCAT(#{applicantName},'%')
            </if>
            <if test="applicantType == 3">
                soa.cellPhone LIKE CONCAT(#{applicantName},'%')
            </if>
        </if>
        <if test='insuredName != null'>
            AND
            <if test="insuredType == 1">
                t6.personName LIKE CONCAT(#{insuredName},'%')
            </if>
            <if test="insuredType == 2">
                t6.idNumber LIKE CONCAT(#{insuredName},'%')
            </if>
            <if test="insuredType == 3">
                t6.cellPhone LIKE CONCAT(#{insuredName},'%')
            </if>
        </if>
        <if test='recommendId != null'>
            AND t7.recommendId=#{recommendId}
        </if>
        <if test='productAttrCode != null'>
            AND t3.productAttrCode=#{productAttrCode}
        </if>
        <if test='regionCode != null'>
            AND t8.regionCode=#{regionCode}
        </if>
        <if test='orgCode != null'>
            AND t8.orgCode=#{orgCode}
        </if>
        <if test='appStatus != null '>
            AND t2.policy_status = #{appStatus}
        </if>
        <if test='companyId != null'>
            AND t3.companyId=#{companyId}
        </if>
        <if test='productIds != null and productIds.size() > 0'>
            AND t7.productId in
            <foreach collection="productIds" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test='villageActivity != null'>
            <if test="villageActivity == 1">
                AND NULLIF(t9.product_activity_code,'') IS NULL
            </if>
            <if test="villageActivity == 0">
                AND NULLIF(t9.product_activity_code,'') IS NOT NULL
            </if>
        </if>
        ORDER BY t2.account_time DESC,t2.order_id DESC
    </select>
</mapper>
