<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.cfpamf.ms.insur.operation.claim.dao.ClaimTaskCallbackMapper">

    <!-- Result Map for ClaimTaskCallback -->
    <resultMap id="ClaimTaskCallbackResultMap" type="com.cfpamf.ms.insur.operation.claim.dto.ClaimTaskCallback">
        <id property="id" column="id"/>
        <result property="contractCode" column="contract_code"/>
        <result property="policyNo" column="policy_no"/>
        <result property="claimNo" column="claim_no"/>
        <result property="applicantName" column="applicant_name"/>
        <result property="applicantIdCard" column="applicant_id_card"/>
        <result property="applicantIdType" column="applicant_id_type"/>
        <result property="applicantMobile" column="applicant_mobile"/>
        <result property="applicantType" column="applicant_type"/>
        <result property="premiumTotal" column="premium_total" javaType="java.math.BigDecimal"/>
        <result property="mainProductName" column="main_product_name"/>
        <result property="finishTime" column="finish_time" javaType="java.util.Date"/>
        <result property="payMoney" column="pay_money" javaType="java.math.BigDecimal"/>
        <result property="claimExperienceSatisfactionScore" column="claim_experience_satisfaction_score"/>
        <result property="claimExperienceSatisfaction" column="claim_experience_satisfaction"/>
        <result property="unreasonableFeeFlag" column="unreasonable_fee_flag"/>
        <result property="giftInsuranceWillingnes" column="gift_insurance_willingnes"/>
        <result property="updateUser" column="update_user"/>
        <result property="createUser" column="create_user"/>
        <result property="updateTime" column="update_time" javaType="java.util.Date"/>
        <result property="revision" column="revision"/>
        <result property="createTime" column="create_time" javaType="java.util.Date"/>
    </resultMap>

    <!-- Insert ClaimTaskCallback -->
    <insert id="insert" parameterType="com.cfpamf.ms.insur.operation.claim.dto.ClaimTaskCallback">
        INSERT INTO claim_task_callback (
            contract_code, policy_no, claim_no, applicant_name, applicant_id_card, applicant_id_type,
            applicant_mobile, applicant_type, premium_total, main_product_name, finish_time,
            pay_money, claim_experience_satisfaction_score, claim_experience_satisfaction,
            unreasonable_fee_flag, gift_insurance_willingnes, update_user, create_user, update_time,
            revision, create_time
        ) VALUES (
            #{contractCode}, #{policyNo}, #{claimNo}, #{applicantName}, #{applicantIdCard}, #{applicantIdType},
            #{applicantMobile}, #{applicantType}, #{premiumTotal}, #{mainProductName}, #{finishTime},
            #{payMoney}, #{claimExperienceSatisfactionScore}, #{claimExperienceSatisfaction},
            #{unreasonableFeeFlag}, #{giftInsuranceWillingnes}, #{updateUser}, #{createUser}, #{updateTime},
            #{revision}, #{createTime}
        )
    </insert>


</mapper>