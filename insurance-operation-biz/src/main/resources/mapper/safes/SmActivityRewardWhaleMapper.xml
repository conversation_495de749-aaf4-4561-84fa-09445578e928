<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.insur.operation.activity.dao.WhaleActivityRewardMapper">
    <resultMap id="BaseResultMap" type="com.cfpamf.ms.insur.operation.activity.entity.SmActivityReward">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="data_id" jdbcType="VARCHAR" property="dataId"/>
        <result column="channel_type" jdbcType="VARCHAR" property="channelType"/>
        <result column="reward_type" jdbcType="VARCHAR" property="rewardType"/>
        <result column="proportion" jdbcType="DECIMAL" property="proportion"/>
        <result column="amount" jdbcType="DECIMAL" property="amount"/>
        <result column="sa_id" jdbcType="INTEGER" property="saId"/>
        <result column="system_activity_product_id" jdbcType="INTEGER" property="systemActivityProductId"/>
        <result column="enabled_flag" jdbcType="TINYINT" property="enabledFlag"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        , data_id, channel_type, reward_type, proportion, sa_id, system_activity_product_id,
    enabled_flag, create_time
    </sql>
    <insert id="insertListDuplicateUpdateProportion">
        INSERT INTO whale_activity_reward
        (data_id,channel_type,reward_type,uuid,proportion,sa_id,system_activity_product_id,amount,batch_no)
        VALUES
        <foreach collection="list" item="record" separator=",">
            (#{record.dataId},#{record.channelType},#{record.rewardType},#{record.uuid},#{record.proportion},#{record.saId},#{record.systemActivityProductId},#{record.amount},#{record.batchNo})
        </foreach>
        ON DUPLICATE KEY UPDATE
        proportion = values(proportion),
        amount = values(amount),
        sa_id = values(sa_id),
        batch_no = values(batch_no)
    </insert>

    <update id="batchUpdateActivityReward">
        update whale_activity_reward
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="proportion =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when id=#{item.id} then #{item.proportion}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
            #{item.id}
        </foreach>
    </update>
    <update id="batchSoftDelete">
        update whale_activity_reward
        set enabled_flag = 1
        where sa_id = #{saId}
          and reward_type = #{rewardType}
    </update>

    <delete id="batchDelete">
        delete
        from whale_activity_reward
        where sa_id = #{saId}
          and reward_type = #{rewardType}
    </delete>

    <update id="updateProportionBySaId">
        update whale_activity_reward
        set proportion = #{proportion}
        where sa_id = #{saId}
        and (batch_no is null or batch_no = '')
    </update>
</mapper>
