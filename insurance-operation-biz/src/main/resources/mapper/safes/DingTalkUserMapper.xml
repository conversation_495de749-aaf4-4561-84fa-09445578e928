<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cfpamf.ms.insur.operation.msg.dao.DingTalkUserMapper">
    <select id="selectByPostAndArea" resultType="com.cfpamf.ms.insur.operation.msg.pojo.dto.DingTalkUserDTO">

        select d.*, up.region_name, up.org_name, up.org_path,up.employee_status
        from ding_talk_user d
        left join user_post up
        on up.main_job_number = d.job_number
        and up.service_type = 0 and up.enabled_flag = 0
        and service_status = 0

        where d.batch_no = #{batchNo}
        and d.post_name = #{postName}
        <if test="orgs != null and orgs.size() > 0 ">
            and up.org_name in
            <foreach collection="orgs" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="regions != null and regions.size() > 0 ">
            and up.region_name in
            <foreach collection="regions" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>

    </select>
</mapper>
