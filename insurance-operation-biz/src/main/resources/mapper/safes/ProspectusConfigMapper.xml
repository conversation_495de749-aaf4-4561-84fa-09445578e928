<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cfpamf.ms.insur.operation.prospectus.dao.ProspectusConfigMapper">
    <!--    通过产品id查询 start-->
    <select id="getByProductId" resultType="com.cfpamf.ms.insur.operation.prospectus.entity.ProspectusConfig">
        select *
        from operation_prospectus_config
        where product_id = #{productId}
          and enabled_flag = 0
    </select>
    <!--    通过产品id查询 end-->


    <!--    通过id查询 start-->
    <select id="getById" resultType="com.cfpamf.ms.insur.operation.prospectus.entity.ProspectusConfig">
        select *
        from operation_prospectus_config
        where id = #{id}
          and enabled_flag = 0
    </select>
    <!--    通过id查询 end-->

    <!--    通过name查询 start-->
    <select id="getByName" resultType="com.cfpamf.ms.insur.operation.prospectus.entity.ProspectusConfig">
        select *
        from operation_prospectus_config
        where name = #{name}
          and enabled_flag = 0
    </select>
    <!--    通过name查询 end-->
    <!--   搜索 start-->
    <select id="search" resultType="com.cfpamf.ms.insur.operation.prospectus.vo.ProspectusConfigVo">
        select *
        from operation_prospectus_config
        where
        enabled_flag = 0
        <if test="enabled!=null and enabled!=''">
            and enabled = #{enabled}
        </if>
        <if test="productId!=null and productId!=''">
            and product_id = #{productId}
        </if>
        <if test="type!=null and type!=''">
            and type = #{type}
        </if>
        order by create_time DESC
    </select>
    <!--    搜索 end-->
    <select id="searchProspectusProduct"
            resultType="com.cfpamf.ms.insur.operation.prospectus.vo.ProspectusProductVo">
        SELECT sp.id, sp.productName, sp.productTags, sp.productFeature, sp.thumbnailImageUrl,
        sp.apiType, sp.h5Url, sp.channel
        from operation_prospectus_config opc
        LEFT JOIN sm_product sp
        on opc.product_id = sp.id and sp.enabled_flag = 0
        WHERE opc.enabled_flag = 0
        and sp.state = 1
        <if test='productIdList != null'>
            AND sp.id IN
            <foreach item="item" index="index" open="(" separator="," close=")" collection="productIdList">
                #{item}
            </foreach>
        </if>
        <if test='productName != null'>
            AND sp.productName like CONCAT('%',#{productName},'%')
        </if>
        order by opc.create_time DESC
    </select>


</mapper>