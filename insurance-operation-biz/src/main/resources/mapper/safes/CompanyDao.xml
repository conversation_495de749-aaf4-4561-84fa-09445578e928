<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cfpamf.ms.insur.operation.claim.dao.CompanyDao">
    <select id="searchByName" resultType="java.util.Map">

        SELECT t1.companyAbbre,t1.reportPhoneNo,t1.companyName
        FROM sm_company t1
        WHERE t1.enabled_flag=0
        <if test="companyName != null and companyName != ''">
            AND (t1.companyName LIKE CONCAT('%',#{companyName},'%') OR t1.companyAbbre LIKE CONCAT('%',#{companyName},'%'))
        </if>
        ORDER BY t1.create_time DESC
    </select>
</mapper>