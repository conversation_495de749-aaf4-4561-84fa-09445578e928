<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.insur.operation.activity.dao.SmActivitySuspendCensusMapper">
  <resultMap id="BaseResultMap" type="com.cfpamf.ms.insur.operation.activity.entity.SmActivitySuspendCensus">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="sa_id" jdbcType="INTEGER" property="saId" />
    <result column="suspend_operation_record_id" jdbcType="INTEGER" property="suspendOperationRecordId" />
    <result column="continue_operation_record_id" jdbcType="INTEGER" property="continueOperationRecordId" />
    <result column="start_time" jdbcType="TIMESTAMP" property="startTime" />
    <result column="end_time" jdbcType="TIMESTAMP" property="endTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, sa_id, suspend_operation_record_id, continue_operation_record_id, start_time, 
    end_time, create_time
  </sql>

</mapper>