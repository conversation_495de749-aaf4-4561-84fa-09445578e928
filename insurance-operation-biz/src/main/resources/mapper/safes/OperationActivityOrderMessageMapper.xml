<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.insur.operation.activity.dao.OperationActivityOrderMessageMapper">

    <select id="getByPayTimeAndActivityId"
            resultType="com.cfpamf.ms.insur.operation.activity.entity.OperationActivityOrderMessage">
        select *
        from operation_activity_order_message
        where
        <![CDATA[ payment_time <= #{endTime}
          and payment_time >= #{startTime}
        ]]>
        <if test="activityIdList!=null">
            and product_id in
            <foreach collection="activityIdList" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="getCountByOrderId"
            resultType="java.lang.Long">
        select count(1)
        from operation_activity_order_message
        where fh_order_id = #{fhOrderId}
    </select>
</mapper>