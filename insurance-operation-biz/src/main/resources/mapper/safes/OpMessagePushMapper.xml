<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cfpamf.ms.insur.operation.msg.dao.OpMessagePushMapper">

    <select id="selectPage" resultType="com.cfpamf.ms.insur.operation.msg.pojo.vo.OpMessagePushVO">
       select o.*,r.rule_name, r.rule_code
        FROM op_message_push o
        left join op_message_rule r on o.message_rule_id = r.id
        <where>
            <if test="receiverType != null and receiverType != ''">
                and receiver_type = #{receiverType}
            </if>

            <if test="ruleCode != null and ruleCode != ''">
                and r.rule_code = #{ruleCode}
            </if>
            <if test = "empName != null and empName != ''">
                and receiver_name = #{empName}
            </if>
            <if test = "startTime != null and startTime != ''">
                and o.create_time &gt;= #{startTime}
            </if>
            <if test = "endTime != null and endTime != ''">
                and o.create_time &lt;= #{endTime}
            </if>
            <if test="state != null">
                and o.state = #{state}
            </if>
        </where>
        order by o.id desc
    </select>
</mapper>
