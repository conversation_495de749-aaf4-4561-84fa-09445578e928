<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.insur.operation.activity.dao.SystemActivityProgrammeMapper">
    <insert id="insertCopyProgramme" useGeneratedKeys="true" keyProperty="id">
        <selectKey keyProperty="id" resultType="java.lang.Long" order="AFTER">
            SELECT LAST_INSERT_ID()
        </selectKey>
        INSERT INTO `system_activity_programme` (
        `title`,
        `type`,
        `conflictRule`,
        `activity_object`,
        `imageUrl`,
        `startTime`,
        `endTime`,
        `content`,
        `regions`,
        `products`,
        `els_activity_number`,
        `els_grant_password`,
        `create_by`,
        `update_by`,
        `open_count`,
        `background_image_url`,
        `config_type`,
        activity_platform,
        reward_mechanism
        )
        SELECT
        CONCAT(`title`, '（复制）'),
        `type`,
        `conflictRule`,
        `activity_object`,
        `imageUrl`,
        `startTime`,
        `endTime`,
        `content`,
        `regions`,
        `products`,
        `els_activity_number`,
        `els_grant_password`,
        #{createBy},
        #{createBy},
        `open_count`,
        `background_image_url`,
        `config_type`,
        activity_platform,
        reward_mechanism
        FROM `system_activity_programme`
        WHERE `id` = #{id} and `enabled_flag` = 0
    </insert>
    <update id="updateActivityFlag">
        update system_activity_programme
        set activeFlag = #{activityFlag}
        <if test="activityFlag == 1">
            , open_count = open_count + 1
        </if>
        where id = #{id}
    </update>

    <select id="getByIdList" resultType="com.cfpamf.ms.insur.operation.activity.entity.SystemActivityProgramme">
        select
        *
        from system_activity_programme
        where enabled_flag = 0
        and id in
        <foreach collection="idList" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="search" resultType="com.cfpamf.ms.insur.operation.activity.vo.SystemActivityProgrammeVo">
        select t1.id,
        t1.title,
        t1.type,
        t1.conflictRule,
        t1.config_type,
        t1.startTime,
        t1.endTime,
        t1.activity_object,
        t1.activeFlag,
        t1.regions,
        t1.imageUrl,
        t1.update_time,
        t1.background_image_url,
        t1.open_count,
        t1.els_activity_number,
        t1.els_grant_password,
        t2.userName as lastConfigUser,
        t1.activity_platform
        from system_activity_programme t1
        left join auth_user t2 on
        t2.enabled_flag = 0 and t2.userId = t1.update_by
        where t1.enabled_flag = 0
        <if test="activeFlag != null">
            and t1.activeFlag = #{activeFlag}
        </if>
        <if test="activityType != null and activityType!='' ">
            and t1.type = #{activityType}
        </if>
        <if test="startTime != null">
            <![CDATA[ AND t1.startTime >= #{startTime}]]>
        </if>
        <if test="endTime != null">
            <![CDATA[ AND t1.endTime <= #{endTime}]]>
        </if>
        <if test="activityName != null and activityName != ''">
            and t1.title like CONCAT(#{activityName},'%')
        </if>
        -- 未发布-UNPUBLISHED 未开始-NOT_START 活动中-IN_ACTIVITY 活动结束-END 活动暂停-SUSPEND 活动作废-CANCEL
        <if test="activeState != null and activeState != ''">
            <if test="activeState == 'UNPUBLISHED'">
                and t1.activeFlag = 0
            </if>

            <if test="activeState == 'NOT_START'">
                and t1.activeFlag = 1
                <![CDATA[ AND t1.startTime >= now()]]>
            </if>

            <if test="activeState == 'IN_ACTIVITY'">
                and t1.activeFlag = 1
                <![CDATA[ AND t1.startTime < now()]]>
                <![CDATA[ AND t1.endTime >= now()]]>
            </if>

            <if test="activeState == 'END'">
                and t1.activeFlag = 1
                <![CDATA[ AND t1.endTime < now()]]>
            </if>

            <if test="activeState == 'SUSPEND'">
                and t1.activeFlag = 2
            </if>

            <if test="activeState == 'CANCEL'">
                and t1.activeFlag = 3
            </if>
        </if>
        <if test="productName != null and productName != ''">
            and t1.id in (
            select sap.sa_id
            from system_activity_product sap
            left join sm_product sp
            on sap.product_id = sp.id
            where sp.productName like CONCAT('%',#{productName},'%') and sap.enabled_flag = 0
            )
        </if>
        Order By t1.create_time desc
    </select>
    <select id="getById" resultType="com.cfpamf.ms.insur.operation.activity.vo.SystemActivityProgrammeVo">
        select t1.id,
               t1.title,
               t1.type,
               t1.conflictRule,
               t1.startTime,
               t1.config_type,
               t1.endTime,
               t1.activity_object,
               t1.activeFlag,
               t1.regions,
               t1.update_time,
               t1.content,
               t1.products,
               t1.imageUrl,
               t1.open_count,
               t1.background_image_url,
               t1.els_activity_number,
               t1.els_grant_password,
               t1.activity_platform,
                t1.reward_mechanism
        from system_activity_programme t1
        where t1.id = #{id}
          and t1.enabled_flag = 0
    </select>

    <select id="getWxActivityList" resultType="com.cfpamf.ms.insur.operation.activity.vo.WxActivitySimpleVo">
        SELECT
        id as saId,
        imageUrl,
        title,
        startTime,
        `type`,
        background_image_url,
        endTime
        FROM system_activity_programme WHERE enabled_flag = 0 AND activeFlag = 1
        <![CDATA[ AND endTime >= CURRENT_TIMESTAMP() ]]>
        <if test="regionCode!=null">
            AND (JSON_CONTAINS( regions->"$[*]" ,'"${regionCode}"', "$") OR JSON_CONTAINS( regions->"$[*]" ,'"ALL"',
            "$"))
        </if>
        <if test="regionCode==null">
            AND JSON_CONTAINS( regions->"$[*]" ,'"ALL"', "$")
        </if>
        ORDER BY startTime DESC
    </select>
    <select id="searchByLazy" resultType="com.cfpamf.ms.insur.operation.activity.vo.SystemActivityProgrammeVo">
        select t1.*
        from system_activity_programme t1
        where t1.enabled_flag = 0
        <if test="activeFlag != null">
            and t1.activeFlag = #{activeFlag}
        </if>
        <if test="activityType != null and activityType!='' ">
            and t1.type = #{activityType}
        </if>
        <if test="configType != null and configType!='' ">
            and t1.config_type = #{configType}
        </if>
        <if test="activityPlatform != null and activityPlatform!='' ">
            and t1.activity_platform = #{activityPlatform}
        </if>
        <if test="activeState != null and activeState != ''">
            <if test="activeState == 'UNPUBLISHED'">
                and t1.activeFlag = 0
            </if>
            <if test="activeState == 'NOT_START'">
                and t1.activeFlag = 1
                <![CDATA[ AND t1.startTime >=  #{relativeTime}]]>
            </if>

            <if test="activeState == 'IN_ACTIVITY'">
                and t1.activeFlag = 1
                <![CDATA[ AND t1.startTime <  #{relativeTime}]]>
                <![CDATA[ AND t1.endTime >=  #{relativeTime}]]>
            </if>

            <if test="activeState == 'END'">
                and t1.activeFlag = 1
                <![CDATA[ AND t1.endTime <  #{relativeTime}]]>
            </if>

            <if test="activeState == 'SUSPEND'">
                and t1.activeFlag = 2
            </if>

            <if test="activeState == 'CANCEL'">
                and t1.activeFlag = 3
            </if>
        </if>
        Order By t1.id desc
    </select>
    <select id="getLastId" resultType="java.lang.Long">
        SELECT LAST_INSERT_ID();
    </select>

</mapper>
