<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.insur.admin.dao.safes.OperationPlanBookMakeInfoMapper">

<!--    <resultMap type="com.cfpamf.ms.insur.admin.dao.safes.entity.OperationPlanBookMakeInfo" id="OperationPlanBookMakeInfoMap">-->
<!--        <result property="id" column="id" jdbcType="INTEGER"/>-->
<!--        <result property="productId" column="product_id" jdbcType="INTEGER"/>-->
<!--        <result property="planId" column="plan_id" jdbcType="INTEGER"/>-->
<!--        <result property="insuredInfo" column="insured_info" jdbcType="VARCHAR"/>-->
<!--        <result property="applicantInfo" column="applicant_info" jdbcType="VARCHAR"/>-->
<!--        <result property="planBookInfo" column="plan_book_info" jdbcType="VARCHAR"/>-->
<!--        <result property="customerName" column="customer_name" jdbcType="VARCHAR"/>-->
<!--        <result property="customerSex" column="customer_sex" jdbcType="VARCHAR"/>-->
<!--        <result property="leaveNote" column="leave_note" jdbcType="VARCHAR"/>-->
<!--        <result property="snapshot" column="snapshot" jdbcType="VARCHAR"/>-->
<!--        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>-->
<!--        <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>-->
<!--        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>-->
<!--        <result property="createBy" column="create_by" jdbcType="VARCHAR"/>-->
<!--        <result property="enabledFlag" column="enabled_flag" jdbcType="INTEGER"/>-->
<!--    </resultMap>-->

<!--    &lt;!&ndash;查询单个&ndash;&gt;-->
<!--    <select id="queryById" resultMap="OperationPlanBookMakeInfoMap">-->
<!--        select-->
<!--          id, product_id, plan_id, insured_info, applicant_info, plan_book_info, customer_name, customer_sex, leave_note, snapshot, update_time, update_by, create_time, create_by, enabled_flag-->
<!--        from operation_plan_book_make_info-->
<!--        where id = #{id}-->
<!--    </select>-->

<!--    &lt;!&ndash;查询指定行数据&ndash;&gt;-->
<!--    <select id="queryAllByLimit" resultMap="OperationPlanBookMakeInfoMap">-->
<!--        select-->
<!--          id, product_id, plan_id, insured_info, applicant_info, plan_book_info, customer_name, customer_sex, leave_note, snapshot, update_time, update_by, create_time, create_by, enabled_flag-->
<!--        from operation_plan_book_make_info-->
<!--        <where>-->
<!--            <if test="id != null">-->
<!--                and id = #{id}-->
<!--            </if>-->
<!--            <if test="productId != null">-->
<!--                and product_id = #{productId}-->
<!--            </if>-->
<!--            <if test="planId != null">-->
<!--                and plan_id = #{planId}-->
<!--            </if>-->
<!--            <if test="insuredInfo != null and insuredInfo != ''">-->
<!--                and insured_info = #{insuredInfo}-->
<!--            </if>-->
<!--            <if test="applicantInfo != null and applicantInfo != ''">-->
<!--                and applicant_info = #{applicantInfo}-->
<!--            </if>-->
<!--            <if test="planBookInfo != null and planBookInfo != ''">-->
<!--                and plan_book_info = #{planBookInfo}-->
<!--            </if>-->
<!--            <if test="customerName != null and customerName != ''">-->
<!--                and customer_name = #{customerName}-->
<!--            </if>-->
<!--            <if test="customerSex != null and customerSex != ''">-->
<!--                and customer_sex = #{customerSex}-->
<!--            </if>-->
<!--            <if test="leaveNote != null and leaveNote != ''">-->
<!--                and leave_note = #{leaveNote}-->
<!--            </if>-->
<!--            <if test="snapshot != null and snapshot != ''">-->
<!--                and snapshot = #{snapshot}-->
<!--            </if>-->
<!--            <if test="updateTime != null">-->
<!--                and update_time = #{updateTime}-->
<!--            </if>-->
<!--            <if test="updateBy != null and updateBy != ''">-->
<!--                and update_by = #{updateBy}-->
<!--            </if>-->
<!--            <if test="createTime != null">-->
<!--                and create_time = #{createTime}-->
<!--            </if>-->
<!--            <if test="createBy != null and createBy != ''">-->
<!--                and create_by = #{createBy}-->
<!--            </if>-->
<!--            <if test="enabledFlag != null">-->
<!--                and enabled_flag = #{enabledFlag}-->
<!--            </if>-->
<!--        </where>-->
<!--        limit #{pageable.offset}, #{pageable.pageSize}-->
<!--    </select>-->

<!--    &lt;!&ndash;统计总行数&ndash;&gt;-->
<!--    <select id="count" resultType="java.lang.Long">-->
<!--        select count(1)-->
<!--        from operation_plan_book_make_info-->
<!--        <where>-->
<!--            <if test="id != null">-->
<!--                and id = #{id}-->
<!--            </if>-->
<!--            <if test="productId != null">-->
<!--                and product_id = #{productId}-->
<!--            </if>-->
<!--            <if test="planId != null">-->
<!--                and plan_id = #{planId}-->
<!--            </if>-->
<!--            <if test="insuredInfo != null and insuredInfo != ''">-->
<!--                and insured_info = #{insuredInfo}-->
<!--            </if>-->
<!--            <if test="applicantInfo != null and applicantInfo != ''">-->
<!--                and applicant_info = #{applicantInfo}-->
<!--            </if>-->
<!--            <if test="planBookInfo != null and planBookInfo != ''">-->
<!--                and plan_book_info = #{planBookInfo}-->
<!--            </if>-->
<!--            <if test="customerName != null and customerName != ''">-->
<!--                and customer_name = #{customerName}-->
<!--            </if>-->
<!--            <if test="customerSex != null and customerSex != ''">-->
<!--                and customer_sex = #{customerSex}-->
<!--            </if>-->
<!--            <if test="leaveNote != null and leaveNote != ''">-->
<!--                and leave_note = #{leaveNote}-->
<!--            </if>-->
<!--            <if test="snapshot != null and snapshot != ''">-->
<!--                and snapshot = #{snapshot}-->
<!--            </if>-->
<!--            <if test="updateTime != null">-->
<!--                and update_time = #{updateTime}-->
<!--            </if>-->
<!--            <if test="updateBy != null and updateBy != ''">-->
<!--                and update_by = #{updateBy}-->
<!--            </if>-->
<!--            <if test="createTime != null">-->
<!--                and create_time = #{createTime}-->
<!--            </if>-->
<!--            <if test="createBy != null and createBy != ''">-->
<!--                and create_by = #{createBy}-->
<!--            </if>-->
<!--            <if test="enabledFlag != null">-->
<!--                and enabled_flag = #{enabledFlag}-->
<!--            </if>-->
<!--        </where>-->
<!--    </select>-->

<!--    &lt;!&ndash;新增所有列&ndash;&gt;-->
<!--    <insert id="insert" keyProperty="id" useGeneratedKeys="true">-->
<!--        insert into operation_plan_book_make_info(product_id, plan_id, insured_info, applicant_info, plan_book_info, customer_name, customer_sex, leave_note, snapshot, update_time, update_by, create_time, create_by, enabled_flag)-->
<!--        values (#{productId}, #{planId}, #{insuredInfo}, #{applicantInfo}, #{planBookInfo}, #{customerName}, #{customerSex}, #{leaveNote}, #{snapshot}, #{updateTime}, #{updateBy}, #{createTime}, #{createBy}, #{enabledFlag})-->
<!--    </insert>-->

<!--    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">-->
<!--        insert into operation_plan_book_make_info(product_id, plan_id, insured_info, applicant_info, plan_book_info, customer_name, customer_sex, leave_note, snapshot, update_time, update_by, create_time, create_by, enabled_flag)-->
<!--        values-->
<!--        <foreach collection="entities" item="entity" separator=",">-->
<!--        (#{entity.productId}, #{entity.planId}, #{entity.insuredInfo}, #{entity.applicantInfo}, #{entity.planBookInfo}, #{entity.customerName}, #{entity.customerSex}, #{entity.leaveNote}, #{entity.snapshot}, #{entity.updateTime}, #{entity.updateBy}, #{entity.createTime}, #{entity.createBy}, #{entity.enabledFlag})-->
<!--        </foreach>-->
<!--    </insert>-->

<!--    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">-->
<!--        insert into operation_plan_book_make_info(product_id, plan_id, insured_info, applicant_info, plan_book_info, customer_name, customer_sex, leave_note, snapshot, update_time, update_by, create_time, create_by, enabled_flag)-->
<!--        values-->
<!--        <foreach collection="entities" item="entity" separator=",">-->
<!--            (#{entity.productId}, #{entity.planId}, #{entity.insuredInfo}, #{entity.applicantInfo}, #{entity.planBookInfo}, #{entity.customerName}, #{entity.customerSex}, #{entity.leaveNote}, #{entity.snapshot}, #{entity.updateTime}, #{entity.updateBy}, #{entity.createTime}, #{entity.createBy}, #{entity.enabledFlag})-->
<!--        </foreach>-->
<!--        on duplicate key update-->
<!--        product_id = values(product_id),-->
<!--        plan_id = values(plan_id),-->
<!--        insured_info = values(insured_info),-->
<!--        applicant_info = values(applicant_info),-->
<!--        plan_book_info = values(plan_book_info),-->
<!--        customer_name = values(customer_name),-->
<!--        customer_sex = values(customer_sex),-->
<!--        leave_note = values(leave_note),-->
<!--        snapshot = values(snapshot),-->
<!--        update_time = values(update_time),-->
<!--        update_by = values(update_by),-->
<!--        create_time = values(create_time),-->
<!--        create_by = values(create_by),-->
<!--        enabled_flag = values(enabled_flag)-->
<!--    </insert>-->

<!--    &lt;!&ndash;通过主键修改数据&ndash;&gt;-->
<!--    <update id="update">-->
<!--        update operation_plan_book_make_info-->
<!--        <set>-->
<!--            <if test="productId != null">-->
<!--                product_id = #{productId},-->
<!--            </if>-->
<!--            <if test="planId != null">-->
<!--                plan_id = #{planId},-->
<!--            </if>-->
<!--            <if test="insuredInfo != null and insuredInfo != ''">-->
<!--                insured_info = #{insuredInfo},-->
<!--            </if>-->
<!--            <if test="applicantInfo != null and applicantInfo != ''">-->
<!--                applicant_info = #{applicantInfo},-->
<!--            </if>-->
<!--            <if test="planBookInfo != null and planBookInfo != ''">-->
<!--                plan_book_info = #{planBookInfo},-->
<!--            </if>-->
<!--            <if test="customerName != null and customerName != ''">-->
<!--                customer_name = #{customerName},-->
<!--            </if>-->
<!--            <if test="customerSex != null and customerSex != ''">-->
<!--                customer_sex = #{customerSex},-->
<!--            </if>-->
<!--            <if test="leaveNote != null and leaveNote != ''">-->
<!--                leave_note = #{leaveNote},-->
<!--            </if>-->
<!--            <if test="snapshot != null and snapshot != ''">-->
<!--                snapshot = #{snapshot},-->
<!--            </if>-->
<!--            <if test="updateTime != null">-->
<!--                update_time = #{updateTime},-->
<!--            </if>-->
<!--            <if test="updateBy != null and updateBy != ''">-->
<!--                update_by = #{updateBy},-->
<!--            </if>-->
<!--            <if test="createTime != null">-->
<!--                create_time = #{createTime},-->
<!--            </if>-->
<!--            <if test="createBy != null and createBy != ''">-->
<!--                create_by = #{createBy},-->
<!--            </if>-->
<!--            <if test="enabledFlag != null">-->
<!--                enabled_flag = #{enabledFlag},-->
<!--            </if>-->
<!--        </set>-->
<!--        where id = #{id}-->
<!--    </update>-->

<!--    &lt;!&ndash;通过主键删除&ndash;&gt;-->
<!--    <delete id="deleteById">-->
<!--        delete from operation_plan_book_make_info where id = #{id}-->
<!--    </delete>-->

</mapper>

