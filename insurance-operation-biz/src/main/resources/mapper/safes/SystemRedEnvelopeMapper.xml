<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.insur.operation.reward.redenvelope.dao.SystemRedEnvelopeMapper">

    <update id="updateAmountAndStateByIdAndState">
        update system_red_envelope
        set amount     = #{amount},
            send_state = '2',
            send_time  = now()
        where id = #{id}
          and send_state = #{state};

    </update>

    <select id="getByDateTypeAndDataIdList"
            resultType="com.cfpamf.ms.insur.operation.reward.redenvelope.po.entity.SystemRedEnvelope">
        select * from
        system_red_envelope
        where sa_id = #{saId}
        and enabled_flag = 0
        <if test="dataIdList!= null and dataIdList.size() > 0">
            and data_id in
            <foreach collection="dataIdList" item="dataId" open="(" close=")" index="index" separator=",">
                #{dataId}
            </foreach>
        </if>
    </select>

    <select id="getOrderBySaIdAndUserId" resultType="com.cfpamf.ms.insur.operation.reward.redenvelope.po.vo.RedEnvelopeOrderItemVO">
        select
               t1.totalAmount,
               t2.id_number as idNumber,
               t2.th_policy_no as thPolicyNo,
               t.create_time as createTime,
               t1.fhorderid,
               case when INSTR (t1.fhorderid ,'_') =0 then 0 else SUBSTRING_INDEX(t1.fhorderid,'_',-1) end orderIdIndex
        from system_red_envelope t
        left join sm_order t1 on t1.fhorderid = t.data_id
        left join sm_order_item t2 on t2.fh_order_id = t.data_id
        where sa_id = #{saId} and user_id = #{userId}
    </select>

    <select id="getCancelOrderItem" resultType="com.cfpamf.ms.insur.operation.reward.redenvelope.po.vo.RedEnvelopeOrderItemVO">
        select t.id_number as idNumber,
               t.create_time as createTime,
               t.th_policy_no as thPolicyNo,
               t.fh_order_id as fhOrderId,
               case when INSTR (t.fh_order_id ,'_') =0 then 0 else SUBSTRING_INDEX(t.fh_order_id,'_',-1) end orderIdIndex
        from sm_order_item t
        left join sm_commission_detail t1 on t1.order_id = t.fh_order_id
        left join sm_order t2 on t2.fhorderid = t.fh_order_id
        where app_status = 4
        <![CDATA[
          and t1.account_time >= '2023-03-01 00:00:00' and t1.account_time <= '2023-04-30 23:59:59'
        ]]>
    </select>

    <select id="selectConfigs" resultType="com.cfpamf.ms.insur.operation.pco.entity.Dictionary">
        select code,name
        from dictionary
        where type = #{type} and code = #{code}
        limit 1
    </select>
</mapper>