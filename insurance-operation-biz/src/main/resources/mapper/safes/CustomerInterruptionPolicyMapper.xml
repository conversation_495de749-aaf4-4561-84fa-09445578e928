<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.insur.operation.customer.dao.CustomerInterruptionPolicyMapper">
    <select id="getQtyByRenewEmp" resultType="com.cfpamf.ms.insur.operation.customer.dto.WxCmsSmyDto">
        select
            sum(case when t.insur = 1 and t.policy_state !=4 and t.total_Amount>0 then 1
                     when t.insur = 0 and t.policy_state !=4 and t.cancel_flag is null then 1
                     else 0 end) as orderQty,
            sum(total_amount) as orderAmount
        from customer_interruption_policy t
        where customer_admin = #{userId}
        and t.enabled_flag = 0
    </select>

    <insert id="insertListDuplicateUpdate">
        INSERT INTO customer_interruption_policy
        (id_number,id_type,customer_name,policy_no,fh_order_id,policy_state
        ,total_Amount,amount,insur,cancel_Flag,renew_time,cancel_time,customer_admin,recommend,renew_state,renew_emp)
        VALUES
        <foreach collection="list" item="record" separator=",">
            (#{record.idNumber},#{record.idType},#{record.customerName},#{record.policyNo}
            ,#{record.fhOrderId},#{record.policyState},#{record.totalAmount},#{record.amount},#{record.insur}
            ,#{record.cancelFlag},#{record.renewTime},#{record.cancelTime},#{record.customerAdmin},#{record.recommend},#{record.renewState},#{record.renewEmp})
        </foreach>
        ON DUPLICATE KEY UPDATE cancel_Flag=values(cancel_Flag),renew_time=values(renew_time),cancel_time=values(cancel_time),amount=values(amount),
        policy_no=VALUES(policy_no),total_Amount=VALUES(total_Amount)
    </insert>

</mapper>