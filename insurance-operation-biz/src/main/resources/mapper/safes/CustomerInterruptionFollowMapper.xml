<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.insur.operation.customer.dao.CustomerInterruptionFollowMapper">
    <select id="selectByCustomer" resultType="com.cfpamf.ms.insur.operation.customer.dto.CustomerInterruptionFollowDto">
        select
        t1.*,case when t1.follow_people = 'system' then '系统' else t2.userName end followPeopleName
        from customer_interruption_follow t1
        left join auth_user t2 on t2.userId = t1.follow_people and t2.enabled_flag = 0
        where customer_id = #{customerId}
        and t1.enabled_flag = 0
        order by follow_time desc,id desc
    </select>

    <select id="selectByCustomerIncludeAI" resultType="com.cfpamf.ms.insur.operation.customer.dto.CustomerInterruptionFollowDto">
        select t1.id,customer_id,follow_time,
               case when t1.follow_people = 'system' then '系统' else t2.userName end followPeopleName,
               follow_people,service_mode,intention,'' intent,reason,reason_remark,remark,next_time,
               '' voiceAddress,'' recordJson,0 talking_time_len,'INTERRUPTION' taskType
        from customer_interruption_follow t1
        left join auth_user t2 on t2.userId = t1.follow_people and t2.enabled_flag = 0
        where customer_id = #{customerId} and t1.enabled_flag = 0
        union all
        select id,target_id customerId,
               create_time as follow_time,
               '机器人' followPeopleName,'机器人' followPeople,'phone' service_mode,
               '' intention,intent,'' reason,'' reasonRemark,'' remark,create_time nextTime,
               voice_address,record_json,talking_time_len,task_type
        from ai_follow_record where target_id = #{customerId} and task_type ='INTERRUPTION'
        union all
        select t1.id,customer_id,follow_time,
               case when t1.follow_people = 'system' then '系统' else t2.userName end followPeopleName,
               follow_people,service_mode,intention,'' intent,reason,reason_remark,remark,next_time,
               '' voiceAddress,'' recordJson,0 talking_time_len,'LOAN' taskType
        from customer_loan_follow t1
                 left join auth_user t2 on t2.userId = t1.follow_people and t2.enabled_flag = 0
        where customer_id = #{customerId} and t1.enabled_flag = 0
        order by follow_time desc,id desc
    </select>

    <update id="updateNewest">
        update customer_interruption_follow set newest = 0
        where customer_id = #{customerId} and newest = 1
    </update>

    <insert id="insertFollow">
        update customer_interruption_follow set newest = 0
        where  customer_id in
        <foreach collection="list" open="(" close=")" separator="," item="item">
            #{item.customerId}
        </foreach>
        and newest = 1 ;
        insert into customer_interruption_follow(customer_id, id_number,intention, follow_time, follow_people)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.customerId}, #{item.idNumber} , 'renewed', #{item.renewTime}, 'system')
        </foreach>
    </insert>

    <select id="queryInterruptionFollowListRetrospective" resultType="com.cfpamf.ms.insur.operation.customer.dto.CustomerInterruptionFollowDto">
        select t1.job_number,t.id,t.customer_id customerId,t3.customer_name customerName,t.follow_time,
        case when t.follow_people = 'system' then '系统' else '' end followPeopleName,
        t.follow_people,t.service_mode,t.intention,'' intent,t.reason,t.reason_remark,t.remark,t.next_time,
        '' voiceAddress,'' recordJson,0 talking_time_len,'INTERRUPTION' taskType,'' todoCustomerIdNumber
        from customer_interruption_follow t
        left join phoenix_emp_todo t1 on t.id_number = t1.target_id and t1.biz_type = 'INTERRUPTION'
        left join customer_interruption t3 on t.id_number = t3.id_number
        where t1.`job_number` = #{empCode} and t.newest= 1 and <![CDATA[t.follow_time >= #{startDate} and t.follow_time < DATE_ADD(#{startDate}, INTERVAL 1 DAY)]]>
        union all
        select t1.job_number,t.id,'' customer_id,'' customer_name,t.follow_time,
        case when t.follow_people = 'system' then '系统' else '' end followPeopleName,
        t.follow_people,t.service_mode,t.intention,'' intent,t.reason,t.reason_remark,t.remark,t.next_time,
        '' voiceAddress,'' recordJson,0 talking_time_len,'RENEW_SHORT' taskType ,
        JSON_UNQUOTE(JSON_EXTRACT(t1.todo_property, '$.idNumber')) as todoCustomerIdNumber
        from sm_order_renewal_follow t
        left join phoenix_emp_todo t1 on t.policy_no = t1.target_id and t1.biz_type = 'RENEW_SHORT'
        where t1.job_number = #{empCode} and t.newest= 1 and <![CDATA[t.follow_time >= #{startDate} and t.follow_time < DATE_ADD(#{startDate}, INTERVAL 1 DAY)]]>
        order by follow_time desc,id desc
    </select>
    <select id="queryRenewalTermFollowListRetrospective" resultType="com.cfpamf.ms.insur.operation.customer.dto.CustomerInterruptionFollowDto">
        select t1.job_number,t.id,t.create_time as followTime,
        case when t.create_by = 'system' then '系统' else create_by end followPeopleName,
        t.create_by as followPeople,t.contact_way as service_mode,t.result as intention,'' intent,t.cause as reason,t.remark as reason_remark,t.remark,null next_time,
        '' voiceAddress,'' recordJson,0 talking_time_len,'RENEW_LONG' taskType ,
        JSON_UNQUOTE(JSON_EXTRACT(t1.todo_property, '$.idNumber')) as todoCustomerIdNumber
        from sm_order_renewal_term_notify_log t
        left join phoenix_emp_todo t1 on concat(t.policy_no, '-', t.term_num)  = t1.target_id and t1.biz_type = 'RENEW_LONG'
        where t1.job_number = #{empCode} and t.newest= 1 and <![CDATA[t.create_time >= #{startDate} and t.create_time < DATE_ADD(#{startDate}, INTERVAL 1 DAY)]]>
    </select>
</mapper>