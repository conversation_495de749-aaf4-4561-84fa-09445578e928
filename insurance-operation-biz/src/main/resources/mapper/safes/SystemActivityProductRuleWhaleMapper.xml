<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.insur.operation.activity.dao.SystemActivityProductRuleWhaleMapper">
    <insert id="insertCopyProductRule">
        INSERT INTO `system_activity_product_rule_whale` (
            `system_activity_product_id`,
            `rule_code`,
            `compare_symbol`,
            `params`,
            `enabled_flag`,
            `create_by`,
            `update_by`
        )
        SELECT
            #{systemActivityProductId},
            r.`rule_code`,
            r.`compare_symbol`,
            r.`params`,
            r.`enabled_flag`,
            #{currentUserId},
            #{currentUserId}
        FROM
            `system_activity_product_rule` r
        WHERE r.enabled_flag = 0 and r.system_activity_product_id=#{oldsystemActivityProductId}
    </insert>

    <update id="softDeleteBySystemActivityProductIdList">
        update system_activity_product_rule_whale
        set  enabled_flag = 1
        where enabled_flag = 0 and system_activity_product_id in
        <foreach collection="systemActivityProductIdList" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>
    <select id="getBySystemActivityProductIdList"
            resultType="com.cfpamf.ms.insur.operation.activity.vo.SystemActivityProductRuleVo">
        select id,
        system_activity_product_id,
        rule_code,
        compare_symbol,
        params from system_activity_product_rule_whale
        where enabled_flag = 0 and system_activity_product_id in
        <foreach collection="systemActivityProductIdList" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
</mapper>