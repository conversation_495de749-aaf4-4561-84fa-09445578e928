<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.insur.operation.activity.dao.TempProductRepurchaseActivityMapper">

    <insert id="initDateTimeData">

        SET
        @r := 1,
@n := 0,
@s := NULL;


        insert into temp_product_repurchase_activity
        (user_name, organization_name, region_name, recommend_id, customer_count, lottery_count, rank,
         start_time, end_time)
        SELECT userName,
               organizationName,
               regionName,
               recommendId,
               customerCount,
               lotteryCount,
               rank,
               #{startTime},
               #{endTime}


        FROM (
                 SELECT temp.*,
                        @n :=
	CASE

			WHEN @s = customerCount THEN
			@n ELSE @r
				END AS rank,
		@s := customerCount,
		@r := @r + 1
                 FROM
                     (
                     SELECT
                     t4.userName AS 'userName',
                     t4.organizationName AS 'organizationName',
                     t4.regionName AS 'regionName',
                     t1.recommendId AS 'recommendId',
                     t1.n AS 'customerCount',
                     t1.n AS 'lotteryCount'
                     FROM
                     (
                     SELECT
                     t.recommendId,
                     COUNT(DISTINCT( t.idNumber)) AS n
                     FROM
                     (
                     SELECT
                     t1.recommendId,
                     t3.idNumber
                     FROM
                     sm_order t1
                     LEFT JOIN sm_product t2 ON t2.id = t1.productId
                     LEFT JOIN sm_order_insured t3 ON t3.fhOrderId = t1.fhOrderId
                     WHERE
                     t1.payStatus = 2
                     AND t1.paymentTime >= #{startTime}
                     AND  <![CDATA[ t1.paymentTime <= #{endTime} ]]>
                     AND TIMESTAMPDIFF( DAY, t1.startTime, t1.endTime )>= 364
                     AND t2.productAttrCode = 'person'
                     AND t3.idNumber IN (
                     SELECT DISTINCT
                     ( t3.idNumber )
                     FROM
                     sm_order t1
                     LEFT JOIN sm_order_insured t3 ON t3.fhOrderId = t1.fhOrderId
                     WHERE
                     t1.payStatus = 2
                     AND <![CDATA[   t1.paymentTime < #{startTime} ]]>
                        AND t3.appStatus = '1'
                     ) UNION ALL
                     SELECT
                     t1.recommendId,
                     t3.idNumber
                     FROM
                     sm_order t1
                     LEFT JOIN sm_product t2 ON t2.id = t1.productId
                     LEFT JOIN sm_order_applicant t3 ON t3.fhOrderId = t1.fhOrderId
                     WHERE
                     t1.payStatus = 2
                     AND t1.paymentTime >= #{startTime}
                     AND  <![CDATA[  t1.paymentTime <= #{endTime} ]]>
AND TIMESTAMPDIFF( DAY, t1.startTime, t1.endTime )>= 364
                     AND t2.productAttrCode = 'group'
                     AND t3.idNumber IN (
                     SELECT DISTINCT
                     ( t3.idNumber )
                     FROM
                     sm_order t1
                     LEFT JOIN sm_order_insured t3 ON t3.fhOrderId = t1.fhOrderId
                     WHERE
                     t1.payStatus = 2
                     AND <![CDATA[   t1.paymentTime < #{startTime} ]]>
AND t3.appStatus = '1'
                     )) t
                     GROUP BY
                     t.recommendId
                     ) t1
                     LEFT JOIN auth_user t4 ON t4.userId = t1.recommendId and t4.enabled_flag = 0
                     WHERE t4.userId is not null
                     ORDER BY
                     t1.n DESC) temp
                 ORDER BY
                     customerCount DESC
             ) final_table
    </insert>

    <select id="getEndTimeQuery" resultType="java.time.LocalDateTime">
        SELECT end_time
        FROM `temp_product_repurchase_activity`
        where start_time = #{startTime}
        GROUP BY end_time
        ORDER BY end_time DESC
    </select>
    <select id="get"
            resultType="com.cfpamf.ms.insur.operation.activity.temp.entity.TempProductRepurchaseActivity">
        SELECT *
        FROM `temp_product_repurchase_activity`
        where start_time = #{startTime}
          and end_time = #{endTime}
    </select>
</mapper>