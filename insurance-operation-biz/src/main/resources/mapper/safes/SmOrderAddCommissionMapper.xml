<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.insur.operation.activity.dao.SmOrderAddCommissionMapper">
    <resultMap id="BaseResultMap" type="com.cfpamf.ms.insur.operation.activity.entity.SmOrderAddCommission">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="fh_order_id" jdbcType="VARCHAR" property="fhOrderId"/>
        <result column="commission_type" jdbcType="VARCHAR" property="commissionType"/>
        <result column="commission_proportion" jdbcType="DECIMAL" property="commissionProportion"/>
        <result column="data_id" jdbcType="VARCHAR" property="dataId"/>
        <result column="enabled_flag" jdbcType="TINYINT" property="enabledFlag"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        , fh_order_id, commission_type, commission_proportion, data_id, enabled_flag, create_time
    </sql>

    <select id="getByDataIdAndType"
            resultType="com.cfpamf.ms.insur.operation.activity.entity.SmOrderAddCommission">
        select *
        from sm_order_add_commission
        where data_id = #{dataId}
          and commission_type = #{type}
          and enabled_flag = 0
    </select>
    <update id="batchUpdate">
        update sm_order_add_commission
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="commission_proportion =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when id=#{item.id} then #{item.commissionProportion}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
            #{item.id}
        </foreach>
    </update>

    <update id="updateOrderAddCommissionProportion">
        UPDATE sm_order t1
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="add_commission_proportion =case" suffix="end,">
                <foreach collection="orderAddCommissionProportionList" item="item" index="index">
                    when fhOrderId=#{item.fhOrderId} then #{item.addCommissionProportion}
                </foreach>
            </trim>
        </trim>
        WHERE
        fhOrderId IN
        <foreach collection="orderAddCommissionProportionList" index="index" item="item" separator="," open="(" close=")">
            #{item.fhOrderId}
        </foreach>
    </update>
    <update id="softDeleteByDataIdAndType">
        update
            sm_order_add_commission
        set enabled_flag = 1
        where data_id = #{dataId}
          and commission_type = #{type}
          and enabled_flag = 0
    </update>
    <delete id="deleteByDataIdAndType">
        delete from
            sm_order_add_commission
        where data_id = #{dataId}
          and commission_type = #{type}
          and enabled_flag = 0
    </delete>
</mapper>