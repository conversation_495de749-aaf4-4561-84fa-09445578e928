<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.insur.operation.activity.dao.SystemActivityProductMapper">
    <resultMap id="BaseResultMap" type="com.cfpamf.ms.insur.operation.activity.entity.SystemActivityProduct">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="sa_id" jdbcType="INTEGER" property="saId"/>
        <result column="product_id" jdbcType="INTEGER" property="productId"/>
        <result column="plan_id" jdbcType="OTHER" property="planId"/>
        <result column="quantity" jdbcType="INTEGER" property="quantity"/>
        <result column="enabled_flag" jdbcType="TINYINT" property="enabledFlag"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="create_by" jdbcType="VARCHAR" property="createBy"/>
        <result column="update_by" jdbcType="VARCHAR" property="updateBy"/>
    </resultMap>
    <select id="selectCopyProduct"
            resultType="com.cfpamf.ms.insur.operation.activity.entity.SystemActivityProduct">
        SELECT
            id,
            `sa_id`,
            `product_id`,
            `plan_id`,
            `quantity`,
            `reward_type`,
            `enabled_flag`,
            `trigger_type`
        FROM `system_activity_product`
        WHERE `sa_id` = #{id} AND `enabled_flag` = 0
    </select>

    <update id="softDeleteBySystemActivityId">
        update system_activity_product
        set enabled_flag = 1
        where sa_id = #{systemActivityId}
          and enabled_flag = 0
    </update>
    <select id="searchByProductName"
            resultType="com.cfpamf.ms.insur.operation.activity.entity.SystemActivityProduct">
        select t1.*
        from system_activity_product t1
                 left join sm_product t2
                           on t1.product_id = t2.productId
        where t2.productName like CONCAT(#{productName}, '%')
          and t1.enabled_flag = 0
    </select>
    <select id="getBySystemActivityIdList"
            resultType="com.cfpamf.ms.insur.operation.activity.vo.SystemActivityProductVo">
        select t1.id ,
        t1.product_id,
        t1.sa_id,
        t1.plan_id,
        t1.quantity,
        t1.trigger_type,
        t1.reward_type,
        t1.sa_id as systemActivityProgrammeId,
        t2.productName
        from system_activity_product t1
        left join sm_product t2
        on t1.product_id = t2.id
        where t1.enabled_flag = 0 and t1.sa_id in
        <foreach collection="systemActivityIdList" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
    <select id="getBySystemActivityId"
            resultType="com.cfpamf.ms.insur.operation.activity.entity.SystemActivityProduct">
        select *
        from system_activity_product
        where sa_id = #{systemActivityId}
          and enabled_flag = 0
    </select>
    <select id="getByListenerProductIdAndPayTime"
            resultType="com.cfpamf.ms.insur.operation.activity.vo.SystemActivityProductDetailVo">
        select t1.*,
               t2.regions,
               t2.els_activity_number,
               t2.els_grant_password,
               t2.conflictRule,
               t2.type
        from system_activity_product t1
                 left join system_activity_programme t2 on t1.sa_id = t2.id
        where (t1.product_id = #{productId} or t1.product_id = -1 )
          and t2.activeFlag = 1
          and t1.enabled_flag = 0
          and
           <![CDATA[   t2.startTime <= #{payTime}
          and t2.endTime >= #{payTime}
        ]]>
            and t1.trigger_type = 'LISTENER'
    </select>

    <select id="getById"
            resultType="com.cfpamf.ms.insur.operation.activity.vo.SystemActivityProductDetailVo">
        select t1.*,
               t2.regions,
               t2.els_activity_number,
               t2.els_grant_password,
               t2.conflictRule,
               t2.type
        from system_activity_product t1
                 left join system_activity_programme t2 on t1.sa_id = t2.id
        where t2.id = #{id} and t1.enabled_flag = 0

    </select>
    <select id="searchProductActivity" resultType="java.lang.Integer">
        select count(1)
        from system_activity_programme t1
                 inner join system_activity_const_rule sa on sa.sa_id = t1.id
        where ((t1.startTime between #{startTime} and #{endTime})
            or (t1.endtime between #{startTime} and #{endTime}))
            and sa.rule_type = #{ruleType}
          <if test="productIds != null and productIds.size()>0">
          and (JSON_OVERLAPS(sa.product_ids, #{productIdJson}) or sa.product_ids = cast('[]' as json));
          </if>
    </select>
    <select id="getLastId" resultType="java.lang.Long">
        SELECT LAST_INSERT_ID();
    </select>

</mapper>
