<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.insur.operation.customer.dao.CustomerLoanFirstFollowMapper">

    <update id="updateNewest">
        update customer_loan_first_follow set newest = 0
        where loan_cust_id = #{loanCustId} and newest = 1
    </update>

    <insert id="insertCustFollow">
        <foreach collection="list" item="item" index="index" open="" close="" separator=";">
            update customer_loan_first_follow set newest = 0,id_number = #{item.idNumber},customer_id=#{item.customerId}
            where loan_cust_id = #{item.loanCustId};
            insert into customer_loan_first_follow(loan_cust_id,customer_id, id_number,follow_state,intention,reason,remark, follow_time, follow_people)
            VALUES (#{item.loanCustId}, #{item.customerId},#{item.idNumber} , 0, 'cust', '','已转化', CURTIME(), 'system')
        </foreach>
    </insert>

    <select id="selectLoanFirstFollowByLoan" resultType="com.cfpamf.ms.insur.operation.customer.dto.CustomerFollowDto">
        select t1.id,customer_id,follow_time,
        case when t1.follow_people = 'system' then '系统' else t2.userName end followPeopleName,
        follow_people,reason,reason_remark,remark,'LOAN_FIRST' followType,intention,follow_state
        from customer_loan_first_follow t1
        left join auth_user t2 on t2.userId = t1.follow_people and t2.enabled_flag = 0
        where loan_cust_id = #{loanCustId} and t1.enabled_flag = 0
    </select>
</mapper>