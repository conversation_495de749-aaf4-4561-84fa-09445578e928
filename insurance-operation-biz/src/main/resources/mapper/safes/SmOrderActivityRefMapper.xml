<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.insur.operation.activity.dao.SmOrderActivityRefMapper">
    <resultMap id="BaseResultMap" type="com.cfpamf.ms.insur.operation.activity.entity.SmOrderActivityRef">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="sa_id" jdbcType="INTEGER" property="saId"/>
        <result column="fh_order_id" jdbcType="VARCHAR" property="fhOrderId"/>
        <result column="enabled_flag" jdbcType="TINYINT" property="enabledFlag"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="quantity" jdbcType="TINYINT" property="quantity"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        , sa_id, fh_order_id,quantity, enabled_flag, create_time, update_time
    </sql>

</mapper>