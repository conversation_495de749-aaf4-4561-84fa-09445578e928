<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.insur.operation.addCommission.dao.WhaleAddCommissionDetailItemBackUpsMapper">

  <insert id="insertBackUps">
    INSERT INTO whale_add_commission_detail_item_backups
    (
      endorsement_no,
      policy_no,
      sell_product_code,
      risk_code,
      product_status,
      term_num,
      commission_type,
      proportion,
      amount,
      data_id,
      uuid,
      enabled_flag,
      settlement_state,
      batch_no,
      add_commission_amount,
      data_index,
      insured_code,
      activity_name,
      operation_remark
    )
    select
      endorsement_no,
      policy_no,
      sell_product_code,
      risk_code,
      product_status,
      term_num,
      commission_type,
      proportion,
      amount,
      data_id,
      uuid,
      enabled_flag,
      settlement_state,
      batch_no,
      add_commission_amount,
      data_index,
      insured_code,
      activity_name,
      #{operationRemarks}
    from whale_add_commission_detail_item
    where id in
    <foreach collection="list" item="item" open="(" separator="," close=")">
      #{item.id}
    </foreach>
  </insert>

</mapper>
