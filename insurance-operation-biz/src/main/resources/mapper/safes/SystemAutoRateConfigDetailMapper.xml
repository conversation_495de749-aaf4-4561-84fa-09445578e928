<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.insur.operation.auto.dao.SystemAutoRateConfigDetailMapper">
    <update id="updateList">
        <foreach collection="detailDtos" item="detailDto" separator=";">
            update system_auto_rate_config_detail set
                product_id = #{detailDto.productId},
                config_id = #{detailDto.configId},
                region_name = #{detailDto.regionName},
                region_code = #{detailDto.regionCode},
                promotion_policy = #{detailDto.promotionPolicy},
                remark = #{detailDto.remark},
                update_by = #{detailDto.updateBy}
            where id = #{detailDto.id}
        </foreach>
    </update>

    <select id="selectByConfigId" resultType="com.cfpamf.ms.insur.operation.auto.dto.SystemAutoRateConfigDetailDto">
        select t.*
        from system_auto_rate_config_detail t
        where t.enabled_flag = 0 and t.config_id = #{configId}
    </select>

    <select id="getConfigListByUser" resultType="com.cfpamf.ms.insur.operation.auto.dto.SystemAutoRateConfigDetailDto">
        select
            t.product_id as productId,
            t1.productName,
            t3.userName as updateBy,
            t.update_time as updateTime,
            t.id,
            t.promotion_policy
        from system_auto_rate_config_detail t
        left join sm_product t1 on t.product_id = t1.id and t1.enabled_flag = 0
        left join auth_user t3 on t3.userId = t.update_by and t3.enabled_flag = 0
        where t.enabled_flag = 0
        <if test="regionName != '' and regionName != null">
            and t.region_name = #{regionName}
        </if>
    </select>
    <select id="selectByProductIdAndRegionName" resultType="com.cfpamf.ms.insur.operation.auto.dto.SystemAutoRateConfigDetailDto">
        select
            t.product_id as productId,
            t1.productName,
            t3.userName as updateBy,
            t.update_time as updateTime,
            t.id,
            t.promotion_policy
        from system_auto_rate_config_detail t
        left join sm_product t1 on t.product_id = t1.id and t1.enabled_flag = 0
        left join auth_user t3 on t3.userId = t.update_by and t3.enabled_flag = 0
        where t.enabled_flag = 0
        <if test="regionName != '' and regionName != null">
            and t.region_name = #{regionName}
        </if>
        <if test="productId != null">
            and t.product_id = #{productId}
        </if>
    </select>
</mapper>