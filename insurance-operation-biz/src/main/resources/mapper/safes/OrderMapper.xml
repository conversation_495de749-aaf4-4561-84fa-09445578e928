<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cfpamf.ms.insur.operation.msg.dao.OrderMapper">

    <select id="selectOrder" resultType="com.cfpamf.ms.insur.operation.msg.pojo.vo.StatOrderDTO">
        select count(1) cts, sum(totalAmount) amounts
        from sm_order
        where paymentTime >= #{startDate}
          <![CDATA[
          and paymentTime < #{endDate}
          ]]>  and payStatus = '2'
    </select>
    <select id="selectRankByProduct" resultType="com.cfpamf.ms.insur.operation.msg.pojo.vo.StatRankVO">
        select recommendId user_id,au.userName user_name,au.regionName region_name,au.organizationName org_name,
        sum(totalAmount) amts, count(1) cts
        <if test="minAmounts !=null">
            , if(#{minAmounts} - sum(totalAmount)>0, #{minAmounts} - sum(totalAmount),0) exp
        </if>
        from sm_order so left join auth_user au on so.recommendId = au.userId and au.enabled_flag = 0
        where
        <![CDATA[ paymentTime >= #{startDate}  and paymentTime < #{endDate} ]]>
        <if test="productIds!=null and productIds.size() >0">
            and productId in
            <foreach collection="productIds" item="productId" open="(" close=")"
                     separator=",">
                #{productId}
            </foreach>
        </if>
        <if test="regionNames!=null and regionNames.size() >0">
            and au.regionName in
            <foreach collection="regionNames" item="item" open="(" close=")"
                     separator=",">
                #{item}
            </foreach>
        </if>
        and payStatus = '2'
        group by recommendId
        <if test="minCts !=null">
            having cts>=#{minCts}
        </if>
        <if test="sortCol == 'cts'">
            order by cts desc,amts desc
        </if>
        <if test="sortCol != 'cts'">
            order by amts desc, cts desc
        </if>
        limit #{maxRows}
    </select>
    <select id="getOrderAmount" resultType="java.math.BigDecimal">
        SELECT totalAmount
        FROM sm_order
        WHERE fhOrderId = #{orderId}
    </select>
    <select id="getCancelOrderAmount" resultType="java.math.BigDecimal">
        select case when total_amount is null then 0 else total_amount end total_amount
        from sm_order_item
        where fh_order_id like CONCAT('%', SUBSTRING_INDEX(#{orderId},'_',1), '%')
        and app_status = 4
        order by create_time asc
        limit 1
    </select>

    <select id="getPolicyAmount" resultType="java.math.BigDecimal">
        SELECT (case when t7.total_amount is not null then t7.total_amount else t2.unitPrice * t2.qty end) as premium
        FROM sm_order_insured t1
                 LEFT JOIN sm_order t2 ON t1.fhOrderId = t2.fhOrderId
                 LEFT JOIN sm_order_item t7 ON t1.fhOrderId = t7.fh_order_id
            AND t1.idNumber = t7.id_number
            AND t1.appStatus = t7.app_status
        WHERE t1.policyNo = #{policyNo} limit 1

    </select>
    <select id="getOrderMessage"
            resultType="com.cfpamf.ms.insur.operation.activity.listener.GrantCouponsOrderMessage">
        SELECT t1.*
        FROM sm_order t1
        WHERE t1.fhOrderId = #{orderId} LIMIT 1
    </select>

    <select id="getOrderMessageList"
            resultType="com.cfpamf.ms.insur.operation.activity.listener.GrantCouponsOrderMessage">
        SELECT t1.*
        FROM sm_order t1
        WHERE t1.fhOrderId in
        <foreach collection="orderIdList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>
    <select id="getOrderRisk" resultType="com.cfpamf.ms.insur.operation.activity.entity.SmOrderRiskDuty">
        select fh_order_id           fhOrderId,
               risk_id               riskId,
               policy_no          as policyNo,
               risk_name             riskName,
               insured_id_number     insuredIdNumber,
               insured_period_type   insuredPeriodType,
               insured_period        insuredPeriod,
               period_type           periodType,
               payment_period        paymentPeriod,
               payment_period_type   paymentPeriodType,
               app_status            appStatus,
               main_insurance        mainInsurance,
               sum(premium)       as premium,
               sum(amount)        as amount,
               sum(refund_amount) as refundAmount
        from sm_order_risk_duty
        where fh_order_id = #{orderId}
          and policy_no = #{policyNo}
          and risk_id = #{riskId}
          and insured_id_number = #{insuredIdNumber}
          and premium > 0
          and enabled_flag = 0
        group by fh_order_id, policy_no, risk_id, insured_id_number
    </select>

    <select id="getOrderTermRisk" resultType="com.cfpamf.ms.insur.operation.activity.entity.SmOrderRiskDuty">
        select order_id           fhOrderId,
               t1.id               riskId,
               t.risk_name             riskName,
               insured_id_number     insuredIdNumber,
               insured_period_type   insuredPeriodType,
               insured_period        insuredPeriod,
               period_type           periodType,
               payment_period        paymentPeriod,
               payment_period_type   paymentPeriodType,
               app_status            appStatus,
               main_insurance        mainInsurance,
               sum(premium)       as premium,
               sum(amount)        as amount,
               sum(refund_amount) as refundAmount
        from sm_order_renewal_term_risk t
        left join sys_risk t1 on t.risk_code = t1.risk_code and t1.enabled_flag = 0
        where order_id = #{orderId}
          and t1.id = #{riskId}
          and insured_id_number = #{insuredIdNumber}
          and premium > 0
          and t.enabled_flag = 0
          and term_num = #{termNum}
        group by order_id, t.id, insured_id_number
    </select>

    <select id="getPolicyPersonAmount" resultType="java.math.BigDecimal">
        SELECT sum(((case when t7.total_amount is not null then t7.total_amount else t2.unitPrice * t2.qty end)
            * (case when t1.appStatus = 1 then 1 else -1 end))) as premium
        FROM sm_order_insured t1
                 LEFT JOIN sm_order t2 ON t1.fhOrderId = t2.fhOrderId
                 LEFT JOIN sm_order_item t7 ON t1.fhOrderId = t7.fh_order_id
            AND t1.idNumber = t7.id_number
            AND t1.appStatus = t7.app_status
        WHERE t1.policyNo = #{policyNo}
          and t2.fhOrderId = #{orderId}
          and t1.idNumber = #{insuredIdNumber}
          and t2.planId = #{planId}
    </select>

    <select id="getPolicyPersonAmountByCommissionDetail" resultType="java.math.BigDecimal">
        SELECT amount
        FROM sm_commission_detail
        WHERE policy_no = #{policyNo}
          and order_id = #{orderId}
          and insured_id_number = #{insuredIdNumber}
          and plan_id = #{planId}
          and term_num = #{termNum}
          and policy_status= '1' limit 1
    </select>
    <select id="getPolicyPersonAmountByEndorNo" resultType="java.math.BigDecimal">
        SELECT total_amount
        FROM sm_order_item
        WHERE policy_no = #{policyNo}
          and fh_order_id = #{orderId}
          and id_number = #{insuredIdNumber}
          and app_status= '4'
          and type = '1'
          limit 1
    </select>

    <select id="getOrderStatus" resultType="java.lang.String">
        select app_status from sm_order_item
        where fh_order_id = #{orderId}
        limit 1
    </select>

    <select id="getOrderItemInfo" resultType="com.cfpamf.ms.insur.operation.activity.dto.RedEnvelopCommissionDetailDTO">
        select
            total_amount totalAmount,
                fh_order_id orderId,
               case when INSTR (fh_order_id ,'_') =0 then 0 else SUBSTRING_INDEX(fh_order_id,'_',-1) end orderIdIndex,
               id_number idNumber,
               create_time createTime,
               app_status policyStatus
        from sm_order_item
        where fh_order_id like CONCAT('%', SUBSTRING_INDEX(#{orderId},'_',1), '%')
        <![CDATA[
          and create_time >='2023-03-01 00:00:00' and create_time <='2023-04-30 23:59:59'
        ]]>
    </select>

    <select id="getOrderInfoByOrderId" resultType="com.cfpamf.ms.insur.operation.order.dto.SmOrderDto">
        select t3.term_num,t.`customerAdminId`,t.fhorderid,sum(t3.amount) as totalAmount,t1.productAttrCode,t.recommendId,t3.account_time accountTime
        ,t2.appStatus
        from sm_order t
        left join sm_product t1 on t1.id = t.productId
        LEFT JOIN sm_order_insured t2 ON t2.fhOrderId=t.fhOrderId
        left join sm_commission_detail t3 on t3.order_id = t2.fhOrderId and t2.idNumber = t3.insured_id_number and
                                              t3.policy_status = t2.appStatus
        where t.fhorderid = #{orderId}
        and t2.appStatus in (1,4)
        group by t.fhorderId,t3.term_num
        order by t3.term_num desc
        limit 1
    </select>

    <select id="getInsurInfoByOrderId" resultType="com.cfpamf.ms.insur.operation.customer.dto.OrderCustomerDto">
        select t.fhOrderId,t.idNumber,t.policyNo,t.policyNo as thPolicyNo,t.personName,t.idType,t.appStatus
        ,t1.*
        from sm_order_insured t
        left join customer_interruption t1 on t1.id_number = t.idNumber
        where fhorderid = #{orderId} and t1.id is not null
    </select>

    <select id="getApplicantInfoByOrderId" resultType="com.cfpamf.ms.insur.operation.customer.dto.OrderCustomerDto">
        select t.fhOrderId,t.idNumber,t2.policy_No,t2.th_policy_no ,t.personName,t.idType,t2.app_Status,t2.type
             ,t1.*
        from sm_order_applicant t
        left join customer_interruption t1 on t1.id_number = t.idNumber
        left join sm_order_item t2 on t2.fh_Order_Id = t.fhOrderId
        where t.fhorderid = #{orderId} and t1.id is not null
        group by t.fhorderid
    </select>

    <select id="getItemInfoByOrderId" resultType="com.cfpamf.ms.insur.operation.customer.dto.OrderCustomerDto">
        select t.fh_Order_Id,t.id_Number,t.policy_No,t.th_policy_no,t1.personName,t.id_type,t.app_status
             ,t2.*
        from sm_order_item t
        left join sm_order_insured t1 on t.id_number = t1.idNumber and t.fh_order_id = t1.fhorderid
        left join customer_interruption t2 on t2.id_Number = t.id_Number
        where t.fh_order_id = #{orderId} and t2.id is not null
    </select>
    <select id="getApplicantLoanInfoByOrderId" resultType="com.cfpamf.ms.insur.operation.customer.dto.LoanOrderCustomerDto">
        select t2.fhOrderId,t2.idNumber,t2.personName,
            t1.policy_no,t1.policy_no as thPolicyNo,t2.personName as insuredPersonName,t2.idType,1 as appStatus
            ,t.*
        from customer_loan t
        left join sm_order_applicant t2 on t2.idNumber = t.id_number
        left join sm_order t1 on t2.fhOrderId=t1.fhOrderId
        where t2.fhorderId = #{orderId}
        limit 1
    </select>
    <select id="getLoanInfoByOrderId" resultType="com.cfpamf.ms.insur.operation.customer.dto.LoanOrderCustomerDto">
        select t2.fhOrderId,t2.idNumber,t2.personName,
            t2.policyNo,t2.policyNo as thPolicyNo,t2.personName as insuredPersonName,t2.idType,t2.appStatus
             ,t.*
        from customer_loan t
        left join sm_order_insured t2 on t2.idNumber = t.id_number
        where t2.fhorderId = #{orderId}
            limit 1
    </select>
    <select id="getPolicyByIdCardAndProductAttrCode" resultType="com.cfpamf.ms.insur.operation.retrospective.dto.CustomerConvertPolicyPremiumDto">
        select sop.fh_order_id,
                sop.policy_no,
                sop.policy_no,
                sop.premium,
                sop.cancel_amount,
                soa.idNumber as applicantIdNumber,
                so.paymentTime,
                sop.policy_state as policyState,
                soi.idNumber as insuredIdNumber
        from sm_order so
        left join sm_order_policy sop on sop.fh_order_id = so.fhOrderId
        left join sm_order_applicant soa on so.fhOrderId = soa.fhOrderId
        left join sm_order_insured soi on so.fhOrderId = soi.fhOrderId
        left join sm_product sp on so.productId = sp.id
        where sp.productAttrCode = 'person'
        <if test="isInsured == false ">
            <if test="idCardList!=null and idCardList.size() >0">
                and soa.idNumber in
                <foreach collection="idCardList" item="idCard" open="(" close=")"
                         separator=",">
                    #{idCard}
                </foreach>
            </if>
        </if>
        <if test="isInsured == true ">
            <if test="idCardList!=null and idCardList.size() >0">
                and soi.idNumber in
                <foreach collection="idCardList" item="idCard" open="(" close=")"
                         separator=",">
                    #{idCard}
                </foreach>
            </if>
        </if>
        <if test="date!=null and date!=''">
            <![CDATA[and so.paymentTime >= DATE_SUB(#{date}, INTERVAL 15 DAY) and so.paymentTime < DATE_ADD(#{date}, INTERVAL 15 DAY)]]>
        </if>
    </select>

    <select id="getGroupPolicyByIdCardAndProductAttrCode" resultType="com.cfpamf.ms.insur.operation.retrospective.dto.CustomerConvertPolicyPremiumDto">
        select sop.fh_order_id,
                sop.policy_no,
                sop.policy_no,
                sop.premium,
                sop.cancel_amount,
                so.paymentTime,
                so.paymentTime,
                soa.idNumber as applicantIdNumber
        from sm_order so
        left join sm_order_policy sop on sop.fh_order_id = so.fhOrderId
        left join sm_order_applicant soa on so.fhOrderId = soa.fhOrderId
        left join sm_product sp on so.productId = sp.id
        where sp.productAttrCode in ('group','employer')
        <if test="idCardList!=null and idCardList.size() >0">
            and soa.idNumber in
            <foreach collection="idCardList" item="idCard" open="(" close=")"
                     separator=",">
                #{idCard}
            </foreach>
        </if>
        <if test="date!=null and date!=''">
            <![CDATA[and so.paymentTime >= DATE_SUB(#{date}, INTERVAL 15 DAY) and so.paymentTime < DATE_ADD(#{date}, INTERVAL 15 DAY)]]>
        </if>
    </select>
</mapper>
