<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.insur.operation.honor.dao.HonorMetricConfigurationMapper">
    <select id="list" resultType="com.cfpamf.ms.insur.operation.honor.dto.HonorMetricConfigurationList">
        select
            level
            ,period
            ,metric_code
            ,metric_name
        from honor_metric_configuration
        where enabled_flag = 0
        <if test="level != null and level != ''">
            and level = #{level}
        </if>
        <if test="period != null and period != ''">
            and period = #{period}
        </if>
        <if test="metricCode != null and metricCode != ''">
            and metric_code = #{metricCode}
        </if>
    </select>
</mapper>