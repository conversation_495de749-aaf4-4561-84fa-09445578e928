<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.insur.operation.claim.dao.ClaimPushMonthDao">

    <resultMap id="BaseResultMap" type="com.cfpamf.ms.insur.operation.claim.po.SmClaimPushMonth">
        <id property="id" column="id" jdbcType="INTEGER"/>
        <result property="regionName" column="region_name" jdbcType="VARCHAR"/>
        <result property="organizationName" column="organization_name" jdbcType="VARCHAR"/>
        <result property="payMoney" column="pay_money" jdbcType="VARCHAR"/>
        <result property="payedNum" column="payed_num" jdbcType="VARCHAR"/>
        <result property="rejectNum" column="reject_num" jdbcType="VARCHAR"/>
        <result property="month" column="month" jdbcType="VARCHAR"/>
        <result property="sendTo" column="send_to" jdbcType="VARCHAR"/>
        <result property="enabledFlag" column="enabled_flag" jdbcType="TINYINT"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,region_name,organization_name,
        pay_money,payed_num,reject_num,
        month,send_to,enabled_flag,
        create_time,update_time
    </sql>
    <update id="updateById">
        update sm_claim_push_month set is_send = #{isSend} where id =#{id}
    </update>
    <select id="listByMonth" resultType="com.cfpamf.ms.insur.operation.claim.po.SmClaimPushMonth">
        select * from sm_claim_push_month where enabled_flag = 0 and is_send = 0 and month = #{month} and service_type = #{serviceType}
    </select>
</mapper>
