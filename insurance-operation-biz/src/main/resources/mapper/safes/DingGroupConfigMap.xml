<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.insur.operation.dingtalk.dao.DingGroupConfigMapper">

    <!--分页查询指定行数据-->
    <select id="queryAllByPage" resultType="com.cfpamf.ms.insur.operation.dingtalk.entity.DingGroupConfig" parameterType="com.cfpamf.ms.insur.operation.dingtalk.form.DingGroupConfigForm">
        select
        c.id,c.group_id,c.group_name,c.remark,c.enabled_flag,concat_ws('-',cu.userName,c.CREATED_BY) as CREATED_BY,c.CREATED_TIME,
        concat_ws('-',uu.userName,c.UPDATED_BY) as UPDATED_BY,c.UPDATED_TIME,c.robot_token,c.robot_token_secret, c.group_context_id, c.params_config
        from ding_group_config c left join auth_user cu on c.CREATED_BY = cu.userId
                                left join auth_user uu on c.UPDATED_BY = uu.userId
        <where>
            <if test="id != null and id != ''">
                and id = #{id}
            </if>
            <if test="groupId != null and groupId != ''">
                and group_id = #{groupId}
            </if>
            <if test="groupName != null and groupName != ''">
                and group_name like '%${groupName}%'
            </if>

            <if test="startDate != null">
                and CREATED_TIME >= #{startDate}
            </if>

            <if test="endDate != null">
                <![CDATA[and CREATED_TIME <= #{endDate}]]>
            </if>

        </where>
        order by c.id desc
    </select>

    <select id="queryByGroupIdAndGroupContextId" resultType="com.cfpamf.ms.insur.operation.dingtalk.entity.DingGroupConfig" >
        select
        c.id,c.group_id,c.group_name,c.remark,c.enabled_flag,c.CREATED_TIME,
        c.UPDATED_TIME,c.robot_token,c.robot_token_secret, c.group_context_id, c.params_config
        from ding_group_config c left join auth_user cu on c.CREATED_BY = cu.userId
        left join auth_user uu on c.UPDATED_BY = uu.userId
        where 1=1
        <if test="groupId != null and groupId != ''">
            and group_id = #{groupId}
        </if>
        <if test="groupContextId != null and groupContextId != ''">
            and group_context_id = #{groupContextId}
        </if>
        order by c.id desc
    </select>
</mapper>