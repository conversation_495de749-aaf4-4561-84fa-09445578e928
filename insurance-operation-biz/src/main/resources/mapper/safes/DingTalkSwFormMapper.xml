<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cfpamf.ms.insur.operation.dingtalk.dao.DingTalkSwFormMapper">
    <insert id="insertListOrUpdate">

        insert into ding_talk_sw_form(form_code, form_name, form_memo, form_setting, form_creator)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.formCode},#{item.formName},#{item.formMemo},#{item.formSetting},#{item.formCreator})
        </foreach>
        on duplicate key update form_name    = values(form_name),
                        form_memo    = values(form_memo),
                        form_setting = values(form_setting)
    </insert>
</mapper>
