<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.insur.operation.honor.dao.HonorRulesConfigurationMapper">
    <select id="list" resultType="com.cfpamf.ms.insur.operation.honor.dto.HonorRulesConfigurationList">
        select
            t1.id
            ,t1.`year`
            ,t1.honor_name
            ,t1.level
            ,t1.start_time
            ,t1.end_time
            ,t1.selection_type
            ,t1.state
            ,t2.userId as operation_code
            ,t2.userName as operation_name
            ,t1.update_time as operation_time
            ,t1.honor_norm_code
            ,t1.honor_norm_name
        from honors_rules_configuration t1
        left join auth_user t2 on t2.userId = t1.update_by and t2.enabled_flag = 0
        where t1.enabled_flag = 0
        <if test="year != null and year != ''">
            and year = #{year}
        </if>
        <if test="honorName != null and honorName != ''">
            and honor_name like concat(#{honorName},'%')
        </if>
        <if test="level != null and level != ''">
            and level = #{level}
        </if>
        order by t1.end_time desc,t1.id desc
    </select>

    <select id="getFinishedHonorsRulesConfigurationPoList" resultType="com.cfpamf.ms.insur.operation.honor.dto.HonorCalculateRule">
        select t1.*
        from honors_rules_configuration t1
        left join honors_selection_results t2 on t1.id = t2.honors_rules_configurations_id and t2.enabled_flag = 0
        where t1.enabled_flag = 0
        and t1.state = 2
        and t1.selection_type = 'system'
        and t2.id is null
    </select>

    <update id="updateState">
        <![CDATA[
            update honors_rules_configuration
            set state = case when start_time >= now() then 0
            when end_time < now() then 2 else 1 end where state in (0,1)
        ]]>
    </update>

    <insert id="insertConfiguration" useGeneratedKeys="true" keyProperty="id">
        <selectKey keyProperty="id" resultType="java.lang.Integer" order="AFTER">
            SELECT LAST_INSERT_ID()
        </selectKey>
        INSERT INTO safes.honors_rules_configuration
            (`year`,
            honor_name,
            `level`,
            start_time,
            end_time,
            period,
            selection_type,
            rule_code,
            honor_norm_name,
            honor_norm_code,
            selection_number,
            pre_selection_number,
            obtained_icon,
            un_obtained_icon,
            honor_rule_content,
            state,
            honor_type,
            file_name,
            file_url,
            create_by,
            update_by)
        VALUES
            (#{year},
            #{honorName},
            #{level},
            #{startTime},
            #{endTime},
            #{period},
            #{selectionType},
            #{ruleCode},
            #{honorNormName},
            #{honorNormCode},
            #{selectionNumber},
            #{preSelectionNumber},
            #{obtainedIcon},
            #{unObtainedIcon},
            #{honorRuleContent},
            #{state},
            #{honorType},
            #{fileName},
            #{fileUrl},
            #{createBy},
            #{updateBy});
    </insert>
</mapper>