<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.cfpamf.ms.insur.operation.promotion.dao.OperationSynMaterialInfoMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.cfpamf.ms.insur.operation.promotion.entity.OperationSynMaterialInfoEntity" id="operationSynMaterialInfoMap">
        <result property="id" column="id"/>
        <result property="materialCode" column="material_code"/>
        <result property="sourceSystemCode" column="source_system_code"/>
        <result property="materialCategory" column="material_category"/>
        <result property="materialType" column="material_type"/>
        <result property="materialResponseId" column="material_response_id"/>
        <result property="materialLinkId" column="material_link_id"/>
        <result property="materialStatus" column="material_status"/>
        <result property="currentOptType" column="current_opt_type"/>
        <result property="productCode" column="product_code"/>
        <result property="productType" column="product_type"/>
        <result property="deleted" column="deleted"/>
        <result property="createUser" column="create_user"/>
        <result property="createTime" column="create_time"/>
        <result property="updateUser" column="update_user"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

	<update id="updateMaterialResponseId">
		update operation_syn_material_info set
        <if test="materialResponseId != null">
            material_response_id = #{materialResponseId} ,
        </if>
        <if test="materialLinkId != null">
            material_link_id = #{materialLinkId},
        </if>
        update_time = now(),
        material_status= #{materialStatus} where id = #{id}
	</update>

    <update id="logicDelete">
        update operation_syn_material_info set deleted = 1 where material_code = #{materialCode} and source_system_code = #{sourceSystemCode}
    </update>
</mapper>