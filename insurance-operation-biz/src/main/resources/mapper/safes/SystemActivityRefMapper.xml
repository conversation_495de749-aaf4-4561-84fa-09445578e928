<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.insur.operation.activity.dao.SystemActivityRefMapper">
    <resultMap id="BaseResultMap" type="com.cfpamf.ms.insur.operation.activity.entity.SystemActivityRef">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="sa_id1" jdbcType="INTEGER" property="saId1"/>
        <result column="sa_id2" jdbcType="INTEGER" property="saId2"/>
        <result column="conflict_rule" jdbcType="VARCHAR" property="conflictRule"/>
        <result column="enabled_flag" jdbcType="TINYINT" property="enabledFlag"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>
    <insert id="insertCopyRef">
        INSERT INTO `system_activity_ref` (
            `sa_id1`,
            `sa_id2`,
            `conflict_rule`,
            `enabled_flag`,
            `update_by`,
            `create_by`
        )
        SELECT
            #{Id},
            `sa_id2`,
            `conflict_rule`,
            `enabled_flag`,
            #{currentUserId},
            #{currentUserId}
        FROM `system_activity_ref`
        WHERE `sa_id1` = #{id}
          AND `enabled_flag` = 0
    </insert>
    <update id="softDeleteBySystemActivityId">
        update system_activity_ref
        set enabled_flag = 1
        where sa_id1 = #{systemActivityId}
          and enabled_flag = 0
    </update>

    <select id="getBySystemActivityId"
            resultType="com.cfpamf.ms.insur.operation.activity.vo.SystemActivityRefVo">
        select t1.id,
               t1.sa_id2 as systemActivityId,
               t1.conflict_rule,
               t2.title  as activityName
        from system_activity_ref t1
                 left join system_activity_programme t2 on t2.id = t1.sa_id2
        where t1.sa_id1 = #{systemActivityId}
          and t1.enabled_flag = 0
    </select>

</mapper>