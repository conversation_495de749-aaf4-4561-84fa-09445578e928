<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.insur.operation.addCommission.dao.WhaleAddCommissionDetailItemMapper">

  <sql id="Base_Column_List">
    id, endorsement_no, policy_no, insured_code, sell_product_code, risk_code, product_status, term_num, commission_type,
    proportion, data_id, uuid, enabled_flag, create_time, update_time
  </sql>

  <update id="batchUpdateProportion">
    INSERT INTO whale_add_commission_detail_item
    (endorsement_no, policy_no, sell_product_code, risk_code, product_status, term_num,
    commission_type, proportion, amount, data_id, uuid, enabled_flag,
    settlement_state, batch_no, add_commission_amount, data_index, insured_code, activity_name)
    values
    <foreach collection="list" item="det" separator=",">
      (#{det.endorsementNo},#{det.policyNo},#{det.sellProductCode},#{det.riskCode},#{det.productStatus},#{det.termNum},
      #{det.commissionType},#{det.proportion},#{det.amount},#{det.dataId},#{det.uuid},#{det.enabledFlag},#{det.settlementState},
      #{det.batchNo},#{det.addCommissionAmount},#{det.dataIndex},#{det.insuredCode},#{det.activityName})
    </foreach>
    ON DUPLICATE KEY UPDATE
    proportion = values(proportion),
    amount = values(amount),
    add_commission_amount = values(add_commission_amount),
    settlement_state = values(settlement_state),
    activity_name = values(activity_name),
    batch_no = if(batch_no = '',values(batch_no),batch_no)
  </update>

  <update id="batchUpdateProportionPreservation">
    INSERT INTO whale_add_commission_detail_item
    (endorsement_no, policy_no, sell_product_code, risk_code, product_status, term_num,
    commission_type, proportion, amount, data_id, uuid, enabled_flag,
    settlement_state, batch_no, add_commission_amount, data_index, insured_code, activity_name,acceptance_state,request_id)
    values
    <foreach collection="list" item="det" separator=",">
      (#{det.endorsementNo},#{det.policyNo},#{det.sellProductCode},#{det.riskCode},#{det.productStatus},#{det.termNum},
      #{det.commissionType},#{det.proportion},#{det.amount},#{det.dataId},#{det.uuid},#{det.enabledFlag},#{det.settlementState},
      #{det.batchNo},#{det.addCommissionAmount},#{det.dataIndex},#{det.insuredCode},#{det.activityName},#{det.acceptanceState},#{det.requestId})
    </foreach>
    ON DUPLICATE KEY UPDATE
    proportion = values(proportion),
    amount = values(amount),
    add_commission_amount = values(add_commission_amount),
    settlement_state = values(settlement_state),
    activity_name = values(activity_name),
    batch_no = if(batch_no = '',values(batch_no),batch_no),
    request_id = values(request_id),
    acceptance_state = values(acceptance_state)
  </update>

  <update id="softDeleteByDataIdAndType">
    update
      whale_add_commission_detail_item
    set enabled_flag = 1
    where data_id = #{dataId}
      and commission_type = #{type}
      and enabled_flag = 0
  </update>
  <update id="updateProportionByDataId">
    update whale_add_commission_detail_item
    set proportion = #{proportion},add_commission_amount = #{proportion}
    where data_id = #{dataId}
      and commission_type = #{type}
      and batch_no = ''
  </update>
  <delete id="deleteByDataIdAndType">
    delete from
    whale_add_commission_detail_item
    where data_id = #{dataId}
      and commission_type = #{type}
      and enabled_flag = 0
  </delete>

  <select id="getByBatchNoAndSaId" resultType="com.cfpamf.ms.insur.operation.addCommission.dto.AddCommissionSettlementPushDto">
    select
      t.id
      ,t.`batch_no`
      ,t.`policy_no`
      ,t1.contract_code
      ,t.`endorsement_no`
      ,t1.`order_time`
      ,t1.`approved_time`
      ,t1.`apply_time`
      ,t1.`payment_time`
      ,t1.`region_name`
      ,t1.`region_code`
      ,t1.`org_code`
      ,t1.`org_name`
      ,t1.`recommend_id`
      ,t1.`recommend_name`
      ,t1.`sell_product_id`
      ,t1.`sell_product_code`
      ,t1.`sell_product_name`
      ,t1.`risk_id`
      ,t1.`risk_code`
      ,t1.risk_name
      ,t1.`long_short_flag`
      ,t1.`applicant_name`
      ,t1.applicant_id_card
      ,t1.insured_name
      ,t1.insured_id_card
      ,t1.`term_num`
      ,t.`amount`
      ,t1.premium
      ,t1.surrender_premium
      ,t.add_commission_amount
      ,t.proportion as addCommissionProportion
      ,t.`proportion`
      ,t1.`product_status`
      ,t1.is_activity_order
      ,t1.is_dist_order
      ,t1.`preservation_type`
      ,t1.preservation_project
      ,t.insured_code
      ,t1.event_type_code
      ,t.uuid
      ,t.create_time addCommissionTime
      ,t.data_id as activityId
      ,t.activity_name
      ,t.settlement_state
      ,t1.preservation_effect_time
      ,t1.account_time
    from whale_add_commission_detail_item t
    left join `dwa_ep_policy_product_info` t1 on t.`policy_no` = t1.`policy_no` and t.`endorsement_no` = t1.`endorsement_no`
    and t.`insured_code` = t1.`insured_code` and t1.`sell_product_code` = t.`sell_product_code` and t.`risk_code` = t1.`risk_code`
    and t.`product_status` = t1.`policy_status` and t.`term_num` = t1.`term_num`
    where settlement_state = 0 and acceptance_state = 1 and add_commission_amount != 0
    <if test="saId != null">
      and t.data_id = #{saId}
    </if>
    <if test="batchNoList != null and batchNoList.size() != 0">
      and t.batch_No in
      <foreach collection="batchNoList" index="index" item="item" separator="," open="(" close=")">
        #{item}
      </foreach>
    </if>
    <if test="id != null">
      and t.id > #{id}
    </if>
    order by t.id
    <if test="id != null">
       limit #{size}
    </if>
    <if test="id == null">
      limit #{offset}, #{size}
    </if>
  </select>

  <select id="getDataByParam" resultType="com.cfpamf.ms.insur.operation.addCommission.dto.AddCommissionSettlementPushDto">
    select
    t.id
    ,t.`batch_no`
    ,t.`policy_no`
    ,t1.contract_code
    ,t.`endorsement_no`
    ,t1.`order_time`
    ,t1.`approved_time`
    ,t1.`apply_time`
    ,t1.`payment_time`
    ,t1.`region_name`
    ,t1.`region_code`
    ,t1.`org_code`
    ,t1.`org_name`
    ,t1.`recommend_id`
    ,t1.`recommend_name`
    ,t1.`sell_product_id`
    ,t.`sell_product_code`
    ,t1.`sell_product_name`
    ,t1.`risk_id`
    ,t.`risk_code`
    ,t1.risk_name
    ,t1.`long_short_flag`
    ,t1.`applicant_name`
    ,t1.applicant_id_card
    ,t1.insured_name
    ,if(t1.insured_id_card is null,'',t1.insured_id_card) insured_id_card
    ,t.`term_num`
    ,t.`amount`
    ,t1.premium
    ,t1.surrender_premium
    ,t.add_commission_amount
    ,t.proportion as addCommissionProportion
    ,t1.`product_status`
    ,t1.is_activity_order
    ,t1.is_dist_order
    ,t1.`preservation_type`
    ,t1.preservation_project
    ,t.insured_code
    ,t1.event_type_code initialEventCode
    ,t.uuid
    ,t.create_time addCommissionTime
    ,t.data_id as activityId
    ,t.activity_name
    ,t.settlement_state
    ,t.commission_type
    ,t.data_index
    ,t.amount
    ,t.product_status policy_status
    ,t1.account_time
    from whale_add_commission_detail_item t
    left join `dwa_ep_policy_product_info` t1 on t.`policy_no` = t1.`policy_no` and t.`endorsement_no` = t1.`endorsement_no`
    and t.`insured_code` = t1.`insured_code` and t1.`sell_product_code` = t.`sell_product_code` and t.`risk_code` = t1.`risk_code`
    and t.`product_status` = t1.`policy_status` and t.`term_num` = t1.`term_num`
    where settlement_state in (0,1,2)
    <if test="saId != null">
      and t.data_id = #{saId}
    </if>
    <if test="insuredCode != null and insuredCode != ''">
      and t.insured_code = #{insuredCode}
    </if>
    <if test="policyNo != null and policyNo != ''">
      and t.policy_no = #{policyNo}
    </if>
    <if test="endorsementNo != null and endorsementNo != ''">
      and t.endorsement_no = #{endorsementNo}
    </if>
    <if test="termNum != null">
      and t.term_num = #{termNum}
    </if>
    order by IF(t1.account_time is null,CURRENT_DATE,t1.`account_time`) desc,t.product_status desc
  </select>

  <update id="updateSettlementState">
    update whale_add_commission_detail_item t
    set t.settlement_state = 3
    where t.id in
    <foreach collection="list" item="item" open="(" separator="," close=")">
      #{item.id}
    </foreach>
  </update>

  <select id="getByDataIndex" resultType="com.cfpamf.ms.insur.operation.addCommission.po.WhaleAddCommissionDetailItem">
    select * from whale_add_commission_detail_item t
    where settlement_state in (0,1,2) and t.data_index in
    <foreach collection="list" item="item" open="(" separator="," close=")">
      #{item}
    </foreach>
  </select>

  <select id="getByBatchNo" resultType="com.cfpamf.ms.insur.operation.addCommission.po.WhaleAddCommissionDetailItem">
    select batch_no,data_id from
    whale_add_commission_detail_item t
    where settlement_state = 0 and batch_no = #{batchNo} and acceptance_state in (0,2)
    group by batch_no,data_id
  </select>

  <update id="updateAcceptanceState">
    update whale_add_commission_detail_item t
    set t.acceptance_state = #{acceptanceState},t.request_id = #{requestId}
    where t.data_id = #{dataId} and batch_no = #{batchNo}
  </update>
  <update id="updateSettlementStateToWait">
    update whale_add_commission_detail_item t
    set t.settlement_state = 1
    where request_id = #{requestId} and settlement_state = 0
  </update>

  <select id="pageAddCommissionByUser" resultType="com.cfpamf.ms.insur.operation.addCommission.dto.WxUserAddCommissionListVo">
    select
    t.id
    ,t.`policy_no`
    ,t1.contract_code contractCode
    ,t.`endorsement_no`
    ,t1.`account_time` businessAccountTime
    ,t1.`region_name`
    ,t1.`region_code`
    ,t1.`org_code`
    ,t1.`org_name`
    ,t1.`recommend_id`
    ,t1.`recommend_name`
    ,if(t1.`sell_product_name` != '',t1.sell_product_name,t1.`risk_name` ) productName
    ,t1.`risk_code` product_code
    ,t1.risk_name
    ,if(t1.`long_short_flag` = 0,'短险','长险') longShortFlag
    ,t1.`applicant_name`
    ,t1.insured_name
    ,t1.`term_num` renewalPeriod
    ,t.`amount`
    ,sum(t1.premium) policyPremium
    ,sum(t.add_commission_amount) addCommissionAmount
    ,t1.`product_status`
    ,if(t1.is_activity_order = 0,'否','是') ruralProxyFlag
    ,if(t1.is_dist_order = 0,'普通订单','分销订单') distributionFlag
    ,t.data_id as activityId
    ,t.activity_name
    from whale_add_commission_detail_item t
    left join `dwa_ep_policy_product_info` t1 on t.`policy_no` = t1.`policy_no` and t.`endorsement_no` = t1.`endorsement_no`
    and t.`insured_code` = t1.`insured_code` and t1.`sell_product_code` = t.`sell_product_code` and t.`risk_code` = t1.`risk_code`
    and t.`product_status` = t1.`policy_status` and t.`term_num` = t1.`term_num`
    where t1.recommend_id = #{userId}
    <if test='startTime != null'>
      <![CDATA[ AND t1.account_time>=#{startTime}]]>
    </if>
    <if test='endTime != null'>
      <![CDATA[ AND t1.account_time<=#{endTime}]]>
    </if>
    <if test='longShortFlag != null'>
      and t1.`long_short_flag` = #{longShortFlag}
    </if>
    <if test='distributionFlag != null'>
      and t1.is_dist_order = #{distributionFlag}
    </if>
    <if test='ruralProxyFlag != null'>
      and t1.is_activity_order = #{ruralProxyFlag}
    </if>
    <if test="policyNo != null and policyNo != ''">
      and t.policy_no = #{policyNo}
    </if>
    <if test="applicantName != null and applicantName != ''">
      and t1.`applicant_name`like concat("%", #{applicantName}, "%")
    </if>
    <if test="insuredName != null and insuredName != ''">
      and t1.`insured_name` like concat("%", #{insuredName}, "%")
    </if>
    <if test="applicantMobile != null and applicantMobile != ''">
      and t1.`applicant_mobile` like concat("%", #{applicantMobile}, "%")
    </if>
    <if test="insuredMobile != null and insuredMobile != ''">
      and t1.`insured_mobile` like concat("%", #{insuredMobile}, "%")
    </if>
    <if test="applicantIdCard != null and applicantIdCard != ''">
      and t1.`applicant_id_card`like concat("%", #{applicantIdCard}, "%")
    </if>
    <if test="insuredIdCard != null and insuredIdCard != ''">
      and t1.`insured_id_card` like concat("%", #{insuredIdCard}, "%")
    </if>
    group by `policy_no` ,`insured_id_card` ,`applicant_id_card` ,t.`product_status`,t.data_id
    having sum(add_commission_amount) != 0
  </select>
  <select id="sumAddCommissionByUserSmy" resultType="com.cfpamf.ms.insur.operation.addCommission.dto.WxCmsSmyVo">
    select
      sum(t.add_commission_amount) addCommissionAmount
    from whale_add_commission_detail_item t
    left join `dwa_ep_policy_product_info` t1 on t.`policy_no` = t1.`policy_no` and t.`endorsement_no` = t1.`endorsement_no`
    and t.`insured_code` = t1.`insured_code` and t1.`sell_product_code` = t.`sell_product_code` and t.`risk_code` = t1.`risk_code`
    and t.`product_status` = t1.`policy_status` and t.`term_num` = t1.`term_num`
    where t1.recommend_id = #{userId}
    <if test='startTime != null'>
      <![CDATA[ AND t1.account_time>=#{startTime}]]>
    </if>
    <if test='endTime != null'>
      <![CDATA[ AND t1.account_time<=#{endTime}]]>
    </if>
    <if test='longShortFlag != null'>
      and t1.`long_short_flag` = #{longShortFlag}
    </if>
    <if test='distributionFlag != null'>
      and t1.is_dist_order = #{distributionFlag}
    </if>
    <if test='ruralProxyFlag != null'>
      and t1.is_activity_order = #{ruralProxyFlag}
    </if>
    <if test="policyNo != null and policyNo != ''">
      and t.policy_no = #{policyNo}
    </if>
    <if test="applicantName != null and applicantName != ''">
      and t1.`applicant_name`like concat("%", #{applicantName}, "%")
    </if>
    <if test="insuredName != null and insuredName != ''">
      and t1.`insured_name` like concat("%", #{insuredName}, "%")
    </if>
    <if test="applicantMobile != null and applicantMobile != ''">
      and t1.`applicant_mobile` like concat("%", #{applicantMobile}, "%")
    </if>
    <if test="insuredMobile != null and insuredMobile != ''">
      and t1.`insured_mobile` like concat("%", #{insuredMobile}, "%")
    </if>
    <if test="applicantIdCard != null and applicantIdCard != ''">
      and t1.`applicant_id_card`like concat("%", #{applicantIdCard}, "%")
    </if>
    <if test="insuredIdCard != null and insuredIdCard != ''">
      and t1.`insured_id_card` like concat("%", #{insuredIdCard}, "%")
    </if>
  </select>

  <update id="updateSettlementStateTo2">
    update whale_add_commission_detail_item set settlement_state = 2
    where settlement_state = 1 and uuid in
    <foreach collection="uuids" item="item" open="(" separator="," close=")">
      #{item}
    </foreach>
  </update>
</mapper>
