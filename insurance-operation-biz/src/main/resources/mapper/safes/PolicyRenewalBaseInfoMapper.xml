<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.insur.operation.phoenix.dao.PolicyRenewalBaseInfoMapper">

    <select id="listPolicyRenewalBaseInfoByOldPolicyNos" resultType="com.cfpamf.ms.insur.operation.phoenix.pojo.po.PolicyRenewalBaseInfo">
        select * from policy_renewal_base_info
        where old_policy_no in
        <foreach collection="oldPolicyList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>


    <select id="listExistTransferPolicyNo" resultType="java.lang.String">
        select t.transfer_policy_no
        from sm_order_renewal_follow t
        where t.transfer_policy_no in
        <foreach collection="transferPolicyList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>


    <select id = "autoQueryPersonTransferPolicy" resultType="com.cfpamf.ms.insur.operation.phoenix.pojo.dto.TransferPolicyDto">
        select distinct t.policyNo,t.idNumber,t1.endTime,t4.productName,t1.fhOrderId,t1.productId,t1.planId,t1.paymentTime,t1.startTime,
        t1.totalAmount,t1.submitTime
        from sm_order_insured t
        left join sm_order t1 on t1.fhOrderId = t.fhOrderId
        left join sm_product t4 on t1.productId = t4.id
        left join dwd_commodity_main_product_name_map t2 on t1.productId = t2.zhnx_product_id and t1.planId = t2.zhnx_plan_id
        where t.policyNo=#{newPolicyNo} and t2.risk_name= #{riskCategory2}
    </select>

    <select id = "autoQueryGroupTransferPolicy" resultType="com.cfpamf.ms.insur.operation.phoenix.pojo.dto.TransferPolicyDto">
        select t2.th_policy_no policyNo,t2.id_Number,t1.endTime,t4.productName,
        REVERSE(SUBSTR( REVERSE( t1.fhOrderId ) FROM INSTR( REVERSE( t1.fhOrderId ), '_' ) + 1 )) fhOrderId,
        t1.productId,t1.planId,t1.paymentTime,t1.startTime,
        t1.totalAmount,t1.submitTime
        from sm_order_applicant t
        left join sm_order t1 on t1.fhOrderId = t.fhOrderId
        left join sm_order_item t2 on t2.fh_order_id = t1.fhOrderId
        left join sm_product t4 on t1.productId = t4.id
        left join dwd_commodity_main_product_name_map t3 on t1.productId = t3.zhnx_product_id and t1.planId = t3.zhnx_plan_id
        where t2.th_policy_no=#{newPolicyNo} and t3.risk_name= #{riskCategory2}
    </select>

    <update id="updateRenewedStatusByPolicyNo">
        update policy_renewal_base_info
        set  new_policy_no=#{newPolicyNo},ins_status=#{insStatus},
        renewal_type=#{renewalType},new_order_time=#{newOrderTime},new_premium=#{newPremium},
        new_product_code=#{newProductCode},new_product_name=#{newProductName},
        renewed_success_time = if(renewed_success_time is null,now(),renewed_success_time),
        todo_status='DONE',
        todo_finish_time= if(todo_finish_time is null,now(),todo_finish_time),
        update_time = now()
        where old_policy_no = #{oldPolicyNo}
    </update>


</mapper>