<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.insur.operation.customer.dao.DictionaryMapper">

    <select id="listDictionarys" resultType="com.cfpamf.ms.insur.operation.base.vo.DictionaryVO">
        SELECT * FROM dictionary WHERE 1=1
        <if test='onlyEnabled'>
            AND enabled_flag=0
        </if>
        <if test='type != null'>
            AND type = #{type}
        </if>
        <if test='keyword != null'>
            AND (code LIKE CONCAT('%',#{keyword},'%') OR name LIKE CONCAT('%',#{keyword},'%') )
        </if>
        order by sorting
    </select>
</mapper>
