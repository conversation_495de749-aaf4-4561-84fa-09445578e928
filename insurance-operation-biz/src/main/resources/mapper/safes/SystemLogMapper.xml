<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.insur.operation.log.dao.SystemLogMapper">

    <insert id="insertSystemLog" useGeneratedKeys="true">
        INSERT INTO system_log (userId, userIp, actionType, moduleName, description, method, url, parameters, result, message, costTime)
        VALUES
        (#{userId}, #{userIp}, #{actionType}, #{moduleName}, #{description}, #{method}, #{url}, #{parameters} ,#{result}, #{message}, #{costTime})
    </insert>

    <select id="listSystemLog" resultType="com.cfpamf.ms.insur.operation.log.vo.SystemLogVO">
        SELECT t1.*, t2.userName FROM system_log t1 LEFT JOIN auth_user t2 ON t1.userId = t2.userId AND t2.enabled_flag = 0
        WHERE 1=1
        <if test = 'startDate != null' >
            <![CDATA[ AND t1.create_time>=#{startDate} ]]>
        </if>
        <if test = 'endDate != null' >
            <![CDATA[ AND t1.create_time<=#{endDate} ]]>
        </if>
        <if test = 'userId !=  null' >
            AND t1.userId = #{userId}
        </if>
        <if test = 'moduleName !=  null' >
            AND t1.moduleName = #{moduleName}
        </if>
        <if test = 'description !=  null' >
            AND t1.description LIKE CONCAT('%',#{description},'%')
        </if>
        ORDER BY t1.create_time DESC
    </select>

    <delete id="deleteLogBeforeDate">
        DELETE FROM system_log WHERE  <![CDATA[ create_time < #{date} ]]>
    </delete>
</mapper>