<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.insur.operation.customer.dao.CustomerLoanFirstMapper">
    <insert id="insertFromDwd">
        INSERT INTO customer_loan_first (customer_name, loan_cust_Id, loan_cust_role, customer_admin ,
        `insurance_phone`, recently_loan_amt, recently_loan_date,
         recently_loan_no,todo_state)
        SELECT
            customer_name,
            loan_cust_Id,
            loan_cust_role,
            customer_admin,
            `insurance_phone`,
            recently_loan_amt,
            recently_loan_date,
            recently_loan_no,
            0
        FROM dwd_safes_loan_first_customer t
            ON DUPLICATE KEY UPDATE
            customer_loan_first.customer_name          = t.customer_name,
            customer_loan_first.customer_admin          = t.customer_admin,
            customer_loan_first.insurance_phone            = t.insurance_phone,
            customer_loan_first.recently_loan_amt          = t.recently_loan_amt,
            customer_loan_first.`recently_loan_no` = t.`recently_loan_no`,
            customer_loan_first.recently_loan_date = t.recently_loan_date;
    </insert>


    <update id="updateByLoanCustId">
        <foreach collection="list" item="item" index="index" open="" close="" separator=";">
            update customer_loan_first
            <set>
                conversion_state=#{item.conversionState},conversion_emp=#{item.conversionEmp}
                ,conversion_order_id=#{item.conversionOrderId},conversion_time=#{item.conversionTime}
                ,customer_id=#{item.customerId},id_number=#{item.idNumber}
            </set>
            where loan_cust_Id = #{item.loanCustId} and conversion_state = 0
        </foreach>
    </update>

    <select id="selectByLoanCustIds" resultType="java.lang.String">
        select loan_cust_id from customer_loan_first
        where loan_cust_id in
        <foreach collection="list" item="item" open="(" close=")" separator=",">
            #{item.loanCustId}
        </foreach>
        and conversion_state = 0
    </select>
</mapper>