<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cfpamf.ms.insur.operation.activity.dao.SystemActivityConstRuleWhaleMapper">
    <insert id="insertCopyRule">
        INSERT INTO `system_activity_const_rule_whale` (
            `sa_id`,
            `product_long_insurance`,
            `sell_product_ids`,
            `sell_product_names`,
            `risk_ids`,
            `risk_names`,
            `rule_type`,
            `joint_type`,
            `reward_type`,
            `quantity`,
            `params`,
            `enabled_flag`
        )
        SELECT
            #{Id},
            `product_long_insurance`,
            `sell_product_ids`,
            `sell_product_names`,
            `risk_ids`,
            `risk_names`,
            `rule_type`,
            `joint_type`,
            `reward_type`,
            `quantity`,
            `params`,
            `enabled_flag`
        FROM `system_activity_const_rule_whale`
        WHERE `sa_id` = #{id} AND `enabled_flag` = 0
    </insert>
    <select id="selectProductIdAndEndTime"
            resultType="com.cfpamf.ms.insur.operation.activity.dto.SystemActivityConstRuleWhaleDTO">
        select t1.*,
               t2.regions,
               t2.conflictRule,
               t2.type
        from system_activity_const_rule_whale t1
                 left join system_activity_programme t2 on t1.sa_id = t2.id
        where t2.activeFlag = 1
          and t1.enabled_flag = 0
          and
           <![CDATA[   t2.startTime <= #{endTime}
          and t2.endTime >= #{endTime}
        ]]>
             and t2.config_type = 'CONST'
          and (JSON_OVERLAPS(t1.sell_product_ids,'[${productId}]') or t1.product_ids='[]')
    </select>
</mapper>
