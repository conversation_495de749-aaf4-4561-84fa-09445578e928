<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.insur.operation.customer.dao.CustomerLoanFollowMapper">
    <insert id="insertFollow">
        update customer_loan_follow set newest = 0
        where  customer_id = #{customerId}
        and newest = 1 ;
        insert into customer_loan_follow(customer_id, id_number,intention, follow_time, follow_people)
        VALUES
        (#{customerId}, #{idNumber} , 'convert', #{accountTime}, 'system')
    </insert>
    <update id="updateNewest">
        update customer_loan_follow set newest = 0
        where customer_id = #{customerId} and newest = 1
    </update>
    <select id="getLoanCustomerRemark" resultType="java.lang.Integer">
        select tag_sort
        from customer_loan
        where customer_id=#{customerId}
    </select>

    <select id="queryLoanFollowListRetrospective" resultType="com.cfpamf.ms.insur.operation.customer.dto.CustomerInterruptionFollowDto">
        select t1.id,customer_id,follow_time,
            case when t1.follow_people = 'system' then '系统' else t2.userName end followPeopleName,
            follow_people,service_mode,intention,'' intent,reason,reason_remark,remark,next_time,
            '' voiceAddress,'' recordJson,0 talking_time_len,'LOAN' taskType
        from customer_loan_follow t1
        left join auth_user t2 on t2.userId = t1.follow_people and t2.enabled_flag = 0
        where t1.follow_people = #{empCode} and t1.enabled_flag = 0
        and <![CDATA[t1.follow_time >= #{startDate} and t1.follow_time <= #{endDate}]]>
        order by follow_time desc,id desc
    </select>
</mapper>