<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.insur.operation.activity.dao.AuthUserMapper">

  <sql id="Base_Column_List">
    id, regionName, organizationName, userId, userName, userMobile, userType, wxOpenId,
    wxNickName, wxImgUrl, expireTime, loginId, create_time, update_time, batchNo, organizationFullName, 
    orgPath, postCode, postName, `status`, enabled_flag, create_by, update_by, regionCode, 
    orgCode, hrOrgId, userIdCard, agentId, agentTopUserId, realVerify, userEmail, serviceType, 
    switchTime, jobCode, hrUserId, bmsUserId, mainJobNumber, bizCode
  </sql>
  <select id="getByUserId" resultType="com.cfpamf.ms.insur.operation.activity.entity.AuthUser">
    select <include refid="Base_Column_List"/>
    from auth_user
    where userId = #{userId} and enabled_flag = 0
  </select>
    <select id="distinctRegion" resultType="com.cfpamf.ms.insur.operation.activity.entity.AuthUser">
        SELECT DISTINCT(organizationName) as organizationName, regionName, regionCode, orgCode FROM auth_user
    </select>
    <select id="getWxOpenIdByUserId" resultType="java.lang.String">
      select wxOpenId
      from auth_user
      where userId=#{userId} and enabled_flag=0
      limit 1
    </select>

  <select id="getUserList" resultType="com.cfpamf.ms.insur.operation.activity.entity.AuthUser">
    select <include refid="Base_Column_List"/>
    from auth_user
    where enabled_flag = 0 and status = 3
  </select>

  <select id="getDistinctUser" resultType="com.cfpamf.ms.insur.operation.activity.entity.AuthUser">
    select userId,userName
    from auth_user
    where enabled_flag = 0 and userid is not null
    group by userId,userName
  </select>

  <select id="getNotHeadUsers" resultType="com.cfpamf.ms.insur.operation.activity.entity.AuthUser">
    select <include refid="Base_Column_List"/>
    from auth_user
    where status = 3 and regionCode !=1 and enabled_flag = 0
  </select>

  <select id="listUserByUserIds" resultType="com.cfpamf.ms.insur.operation.activity.entity.AuthUser">
    select <include refid="Base_Column_List"/>
    from auth_user
    where userId in
    <foreach collection="list" open="(" close=")" separator="," item="item">
      #{item}
    </foreach>
    and enabled_flag = 0
  </select>
</mapper>