<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.insur.operation.auto.dao.SystemAutoRateConfigDetailHistoryMapper">
    <select id="selectByHistoryConfigId" resultType="com.cfpamf.ms.insur.operation.auto.dto.SystemAutoRateConfigDetailDto">
        select t.*
        from system_auto_rate_config_detail_history t
        where t.enabled_flag = 0 and t.config_id = #{configId}
    </select>
</mapper>