<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.insur.operation.prospectus.dao.PlanMapper">

    <select id="getById" resultType="com.cfpamf.ms.insur.operation.prospectus.entity.SmPlan">
        select *
        from sm_plan
        where id = #{id}
          and enabled_flag = 0
    </select>
    <select id="getByIdList" resultType="com.cfpamf.ms.insur.operation.activity.vo.PlanSimpleVo">
        select planName,id,productId
        from sm_plan
        where id in
        <foreach collection="idList" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
</mapper>