<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cfpamf.ms.insur.operation.pco.dao.PcoExtendInfoMapper">
    <update id="updateJobCode">
        update pco_extend_info p inner join user_post up
        on p.job_number = up.job_number and up.post_code = '2357'
            set p.job_code = up.job_code
        where p.job_code = ''
    </update>
    <select id="selectMissInfoDingUser" resultType="java.lang.String">
        select distinct submit_user_id
        from ding_talk_sw_form_instance t
                 left join pco_extend_info pei on t.submit_user_id = pei.ding_user_id
        where pei.id is null
    </select>

    <select id="selectUserByPost" resultType="com.cfpamf.ms.insur.operation.pco.vo.UserVo">

        SELECT up.*, au.userName user_name,au.userId as userId
        FROM user_post up
                 left join auth_user au on up.job_number = au.userId and au.enabled_flag = 0
        WHERE up.post_code = #{postCode}
          AND up.enabled_flag = 0
          <if test="keyword!=null">
          AND (up.job_number LIKE
               CONCAT('%', #{keyword}, '%') or
              up.main_job_number LIKE CONCAT('%', #{keyword}, '%') OR au.userName LIKE CONCAT('%',#{keyword} ,'%'))
        </if>
        ORDER BY regionName DESC,
                 organizationName DESC,
                 userId
                ASC, up.create_time DESC
    </select>

    <update id="updateExtendInfo">
        <foreach collection="list" item="item" index="index" separator=";">
            update pco_extend_info
            <set>
                <if test="item.pcoLevel != null">pco_level = #{item.pcoLevel}, </if>
                <if test="item.beQualified != null">be_qualified = #{item.beQualified}, </if>
                update_time = CURDATE(),
            </set>
            where job_code = #{item.jobCode}
        </foreach>
    </update>
</mapper>
