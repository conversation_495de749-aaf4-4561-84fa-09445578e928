<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cfpamf.ms.insur.operation.msg.dao.UserPostMapper">
    <select id="selectByPostNotMain" resultType="com.cfpamf.ms.insur.operation.msg.pojo.po.UserPostSimple">
        select distinct userName,
                        au.userMobile mobile,
                        main_job_number,
                        job_number,
                        region_name,
                        region_code,
                        org_name,
                        org_code
        from user_post up
                 left join auth_user au on au.userId = up.job_number and au.enabled_flag = 0
        where post_code = #{postCode}
          and service_status = 0
       <![CDATA[ and region_code <> '1'
          and employee_status <> 8
        ]]>
    </select>


    <select id="selectByPostsNotMain" resultType="com.cfpamf.ms.insur.operation.msg.pojo.po.UserPostSimple">
        select distinct userName,
                        au.userMobile mobile,
                        main_job_number,
                        job_number,
                        region_name,
                        region_code,
                        org_name,
                        org_code
        from user_post up
                 left join auth_user au on au.userId = up.job_number and au.enabled_flag = 0
        where post_code in
            <foreach collection="postCodes" item="post_codes" separator="," open="(" close=")">
                #{post_codes}
            </foreach>
          and service_status = 0
       <![CDATA[ and region_code <> '1'
          and employee_status <> 8
        ]]>
    </select>
    <!-- 根据岗位编码查询员工信息，包含总部员工信息-->
    <select id="selectByPostList" resultType="com.cfpamf.ms.insur.operation.msg.pojo.po.UserPostSimple">
        select  distinct userName,
                au.userMobile mobile,
                main_job_number,
                job_number,
                region_name,
                region_code,
                org_name,
                org_code
        from user_post up
        left join auth_user au on au.userId = up.job_number and au.enabled_flag = 0
        where post_code in
        <foreach collection="postCodes" item="post_codes" separator="," open="(" close=")">
            #{post_codes}
        </foreach>
        and service_status = 0
        <![CDATA[
          and employee_status <> 8
        ]]>
    </select>

    <select id="selectMissInfoDingUser" resultType="java.lang.String">
        select distinct submit_user_id
        from ding_talk_sw_form_instance t
                 left join pco_extend_info pei on t.submit_user_id = pei.ding_user_id
        where pei.id is null
    </select>
    <select id="selectPcoByNotSync" resultType="com.cfpamf.ms.insur.operation.msg.pojo.po.UserPostSimple">
        select distinct userName
                      , main_job_number
                      , up.job_number
                      , region_name
                      , region_code
                      , org_name
                      , org_code
                      , au.userMobile mobile
                      , up.job_code
                      , pei.ding_user_id
                      , pei.id
        from user_post up
                 left join auth_user au on au.userId = up.job_number and au.enabled_flag = 0
                 left join pco_extend_info pei on pei.job_code = up.job_code
        where post_code = #{postCode}
          and service_status = 0 <![CDATA[
          and employee_status <> 8
          ]]>
          and (pei.id is null or pei.ding_user_id = '')
    </select>
    <select id="selectByRegionSup" resultType="com.cfpamf.ms.insur.operation.msg.pojo.po.UserPostSimple">
        select distinct userName,
                        userMobile mobile,
                        main_job_number,
                        job_number,
                        region_name,
                        region_code,
                        org_name,
                        org_code
        from user_post up
                 left join auth_user au on au.userId = up.job_number and au.enabled_flag = 0
        where main_job_number in (select DISTINCT main_job_number
                                  from user_post
                                  where post_code = '2363')
          and service_status = 0
          and org_name like '%管理部'
    <![CDATA[
          and employee_status <> 8
        ]]>
    </select>
    <select id="selectAllNotMain" resultType="com.cfpamf.ms.insur.operation.msg.pojo.po.UserPostSimple">
        select distinct userName,
                        userMobile       mobile,
                        mainJobNumber    main_job_number,
                        userId           job_number,
                        regionName       region_name,
                        regionCode       region_code,
                        organizationName org_name,
                        orgCode          org_code
        from auth_user
        where userType = 'employee'
          <![CDATA[
          and `status` <> '8'
          and `regionName` <> '总部'
        ]]>
    </select>
    <select id="selectJobNumberByPostsInOffice" resultType="com.cfpamf.ms.insur.operation.msg.pojo.po.UserPostSimple">
        select  distinct main_job_number
        from user_post up
        where service_status = 0
       <![CDATA[
          and employee_status <> 8
        ]]>
       and post_code in
        <foreach collection="postCodes" item="post_codes" separator="," open="(" close=")">
            #{post_codes}
        </foreach>
    </select>
    <select id="selectJobNumberByPostsInOfficeAndOrgCodes"
            resultType="com.cfpamf.ms.insur.operation.msg.pojo.po.UserPostSimple">
        select  distinct main_job_number
        from user_post up
        where service_status = 0
        <![CDATA[
          and employee_status <> 8
        ]]>
        and post_code in
        <foreach collection="postCodes" item="post_codes" separator="," open="(" close=")">
            #{post_codes}
        </foreach>
        <if test="orgCodes != null and orgCodes.size() > 0">
            <foreach collection="orgCodes" item="org_code" separator="," open="and org_code in (" close=")">
                #{org_code}
            </foreach>
        </if>

    </select>

</mapper>
