<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.insur.operation.pco.dao.PcosScheduleMapper">
    <select id="list" resultType="com.cfpamf.ms.insur.operation.pco.vo.PcoScheduleInfo">
        select
            t.id as id,
            t.start_date as startDate,
            t.end_date as endDate,
            t.region_name as regionName,
            t.org_name as orgName,
            t.title as title,
            t.content as content,
            t.be_dispose as beDispose,
            t.create_time as createTime
        from pco_schedule t
        where 1=1
        <if test="startDate!=null">
            and date(t.start_date) >= date(#{startDate})
        </if>
        <if test="endDate!=null">
            <![CDATA[
                and date(end_date) <= date(#{endDate})
            ]]>
        </if>
        <if test="orgName!=null">
            and t.org_name = #{orgName}
        </if>
        <if test="regionName!=null">
            and t.region_name = #{regionName}
        </if>
        <if test="beDispose!=null">
            and t.be_dispose =  #{beDispose}
        </if>
        order by t.id desc
    </select>

    <update id="updateBedispose">
        update pco_schedule set be_dispose = 0
        where region_name = #{regionName} and date(start_date) = date(#{startDate}) and date(end_date) = date(#{endDate})
    </update>

    <select id="selectLaskWeekSchedule" resultType="com.cfpamf.ms.insur.operation.pco.vo.PcoScheduleInfo">
        select t1.region_name,t1.org_name,'每周PCO评分' as title,
               concat(MONTH(t.start_date),'月',DAY(t.start_date),'日~',
                   MONTH(DATE_SUB(CURDATE(),INTERVAL WEEKDAY(CURDATE()) + 1 DAY)),'月'
                   ,DAY(DATE_SUB(CURDATE(),INTERVAL WEEKDAY(CURDATE()) + 1 DAY)),'日PCO工作评分，请您及时处理。') as content,
               1 as be_dispose,
               t.start_date,
               DATE_SUB(CURDATE(),INTERVAL WEEKDAY(CURDATE()) + 1 DAY) end_date
        from pco_weeks_score t
        left join user_post t1 on t.job_code = t1.job_code
        left join pco_schedule t2 on t2.region_name = t1.region_name and t2.start_date = t.start_date
        where date(t.start_date) = DATE_SUB(CURDATE(),INTERVAL WEEKDAY(CURDATE()) + 7 DAY)
        and t1.region_name != '总部'
        and t2.region_name is null
        group by t1.region_name
    </select>
</mapper>