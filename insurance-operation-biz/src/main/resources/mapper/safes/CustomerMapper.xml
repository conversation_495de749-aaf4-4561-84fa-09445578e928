<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.insur.operation.customer.dao.CustomerMapper">

    <select id="listBackBreakCustomer" resultType="com.cfpamf.ms.insur.operation.customer.dto.WxInterruptionCustomerDto">
        SELECT DISTINCT t2.id AS customerId, t2.customerName,t1.last_interruption_time,t2.idNumber
        ,t1.current_loan,t1.loan,t4.intention,t4.follow_time
        ,t1.last_customer_admin,t1.renew_emp
        ,t1.extend,t2.cellPhone
        ,case when datediff(NOW(),t1.last_interruption_time)=0 then 1 else datediff(NOW(),t1.last_interruption_time) end days
        ,t1.renew_time
        ,t6.userName lastCustomerAdminName
        <if test="userType == 1">
            ,t6.regionName,t6.organizationName orgName
        </if>
        <if test="userType == 2">
            ,sum(case when t5.insur = 1 and t5.policy_state != 4 and t5.total_Amount>0 then 1
            when t5.insur = 0 and t5.policy_state != 4 and t5.cancel_flag is null then 1
            else 0 end) as renewPolicyCount,
            sum(t5.total_amount) as renewAmount
            ,t7.regionName,t7.organizationName orgName,t7.userName renewEmpName
        </if>
        FROM customer_interruption t1
        LEFT JOIN customer t2 ON t1.customer_id = t2.id
        LEFT JOIN customer_interruption_follow t4 on t2.id = t4.customer_id and newest = 1
        <if test="userType == 2">
            Left Join auth_user t7 on t7.userId = t1.renew_emp
            LEFT JOIN customer_interruption_policy t5 on t5.id_number = t1.id_number
        </if>
        left join auth_user t6 on t6.userId = t1.last_customer_admin
        WHERE t2.id is not null
        <if test="userType == 1">
            and t1.renew_state = 'UN_RENEW'
            <if test="orgName!=null">
                and t6.organizationName = #{orgName}
            </if>
            <if test="regionName!=null">
                and t6.regionName = #{regionName}
            </if>
            <if test="lastCustomerAdmin != null and lastCustomerAdmin!=''">
                and (t1.last_customer_admin like concat(#{lastCustomerAdmin},'%') or t6.userName like concat(#{lastCustomerAdmin},'%'))
            </if>
        </if>
        <if test="startDate !=  null">
            AND t1.last_interruption_time >= str_to_date(#{startDate}, '%Y-%m-%d')
        </if>
        <if test="endDate != null">
            and  <![CDATA[ t1.last_interruption_time < str_to_date(#{endDate}, '%Y-%m-%d')]]>
        </if>
        <if test="userType == 2">
            and t1.renew_state = 'RENEW'
            <if test="renewStartDate !=  null">
                AND t1.renew_time >= str_to_date(#{renewStartDate}, '%Y-%m-%d')
            </if>
            <if test="renewEndDate != null">
                and  <![CDATA[ t1.renew_time < str_to_date(#{renewEndDate}, '%Y-%m-%d')]]>
            </if>
            <if test="orgName!=null">
                and t7.organizationName = #{orgName}
            </if>
            <if test="regionName!=null">
                and t7.regionName = #{regionName}
            </if>
            <if test="renewEmp != null and renewEmp!=''">
                and (t1.renew_emp like concat(#{renewEmp},'%') or t7.userName like concat(#{renewEmp},'%'))
            </if>
        </if>
        <if test='intention != null and intention != ""'>
            <if test = 'intention == "noFollowUp"'>
                and t4.id is null
            </if>
            <if test = 'intention != "noFollowUp"'>
                and t4.intention = #{intention}
            </if>
        </if>
        <if test="productTypes != null and productTypes.size() != 0">
            and
            <foreach collection="productTypes" item = "productType" open = "(" separator="or" close=")">
                JSON_CONTAINS(t1.extend,JSON_OBJECT(#{productType},1))
            </foreach>
        </if>
        <if test='customerName!=null and customerName != ""'>
            and t2.customerName like concat(#{customerName},'%')
        </if>
        group by t2.id
        <if test="userType==1">
            ORDER BY t1.last_interruption_time desc
        </if>
        <if test="userType==2">
            ORDER BY t1.renew_time desc
        </if>
    </select>

    <select id="listBreakCustomer" resultType="com.cfpamf.ms.insur.operation.customer.dto.WxInterruptionCustomerDto">
        SELECT DISTINCT t2.id AS customerId, t2.customerName,t1.last_interruption_time,t2.idNumber
        ,t1.current_loan,t1.loan,t4.intention
        ,t1.extend
        ,case when datediff(NOW(),t1.last_interruption_time)=0 then 1 else datediff(NOW(),t1.last_interruption_time) end days
        ,t1.renew_time
        <if test="userType == 2">
            ,sum(case when t5.insur = 1 and t5.policy_state != 4 and t5.total_Amount>0 then 1
                    when t5.insur = 0 and t5.policy_state != 4 and t5.cancel_flag is null then 1
                    else 0 end) as renewPolicyCount,
            sum(t5.total_amount) as renewAmount
        </if>
        FROM customer_interruption t1
        LEFT JOIN customer t2 ON t1.customer_id = t2.id
        LEFT JOIN customer_interruption_follow t4 on t2.id = t4.customer_id and newest = 1
        <if test="userType == 2">
            LEFT JOIN customer_interruption_policy t5 on t5.id_number = t1.id_number and t5.customer_admin = #{userId}
        </if>
        WHERE t2.id is not null
        <if test="userType == 1">
            and t1.last_customer_admin = #{userId} and t1.renew_state = 'UN_RENEW'
            <if test="startDate !=  null">
                AND t1.last_interruption_time >= str_to_date(#{startDate}, '%Y-%m-%d')
            </if>
            <if test="endDate != null">
                and  <![CDATA[ t1.last_interruption_time < str_to_date(#{endDate}, '%Y-%m-%d')]]>
            </if>
        </if>
        <if test="userType == 2">
            and t1.renew_emp = #{userId} and t1.renew_state = 'RENEW'
            <if test="startDate !=  null">
                AND t1.renew_time >= str_to_date(#{startDate}, '%Y-%m-%d')
            </if>
            <if test="endDate != null">
                and  <![CDATA[ t1.renew_time < str_to_date(#{endDate}, '%Y-%m-%d')]]>
            </if>
        </if>
        <if test='intention != null and intention != ""'>
            <if test = 'intention == "noFollowUp"'>
                and t4.id is null
            </if>
            <if test = 'intention != "noFollowUp"'>
                and t4.intention = #{intention}
            </if>
        </if>
        <if test="productTypes != null and productTypes.size() != 0">
            and
            <foreach collection="productTypes" item = "productType" open = "(" separator="or" close=")">
                JSON_CONTAINS(t1.extend,JSON_OBJECT(#{productType},1))
            </foreach>
        </if>
        <if test='customerName!=null and customerName != ""'>
            and t2.customerName like concat('%',#{customerName},'%')
        </if>
        group by t2.id
        <if test="userType==1">
            ORDER BY t1.last_interruption_time desc
        </if>
        <if test="userType==2">
            ORDER BY t1.renew_time desc
        </if>
    </select>

    <select id="getInterruptionQty" resultType="java.lang.Integer">
        SELECT count(t1.id)
        FROM customer_interruption t1
        LEFT JOIN customer t2 ON t1.customer_id = t2.id
        WHERE t2.id is not null
        <if test="userType == 1">
            and t1.last_customer_admin = #{userId} and t1.renew_state = 'UN_RENEW'
        </if>
        <if test="userType == 2">
            and t1.renew_emp = #{userId} and t1.renew_state = 'RENEW'
        </if>
    </select>

    <select id="listAppPolicyListAll" resultType="com.cfpamf.ms.insur.operation.customer.dto.CustomerPolicyDto">
        SELECT t3.id AS policyId,
        t1.fhOrderId,
        t1.unitPrice*t1.qty AS totalAmount,
        t1.payStatus,
        t3.appStatus,
        t2.personName AS applicantPersonName,
        t3.personName AS insuredPersonName,
        t3.policyNo AS policyNo,
        t4.productName,
        t5.planName,
        t3.appStatus,
        t3.downloadURL,
        t6.amount,
        DATE_FORMAT(t1.startTime ,'%Y-%m-%d') AS startTime,
        DATE_FORMAT(t1.endTime ,'%Y-%m-%d') AS endTime,
        DATE_FORMAT(t1.endTime ,'%Y-%m-%d %H:%i:%s') AS fullEndTime,
        DATE_FORMAT(t1.create_time ,'%Y-%m-%d %T') AS createTime,
        t4.productType as productType,
        t7.name as productTypeName,
        t1.customerAdminId customerAdmin
        FROM sm_order t1
        LEFT JOIN sm_order_applicant t2 ON t1.fhOrderId=t2.fhOrderId
        LEFT JOIN sm_order_insured t3 ON t1.fhOrderId=t3.fhOrderId
        LEFT JOIN sm_product t4 ON t4.id=t1.productId
        LEFT JOIN sm_plan t5 ON t5.id=t1.planId
        left join sm_order_policy t6 on t6.policy_no = t3.policyNo
        and t6.fh_order_id = t1.fhOrderId and t3.appStatus = t6.policy_state
        left join dictionary t7 on t7.code = t4.productType and t7.type ='productGroup'
        WHERE
        <if test="isInsured == false ">
            t2.idNumber = #{idNumber}
        </if>
        <if test="isInsured == true ">
            t3.idNumber = #{idNumber}
        </if>AND t1.payStatus = '2'
        ORDER BY t1.create_time DESC
    </select>

    <select id="listAppPolicyCount" resultType="com.cfpamf.ms.insur.operation.customer.dto.WxCmsSmyDto">
        SELECT count(t1.fhorderid) orderQty,sum(t1.totalAmount) orderAmount
        FROM sm_order t1
        LEFT JOIN sm_order_applicant t2 ON t1.fhOrderId=t2.fhOrderId
        LEFT JOIN sm_order_insured t3 ON t1.fhOrderId=t3.fhOrderId
        LEFT JOIN sm_product t4 ON t4.id=t1.productId
        LEFT JOIN sm_plan t5 ON t5.id=t1.planId
        left join sm_order_policy t6 on t6.policy_no = t3.policyNo
        and t6.fh_order_id = t1.fhOrderId and t3.appStatus = t6.policy_state
        left join dictionary t7 on t7.code = t4.productType and t7.type ='productGroup'
        WHERE
        <if test="isInsured == false ">
            t2.idNumber = #{idNumber}
        </if>
        <if test="isInsured == true ">
            t3.idNumber = #{idNumber}
        </if>AND t1.payStatus = '2'
    </select>

    <select id="InsurOrderInfo" resultType="com.cfpamf.ms.insur.operation.customer.po.CustomerInterruptionPolicyPo">
        select t.customerAdminId
             ,t.totalAmount,t1.idnumber,t1.personName
             ,t1.idType,t1.policyNo,t1.appStatus,t3.renew_state,t3.renew_emp
        from sm_order t
        left join sm_order_insured t1 on t1.fhorderid = t.fhorderid
        left join customer_interruption t3 on t3.id_number = t1.idnumber
        where t3.id is not null and t.fhorderid = #{fhOrderId}
    </select>

    <select id="getIdNumberByCustomerId" resultType="java.lang.String">
        select idNumber from customer where id = #{customerId}
    </select>
    <select id="getLoanIdNumberByCustomerId" resultType="java.lang.String">
        select idNumber from customer where id = #{customerId}
    </select>

    <select id="selectInterruptionFollowByCustomer" resultType="com.cfpamf.ms.insur.operation.customer.dto.CustomerFollowDto">
        select t1.id,customer_id,follow_time,
        case when t1.follow_people = 'system' then '系统' else t2.userName end followPeopleName,
        follow_people,service_mode,intention,'' intent,reason,reason_remark,remark,next_time
        ,'INTERRUPTION' followType
        from customer_interruption_follow t1
        left join auth_user t2 on t2.userId = t1.follow_people and t2.enabled_flag = 0
        where customer_id = #{customerId} and t1.enabled_flag = 0
    </select>
    <select id="selectAiFollowByCustomer" resultType="com.cfpamf.ms.insur.operation.customer.dto.CustomerFollowDto">
        select id,target_id customerId,
        create_time as follow_time,
        '机器人' followPeopleName,'机器人' followPeople,'phone' service_mode,
        intent intention,voice_address,record_json,talking_time_len,'AI_CALL' followType,task_type
        from ai_follow_record where target_id = #{customerId} and task_type ='INTERRUPTION'
    </select>
    <select id="selectLoanFollowByCustomer" resultType="com.cfpamf.ms.insur.operation.customer.dto.CustomerFollowDto">
        select t1.id,customer_id,follow_time,
        case when t1.follow_people = 'system' then '系统' else t2.userName end followPeopleName,
        follow_people,service_mode,intention,reason,reason_remark,remark,next_time,
        'LOAN' followType
        from customer_loan_follow t1
        left join auth_user t2 on t2.userId = t1.follow_people and t2.enabled_flag = 0
        where customer_id = #{customerId} and t1.enabled_flag = 0
    </select>
    <select id="selectLoanFirstFollowByCustomer" resultType="com.cfpamf.ms.insur.operation.customer.dto.CustomerFollowDto">
        select t1.id,customer_id,follow_time,
        case when t1.follow_people = 'system' then '系统' else t2.userName end followPeopleName,
        follow_people,reason,reason_remark,remark,'LOAN_FIRST' followType,intention,follow_state
        from customer_loan_first_follow t1
        left join auth_user t2 on t2.userId = t1.follow_people and t2.enabled_flag = 0
        where customer_id = #{customerId} and t1.enabled_flag = 0
    </select>
    <select id="listCustomerByIdNumberList" resultType="com.cfpamf.ms.insur.operation.customer.dto.WxInterruptionCustomerDto">
        select id as customer_id,customerType,customerName,IdNumber
        from customer where idNumber in
        <foreach collection="customerIdNumberList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="listInterruptionFollowCntByJobNumbers" resultType="com.cfpamf.ms.insur.operation.customer.dto.CustomerFollowCntDto">
        select
            t1.job_number empCode
            ,'INTERRUPTION' bizType
            ,count( DISTINCT(t.`id_number`)) followCnt
            ,t2.userName empName
            ,t2.orgCode bchCode
            ,t2.organizationFullName bchName
        from customer_interruption_follow t
        left join phoenix_emp_todo t1 on t.id_number = t1.target_id and t1.biz_type = 'INTERRUPTION'
        left join auth_user t2 on t1.job_number = t2.userId
        where
            t1.`job_number` in
            <foreach collection="query.empCodes" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
            and t.newest= 1
            <![CDATA[
                and t1.create_time >= DATE_FORMAT(#{query.startDate}, '%Y-%m-01 00:00:00')
                and t1.create_time <= #{query.endDate}
                and follow_time >= #{query.startDate}
                and follow_time <= #{query.endDate}
            ]]>
        group by t1.`job_number`
    </select>
    <select id="listInterruptionConversionByJobNumbers" resultType="com.cfpamf.ms.insur.operation.customer.dto.CustomerFollowCntDto">
        select
            t2.job_number empCode
            ,'INTERRUPTION' bizType
            ,sum(if(t1.id is not null and t.policy_state = '1',`total_amount`, 0)) conversionAmt
            ,count(DISTINCT(t1.`id_number`)) conversionCnt
            ,t3.userName empName
            ,t3.orgCode bchCode
            ,t3.organizationFullName bchName
        from `customer_interruption_policy` t
        left join `customer_interruption_follow` t1 on t.id_number = t1.id_number and t1.newest = 1
        left join `phoenix_emp_todo` t2 on t.id_number = t2.`target_id` and t2.`biz_type` = 'INTERRUPTION'
        left join auth_user t3 on t2.`job_number` = t3.`userId`
        where
            t2.`job_number` in
            <foreach collection="query.empCodes" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
            <![CDATA[
                and t2.create_time >= DATE_FORMAT(#{query.startDate}, '%Y-%m-01 00:00:00')
                and t2.create_time <= #{query.endDate}
                and t.`create_time` >= #{query.startDate}
                and  t.`create_time` <= #{query.endDate}
            ]]>
        group by t2.job_number
    </select>

    <select id="listRenewLongFollowCntByJobNumbers" resultType="com.cfpamf.ms.insur.operation.customer.dto.CustomerFollowCntDto">
        select
            t1.job_number empCode
            ,'RENEW_LONG' bizType
            ,count( DISTINCT t.`policy_no`,t.term_num) followCnt
            ,t2.userName empName
            ,t2.orgCode bchCode
            ,t2.organizationFullName bchName
        from `sm_order_renewal_term_notify_log` t
        left join phoenix_emp_todo t1 on t1.target_id = concat(t.policy_no, '-', t.term_num) and t1.biz_type = 'RENEW_LONG'
        left join auth_user t2 on t1.`job_number` = t2.`userId`
        where
            t1.`job_number` in
            <foreach collection="query.empCodes" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
            and t.newest= 1
            <![CDATA[
                and t1.create_time >= DATE_FORMAT(#{query.startDate}, '%Y-%m-01 00:00:00')
                and t1.create_time <= #{query.endDate}
                and t.`create_time` >= #{query.startDate}
                and  t.`create_time` <= #{query.endDate}
            ]]>
        group by t1.`job_number`
    </select>
    <select id="listRenewLongConversionByJobNumbers" resultType="com.cfpamf.ms.insur.operation.customer.dto.CustomerFollowCntDto">
        select
            t2.`job_number` empCode
            ,'RENEW_LONG' bizType
            ,count( DISTINCT t1.`policy_no`,t1.term_num) conversionCnt
            ,sum(if(t1.id is not null,t.renewal_amount,0)) conversionAmt
            ,t3.userName empName
            ,t3.orgCode bchCode
            ,t3.organizationFullName bchName
        from sm_order_renewal_term t
        left join sm_order_renewal_term_notify_log t1 on t.`policy_no` = t1.`policy_no` and t.`term_num` = t1.`term_num` and t1.`newest` =1
        left join `phoenix_emp_todo` t2 on t2.target_id = concat(t.policy_no, '-', t.term_num) and t2.biz_type = 'RENEW_LONG'
        left join auth_user t3 on t2.`job_number` = t3.`userId`
        where
            t2.`job_number` in
            <foreach collection="query.empCodes" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
            <![CDATA[
                and t2.create_time >= DATE_FORMAT(#{query.startDate}, '%Y-%m-01 00:00:00')
                and t2.create_time <= #{query.endDate}
                and t.`payment_time` >= #{query.startDate}
                and  t.`payment_time` <= #{query.endDate}
            ]]>
        group by t2.`job_number`
    </select>

    <select id="listRenewShortFollowCntByJobNumbers" resultType="com.cfpamf.ms.insur.operation.customer.dto.CustomerFollowCntDto">
        select
            t1.job_number empCode
            ,'RENEW_SHORT' bizType
            ,count( DISTINCT(t.`policy_no`)) followCnt
            ,t2.userName empName
            ,t2.orgCode bchCode
            ,t2.organizationFullName bchName
        from `sm_order_renewal_follow` t
        left join phoenix_emp_todo t1 on t.policy_no = t1.target_id and t1.biz_type = 'RENEW_SHORT'
        left join auth_user t2 on t1.`job_number` = t2.`userId`
        where t1.`job_number` in
        <foreach collection="query.empCodes" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        and t.newest= 1
        <![CDATA[
            and t1.create_time >= DATE_FORMAT(#{query.startDate}, '%Y-%m-01 00:00:00')
            and t1.create_time <= #{query.endDate}
            and follow_time >= #{query.startDate}
            and follow_time <= #{query.endDate}
        ]]>
        group by t1.`job_number`
    </select>
    <select id="listRenewShortConversionByJobNumbers" resultType="com.cfpamf.ms.insur.operation.customer.dto.CustomerFollowCntDto">
        select
            t2.`job_number` empCode
            ,'RENEW_SHORT' bizType
            ,count( DISTINCT(t1.`policy_no`)) conversionCnt
            ,sum(if(t.`ins_status`='renewed', `total_amount` ,0)) conversionAmt
            ,t3.userName empName
            ,t3.orgCode bchCode
            ,t3.organizationFullName bchName
        from `insurance_renewal` t
        left join `sm_order_renewal_follow` t1 on t.`old_policy_no` = t1.`policy_no` and t1.`newest` = 1
        left join `phoenix_emp_todo` t2 on t2.`target_id` = t.`old_policy_no`  and t2.`biz_type` = 'RENEW_SHORT' and t2.`state` = 'DONE'
        left join auth_user t3 on t2.`job_number` = t3.`userId`
        where
            t2.`job_number` in
            <foreach collection="query.empCodes" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
            <![CDATA[
                and t2.create_time >= DATE_FORMAT(#{query.startDate}, '%Y-%m-01 00:00:00')
                and t2.create_time <= #{query.endDate}
                and t2.`finish_time` >= #{query.startDate}
                and t2.`finish_time` <= #{query.endDate}
            ]]>
        group by t2.`job_number`
    </select>
</mapper>
