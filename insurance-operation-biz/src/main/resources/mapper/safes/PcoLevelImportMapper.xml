<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.insur.operation.pco.dao.PcoLevelImportMapper">
    <select id="list" resultType="com.cfpamf.ms.insur.operation.pco.vo.PcoLevelImport">
        select * from pco_level_import
        where 1=1
        <if test="startDate!=null">
            and date(create_Time) >= #{startDate}
        </if>
        <if test="endDate!=null">
            <![CDATA[
                and date(create_Time) <= #{endDate}
            ]]>
        </if>
        order by create_time desc
    </select>
</mapper>