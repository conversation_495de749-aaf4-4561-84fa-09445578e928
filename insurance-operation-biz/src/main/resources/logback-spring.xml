<?xml version="1.0" encoding="UTF-8"?>

<configuration scam="true">
    <!--%d表示日期，%X表示输出所有,:null表示为空时输出null，%level表示日志级别 %thread表示线程名字 %c表示类名 %L表示行号 %n表示换行符-->
    <property name="PATTERN"
              value="%-12(%d{yyyy-MM-dd HH:mm:ss.SSS}) [%X{EagleEye-TraceID}%X{EagleEye-TraceID-Copy}] %level [%thread] %c[%L] %msg%n"/>
    <property name="LOG_FILE_PATH" value="./logs"/>

    <appender name="STDOUT"
              class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>${PATTERN}</pattern>
        </encoder>
    </appender>

    <appender name="INFO_FILE_APPENDER" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${LOG_FILE_PATH}/${hostname}/info.%d{yyyy-MM-dd}.log</fileNamePattern>
            <MaxHistory>31</MaxHistory>
        </rollingPolicy>
        <encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">
            <layout class="ch.qos.logback.classic.PatternLayout">
                <pattern>${PATTERN}</pattern>
            </layout>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>WARN</level>
            <onMatch>DENY</onMatch>
            <onMismatch>NEUTRAL</onMismatch>
        </filter>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>ERROR</level>
            <onMatch>DENY</onMatch>
            <onMismatch>NEUTRAL</onMismatch>
        </filter>
    </appender>

    <appender name="WARN_FILE_APPENDER" class="ch.qos.logback.core.FileAppender">
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>WARN</level>
        </filter>
        <file>${LOG_FILE_PATH}/${hostname}/warn.log</file>
        <append>true</append>
        <encoder>
            <pattern>${PATTERN}</pattern>
        </encoder>
    </appender>
    <root level="INFO">
        <appender-ref ref="INFO_FILE_APPENDER"/>
        <appender-ref ref="WARN_FILE_APPENDER"/>
        <appender-ref ref="STDOUT"/>
    </root>
</configuration>
