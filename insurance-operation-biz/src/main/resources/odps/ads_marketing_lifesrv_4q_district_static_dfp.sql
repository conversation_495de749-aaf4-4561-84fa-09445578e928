--odps sql
--********************************************************************--
--author:廖薇薇
--create time:2024-10-23 11:27:38
--********************************************************************--
--odps sql
--********************************************************************--
--author:廖薇薇
--create time:2024-10-22 17:00:09
--********************************************************************--
alter table ads_marketing_lifesrv_4q_district_static_dfp drop if exists partition (pt='${bizdate}');

INSERT INTO ads_marketing_lifesrv_4q_district_static_dfp partition (pt='${bizdate}')
(
    area_code,
    area_name,
    district_code,
    district_name,
    district_leader_id,
    district_leader_name,
    sy_norm_insurance_amt_target,
    sy_norm_insurance_amt,
    sy_norm_insurance_amt_target_diff,
    sy_norm_insurance_amt_finish_ratio,
    q4_offline_loan_insurance_rate,
    q4_offline_loan_insurance_rate_gt70_rank,
    activity_avg_wine_revenue_amt,
    activity_wine_revenue_amt,
    cur_work_emp_cnt,
    insurance_staff_sprint_reward_remark,
    offline_loan_insurance_rate_reward_remark,
    wine_order_reward_remark
)
select
    area_code,
    area_name,
    district_code,
    district_name,
    district_leader_id,
    district_leader_name,
    cast(sy_norm_insurance_amt_target AS DECIMAL(18,4)) sy_norm_insurance_amt_target,
    cast(COALESCE(sy_norm_insurance_amt, 0) AS DECIMAL(18,4)) sy_norm_insurance_amt,
    cast(COALESCE(sy_norm_insurance_amt_target_diff, 0) AS DECIMAL(18,4)) sy_norm_insurance_amt_target_diff,
    cast(COALESCE(sy_norm_insurance_amt_finish_ratio, 0) AS DECIMAL(18,4)) sy_norm_insurance_amt_finish_ratio,
    cast(COALESCE(q4_offline_loan_insurance_rate, 0) AS DECIMAL(18,4)) q4_offline_loan_insurance_rate,
    cast(q4_offline_loan_insurance_rate_gt70_rank AS INT) q4_offline_loan_insurance_rate_gt70_rank,
    cast(COALESCE(activity_avg_wine_revenue_amt, 0) AS DECIMAL(18,4)) activity_avg_wine_revenue_amt,
    cast(COALESCE(activity_wine_revenue_amt, 0) AS DECIMAL(18,4)) activity_wine_revenue_amt,
    cast(COALESCE(cur_work_emp_cnt, 0) AS INT) cur_work_emp_cnt,
    case when COALESCE(sy_norm_insurance_amt_finish_ratio, 0)*100 >= 100 then '庆功酒一箱'
         else '-'
         end insurance_staff_sprint_reward_remark, --B类业务全员冲刺奖励
    case when q4_offline_loan_insurance_rate_gt70_rank>=1 and q4_offline_loan_insurance_rate_gt70_rank<=5 then '一档奖励'
         when q4_offline_loan_insurance_rate_gt70_rank>=6 and q4_offline_loan_insurance_rate_gt70_rank<=15 then '二档奖励'
         when q4_offline_loan_insurance_rate_gt70_rank>=16 and q4_offline_loan_insurance_rate_gt70_rank<=20 then '三档奖励'
         else '-'
         end offline_loan_insurance_rate_reward_remark, --B类业务-交叉销售比奖励
    case when COALESCE(activity_avg_wine_revenue_amt, 0) >= 30000 then '茶台&手表兼得'
         when COALESCE(activity_avg_wine_revenue_amt, 0) >= 15000 then '茶台&手表二选一'
         else '-'
         end wine_order_reward_remark --酒水全员开单奖励
from cdfinance.ads_marketing_lifesrv_4q_district_static_dfp
where pt = '${bizdate}';