--odps sql
--********************************************************************--
--author:廖薇薇
--create time:2024-10-25 10:18:19
--********************************************************************--
alter table ads_marketing_lifesrv_4q_spvsr_static_dfp drop if exists partition (pt='${bizdate}');

INSERT INTO ads_marketing_lifesrv_4q_spvsr_static_dfp partition (pt='${bizdate}')
(
    area_code,
    area_name,
    district_code,
    district_name,
    bch_code,
    bch_name,
    spvsr_id,
    spvsr_name,
    activity_avg_wine_revenue_amt,
    activity_wine_revenue_amt,
    cur_work_emp_cnt,
    wine_order_reward_remark
)
select
    area_code,
    area_name,
    district_code,
    district_name,
    bch_code,
    bch_name,
    spvsr_id,
    spvsr_name,
    cast(COALESCE(activity_avg_wine_revenue_amt, 0) AS DECIMAL(18,4)) activity_avg_wine_revenue_amt,
    cast(COALESCE(activity_wine_revenue_amt, 0) AS DECIMAL(18,4)) activity_wine_revenue_amt,
    cast(COALESCE(cur_work_emp_cnt,0) as BIGINT) cur_work_emp_cnt,
    case when COALESCE(activity_avg_wine_revenue_amt, 0) >= 30000 then '茶台&手表兼得'
         when COALESCE(activity_avg_wine_revenue_amt, 0) >= 15000 then '茶台&手表二选一'
         else '-'
         end wine_order_reward_remark --酒水全员开单奖励
from cdfinance.ads_marketing_lifesrv_4q_spvsr_static_dfp
where pt = '${bizdate}';