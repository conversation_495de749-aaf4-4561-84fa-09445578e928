-- 删除已存在的分区，如果存在的话
alter table temp_safes_stats_new_year_2024 drop if exists partition (pt='${bizdate}');

-- 使用WITH子句定义一个名为t_all_emp的临时表
WITH t_risk AS
(
    SELECT  fh_order_id
            ,IF(SUM(
                CASE    WHEN risk_code IN ('TBJKLYB23031501','TBJKLYB23031504','ZYCXJXZY23111701','ZYCXJXZY23111702','ZYCXJXZY23111703') THEN 1
                        ELSE 0
                END
            ) > 0,1,0) max_main_insurance
    FROM    cdfinance.stg_safes_sm_order_risk_duty
    WHERE   pt = '${bizdate}'
    GROUP BY fh_order_id
)
,t_dbd as (
    select area_code,area_name,district_code,district_name,bch_code,bch_name from cdfinance.dim_branch_detail_dfp
    where pt = '${bizdate}' and (area_code is not NULL or area_code != '')
    group by area_code,area_name,district_code,district_name,bch_code,bch_name
)
,t_policy AS
(
    SELECT  t1.insurance_order_id
            ,t1.policy_no
            ,t1.insurance_type
            ,t1.bch_code
            ,t1.bch_name
            ,t1.area_code
            ,t1.area_name
            ,t1.district_code
            ,t1.district_name
            ,sum(t1.insurance_amt)
            ,t1.is_activity_order
            ,t1.is_village_promotion_order
            ,t1.insure_cust_id
            ,t1.insurance_term_num
            ,t1.trade_time
            ,t1.insurance_product_id
            ,t1.insurance_product_name
            ,t1.recommend_emp_id
            ,t1.recommend_emp_name
            ,t1.is_loan_flag
            ,t1.is_valid_loan
            ,t1.pt
            ,t2.account_time
    FROM    CDFinance.ads_insurance_policy_index_progress_dfp t1
    LEFT JOIN CDFinance.stg_safes_customer_base_info t3
    ON      t1.insured_cust_id = t3.insur_cust_no
    AND     t3.pt = '${bizdate}'
    LEFT JOIN CDFinance.stg_safes_sm_commission_detail t2
    ON      t2.policy_no = t1.policy_no
    AND     t1.insurance_order_id = t2.order_id
    AND     TOUPPER(COALESCE(t3.id_number,t1.insured_cust_id)) = TOUPPER(t2.insured_id_number)
    AND     t1.insurance_term_num = t2.term_num
    AND     IF(t1.insurance_type = 1,1,4) = t2.policy_status
    AND     t2.pt = '${bizdate}'
    WHERE   t1.pt = '${bizdate}' -- 活动时间
    and t1.is_activity_order = 0 --整村推进=否
    AND     t2.account_time >= '2024-10-01 00:00:00'
    AND     t2.account_time < '2025-01-01'
    AND     (
                t1.insurance_type = 1
                AND     t1.is_loan_flag = 0
                AND     t1.is_valid_loan = 0
                OR      (
                            t1.insurance_type = -1
                            AND     t1.policy_no IN (
                                        SELECT  temp.policy_no
                                        FROM    CDFinance.ads_insurance_policy_index_progress_dfp temp
                                        LEFT JOIN CDFinance.stg_safes_sm_commission_detail temp2
                                        ON      temp2.policy_no = temp.policy_no
                                        AND     temp.insurance_order_id = temp2.order_id
                                        AND     temp.insurance_term_num = temp2.term_num
                                        AND     temp2.policy_status = 1
                                        AND     temp2.pt = '${bizdate}'
                                        WHERE   temp.pt = '${bizdate}' -- 活动时间
                                        AND temp2.account_time >= '2024-10-01 00:00:00'
                                        AND     temp2.account_time < '2025-01-01'
                                        AND     temp.insurance_type = 1
                                        AND     temp.is_loan_flag = 0
                                        AND     temp.is_valid_loan = 0
                                    )
                )
                OR      (
                            t1.insurance_type = -1
                            AND     t1.policy_no IN (
                                        SELECT  temp.policy_no
                                        FROM    CDFinance.ads_insurance_policy_index_progress_temp_dfp temp
                                        LEFT JOIN CDFinance.stg_safes_sm_commission_detail temp2
                                        ON      temp2.policy_no = temp.policy_no
                                        AND     temp.insurance_order_id = temp2.order_id
                                        AND     temp.insurance_term_num = temp2.term_num
                                        AND     temp2.policy_status = 1
                                        AND     temp2.pt = '********'
                                        WHERE   temp.pt = '********' -- 活动时间
                                        AND     temp2.account_time >= '2024-10-01 00:00:00'
                                        AND     temp2.account_time < '2025-01-01'
                                        AND     temp.insurance_type = 1
                                        AND     temp.is_loan_flag = 0
                                    )
                )
    )
    group by t1.insurance_order_id
            ,t1.policy_no
            ,t1.insurance_type
            ,t1.bch_code
            ,t1.bch_name
            ,t1.area_code
            ,t1.area_name
            ,t1.district_code
            ,t1.district_name
            ,t1.is_activity_order
            ,t1.is_village_promotion_order
            ,t1.insure_cust_id
            ,t1.insurance_term_num
            ,t1.trade_time
            ,t1.insurance_product_id
            ,t1.insurance_product_name
            ,t1.recommend_emp_id
            ,t1.recommend_emp_name
            ,t1.is_loan_flag
            ,t1.is_valid_loan
            ,t1.pt
            ,t2.account_time
)
,t_pt as (
    select pt from CDFinance.ads_insurance_bch_index_progress_dfp
    where pt in ('********','********','********') and pt<='${bizdate}'
    group by pt
)
--分支平均客户经理数
,bch_manage_avg_cnt AS  (
    select t1.area_code,t1.area_name,t1.bch_code,t1.bch_name,t1.district_code,
    COALESCE(t2.bch_manage_avg_cnt,0) bch_manage_avg_cnt
    from CDFinance.ads_insurance_bch_index_progress_dfp t1
    left join (select a.bch_code,a.bch_name,max(a.district_code),a.area_code,a.area_name,
                SUM(a.sm_manage_cnt)/(select count(*) from t_pt) bch_manage_avg_cnt
               from CDFinance.ads_insurance_bch_index_progress_dfp a
               where a.pt in ('********','********','********')
               group by  a.bch_code,a.bch_name,a.area_code,a.area_name
            ) t2 on  t1.bch_code=t2.bch_code
    where t1.pt = ${bizdate}
)
--区域平均客户经理数
,area_manage_avg_cnt AS (
    select a.area_code,a.area_name,
    COALESCE(SUM(a.sm_manage_cnt)/count(pt),0) area_manage_avg_cnt
    from CDFinance.ads_insurance_area_index_progress_dfp a
    where a.pt in ('********','********','********')
    group by a.area_code,a.area_name
)
-- 如果10,11,12任意一月的月初为客户经理则认为是月初客户经理
,is_begin_manage as (
    select
        a.emp_name,
        a.emp_id,
        case when sum(a.is_begin_sm_manage) > 0 THEN 1
        ELSE  0  END is_begin_sm_manage
    from CDFinance.ads_insurance_emp_index_progress_dfp a
    where a.pt in ('********','********','********') GROUP by a.emp_name, a.emp_id
)
,t_all_emp AS
(

     -- 选择一系列字段，包括地区名、分支机构名、分支代码等
    SELECT  t1.area_name
            ,t1.area_code
            ,t1.bch_name
            ,t1.bch_code
            ,t1.district_code
            ,t1.district_name
            ,t1.recommend_emp_id
            ,t1.recommend_emp_name
            ,t6.is_begin_sm_manage
            ,SUM(CASE  WHEN  insurance_type = 1 THEN 1
                    ELSE 0
            END) total_insured
            ,SUM(CASE   WHEN  insurance_type = -1 THEN 1
                    ELSE 0
            END) total_surrender
            ,SUM(CASE  WHEN  insurance_type = 1 and insurance_product_id in (1310,1312,1204) THEN 1
                    ELSE 0
            END) activity1_total_insured
            ,SUM(CASE   WHEN  insurance_type = -1 and insurance_product_id in (1310,1312,1204) THEN 1
                    ELSE 0
            END) activity1_total_surrender
            -- 分别统计 7-9 月的单量
            ,SUM(CASE WHEN insurance_type = 1 and SUBSTR(account_time,1,7) ='2024-10'  THEN 1
                    ELSE 0
            END) total_insured_oct
            ,SUM(CASE WHEN insurance_type = -1 and SUBSTR(account_time,1,7) ='2024-10' THEN 1
                    ELSE 0
            END) total_surrender_oct
            ,SUM(CASE WHEN insurance_type = 1 and SUBSTR(account_time,1,7) ='2024-11'  THEN 1
                    ELSE 0
            END) total_insured_nov
            ,SUM(CASE WHEN insurance_type = -1 and SUBSTR(account_time,1,7) ='2024-11' THEN 1
                    ELSE 0
            END) total_surrender_nov
            ,SUM(CASE WHEN insurance_type = 1 and SUBSTR(account_time,1,7) ='2024-12'  THEN 1
                    ELSE 0
            END) total_insured_dec
            ,SUM(CASE WHEN insurance_type = -1 and SUBSTR(account_time,1,7) ='2024-12' THEN 1
                    ELSE 0
            END) total_surrender_dec
             -- 分别统计 7-9 月的单量 鲸喜三星全家福综合保障 产品
            -- ,SUM(CASE WHEN insurance_type = 1 and SUBSTR(account_time,1,7) ='2024-07' and insurance_product_id = '1204'  THEN 1
            --         ELSE 0
            -- END) jingxi_sanxing_quanjiafu_insured_jul
            -- ,SUM(CASE WHEN insurance_type = -1 and SUBSTR(account_time,1,7) ='2024-07' and insurance_product_id = '1204' THEN 1
            --         ELSE 0
            -- END) jingxi_sanxing_quanjiafu_surrender_jul
            -- ,SUM(CASE WHEN insurance_type = 1 and SUBSTR(account_time,1,7) ='2024-08' and insurance_product_id = '1204'  THEN 1
            --         ELSE 0
            -- END) jingxi_sanxing_quanjiafu_insured_aug
            -- ,SUM(CASE WHEN insurance_type = -1 and SUBSTR(account_time,1,7) ='2024-08' and insurance_product_id = '1204' THEN 1
            --         ELSE 0
            -- END) jingxi_sanxing_quanjiafu_surrender_aug
            -- ,SUM(CASE WHEN insurance_type = 1 and SUBSTR(account_time,1,7) ='2024-09' and insurance_product_id = '1204'  THEN 1
            --         ELSE 0
            -- END) jingxi_sanxing_quanjiafu_insured_sept
            -- ,SUM(CASE WHEN insurance_type = -1 and SUBSTR(account_time,1,7) ='2024-09' and insurance_product_id = '1204' THEN 1
            --         ELSE 0
            -- END) jingxi_sanxing_quanjiafu_surrender_sept
            -- 对不同类型的保险产品进行分类统计
            -- 每个COUNT(CASE ...)语句计算一个特定条件下的记录数量
            -- 例如，统计特定类型的保险承保或退保数量
            -- '华农家财险'、'鲸喜众安合家欢综合保障'等是保险产品的名称
            -- 鲸喜华农家庭财产综合保险 承保
            ,COUNT(CASE    WHEN t1.insurance_product_id = '1062' AND insurance_type = 1 THEN 1
                    ELSE NULL
            END) AS huangong_insured -- 鲸喜华农家庭财产综合保险 退保
            ,COUNT(CASE    WHEN t1.insurance_product_id = '1062' AND insurance_type = -1 THEN 1
                    ELSE NULL
            END) AS huangong_surrender -- 鲸喜众安合家欢综合保障 承保
            ,COUNT(
                  CASE    WHEN t1.insurance_product_id = '1036' AND insurance_type = 1 THEN 1
                          ELSE NULL
                  END
            ) AS jingxi_zhongan_hejiahuan_insured -- 鲸喜众安合家欢综合保障 退保
            ,COUNT(
                  CASE    WHEN t1.insurance_product_id = '1036' AND insurance_type = -1 THEN 1
                          ELSE NULL
                  END
            ) AS jingxi_zhongan_hejiahuan_surrender -- 鲸喜中银全家福综合保障 承保
            ,COUNT(
                  CASE    WHEN t1.insurance_product_id = '1163' AND insurance_type = 1 THEN 1
                          ELSE NULL
                  END
            ) AS jingxi_zhongyin_quanjiafu_insured -- 鲸喜中银全家福综合保障 退保
            ,COUNT(
                  CASE    WHEN t1.insurance_product_id = '1163' AND insurance_type = -1 THEN 1
                          ELSE NULL
                  END
            ) AS jingxi_zhongyin_quanjiafu_surrender -- 鲸喜三星全家福综合保障 承保
            ,COUNT(
                  CASE    WHEN t1.insurance_product_id = '1204' AND insurance_type = 1 THEN 1
                          ELSE NULL
                  END
            ) AS jingxi_sanxing_quanjiafu_insured -- 鲸喜三星全家福综合保障 退保
            ,COUNT(
                  CASE    WHEN t1.insurance_product_id = '1204' AND insurance_type = -1 THEN 1
                          ELSE NULL
                  END
            ) AS jingxi_sanxing_quanjiafu_surrender -- 鲸喜中华全家福综合保障 承保
            ,COUNT(
                  CASE    WHEN t1.insurance_product_id = '1194' AND insurance_type = 1 THEN 1
                          ELSE NULL
                  END
            ) AS jingxi_zhonghua_quanjiafu_insured -- 鲸喜中华全家福综合保障 退保
            ,COUNT(
                  CASE    WHEN t1.insurance_product_id = '1194' AND insurance_type = -1 THEN 1
                          ELSE NULL
                  END
            ) AS jingxi_zhonghua_quanjiafu_surrender -- 尊享e生百万医疗慢病版（续保） 承保
            ,COUNT(
                  CASE    WHEN t1.insurance_product_id = '248' AND insurance_type = 1 THEN 1
                          ELSE NULL
                  END
            ) AS zunxiang_eman_slowdisease_renewal_insured -- 尊享e生百万医疗慢病版（续保） 退保
            ,COUNT(
                  CASE    WHEN t1.insurance_product_id = '248' AND insurance_type = -1 THEN 1
                          ELSE NULL
                  END
            ) AS zunxiang_eman_slowdisease_renewal_surrender -- 尊享e生百万医疗2022版（小鲸） 承保
            ,COUNT(
                  CASE    WHEN t1.insurance_product_id = '316' AND insurance_type = 1 THEN 1
                          ELSE NULL
                  END
            ) AS zunxiang_eman_2022_xiaojing_insured -- 尊享e生百万医疗2022版（小鲸） 退保
            ,COUNT(
                  CASE    WHEN t1.insurance_product_id = '316' AND insurance_type = -1 THEN 1
                          ELSE NULL
                  END
            ) AS zunxiang_eman_2022_xiaojing_surrender -- 尊享e生百万医疗2021版（续保） 承保
            ,COUNT(
                  CASE    WHEN t1.insurance_product_id = '245' AND insurance_type = 1 THEN 1
                          ELSE NULL
                  END
            ) AS zunxiang_eman_2021_renewal_insured -- 尊享e生百万医疗2021版（续保） 退保
            ,COUNT(
                  CASE    WHEN t1.insurance_product_id = '245' AND insurance_type = -1 THEN 1
                          ELSE NULL
                  END
            ) AS zunxiang_eman_2021_renewal_surrender -- 尊享e生百万医疗2021版（含加油包） 承保
            ,COUNT(
                  CASE    WHEN t1.insurance_product_id = '267' AND insurance_type = 1 THEN 1
                          ELSE NULL
                  END
            ) AS zunxiang_eman_2021_with_fuel_insured -- 尊享e生百万医疗2021版（含加油包） 退保
            ,COUNT(
                  CASE    WHEN t1.insurance_product_id = '267' AND insurance_type = -1 THEN 1
                          ELSE NULL
                  END
            ) AS zunxiang_eman_2021_with_fuel_surrender -- 尊享e生百万医疗2021版 承保
            ,COUNT(
                  CASE    WHEN t1.insurance_product_id = '158' AND insurance_type = 1 THEN 1
                          ELSE NULL
                  END
            ) AS zunxiang_eman_2021_insured -- 尊享e生百万医疗2021版 退保
            ,COUNT(
                  CASE    WHEN t1.insurance_product_id = '158' AND insurance_type = -1 THEN 1
                          ELSE NULL
                  END
            ) AS zunxiang_eman_2021_surrender -- 尊享e生2023版（续保专用） 承保
            ,COUNT(
                  CASE    WHEN t1.insurance_product_id = '538' AND insurance_type = 1 THEN 1
                          ELSE NULL
                  END
            ) AS zunxiang_eman_2023_renewal_only_insured -- 尊享e生2023版（续保专用） 退保
            ,COUNT(
                  CASE    WHEN t1.insurance_product_id = '538' AND insurance_type = -1 THEN 1
                          ELSE NULL
                  END
            ) AS zunxiang_eman_2023_renewal_only_surrender -- 尊享e生2023（小鲸）（续保） 承保
            ,COUNT(
                  CASE    WHEN t1.insurance_product_id = '1160' AND insurance_type = 1 THEN 1
                          ELSE NULL
                  END
            ) AS zunxiang_eman_2023_xiaojing_renewal_insured -- 尊享e生2023（小鲸）（续保） 退保
            ,COUNT(
                  CASE    WHEN t1.insurance_product_id = '1160' AND insurance_type = -1 THEN 1
                          ELSE NULL
                  END
            ) AS zunxiang_eman_2023_xiaojing_renewal_surrender -- 尊享e生2023（小鲸） 承保
            ,COUNT(
                  CASE    WHEN t1.insurance_product_id = '675' AND insurance_type = 1 THEN 1
                          ELSE NULL
                  END
            ) AS zunxiang_eman_2023_xiaojing_insured -- 尊享e生2023（小鲸） 退保
            ,COUNT(
                  CASE    WHEN t1.insurance_product_id = '675' AND insurance_type = -1 THEN 1
                          ELSE NULL
                  END
            ) AS zunxiang_eman_2023_xiaojing_surrender -- 众安在线尊享e生百万医疗保险2022版（续保）（小鲸） 承保
            ,COUNT(
                  CASE    WHEN t1.insurance_product_id = '896' AND insurance_type = 1 THEN 1
                          ELSE NULL
                  END
            ) AS zhongan_online_zunxiang_eman_2022_renewal_xiaojing_insured -- 众安在线尊享e生百万医疗保险2022版（续保）（小鲸） 退保
            ,COUNT(
                  CASE    WHEN t1.insurance_product_id = '896' AND insurance_type = -1 THEN 1
                          ELSE NULL
                  END
            ) AS zhongan_online_zunxiang_eman_2022_renewal_xiaojing_surrender -- 泰康百万医疗险2022续保方案（小鲸） 承保
            ,COUNT(
                  CASE    WHEN t1.insurance_product_id = '895' AND insurance_type = 1 THEN 1
                          ELSE NULL
                  END
            ) AS taikang_baowan_2022_renewal_xiaojing_insured -- 泰康百万医疗险2022续保方案（小鲸） 退保
            ,COUNT(
                  CASE    WHEN t1.insurance_product_id = '895' AND insurance_type = -1 THEN 1
                          ELSE NULL
                  END
            ) AS taikang_baowan_2022_renewal_xiaojing_surrender -- 泰康百万医疗2022版（小鲸） 承保
            ,COUNT(
                  CASE    WHEN t1.insurance_product_id = '309' AND insurance_type = 1 THEN 1
                          ELSE NULL
                  END
            ) AS taikang_baowan_2022_xiaojing_insured -- 泰康百万医疗2022版（小鲸） 退保
            ,COUNT(
                  CASE    WHEN t1.insurance_product_id = '309' AND insurance_type = -1 THEN 1
                          ELSE NULL
                  END
            ) AS taikang_baowan_2022_xiaojing_surrender -- 蓝医保长期医疗保险 承保
            ,COUNT(
                  CASE    WHEN t1.insurance_product_id = '743' AND insurance_type = 1 THEN 1
                          ELSE NULL
                  END
            ) AS lanyibao_longterm_medical_insured -- 蓝医保长期医疗保险 退保
            ,COUNT(
                  CASE    WHEN t1.insurance_product_id = '743' AND insurance_type = -1 THEN 1
                          ELSE NULL
                  END
            ) AS lanyibao_longterm_medical_surrender -- 众安癌症疾病险2024版 承保
            ,COUNT(
                  CASE    WHEN t1.insurance_product_id = '1167' AND insurance_type = 1 THEN 1
                          ELSE NULL
                  END
            ) AS zhongan_cancer_2024_insured -- 众安癌症疾病险2024版 退保
            ,COUNT(
                  CASE    WHEN t1.insurance_product_id = '1167' AND insurance_type = -1 THEN 1
                          ELSE NULL
                  END
            ) AS zhongan_cancer_2024_surrender -- 国泰癌症疾病险2024版 承保
            ,COUNT(
                  CASE    WHEN t1.insurance_product_id = '1181' AND insurance_type = 1 THEN 1
                          ELSE NULL
                  END
            ) AS guotai_cancer_2024_insured -- 国泰癌症疾病险2024版 退保
            ,COUNT(
                  CASE    WHEN t1.insurance_product_id = '1181' AND insurance_type = -1 THEN 1
                          ELSE NULL
                  END
            ) AS guotai_cancer_2024_surrender
            ,COUNT(--'中和泰康百万医疗2024版'
                  CASE    WHEN t1.insurance_product_id = '1192' AND insurance_type = 1 THEN 1
                          ELSE NULL
                  END
            ) AS zhonghe_taikang_baiwan_insured -- 中和泰康百万医疗2024版退保
            ,COUNT(
                  CASE    WHEN t1.insurance_product_id = '1192' AND insurance_type = -1 THEN 1
                          ELSE NULL
                  END
            ) AS zhonghe_taikang_baiwan_surrender
            ,COUNT(--'泰康百万医疗2024续保版'
                  CASE    WHEN t1.insurance_product_id = '1179' AND insurance_type = 1 THEN 1
                          ELSE NULL
                  END
            ) AS taikang_baiwan_2024_renewal_insured -- 泰康百万医疗2024续保版退保
            ,COUNT(
                  CASE    WHEN t1.insurance_product_id = '1179' AND insurance_type = -1 THEN 1
                          ELSE NULL
                  END
            ) AS taikang_baiwan_2024_renewal_surrender
            --众安癌症疾病险2024版（续保）
            ,COUNT(CASE WHEN insurance_type = 1 and insurance_product_id = '1312'  THEN 1
                    ELSE NULL
            END) zhongan_cancer_xb_insured
            ,COUNT(CASE WHEN insurance_type = -1 and insurance_product_id = '1312' THEN 1
                    ELSE NULL
            END) zhongan_cancer_xb_surrender
            --鲸喜人保全家福综合保障
            ,COUNT(CASE WHEN insurance_type = 1  and insurance_product_id = '1310'  THEN 1
                    ELSE NULL
            END) jingxi_renbao_quanjiafu_insured
            ,COUNT(CASE WHEN insurance_type = -1 and insurance_product_id = '1310' THEN 1
                    ELSE NULL
            END
            ) jingxi_renbao_quanjiafu_surrender
            --蓝医保(续保)
            ,COUNT(CASE WHEN insurance_type = 1  and insurance_product_id = '1261'  THEN 1
                    ELSE NULL
            END) lanyibao_xb_insured
            ,COUNT(CASE WHEN insurance_type = -1 and insurance_product_id = '1261' THEN 1
                    ELSE NULL
            END
            ) lanyibao_xb_surrender
    FROM    t_policy t1
    left join t_risk t5 on t1.insurance_order_id = t5.fh_order_id
    -- 确定 1 月初是不是客户经理
    left join  is_begin_manage
     t6 on t6.emp_id = t1.recommend_emp_id
    WHERE   t1.pt = '${bizdate}'
    -- 活动产品
    AND   (
        -- 产品名字必须是活动产品
          t1.insurance_product_id IN ('1062','1036','1204','1194','248','316','245','267','158','538','1160','675','896','895','309','1167','1181','1192','1179','1312','1310')
    -- 蓝医保不太一样
    or  (t1.insurance_product_id in ('743','1163','1261') and t5.max_main_insurance = 1)
    )
    GROUP BY t1.area_name
             ,t1.area_code
             ,t1.bch_name
             ,t1.bch_code
             ,t1.recommend_emp_id
             ,t1.recommend_emp_name
             ,t1.district_code
             ,t1.district_name
             ,t6.is_begin_sm_manage
)
-- 插入语句 注释掉就是 查询
INSERT INTO temp_safes_stats_new_year_2024 partition (pt='${bizdate}')
(
area_name,area_code, bch_name, bch_code
,district_code,district_name
,recommend_emp_id, recommend_emp_name
,bch_manage_avg_cnt, area_manage_avg_cnt, total_insured, total_surrender
,total_insured_oct,total_surrender_oct
,total_insured_nov,total_surrender_nov
,total_insured_dec,total_surrender_dec
,huangong_insured, huangong_surrender, jingxi_zhongan_hejiahuan_insured, jingxi_zhongan_hejiahuan_surrender
, jingxi_zhongyin_quanjiafu_insured, jingxi_zhongyin_quanjiafu_surrender, jingxi_sanxing_quanjiafu_insured
, jingxi_sanxing_quanjiafu_surrender, jingxi_zhonghua_quanjiafu_insured, jingxi_zhonghua_quanjiafu_surrender
, zunxiang_eman_slowdisease_renewal_insured, zunxiang_eman_slowdisease_renewal_surrender, zunxiang_eman_2022_xiaojing_insured
, zunxiang_eman_2022_xiaojing_surrender, zunxiang_eman_2021_renewal_insured, zunxiang_eman_2021_renewal_surrender
, zunxiang_eman_2021_with_fuel_insured, zunxiang_eman_2021_with_fuel_surrender, zunxiang_eman_2021_insured
, zunxiang_eman_2021_surrender, zunxiang_eman_2023_renewal_only_insured, zunxiang_eman_2023_renewal_only_surrender
, zunxiang_eman_2023_xiaojing_renewal_insured, zunxiang_eman_2023_xiaojing_renewal_surrender, zunxiang_eman_2023_xiaojing_insured
, zunxiang_eman_2023_xiaojing_surrender, zhongan_online_zunxiang_eman_2022_renewal_xiaojing_insured
, zhongan_online_zunxiang_eman_2022_renewal_xiaojing_surrender, taikang_baowan_2022_renewal_xiaojing_insured
, taikang_baowan_2022_renewal_xiaojing_surrender, taikang_baowan_2022_xiaojing_insured, taikang_baowan_2022_xiaojing_surrender
, lanyibao_longterm_medical_insured, lanyibao_longterm_medical_surrender, zhongan_cancer_2024_insured, zhongan_cancer_2024_surrender
, guotai_cancer_2024_insured, guotai_cancer_2024_surrender
,zhonghe_taikang_baiwan_insured,zhonghe_taikang_baiwan_surrender,taikang_baiwan_2024_renewal_insured,taikang_baiwan_2024_renewal_surrender
-- ,jingxi_sanxing_quanjiafu_insured_jul,jingxi_sanxing_quanjiafu_surrender_jul,jingxi_sanxing_quanjiafu_insured_aug
-- ,jingxi_sanxing_quanjiafu_surrender_aug,jingxi_sanxing_quanjiafu_insured_sept,jingxi_sanxing_quanjiafu_surrender_sept
,is_jan_sm_manage
--“鲸耕细作”活动升级增加字段
,activity1_total_insured --活动一总承保单量
,activity1_total_surrender --活动一总退保单量
,zhongan_cancer_xb_insured --众安癌症疾病险2024版（续保）承保
,zhongan_cancer_xb_surrender --众安癌症疾病险2024版（续保）退保
,jingxi_renbao_quanjiafu_insured --鲸喜人保全家福综合保障 承保
,jingxi_renbao_quanjiafu_surrender --鲸喜人保全家福综合保障 退保
,lanyibao_xb_insured --蓝医保（续保） 承保
,lanyibao_xb_surrender --蓝医保（续保） 退保
)
SELECT
COALESCE(t1.area_name,t.area_name) area_name,
COALESCE(t1.area_code,t.area_code) area_code,
COALESCE(t1.bch_name,t.bch_name) bch_name,
COALESCE(t1.bch_code,t.bch_code) bch_code,
dbd.district_code,dbd.district_name,
t.emp_id, t.emp_name,
 -- 机构月初客户经理数
 t2.bch_manage_avg_cnt ,
--  区域月初客户经理数
 t3.area_manage_avg_cnt ,
 COALESCE(total_insured,0) total_insured,
 COALESCE(total_surrender,0) total_surrender,
 COALESCE(total_insured_oct,0) total_insured_oct,
 COALESCE(total_surrender_oct,0) total_surrender_oct,
 COALESCE(total_insured_nov,0) total_insured_nov,
 COALESCE(total_surrender_nov,0) total_surrender_nov,
 COALESCE(total_insured_dec,0) total_insured_dec,
 COALESCE(total_surrender_dec,0) total_surrender_dec,
 COALESCE(huangong_insured,0) huangong_insured,
 COALESCE(huangong_surrender,0) huangong_surrender,
 COALESCE(jingxi_zhongan_hejiahuan_insured,0) jingxi_zhongan_hejiahuan_insured,
 COALESCE(jingxi_zhongan_hejiahuan_surrender,0) jingxi_zhongan_hejiahuan_surrender,
 COALESCE(jingxi_zhongyin_quanjiafu_insured,0) jingxi_zhongyin_quanjiafu_insured,
 COALESCE(jingxi_zhongyin_quanjiafu_surrender,0) jingxi_zhongyin_quanjiafu_surrender,
 COALESCE(jingxi_sanxing_quanjiafu_insured,0) jingxi_sanxing_quanjiafu_insured,
 COALESCE(jingxi_sanxing_quanjiafu_surrender,0) jingxi_sanxing_quanjiafu_surrender,
 COALESCE(jingxi_zhonghua_quanjiafu_insured,0) jingxi_zhonghua_quanjiafu_insured,
 COALESCE(jingxi_zhonghua_quanjiafu_surrender,0) jingxi_zhonghua_quanjiafu_surrender,
 COALESCE(zunxiang_eman_slowdisease_renewal_insured,0) zunxiang_eman_slowdisease_renewal_insured,
 COALESCE(zunxiang_eman_slowdisease_renewal_surrender,0) zunxiang_eman_slowdisease_renewal_surrender,
 COALESCE(zunxiang_eman_2022_xiaojing_insured,0) zunxiang_eman_2022_xiaojing_insured,
 COALESCE(zunxiang_eman_2022_xiaojing_surrender,0) zunxiang_eman_2022_xiaojing_surrender,
 COALESCE(zunxiang_eman_2021_renewal_insured,0) zunxiang_eman_2021_renewal_insured,
 COALESCE(zunxiang_eman_2021_renewal_surrender,0) zunxiang_eman_2021_renewal_surrender,
 COALESCE(zunxiang_eman_2021_with_fuel_insured,0) zunxiang_eman_2021_with_fuel_insured,
 COALESCE(zunxiang_eman_2021_with_fuel_surrender,0) zunxiang_eman_2021_with_fuel_surrender,
 COALESCE(zunxiang_eman_2021_insured,0) zunxiang_eman_2021_insured,
 COALESCE(zunxiang_eman_2021_surrender,0) zunxiang_eman_2021_surrender,
 COALESCE(zunxiang_eman_2023_renewal_only_insured,0) zunxiang_eman_2023_renewal_only_insured,
 COALESCE(zunxiang_eman_2023_renewal_only_surrender,0) zunxiang_eman_2023_renewal_only_surrender,
 COALESCE(zunxiang_eman_2023_xiaojing_renewal_insured,0) zunxiang_eman_2023_xiaojing_renewal_insured,
 COALESCE(zunxiang_eman_2023_xiaojing_renewal_surrender,0) zunxiang_eman_2023_xiaojing_renewal_surrender,
 COALESCE(zunxiang_eman_2023_xiaojing_insured,0) zunxiang_eman_2023_xiaojing_insured,
 COALESCE(zunxiang_eman_2023_xiaojing_surrender,0) zunxiang_eman_2023_xiaojing_surrender,
 COALESCE(zhongan_online_zunxiang_eman_2022_renewal_xiaojing_insured,0) zhongan_online_zunxiang_eman_2022_renewal_xiaojing_insured,
 COALESCE(zhongan_online_zunxiang_eman_2022_renewal_xiaojing_surrender,0) zhongan_online_zunxiang_eman_2022_renewal_xiaojing_surrender,
 COALESCE(taikang_baowan_2022_renewal_xiaojing_insured,0) taikang_baowan_2022_renewal_xiaojing_insured,
 COALESCE(taikang_baowan_2022_renewal_xiaojing_surrender,0) taikang_baowan_2022_renewal_xiaojing_surrender,
 COALESCE(taikang_baowan_2022_xiaojing_insured,0) taikang_baowan_2022_xiaojing_insured,
 COALESCE(taikang_baowan_2022_xiaojing_surrender,0) taikang_baowan_2022_xiaojing_surrender,
 COALESCE(lanyibao_longterm_medical_insured,0) lanyibao_longterm_medical_insured,
 COALESCE(lanyibao_longterm_medical_surrender,0) lanyibao_longterm_medical_surrender,
 COALESCE(zhongan_cancer_2024_insured,0) zhongan_cancer_2024_insured,
 COALESCE(zhongan_cancer_2024_surrender,0) zhongan_cancer_2024_surrender,
 COALESCE(guotai_cancer_2024_insured,0) guotai_cancer_2024_insured,
 COALESCE(guotai_cancer_2024_surrender,0) guotai_cancer_2024_surrender,
 COALESCE(zhonghe_taikang_baiwan_insured,0) zhonghe_taikang_baiwan_insured,
 COALESCE(zhonghe_taikang_baiwan_surrender,0) zhonghe_taikang_baiwan_surrender,
 COALESCE(taikang_baiwan_2024_renewal_insured,0) taikang_baiwan_2024_renewal_insured,
 COALESCE(taikang_baiwan_2024_renewal_surrender,0) taikang_baiwan_2024_renewal_surrender,
--  COALESCE(jingxi_sanxing_quanjiafu_insured_jul,0) jingxi_sanxing_quanjiafu_insured_jul,
--  COALESCE(jingxi_sanxing_quanjiafu_surrender_jul,0) jingxi_sanxing_quanjiafu_surrender_jul,
--  COALESCE(jingxi_sanxing_quanjiafu_insured_aug,0) jingxi_sanxing_quanjiafu_insured_aug,
--  COALESCE(jingxi_sanxing_quanjiafu_surrender_aug,0) jingxi_sanxing_quanjiafu_surrender_aug,
--  COALESCE(jingxi_sanxing_quanjiafu_insured_sept,0) jingxi_sanxing_quanjiafu_insured_sept,
--  COALESCE(jingxi_sanxing_quanjiafu_surrender_sept,0) jingxi_sanxing_quanjiafu_surrender_sept,
 COALESCE(t4.is_begin_sm_manage,0)  is_begin_sm_manage,
 COALESCE(activity1_total_insured,0) activity1_total_insured,--活动一总承保单量,
 COALESCE(activity1_total_surrender,0) activity1_total_surrender, --活动一总退保单量,
 COALESCE(zhongan_cancer_xb_insured,0) zhongan_cancer_xb_insured,--众安癌症疾病险2024版（续保）承保,
 COALESCE(zhongan_cancer_xb_surrender,0) zhongan_cancer_xb_surrender,--众安癌症疾病险2024版（续保）退保,
 COALESCE(jingxi_renbao_quanjiafu_insured,0) jingxi_renbao_quanjiafu_insured, --鲸喜人保全家福综合保障 承保,
 COALESCE(jingxi_renbao_quanjiafu_surrender,0) jingxi_renbao_quanjiafu_surrender,--鲸喜人保全家福综合保障 退保
 COALESCE(lanyibao_xb_insured,0) lanyibao_xb_insured, --蓝医保（续保） 承保
 COALESCE(lanyibao_xb_surrender,0) lanyibao_xb_surrender --蓝医保（续保） 退保
FROM CDFinance.ads_insurance_emp_index_progress_dfp t
left join t_all_emp t1 on t.emp_name = t1.recommend_emp_name
and t.emp_id = t1.recommend_emp_id-- 这里取 1 月初的数据 固定 1 月 1 号分区
-- 客户经理数
LEFT JOIN  bch_manage_avg_cnt t2
ON      COALESCE(t1.bch_code,t.bch_code) = t2.bch_code
-- 客户经理数
LEFT JOIN area_manage_avg_cnt t3
ON      COALESCE(t1.area_name,t.area_name) = t3.area_name
-- 是否客户经理
left join is_begin_manage t4
on t4.emp_name = t.emp_name and t4.emp_id = t.emp_id
--片区信息
left join t_dbd dbd on dbd.bch_code = t.bch_code
where
t.area_name = '甘肃区域'--四季度活动只统计甘肃区域的数据
and t.pt = '${bizdate}'
;
