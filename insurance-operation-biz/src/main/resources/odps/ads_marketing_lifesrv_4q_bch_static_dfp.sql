--odps sql
--********************************************************************--
--author:廖薇薇
--create time:2024-10-23 17:11:58
--********************************************************************
alter table ads_marketing_lifesrv_4q_bch_static_dfp drop if exists partition (pt='${bizdate}');

INSERT INTO ads_marketing_lifesrv_4q_bch_static_dfp partition (pt='${bizdate}')
(
    area_code,
    area_name,
    district_code,
    district_name,
    bch_code,
    bch_name,
    bch_leader_id,
    bch_leader_name,
    sy_norm_insurance_amt_target,--年度标保目标
    sy_norm_insurance_amt,--年度标保
    sy_norm_insurance_amt_target_diff,--年度标保差值
    sy_norm_insurance_amt_finish_ratio,--年度标保完成率
    sy_grant_amount,--年度保险推广费
    sy_avg_emp_norm_insurance_amt,--当年的月人均标保
    sy_wine_revenue_amt_aim,--年度酒水营收目标
    sy_wine_revenue_amt,--年度酒水营收
    sy_wine_revenue_amt_aim_diff,--年度酒水营收差值
    sq_acm_wine_revenue_amt_ratio,--年度酒水营收完成率
    sy_wine_ysh_valid_box_num,--年度回款箱数（映山红）
    sy_wine_spzc_valid_box_num,--年度回款箱数（尚品臻陈）
    activity_wine_ysh_valid_box_num,--活动期间回款箱数（映山红）
    activity_wine_spzc_valid_box_num,--活动期间回款箱数（尚品臻陈）
    activity_avg_wine_revenue_amt,--酒水活动期间人均回款
    activity_wine_revenue_amt,--酒水活动期间回款金额
    cur_work_emp_cnt,--当前在职员工数（剔除外部员工）
    insurance_staff_sprint_reward_remark,--B类业务-全员冲刺奖励
    insurance_incremental_bonus,--B类业务-增量奖金
    insurance_incremental_reward_remark,--B类业务-增量奖励
    insurance_achievement_reward_remark,--B类业务-机构达标奖励
    wine_order_reward_remark,--酒水-全员开单奖励
    wine_achievement_reward_remark_1118,--酒水-1118达成奖励
    sy_wine_ysh_valid_box_interval,--年度回款箱数区间（映山红）
    wine_ysh_emp_amt_reward,--酒水-映山红动销奖励员工金额
    sy_wine_spzc_valid_box_interval,--酒水-年度回款箱数区间（尚品臻陈）
    wine_spzc_emp_amt_reward,--酒水-尚品臻陈动销奖励员工金额
    wine_spzc_director_amt_reward,--酒水-尚品臻陈动销奖励主任金额
    wine_spzc_box_num_reward,--酒水-尚品臻陈动销赠酒奖励(箱数)
    activity_wine_spzc_reward_type--酒水尚品臻陈奖励方案:A/B
)
select
    area_code,
    area_name,
    district_code,
    district_name,
    bch_code,
    bch_name,
    bch_leader_id,
    bch_leader_name,
    cast(sy_norm_insurance_amt_target AS DECIMAL(18,4)) sy_norm_insurance_amt_target,
    cast(COALESCE(sy_norm_insurance_amt, 0) AS DECIMAL(18,4)) sy_norm_insurance_amt,
    cast(COALESCE(sy_norm_insurance_amt_target_diff, 0) AS DECIMAL(18,4)) sy_norm_insurance_amt_target_diff,
    cast(COALESCE(sy_norm_insurance_amt_finish_ratio, 0) AS DECIMAL(18,4)) sy_norm_insurance_amt_finish_ratio,
    cast(COALESCE(sy_grant_amount, 0) AS DECIMAL(18,4)) sy_grant_amount,
    cast(COALESCE(sy_avg_emp_norm_insurance_amt, 0) AS DECIMAL(18,4)) sy_avg_emp_norm_insurance_amt,
    cast(sy_wine_revenue_amt_aim AS DECIMAL(18,4)) sy_wine_revenue_amt_aim,
    cast(COALESCE(sy_wine_revenue_amt, 0) AS DECIMAL(18,4)) sy_wine_revenue_amt,
    cast(COALESCE(sy_wine_revenue_amt, 0) - COALESCE(sy_wine_revenue_amt_aim,0) AS DECIMAL(18,4)) sy_wine_revenue_amt_aim_diff,
    cast(COALESCE(sq_acm_wine_revenue_amt_ratio, 0) AS DECIMAL(18,4)) sq_acm_wine_revenue_amt_ratio,
    cast(COALESCE(sy_wine_ysh_valid_box_num, 0) AS DECIMAL(18,4)) sy_wine_ysh_valid_box_num,
    cast(COALESCE(sy_wine_spzc_valid_box_num, 0) AS DECIMAL(18,4)) sy_wine_spzc_valid_box_num,
    cast(COALESCE(activity_wine_ysh_valid_box_num, 0) AS DECIMAL(18,4)) activity_wine_ysh_valid_box_num,
    cast(COALESCE(activity_wine_spzc_valid_box_num, 0) AS DECIMAL(18,4)) activity_wine_spzc_valid_box_num,
    cast(COALESCE(activity_avg_wine_revenue_amt, 0) AS DECIMAL(18,4)) activity_avg_wine_revenue_amt,
    cast(COALESCE(activity_wine_revenue_amt,0) as DECIMAL(18,4)) activity_wine_revenue_amt,
    cast(COALESCE(cur_work_emp_cnt,0) as INT) cur_work_emp_cnt,
    case when COALESCE(sy_norm_insurance_amt_target,0)>100000 and COALESCE(sy_norm_insurance_amt_finish_ratio, 0)*100 >= 100 then '庆功酒一箱'
         when COALESCE(sy_norm_insurance_amt_target,0)<=100000 and COALESCE(sy_avg_emp_norm_insurance_amt,0)>=1000 then '庆功酒一箱'
         else '-'
         end insurance_staff_sprint_reward_remark, --B类业务全员冲刺奖励
    case when ((COALESCE(sy_norm_insurance_amt_target,0)>100000 or (COALESCE(sy_norm_insurance_amt_target,0)<=100000 and COALESCE(sy_avg_emp_norm_insurance_amt,0)>=1000)))
         then
            case when COALESCE(sy_norm_insurance_amt_finish_ratio, 0)*100 > 100 and COALESCE(sy_norm_insurance_amt_finish_ratio, 0)*100 < 110 then cast(COALESCE(sy_norm_insurance_amt_target_diff,0)*5/100 as DECIMAL(18,2))
                when COALESCE(sy_norm_insurance_amt_finish_ratio, 0)*100 >= 110 then cast(COALESCE(sy_norm_insurance_amt_target_diff,0)*7/100 as DECIMAL(18,2))
                else '-'
                END
         else '-'
         end insurance_incremental_bonus, --B类业务-增量奖金
    case when ((COALESCE(sy_norm_insurance_amt_target,0)>100000 or (COALESCE(sy_norm_insurance_amt_target,0)<=100000 and COALESCE(sy_avg_emp_norm_insurance_amt,0)>=1000)))
         then
            case when COALESCE(sy_norm_insurance_amt_finish_ratio, 0)*100 > 100 and COALESCE(sy_norm_insurance_amt_finish_ratio, 0)*100 < 110 then '尚品/臻陈10箱'
                when COALESCE(sy_norm_insurance_amt_finish_ratio, 0)*100 >= 110 and COALESCE(sy_norm_insurance_amt_finish_ratio, 0)*100 < 120 then '尚品/臻陈15箱'
                when COALESCE(sy_norm_insurance_amt_finish_ratio, 0)*100 >= 120 then '尚品/臻陈20箱'
                else '-'
                end
         else '-'
         end insurance_incremental_reward_remark, --B类业务-增量奖励
    case when COALESCE(sy_norm_insurance_amt_finish_ratio, 0)*100 >= 100 then cast(COALESCE(sy_grant_amount,0)*7/100 as DECIMAL(18,2))
         else '-'
         end insurance_achievement_reward_remark, --B类业务-机构达标奖励
    case when COALESCE(activity_avg_wine_revenue_amt, 0) >= 30000 then '茶台&手表兼得'
         when COALESCE(activity_avg_wine_revenue_amt, 0) >= 15000 then '茶台&手表二选一'
         else '-'
         end wine_order_reward_remark, --酒水全员开单奖励
    case when '${bizdate}' <= '20241118' and COALESCE(sq_acm_wine_revenue_amt_ratio, 0)*100 >= 100 then '庆功酒人均一箱'
         else '-'
         end wine_achievement_reward_remark_1118, --酒水-1118达成奖励
    case when COALESCE(sy_wine_ysh_valid_box_num,0) < 100 then '(0,100)'
         when COALESCE(sy_wine_ysh_valid_box_num,0) >= 100 and COALESCE(sy_wine_ysh_valid_box_num,0) < 300 then '[100,300)'
         when COALESCE(sy_wine_ysh_valid_box_num,0) >= 300 then '[300,∞)'
         else '-'
         end sy_wine_ysh_valid_box_interval,--酒水-年度回款箱数区间（映山红）
    case when COALESCE(sy_wine_ysh_valid_box_num,0) < 100 then CAST(COALESCE(activity_wine_ysh_valid_box_num,0)*10 as DECIMAL(18,2))
         when COALESCE(sy_wine_ysh_valid_box_num,0) >= 100 and COALESCE(sy_wine_ysh_valid_box_num,0) < 300 then CAST(COALESCE(activity_wine_ysh_valid_box_num,0)*20 as DECIMAL(18,2))
         when COALESCE(sy_wine_ysh_valid_box_num,0) >= 300 then CAST(COALESCE(activity_wine_ysh_valid_box_num,0)*30 as DECIMAL(18,2))
         else '-'
         end wine_ysh_emp_amt_reward,--酒水-映山红动销奖励员工金额
    case when COALESCE(sy_wine_spzc_valid_box_num,0) >= 100 and COALESCE(sy_wine_spzc_valid_box_num,0)<300 then '[100,300)'
         when COALESCE(sy_wine_spzc_valid_box_num,0) >= 300 and COALESCE(sy_wine_spzc_valid_box_num,0) < 1000 then '[300,1000)'
         when COALESCE(sy_wine_spzc_valid_box_num,0) >= 1000 and COALESCE(sy_wine_spzc_valid_box_num,0) < 1500 then '[1000,1500)'
         when COALESCE(sy_wine_spzc_valid_box_num,0) >= 1500 then '[1500,∞)'
         else '(0,100)'
         end sy_wine_spzc_valid_box_interval,--酒水-年度回款箱数区间（尚品臻陈）
    case when COALESCE(sy_wine_spzc_valid_box_num,0) >= 100 and COALESCE(sy_wine_spzc_valid_box_num,0)<300 then CAST(COALESCE(activity_wine_spzc_valid_box_num,0)*5 as DECIMAL(18,2))
         when COALESCE(sy_wine_spzc_valid_box_num,0) >= 300 and COALESCE(sy_wine_spzc_valid_box_num,0) < 1000 then CAST(COALESCE(activity_wine_spzc_valid_box_num,0)*15 as DECIMAL(18,2))
         when COALESCE(sy_wine_spzc_valid_box_num,0) >= 1000 and COALESCE(sy_wine_spzc_valid_box_num,0) < 1500 then CAST(COALESCE(activity_wine_spzc_valid_box_num,0)*20 as DECIMAL(18,2))
         when COALESCE(sy_wine_spzc_valid_box_num,0) >= 1500 then CAST(COALESCE(activity_wine_spzc_valid_box_num,0)*25 as DECIMAL(18,2))
         else '-'
         end wine_spzc_emp_amt_reward,--酒水-尚品臻陈动销奖励员工金额
    case when COALESCE(sy_wine_spzc_valid_box_num,0) >= 100 then CAST(COALESCE(activity_wine_spzc_valid_box_num,0)*5 as DECIMAL(18,2))
         else '-'
         end wine_spzc_director_amt_reward,--酒水-尚品臻陈动销奖励主任金额
    case when COALESCE(activity_wine_spzc_valid_box_num,0) >=5 then cast(COALESCE(activity_wine_spzc_valid_box_num,0)/5 as int) || '箱'
         ELSE '-'
         end wine_spzc_box_num_reward, --酒水-尚品臻陈动销赠酒奖励(箱数)
    activity_wine_spzc_reward_type
from cdfinance.ads_marketing_lifesrv_4q_bch_static_dfp
where pt = '${bizdate}';