spring:
  redis:
    host: r-2zef4d5b5cdab4e4.redis.rds.aliyuncs.com
    port: 6379
    password: Db123456
    database: 119
    jedis:
      pool:
        max-idle: 5
        min-idle: 0
        max-active: 20
    timeout: 30000
  session:
    store-type: redis
  datasource:
    ## 保险业务数据库
    safes-read:
      name: safes-read
#      url: ********************************************************************************************************************************************************************************************************************************
#      username: rdo
#      password: rdoRDO123
      url: ********************************************************************************************************************************************************************************************************************************
      username: safes
      password: Zhnx#safes@T
      driverClassName: com.mysql.cj.jdbc.Driver
      type: com.alibaba.druid.pool.DruidDataSource
      initialSize: 3
      minIdle: 1
      maxActive: 30
      maxWait: 180000
      timeBetweenEvictionRunsMillis: 180000
      minEvictableIdleTimeMillis: 300000
      testWhileIdle: true
      testOnBorrow: false
      testOnReturn: false
      poolPreparedStatements: true
      maxPoolPreparedStatementPerConnectionSize: 20
    safes-write:
      name: safes-write
#      url: ********************************************************************************************************************************************************************************************************************************
#      username: safes
#      password: Zhnx#safes
      url: ********************************************************************************************************************************************************************************************************************************
      username: safes
      password: Zhnx#safes@T
      driverClassName: com.mysql.cj.jdbc.Driver
      type: com.alibaba.druid.pool.DruidDataSource
      initialSize: 3
      minIdle: 1
      maxActive: 30
      maxWait: 180000
      timeBetweenEvictionRunsMillis: 180000
      minEvictableIdleTimeMillis: 300000
      testWhileIdle: true
      testOnBorrow: false
      testOnReturn: false
      poolPreparedStatements: true
      maxPoolPreparedStatementPerConnectionSize: 20
    ## 数据回溯 慢库
    odps:
      name: odps
      url: *********************************************************************************************************
      username: LTAI4FkbeKUT5tvHK1vRCNu1
      password: ******************************
      driverClassName: org.postgresql.Driver
      initialSize: 3
      minIdle: 1
      maxActive: 30
      type: com.alibaba.druid.pool.DruidDataSource
      maxWait: 180000
      timeBetweenEvictionRunsMillis: 180000
      minEvictableIdleTimeMillis: 300000
      testWhileIdle: true
      testOnBorrow: false
      testOnReturn: false
      poolPreparedStatements: true
      maxPoolPreparedStatementPerConnectionSize: 20
    dw:
      url: *****************************************************************************************************************
      driverClassName: org.postgresql.Driver
      username: devops
      password: devops123
      initialSize: 3
      minIdle: 1
      maxActive: 30
      type: com.alibaba.druid.pool.DruidDataSource
      maxWait: 180000
      timeBetweenEvictionRunsMillis: 180000
      minEvictableIdleTimeMillis: 300000
      testWhileIdle: true
      testOnBorrow: false
      testOnReturn: false
      poolPreparedStatements: true
      maxPoolPreparedStatementPerConnectionSize: 20
    dwminer:
      url: ******************************************************************************************************************
      driverClassName: org.postgresql.Driver
      username: devops
      password: devops123
      initialSize: 3
      minIdle: 1
      maxActive: 30
      type: com.alibaba.druid.pool.DruidDataSource
      maxWait: 180000
      timeBetweenEvictionRunsMillis: 180000
      minEvictableIdleTimeMillis: 300000
      testWhileIdle: true
      testOnBorrow: false
      testOnReturn: false
      poolPreparedStatements: true
      maxPoolPreparedStatementPerConnectionSize: 20
  rabbitmq:
    #    host: rabbitmq.dsg.cfpamf.com
    #    port: 5672
    #    username: admin
    #    password: admin
    host: rabbitmq.tsg.cfpamf.com
    port: 5672
    username: admin
    password: donttelldev
bms:
  api:
    url: http://bms-service.tsg.cfpamf.com/
  system:
    id: 3
els-service:
  url: http://els-service-biz.tsg.cfpamf.com/
dynamics-image:
  url: https://node-canvas-scrm-ming-robot-jbjpsqdvwz.cn-hangzhou.fcapp.run
insurance:
  service-info:
    enable: true
    domain: insurance-operation
  api-doc:
    enable: true



wechat:
  app-id: wxffdc81bf67a9f8bd
  app-secret: 7f373858341059b55ab28c7bdc82769c
  temp-message-channel-common: 6v6i-T9P6cqKmqBlFNE4xiF-Q1tFdfZl5YzbFSvwJfU
  temp-message-AI-follow: 1cOlGdjZ-Dj1kSheRqnIPHY-Ay9LWglpYV__gv9OtnE
  todo-interruption-url: https://open.weixin.qq.com/connect/oauth2/authorize?appid=${wechat.app-id}&redirect_uri=https%3A%2F%2Fbmstest.cdfinance.com.cn%2Fapi%2Finsurance%2Fmicro%2Fwx%2Findex%2Fauthorize_redirect%3Frdl%3DaHR0cHM6Ly9oNS10ZXN0LnhpYW93aGFsZS5jb20vIy9icmVha0luc1RvZG9MaXN0&response_type=code&scope=snsapi_userinfo&state=state#wechat_redirect
  front-domain: https://h5-test.xiaowhale.com
  customer-detail-url: ${wechat.front-domain}/#/breakInsCustomerDetail/%s/insur?type=break&noticeWay=%s
  front-customer-detail-url: https://open.weixin.qq.com/connect/oauth2/authorize?appid=${wechat.app-id}&redirect_uri=%s&response_type=code&scope=snsapi_userinfo&state=%s#wechat_redirect
  car-promotion-fee: ${wechat.front-domain}/#/carPromotionFee?productId=%s&regionName=%s
  follow-message: 1cOlGdjZ-Dj1kSheRqnIPHY-Ay9LWglpYV__gv9OtnE
  api-domain: https://bmstest.cdfinance.com.cn/api/insurance/micro
  renew-detail-url: ${wechat.front-domain}/#/renewDetail?productAttrCode=%s&policyNo=%s&fhOrderId=%s&noticeWay=%s


dingtalk:
  agent-id: **********
  app-key: ding5jsclfjd6xduex7w
  app-secret: mhlYBK8tGDrCnLB12e0DEf472ISX_9EbIMsaShNMnWoExkEFlqTmh6zg4VVIq1Wa

#todo 后期配置成集合 统一配置扫描
operation-mq:
  exchange: insurance.operation
  routing-key: activity
  queue: ${operation-mq.exchange}.${operation-mq.routing-key}
policy-info:
  exchange: insurance-biz.exchange.policy.info-notify
  routing-key: insurance-biz.queue.policy.info-notify.key
  queue: insurance-biz.queue.policy.info-notify
marketing-info:
  exchange: omsbase.mq.exchange.marketing_list_exchange
  routing-key: insurance-operation.queue.marketing.info-notify.key
  queue: insurance-operation.queue.marketing.info-notify

logging:
  level:
    root: info
    com.cfpamf.ms.insur.operation.fegin.bms.facede.BmsPostFacade: debug
    org.springframework.jdbc.core.JdbcTemplate: trace
    com.cfpamf.ms.insur.operation.msg.dao: trace
    com.cfpamf.ms.insur.operation.phoenix.dao: trace
aliyun:
  #生产的新key：
  #key：LTAI4FeQAMY7LFxtNFEPZQax
  #secret：******************************
  #bucket：safesfiles
  # 非生产的新key：
  #key：LTAI4FkaWm37HxWhJWVPU4wv
  #secret：******************************
  #bucket：safesfiles-test
  oss:
    aliyunOssBucket: safesfiles-test
    subBucketImage: image
    aliyunOssEndpoint: https://oss-cn-beijing.aliyuncs.com/
    aliyunOssAccessKeyId: LTAI4FkaWm37HxWhJWVPU4wv
    aliyunOssAccessKeySecret: ******************************
    aliyunOssExpiretime: ************
    accessEndpoint: https://${aliyun.oss.aliyunOssBucket}.oss-cn-beijing.aliyuncs.com/
ahas.namespace: test # 建议配置,区分不同的环境 其他环境可以不加 默认default
feign.sentinel.enabled: true


phantom:
  js-path: /Users/<USER>/gitwork/zhnx/html-render/rasterize.js
  phantom-path: /Users/<USER>/gitwork/zhnx/html-render/phantomjs
  success: success

xxl:
  #换成新版本的key值
  job:
    open: false
    admin:
      #新版本的url地址
      addresses: http://pub-xxl-job.tsg.cfpamf.com/xxl-job-admin
    executor:
      appname: insurance-operation-biz
      ip: ************
      port: 9991
      logpath: /Users/<USER>/temp/logs/
      logretentiondays: -1
    accessToken:
  # 需要添加的内容
  newjob:
    admin:
      # dev地址
      #addresses: http://xxl-job2.dsg.cfpamf.com/xxl-job-admin
      # test地址
      addresses: http://xxl-job2.tsg.cfpamf.com/xxl-job-admin
    executor:
      appname: ${spring.application.name}
      ip:
      port: 9991
      logpath: /${spring.application.name}/xxl
      logretentiondays: 5
    accessToken:
author:
  token:
    expireTime: 3600

redenvelope:
  gracePeriod: 10 # unit：day


# 公共配置 企业微信
#ww4e65b0fffa1c2985
#小鲸向海
#1000043
#qtFF7aVa1JKcK3PjaTr3ukFe9lGMydAB_QfVJL8BD1E
#
#
#JVYT00UW38KJT4BgsQW2Zrfjzizdh6ve5R7h4cXAmjc
#JVYT00UW38KJT4BgsQW2Zrfjzizdh6ve5R7h4cXAmjc
qy:
  corp-id: ww4e65b0fffa1c2985
  corp-secret: qtFF7aVa1JKcK3PjaTr3ukFe9lGMydAB_QfVJL8BD1E
  agent-id: 1000043
ai:
  coze:
    claim-classify:
      base-url: https://api.coze.cn
      work-flow-id: 7490454953161785378
      token: pat_iY4Esu1E5CEJ31xZ7wz5iA7xxsiIbbBpHtVcJ0XtOCjkYzY6chsU5xR3zCohnfyg
