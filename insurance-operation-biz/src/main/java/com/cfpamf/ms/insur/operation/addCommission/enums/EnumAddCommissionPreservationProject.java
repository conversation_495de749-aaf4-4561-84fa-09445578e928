package com.cfpamf.ms.insur.operation.addCommission.enums;

import lombok.Getter;

import java.util.Arrays;

/**
 * 保全项目枚举
 *
 * <AUTHOR>
 */
@Getter
public enum EnumAddCommissionPreservationProject {

    PERSON_SURRENDER("personSurrender", "个险退保"),
    GROUP_SURRENDER("groupSurrender", "团险退保"),
    INSURED_BASE_INFO_CHANGE("insuredInfoChange", "被保人重要信息变更"),
    DELETE_PRESERVATION("delete", "保单删除"),
    CHANGE_POLICY_CODE("changePolicyCode", "保单号变更"),

    ;

    private final String code;

    @Getter
    private final String name;

    EnumAddCommissionPreservationProject(String code, String name) {
        this.code = code;
        this.name = name;
    }

    /**
     * 根据编码获取理保全项目枚举
     *
     * @param code 材料编码
     * @return PreservationProjectEnum
     */
    public static EnumAddCommissionPreservationProject decode(String code) {
        return Arrays.stream(EnumAddCommissionPreservationProject.values())
                .filter(x -> x.code.equals(code))
                .findFirst()
                .orElse(null);
    }


}