package com.cfpamf.ms.insur.operation.job;

import com.cfpamf.common.ms.exception.MSBizNormalException;
import com.cfpamf.ms.insur.operation.activity.dao.SmActivityRewardMapper;
import com.cfpamf.ms.insur.operation.activity.entity.SmActivityReward;
import com.cfpamf.ms.insur.operation.activity.enums.*;
import com.cfpamf.ms.insur.operation.reward.dto.RewardDTO;
import com.cfpamf.ms.insur.operation.activity.service.SmOrderActivityService;
import com.cfpamf.ms.insur.operation.activity.service.SystemActivityProgrammeService;
import com.cfpamf.ms.insur.operation.activity.vo.SystemActivityProductRuleVo;
import com.cfpamf.ms.insur.operation.activity.vo.SystemActivityProductVo;
import com.cfpamf.ms.insur.operation.activity.vo.SystemActivityProgrammeVo;
import com.cfpamf.ms.insur.operation.base.constant.GroovyCodeConstants;
import com.cfpamf.ms.insur.operation.base.service.SystemGroovyService;
import com.cfpamf.ms.insur.operation.base.util.BeanUtil;
import com.cfpamf.ms.insur.operation.msg.service.MsgStatService;
import com.cfpamf.ms.insur.operation.reward.service.RewardService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 活动job
 * 用于统计活动数据
 *
 * <AUTHOR>
 * @date 2021/12/13 10:30
 */
@Slf4j
@Component
public class SystemActivityProgrammeJobHandler {
    @Autowired
    MsgStatService msgStatService;

    @Autowired
    SmOrderActivityService smOrderActivityService;

    @Autowired
    SystemActivityProgrammeService systemActivityProgrammeService;

    @Autowired
    @Lazy
    private SystemGroovyService systemGroovyService;

    @Autowired
    private SmActivityRewardMapper smActivityRewardMapper;

    @Autowired
    Map<String, RewardService> rewardServiceMap;

    /**
     * 新增活动奖励
     */
    private List<SmActivityReward> addSmActivityRewardList;
    /**
     * 跟新活动奖励
     */
    private List<SmActivityReward> updateSmActivityRewardList;


    @Transactional(rollbackFor = Exception.class)
    @XxlJob("insurance-operation-system-activity-programme")
    public void execute() {
        addSmActivityRewardList = Lists.newArrayList();
        updateSmActivityRewardList = Lists.newArrayList();
        List<SystemActivityProgrammeVo> activityProgrammeList = systemActivityProgrammeService.getSystemActivityProgrammeByState(EnumActivityState.IN_ACTIVITY);
        if (CollectionUtils.isEmpty(activityProgrammeList)) {
            log.info("暂无活动");
            return;
        }
        //过滤掉普通活动类型
        activityProgrammeList = activityProgrammeList.stream()
                .filter(systemActivityProductDetailVo -> !ActivityType.NORMAL.equals(systemActivityProductDetailVo.getType()))
                .filter(systemActivityProductDetailVo -> !ActivityType.RED_ENVELOPE.equals(systemActivityProductDetailVo.getType()))
                .filter(systemActivityProductDetailVo -> !ActivityType.GOAL_ACHIEVE.equals(systemActivityProductDetailVo.getType()))
                //过滤掉手动配置的
                .filter(systemActivityProductDetailVo -> !EnumActivityConfigType.CONST.equals(systemActivityProductDetailVo.getConfigType()))
                .filter(systemActivityProductDetailVo -> "nb".equals(systemActivityProductDetailVo.getActivityPlatform()))
                .collect(Collectors.toList());
        log.info("活动数量:{}", activityProgrammeList.size());
        //遍历活动
        for (SystemActivityProgrammeVo systemActivityProgrammeVo : activityProgrammeList) {
            // 如果是加佣活动，计算前先把当前活动奖励更新成0
            if (ActivityType.ADD_COMMISSION.name().equals(systemActivityProgrammeVo.getType().name())) {
                smActivityRewardMapper.updateProportionBySaId(systemActivityProgrammeVo.getId(), BigDecimal.ZERO);
            }
            //计算活动奖励
            calculationActivityReward(systemActivityProgrammeVo);
        }
        //更新活动奖励记录
        syncActivityReward();
        //对比已发送活动奖励 进行差值再次进行发送
        //遍历活动
        for (SystemActivityProgrammeVo systemActivityProgrammeVo : activityProgrammeList) {
            Long saId = systemActivityProgrammeVo.getId();
            List<SmActivityReward> rewardList = smActivityRewardMapper.getBySaId(saId);
            if (CollectionUtils.isEmpty(rewardList)) {
                continue;
            }
            Map<String, BigDecimal> dataIdMap = calculationConflict(rewardList, systemActivityProgrammeVo.getConflictRule());
            RewardType rewardType = systemActivityProgrammeVo.getSystemActivityProductList().stream().findFirst().get().getRewardType();
            RewardService rewardService = rewardServiceMap.get(rewardType.name());
            if (Objects.isNull(rewardService)) {
                throw new MSBizNormalException("", rewardType.name() + "该奖励方式暂未实现");
            }

            //奖励发送
            rewardService.awardByJob(systemActivityProgrammeVo, dataIdMap);
        }
    }

    /**
     * 执行加佣活动逻辑
     * s63改版
     */
    private void executeAddCommissionProgramme() {

    }

    /**
     * 计算当前活动的最终奖励
     *
     * @param rewardList
     */
    private Map<String, BigDecimal> calculationConflict(List<SmActivityReward> rewardList, ConflictRule conflictRule) {
        Map<String, BigDecimal> result = Maps.newHashMap();
        //当前活动通过数据id标识分组
        Map<String, List<SmActivityReward>> dataIdActivityRewardMap = rewardList.stream()
                .collect(Collectors.groupingBy(SmActivityReward::getDataId));

        for (String dataId : dataIdActivityRewardMap.keySet()) {
            //遍历每个数据id的奖励集合
            List<SmActivityReward> smActivityRewardList = dataIdActivityRewardMap.get(dataId);
            List<BigDecimal> proportionList = smActivityRewardList.stream()
                    //通过活动产品规则id集合分组
                    .collect(Collectors.groupingBy(SmActivityReward::getSystemActivityProductId))
                    .values()
                    .stream()
                    //叠加每个活动产品规则id的奖励
                    .map(list -> list.stream()
                            .map(SmActivityReward::getProportion)
                            .filter(Objects::nonNull)
                            .reduce(BigDecimal.ZERO, BigDecimal::add))
                    .collect(Collectors.toList());
            BigDecimal proportion;
            switch (conflictRule) {
                case OVERLAY:
                    proportion = proportionList.stream().filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
                    break;
                case OPTIMAL:
                    //默认的叠加方式：OPTIMAL 最优
                default:
                    proportion = proportionList.stream().max((x1, x2) -> x1.compareTo(x2)).get();
                    break;
            }
            result.put(dataId, proportion);
        }
        return result;
    }

    /**
     * 同步活动奖励
     */
    private void syncActivityReward() {
        //新增活动奖励
        if (CollectionUtils.isNotEmpty(addSmActivityRewardList)) {
            smActivityRewardMapper.insertListDuplicateUpdateProportion(addSmActivityRewardList);
            addSmActivityRewardList.clear();
        }
        //修改活动奖励
        if (CollectionUtils.isNotEmpty(updateSmActivityRewardList)) {
            smActivityRewardMapper.batchUpdateActivityReward(updateSmActivityRewardList);
            updateSmActivityRewardList.clear();
        }
    }

    /**
     * 计算活动奖励
     *
     * @param systemActivityProgrammeVo
     */
    private void calculationActivityReward(SystemActivityProgrammeVo systemActivityProgrammeVo) {
        //遍历活动产品规则
        List<SystemActivityProductVo> systemActivityProductList = systemActivityProgrammeVo.getSystemActivityProductList();
        Long saId = systemActivityProgrammeVo.getId();
        systemActivityProductListFor:
        for (SystemActivityProductVo systemActivityProductVo : systemActivityProductList) {
            //执行脚本判断当前活动规则是否为JOB触发类型
            if (Objects.equals(systemActivityProductVo.getTriggerType(), EnumActivityTriggerType.JOB.name())) {
                log.info("计算活动saId:{}的活动奖励", saId);
                //获取当前活动的数据通过活动产品规则id分组
                List<SmActivityReward> rewardList = smActivityRewardMapper.getBySaId(saId);
                Map<Long, List<SmActivityReward>> activityProductRewardMap = rewardList.stream()
                        .collect(Collectors.groupingBy(SmActivityReward::getSystemActivityProductId));
                calculationActivityProductReward(systemActivityProgrammeVo.getType(), activityProductRewardMap, systemActivityProductVo);
            }
        }


    }

    /**
     * 计算活动产品奖励
     *
     * @param activityProductRewardMap
     * @param systemActivityProductVo
     */
    private void calculationActivityProductReward(ActivityType type, Map<Long, List<SmActivityReward>> activityProductRewardMap, SystemActivityProductVo systemActivityProductVo) {
        Long activityProductRewardId = systemActivityProductVo.getId();
        Long saId = systemActivityProductVo.getSaId();
        List<SystemActivityProductRuleVo> systemActivityProductRuleList = systemActivityProductVo.getSystemActivityProductRuleList();
        //执行脚本判断当前活动规则是否为JOB触发类型
        if (!Objects.equals(systemActivityProductVo.getTriggerType(), EnumActivityTriggerType.JOB.name())) {
            return;
        }

        //执行规则获取执行结果
        SystemActivityProductRuleVo systemActivityProductRuleVo = systemActivityProductRuleList.get(0);
        Object result = systemGroovyService.executeForCodeByRuleMethod(GroovyCodeConstants.OPERATION_ACTIVITY_GROOVY_TYPE, systemActivityProductRuleVo.getRuleCode(), systemActivityProductRuleVo.getParams());
        //获取当前活动的奖励数据
        List<SmActivityReward> activityRewardList = activityProductRewardMap.getOrDefault(activityProductRewardId, Lists.newArrayList());
        RewardType rewardType = systemActivityProductVo.getRewardType();
        List<RewardDTO> rewardDTOList = BeanUtil.castObjectToListBean(result, RewardDTO.class);
        if (CollectionUtils.isEmpty(rewardDTOList)) {
            return;
        }

        //通过数据id 和活动奖励的映射关系
        Map<String, SmActivityReward> rewardMap = activityRewardList.stream()
                .collect(Collectors.toMap(SmActivityReward::getDataId, Function.identity()));

        for (RewardDTO rewardDTO : rewardDTOList) {
            String dataId = rewardDTO.getDataId();
            SmActivityReward smActivityReward = rewardMap.get(dataId);
            buildReward(type, systemActivityProductVo, rewardDTO, smActivityReward);
        }
    }

    /**
     * 奖励对象转换
     *
     * @param systemActivityProductVo
     * @param rewardDTO               当前脚本跑的奖励对象
     * @param smActivityReward        历史脚本跑的奖励对象
     */
    private void buildReward(ActivityType type,
                             SystemActivityProductVo systemActivityProductVo,
                             RewardDTO rewardDTO,
                             SmActivityReward smActivityReward) {
        Long activityProductRewardId = systemActivityProductVo.getId();
        Long saId = systemActivityProductVo.getSaId();
        RewardType rewardType = systemActivityProductVo.getRewardType();
        BigDecimal proportion = rewardDTO.getProportion();
        if (Objects.isNull(smActivityReward)) {
            //如果当前没有活动奖励
            SmActivityReward addActivityReward = new SmActivityReward(rewardDTO, EnumActivityTriggerType.JOB, rewardType, saId, activityProductRewardId);
            //新增奖励
            addSmActivityRewardList.add(addActivityReward);
        } else {
            BigDecimal rewardProportion = smActivityReward.getProportion();
            /**
             * 加佣奖励直接覆盖原有的奖励数据；
             * 其他类型逻辑不变，有疑问可跟刘奇隆&李雪沟通需求；
             */
            if (Objects.equals(ActivityType.ADD_COMMISSION, type)) {
                smActivityReward.setProportion(proportion);
                updateSmActivityRewardList.add(smActivityReward);
            } else {
                //如果有活动奖励 并且奖励数量当前奖励数量
                if (proportion.compareTo(rewardProportion) > 0) {
                    //修改奖励数量
                    smActivityReward.setProportion(proportion);
                    //更新奖励
                    updateSmActivityRewardList.add(smActivityReward);
                }
            }
        }
    }
}
