package com.cfpamf.ms.insur.operation.customer.dao;

import com.cfpamf.ms.insur.operation.base.dao.CommonMapper;
import com.cfpamf.ms.insur.operation.customer.dto.CustomerFollowDto;
import com.cfpamf.ms.insur.operation.customer.po.CustomerLoanFirstFollowPo;
import com.cfpamf.ms.insur.operation.customer.po.CustomerLoanFirstPo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface CustomerLoanFirstFollowMapper extends CommonMapper<CustomerLoanFirstFollowPo> {

    /**
     * 更新以往最新记录为0
     *
     * @param loanCustId
     */
    void updateNewest(@Param("loanCustId") String loanCustId);

    void insertCustFollow(@Param("list")List<CustomerLoanFirstPo> loanFirstList);

    List<CustomerFollowDto> selectLoanFirstFollowByLoan(@Param("loanCustId") String loanCustId);
}
