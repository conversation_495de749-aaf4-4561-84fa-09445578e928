package com.cfpamf.ms.insur.operation.aicall.entity;

import com.cfpamf.ms.insur.operation.base.entity.BaseNoUserEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Table;


@ApiModel(value = "AI94任务回调记录对话明细表",description = "")
@Table(name="ai94_call_back_record_detail")
@Data
public class Ai94CallBackRecordDetail extends BaseNoUserEntity {
    /**  */
    @ApiModelProperty(name = "记录id",notes = "")
    private Long recordId ;
    /** 说话号码，其中0为机器人 */
    @ApiModelProperty(name = "说话号码，其中0为机器人",notes = "")
    private String fromNumber ;
    /** 说话内容 */
    @ApiModelProperty(name = "说话内容",notes = "")
    private String content ;
    /** 说话时间，格式:2019-01-09 14:14:19 */
    @ApiModelProperty(name = "说话时间，格式:2019-01-09 14:14:19",notes = "")
    private String createTime ;
    /** AI说话内容所属的节点名称或知识库名称，例如：节点名称：开场白 */
    @ApiModelProperty(name = "AI说话内容所属的节点名称或知识库名称，例如：节点名称：开场白",notes = "")
    private String chatName ;

}
