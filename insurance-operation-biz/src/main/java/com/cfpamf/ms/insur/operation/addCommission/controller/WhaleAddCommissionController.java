package com.cfpamf.ms.insur.operation.addCommission.controller;

import com.alibaba.fastjson.JSONObject;
import com.cfpamf.ms.bms.facade.vo.JwtUserInfo;
import com.cfpamf.ms.insur.operation.addCommission.dto.AddCommissionSettlementPushDto;
import com.cfpamf.ms.insur.operation.addCommission.dto.PreservationDto;
import com.cfpamf.ms.insur.operation.addCommission.dto.WxCmsSmyVo;
import com.cfpamf.ms.insur.operation.addCommission.dto.WxUserAddCommissionListVo;
import com.cfpamf.ms.insur.operation.addCommission.query.WhaleAddCommissionPushQuery;
import com.cfpamf.ms.insur.operation.addCommission.query.WxAddCommissionQuery;
import com.cfpamf.ms.insur.operation.addCommission.service.AddCommissionSettlementPushService;
import com.cfpamf.ms.insur.operation.addCommission.service.WhaleAddCommissionItemService;
import com.cfpamf.ms.insur.operation.base.annotaions.ResponseDecorated;
import com.cfpamf.ms.insur.operation.base.bean.SmyPageInfo;
import com.cfpamf.ms.insur.operation.base.constant.BaseConstants;
import com.cfpamf.ms.insur.operation.whale.model.PreservationDetail;
import com.cfpamf.ms.insur.operation.whale.model.WhaleContract;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 加佣管理
 *
 * <AUTHOR>
 * @date 2023/10/10 16:04
 */
@Slf4j
@Api(value = "加佣管理(业财对接接口)", tags = {"加佣管理(业财对接接口)"})
@RequestMapping(value = {BaseConstants.ADMIN_VERSION + "/whaleCommission"})
@ResponseDecorated
@RestController
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class WhaleAddCommissionController {

    WhaleAddCommissionItemService whaleAddCommissionItemService;

    AddCommissionSettlementPushService addCommissionSettlementPushService;

    @PostMapping("/query/getSettlementDataByPage")
    @ApiOperation("加佣明细分页查询")
    public List<AddCommissionSettlementPushDto> getSettlementDataByPage(@RequestBody WhaleAddCommissionPushQuery query) {
        return whaleAddCommissionItemService.getSettlementDataByPage(query);
    }

    /**
     * 微信24版我的推广费-加佣(列表+汇总)
     *
     * @param query
     * @return
     */
    @ApiOperation(value = "我的推广费-加佣(分页列表+汇总)")
    @PostMapping("/user/costListAndSummary")
    public SmyPageInfo<WxUserAddCommissionListVo, WxCmsSmyVo> getWxUserCostListAndSummary(@RequestBody WxAddCommissionQuery query) {
        log.info("我的推广费-加佣(分页列表+汇总), 入参:{}", query);
        return whaleAddCommissionItemService.pageWxUserAddCommissionListAndSummary(query);
    }
    /**
     * 加佣-保全事件处理
     *
     * @param preservationDto 保全事件信息
     */
    @ApiOperation(value = "加佣-保全事件处理")
    @PostMapping("/doCorrect")
    public void doAddCommissionCorrect(@RequestBody PreservationDto preservationDto) {
        log.info("加佣-保全事件处理, 入参:{}", JSONObject.toJSONString(preservationDto));
        addCommissionSettlementPushService.doCorrect(preservationDto);
    }

    /**
     * 加佣-同步业财结算状态
     *
     * @param uuids 唯一编码
     */
    @ApiOperation(value = "加佣-同步业财结算状态")
    @PostMapping("/syncSettlementStatus")
    public void syncSettlementStatus(@RequestParam("uuids") List<String> uuids) {
        log.info("加佣-同步业财结算状态, 入参:加佣唯一健编码：{}", JSONObject.toJSONString(uuids));
        addCommissionSettlementPushService.syncSettlementStatus(uuids);
    }
}
