package com.cfpamf.ms.insur.operation.activity.form;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/7/15 14:32
 */
@ApiModel("场景查询信息")
@FieldDefaults(level = AccessLevel.PRIVATE)
@Data
public class SystemActivityConstProductSceneForm {
    @NotNull(message = "产品不能为null")
    @ApiModelProperty("包含一个全部就传空数组 否则传交集")
    List<Integer> productIds;

    @ApiModelProperty(value = "场景编码 ORDER_PREMIUM SCALE SUM", access = "ORDER_PREMIUM,SCALE,SUM", example = "ORDER_PREMIUM")
    String ruleType;

    @JsonIgnore
    public String getProductIdJson() {
        return JSON.toJSONString(productIds);
    }

}
