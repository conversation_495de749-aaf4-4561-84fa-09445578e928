package com.cfpamf.ms.insur.operation.settlement.controller;

import com.cfpamf.common.ms.result.Result;
import com.cfpamf.ms.insur.operation.base.response.CommonResult;
import com.cfpamf.ms.insur.operation.fegin.wx.request.WxaCodeCreateInput;
import com.cfpamf.ms.insur.operation.fegin.wx.response.ProductList;
import com.cfpamf.ms.insur.operation.fegin.wx.response.SellProductListOut;
import com.cfpamf.ms.insur.operation.settlement.resp.ZhSettlementCostInfo;
import com.cfpamf.ms.insur.operation.settlement.service.WhaleSettlementBaseService;
import com.cfpamf.ms.insur.operation.xj.service.WhalePublicApiService;
import com.cfpamf.ms.insur.operation.xj.service.impl.WhalePublicApiBaseService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/4/29 16:16
 * @Version 1.0
 */
@Slf4j
@Api(tags = "小鲸业财结算服务")
@RestController
@RequestMapping("/whale/settlement")
public class WhaleSettlementController {

    @Autowired
    private WhaleSettlementBaseService whaleSettlementBaseService;

    /**
     * 获取商品下拉列表
     */
    @ApiOperation(value = "获取结算支出端基础明细记录", notes = "获取结算支出端基础明细记录")
    @PostMapping("/listZhSettlementCostByPolicyNos")
    public CommonResult<List<ZhSettlementCostInfo>> querySellProductList(@RequestParam("policyNos") @ApiParam(name = "policyNos",value = "保单号",required = true) List<String> policyNos,
                                                                         @RequestParam("renewalPeriod") @ApiParam(name = "renewalPeriod",value = "续期期数") Integer renewalPeriod) {
        log.info("获取结算支出端基础明细记录，查询条件={},{}",policyNos,renewalPeriod);
        List<ZhSettlementCostInfo> list = whaleSettlementBaseService.listZhSettlementCostByPolicyNos(policyNos,renewalPeriod);
        return CommonResult.successResult(list);
    }
}
