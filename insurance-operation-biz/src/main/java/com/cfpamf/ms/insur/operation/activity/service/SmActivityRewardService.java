package com.cfpamf.ms.insur.operation.activity.service;

import com.cfpamf.ms.insur.operation.activity.entity.SmActivityReward;
import com.cfpamf.ms.insur.operation.activity.form.TakeBackRewardForm;
import com.cfpamf.ms.insur.operation.activity.listener.GrantCouponsOrderMessage;
import com.cfpamf.ms.insur.operation.activity.vo.SystemActivityProductDetailVo;
import com.cfpamf.ms.insur.operation.activity.vo.SystemActivityProductRuleVo;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/1/6 16:03
 */
public interface SmActivityRewardService {
    /**
     * 处理消息通知奖励
     *
     * @param couponsOrderMessage
     * @param systemActivityMap
     */
    void handlerMessageActivity(GrantCouponsOrderMessage couponsOrderMessage, Map<Long, List<SystemActivityProductDetailVo>> systemActivityMap);

    /**
     * send reward
     *
     * @param smActivityRewardList
     */
    void sendReward(List<SmActivityReward> smActivityRewardList);

    /**
     * 计算奖励
     *
     * @param couponsOrderMessage
     * @param activityProductDetailList
     * @param activityProductRuleMap
     * @return
     */
    List<SmActivityReward> calculateRewards(GrantCouponsOrderMessage couponsOrderMessage, List<SystemActivityProductDetailVo> activityProductDetailList, Map<Long, List<SystemActivityProductRuleVo>> activityProductRuleMap);

    /**
     * 通过活动id批量软删除活动奖励
     *
     * @param takeBackRewardForm
     */
    void takeBackReward(TakeBackRewardForm takeBackRewardForm);

    /**
     * 删除活动奖励
     *
     * @param takeBackRewardForm
     */
    void delete(TakeBackRewardForm takeBackRewardForm);
}
