package com.cfpamf.ms.insur.operation.phoenix.api;

import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;

/**
 * <AUTHOR> 2020/4/3 16:12
 */
@EnableConfigurationProperties(WhaleApiProperties.class)
@ConfigurationProperties("whale")
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class WhaleApiProperties {

    String domain = "https://api-channel-test.xiaowhale.com/public-api";
    /**
     * 渠道标识
     */
    String aid;
}
