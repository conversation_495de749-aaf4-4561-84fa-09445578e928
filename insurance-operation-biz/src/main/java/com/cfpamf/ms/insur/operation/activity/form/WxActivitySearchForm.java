package com.cfpamf.ms.insur.operation.activity.form;


import com.cfpamf.ms.insur.operation.base.form.PageForm;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 微信活动查询表单
 *
 * <AUTHOR>
 */
@Data
@ApiModel("微信活动查询表单")
public class WxActivitySearchForm extends PageForm {

    /**
     * 微信openId
     */
    @ApiModelProperty("微信openId")
    private String openId;

    /**
     * 授权token
     */
    @ApiModelProperty("授权token")
    @NotBlank(message = "授权token")
    private String authorization;

    /**
     * 区域
     */
    @ApiModelProperty(value = "区域", hidden = true)
    private String regionCode;
}
