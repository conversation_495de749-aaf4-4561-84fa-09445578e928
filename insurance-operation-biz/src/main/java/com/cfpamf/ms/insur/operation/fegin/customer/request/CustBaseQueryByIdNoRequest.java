package com.cfpamf.ms.insur.operation.fegin.customer.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

import java.io.Serializable;

/**
 * 根据身份证查询客户基本信息
 *
 * <AUTHOR>
 * @create 2019-09-05 10:13
 */
@Data
@ApiModel
public class CustBaseQueryByIdNoRequest implements Serializable {

    @ApiModelProperty(value = "身份证号", required = true)
    @NotBlank(message = "身份证号不能为空")
    private String idNo;
}
