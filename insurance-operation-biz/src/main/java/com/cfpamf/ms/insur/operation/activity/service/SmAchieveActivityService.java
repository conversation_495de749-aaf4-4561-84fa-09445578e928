package com.cfpamf.ms.insur.operation.activity.service;

import com.cfpamf.ms.insur.operation.activity.form.AchieveActivityParams;
import com.cfpamf.ms.insur.operation.activity.vo.AchieveSummaryVO;
import com.cfpamf.ms.insur.operation.activity.vo.AchieveTableVO;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/5/9 10:02
 * @Version 1.0
 */
public interface SmAchieveActivityService {

    /**
     * 汇总数据查询
     *
     * @return
     */
    AchieveSummaryVO querySummaryData(AchieveActivityParams params);


    void handlerParams(AchieveActivityParams params);

    /**
     * 查询表格数据
     *
     * @return
     */
    List<AchieveTableVO> queryTableData(AchieveActivityParams params);

    void editPopUp(String activityId, String state);

    AchieveSummaryVO queryOrgData(AchieveActivityParams params);

    String getPopUp(String activityId);

    String getAcId();

    void importFile(String fileUrl);
}
