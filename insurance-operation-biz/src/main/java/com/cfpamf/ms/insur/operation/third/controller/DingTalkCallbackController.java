package com.cfpamf.ms.insur.operation.third.controller;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cfpamf.common.ms.exception.MSBizNormalException;
import com.cfpamf.ms.insur.operation.base.constant.BaseConstants;
import com.cfpamf.ms.insur.operation.third.util.DingCallbackCrypto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * <AUTHOR> shezhigao
 * @date : 2024-6-24
 */
@Slf4j
@Api(tags = "接收钉钉推送消息服务")
@RestController
@RequestMapping({BaseConstants.OPEN_API + BaseConstants.OPEN_API_DING_TALK})
public class DingTalkCallbackController{


//    @ApiOperation("接收钉钉事件")
//    @RequestMapping(value = "/acceptDingEventMessage"+BaseConstants.API_VERSION_1, method = {RequestMethod.POST, RequestMethod.GET})
//    public Map<String, String> acceptDingEventMessage(
//            @RequestParam(value = "msg_signature", required = false) String msg_signature,
//            @RequestParam(value = "timestamp", required = false) String timeStamp,
//            @RequestParam(value = "nonce", required = false) String nonce,
//            @RequestBody(required = false) JSONObject json) {
//        try {
//            String aes_key = "8PjdurPsXz4rtUHuk15hMqocX0b2lCVwZMnKfKEItZ4";
//            String token = "9zbyAc8tGeIFRHuiK4rksSFVwrl5Dim9RRXTS";
//            String appId = "dingqmkwcdeoxpcrvoxw";
//            // 1. 从http请求中获取加解密参数
//
//            // 2. 使用加解密类型
//            // Constant.OWNER_KEY 说明：
//            // 1、开发者后台配置的订阅事件为应用级事件推送，此时OWNER_KEY为应用的APP_KEY。
//            // 2、调用订阅事件接口订阅的事件为企业级事件推送，
//            //      此时OWNER_KEY为：企业的appkey（企业内部应用）或SUITE_KEY（三方应用）
//            DingCallbackCrypto callbackCrypto = null;
//            String body = (json == null)? null : json.toJSONString();
//            log.info("acceptDingEventMessage 解密前 msg_signature= {}, timeStamp= {}, nonce= {}, body= {}",msg_signature,timeStamp,nonce,body);
//            try {
//                callbackCrypto = new DingCallbackCrypto(token, aes_key, appId);
//            } catch (DingCallbackCrypto.DingTalkEncryptException e) {
//                log.warn("读取回传信息异常 msg_signature= {}, timeStamp= {}, nonce= {}, body= {}",msg_signature,timeStamp,nonce,body, e);
//                throw new MSBizNormalException("", "读取回传信息异常");
//            }
//            String encryptMsg = json.getString("encrypt");
//            String decryptMsg = null;
//            try {
//                decryptMsg = callbackCrypto.getDecryptMsg(msg_signature, timeStamp, nonce, encryptMsg);
//            } catch (DingCallbackCrypto.DingTalkEncryptException e) {
//                log.warn("读取回传信息解密异常 msg_signature= {}, timeStamp= {}, nonce= {}, body= {}",msg_signature,timeStamp,nonce,body, e);
//                throw new MSBizNormalException("", "读取回传信息解密异常");
//            }
//            log.info("acceptDingEventMessage 解密后 msg_signature= {}, timeStamp= {}, nonce= {}, body= {}",msg_signature,timeStamp,nonce,decryptMsg);
//            // 3. 反序列化回调事件json数据
//            JSONObject eventJson = JSON.parseObject(decryptMsg);
//            String eventType = eventJson.getString("EventType");
//
//            // 4. 根据EventType分类处理
//            if ("check_url".equals(eventType)) {
//                // 测试回调url的正确性
//                log.info("测试回调url的正确性");
//            } else if ("user_add_org".equals(eventType)) {
//                // 处理通讯录用户增加事件
//                log.info("发生了：" + eventType + "事件");
//            } else {
//                // 添加其他已注册的
//                log.info("发生了：" + eventType + "事件");
//            }
//
//            // 5. 返回success的加密数据
//            Map<String, String> successMap = callbackCrypto.getEncryptedMap("success");
//            return successMap;
//
//        } catch (DingCallbackCrypto.DingTalkEncryptException e) {
//            log.warn("读取回传信息异常 msg_signature= {}, timeStamp= {}, nonce= {}, body= {}",msg_signature,timeStamp,nonce, e);
//            throw new MSBizNormalException("", "读取回传信息异常");
//        }
//    }

    @ApiOperation("接收钉钉事件")
    @RequestMapping(value = "/acceptDingEventMessage"+BaseConstants.API_VERSION_1, method = {RequestMethod.POST, RequestMethod.GET})
    public Map<String, String> acceptDingEventMessage(
            @RequestBody(required = false) JSONObject json) {
        try {
            String aes_key = "8PjdurPsXz4rtUHuk15hMqocX0b2lCVwZMnKfKEItZ4";
            String token = "9zbyAc8tGeIFRHuiK4rksSFVwrl5Dim9RRXTS";
            String appId = "dingqmkwcdeoxpcrvoxw";
            // 1. 从http请求中获取加解密参数

            // 2. 使用加解密类型
            // Constant.OWNER_KEY 说明：
            // 1、开发者后台配置的订阅事件为应用级事件推送，此时OWNER_KEY为应用的APP_KEY。
            // 2、调用订阅事件接口订阅的事件为企业级事件推送，
            //      此时OWNER_KEY为：企业的appkey（企业内部应用）或SUITE_KEY（三方应用）
            DingCallbackCrypto callbackCrypto = null;
            String body = (json == null)? null : json.toJSONString();
            log.info("acceptDingEventMessage body= {}",body);
            Map<String, String> successMap = callbackCrypto.getEncryptedMap("success");
            return successMap;

        } catch (DingCallbackCrypto.DingTalkEncryptException e) {
            log.warn("读取回传信息异常", e);
            throw new MSBizNormalException("", "读取回传信息异常");
        }
    }
}
