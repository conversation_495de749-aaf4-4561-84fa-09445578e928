package com.cfpamf.ms.insur.operation.fegin.image.service.impl;

import com.cfpamf.common.ms.exception.MSBizNormalException;
import com.cfpamf.ms.insur.operation.base.response.CommonResult;
import com.cfpamf.ms.insur.operation.fegin.image.facade.DynamicsImageFacade;
import com.cfpamf.ms.insur.operation.fegin.image.response.ImageResult;
import com.cfpamf.ms.insur.operation.fegin.image.service.DynamicsImageService;
import com.cfpamf.ms.insur.operation.fegin.image.vo.ImageUrl;
import com.cfpamf.ms.insur.operation.msg.enums.ImageNatureEnum;
import feign.RetryableException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.Map;
import java.util.Objects;

@Service
@Slf4j
public class DynamicsImageServiceImpl implements DynamicsImageService {

    @Autowired
    private DynamicsImageFacade dynamicsImageFacade;

    public CommonResult<ImageUrl> image(Map<String, Object> paras){
        log.info("接收到的生成图片入参,{}",paras);
        if(Objects.isNull(paras)){
            throw new MSBizNormalException("","生成图片的参数不能为空");
        }
        if(!paras.containsKey("htmlValue")){
            throw new MSBizNormalException("","生产图片的参数必须包含ejs模板字符串");
        }
        paras.put("template",paras.get("htmlValue"));

        ImageResult  result = null;
        try {
            result = getImageUrl(paras);
        }catch (RetryableException e){
            try {
                Thread.sleep(1000);
            } catch (InterruptedException ex) {
                log.info("生成图片结果重试失败",ex);
                throw new RuntimeException(ex);
            }
            result = getImageUrl(paras);
        }
        log.info("接收到的生成图片结果,{}",result);

        CommonResult<ImageUrl> imageResult = new CommonResult();
        if(result != null && result.getSuccess()){
            ImageUrl img = new ImageUrl();
            img.setUrl(result.getData());
            imageResult.setCode(CommonResult.SUCCESS_CODE);
            imageResult.setData(img);
        }else{
            imageResult.setCode(CommonResult.FAIL_CODE);
            imageResult.setMessage(result.getError());
            imageResult.setErrorCode(CommonResult.FAIL_CODE);
        }
        return imageResult;

    }

    private ImageResult getImageUrl(Map<String, Object> paras){

        if (paras.containsKey("imageNature")) {
            String imageNature = paras.get("imageNature").toString();
            if (ImageNatureEnum.DYNAMICS.isMe(imageNature)) {
                return dynamicsImageFacade.gifPosterV2(paras);
            } else {
                return dynamicsImageFacade.getPosterUrlV2(paras);
            }
        } else {
            return dynamicsImageFacade.getPosterUrlV2(paras);
        }

    }
}
