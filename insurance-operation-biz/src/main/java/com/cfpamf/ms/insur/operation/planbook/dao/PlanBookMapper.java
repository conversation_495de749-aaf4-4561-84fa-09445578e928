package com.cfpamf.ms.insur.operation.planbook.dao;

import com.cfpamf.ms.insur.operation.planbook.form.PlanBookPageQuery;
import com.cfpamf.ms.insur.operation.planbook.vo.*;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.HashSet;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/12/8 15:19
 * @Version 1.0
 */
@Mapper
public interface PlanBookMapper {

    /**
     * 查询产品最大的版本号
     *
     * @param productId
     * @return
     */
    Integer productCurrentVersion(Integer productId);

    /**
     * 根据产品id查询生效的计划
     *
     * @param productId
     * @return
     */
    List<ProductPlanVO> queryPlanByProductId(@Param("productId") Integer productId, @Param("version") Integer version);

    /**
     * 根据计划id查询生效的险种
     *
     * @param planIds
     * @return
     */
    List<SmPlanRiskVO> queryNewestRisksByPlanIds(@Param("planIds") List<Integer> planIds,
                                                 @Param("version") Integer version);

    /**
     * 查看产品主险种
     *
     * @param productId
     * @return
     */
    SmProductRiskVO queryProductMainRiskByProductId(@Param("productId") Integer productId,
                                                    @Param("version") Integer version);

    /**
     * 根据险种key查询责任
     *
     * @param planIds
     * @return
     */
    List<SmPlanRiskDutyVO> queryNewestDutyByRiskKey(@Param("planIds") List<Integer> planIds,
                                                    @Param("version") Integer version);

    /**
     * 根据险种key查询费率因子
     *
     * @param riskKeyList
     * @return
     */
    List<SysRiskFactorVO> queryNewestFactorByRiskKey(List<String> riskKeyList);

    List<RiskFactorConfigVO> queryNewestFactorConfigByRiskKey(List<String> riskKeyList);

    List<SysRiskAmountVO> queryRiskAmount(List<Integer> riskId);

    List<SysRiskDutyAmountVO> queryRiskDutyAmount(List<Integer> dutyId);

    /**
     * 根据计划书id查找预览信息
     *
     * @param planBookId
     * @return
     */
    ProductPreviewVO queryProductPreview(Integer planBookId);

    /**
     * 查询产品条款
     *
     * @param productId
     * @return
     */
    List<ProductClauseVO> queryProductClause(Integer productId);

    List<PlanBookDutyPreviewVO> queryPlanBookDutyPreview(Integer planBookId);

    HashSet<Integer> existDutyRiskDetail(List<Integer> dutyIds);

    /**
     * 病种详情
     *
     * @param dutyId
     * @return
     */
    List<RiskDutyDiseaseVO> queryRiskDutyDiseaseVO(@Param("dutyId") Integer dutyId, @Param("keyWord") String keyWord);

    /**
     * 计划书分页列表查询
     *
     * @param query
     * @return
     */
    List<PlanBookPageVO> queryPlanBookPage(PlanBookPageQuery query);

    Integer queryPlanBookPersonStatistic(PlanBookPageQuery query);
}
