package com.cfpamf.ms.insur.operation.honor.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @Date 2023/04/19 15:37
 * @Version 1.0
 */
@AllArgsConstructor
@Getter
public enum EnumHonorLevel {

    /**
     * 荣誉对象：area区域 bah 分支 emp个人
     */
    AREA("area","区域"),
    BCH("bch","分支"),
    EMP("emp","个人");

    String code;
    String desc;
    public static String dict(String code) {
        if(Objects.isNull(code)){
            return "";
        }
        return Stream.of(values())
                .filter(em -> em.getCode().equals(code))
                .findFirst().map(EnumHonorLevel::getDesc)
                .orElse("");
    }
}
