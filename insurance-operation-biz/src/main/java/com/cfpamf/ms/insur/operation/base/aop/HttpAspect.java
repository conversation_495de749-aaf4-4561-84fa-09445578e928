package com.cfpamf.ms.insur.operation.base.aop;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cfpamf.common.ms.util.NetworkUtils;
import com.cfpamf.ms.insur.operation.base.exception.BusinessException;
import com.cfpamf.ms.insur.operation.base.response.CommonResult;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.Arrays;
import java.util.Enumeration;
import java.util.List;
import java.util.UUID;

/**
 * <AUTHOR>
 * @date 2021/8/9 10:22
 */
@Aspect
@Component
@Order(-5)
public class HttpAspect {
    private static final Logger log = LoggerFactory.getLogger(HttpAspect.class);
    ThreadLocal<Long> startTime = new ThreadLocal<>();
    ThreadLocal<String> ID = new ThreadLocal<>();

    public HttpAspect() {
    }

    @Pointcut("execution(public * com.cfpamf..*.controller..*.*(..))")
    public void webLog() {
    }

    @Before("webLog()")
    public void doBefore(JoinPoint joinPoint) throws IOException {
        this.startTime.set(System.currentTimeMillis());
        String uid = UUID.randomUUID().toString();
        this.ID.set(uid);
        MDC.put("requestId", uid.substring(24, 36));
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = attributes.getRequest();
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("ID", uid);
        jsonObject.put("URL", request.getRequestURL());
        jsonObject.put("PATH", request.getServletPath());
        jsonObject.put("HTTP_METHOD", request.getMethod());
        jsonObject.put("CLASS_METHOD", joinPoint.getSignature().getDeclaringTypeName() + "." + joinPoint.getSignature().getName());
        jsonObject.put("IP", NetworkUtils.getIpAddress(request));
        jsonObject.put("ARGS", Arrays.toString(joinPoint.getArgs()));
        Enumeration<String> enu = request.getParameterNames();
        JSONObject paramsobj = new JSONObject();

        while (enu.hasMoreElements()) {
            String paraName = (String) enu.nextElement();
            paramsobj.put(paraName, request.getParameter(paraName));
        }

        jsonObject.put("params", paramsobj);
        if (log.isInfoEnabled()) {
            log.info("request : {}", jsonObject.toString());
        }

    }

    @AfterReturning(
            returning = "object",
            pointcut = "webLog()"
    )
    public void doAfterReturning(Object object) {
        if (log.isInfoEnabled()) {
            logResponse(object);
        }
        MDC.clear();
        ID.remove();
        startTime.remove();
    }

    /**
     * 打印响应日志
     *
     * @param object
     */
    private void logResponse(Object object) {
        if (object instanceof List) {
            List data = (List) object;
            log.info("response data size: {},耗时（毫秒）{},ID: {}  ", data.size(), System.currentTimeMillis() - (Long) this.startTime.get(), this.ID.get());
        } else {
            log.info("response : {},耗时（毫秒）{},ID: {}  ", new Object[]{JSON.toJSON(object), System.currentTimeMillis() - (Long) this.startTime.get(), this.ID.get()});
        }
    }

    @AfterThrowing(
            throwing = "ex",
            pointcut = "execution(public * com.cfpamf..*.controller..*.*(..))"
    )
    public void doRecoveryActions(Throwable ex) {
        CommonResult result = new CommonResult();
        if (ex instanceof BusinessException) {
            BusinessException r = (BusinessException) ex;
            result.setCode(r.getCode());
            result.setMessage(r.getMessage());
        } else {
            result.setFail();
        }

        if (log.isInfoEnabled()) {
            logResponse(result);
        }

        MDC.clear();
        ID.remove();
        startTime.remove();
    }
}
