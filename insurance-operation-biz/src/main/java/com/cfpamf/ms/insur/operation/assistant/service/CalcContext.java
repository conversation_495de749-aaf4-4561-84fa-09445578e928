package com.cfpamf.ms.insur.operation.assistant.service;

import lombok.Data;
import java.math.BigDecimal;

@Data
public class CalcContext {
    /**
     * 分支名称
     */
    private String bchName;
    /**
     * 分支编码
     */
    private String bchCode;
    /**
     * 年度余额目标。
     */
    private BigDecimal syLoanBalanceTarget;

    /**
     * 月度放款目标。
     */
    private BigDecimal smLoanAmountTarget;
    /**
     * 下月还款计划金额。
     */
    private BigDecimal nmRepaymentPlanAmt;

    /**
     * 月底贷款余额。
     */
    private BigDecimal smLoanBalance;

    /**
     * 月度标准保费目标。
     */
    private BigDecimal smNormInsuranceAmtTarget;

    /**
     * 月度主营标准保费。
     */
    private BigDecimal smLoanNormInsuranceAmt;

    /**
     * 月度主营标准保费目标。
     */
    private BigDecimal smLoanNormInsuranceAmtTarget;

    /**
     * 月度主营规模保费。
     */
    private BigDecimal smLoanInsuranceAmt;
    /**
     * 月度主营规模保费目标。
     */
    private BigDecimal smLoanInsuranceAmtTarget;

    /**
     * 月度非主营留存标准保费。
     */
    private BigDecimal smUnloanRpNormInsuranceAmt;

    /**
     * 月度非主营留存规模保费。
     */
    private BigDecimal smUnloanRpInsuranceAmt;

    /**
     * 月度非主营留存标准保费目标。
     */
    private BigDecimal smUnloanRpNormInsuranceAmtTarget;

    /**
     * 月度非主营留存规模保费目标。
     */
    private BigDecimal smUnloanRpInsuranceAmtTarget;

    /**
     * 月度主营留存标准保费。
     */
    private BigDecimal smLoanRpNormInsuranceAmt;

    /**
     * 月度主营留存规模保费。
     */
    private BigDecimal smLoanRpInsuranceAmt;

    /**
     * 月度主营留存标准保费目标。
     */
    private BigDecimal smLoanRpNormInsuranceAmtTarget;

    /**
     * 月度主营留存规模保费。
     */
    private BigDecimal smLoanRpInsuranceAmtTarget;

    /**
     * 月度非主营拓新标准保费。
     */
    private BigDecimal smUnloanNcNormInsuranceAmt;

    /**
     * 月度非主营拓新规模保费。
     */
    private BigDecimal smUnloanNcInsuranceAmt;

    /**
     * 月度非主营拓新标准保费目标。
     */
    private BigDecimal smUnloanNcNormInsuranceAmtTarget;

    /**
     * 月度非主营拓新规模保费目标。
     */
    private BigDecimal smUnloanNcInsuranceAmtTarget;

    /**
     * 当月交叉销售比 sm_offline_loan_insurance_rate
     */
    private BigDecimal smOfflineLoanInsuranceRate;

    /**
     * 当月交叉销售比建议提升值
     */
    private BigDecimal smOfflineLoanInsuranceRateSuggest = new BigDecimal("10");

    /**
     * 当月生成非主营的续保待办保单保费
     */
    private BigDecimal smUnloanRenewalShortTodoInsuranceAmt;

    /**
     * 当月生成主营的续保待办保单保费
     */
    private BigDecimal smLoanRenewalShortTodoInsuranceAmt;
    /**
     * 截止当前非贷续保待办保单金额
     */
    private BigDecimal unloanRenewShortTodoInsuranceAmt;
    /**
     * 截止当前信贷续保待办保单金额
     */
    private BigDecimal LoanRenewShortTodoInsuranceAmt;

    /**
     * 建议续保待办捞回率
     */
    private BigDecimal renewalTodoRateSuggest = new BigDecimal("0.42");

}
