package com.cfpamf.ms.insur.operation.planbook.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2021/12/13 11:50
 * @Version 1.0
 */
@Data
public class ProductClauseVO {
    @ApiModelProperty(value = "条款Id")
    private Integer id;

    @ApiModelProperty(value = "条款名称")
    private String clauseName;

    @ApiModelProperty(value = "条款文件Url")
    private String clauseUrl;

    @ApiModelProperty("排序号")
    Integer sort;
}
