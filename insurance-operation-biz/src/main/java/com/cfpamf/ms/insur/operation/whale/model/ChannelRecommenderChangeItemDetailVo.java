package com.cfpamf.ms.insur.operation.whale.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: yang<PERSON>lin
 * @create: 2024-03-04 14:20
 * @description: 渠道推荐人变更保全项明细
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ChannelRecommenderChangeItemDetailVo {
    /**
     * 批改类型 新、增、减 对应的类型 new、add、batch
     */
    private String operationType;
    /**
     * 变更前渠道推人工号
     */
    private String beforeChannelRecommender;
    /**
     * 变更前渠道推荐人机构
     */
    private String beforeCustomerManagerChannelOrgCode;
    /**
     * 变更前渠道
     */
    private String beforeChannelCode;
    /**
     * 被保人证件类型
     */
    private String insuredIdType;
    /**
     * 被保人姓名
     */
    private String insuredIdName;
    /**
     * 被保人证件号码
     */
    private String insuredIdNo;
}
