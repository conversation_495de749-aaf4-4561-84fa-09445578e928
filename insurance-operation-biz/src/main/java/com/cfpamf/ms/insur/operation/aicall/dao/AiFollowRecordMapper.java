package com.cfpamf.ms.insur.operation.aicall.dao;

import com.cfpamf.ms.insur.operation.aicall.entity.AiFollowRecordPo;
import com.cfpamf.ms.insur.operation.base.dao.CommonMapper;
import com.cfpamf.ms.insur.operation.customer.po.CustomerInterruptionPo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface AiFollowRecordMapper extends CommonMapper<AiFollowRecordPo> {

    /**
     * 更新以往最新记录为0
     * @param customerId
     */
    void updateNewest(@Param("customerId") String customerId);

    /**
     * 新增跟进记录
     * @param list
     */
    void insertFollow(@Param("list") List<CustomerInterruptionPo> list);

    List<AiFollowRecordPo> getListByOutSourceId(@Param("outSourceIds") List<Long> outSourceId);

}
