
package com.cfpamf.ms.insur.operation.qy.form;

import com.cfpamf.ms.insur.operation.base.entity.BaseNoUserEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import javax.validation.constraints.NotNull;

/**
 * Created by zhengjing  on 2022-08-05 15:21:46
 *
 * <AUTHOR>
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class OpeQyLabelRuleForm extends BaseNoUserEntity {


    /**
     * 字段名称 问卷id
     */
    @NotNull(message = "问卷id不能为空")
    @ApiModelProperty(value = "问卷id", required = true)
    Long pagerId;

    String labelId;

    String labelName;

    OpeQyLabelRuleParams labelRule;
}

