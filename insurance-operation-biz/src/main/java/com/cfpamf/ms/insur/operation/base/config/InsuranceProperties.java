package com.cfpamf.ms.insur.operation.base.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * 保险服务配置
 *
 * <AUTHOR>
 * @date 2021/4/29 14:29
 */
@Data
@ConfigurationProperties(prefix = "insurance")
public class InsuranceProperties {
    private ServiceInfo serviceInfo;
    private ApiDoc apiDoc;

    @Data
    public static class ServiceInfo {
        private boolean enable;
        private String domain;
    }

    @Data
    public static class ApiDoc {
        private String apiVersion = "";
        private boolean enable = true;
    }
}
