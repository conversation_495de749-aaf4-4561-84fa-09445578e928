
package com.cfpamf.ms.insur.operation.msg.pojo.po;

import com.cfpamf.ms.insur.operation.base.entity.BaseNoUserEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import javax.persistence.Table;
import java.time.LocalDateTime;

/**
 * Created by zhengjing  on 2021-07-15 18:19:36
 *
 * <AUTHOR>
 */
@ApiModel("营销工具-消息推送暂存池")
@Table(name = "op_message_push_pool")
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class OpMessagePushPool extends BaseNoUserEntity {

    /**
     * 字段名称 规则id
     */
    @ApiModelProperty(value = "规则id")
    Long messageRuleId;

    String contextId;

    @ApiModelProperty("执行时间")
    LocalDateTime execTime;

    /**
     * 字段名称 接受者类型 chat employee
     */
    @ApiModelProperty(value = "状态 1-初始化 2-已发送 3-发送失败")
    Integer state;
    /**
     * 字段名称 接受者 类型为群时
     */
    @ApiModelProperty(value = "备注")
    String remark;
}

