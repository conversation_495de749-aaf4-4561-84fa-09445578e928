package com.cfpamf.ms.insur.operation.pco.service;

import org.springframework.util.StringUtils;
import com.alibaba.fastjson.JSON;
import com.cfpamf.ms.insur.operation.base.exception.BusinessException;
import com.cfpamf.ms.insur.operation.base.helper.WxCheckAuthorityHelper;
import com.cfpamf.ms.insur.operation.base.service.DataAuthService;
import com.cfpamf.ms.insur.operation.base.util.HttpRequestUtil;
import com.cfpamf.ms.insur.operation.base.vo.WxSessionVO;
import com.cfpamf.ms.insur.operation.pco.dao.PcoExtendInfoMapper;
import com.cfpamf.ms.insur.operation.pco.dao.PcoLevelInfoMapper;
import com.cfpamf.ms.insur.operation.pco.dao.PcoWeeksScoreMapper;
import com.cfpamf.ms.insur.operation.pco.dao.PcosScheduleMapper;
import com.cfpamf.ms.insur.operation.pco.dto.PcoLevelAdjustDTO;
import com.cfpamf.ms.insur.operation.pco.dto.PcoLevelAdjustExcelDTO;
import com.cfpamf.ms.insur.operation.pco.dto.PcoNewestLevelExcelDTO;
import com.cfpamf.ms.insur.operation.pco.entity.PcoExtendInfo;
import com.cfpamf.ms.insur.operation.pco.enums.PcoLevelEnum;
import com.cfpamf.ms.insur.operation.pco.enums.PcoQualifiedlEnum;
import com.cfpamf.ms.insur.operation.pco.query.*;
import com.cfpamf.ms.insur.operation.pco.util.DownloadUtil;
import com.cfpamf.ms.insur.operation.pco.util.ExcelReadUtils;
import com.cfpamf.ms.insur.operation.pco.validation.PcoLevelValidation;
import com.cfpamf.ms.insur.operation.pco.validation.ValidationResult;
import com.cfpamf.ms.insur.operation.pco.vo.*;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.io.FileInputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class PcoLevelService {
    private ObjectMapper objectMapper;

    private PcoLevelValidation pcoLevelValidation;

    private PcoLevelImportService pcoLevelImportService;

    private PcoLevelInfoMapper pcoLevelInfoMapper;

    private PcosScheduleMapper pcosScheduleMapper;

    private WxCheckAuthorityHelper wxCheckAuthorityHelper;

    private PcoWeeksScoreService pcoWeeksScoreService;

    DataAuthService dataAuthService;

    private PcoExtendInfoMapper pcoExtendInfoMapper;

    /**
     * 微信PCO分级入口页
     * @param openId
     * @param authorization
     * @return
     */
    public PcoLevelList getPcoList(String openId,String authorization){
        PcoLevelList list = new PcoLevelList();

        //获取微信session
        WxSessionVO sessionVO = wxCheckAuthorityHelper.checkAuthority(openId,authorization);

        //钉钉用户信息
        DingUserInfo dingUserInfo = pcoLevelInfoMapper.getDingUserByJobCode(sessionVO.getJobCode());
        log.info("微信用户对应钉钉用户信息 wxOpenId=【{}】,authorization=【{}】, userId=【{}】,userInfo=【{}】", openId,
                authorization,sessionVO.getUserId(), JSON.toJSONString(dingUserInfo));
        list.setDingUserInfo(dingUserInfo);

        PcoLevelQuery query = new PcoLevelQuery();
        dataAuthService.dataAuth(query);
        //渠道PCO分级信息
        list.setPcoLevelStats(pcoLevelInfoMapper.countOfLevel(query));

        //PCO未处理待办事项列表
        PcoScheduleQuery pcoScheduleQuery = new PcoScheduleQuery();
        dataAuthService.dataAuth(pcoScheduleQuery);
        //总部角色不显示待办事项
        if(!StringUtils.isEmpty(pcoScheduleQuery.getRegionName())) {
            pcoScheduleQuery.setBeDispose(1);
            list.setPcoScheduleInfos(pcosScheduleMapper.list(pcoScheduleQuery));
        }
        return list;
    }

    /**
     * 获取PCO分级列表（最新调整记录列表）
     * @param query
     * @return
     */
    public PageInfo<PcoNewestLevelList> getNewestLevelByPage(PcoLevelQuery query){
        if (query.getPage() > 0) {
            PageHelper.startPage(query.getPage(), query.getSize());
        }
        dataAuthService.dataAuth(query);
        List<PcoNewestLevelList> pcoNewestLevelLists = pcoLevelInfoMapper.listOfPCONewestLevel(query);
        return new PageInfo<>(pcoNewestLevelLists);
    }

    /**
     * 根据用户Id获取PCO等级调整信息
     * @param query
     * @return
     */
    public PageInfo<PcoLevelChangeRecord> changeListByUserId(PcoLevelChangeQuery query){
        if (query.getPage() > 0) {
            PageHelper.startPage(query.getPage(), query.getSize());
        }
        List<PcoLevelChangeRecord> pcoLevelChangeRecords = pcoLevelInfoMapper.listByJobCodeFilterSystem(query.getJobCode(),null);
        return new PageInfo<>(pcoLevelChangeRecords);
    }

    /**
     * 根据用户Id获取PCO等级详情
     * @param jobCode
     * @return
     */
    public PcoLevelDetail getLevelDetail(String jobCode){
        PcoLevelDetail detail = new PcoLevelDetail();
        //钉钉用户信息
        DingUserInfo dingUserInfo = pcoLevelInfoMapper.getDingUserByJobCode(jobCode);
        detail.setDingUserInfo(dingUserInfo);

        //最新调整等级记录
        List<PcoLevelChangeRecord> pcoLevelChangeRecords = pcoLevelInfoMapper.listByJobCode(jobCode,1);
        if (CollectionUtils.isNotEmpty(pcoLevelChangeRecords)){
            detail.setNewestLevelInfo(pcoLevelChangeRecords.get(0));
        }

        //周评分列表
        WeeksScoreListQuery query = new WeeksScoreListQuery();
        query.setJobCode(jobCode);
        query.setPage(1);
        query.setSize(6);
        PageInfo<PcoWeeksScoreVo> pcoWeeksScoreVos = pcoWeeksScoreService.getFillWeeksScoreByPage(query);
        detail.setPcoWeeksScoreVos(pcoWeeksScoreVos.getList());
        return detail;
    }

    /**
     * PCO分级管理等级调整导入
     * @param dto
     * @param userId
     */
    public PcoLevelAdjustResult levelAdjust(PcoLevelAdjustDTO dto, String userId) throws Exception{
        String key = ":import:" + userId;
        PcoLevelAdjustResult result =  new PcoLevelAdjustResult();
        List<PcoLevelAdjustExcelDTO> dtos = ExcelReadUtils
                .readWorkbookByStream(
                        DownloadUtil.downloadByUrl(dto.getFileUrl()), PcoLevelAdjustExcelDTO.class
                        , 7, false);
        result.setTotalNumber(dtos.size());

        List<DingUserInfo> dingUserInfo = pcoLevelInfoMapper.queryPcoInfoByUserId();
        Map<String, List<DingUserInfo>> dingUserMap = dingUserInfo
                .stream()
                .collect(Collectors.toMap(DingUserInfo::getUserId,
                        Lists::newArrayList
                        , (l1, l2) -> {
                            l1.addAll(l2);
                            return l1;
                        }));


        //错误信息要还原 把导入 列表复制一份
        List<PcoLevelAdjustExcelDTO> excelClones = objectMapper.convertValue(dtos, new TypeReference<List<PcoLevelAdjustExcelDTO>>() {
        });
        //复制错误数据 并赋值错误信息
        List<PcoLevelAdjustExcelDTO> errorDatas = new ArrayList<>();
        List<ValidationResult<PcoLevelAdjustExcelDTO>> validationResults = pcoLevelValidation.valid(dtos,dingUserMap);

        for (int i = 0; i < validationResults.size(); i++) {
            ValidationResult<PcoLevelAdjustExcelDTO> smOrderExcelDTOValidationResult = validationResults.get(i);
            if (!smOrderExcelDTOValidationResult.isSuccess()) {
                PcoLevelAdjustExcelDTO error = excelClones.get(i);
                error.setErrorMsg(smOrderExcelDTOValidationResult.getMessage());
                errorDatas.add(error);
            }
        }

        Map<Boolean, List<ValidationResult<PcoLevelAdjustExcelDTO>>> validMap
                = validationResults.stream().collect(Collectors.partitioningBy(ValidationResult::isSuccess));

        //校验成功记录
        List<ValidationResult<PcoLevelAdjustExcelDTO>> successDtos = validMap.getOrDefault(Boolean.TRUE, Collections.emptyList());
        //添加调整记录
        addAdjustRecord(successDtos,userId,dingUserMap);

        result.setSuccessNumber(successDtos.size());

        //添加导入记录
        PcoLevelImport res = pcoLevelImportService.addImportRecord(dto, dtos.size() - errorDatas.size(), errorDatas.size(), userId);
        //失败记录处理
        List<ValidationResult<PcoLevelAdjustExcelDTO>> errors = validMap.getOrDefault(Boolean.FALSE, Collections.emptyList());
        result.setErrorNumber(errors.size());
        //有校验出错的数据
        if (!org.springframework.util.CollectionUtils.isEmpty(errorDatas)) {
            pcoLevelImportService.addImportErrorRecord(res, errorDatas, errors);
        }
        result.setErrUrl(res.getErrorUrl());
        return result;
    }

    /**
     * 添加等级调整记录
     * @param successDtos
     */
    private void addAdjustRecord(List<ValidationResult<PcoLevelAdjustExcelDTO>> successDtos,String userId,Map<String, List<DingUserInfo>> dingUserMap) {
        if (CollectionUtils.isEmpty(successDtos)) {
            return;
        }
        List<PcoNewestLevelList> lists = new ArrayList<>();
        List<String> jobCodes = new ArrayList<>();
        List<PcoExtendInfo> pcoExtendInfoList = new ArrayList<>();
        successDtos.forEach(
            validation->{
                PcoLevelAdjustExcelDTO dto = validation.getSource();
                PcoExtendInfo pcoExtendInfo = new PcoExtendInfo();
                List<DingUserInfo> dingUserInfoList = dingUserMap.get(dto.getUserId());
                DingUserInfo userInfo = dingUserInfoList.stream().filter(user->dto.getUserName().equals(user.getUserName())
                        && dto.getRegionName().equals(user.getRegionName())
                        && dto.getOrgName().equals(user.getOrgName()))
                        .findFirst().orElseGet(() -> null);

                if (!Objects.isNull(userInfo)) {
                    PcoNewestLevelList list = new PcoNewestLevelList();
                    BeanUtils.copyProperties(dto, list);
                    BeanUtils.copyProperties(userInfo, list);

                    if (!Objects.isNull(dto.getClaimTimelinessRatio())){
                        list.setClaimTimelinessRatio(dto.getClaimTimelinessRatio().multiply(new BigDecimal(100)).setScale(2)+"%");
                    }

                    list.setPcoLevel(PcoLevelEnum.deName(dto.getPcoLevel()).getValue());
                    list.setBeQualified(PcoQualifiedlEnum.deName(dto.getBeQualified()).getValue());
                    list.setBeNewest(1);
                    list.setCreateBy(userId);

                    BeanUtils.copyProperties(list, pcoExtendInfo);
                    pcoExtendInfo.setJobNumber(list.getUserId());
                    pcoExtendInfoList.add(pcoExtendInfo);
                    jobCodes.add(userInfo.getJobCode());
                    lists.add(list);
                }
            }
        );

        //更新原有调整记录为历史调整
        pcoLevelInfoMapper.updateBeNewest(jobCodes);

        //插入最新调整记录
        pcoLevelInfoMapper.insertLevelList(lists);

        //更新拓展表中的pco等级和胜任状态
        pcoExtendInfoMapper.updateExtendInfo(pcoExtendInfoList);
    }

    /**
     * PCO分级等级调整记录列表查询
     * @param query
     * @return
     */
    public PageInfo<PcoLevelImport> listOfImport(PcoLevelImportQuery query){
        if (query.getPage() > 0) {
            PageHelper.startPage(query.getPage(), query.getSize());
        }
        List<PcoLevelImport> pcoLevelImports = pcoLevelImportService.list(query);
        return new PageInfo<>(pcoLevelImports);
    }

    /**
     * PCO分级列表导出（最新调整记录列表）
     * @param query
     * @return
     */
    public List<PcoNewestLevelExcelDTO> levelNewestExport(PcoLevelQuery query){
        List<PcoNewestLevelList> pcoNewestLevelLists = pcoLevelInfoMapper.listOfPCONewestLevel(query);
        List<PcoNewestLevelExcelDTO> pcoNewestLevelExcelDTOS = pcoNewestLevelLists.stream()
                .map(x->{
                    PcoNewestLevelExcelDTO pcoNewestLevelExcelDTO = new PcoNewestLevelExcelDTO();
                    BeanUtils.copyProperties(x, pcoNewestLevelExcelDTO);
                    if (!StringUtils.isEmpty(x.getPcoLevel())) {
                        pcoNewestLevelExcelDTO.setPcoLevel(PcoLevelEnum.deValue(x.getPcoLevel()).getName());
                    }
                    if(x.getBeQualified()!=null) {
                        pcoNewestLevelExcelDTO.setBeQualified(PcoQualifiedlEnum.deValue(x.getBeQualified()).getName());
                    }
                    return pcoNewestLevelExcelDTO;
                }).collect(Collectors.toList());
        return pcoNewestLevelExcelDTOS;
    }
}
