package com.cfpamf.ms.insur.operation.msg.service.push;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cfpamf.common.ms.exception.MSBizNormalException;
import com.cfpamf.ms.insur.operation.base.response.CommonResult;
import com.cfpamf.ms.insur.operation.base.service.SystemGroovyService;
import com.cfpamf.ms.insur.operation.base.util.DownLoadUtil;
import com.cfpamf.ms.insur.operation.dingtalk.dao.DingGroupConfigMapper;
import com.cfpamf.ms.insur.operation.dingtalk.entity.DingGroupConfig;
import com.cfpamf.ms.insur.operation.fegin.image.facade.DynamicsImageFacade;
import com.cfpamf.ms.insur.operation.fegin.image.facade.InsuranceImageFacade;
import com.cfpamf.ms.insur.operation.fegin.image.service.DynamicsImageService;
import com.cfpamf.ms.insur.operation.msg.dao.OpMessagePushMapper;
import com.cfpamf.ms.insur.operation.msg.dao.OpMessageRuleReceiverMapper;
import com.cfpamf.ms.insur.operation.msg.enums.EnumMessagePushState;
import com.cfpamf.ms.insur.operation.msg.enums.EnumReceiverAuditState;
import com.cfpamf.ms.insur.operation.msg.enums.OpMessageReceiverTypeEnum;
import com.cfpamf.ms.insur.operation.msg.pojo.dto.OpMessageRuleGroovyDTO;
import com.cfpamf.ms.insur.operation.msg.pojo.po.OpMessagePush;
import com.cfpamf.ms.insur.operation.msg.pojo.po.OpMessageRule;
import com.cfpamf.ms.insur.operation.msg.pojo.po.OpMessageRuleReceiver;
import com.cfpamf.ms.insur.operation.msg.pojo.vo.ParamsConfigVo;
import com.cfpamf.ms.insur.operation.msg.service.DingTalkService;
import com.cfpamf.ms.insur.operation.msg.service.push.model.CacheVo;
import lombok.AccessLevel;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.function.Supplier;

/**
 * <AUTHOR> 2021/8/30 11:05
 */
@FieldDefaults(level = AccessLevel.PROTECTED)
@Slf4j
public abstract class AbstractPusher implements Pusher {

    /**
     * 缓存的前缀  push/{规则id}/{contextId}/{recervier}.jpg
     */
    protected static final String CACHE_KEY = "push/%d/%s/%s.jpg";

    protected static final String IMAGE_NATURE = "imageNature";
    @Autowired
    SystemGroovyService groovyService;

    @Autowired
    DingTalkService dingTalkService;

    @Autowired
    OpMessageRuleReceiverMapper receiverMapper;

    @Autowired
    OpMessagePushMapper pushMapper;

    @Autowired
    DingGroupConfigMapper dingGroupConfigMapper;

    @Autowired
    InsuranceImageFacade imageFacade;

    @Autowired
    DynamicsImageService dynamicsImageService;

    protected void saveAuditingMessagePush(OpMessageRuleReceiver receiver,
                                           String contextId,
                                           String content,
                                           String cache) {
        final OpMessagePush push = new OpMessagePush();
        push.setContextId(contextId);
        push.setReceiver(receiver.getReceiver());
        push.setReceiverType(receiver.getReceiverType());
        push.setReceiverName(receiver.getReceiverName());
        push.setMessageRuleId(receiver.getMessageRuleId());
        push.setContent(content);
        push.setMessageCache(cache);
        push.setState(EnumReceiverAuditState.AUDIT.getCode());
        pushMapper.insertUseGeneratedKeys(push);
    }
    protected void saveMultipleAuditingMessagePush(OpMessageRuleReceiver receiver,
                                           String contextId,
                                           String content,
                                           String cache,
                                           String ruleCode,
                                           String bizCode) {
        final OpMessagePush push = new OpMessagePush();
        push.setContextId(contextId);
        push.setReceiver(receiver.getReceiver());
        push.setReceiverType(receiver.getReceiverType());
        push.setReceiverName(receiver.getReceiverName());
        push.setMessageRuleId(receiver.getMessageRuleId());
        push.setContent(content);
        push.setMessageCache(cache);
        push.setBizCode(bizCode);
        push.setRuleCode(ruleCode);
        push.setState(EnumReceiverAuditState.AUDIT.getCode());
        pushMapper.insertUseGeneratedKeys(push);
    }

    protected void updateAuditMessagePush(Long id, String messageId) {
        final OpMessagePush update = new OpMessagePush();
        update.setId(id);
        update.setMessageId(messageId);
        update.setState(EnumMessagePushState.PUSHED.getCode());
        pushMapper.updateByPrimaryKeySelective(update);
    }

    protected OpMessagePush saveMessagePushTodo(OpMessageRuleReceiver receiver,
                                                String content, String contextId, String cache) {

        OpMessagePush push = buildByReceiver(receiver, content, contextId, cache);
        push.setState(EnumMessagePushState.TODO.getCode());
        pushMapper.insertUseGeneratedKeys(push);
        return push;
    }

    protected OpMessagePush saveMultipleMessagePushTodo(OpMessageRuleReceiver receiver,
                                                String content, String contextId, String cache,String ruleCode,
                                                String bizCode) {

        OpMessagePush push = new OpMessagePush();
        push.setMessageId("");
        push.setContextId(contextId);
        push.setReceiver(receiver.getReceiver());
        push.setReceiverType(receiver.getReceiverType());
        push.setReceiverName(receiver.getReceiverName());
        push.setMessageRuleId(receiver.getMessageRuleId());
        push.setContent(content);
        push.setMessageCache(cache);
        push.setBizCode(bizCode);
        push.setRuleCode(ruleCode);
        push.setState(EnumMessagePushState.TODO.getCode());
        pushMapper.insertUseGeneratedKeys(push);
        return push;
    }

    private OpMessagePush buildByReceiver(OpMessageRuleReceiver receiver,
                                          String content, String contextId, String cache) {

        OpMessagePush push = new OpMessagePush();
        push.setMessageId("");
        push.setContextId(contextId);
        push.setReceiver(receiver.getReceiver());
        push.setReceiverType(receiver.getReceiverType());
        push.setReceiverName(receiver.getReceiverName());
        push.setMessageRuleId(receiver.getMessageRuleId());
        push.setContent(content);
        push.setMessageCache(cache);

        return push;
    }

    protected void saveMessagePush(OpMessageRuleReceiver receiver,
                                   String content, String messageId, String contextId, String cache) {
        OpMessagePush push = buildByReceiver(receiver, content, contextId, cache);
        push.setMessageId(messageId);
        push.setState(EnumMessagePushState.PUSHED.getCode());
        pushMapper.insertUseGeneratedKeys(push);
    }

    /**
     * 根据消息id修改状态
     *
     * @param push      推送处理对象
     * @param messageId 为空代表推送失败
     */
    protected void updateByMessageId(OpMessagePush push, String messageId) {

        if (Objects.isNull(push) || Objects.isNull(push.getId())) {
            log.info("推送信息为空:[{}]", messageId);
            return;
        }
        OpMessagePush update = new OpMessagePush();
        update.setId(push.getId());
        update.setMessageId(messageId);
        update.setState(StringUtils.isNotBlank(messageId) ? EnumMessagePushState.PUSHED.getCode() : EnumMessagePushState.FAIL.getCode());
        pushMapper.updateByPrimaryKeySelective(update);
    }

    /**
     * 解析钉钉群参数配置序列化为ParamsConfigVo对象
     * @return
     */
    private ParamsConfigVo getParamsConfig(OpMessageRuleReceiver dbReceiver){
        String groupId = null;
        String groupContextId = null;
        if (OpMessageReceiverTypeEnum.CHAT.isMe(dbReceiver.getReceiverType())
                || OpMessageReceiverTypeEnum.PERSON.isMe(dbReceiver.getReceiverType())) {
            groupId = dbReceiver.getReceiver();
        }else if (OpMessageReceiverTypeEnum.CHAT_CONTEXT.isMe(dbReceiver.getReceiverType())) {
            //该推送类型会推送到目标群,开发配置时需要注意"钉钉和场景"这种类型不能与其他推送方式混合配置,
            //该功能是开发人员使用并已告知其他开发人员,后续会加个配置注意事项文档特别说明
            groupContextId = dbReceiver.getGroupContextId();
        }else {
            return null;
        }
        if(StringUtils.isBlank(groupId) && StringUtils.isBlank(groupContextId)){
            return null;
        }
        List<DingGroupConfig> dingGroupConfigList = dingGroupConfigMapper.queryByGroupIdAndGroupContextId(groupId,groupContextId);
        if(CollectionUtils.isEmpty(dingGroupConfigList)){
            return null;
        }
        if(dingGroupConfigList.size() > 1){
            log.warn("同一个GroupId对应多个参数配置请核实 dingGroupConfigList = {}", JSON.toJSONString(dingGroupConfigList));
        }
        DingGroupConfig dingGroupConfig = dingGroupConfigList.get(0);
        if(StringUtils.isBlank(dingGroupConfig.getParamsConfig())){
            return null;
        }
        ParamsConfigVo vo = JSON.parseObject(dingGroupConfig.getParamsConfig(), ParamsConfigVo.class);
        return vo;
    }

    /**
     * 把钉钉群配置的json参数添加到groovy上下文对象中 如区域列表,片区列表,分支列表
     * @param dto
     */
    protected void fillDataToOpMessageRuleGroovyDTO(OpMessageRuleGroovyDTO dto,OpMessageRuleReceiver receiver){
        ParamsConfigVo paramsConfigVo = getParamsConfig(dto.getReceiver());
        if(Objects.isNull(paramsConfigVo)){
            return;
        }
        //将配置的区域列表转化为SQL查询的value
        dto.setAreaCodeListString(getSqlListString(paramsConfigVo.getAreaCodeList()));
        //将配置的片区列表转化为SQL查询的value
        dto.setDistrictCodeListString(getSqlListString(paramsConfigVo.getDistrictCodeList()));
        //将配置的分支列表转化为SQL查询的value
        dto.setBchCodeListString(getSqlListString(paramsConfigVo.getBchCodeList()));
    }

    /**
     * 将字符串列表转换为适用于SQL查询的字符串格式
     *
     * @param list 字符串列表，代表需要转换的值
     * @return 转换后的字符串，格式为"'value1','value2',..."，如果列表为空返回null
     */
    private String getSqlListString(List<String> list){
        // 检查列表是否为空，如果为空则返回null，避免空列表导致的无效查询
        if(org.springframework.util.CollectionUtils.isEmpty(list)){
            return null;
        }
        // 使用StringBuilder高效拼接字符串
        StringBuilder sb = new StringBuilder();
        // 遍历列表，将每个元素转换为SQL查询友好的格式，并添加到StringBuilder中
        for(String s : list){
            sb.append("'").append(s).append("',");
        }
        // 删除最后一个多余的逗号，确保返回的字符串格式正确
        return sb.substring(0,sb.length()-1);
    }

    /**
     * 查询指定接收者和规则的消息推送数量
     *
     * 此方法主要用于获取特定消息接收者和消息规则下，处于审核状态的消息推送数量它可以用于统计和监控，
     * 以便于了解消息审核的效率和状态
     *
     * @param receiver 消息接收者信息，用于指定查询哪个接收者的消息
     * @param rule 消息规则信息，用于指定查询哪条规则下的消息
     * @return 处于审核状态的消息数量
     */
    protected final Integer getAuditCount(OpMessageRuleReceiver receiver, OpMessageRule rule){
        // 创建查询对象，用于设置查询条件
        OpMessagePush query = new OpMessagePush();
        // 设置消息推送状态为审核中
        query.setState(EnumMessagePushState.AUDIT.getCode());
        // 设置消息接收者
        query.setReceiver(receiver.getReceiver());
        // 设置消息规则ID
        query.setMessageRuleId(rule.getId());
        // 执行查询，返回符合条件的消息数量
        return pushMapper.selectCount(query);
    }


    /**
     * 上传图片到缓存
     * 此方法根据接收消息的类型（个人或群组）上传图片到不同的位置，并返回相应的URL
     * 对于个人消息，图片直接上传至指定的图片服务对于群组消息，图片先上传至临时位置，然后缓存到OSS
     *
     * @param json       包含图片信息的JSON对象
     * @param receiver   消息接收者对象，用于判断是个人还是群组
     * @param html       图片的HTML内容
     * @return CacheVo   包含原始图片URL和缓存后图片URL的对象
     */
    protected CacheVo uploadImg(JSONObject json, OpMessageRuleReceiver receiver, String html,String imageNature) {
        // 将HTML内容放入JSON对象，以便上传图片时包含该信息
        json.put("htmlValue", html);
        //json.put(IMAGE_NATURE,receiver.getImageNature());
        json.put(IMAGE_NATURE,imageNature);
        // 判断消息接收者是否为个人
        if (receiver.isPerson()) {

            // 调用API上传图片，并获取上传后的URL
            //String url = apiCall(() -> imageFacade.image(json)).getUrl();
            String url = apiCall(() -> dynamicsImageService.image(json)).getUrl();
            // 返回包含上传图片URL的对象
            return new CacheVo(url, url);
        } else {
            // 调用API上传图片，并获取上传后的URL
            //String url = apiCall(() -> imageFacade.image(json)).getUrl();
            String url = apiCall(() -> dynamicsImageService.image(json)).getUrl();
            // 从URL下载图片，并将其转换为字节数组
            byte[] bytes = DownLoadUtil.downloadByUrlToByte(url);
            // 记录图片缓存到OSS成功的日志
            log.info("图片缓存到oss成功{}", url);
            // 上传图片到DingTalk，并返回包含上传图片URL和原始URL的对象
            return new CacheVo(dingTalkService.uploadImage(bytes), url);
        }
    }

    /**
     * 统一处理API调用的模板方法
     * 本方法负责调用传入的API供应商，并处理其返回结果
     * 如果API调用成功，则返回API调用结果数据；如果调用失败，则抛出业务异常
     *
     * @param api API供应商，用于提供API调用的接口
     * @param <T> 泛型参数，表示API调用返回的数据类型
     * @return T 类型的数据，来自API调用的成功返回结果
     * @throws MSBizNormalException 如果API调用失败，抛出业务异常，包含错误码和错误信息
     */
    private <T> T apiCall(Supplier<CommonResult<T>> api) {
        CommonResult<T> tCommonResult = api.get(); // 执行API调用，获取返回结果
        if (tCommonResult.isSuccess()) { // 检查API调用是否成功
            return tCommonResult.getData(); // 如果成功，返回API调用结果数据
        }
        throw new MSBizNormalException(tCommonResult.getErrorCode(), tCommonResult.getMessage()); // 如果调用失败，抛出业务异常
    }
}
