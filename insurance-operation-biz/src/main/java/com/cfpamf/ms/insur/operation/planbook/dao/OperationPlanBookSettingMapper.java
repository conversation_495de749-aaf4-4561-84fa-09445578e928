package com.cfpamf.ms.insur.operation.planbook.dao;

import com.cfpamf.ms.insur.operation.base.dao.CommonMapper;
import com.cfpamf.ms.insur.operation.planbook.entity.OperationPlanBookSetting;
import tk.mybatis.mapper.entity.Example;

/**
 * <AUTHOR>
 */
public interface OperationPlanBookSettingMapper extends CommonMapper<OperationPlanBookSetting> {

    /**
     * 查找生效的数据
     * @param planBookId
     * @return
     */
    default OperationPlanBookSetting selectByPlanBookIdWithEnable(Integer planBookId) {
        Example example = new Example(OperationPlanBookSetting.class);
        example.createCriteria().andEqualTo("planBookId", planBookId).andEqualTo("enabledFlag", 0);
        return selectOneByExample(example);
    }

}