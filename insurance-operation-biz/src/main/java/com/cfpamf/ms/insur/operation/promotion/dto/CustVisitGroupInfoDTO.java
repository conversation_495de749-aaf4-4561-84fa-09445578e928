package com.cfpamf.ms.insur.operation.promotion.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class CustVisitGroupInfoDTO {

    /**
     * 素材类型
     */
    @ApiModelProperty(value = "素材类型")
    private String marketingType;

    /**
     * 素材分类
     */
    @ApiModelProperty(value = "素材分类")
    private String marketingCategory;

    /**
     * 用户名称
     */
    @ApiModelProperty(value = "用户名称")
    private String customerName;

    /**
     * 素材分类
     */
    @ApiModelProperty(value = "微信头像信息")
    private String avatarUrl;

    /**
     * 素材分类
     */
    @ApiModelProperty(value = "用户手机号码")
    private String mobile;

    /**
     * 转化标识
     */
    @ApiModelProperty(value = "转化标识")
    private String transformFlag;

    /**
     * 素材分类
     */
    @ApiModelProperty(value = "跟进标识")
    private String followFlag;

    /**
     * 素材分类
     */
    @ApiModelProperty(value = "标题")
    private String title;

    /**
     * 素材分类
     */
    @ApiModelProperty(value = "跟进建议")
    private String followAdvice;

    /**
     * 素材分类
     */
    @ApiModelProperty(value = "素材分类")
    private String shareTime;

    /**
     * 素材分类
     */
    @ApiModelProperty(value = "素材分类")
    private String visitorTime;
}
