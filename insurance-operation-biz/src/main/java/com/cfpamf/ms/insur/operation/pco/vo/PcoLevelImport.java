package com.cfpamf.ms.insur.operation.pco.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.OrderBy;
import javax.persistence.Table;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@ApiModel
@Table(name = "pco_level_import")
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class PcoLevelImport{

    @ApiModelProperty("文件名称 ")
    @Column(name = "file_name")
    String fileName;

    @ApiModelProperty("文件上传地址（注意传相对地址）")
    @Column(name = "file_url")
    String fileUrl;

    @ApiModelProperty("成功行数")
    @Column(name = "success")
    Integer success;

    @ApiModelProperty("失败行数")
    @Column(name = "error")
    Integer error;

    @ApiModelProperty("失败文件地址")
    @Column(name = "error_url")
    String errorUrl;

    @ApiModelProperty("操作人")
    String createBy;

    @ApiModelProperty("导入时间")
    LocalDateTime createTime;

    @Id
    @OrderBy("desc")
    protected Integer id;

    /**
     * 是否删除
     */
    @ApiModelProperty(hidden = true)
    protected Integer enabledFlag = 0;

}
