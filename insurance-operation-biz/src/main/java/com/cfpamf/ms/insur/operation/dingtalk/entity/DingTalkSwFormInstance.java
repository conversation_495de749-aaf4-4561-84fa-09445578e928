package com.cfpamf.ms.insur.operation.dingtalk.entity;

import com.cfpamf.ms.insur.operation.base.entity.BaseNoUserEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import javax.persistence.Column;
import javax.persistence.Table;

/**
 * 钉钉智能填表实例
 *
 * <AUTHOR>
 */
@ApiModel
@Table(name = "ding_talk_sw_form_instance")
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class DingTalkSwFormInstance extends BaseNoUserEntity {


    @ApiModelProperty("表单编码")
    @Column(name = "form_code")
    String formCode;

    @ApiModelProperty("提交人用户id")
    @Column(name = "submit_user_id")
    String submitUserId;

    @ApiModelProperty("提交名字")
    @Column(name = "submit_user_name")
    String submitUserName;

    @ApiModelProperty("提交时间")
    @Column(name = "submit_time")
    java.time.LocalDateTime submitTime;

    @ApiModelProperty("表单内容中的日期")
    @Column(name = "form_date")
    java.time.LocalDate formDate;

    @ApiModelProperty("创建者")
    @Column(name = "form_instance_id")
    String formInstanceId;

    @ApiModelProperty("单日自评分数")
    Integer scope;


}
