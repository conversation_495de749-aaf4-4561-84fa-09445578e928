package com.cfpamf.ms.insur.operation.promotion.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.util.List;

@Data
public class PutDownMarketingDTO {

    /**
     * 素材大类
     */
    @ApiModelProperty(value = "素材大类 理赔:4001;时事热点:1002;产品:4003;理念科普:4004")
    private Integer categoryCode;
    /**
     * 来源系统
     */
    @ApiModelProperty(value = "来源系统编码")
    private String sourceSystemCode;

    /**
     * 来源系统素材编码
     */
    @ApiModelProperty(value = "来源系统素材编码")
    private String materialCode;
}
