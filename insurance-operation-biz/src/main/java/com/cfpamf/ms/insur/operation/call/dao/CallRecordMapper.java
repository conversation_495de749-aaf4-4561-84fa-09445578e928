package com.cfpamf.ms.insur.operation.call.dao;

import com.cfpamf.ms.insur.operation.base.dao.CommonMapper;
import com.cfpamf.ms.insur.operation.call.entity.CallRecordPo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 呼叫记录数据访问层
 *
 * <AUTHOR>
 * @date 2025-07-22
 */
@Mapper
public interface CallRecordMapper extends CommonMapper<CallRecordPo> {

    /**
     * 根据保单号查询呼叫记录
     *
     * @param policyNo 保单号
     * @return 呼叫记录列表
     */
    List<CallRecordPo> selectByPolicyNo(@Param("policyNo") String policyNo);

    /**
     * 查询未获取录音的记录
     *
     * @param limit 限制数量
     * @return 呼叫记录列表
     */
    List<CallRecordPo> selectUnRecordedCalls(@Param("limit") Integer limit);

    /**
     * 查询已获取录音但未匹配跟进记录的记录
     *
     * @param limit 限制数量
     * @return 呼叫记录列表
     */
    List<CallRecordPo> selectUnMatchedFollowCalls(@Param("limit") Integer limit);

    /**
     * 根据呼叫SID查询记录
     *
     * @param callSid 呼叫SID
     * @return 呼叫记录
     */
    CallRecordPo selectByCallSid(@Param("callSid") String callSid);

    /**
     * 更新录音信息
     *
     * @param id 记录ID
     * @param recordUrl 录音地址
     * @param recordDuration 录音时长
     * @param recordStatus 录音状态
     */
    void updateRecordInfo(@Param("id") Long id, 
                         @Param("recordUrl") String recordUrl, 
                         @Param("recordDuration") String recordDuration, 
                         @Param("recordStatus") Integer recordStatus);

    /**
     * 更新跟进匹配信息
     *
     * @param id 记录ID
     * @param followRecordId 跟进记录ID
     * @param followMatchStatus 匹配状态
     */
    void updateFollowMatchInfo(@Param("id") Long id, 
                              @Param("followRecordId") Long followRecordId, 
                              @Param("followMatchStatus") Integer followMatchStatus);
}
