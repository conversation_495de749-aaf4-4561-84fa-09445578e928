package com.cfpamf.ms.insur.operation.promotion.service.impl;

import com.alibaba.fastjson.JSON;
import com.cfpamf.cmis.common.utils.DateUtils;
import com.cfpamf.common.ms.exception.MSBizNormalException;
import com.cfpamf.ms.insur.operation.base.response.CommonResult;
import com.cfpamf.ms.insur.operation.base.util.AliYunOssUtil;
import com.cfpamf.ms.insur.operation.fegin.bms.enums.ExcptEnum;
import com.cfpamf.ms.insur.operation.fegin.oms.request.MarketSubmitRequest;
import com.cfpamf.ms.insur.operation.fegin.oms.request.MarketingAttachmentAddDTOS;
import com.cfpamf.ms.insur.operation.fegin.oms.response.MarketingForwardGroupByDateDTO;
import com.cfpamf.ms.insur.operation.fegin.oms.response.MarketingForwardListQueryOutputDTO;
import com.cfpamf.ms.insur.operation.fegin.oms.response.MarketingLinkDTO;
import com.cfpamf.ms.insur.operation.fegin.oms.response.OmsBaseResponse;
import com.cfpamf.ms.insur.operation.fegin.wx.request.CustVisitGroupInfo;
import com.cfpamf.ms.insur.operation.fegin.wx.request.CustVisitGroupListQueryInput;
import com.cfpamf.ms.insur.operation.fegin.wx.request.CustVisitGroupOutput;
import com.cfpamf.ms.insur.operation.fegin.wx.request.CustomerGroupVisitGroupQueryInput;
import com.cfpamf.ms.insur.operation.order.dao.SmOrderInsuredMapper;
import com.cfpamf.ms.insur.operation.promotion.constants.MarketingConstants;
import com.cfpamf.ms.insur.operation.promotion.dao.OperationSynMaterialInfoMapper;
import com.cfpamf.ms.insur.operation.promotion.dto.*;
import com.cfpamf.ms.insur.operation.promotion.entity.OperationSynMaterialInfoEntity;
import com.cfpamf.ms.insur.operation.promotion.enums.*;
import com.cfpamf.ms.insur.operation.promotion.service.MarketingService;
import com.cfpamf.ms.insur.operation.promotion.service.OmsBaseService;
import com.cfpamf.ms.insur.operation.promotion.service.OperationSynMaterialInfoService;
import com.cfpamf.ms.insur.operation.xj.service.impl.WhalePublicApiBaseService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.ProtocolException;
import java.net.URL;
import java.nio.channels.Channels;
import java.nio.channels.ReadableByteChannel;
import java.text.ParseException;
import java.util.*;
import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service("marketingService")
public class MarketingServiceImpl implements MarketingService {

    @Autowired
    private OperationSynMaterialInfoService materialInfoService;

    @Value("${marketing.applet-home}")
    private String appletHome;

    @Value("${marketing.create-job-number}")
    private String createJobNumber;

    @Resource(name="omsBaseService")
    private OmsBaseService omsBaseService;

    @Autowired
    private OperationSynMaterialInfoService operationSynMaterialInfoService;

    @Autowired
    private OperationSynMaterialInfoMapper operationSynMaterialInfoMapper;

    @Autowired
    SmOrderInsuredMapper smOrderInsuredMapper;

    @Value("${marketing.product-default-pic}")
    private String productDefaultPic;

    //素材圈上传素材到OSS参数
    private static final String MARKETING_PREVIEW_UPLOAD_OSS_KEY = "%s-%d-%s";

    @Autowired
    private WhalePublicApiBaseService whalePublicApiBaseService;

    @Override
    public Long addMaterial(MarketingInfoDTO dto) {
        //枚举值转换
        MarketingAttachmentTypeEnum marketingAttachmentTypeEnum = MarketingAttachmentTypeEnum.decode(dto.getAttachmentType());
        InsureMarketingCategoryEnum categoryEnum = InsureMarketingCategoryEnum.decode(buildMaterialLevelCode(dto.getLv1CategoryCode(),dto.getLv2CategoryCode()));
        List<MarketingAttachmentAddDTOS> marketingAttachmentAddDTOS = null;
        //初始化素材同步实体
        OperationSynMaterialInfoEntity operationSynMaterialInfoEntity = initOperationSynMaterialInfoEntity(dto);
        //封装请求附件对象
        if(!CollectionUtils.isEmpty(dto.getMarketingAttachmentAddDTOS())){
            marketingAttachmentAddDTOS = dto.getMarketingAttachmentAddDTOS().stream().map(item -> {
                String fileUrl = picHandle(dto,item);
                MarketingAttachmentAddDTOS marketingAttachmentAddDTO = MarketingAttachmentAddDTOS.builder()
                        .folderName(MarketingOptSystemEnum.INSURE.getSystemCode())
                        .originalFileName(item.getOriginalFileName())
                        .fileUrl(fileUrl)
                        .build();
                return marketingAttachmentAddDTO;
            }).collect(Collectors.toList());
        }
        //封装请求对象
        MarketSubmitRequest request = MarketSubmitRequest.builder()
                .attachmentType(marketingAttachmentTypeEnum.getAttachmentType())
                .bizType(MarketingBizTypeEnum.INSUR.getBizCode())
                .content(dto.getContent())
                .attachmentRemark(categoryEnum.getCategoryName())
                .lv1CategoryCode(dto.getLv1CategoryCode())
                .lv2CategoryCode(dto.getLv2CategoryCode())
                .goodsNo(dto.getProductCode()).marketingAttachmentAddDTOS(marketingAttachmentAddDTOS).build();
        //封装默认值
        request = fillDefaultInfo(request);
        //调用创建跳转链接接口
        Long marketingLinkId = getMarketingLinkId(dto,operationSynMaterialInfoEntity);
        request.setLinkId(marketingLinkId.intValue());
        OmsBaseResponse<Long> response = omsBaseService.marketingSubmit(request);
        Long materialResponseId = response.getData();
        //素材圈返回的素材ID更新到营销自己的同步表
        operationSynMaterialInfoMapper.updateMaterialResponseId(operationSynMaterialInfoEntity.getId(), marketingLinkId.intValue(), materialResponseId, MarketingConstants.MARKETING_STATUS_SYN_SUCCESS);
        return materialResponseId;
    }

    @Override
    public void putDownMaterial(PutDownMarketingDTO putDownMarketingDTO) {
        //数据初始化
        String materialCode = putDownMarketingDTO.getMaterialCode();
        String sourceSystemCode = putDownMarketingDTO.getSourceSystemCode();
        Integer categoryCode = putDownMarketingDTO.getCategoryCode();
        OperationSynMaterialInfoEntity operationSynMaterialInfoEntity = operationSynMaterialInfoService.selectByMaterialCodeSystemCode(materialCode,sourceSystemCode,categoryCode,MarketingConstants.MARKETING_STATUS_SYN_SUCCESS);
        if(Objects.isNull(operationSynMaterialInfoEntity)){
            log.info("历史数据未同步素材圈所以不需要下架 req= {}",JSON.toJSONString(putDownMarketingDTO));
            //历史数据未同步素材圈所以不需要下架
            return;
        }
        //已同步素材圈下架处理
        List<Long> marketingIdList = Lists.newArrayList(Long.valueOf(operationSynMaterialInfoEntity.getMaterialResponseId()));
        MarketingLaunchDTO marketingLaunchDTO = new MarketingLaunchDTO();
        marketingLaunchDTO.setIdList(marketingIdList);
        marketingLaunchDTO.setLaunchOperation(MarketingOperationEnum.PUT_DOWN_MATERIAL.getOperationCode());
        marketingLaunchDTO.setRemark(MarketingConstants.MARKETING_OPT_PUT_DOWN_DEFAULT_REMARK);
        omsBaseService.launch(marketingLaunchDTO);
        operationSynMaterialInfoMapper.updateMaterialResponseId(operationSynMaterialInfoEntity.getId(), null, operationSynMaterialInfoEntity.getMaterialResponseId(), MarketingConstants.MARKETING_STATUS_PUT_DOWN);
    }

    /**
     * 调用创建跳转链接接口
     * @param dto
     * @return
     */
    private Long addMarketingLink(MarketingInfoDTO dto,OperationSynMaterialInfoEntity operationSynMaterialInfoEntity){
        MarketingLinkDTO marketingLinkDTO = null;
        String linkPath = appletHome;
        linkPath = linkpathTransfer(dto,linkPath);
        //根据不同主题封装跳转链接对象
        if(InsureMarketingCategoryEnum.PRODUCT.getCategoryCode().equals(operationSynMaterialInfoEntity.getMaterialLevelCode())){
            //设置产品跳转链接
            marketingLinkDTO = buildMarketingLinkDTO(dto,InsureMarketingCategoryEnum.PRODUCT.getCategoryName(), MarketingLinkTypeEnum.MINI_PROGRAM.getLinkType(),linkPath);
        }else if(InsureMarketingCategoryEnum.PAYMENT_OF_CLAIMS.getCategoryCode().equals(operationSynMaterialInfoEntity.getMaterialLevelCode())){
            //设置理赔跳转链接
            marketingLinkDTO = buildMarketingLinkDTO(dto,InsureMarketingCategoryEnum.PAYMENT_OF_CLAIMS.getCategoryName(),MarketingLinkTypeEnum.MINI_PROGRAM.getLinkType(),linkPath);
        }else {
            log.error("素材主题不匹配 req= {}",JSON.toJSONString(dto));
            throw new MSBizNormalException("", "素材主题不匹配");
        }
        return omsBaseService.marketingLinkSubmit(marketingLinkDTO).getData();
    }

    /**
     * 构建跳转链接
     * @param dto
     * @param linkName
     * @param linkType
     * @param linkPath
     * @return
     */
    private MarketingLinkDTO buildMarketingLinkDTO(MarketingInfoDTO dto,String linkName,Integer linkType,String linkPath){
        if(StringUtils.isNotBlank(dto.getProductName())){
            linkName = dto.getProductName();
        }else{
            linkName = linkName+System.currentTimeMillis();
        }
        return MarketingLinkDTO.builder()
                .bizType(MarketingBizTypeEnum.INSUR.getBizCode())
                .linkName(linkName)
                .linkType(linkType)
                .linkPath(linkPath)
                .firstChannel(MarketingShareChannelEnum.INSURE_HELP.getShareChannelCode())
                .firstChannelDesc(MarketingShareChannelEnum.INSURE_HELP.getShareChannelName())
                .optSystem(MarketingOptSystemEnum.INSURE).build();
    }

    /**
     * 填充素材默认值
     * @param request
     * @return
     */
    private MarketSubmitRequest fillDefaultInfo(MarketSubmitRequest request){
        //展示方式（0 默认按素材文件 1 商品主图 2 商品详情图）
        request.setDisplayMode(0);
        //视频/图片来源 1-个人原创 2-网络下载已购买商用版权 3-网络下载未购买商用版权
        request.setFileSource(0);
        //文件类型（0 未知无 1 图片 2 视频）
        request.setFileType(1);
        //文字来源 1-无文字 2-不清楚文字字体 3-已知文字字体
        request.setTextSource(3);
        //字体信息 1-宋体 2-黑体 3-楷体 4-其他字体（不可用）
        request.setFrontType(1);
        request.setOptSystem(MarketingOptSystemEnum.INSURE);
        request.setAuthorName(this.createJobNumber);
        return request;
    }

    /**
     * 初始化素材同步实体
     * @param dto
     */
    private OperationSynMaterialInfoEntity initOperationSynMaterialInfoEntity(MarketingInfoDTO dto){
        OperationSynMaterialInfoEntity operationSynMaterialInfoEntity = new OperationSynMaterialInfoEntity();
        //该字段不用了,改动materialLevelCode
        operationSynMaterialInfoEntity.setMaterialCategory(0);
        operationSynMaterialInfoEntity.setMaterialCode(dto.getMaterialCode());
        operationSynMaterialInfoEntity.setProductCode(dto.getProductCode());
        operationSynMaterialInfoEntity.setMaterialType(dto.getAttachmentType());
        operationSynMaterialInfoEntity.setProductType(dto.getProductType());
        operationSynMaterialInfoEntity.setSourceSystemCode(dto.getSourceSystemCode());
        //获取素材级别编码
        String materialLevelCode = buildMaterialLevelCode(dto.getLv1CategoryCode(),dto.getLv2CategoryCode());
        operationSynMaterialInfoEntity.setMaterialLevelCode(materialLevelCode);
        //素材状态 1-待同步 2-已同步 3-同步失败
        operationSynMaterialInfoEntity.setMaterialStatus(1);
        operationSynMaterialInfoEntity.setDeleted(0);
        operationSynMaterialInfoEntity.setCreateTime(new Date());
        operationSynMaterialInfoEntity.setUpdateTime(new Date());
        operationSynMaterialInfoMapper.insertUseGeneratedKeys(operationSynMaterialInfoEntity);
        return operationSynMaterialInfoEntity;
    }

    private Long getMarketingLinkId(MarketingInfoDTO dto,OperationSynMaterialInfoEntity operationSynMaterialInfoEntity){
        Long marketingLinkId = null;
        if(Objects.equals(InsureMarketingCategoryEnum.PRODUCT.getCategoryCode(),operationSynMaterialInfoEntity.getMaterialLevelCode())){
            if(StringUtils.isBlank(dto.getProductCode())){
                log.warn("同步产品素材产品编码不能为空");
                throw new MSBizNormalException(ExcptEnum.PARAMS_ERROR.getCode(),"该产品已存在费率配置，不能重复配置！");
            }
            marketingLinkId = operationSynMaterialInfoService.queryLinkIdByProductCode(dto.getProductCode());
        }else if(Objects.equals(InsureMarketingCategoryEnum.PAYMENT_OF_CLAIMS.getCategoryCode(),operationSynMaterialInfoEntity.getMaterialLevelCode())){
            if(StringUtils.isBlank(dto.getProductCode())){
                //如果理赔没有商品编码则默认取最近一个没有商品编码的跳转链接Id
                marketingLinkId = operationSynMaterialInfoService.queryDefaultLinkId(operationSynMaterialInfoEntity.getMaterialLevelCode());
                dto.setProductName(InsureMarketingCategoryEnum.PAYMENT_OF_CLAIMS.getCategoryName());
            }else{
                marketingLinkId = operationSynMaterialInfoService.queryLinkIdByProductCode(dto.getProductCode());
            }

        }else if(Objects.equals(InsureMarketingCategoryEnum.AFFAIRS_HOTSPOTS.getCategoryCode(),operationSynMaterialInfoEntity.getMaterialLevelCode())){
            marketingLinkId = operationSynMaterialInfoService.queryLinkIdByMaterialCategory(operationSynMaterialInfoEntity.getMaterialLevelCode());
            dto.setProductName(InsureMarketingCategoryEnum.AFFAIRS_HOTSPOTS.getCategoryName());
        }else if(Objects.equals(InsureMarketingCategoryEnum.INSURANCE_SCIENCE.getCategoryCode(),operationSynMaterialInfoEntity.getMaterialLevelCode())){
            marketingLinkId = operationSynMaterialInfoService.queryLinkIdByMaterialCategory(operationSynMaterialInfoEntity.getMaterialLevelCode());
            dto.setProductName(InsureMarketingCategoryEnum.INSURANCE_SCIENCE.getCategoryName());
        }

        if(marketingLinkId == null || Objects.equals(0,marketingLinkId)){
            marketingLinkId = addMarketingLink(dto,operationSynMaterialInfoEntity);
        }
        if(Objects.isNull(marketingLinkId)){
            log.warn("创建跳转链接失败 req= {}",JSON.toJSONString(dto));
            throw new MSBizNormalException("", "创建跳转链接失败");
        }

        return marketingLinkId;
    }

    /**
     * 跳转链接路径转换,理赔会存在没有商品编码的情况,如果没有的话不设置产品编码
     * @param dto
     * @param linkPath
     * @return
     */
    private String linkpathTransfer(MarketingInfoDTO dto,String linkPath){
        if(StringUtils.isNotBlank(dto.getProductCode())){
            linkPath = linkPath.replace("#productCode#",dto.getProductCode());
        }else{
            linkPath = linkPath.replace("#productCode#","");
        }
        return linkPath;
    }


    private String picHandle(MarketingInfoDTO dto, MarketingAttachmentDTO item){
        String ossUrl = null;
        String materialLevelCode = buildMaterialLevelCode(dto.getLv1CategoryCode(),dto.getLv2CategoryCode());
        if(!(
                Objects.equals(MarketingAttachmentTypeEnum.IMAGE.getAttachmentType(),dto.getAttachmentType())
                        && (Objects.equals(InsureMarketingCategoryEnum.PRODUCT.getCategoryCode(),materialLevelCode))
        )){
            return item.getFileUrl();
        }
        //远程下载图片对象
        BufferedImage bufferedImage = null;
        //本地图片对象
        BufferedImage localBufferedImage = null;
        try {
            bufferedImage = downLoadPic(dto,item.getFileUrl());
            localBufferedImage = getLocalBufferedImage();
            ossUrl = picAppendHandleToOss(bufferedImage,localBufferedImage);
        } catch (IOException e) {
            log.error("下载素材图片异常",e);
            throw new MSBizNormalException("", "下载图片异常");
        }
        return ossUrl;
    }

    private BufferedImage downLoadPic(MarketingInfoDTO dto, String imageUrl) throws IOException {
        BufferedImage bufferedImage = null;
        InputStream inputStream = null;
        try{
            // 创建URL对象
            URL url = new URL(imageUrl);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            // 设置请求方法为GET
            connection.setRequestMethod("GET");
            // 默认情况下，HttpURLConnection会自动处理重定向，但为了明确起见，可以设置允许重定向
            connection.setInstanceFollowRedirects(true);
            // 设置连接超时和读取超时
            connection.setConnectTimeout(3000); // 链接超时时间3秒,这里不做特别配置了
            connection.setReadTimeout(5000); // 处理超时时间5秒,这里不做特别配置了
            // 检查HTTP响应码是否成功
            if (connection.getResponseCode() != HttpURLConnection.HTTP_OK) {
                throw new MSBizNormalException("", "创建跳转链接失败");
            }
            inputStream = connection.getInputStream();
            bufferedImage = ImageIO.read(inputStream);
        }catch(Exception e){
            log.error("下载素材图片异常",e);
            throw new MSBizNormalException("", "下载图片异常");
        }finally {
            if(inputStream != null){
                inputStream.close();
            }
        }
        return bufferedImage;
    }

    private BufferedImage getLocalBufferedImage() throws IOException {
        org.springframework.core.io.Resource resource = new ClassPathResource(productDefaultPic);
        InputStream inputStream = null;
        BufferedImage bufferedImage = null;
        try{
            inputStream = resource.getInputStream();
            bufferedImage = ImageIO.read(inputStream);
        }catch(Exception e){
            log.error("本地图片转换处理异常",e);
            throw new MSBizNormalException("", "本地图片转换处理异常");
        }finally {
            if(inputStream != null){
                inputStream.close();
            }
        }
        return bufferedImage;
    }

    /**
     * 图片拼接处理
     * @param image1    远程图片
     * @param image2    本地默认拼接图片
     */
    private String picAppendHandleToOss(BufferedImage image1, BufferedImage image2) throws IOException {
        String ossUrl = null;
        ByteArrayOutputStream outputStream = null;
        int width = Math.max(image1.getWidth(), image2.getWidth());
        int height = image1.getHeight()+ image2.getHeight();

        // 创建一个新的BufferedImage来存放拼接后的图片
        BufferedImage combinedImage = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);

        // 获取Graphics2D对象来进行绘制
        Graphics2D g = combinedImage.createGraphics();
        g.setBackground(Color.WHITE);
        g.fillRect(0, 0, width, height);
        // 绘制第一张图片
        g.drawImage(image1, 0, 0, null);

        // 绘制第二张图片，从第一张图片的下侧开始
        g.drawImage(image2, 0, image1.getHeight(), null);
        // 关闭Graphics2D上下文
        g.dispose();
        outputStream = new ByteArrayOutputStream();
        try {
            ImageIO.write(combinedImage, "png", outputStream);
            ossUrl = AliYunOssUtil.uploadByBytes(outputStream.toByteArray(),
                    String.format(
                            MARKETING_PREVIEW_UPLOAD_OSS_KEY,
                            "PREVIEW",
                            System.currentTimeMillis(),
                            UUID.randomUUID().toString().replaceAll("-", "")
                    ));
        }catch (Exception e){
            log.error("图片拼接处理异常",e);
            throw new MSBizNormalException("", "图片拼接处理异常");
        }finally {
            if(outputStream != null){
                outputStream.close();
            }
        }
        return ossUrl;
    }

    @Override
    public CustVisitGroupOutput queryCustVisitGroupList(CustVisitGroupListQueryInput custVisitGroupListQueryInput) {
        MarketingForwardListQueryDTO marketingForwardListQueryDTO = new MarketingForwardListQueryDTO();
        marketingForwardListQueryDTO.setManageCode(custVisitGroupListQueryInput.getManageCode());
        marketingForwardListQueryDTO.setPage(custVisitGroupListQueryInput.getPageNum()-1);
        marketingForwardListQueryDTO.setSize(custVisitGroupListQueryInput.getPageSize());
        OmsBaseResponse<MarketingForwardListQueryOutputDTO> outputDTO = omsBaseService.forwardListForInsure(marketingForwardListQueryDTO);
        MarketingForwardListQueryOutputDTO marketingForwardListQueryOutputDTO = outputDTO.getData();
        if(Objects.isNull(outputDTO) || Objects.isNull(outputDTO.getData()) || CollectionUtils.isEmpty(marketingForwardListQueryOutputDTO.getRecords())){
            CustVisitGroupOutput output = new CustVisitGroupOutput();
            output.setTotal(0);
            output.setList(Lists.newArrayList());
            return output;
        }
        List<MarketingForwardGroupByDateDTO> list = Lists.newArrayList();
        MarketingForwardGroupByDateDTO forwardGroupByDateDTO = null;
        String materialLevelCode = null;
        for(MarketingForwardGroupByDateDTO dto : marketingForwardListQueryOutputDTO.getRecords()){
            materialLevelCode = buildMaterialLevelCode(dto.getLv1CategoryCode(),dto.getLv2CategoryCode());
            //因素材圈错误数据没有过滤,临时兼容
            if(Objects.isNull(dto.getMarketingId())){
                continue;
            }
            //过滤掉目前不支持的素材主题
            if(filterMarketingCategory(materialLevelCode)){
                continue;
            }
            forwardGroupByDateDTO = new MarketingForwardGroupByDateDTO();
            forwardGroupByDateDTO.setMarketingId(dto.getMarketingId());
            forwardGroupByDateDTO.setTopicType(dto.getTopicType());
            forwardGroupByDateDTO.setFileType(dto.getFileType());
            forwardGroupByDateDTO.setGoodsNo(dto.getGoodsNo());

            forwardGroupByDateDTO.setLv1CategoryCode(dto.getLv1CategoryCode());
            forwardGroupByDateDTO.setLv2CategoryCode(dto.getLv2CategoryCode());
            list.add(forwardGroupByDateDTO);
        }
        //如果素材主题为空，则返回空
        if(CollectionUtils.isEmpty(list)){
            CustVisitGroupOutput output = new CustVisitGroupOutput();
            output.setTotal(0);
            output.setList(Lists.newArrayList());
            return output;
        }
        CustomerGroupVisitGroupQueryInput customerGroupVisitGroupQueryInput = new CustomerGroupVisitGroupQueryInput();
        BeanUtils.copyProperties(custVisitGroupListQueryInput,customerGroupVisitGroupQueryInput);
        customerGroupVisitGroupQueryInput.setMarketingForwardGroupByDateDTOList(list);
        log.info("queryCustVisitGroupList req= {}",JSON.toJSONString(customerGroupVisitGroupQueryInput));
        CustVisitGroupOutput output = whalePublicApiBaseService.queryCustVisitGroupList(customerGroupVisitGroupQueryInput);
        return buildCustVisitGroupOutput(marketingForwardListQueryOutputDTO,output);
    }

    private CustVisitGroupOutput buildCustVisitGroupOutput(MarketingForwardListQueryOutputDTO marketingForwardListQueryOutputDTO, CustVisitGroupOutput output){
        CustVisitGroupOutput out = new CustVisitGroupOutput();
        List<MarketingForwardGroupByDateDTO> marketingForwardGroupByDateDTOList = marketingForwardListQueryOutputDTO.getRecords();
        List<CustVisitGroupInfo> list = output.getList();
        Map<String,CustVisitGroupInfo> custVisitGroupInfoMap = null;
        if(CollectionUtils.isEmpty(list)){
            custVisitGroupInfoMap = Maps.newHashMap();
        }else{
            custVisitGroupInfoMap = list.stream().collect(Collectors.toMap(CustVisitGroupInfo::getMarketingCode, Function.identity()));
        }
        List<CustVisitGroupInfo> outList = Lists.newArrayList();
        out.setList(outList);
        out.setTotal(marketingForwardListQueryOutputDTO.getTotal().intValue());
        CustVisitGroupInfo tmpGroupInfo = null;
        CustVisitGroupInfo outGroupInfo = null;
        Date shareTime = null;
        String materialLevelCode = null;
        for(MarketingForwardGroupByDateDTO dto : marketingForwardGroupByDateDTOList){
            materialLevelCode = buildMaterialLevelCode(dto.getLv1CategoryCode(),dto.getLv2CategoryCode());
            //因素材圈错误数据没有过滤,临时兼容
            if(Objects.isNull(dto.getMarketingId())){
                continue;
            }
            //过滤掉目前不支持的素材主题
            if(filterMarketingCategory(materialLevelCode)){
                continue;
            }
            try {
                shareTime = DateUtils.convertToDate(DateUtils.FORMAT_TIME,dto.getForwardTimeStr());
            } catch (ParseException e) {
                log.error("分享时间转换异常forwardTimeStr= {}",dto.getForwardTimeStr(),e);
                throw new MSBizNormalException("", "分享时间转换异常forwardTimeStr= "+dto.getForwardTimeStr());
            }
            //本地临时对象
            tmpGroupInfo = custVisitGroupInfoMap.get(String.valueOf(dto.getMarketingId()));
            //把素材一级和二级分类编码拼接
            materialLevelCode = buildMaterialLevelCode(dto.getLv1CategoryCode(),dto.getLv2CategoryCode());
            //对外输出对象
            outGroupInfo = new CustVisitGroupInfo();
            if(tmpGroupInfo != null){
                outGroupInfo.setMarketingCode(tmpGroupInfo.getMarketingCode());
                outGroupInfo.setContent(tmpGroupInfo.getContent());
                outGroupInfo.setAvatarUrlList(tmpGroupInfo.getAvatarUrlList());
                if(StringUtils.isNotBlank(dto.getGoodsNo())){
                    outGroupInfo.setContent(tmpGroupInfo.getContent());
                }else{
                    outGroupInfo.setContent(InsureMarketingCategoryEnum.decode(materialLevelCode).getCategoryName());
                }
                outGroupInfo.setPeople(tmpGroupInfo.getPeople());
            }else{
                outGroupInfo.setMarketingCode(String.valueOf(dto.getMarketingId()));
                outGroupInfo.setContent(InsureMarketingCategoryEnum.decode(materialLevelCode).getCategoryName());
                outGroupInfo.setAvatarUrlList(Lists.newArrayList());
                outGroupInfo.setPeople("0");
            }
            outGroupInfo.setLv1CategoryCode(dto.getLv1CategoryCode());
            outGroupInfo.setLv2CategoryCode(dto.getLv2CategoryCode());
            outGroupInfo.setFirstPicUrl(dto.getThumbnailUrl());
            outGroupInfo.setShareTime(getShareTime(shareTime));
            outGroupInfo.setMarketingCategory(InsureMarketingCategoryEnum.decode(materialLevelCode).getCategoryName());
            outGroupInfo.setMarketingType(OmsMarketingAttachmentTypeEnum.decode(dto.getFileType()).getAttachmentTypeName());
            outList.add(outGroupInfo);
        }
        return out;
    }

    /**
     * 聚合视图分享时间处理
     * @param shareTime
     * @return
     */
    private String getShareTime(Date shareTime){
        Date now = new Date();
        String yyyyMMdd = "yyyy-MM-dd";
        if(Objects.equals(DateUtils.format(now,yyyyMMdd),DateUtils.format(shareTime,yyyyMMdd))){
            return DateUtils.format(shareTime,"HH:mm");
        }else{
            return DateUtils.format(shareTime,"yyyy-MM-dd");
        }
    }

    /**
     * 过滤掉目前不支持的素材主题
     * @param materialLevelCode
     * @return
     */
    private boolean filterMarketingCategory(String materialLevelCode){
        if(Objects.equals(InsureMarketingCategoryEnum.AFFAIRS_HOTSPOTS.getCategoryCode(),materialLevelCode)){
            return false;
        }else if(Objects.equals(InsureMarketingCategoryEnum.INSURANCE_SCIENCE.getCategoryCode(),materialLevelCode)){
            return false;
        }else if(Objects.equals(InsureMarketingCategoryEnum.PRODUCT.getCategoryCode(),materialLevelCode)){
            return false;
        }else if(Objects.equals(InsureMarketingCategoryEnum.PAYMENT_OF_CLAIMS.getCategoryCode(),materialLevelCode)){
            return false;
        }else{
            return true;
        }
    }

    /**
     * 拼接素材圈一级和二级编码作为我们的素材级别
     * @param lv1CategoryCode 素材一级分类编码
     * @param lv2CategoryCode 素材二级分类编码
     * @return
     */
    private String buildMaterialLevelCode(String lv1CategoryCode,String lv2CategoryCode){
        return lv1CategoryCode+"_"+lv2CategoryCode;
    }
}
