package com.cfpamf.ms.insur.operation.activity.util;

import com.alibaba.fastjson.JSONArray;
import com.cfpamf.ms.insur.operation.activity.enums.EnumMathOperator;
import com.cfpamf.ms.insur.operation.activity.form.SystemActivityConstRuleForm;
import com.cfpamf.ms.insur.operation.activity.form.SystemActivityConstRuleParamForm;
import lombok.experimental.UtilityClass;

import java.math.BigDecimal;
import java.util.Iterator;
import java.util.List;
import java.util.StringJoiner;

/**
 * <AUTHOR> 2022/6/28 14:54
 */
@UtilityClass
public class ActivityConstOpUtils {

    /**
     * 计算规则
     *
     * @param actualVal
     * @param rule
     * @return
     */
    public static boolean executeRule(Object actualVal, SystemActivityConstRuleForm rule) {
        EnumMathOperator enumMathOperator = EnumMathOperator.valueOfExpression(rule.getParams().getCompareSymbol());
        return enumMathOperator.execute(rule.getParams().getValue(), actualVal);
    }

    /**
     * 把配置的条件转换成SQL
     *
     * @param rule
     * @return
     */
    public static String toSql(SystemActivityConstRuleForm rule) {
        SystemActivityConstRuleParamForm params = rule.getParams();
        EnumMathOperator enumMathOperator = EnumMathOperator.valueOfExpression(params.getCompareSymbol());
        StringJoiner joiner = new StringJoiner(" ");
        switch (enumMathOperator) {
            //常规的判断直接拼接
            case GT:
            case G:
            case L:

            case LT:
            case T:
                return joiner.add(enumMathOperator.getExpression()).add(new BigDecimal(params.getValue()).toString()).toString();
            // 拼接范围查询
            case BETWEEN:
                List<BigDecimal> bigDecimals = JSONArray.parseArray(params.getValue(), BigDecimal.class);
                Iterator<BigDecimal> iterator = bigDecimals.iterator();
                return joiner.add(enumMathOperator.getExpression()).add(iterator.next().toString()).add("and")
                        .add(iterator.next().toString()).toString();
            default:
                return "";
        }

    }

}
