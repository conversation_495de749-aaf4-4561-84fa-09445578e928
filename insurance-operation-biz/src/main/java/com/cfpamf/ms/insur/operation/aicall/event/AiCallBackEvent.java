package com.cfpamf.ms.insur.operation.aicall.event;

import com.cfpamf.ms.insur.operation.base.event.BaseEvent;
import lombok.Data;

import java.util.List;

@Data
public class AiCallBackEvent implements BaseEvent {
    /**
     * taskType	String	是	业务端创建任务时指定的任务类型
     * taskId	bigint	是	本地ai电话任务id
     * outSourceId	String	是	外部记录关联Id
     * mobile	String	是	电话号码
     * durationTimeLen	int	是	拨号总时长，单位为秒
     * ringingTimeLen	int	是	振铃时长，单位为秒
     * talkingTimeLen	int	是	对话时长，单位为秒
     * startTime	long	是	呼叫开始时间-Unix时间戳(单位:毫秒)
     * endType	int	是	接通状态，1-已接通 0-未接通	1
     * endTypeReason	string	是	未接通原因，详见 endTypeReason 列表	"notexist"
     * ringStartTime	long	是	振铃开始时间-Unix时间戳(单位:毫秒)
     * recordJson	Object	是	对话轮次json
     * intent	String		意图
     * recordUrl	String	录音文件试听URL
     * downloadUrl	String	录音文件下载URL
     * contactUUID String 获取录音文件id
     * callTimes 拨打次数
     */
    private String taskType;
    private Long taskId;
    private Long outSourceId;
    private String mobile;
    private Integer durationTimeLen;
    private Integer ringingTimeLen;
    private Integer talkingTimeLen;
    private Long startTime;
    private Integer endType;
    private String endTypeReason;
    private Long ringStartTime;
    private List<BackRecord> records;
    private String intent;
    private String individualTag;
    private String recordUrl;
    private String downloadUrl;
    private String contactUUID;
    private Integer callTimes;
}
