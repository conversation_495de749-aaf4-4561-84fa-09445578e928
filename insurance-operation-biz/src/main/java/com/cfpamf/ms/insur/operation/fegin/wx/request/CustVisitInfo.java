package com.cfpamf.ms.insur.operation.fegin.wx.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class CustVisitInfo {

    /**
     * 访客记录
     */
    @ApiModelProperty(value = "访客记录")
    private Integer visitId;

    /**
     * 素材分类
     */
    @ApiModelProperty(value = "用户手机号码")
    private String mobile;

    /**
     * 素材分类
     */
    @ApiModelProperty(value = "文案")
    private String content;

    /**
     * 素材分类
     */
    @ApiModelProperty(value = "跟进建议")
    private String followAdvice;

    /**
     * 素材类型
     */
    @ApiModelProperty(value = "素材类型")
    private String marketingType;

    /**
     * 素材分类
     */
    @ApiModelProperty(value = "素材分类")
    private String marketingCategory;

    /**
     * 用户名称
     */
    @ApiModelProperty(value = "用户名称")
    private String customerName;

    /**
     * 素材分类
     */
    @ApiModelProperty(value = "微信头像信息")
    private String avatarUrl;

    /**
     * 跟进状态 0 访问未注册 ; 1 已注册且访问后未投保 ; 2 注册且访问后有投保
     */
    @ApiModelProperty(value = "跟进状态 0 访问未注册 ; 1 已注册且访问后未投保 ; 2 注册且访问后有投保")
    private Integer followStatus;

    /**
     * 跟进标识 0 未跟进, 1 已跟进
     */
    @ApiModelProperty(value = "跟进标识 0 未跟进, 1 已跟进")
    private Integer followFlag;

    /**
     * 访问时间
     */
    @ApiModelProperty(value = "访问时间")
    private String visitorTime;

    /**
     * 素材圈编码
     */
    @ApiModelProperty(value = "素材圈编码")
    private String marketingCode;

    /**
     * openId
     */
    @ApiModelProperty(value = "openId")
    private String openId;

    /**
     * 商品名称
     */
    @ApiModelProperty(value = "商品编码")
    private String productCode;

    @ApiModelProperty(value = "客户经理编码")
    private String managerCode;

    @ApiModelProperty(value = "下一页页码")
    private Integer nextpage;

    @ApiModelProperty(value = "总条数")
    private Integer total;

    @ApiModelProperty(value = "客户注册标识")
    private boolean registerFlag;

    @ApiModelProperty("一级分类编码")
    private String lv1CategoryCode;
    @ApiModelProperty("二级分类编码")
    private String lv2CategoryCode;
}
