package com.cfpamf.ms.insur.operation.whale.preservation;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * @author: yangdonglin
 * @create: 2023/12/15 10:08
 * @description: 投保人基础信息变更保全
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "投保人基础信息变更保全", description = "投保人基础信息变更保全")
public class EpPreservationApplicantInfoChangeReq {

    @ApiModelProperty(value = "合同编码")
    private String contractCode;

    @ApiModelProperty(value = "关联投保人id")
    private Integer applicantId;

    @ApiModelProperty(value = "类型：0=变更前信息，1=变更后信息")
    private Integer type;

    @ApiModelProperty(value = "投保主体类型 0:非自然人; 1:自然人")
    private Integer applicantType;

    @ApiModelProperty(value = "投保主体证件类型")
    private String applicantIdType;

    @ApiModelProperty(value = "投保主体证件号")
    private String applicantIdCard;

    @ApiModelProperty(value = "投保人名称")
    private String applicantName;

    @ApiModelProperty(value = "投保主体证件起始时间")
    private Date applicantIdCardValidityStart;

    @ApiModelProperty(value = "投保主体证件截止时间")
    private Date applicantIdCardValidityEnd;

    @ApiModelProperty(value = "投保主体证件永久有效 0：否；1：是")
    private Integer applicantIdCardPermanent;

    @ApiModelProperty(value = "投保人性别 0：女 1：男")
    private String applicantGender;

    @ApiModelProperty(value = "投保人手机号")
    private String applicantMobile;

    @ApiModelProperty(value = "投保人投保时年龄")
    private Integer applicantAge;

    @ApiModelProperty(value = "投保人出生日期")
    private Date applicantBirthday;

    @ApiModelProperty(value = "投保人国籍")
    private String applicantNation;

    @ApiModelProperty(value = "投保人民族")
    private String applicantNationality;

    @ApiModelProperty(value = "投保人学历")
    private String applicantEducation;

    @ApiModelProperty(value = "投保人婚姻状况")
    private String applicantMarital;

    @ApiModelProperty(value = "投保人年收入")
    private String applicantAnnualIncome;

    @ApiModelProperty(value = "投保人职业")
    private String applicantCareer;

    @ApiModelProperty(value = "投保人工作单位")
    private String applicantCompany;

    @ApiModelProperty(value = "投保人职位")
    private String applicantPosition;

    @ApiModelProperty(value = "投保人邮箱")
    private String applicantEmail;

    @ApiModelProperty(value = "投保人是否拥有社会医疗保险 0:否 1:是")
    private Integer applicantMedicare;

    @ApiModelProperty(value = "投保人职业类别")
    private String applicantOccupationalCategory;

    @ApiModelProperty(value = "投保主体行业类别")
    private String applicantIndustryCategory;

    @ApiModelProperty(value = "单位性质")
    private String companyNature;

    @ApiModelProperty(value = "投保主体地址邮编")
    private String applicantPostcode;

    @ApiModelProperty(value = "投保主体省份编码")
    private String applicantProvinceCode;

    @ApiModelProperty(value = "投保主体城市编码")
    private String applicantCityCode;

    @ApiModelProperty(value = "投保主体区域编码")
    private String applicantRegionCode;

    @ApiModelProperty(value = "投保主体详细地址")
    private String applicantAddress;

    @ApiModelProperty(value = "其他联系电话")
    private String applicantTelephone;

    @ApiModelProperty(value = "社保登记号")
    private String companySocialSecurityNum;

    @ApiModelProperty(value = "单位总人数")
    private String companyEmployeeNum;

    @ApiModelProperty(value = "单位联系人姓名")
    private String companyContactName;

    @ApiModelProperty(value = "单位联系电话")
    private String companyContactMobile;

    @ApiModelProperty(value = "法人姓名")
    private String legalPersonName;

    @ApiModelProperty(value = "法人证件类型")
    private String legalPersonIdType;

    @ApiModelProperty(value = "法人证件号码")
    private String legalPersonIdCard;

    @ApiModelProperty(value = "法人证件起始时间")
    private Date legalPersonIdCardValidityStart;

    @ApiModelProperty(value = "法人证件截至时间")
    private Date legalPersonIdCardValidityEnd;

    @ApiModelProperty(value = "法人证件永久有效 0：否；1：是")
    private Integer legalPersonIdCardPermanent;

    @ApiModelProperty(value = "银行编码")
    private String bankCode;

    @ApiModelProperty(value = "银行名称")
    private String bankName;

    @ApiModelProperty(value = "银行卡号")
    private String cardNo;

    @ApiModelProperty(value = "银行卡名称")
    private String cardName;

    @ApiModelProperty(value = "缴费期次")
    private Integer paymentPhase;

    @ApiModelProperty(value = "备注")
    private String remark;
}
