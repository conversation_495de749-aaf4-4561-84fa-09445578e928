package com.cfpamf.ms.insur.operation.base.config;


import com.alibaba.druid.pool.DruidDataSource;
import com.cfpamf.ms.insur.operation.base.helper.DataSourceHolder;
import com.google.common.collect.Maps;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.SqlSessionTemplate;
import org.mybatis.spring.boot.autoconfigure.MybatisProperties;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.jdbc.datasource.lookup.AbstractRoutingDataSource;
import tk.mybatis.spring.annotation.MapperScan;

import javax.sql.DataSource;
import java.util.Map;

/**
 * <AUTHOR>
@Configuration
@EnableConfigurationProperties(MybatisProperties.class)
@MapperScan(basePackages = {"com.cfpamf.ms.insur.operation.prospectus.dao", "com.cfpamf.ms.insur.operation.log.dao", "com.cfpamf.ms.insur.operation.activity.dao", "com.cfpamf.ms.insur.operation.msg.dao", "com.cfpamf.ms.insur.operation.reward.redenvelope.dao", "com.cfpamf.ms.insur.operation.planbook.dao", "com.cfpamf.ms.insur.operation.reward.addcommission.dao"
        ,
        "com.cfpamf.ms.insur.operation.qy.dao",
        "com.cfpamf.ms.insur.operation.phoenix.dao",
        "com.cfpamf.ms.insur.operation.pco.dao",
        "com.cfpamf.ms.insur.operation.pk.dao",
        "com.cfpamf.ms.insur.operation.customer.dao",
        "com.cfpamf.ms.insur.operation.honor.dao",
        "com.cfpamf.ms.insur.operation.auto.dao",
        "com.cfpamf.ms.insur.operation.order.dao",
        "com.cfpamf.ms.insur.operation.dingtalk.dao",
        "com.cfpamf.ms.insur.operation.aicall.dao",
        "com.cfpamf.ms.insur.operation.claim.dao",
        "com.cfpamf.ms.insur.operation.promotion.dao",
        "com.cfpamf.ms.insur.operation.addCommission.dao",
        "com.cfpamf.ms.insur.operation.ai.coze.dao",
        "com.cfpamf.ms.insur.operation.call.dao"
},
        sqlSessionFactoryRef = "sqlSessionFactorySafes",
        sqlSessionTemplateRef = "sqlSessionTemplateSafes")
public class DataSourceSafesConfig {
    static final String MAPPER_LOCATION = "classpath*:mapper/safes/*.xml";

    final
    MybatisProperties properties;

    @Autowired(required = false)
    public DataSourceSafesConfig(MybatisProperties properties) {
        this.properties = properties;
    }

    @ConfigurationProperties(prefix = "spring.datasource.safes-read")
    @Bean(name = "readSafesDataSource")
    public DataSource readSafesDataSource() {
        return new DruidDataSource();
    }

    @ConfigurationProperties(prefix = "spring.datasource.safes-write")
    @Bean(name = "writeSafesDataSource")
    public DataSource writeSafesDataSource() {
        return new DruidDataSource();
    }

    @Bean(name = "safeRoutingDataSource")
    public ReadWriteRoutingDataSource routingDataSource(
            @Qualifier("readSafesDataSource") DataSource readDataSource,
            @Qualifier("writeSafesDataSource") DataSource writeDataSource) {

        final ReadWriteRoutingDataSource readWriteRoutingDataSource = new ReadWriteRoutingDataSource();

        final Map<Object, Object> map = Maps.newHashMapWithExpectedSize(2);
        map.put(DataSourceHolder.DataBaseType.READ, readDataSource);
        map.put(DataSourceHolder.DataBaseType.WRITE, writeDataSource);
        readWriteRoutingDataSource.setDefaultTargetDataSource(writeDataSource);
        readWriteRoutingDataSource.setTargetDataSources(map);
        return readWriteRoutingDataSource;
    }

    @Bean(name = "sqlSessionFactorySafes")
    public SqlSessionFactory sqlSessionFactorySafes(@Qualifier("safeRoutingDataSource") DataSource dataSource) throws Exception {
        final SqlSessionFactoryBean sessionFactory = new SqlSessionFactoryBean();
        sessionFactory.setDataSource(dataSource);
        applyConfiguration(sessionFactory);
        Resource[] resources = new PathMatchingResourcePatternResolver().getResources(MAPPER_LOCATION);
        sessionFactory.setMapperLocations(resources);
        return sessionFactory.getObject();
    }

    /**
     * @param dataSource
     * @return
     */
    @Bean("safeJdbcTemplate")
    @Primary
    public JdbcTemplate safeJdbcTemplate(@Qualifier("safeRoutingDataSource") DataSource dataSource) {
        return new JdbcTemplate(dataSource);
    }

    @Bean(name = "dataSourceTransactionManagerSafes")
    @Primary
    public DataSourceTransactionManager dataSourceTransactionManagerSafes(@Qualifier("safeRoutingDataSource") DataSource dataSource) {
        return new DataSourceTransactionManager(dataSource);
    }

    @Bean(name = "sqlSessionTemplateSafes")
    public SqlSessionTemplate sqlSessionTemplateSafes(@Qualifier("sqlSessionFactorySafes") SqlSessionFactory sqlSessionFactory) throws Exception {
        return new SqlSessionTemplate(sqlSessionFactory);
    }

    private void applyConfiguration(SqlSessionFactoryBean factory) {
        org.apache.ibatis.session.Configuration configuration = new org.apache.ibatis.session.Configuration();
        configuration.setMapUnderscoreToCamelCase(true);
        factory.setConfiguration(configuration);
    }


    static class ReadWriteRoutingDataSource extends AbstractRoutingDataSource {
        @Override
        protected Object determineCurrentLookupKey() {
            return DataSourceHolder.get();
        }
    }

}
