package com.cfpamf.ms.insur.operation.activity.dao;


import com.cfpamf.ms.insur.operation.activity.entity.SystemActivityProductRule;
import com.cfpamf.ms.insur.operation.activity.entity.SystemActivityProductRuleWhale;
import com.cfpamf.ms.insur.operation.activity.vo.SystemActivityProductRuleVo;
import com.cfpamf.ms.insur.operation.base.dao.CommonMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface SystemActivityProductRuleWhaleMapper extends CommonMapper<SystemActivityProductRuleWhale> {
    /**
     * 通过活动产品id获取素所有的规则
     *
     * @param systemActivityProductIdList
     * @return
     */
    List<SystemActivityProductRuleVo> getBySystemActivityProductIdList(@Param("systemActivityProductIdList") List<Long> systemActivityProductIdList);

    /**
     * 通过活动产品id软删除所有的规则
     * @param systemActivityProductIdList
     */
    void softDeleteBySystemActivityProductIdList(@Param("systemActivityProductIdList") List<Long> systemActivityProductIdList);

    void insertCopyProductRule(Long oldsystemActivityProductId, String currentUserId, Long systemActivityProductId);
}