package com.cfpamf.ms.insur.operation.activity.form;

import com.alibaba.fastjson.JSON;
import com.cfpamf.ms.insur.operation.activity.enums.ActivityObject;
import com.cfpamf.ms.insur.operation.activity.enums.ActivityType;
import com.cfpamf.ms.insur.operation.activity.enums.ConflictRule;
import com.cfpamf.ms.insur.operation.activity.enums.EnumActivityConfigType;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections4.CollectionUtils;
import org.hibernate.validator.constraints.Length;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 活动表单
 *
 * <AUTHOR>
 * @date 2021/7/5 14:32
 */
@Getter
@Setter
@ApiModel("活动方案表单")
public class SystemActivityProgrammeForm {

    /**
     * 活动标题
     */
    @ApiModelProperty(value = "活动标题")
    @NotBlank(message = "标题不能为空")
    @Length(max = 20, message = "标题不能超过20字")
    private String title;

    /**
     * 活动类型 normal-普通活动 commission-加佣 ticket-抽奖券
     */
    @ApiModelProperty(value = "活动类型 NORMAL-普通活动 ADD_COMMISSION-加佣 TICKET-抽奖券 RED_ENVELOPE-红包")
    @NotNull(message = "活动类型不能为空")
    private ActivityType type;
    /**
     * 活动对象
     */
    @ApiModelProperty(value = "活动对象 STAFF_MANAGER-员工+客户经理 USER-普通用户 VILLAGE_AGENT-村代")
    @NotNull(message = "活动对象不能为空")
    private ActivityObject activityObject;
    /**
     * 优惠类型 overlay-叠加 optimal-最优
     */
    @ApiModelProperty(value = "优惠类型 OVERLAY-叠加 OPTIMAL-最优")
    private ConflictRule conflictRule;

    @ApiModelProperty(value = "配置类型 GROOVY-脚本 CONST-手动",
            allowableValues = "GROOVY,CONST", example = "GROOVY")
    private EnumActivityConfigType configType;

    @ApiModelProperty(value = "背景图")
    private String backgroundImageUrl;
    /**
     * 列表图片
     */
    @NotBlank(message = "列表图片不能为空")
    @ApiModelProperty(value = "列表图片")
    private String imageUrl;

    /**
     * 活动时间From
     */
    @ApiModelProperty(value = "活动时间From")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @NotNull(message = "活动时间不能为空")
    private LocalDateTime startTime;

    /**
     * 活动时间To
     */
    @ApiModelProperty(value = "活动时间To")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @NotNull(message = "活动时间不能为空")
    private LocalDateTime endTime;

    /**
     * 活动介绍
     */
    @ApiModelProperty(value = "活动介绍")
    private String content;

    /**
     * 活动区域
     */
    @ApiModelProperty(value = "活动区域")
    @NotEmpty(message = "活动区域不能为空")
    private List<String> regionList;

    /**
     * 推荐产品
     */
    @ApiModelProperty(value = "推荐产品")
    private List<Long> productList;

    /**
     * els活动编码
     */
    @ApiModelProperty(value = "els活动编码")
    private Integer elsActivityNumber;

    /**
     * els赠送口令
     */
    @ApiModelProperty(value = "els赠送口令")
    private String elsGrantPassword;

    /**
     * 活动平台
     */
    @ApiModelProperty(value = "活动平台：nb农保,xj小鲸(后续都默认平台为小鲸)")
    private String activityPlatform = "nb";

    @ApiModelProperty(value = "兑换机制:monthly按月兑现，end活动结束兑现")
    private String rewardMechanism;

    /**
     * 关联活动
     */
    @ApiModelProperty(value = "关联活动")
    @Valid
    private List<SystemActivityRefForm> systemActivityRefList;

    /**
     * 活动方案产品表单
     */
    @Valid
    @ApiModelProperty(value = "活动产品以及规则奖励集合")
    private List<SystemActivityProductForm> systemActivityProductList;

    @Valid
    @ApiModelProperty("手动配置的奖励规则")
    private SystemActivityConstRuleWrapperForm constRule;

    @JsonIgnore
    public String getRegionJson() {
        return JSON.toJSONString(regionList);
    }

    @JsonIgnore
    public String getProductsJson() {
        if (CollectionUtils.isNotEmpty(productList)) {
            return JSON.toJSONString(productList);
        }
        return JSON.toJSONString(CollectionUtils.EMPTY_COLLECTION);
    }

    @JsonIgnore
    public boolean isConst() {
        return configType == EnumActivityConfigType.CONST;
    }
}
