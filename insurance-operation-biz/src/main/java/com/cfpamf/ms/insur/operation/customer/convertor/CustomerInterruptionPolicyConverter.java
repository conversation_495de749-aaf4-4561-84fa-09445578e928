package com.cfpamf.ms.insur.operation.customer.convertor;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.cfpamf.ms.insur.operation.customer.dto.OrderCustomerDto;
import com.cfpamf.ms.insur.operation.customer.po.CustomerInterruptionPo;
import com.cfpamf.ms.insur.operation.customer.po.CustomerInterruptionPolicyPo;
import com.cfpamf.ms.insur.operation.order.dto.SmOrderDto;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;


/**
 * <AUTHOR>
 */
@Mapper(imports = {JSONObject.class, JSONArray.class, JSON.class})
public interface CustomerInterruptionPolicyConverter {
    CustomerInterruptionPolicyConverter INS = Mappers.getMapper(CustomerInterruptionPolicyConverter.class);


    /**
     * DTO转PO
     *
     * @param dto
     * @param orderDto
     * @param interruptionPo
     * @return
     */
    @Mappings({
            @Mapping(target = "idNumber", source = "dto.idNumber"),
            @Mapping(target = "idType", source = "dto.idType"),
            @Mapping(target = "customerName", source = "dto.personName"),
            @Mapping(target = "policyNo", source = "dto.policyNo"),
            @Mapping(target = "fhOrderId", source = "dto.fhOrderId"),
            @Mapping(target = "policyState", source = "dto.appStatus"),
            @Mapping(target = "amount", source = "orderDto.totalAmount"),
            @Mapping(target = "customerAdmin", source = "orderDto.customerAdminId"),
            @Mapping(target = "recommend", source = "orderDto.recommendId"),
            @Mapping(target = "renewState", source = "interruptionPo.renewState"),
            @Mapping(target = "renewEmp", source = "interruptionPo.renewEmp"),
            @Mapping(target = "renewTime", source = "orderDto.accountTime"),
            @Mapping(target = "id", source = "dto.id"),
            @Mapping(target = "enabledFlag", source = "dto.enabledFlag")
    })
    CustomerInterruptionPolicyPo dtoToPo(OrderCustomerDto dto, SmOrderDto orderDto, CustomerInterruptionPo interruptionPo);
}
