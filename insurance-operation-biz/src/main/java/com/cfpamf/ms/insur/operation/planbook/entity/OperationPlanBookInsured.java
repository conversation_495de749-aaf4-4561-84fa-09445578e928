package com.cfpamf.ms.insur.operation.planbook.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;

/**
 * operation_plan_book_insured
 *
 * <AUTHOR>
@ApiModel(value = "generate.OperationPlanBookInsured")
@Data
public class OperationPlanBookInsured {
    private Integer id;

    /**
     * 计划书id
     */
    @ApiModelProperty(value = "计划书id")
    private Integer planBookId;

    /**
     * 性别
     */
    @ApiModelProperty(value = "性别")
    private String personGender;

    /**
     * 出生日期
     */
    @ApiModelProperty(value = "出生日期")
    private LocalDate birthday;

}