package com.cfpamf.ms.insur.operation.reward.service;

import com.cfpamf.ms.insur.operation.activity.entity.SmActivityReward;
import com.cfpamf.ms.insur.operation.activity.vo.SystemActivityProgrammeVo;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/1/6 15:41
 */
public interface RewardService {

    /**
     * 监听触发发送奖励
     *
     * @param smActivityRewardList
     */
    void awardByListener(List<SmActivityReward> smActivityRewardList);

    /**
     * 任务触发发放奖励
     *
     * @param systemActivityProgrammeVo 活动详情
     * @param dataIdMap                 key->奖励对象唯一标识（userId,orderId,...）  value-> 奖励数量
     */
    void awardByJob(SystemActivityProgrammeVo systemActivityProgrammeVo, Map<String, BigDecimal> dataIdMap);
    /**
     * 任务触发发放奖励
     *
     * @param systemActivityProgrammeVo 活动详情
     * @param dataIdMap                 key->奖励对象唯一标识（userId,orderId,...）  value-> 奖励数量
     */
    void awardByJobV2(SystemActivityProgrammeVo systemActivityProgrammeVo, Map<String, BigDecimal> dataIdMap, Map<String, BigDecimal> dataIdAmountMap);


    /**
     * 回收奖励
     *
     * @param saId
     * @param softDelete
     */
    void takeBackReward(Long saId, boolean softDelete);

}
