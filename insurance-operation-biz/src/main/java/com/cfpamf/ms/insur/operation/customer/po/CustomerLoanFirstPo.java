package com.cfpamf.ms.insur.operation.customer.po;

import com.cfpamf.ms.insur.operation.base.po.BasePO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import javax.persistence.Column;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since Value(YearOfEra, 4, 19, EXCEEDS_PAD)'-'Value(MonthOfYear,2)'-'Value(DayOfMonth,2)' 'Value(HourOfDay,2)':'Value(MinuteOfHour,2)':'Value(DayOfMonth,2)
 */

@ApiModel("A类客户")
@Table(name = "customer_loan_first")
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class CustomerLoanFirstPo extends BasePO {


    @ApiModelProperty("客户id")
    Long customerId;

    @ApiModelProperty("证件号码")
    String idNumber;

    @ApiModelProperty("客户名称")
    String customerName;
    @ApiModelProperty("信贷客户编码")
    String loanCustId;

    @ApiModelProperty("客户身份 借款人、共借人、担保人")
    String loanCustRole;

    @ApiModelProperty("管护经理工号")
    String customerAdmin;
    @ApiModelProperty("手机号")
    String insurancePhone;

    @ApiModelProperty("最近一笔放款/授信金额")
    BigDecimal recentlyLoanAmt;

    @ApiModelProperty("最近一笔放款/授信时间")
    LocalDateTime recentlyLoanDate;

    @ApiModelProperty("最近一笔借据编号")
    String recentlyLoanNo;

    @ApiModelProperty("转化时间")
    java.time.LocalDateTime conversionTime;

    @ApiModelProperty("转化订单")
    String conversionOrderId;

    @ApiModelProperty("转化状态")
    Integer conversionState;

    @ApiModelProperty("转化员工")
    String conversionEmp;

    @ApiModelProperty("是否生成待办")
    Integer todoState;

}
