package com.cfpamf.ms.insur.operation.base.exception;


import com.cfpamf.common.ms.enums.MSEnum;
import com.cfpamf.ms.insur.operation.fegin.bms.enums.ExcptEnum;
import lombok.Data;

/**
 * Title: 业务异常
 * Build: 2020-01-06 18:19:32
 *
 * <AUTHOR>
 */
@Data
public class BusinessException extends RuntimeException {
    private static final long serialVersionUID = 3893749155794053407L;
    private String message;
    private String code;

    public BusinessException(String code, String message) {
        this.code = code;
        this.message = message;
    }

    public BusinessException(ExcptEnum enm) {
        this.message = enm.getMsg();
        this.code = enm.getCode();
    }

    public BusinessException(MSEnum enm) {
        this.message = enm.getMsg();
        this.code = enm.getCode();
    }

    public BusinessException(MSEnum enm, Throwable e) {
        super(enm.getMsg(), e);
        this.message = enm.getMsg();
        this.code = enm.getCode();
    }

    public static BusinessException buildException(String code, String message) {
        return new BusinessException(code, message);
    }

    public static void checkAndThrow(Boolean expression, BusinessException exception) {
        if (expression) {
            throw exception;
        }
    }

    public static void checkAndThrow(Boolean expression, String code, String message) {
        if (expression) {
            throw buildException(code, message);
        }
    }
}
