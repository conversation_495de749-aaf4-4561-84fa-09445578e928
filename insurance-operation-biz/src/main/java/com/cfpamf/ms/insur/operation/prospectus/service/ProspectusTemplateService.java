package com.cfpamf.ms.insur.operation.prospectus.service;

import com.cfpamf.ms.insur.operation.prospectus.vo.ProspectusTemplateConfigVo;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 计划书模板配置业务层接口
 *
 * <AUTHOR>
 * @date 2021/5/19 14:54
 */
@Service
public interface ProspectusTemplateService {

    /**
     * 修改计划书模板配置
     *
     * @param coverFileList
     */

    void updateTemplate(List<String> coverFileList);

    /**
     * 查看计划书配置详情
     *
     * @return
     */
    ProspectusTemplateConfigVo getById();


}
