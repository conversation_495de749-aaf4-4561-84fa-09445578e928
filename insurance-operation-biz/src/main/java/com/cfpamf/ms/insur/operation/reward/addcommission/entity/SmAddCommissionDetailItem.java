package com.cfpamf.ms.insur.operation.reward.addcommission.entity;

import com.cfpamf.ms.insur.operation.base.entity.BaseNoUserEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.Objects;

import lombok.Data;

import javax.persistence.Column;

/**
 * sm_add_commission_detail_item
 * <AUTHOR>
@ApiModel(value="com.cfpamf.ms.insur.operation.reward.addcommission.SmAddCommissionDetailItem加佣详情表")
@Data
public class SmAddCommissionDetailItem extends BaseNoUserEntity  {


    /**
     * 订单id
     */
    @ApiModelProperty(value="订单id")
    private String orderId;

    /**
     * 保单号
     */
    @ApiModelProperty(value="保单号")
    private String policyNo;

    /**
     * 被保人证件号
     */
    @ApiModelProperty(value="被保人证件号")
    private String insuredIdNumber;

    /**
     * 计划id
     */
    @ApiModelProperty(value="计划id")
    private Integer planId;

    /**
     * 险种id，险种不存在时默认-1
     */
    @ApiModelProperty(value="险种id，险种不存在时默认-1")
    private Integer riskId;

    /**
     * 期数
     */
    @ApiModelProperty(value="期数")
    private Integer termNum;

    /**
     * 加佣类型 ACTIVTY-活动加佣
     */
    @ApiModelProperty(value="加佣类型 ACTIVTY-活动加佣")
    private String commissionType;

    /**
     * 加佣比例
     */
    @ApiModelProperty(value="加佣比例")
    private BigDecimal proportion;

    /**
     * 数据标识 如活动id
     */
    @ApiModelProperty(value="数据标识 如活动id")
    private String dataId;

    /**
     * 唯一标识，唯一索引字段值拼接而成 拼接字符|
     */
    @ApiModelProperty(value="唯一标识，唯一索引字段值拼接而成 拼接字符|")
    private String uuid;
    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private LocalDateTime createTime;
    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private LocalDateTime updateTime;

    public Integer getRiskId() {
        return Objects.nonNull(riskId) ? riskId: -1;
    }
}