package com.cfpamf.ms.insur.operation.msg.enums;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.experimental.FieldDefaults;

/**
 * <AUTHOR> 2021/7/15 17:06
 */
@Getter
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public enum EnumReceiverAuditState {
    /**
     * 初始化
     */
    AUTO(1, "自动推送"),
    /**
     * 已推送
     */
    AUDIT(2, "审核推送"),
    ;

    Integer code;

    String desc;

    public boolean isMe(Integer code) {
        return this.code.equals(code);
    }
}
