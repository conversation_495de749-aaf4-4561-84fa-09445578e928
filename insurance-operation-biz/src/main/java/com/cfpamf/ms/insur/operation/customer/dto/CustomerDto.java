package com.cfpamf.ms.insur.operation.customer.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

/**
 * <AUTHOR>
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
@ApiModel
public class CustomerDto{

    @ApiModelProperty(value = "证件号码")
    String idNumber;

    @ApiModelProperty(value = "姓名")
    String personName;

    @ApiModelProperty(value = "证件类型")
    String idType;

    @ApiModelProperty(name="客户ID")
    Long customerId;

}
