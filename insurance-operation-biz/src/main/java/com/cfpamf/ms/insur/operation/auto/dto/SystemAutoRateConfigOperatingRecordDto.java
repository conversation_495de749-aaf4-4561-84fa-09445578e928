package com.cfpamf.ms.insur.operation.auto.dto;

import com.cfpamf.ms.insur.operation.base.po.BasePO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import javax.persistence.Table;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * 客户断保跟进记录表
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class SystemAutoRateConfigOperatingRecordDto extends BasePO {
    @ApiModelProperty(name="产品主键")
    Integer productId;

    @ApiModelProperty(name="车险推广费配置id")
    Integer configId;

    @ApiModelProperty(name="区域名称")
    String regionName;

    @ApiModelProperty(name="区域编码")
    String regionCode;

    @ApiModelProperty(name="操作类型，add 新增，update 修改，delete 删除")
    String operationType;

    @ApiModelProperty(name="备注")
    String remark;
}
