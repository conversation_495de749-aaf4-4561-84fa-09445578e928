package com.cfpamf.ms.insur.operation.customer.po;

import com.cfpamf.ms.insur.operation.base.po.BasePO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import javax.persistence.Table;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * 客户断保跟进记录表
 */
@Data
@Table(name = "customer_loan_first_follow")
@FieldDefaults(level = AccessLevel.PRIVATE)
public class CustomerLoanFirstFollowPo extends BasePO {

    @ApiModelProperty(name = "客户Id")
    Long customerId;

    @ApiModelProperty("证件号")
    String idNumber;

    @ApiModelProperty(name = "信贷客户编码")
    String loanCustId;

    @ApiModelProperty(name = "未转化原因 7:借新还旧客户 8:未能成功说服客户购买")
    String reason;

    @ApiModelProperty(name = "未转化原因详细说明")
    String reasonRemark;

    @ApiModelProperty(name = "备注")
    String remark;

    @ApiModelProperty(name = "是否继续跟进 0是 1否")
    Integer followState;

    @ApiModelProperty(name = "跟进人")
    String followPeople;

    @ApiModelProperty(name = "跟进人")
    String followPeopleName;

    @ApiModelProperty(name = "跟进时间")
    LocalDateTime followTime;

}
