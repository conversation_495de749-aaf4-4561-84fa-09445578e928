package com.cfpamf.ms.insur.operation.call.controller;

import com.cfpamf.ms.insur.operation.base.annotaions.ResponseDecorated;
import com.cfpamf.ms.insur.operation.base.constant.BaseConstants;
import com.cfpamf.ms.insur.operation.call.dao.CallRecordMapper;
import com.cfpamf.ms.insur.operation.call.dto.CallRequestDto;
import com.cfpamf.ms.insur.operation.call.dto.CallResponseDto;
import com.cfpamf.ms.insur.operation.call.entity.CallRecordPo;
import com.cfpamf.ms.insur.operation.call.service.CallFollowMatchService;
import com.cfpamf.ms.insur.operation.call.service.CallRecordSyncService;
import com.cfpamf.ms.insur.operation.call.service.CallService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 一键呼叫控制器
 *
 * <AUTHOR>
 * @date 2025-07-22
 */
@Slf4j
@Api(value = "一键呼叫", tags = {"一键呼叫"})
@RequestMapping(value = {BaseConstants.ADMIN_VERSION + "/call"})
@ResponseDecorated
@RestController
@AllArgsConstructor
public class CallController {

    private final CallService callService;
    private final CallRecordMapper callRecordMapper;
    private final CallRecordSyncService callRecordSyncService;
    private final CallFollowMatchService callFollowMatchService;

    /**
     * 一键呼叫
     *
     * @param request 呼叫请求
     * @return 呼叫响应
     */
    @ApiOperation(value = "一键呼叫")
    @PostMapping("/oneClick")
    public CallResponseDto oneClickCall(@Valid @RequestBody CallRequestDto request) {
        log.info("收到一键呼叫请求：{}", request);
        return callService.oneClickCall(request);
    }

    /**
     * 根据保单号查询呼叫记录
     *
     * @param policyNo 保单号
     * @return 呼叫记录列表
     */
    @ApiOperation(value = "查询呼叫记录")
    @GetMapping("/records")
    public List<CallRecordPo> getCallRecords(@ApiParam(value = "保单号", required = true) @RequestParam String policyNo) {
        log.info("查询呼叫记录，保单号：{}", policyNo);
        return callRecordMapper.selectByPolicyNo(policyNo);
    }

    /**
     * 手动触发呼叫记录同步
     *
     * @return 同步结果
     */
    @ApiOperation(value = "手动同步呼叫记录")
    @PostMapping("/sync/records")
    public String syncCallRecords() {
        log.info("手动触发呼叫记录同步");
        try {
            callRecordSyncService.syncCallRecords();
            return "同步成功";
        } catch (Exception e) {
            log.error("同步失败", e);
            return "同步失败：" + e.getMessage();
        }
    }

    /**
     * 手动触发跟进记录匹配
     *
     * @return 匹配结果
     */
    @ApiOperation(value = "手动匹配跟进记录")
    @PostMapping("/sync/follow")
    public String matchFollowRecords() {
        log.info("手动触发跟进记录匹配");
        try {
            callFollowMatchService.matchCallWithFollowRecords();
            return "匹配成功";
        } catch (Exception e) {
            log.error("匹配失败", e);
            return "匹配失败：" + e.getMessage();
        }
    }
}
