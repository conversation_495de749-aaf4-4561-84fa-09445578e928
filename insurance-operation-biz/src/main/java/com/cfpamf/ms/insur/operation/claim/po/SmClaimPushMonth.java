package com.cfpamf.ms.insur.operation.claim.po;

import lombok.Data;

import javax.persistence.Id;
import javax.persistence.OrderBy;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2024/3/11
 * @Version 1.0
 */
@Data
@Table(name = "sm_claim_push_month")
public class SmClaimPushMonth implements Serializable {
    /**
     *
     */
    @Id
    @OrderBy("desc")
    private Integer id;

    /**
     * 区域名称
     */
    private String regionName;

    /**
     * 区域编码
     */
    private String regionCode;

    /**
     * 机构名称
     */
    private String organizationName;

    /**
     * 机构编码
     */
    private String organizationCode;

    /**
     * 赔付金额
     */
    private String payMoney;

    /**
     * 已赔付数量
     */
    private String payedNum;

    /**
     * 拒绝赔付数量
     */
    private String rejectNum;

    /**
     * 月份
     */
    private String month;

    /**
     * 发送人
     */
    private String sendTo;

    /**
     *
     */
    private Integer enabledFlag;

    /**
     *
     */
    private Date createTime;

    /**
     *
     */
    private Date updateTime;

    /**
     * 发送类型
     */
    private String sentType;

    /**
     * 发送类型
     */
    private String isSend;

    private String sendUrl;

    private static final long serialVersionUID = 1L;
}