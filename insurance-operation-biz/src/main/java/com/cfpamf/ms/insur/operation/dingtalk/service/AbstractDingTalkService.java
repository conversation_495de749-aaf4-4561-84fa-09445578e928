package com.cfpamf.ms.insur.operation.dingtalk.service;

import com.alibaba.fastjson.JSON;
import com.aliyun.tea.TeaException;
import com.aliyun.teautil.models.RuntimeOptions;
import com.cfpamf.ms.insur.operation.base.exception.BusinessException;
import com.cfpamf.ms.insur.operation.fegin.bms.enums.OperationErrorEnum;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR> 2022/9/23 11:39
 */
@Data
@Slf4j
public abstract class AbstractDingTalkService {

    /**
     * 由于hr状态异常问题添加userid特殊判断
     */
    static final String DING_USERID_ERROR = "userIds";

    /**
     * 封装钉钉api异常
     *
     * @param call
     * @param <T>
     * @return
     */
    protected static <T> T call(ApiCall<T> call) {
        try {

            return call.call();
        } catch (TeaException e) {

            if (StringUtils.contains(e.getMessage(), DING_USERID_ERROR)) {
                log.info("钉钉接口调用失败userid异常，请检查是否离职{}", JSON.toJSONString(e.data), e);
                throw new BusinessException(OperationErrorEnum.DING_TALK_API_ERROR_USERID, e);
            } else {
                log.info("钉钉api调用失败{}", e.data, e);
                throw new BusinessException(OperationErrorEnum.DING_TALK_API_ERROR, e);
            }
        } catch (Exception e) {
            log.info("钉钉api调用失败", e);
            throw new BusinessException(OperationErrorEnum.DING_TALK_API_ERROR, e);
        }
    }

    interface ApiCall<T> {
        /**
         * 调用api
         *
         * @return
         * @throws Exception 钉钉报错
         */
        T call() throws Exception;
    }


    protected RuntimeOptions buildOptions() {
        RuntimeOptions runtimeOptions = new RuntimeOptions();
        runtimeOptions.readTimeout = 20000;
        runtimeOptions.connectTimeout = 2000;
        runtimeOptions.autoretry = Boolean.TRUE;
        runtimeOptions.maxAttempts = 3;
        return runtimeOptions;
    }

}
