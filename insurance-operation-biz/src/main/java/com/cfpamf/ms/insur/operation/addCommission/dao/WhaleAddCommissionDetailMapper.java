package com.cfpamf.ms.insur.operation.addCommission.dao;

import com.cfpamf.ms.insur.operation.addCommission.po.WhaleAddCommissionDetail;
import com.cfpamf.ms.insur.operation.base.dao.CommonMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.entity.Example;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface WhaleAddCommissionDetailMapper extends CommonMapper<WhaleAddCommissionDetail> {
    /**
     * 根据data_index查询加佣记录
     */
    List<WhaleAddCommissionDetail> getSumAmountByDataIndex(List<String> dataIndex);

    /**
     * 更新汇总比例
     * @param detailList 汇总数据
     */
    void updateProportion(@Param("list") List<WhaleAddCommissionDetail> detailList);

    /**
     * 批量更新
     *
     * @param smAddCommissionDetailCollection
     */
    void batchUpdate(@Param("list") Collection<WhaleAddCommissionDetail> smAddCommissionDetailCollection);

    int insertAddCommissionDetail();


    /**
     * 更新金额
     *
     * @param smAddCommissionDetail
     */
    void updateAmount(WhaleAddCommissionDetail smAddCommissionDetail);

    /**
     * 获取为计算的加佣数据
     *
     * @return
     */
    default List<WhaleAddCommissionDetail> getNotCalculatedSmAddCommissionDetail() {
        Example example = new Example(WhaleAddCommissionDetail.class);
        example.createCriteria()
                .andEqualTo("enabledFlag",0)
                .andIsNull("addCommissionAmount")
                .andIsNull("amount");
        return selectByExample(example);
    }

    /**
     * 通过订单号获取加佣
     *
     * @param idNumberList
     * @return
     */
    default List<WhaleAddCommissionDetail> getByIdNumberList(List<String> idNumberList) {
        Example example = new Example(WhaleAddCommissionDetail.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("enabledFlag", 0).andIn("insuredIdNumber", idNumberList);
        return selectByExample(example);
    }
}