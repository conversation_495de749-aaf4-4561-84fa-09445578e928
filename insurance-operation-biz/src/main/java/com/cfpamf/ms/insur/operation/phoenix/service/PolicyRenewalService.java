package com.cfpamf.ms.insur.operation.phoenix.service;

import com.cfpamf.common.ms.exception.MSBizNormalException;
import com.cfpamf.ms.insur.operation.order.dao.SmOrderInsuredMapper;
import com.cfpamf.ms.insur.operation.order.dao.SmOrderMapper;
import com.cfpamf.ms.insur.operation.order.dto.SmBaseOrderVO;
import com.cfpamf.ms.insur.operation.order.dto.SmOrderInsured;
import com.cfpamf.ms.insur.operation.order.dto.SmOrderInsuredDto;
import com.cfpamf.ms.insur.operation.phoenix.dao.PolicyRenewalBaseInfoMapper;
import com.cfpamf.ms.insur.operation.phoenix.enums.EnumRenewalIntention;
import com.cfpamf.ms.insur.operation.phoenix.pojo.dto.ManualMatchRenewalPolicyDto;
import com.cfpamf.ms.insur.operation.phoenix.pojo.dto.SmOrderRenewalFollowDto;
import com.cfpamf.ms.insur.operation.phoenix.pojo.dto.TransferPolicyDto;
import com.cfpamf.ms.insur.operation.phoenix.pojo.po.PolicyRenewalBaseInfo;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.TransactionCallback;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class PolicyRenewalService {

    PolicyRenewalBaseInfoMapper policyRenewalBaseInfoMapper;
    SmOrderInsuredMapper smOrderInsuredMapper;
    SmOrderRenewalFollowService smOrderRenewalFollowService;
    TransactionTemplate transactionTemplate;

    SmOrderMapper smOrderMapper;

    public void manualMatchRenewalPolicy(ManualMatchRenewalPolicyDto policyDto){
        log.info("手工处理保单{}的转保操作",policyDto.getOldPolicyNo());
        List<PolicyRenewalBaseInfo> baseInfos = policyRenewalBaseInfoMapper.listPolicyRenewalBaseInfoByOldPolicyNos(Arrays.asList(policyDto.getOldPolicyNo()));

        if(CollectionUtils.isEmpty(baseInfos)){
            log.info("手工处理保单{}续保基础数据不存在",policyDto.getOldPolicyNo());
            return ;
        }

        PolicyRenewalBaseInfo baseInfo = baseInfos.get(0);
        if(StringUtils.isNotBlank(baseInfo.getNewPolicyNo())){
            log.info("");
            log.info("手工处理保单{}已续保，对应的续保保单号为：{}",policyDto.getOldPolicyNo(),baseInfo.getNewPolicyNo());
        }
        List<SmOrderInsured> insuredLst = smOrderInsuredMapper.selectByRenewalOldPolicyNo(policyDto.getOldPolicyNo());
        if(org.apache.commons.collections4.CollectionUtils.isEmpty(insuredLst)){
            throw new MSBizNormalException("1000001","保单"+policyDto.getOldPolicyNo()+"被保人列表不存在");
        }

        String oldOrderId = insuredLst.get(0).getFhOrderId();
        log.info("获取保单{}的订单号为{}",policyDto.getOldPolicyNo(),oldOrderId);

        //获取可转投保保单
        List<TransferPolicyDto> transferPolicyVos = this.listAutoQueryTransferPolicyList(policyDto.getNewPolicyNo(),baseInfo);

        if(org.apache.commons.collections4.CollectionUtils.isNotEmpty(transferPolicyVos)){
            log.info("");
            TransferPolicyDto vo = matcherTransferPolicy(transferPolicyVos);
            if(Objects.nonNull(vo)) {
                SmOrderRenewalFollowDto dto = toSmOrderRenewalFollowDto(oldOrderId,baseInfo,vo);

                Boolean result = transactionTemplate.execute(new TransactionCallback<Boolean>() {
                    @Override
                    public Boolean doInTransaction(TransactionStatus transactionStatus) {
                        try {
                            smOrderRenewalFollowService.saveRenewalFollowRecord(dto);
                            smOrderRenewalFollowService.updateTransferBaseInfo(vo,baseInfo.getOldPolicyNo());
                            return Boolean.TRUE;
                        } catch (Exception e){
                            //回滚
                            log.warn("转保信息保存失败",e);
                            transactionStatus.setRollbackOnly();
                            return Boolean.FALSE;
                        }
                    }
                });
                if(!result){
                    throw new RuntimeException("保单"+baseInfo.getOldPolicyNo()+"转保异常");
                }
            }
        }
    }

    private SmOrderRenewalFollowDto toSmOrderRenewalFollowDto(String oldOrderId,PolicyRenewalBaseInfo po, TransferPolicyDto vo){
        SmOrderRenewalFollowDto dto = new SmOrderRenewalFollowDto();
        dto.setFollowPeople("system");

        dto.setFollowTime(LocalDateTime.now());
        dto.setOrderId(oldOrderId);
        dto.setPolicyNo(po.getOldPolicyNo());
        dto.setProductId(vo.getProductId());
        dto.setPlanId(vo.getPlanId());
        if(!Objects.equals(po.getPolicyProductType(), "团险")){
            dto.setIdNumber(po.getInsuredIdNumber());
        }else {
            dto.setIdNumber(po.getApplicantIdNumber());
        }
        dto.setIntention(EnumRenewalIntention.TRANSFER.getCode());
        dto.setIntentionName(EnumRenewalIntention.TRANSFER.getDesc());
        dto.setTransferOrderId(vo.getFhOrderId());
        dto.setTransferPolicyNo(vo.getPolicyNo());
        return dto;
    }

    private TransferPolicyDto matcherTransferPolicy(List<TransferPolicyDto> transferPolicyVos){
        List<String> policyNos = transferPolicyVos.stream().map(TransferPolicyDto::getPolicyNo).collect(Collectors.toList());
        List<String> existPolicy = this.listExistTransferPolicyNo(policyNos);
        transferPolicyVos = transferPolicyVos.stream().filter(c->!existPolicy.contains(c.getPolicyNo())).collect(Collectors.toList());
        if(org.apache.commons.collections4.CollectionUtils.isEmpty(transferPolicyVos)){
            return null;
        }
        return transferPolicyVos.stream().min(Comparator.comparing(TransferPolicyDto::getPaymentTime)).get();
    }

    /**
     * 新保单是否备用过了
     * @param policyNos
     * @return
     */
    public List<String> listExistTransferPolicyNo(List<String> policyNos){
        if(CollectionUtils.isEmpty(policyNos)){
            return Collections.EMPTY_LIST;
        }
        List<String> ret = policyRenewalBaseInfoMapper.listExistTransferPolicyNo(policyNos);
        return CollectionUtils.isEmpty(ret)?Collections.EMPTY_LIST:ret;
    }

    public List<TransferPolicyDto> listAutoQueryTransferPolicyList(String newPolicyNo,PolicyRenewalBaseInfo baseInfo) {
        //InsuranceRenewPo insuranceRenewPo = insuranceRenewMapper.getRenewDataByOldOrderId(fhOrderId);
        if(Objects.equals("团险",baseInfo.getPolicyProductType())){
            return policyRenewalBaseInfoMapper.autoQueryGroupTransferPolicy(newPolicyNo,baseInfo.getRiskCatagoryName());
        }else{
            return policyRenewalBaseInfoMapper.autoQueryPersonTransferPolicy(newPolicyNo,baseInfo.getRiskCatagoryName());
        }

    }



}
