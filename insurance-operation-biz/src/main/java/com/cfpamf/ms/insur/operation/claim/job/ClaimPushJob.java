package com.cfpamf.ms.insur.operation.claim.job;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cfpamf.ms.insur.operation.claim.service.ClaimPushServiceImpl;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * <AUTHOR>
 * @Date 2024/3/11
 * @Version 1.0
 */
@Slf4j
@Component
public class ClaimPushJob {


    @Autowired
    private ClaimPushServiceImpl claimPushService;



    /**
     * 理赔每月赔付统计推送
     */
    @XxlJob("claim-push")
    public void push() throws InterruptedException {



        String s = XxlJobHelper.getJobParam();
//        String s = "{\"month\":\"2022-11-01 01:00:00\"}";

        LocalDateTime now = LocalDateTime.now();
        now = now.minusMonths(1);

        if (StringUtils.isNotEmpty(s)) {
            JSONObject jsonObject = JSON.parseObject(s);
            if (jsonObject.containsKey("month")) {
                now = LocalDateTime.parse(jsonObject.getString("month"), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            }

            claimPushService.sendClaimDataByMonth(now.format(DateTimeFormatter.ofPattern("yyyy-MM")));
        }
        log.info("生成{}月份理赔数据", now);

    }

}
