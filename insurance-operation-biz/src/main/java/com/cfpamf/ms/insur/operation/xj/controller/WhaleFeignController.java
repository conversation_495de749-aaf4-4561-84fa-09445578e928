package com.cfpamf.ms.insur.operation.xj.controller;

import com.cfpamf.ms.insur.operation.base.annotaions.ResponseDecorated;
import com.cfpamf.ms.insur.operation.base.response.CommonResult;
import com.cfpamf.ms.insur.operation.phoenix.pojo.query.PhoenixEmpTodoBackQuery;
import com.cfpamf.ms.insur.operation.phoenix.pojo.vo.PhoenixEmpTodoBackVo;
import com.cfpamf.ms.insur.operation.xj.query.WxDataQuery;
import com.cfpamf.ms.insur.operation.xj.service.WhaleFeignService;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
@RestController
@ResponseDecorated
@RequestMapping("/whale/notice")
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@AllArgsConstructor
@Api(value = "提供给小鲸公众号通知", tags = "提供给小鲸公众号通知")
public class WhaleFeignController {

    @Autowired
    WhaleFeignService whaleFeignService;

    @ApiOperation(value = "发送公众号通知")
    @PostMapping("")
    public void notice(@RequestBody WxDataQuery query) {
        whaleFeignService.notice(query);
    }
}
