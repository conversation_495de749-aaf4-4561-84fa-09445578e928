
package com.cfpamf.ms.insur.operation.addCommission.po;

import com.cfpamf.ms.insur.operation.base.entity.BaseNoUserEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import javax.persistence.Table;

/**
 * <AUTHOR>
 */
@ApiModel("结佣结算推送记录表")
@Table(name = "whale_settlement_push_log")
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class WhaleSettlementPushLog extends BaseNoUserEntity {
    /**
     * 字段名称 批单号
     */
    @ApiModelProperty(value = "批单号")
    String batchNo;
    /**
     * 字段名称 活动id
     */
    @ApiModelProperty(value = "活动id")
    Integer activityId;
    /**
     * 字段名称 流水号
     */
    @ApiModelProperty(value = "流水号")
    String requestId;
    /**
     * 字段名称 推送备注：首次结算/重算
     */
    @ApiModelProperty(value = "推送备注：首次结算/重算")
    String pushRemark;
    /**
     * 字段名称 响应内容json
     */
    @ApiModelProperty(value = "响应内容json")
    String responseData;
    /**
     * 字段名称 推送内容
     */
    @ApiModelProperty(value = "推送内容")
    String pushData;

    @ApiModelProperty("推送状态 1-成功 2失败")
    private Integer pushState;

    @ApiModelProperty(value = "结算状态：0核算中，1待结算，2已结算，3作废")
    private Integer settlementState;
}

