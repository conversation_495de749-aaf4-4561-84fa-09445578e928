package com.cfpamf.ms.insur.operation.call.service.impl;

import com.cfpamf.ms.insur.operation.call.dao.CallRecordMapper;
import com.cfpamf.ms.insur.operation.call.feign.CallServiceFacade;
import com.cfpamf.ms.insur.operation.call.feign.dto.AudioQueryRequestDto;
import com.cfpamf.ms.insur.operation.call.feign.dto.AudioQueryResponseDto;
import com.cfpamf.ms.insur.operation.call.feign.dto.RecordQueryRequestDto;
import com.cfpamf.ms.insur.operation.call.feign.dto.RecordQueryResponseDto;
import com.cfpamf.ms.insur.operation.call.po.CallRecordPo;
import com.cfpamf.ms.insur.operation.call.service.CallRecordSyncService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 呼叫记录同步服务
 *
 * <AUTHOR>
 * @date 2025-07-22
 */
@Service
@Slf4j
@AllArgsConstructor
public class CallRecordSyncServiceImpl implements CallRecordSyncService {

    private final CallRecordMapper callRecordMapper;
    private final CallServiceFacade callServiceFacade;

    /**
     * 同步呼叫记录信息
     * 查询第三方服务获取呼叫详情，更新本地记录
     */
    public void syncCallRecords() {
        log.info("开始同步呼叫记录信息");
        
        // 查询未获取录音的记录，限制每次处理100条
        List<CallRecordPo> unRecordedCalls = callRecordMapper.selectUnRecordedCalls(100);
        
        if (CollectionUtils.isEmpty(unRecordedCalls)) {
            log.info("没有需要同步的呼叫记录");
            return;
        }

        log.info("找到{}条需要同步的呼叫记录", unRecordedCalls.size());

        for (CallRecordPo callRecord : unRecordedCalls) {
            try {
                syncSingleCallRecord(callRecord);
            } catch (Exception e) {
                log.error("同步呼叫记录失败，记录ID：{}，错误信息：{}", callRecord.getId(), e.getMessage(), e);
            }
        }

        log.info("呼叫记录同步完成");
    }

    /**
     * 同步单条呼叫记录
     */
    private void syncSingleCallRecord(CallRecordPo callRecord) {
        log.info("开始同步呼叫记录，ID：{}，CallSid：{}", callRecord.getId(), callRecord.getCallSid());

        // 如果没有CallSid，跳过
        if (!StringUtils.hasText(callRecord.getCallSid())) {
            log.warn("呼叫记录没有CallSid，跳过同步，记录ID：{}", callRecord.getId());
            return;
        }

        try {
            // 先查询呼叫记录详情
            RecordQueryRequestDto queryRequest = new RecordQueryRequestDto();
            queryRequest.setFrom(callRecord.getEmployeePhone());
            queryRequest.setTo(callRecord.getCustomerPhone());
            queryRequest.setKey(callRecord.getPolicyNo());
            queryRequest.setModule("INSURANCE_OPERATION");
            queryRequest.setPageIndex(1);
            queryRequest.setPageSize(10);

            RecordQueryResponseDto queryResponse = callServiceFacade.queryRecord(queryRequest);
            
            if (queryResponse.getSuccess() && queryResponse.getRecords() != null 
                && !CollectionUtils.isEmpty(queryResponse.getRecords().getItems())) {
                
                // 查找匹配的记录
                RecordQueryResponseDto.RecordItem matchedItem = findMatchedRecord(callRecord, queryResponse.getRecords().getItems());
                
                if (matchedItem != null) {
                    // 更新呼叫记录基本信息
                    updateCallRecordFromQueryResult(callRecord, matchedItem);
                    
                    // 如果有录音地址，直接更新；否则尝试获取录音
                    if (StringUtils.hasText(matchedItem.getRecordUrl())) {
                        updateRecordInfo(callRecord.getId(), matchedItem.getRecordUrl(), null, 1);
                        log.info("从查询结果中获取到录音地址，记录ID：{}", callRecord.getId());
                    } else {
                        // 尝试获取录音
                        tryGetRecordUrl(callRecord);
                    }
                } else {
                    log.warn("未找到匹配的呼叫记录，记录ID：{}", callRecord.getId());
                }
            } else {
                log.warn("查询呼叫记录失败或无结果，记录ID：{}，响应：{}", callRecord.getId(), queryResponse.getMessage());
                // 尝试直接获取录音
                tryGetRecordUrl(callRecord);
            }

        } catch (Exception e) {
            log.error("同步呼叫记录异常，记录ID：{}，错误：{}", callRecord.getId(), e.getMessage(), e);
        }
    }

    /**
     * 查找匹配的记录
     */
    private RecordQueryResponseDto.RecordItem findMatchedRecord(CallRecordPo callRecord, List<RecordQueryResponseDto.RecordItem> items) {
        return items.stream()
                .filter(item -> callRecord.getCallSid().equals(item.getCallSid()))
                .findFirst()
                .orElse(null);
    }

    /**
     * 从查询结果更新呼叫记录
     */
    private void updateCallRecordFromQueryResult(CallRecordPo callRecord, RecordQueryResponseDto.RecordItem item) {
        callRecord.setBeginCallTime(item.getBeginCallTime());
        callRecord.setEndCallTime(item.getEndTime());
        callRecord.setDuration(item.getDuration());
        
        // 根据通话时长判断呼叫状态
        if (item.getDuration() != null && item.getDuration() > 0) {
            callRecord.setCallStatus(2); // 呼叫成功
        } else {
            callRecord.setCallStatus(3); // 呼叫失败
        }
        
        callRecord.setUpdateTime(LocalDateTime.now());
        callRecordMapper.updateByPrimaryKey(callRecord);
    }

    /**
     * 尝试获取录音地址
     */
    private void tryGetRecordUrl(CallRecordPo callRecord) {
        try {
            AudioQueryRequestDto audioRequest = new AudioQueryRequestDto();
            audioRequest.setCallSid(callRecord.getCallSid());

            AudioQueryResponseDto audioResponse = callServiceFacade.queryAudio(audioRequest);

            if (audioResponse.getSuccess() && audioResponse.getTelecomAudioResponseDto() != null) {
                AudioQueryResponseDto.TelecomAudioResponse audioData = audioResponse.getTelecomAudioResponseDto();
                
                if (StringUtils.hasText(audioData.getRecordUrl())) {
                    updateRecordInfo(callRecord.getId(), audioData.getRecordUrl(), 
                                   audioData.getRecordDuration(), 1);
                    log.info("获取到录音地址，记录ID：{}", callRecord.getId());
                } else {
                    log.info("暂未获取到录音地址，记录ID：{}", callRecord.getId());
                }
            } else {
                log.warn("获取录音失败，记录ID：{}，响应：{}", callRecord.getId(), audioResponse.getMessage());
            }

        } catch (Exception e) {
            log.error("获取录音异常，记录ID：{}，错误：{}", callRecord.getId(), e.getMessage(), e);
            // 标记为获取失败
            updateRecordInfo(callRecord.getId(), null, null, 2);
        }
    }

    /**
     * 更新录音信息
     */
    private void updateRecordInfo(Integer id, String recordUrl, String recordDuration, Integer recordStatus) {
        callRecordMapper.updateRecordInfo(id, recordUrl, recordDuration, recordStatus);
    }
}
