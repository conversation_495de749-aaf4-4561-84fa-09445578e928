package com.cfpamf.ms.insur.operation.msg.dao;

import com.cfpamf.ms.insur.operation.base.dao.CommonMapper;
import com.cfpamf.ms.insur.operation.msg.pojo.po.OpMessageRule;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.common.IdsMapper;

import java.util.List;

/**
 * 订单相关统计
 *
 * <AUTHOR> 2021/7/7 13:45
 */
@Mapper
public interface OpMessageRuleMapper extends CommonMapper<OpMessageRule>, IdsMapper<OpMessageRule> {

    /**
     * 根据规则编码查询配置信息
     *
     * @param ruleCode
     * @return
     */
    default OpMessageRule selectByCode(@Param("ruleCode") String ruleCode) {
        final OpMessageRule query = new OpMessageRule();
        query.setRuleCode(ruleCode);
        return selectOne(query);
    }

    /**
     * 根据编码匹查询所有关联的配置信息
     * @param ruleCode
     * @return
     */
    default List<OpMessageRule> selectListByCode(@Param("ruleCode") String ruleCode) {
        final OpMessageRule query = new OpMessageRule();
        query.setRuleCode(ruleCode);
        return select(query);
    }
}
