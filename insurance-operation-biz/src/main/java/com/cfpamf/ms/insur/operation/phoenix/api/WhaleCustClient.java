package com.cfpamf.ms.insur.operation.phoenix.api;

import com.cfpamf.ms.insur.operation.phoenix.api.model.PkUserQueryRequest;
import com.cfpamf.ms.insur.operation.phoenix.api.model.PkUserQueryResp;
import com.cfpamf.ms.insur.operation.phoenix.api.model.WhaleResp;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR> 2020/4/10 15:36
 */
@FeignClient(url = "${whale.domain}", value = "whaleCustClient")
public interface WhaleCustClient {

    /**
     *
     * @param aid
     * @param version
     * @param req
     * @return
     */
    @PostMapping("/channel/api/pk/user/count")
    WhaleResp<List<PkUserQueryResp>> userCount(@RequestParam("aid") String aid,
                                               @RequestParam("version") String version,
                                               @RequestBody PkUserQueryRequest req);

}
