package com.cfpamf.ms.insur.operation.msg.job;

import com.cfpamf.ms.insur.operation.msg.service.MsgPushService;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * 发送消息推送mq
 *
 * <AUTHOR>
 * @date 2021/12/13 10:30
 */
@Slf4j
@Component
public class MsgPushHandler {

    @Autowired
    private MsgPushService msgPushService;
    @Transactional(rollbackFor = Exception.class)
    @XxlJob("msg_push_mq")
    public void execute() {
        //汇总加佣总比例
        msgPushService.sendMessage();
    }


}
