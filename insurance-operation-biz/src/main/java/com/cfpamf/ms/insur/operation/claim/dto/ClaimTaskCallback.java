package com.cfpamf.ms.insur.operation.claim.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class ClaimTaskCallback {
    private Long id;
    private String contractCode;
    private String policyNo;
    private String claimNo;
    private String applicantName;
    private String applicantIdCard;
    private String applicantIdType;
    private String applicantMobile;
    private String applicantType;
    private BigDecimal premiumTotal;
    private String mainProductName;
    private Date finishTime;
    private BigDecimal payMoney;
    private String claimExperienceSatisfactionScore;
    private String claimExperienceSatisfaction;
    private String unreasonableFeeFlag;
    private String giftInsuranceWillingnes;
    private String updateUser;
    private String createUser;
    private Date updateTime;
    private Long revision;
    private Date createTime;
}