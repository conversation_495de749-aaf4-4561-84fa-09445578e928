package com.cfpamf.ms.insur.operation.promotion.enums;

import lombok.Getter;

@Getter
public enum MarketingOperationEnum {

    PUT_DOWN_MATERIAL(0,"下架素材"),
    LAUNCH_MATERIAL(1,"上架素材"),
    DELETE_MATERIAL(2,"删除素材")
    ;
    private Integer operationCode;
    private String operationName;

    MarketingOperationEnum(Integer operationCode,String operationName){
        this.operationCode = operationCode;
        this.operationName = operationName;
    }
}
