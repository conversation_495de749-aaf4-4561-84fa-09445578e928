package com.cfpamf.ms.insur.operation.honor.query;

import com.cfpamf.ms.insur.operation.base.form.PageForm;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR> 2022/9/8 14:52
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class HonorRulesConfigurationQuery extends PageForm {
    @ApiModelProperty(name="年度")
    String year;

    @ApiModelProperty("荣誉称号")
    String honorName;

    @ApiModelProperty(name="评选对象 area:区域，bch：分支，emp：个人")
    String level;
}
