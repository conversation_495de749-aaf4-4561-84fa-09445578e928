package com.cfpamf.ms.insur.operation.base.util;

import org.apache.commons.lang3.StringUtils;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @date 2022/5/7 18:01
 */
public class DateUtils {
    public static String toDateString(Date date) {
        // 定义日期格式
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        // 将 Date 对象转换为 String
        return dateFormat.format(date);
    }
}
