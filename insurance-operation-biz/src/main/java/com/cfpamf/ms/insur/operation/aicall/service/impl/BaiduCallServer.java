package com.cfpamf.ms.insur.operation.aicall.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baidu.ccc.auth.api.token.ApiTokenV1;
import com.cfpamf.ms.insur.operation.aicall.dao.AiProviderConfigurationMapper;
import com.cfpamf.ms.insur.operation.aicall.dao.BaiduCallTaskDetailMapper;
import com.cfpamf.ms.insur.operation.aicall.dao.BaiduCallTaskMapper;
import com.cfpamf.ms.insur.operation.aicall.dao.CallTaskMapper;
import com.cfpamf.ms.insur.operation.aicall.entity.*;
import com.cfpamf.ms.insur.operation.aicall.exceptions.CreateCallTaskException;
import com.cfpamf.ms.insur.operation.aicall.exceptions.ImportPhoneException;
import com.cfpamf.ms.insur.operation.aicall.exceptions.UpdateCallTaskStatusException;
import com.cfpamf.ms.insur.operation.aicall.service.CallServer;
import com.cfpamf.ms.insur.operation.aicall.vo.AiCallTask;
import com.cfpamf.ms.insur.operation.aicall.vo.AiCallTaskDetail;
import com.cfpamf.ms.insur.operation.aicall.vo.request.*;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.client.ClientProtocolException;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;
import tk.mybatis.mapper.entity.Example;

import java.io.IOException;
import java.net.URISyntaxException;
import java.util.*;

/***
 * <AUTHOR>
 */
@Component("BaiduCall")
@Slf4j(topic = "BaiduCallServer")
public class BaiduCallServer extends AbstractCallServer<DialTask,ImportPhone> {

    @Autowired
    CallTaskMapper callTaskMapper;

    @Autowired
    BaiduCallTaskMapper baiduCallTaskMapper;

    @Autowired
    BaiduCallTaskDetailMapper baiduCallTaskDetailMapper;

    @Autowired
    AiProviderConfigurationMapper aiProviderConfigurationMapper;


    /**
     * 创建任务
     *
     * @param dialTask  拨号任务对象
     * @param taskType  任务类型
     * @return 任务ID（成功创建百度任务的返回ID，若创建失败则返回空字符串）
     */
    public String createTask(DialTask dialTask,String taskType) {

        String result = postObject(null,dialTask,"/api/v3/console/apitask/create");
        String returnString = dialWithBaiduResponse(result);
        if(StringUtils.isBlank(returnString)){ //如果在百度创建任务失败直接返回不创建本地任务记录。
            return returnString;
        }

        CallTask callTask = new CallTask();
        BeanUtils.copyProperties(dialTask,callTask);
        callTask.setAiProvider("BaiduCall");
        callTask.setTaskType(taskType);
        callTask.setCreatedTime(new Date());
        callTask.setUpdatedTime(new Date());
        BaiduCallTask baiduCallTask =new BaiduCallTask();
        BeanUtils.copyProperties(dialTask,baiduCallTask);
        this.callTaskMapper.insertOrUpdate(callTask);
        dialTask.setTaskId(callTask.getId().toString());
        baiduCallTask.setTaskId(callTask.getId());
        baiduCallTask.setTaskType(taskType);
        baiduCallTask.setCreatedTime(new Date());
        baiduCallTask.setUpdatedTime(new Date());
        this.baiduCallTaskMapper.insertOrUpdate(baiduCallTask);

        return returnString;
    }

    /**
     * 解析百度外呼返回的JSON字符串
     * @param result 百度搜索返回的JSON字符串
     * @return 处理后的JSON字符串
     */
    private String dialWithBaiduResponse(String result) {
        if(StringUtils.isNotBlank(result)){
            JSONObject jsonObject = JSON.parseObject(result);
            if(jsonObject.getInteger("code")==200){
                return jsonObject.getJSONObject("data").toJSONString();
            }else{
                return null;
            }
        }
        return null;
    }

    @Override
    public String importPhone(ImportPhone importPhone,DialTask dialTask) {
        List<BaiduCallTaskDetail> list = new ArrayList<>();
        importPhone.getCustomerInfoList().forEach(item->{
            BaiduCallTaskDetail detail = new BaiduCallTaskDetail();
            detail.setTaskId(Long.valueOf(dialTask.getTaskId()));
            BeanUtils.copyProperties(importPhone,detail);
            detail.setTaskBatchCode(importPhone.getTaskId());
            BeanUtils.copyProperties(item,detail);
            detail.setOutSourceId(Long.valueOf(item.getExtJson()));
            detail.setVar(JSON.toJSONString(Optional.ofNullable(item.getVar()).orElse(new HashMap<>())));
            detail.setCreatedTime(new Date());
            detail.setUpdatedTime(new Date());
            list.add(detail);
        });
        this.baiduCallTaskDetailMapper.insertList(list);
        String result = postObject(null,importPhone,"/api/v3/console/apitask/import");
        String returnString = dialWithBaiduResponse(result);
        return returnString;
    }

    @Override
    public String updateTaskStatus(String taskBatchCode) {
        UpdateCallTaskStatus updateCallTaskStatus = new UpdateCallTaskStatus();
        updateCallTaskStatus.setTaskId(taskBatchCode);
        updateCallTaskStatus.setTaskStatus(2);
        return postObject(null,updateCallTaskStatus,"/api/v3/console/apitask/task/status/update");
    }



    @Override
    public String getRecordFileUrl(String contactUuid) {
        Map<String,String> param = new HashMap<>();
        param.put("contactUuid",contactUuid);
        param.put("operation","3");
        String result = this.getObject(null,param,"/api/v1/record");
        String resultString = "";
        JSONObject resultJson = JSON.parseObject(result);
        if(resultJson.getInteger("code")==200){
            resultString=resultJson.getJSONArray("data").getJSONObject(0).toJSONString();
        }else {
            log.warn("录音地址不存在，"+resultJson.getString("msg"));
        }
        return resultString;
    }

    protected void updateLocalTaskDetail(DialTask dialTask, String importPhoneResult) {
        JSONObject importPhoneResultJsonResult = JSON.parseObject(importPhoneResult);
        JSONArray resList = importPhoneResultJsonResult.getJSONArray("resList");
        resList.forEach(item->{
            Example example = new Example(BaiduCallTaskDetail.class);
            example.createCriteria().andEqualTo("outSourceId",((JSONObject)item).getInteger("extJson"))
                    .andEqualTo("taskId",Long.valueOf(dialTask.getTaskId()));
            BaiduCallTaskDetail detail = new BaiduCallTaskDetail();
            detail.setTaskMemberId(((JSONObject)item).getString("taskMemberId"));
            this.baiduCallTaskDetailMapper.updateByExampleSelective(detail,example);
        });
    }

    protected void updateLocalTask(DialTask dialTask, String taskBatchCode) {
        CallTask callTask = new CallTask();
        callTask.setId(Long.valueOf(dialTask.getTaskId()));
        callTask.setTaskBatchCode(taskBatchCode);
        this.callTaskMapper.updateByPrimaryKeySelective(callTask);
        BaiduCallTask baiduCallTask = new BaiduCallTask();
        baiduCallTask.setTaskBatchCode(taskBatchCode);
        Example example = new Example(BaiduCallTask.class);
        example.createCriteria().andEqualTo("taskId",Long.valueOf(dialTask.getTaskId()));
        this.baiduCallTaskMapper.updateByExampleSelective(baiduCallTask,example);
    }

    protected ImportPhone buildImportPhone(List<List<AiCallTaskDetail>> partitions, int i, String taskBatchCode) {
        ImportPhone importPhone = new ImportPhone();
        importPhone.setTaskId(taskBatchCode);
        importPhone.setSecretType(2);//默认不加密
        List<CustomerInfo> customerInfos = new ArrayList<>();
        importPhone.setCustomerInfoList(customerInfos);
        partitions.get(i).forEach(item->{
            CustomerInfo customerInfo = new CustomerInfo();
            customerInfo.setExtJson(item.getOutSourceId().toString());
            customerInfo.setMobile(item.getMobile());
            customerInfo.setVar(item.getVarMap());
            customerInfos.add(customerInfo);
        });
        return importPhone;
    }

    public DialTask buildCallTask(AiCallTask aiCallTask, int i, RobortConfiguration robortConfiguration) {
        DialTask dialTask =new DialTask();
        dialTask.setTaskName(aiCallTask.getTaskName()+"_"+ i);
        dialTask.setRobotId(robortConfiguration.getRobotId());
        dialTask.setDialStartDate(aiCallTask.getDialStartDate());
        dialTask.setDialStartTime(Optional.ofNullable(aiCallTask.getDialStartTime()).orElse("09:00"));
        dialTask.setDialEndTime(Optional.ofNullable(aiCallTask.getDialEndTime()).orElse("21:00"));
        dialTask.setIsOpenEmptyNum(false);//不开启24小时空号检测
        dialTask.setIsOpenPhoneDown(false);//不开启12小时停机检测
        dialTask.setIsOpenRepeatFilter(true);//过滤重复号码
        dialTask.setBlackListStrategy(2);//触发黑名单，该号码不再拨打
        List<ForbidDialTime> list = new ArrayList<>();
        ForbidDialTime forbidDialTime = new ForbidDialTime();
        forbidDialTime.setForbidDialStartTime("12:30");
        forbidDialTime.setForbidDialEndTime("13:30");
        list.add(forbidDialTime);
        dialTask.setForbidDialTime(list);
        return dialTask;
    }

    private String getToken(String methodurl,String method,String query) {
        ApiTokenV1.ApiAuthTokenV1Builder builder = new ApiTokenV1.ApiAuthTokenV1Builder();
        // 必填参数 ak,sk,uri,host,http method, 过期时间
        builder.ak(this.aiProviderConfiguration.getAccountCode());   // ak
        builder.sk(this.aiProviderConfiguration.getAccountKey());   // sk
        builder.uri(methodurl);                // 接口路径
        builder.host(this.aiProviderConfiguration.getHost());               // host
        builder.httpMethod(method);                        // http method
        builder.expireInSeconds(1800);                    // 过期时间

        if("POST".equals(method)){
            builder.httpHeader("Content-Type", "application/json");
        }
        // url里?带的参数，如/api/v1/robot/list?robotName=test, 没有？参数可以不调用此方法
        if ("GET".equals(method) && StringUtils.isNotBlank(query)){
           builder.queryStr(query);
        }

        String token = builder.build().getToken();

        return token;
    }


    @Override
    protected String postObject(Map<String,String> header, Object param, String method){
        header = Optional.ofNullable(header).orElse(new HashMap<>());
        header.put("Authorization",this.getToken(method,"POST",null));
        return super.postObject(header,param,method);
    }

    @Override
    protected String getObject(Map<String,String> header, Map<String,String> param, String method){
        try{
            header = Optional.ofNullable(header).orElse(new HashMap<>());
            URIBuilder uriBuilder = new URIBuilder("https://"+this.aiProviderConfiguration.getHost()+method);
            header.put("Authorization",this.getToken(method,"GET",uriBuilder.build().getQuery()));
        }catch (Exception e){

        }
        return super.getObject(header,param,method);
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        AiProviderConfiguration queryParam = new AiProviderConfiguration();
        queryParam.setAiProvider("BaiduCall");
        this.aiProviderConfiguration=aiProviderConfigurationMapper.selectOne(queryParam);
    }
}
