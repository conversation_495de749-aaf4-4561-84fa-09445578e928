package com.cfpamf.ms.insur.operation.whale.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;


/**
 * <AUTHOR>
 */
@FieldDefaults(level = AccessLevel.PRIVATE)
@Data
public class ContractBaseInfo {

    String adminPolicyStatus;

    /**
     * 投保单号
     */
    String applicantPolicyNo;

    /**
     * 保险公司编码
     */
    String companyCode;

    String companyName;

    String insuranceSubjectMatter;

    /**
     *保单主险编码
     */
    String mainProductCode;

    String mainProductName;

    String policyNo;

    String policyStatus;

    String portfolioName;

    Integer salesType;

    /**
     * 保单下载地址
     */
    String policyUrl;

    /**
     * 是续保保单会存在值
     */
    String sourcePolicyNo;

    @ApiModelProperty(value = "保司-保单号(续保场景)", example = "CS0001")
    private String thirdPolicyNo;

    @ApiModelProperty(value = "短险续保期数(不是续期)", example = "1")
    private Integer renewalPeriod;

    @ApiModelProperty("主险-长短险标志")
    private Integer longShortFlag;
}
