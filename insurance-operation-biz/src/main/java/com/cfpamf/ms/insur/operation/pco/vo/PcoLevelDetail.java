package com.cfpamf.ms.insur.operation.pco.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * PCO等级详情
 */
@Data
public class PcoLevelDetail {
    /**
     * 钉钉用户信息
     */
    @ApiModelProperty("钉钉用户信息")
    private DingUserInfo dingUserInfo;

    /**
     * 最新调整等级记录
     */
    @ApiModelProperty("最新调整等级记录")
    private PcoLevelChangeRecord newestLevelInfo;

    /**
     * 周评分记录
     */
    @ApiModelProperty("周评分记录")
    private List<PcoWeeksScoreVo> pcoWeeksScoreVos;
}
