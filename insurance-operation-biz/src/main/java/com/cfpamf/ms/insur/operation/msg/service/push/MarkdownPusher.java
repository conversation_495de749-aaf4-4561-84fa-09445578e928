package com.cfpamf.ms.insur.operation.msg.service.push;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cfpamf.common.ms.exception.MSBizNormalException;
import com.cfpamf.ms.insur.operation.base.event.EventBusEngine;
import com.cfpamf.ms.insur.operation.base.exception.BusinessException;
import com.cfpamf.ms.insur.operation.base.response.CommonResult;
import com.cfpamf.ms.insur.operation.dingtalk.event.DingTalkUserIdErrorEvent;
import com.cfpamf.ms.insur.operation.dingtalk.service.DingTalkRobotService;
import com.cfpamf.ms.insur.operation.fegin.bms.enums.OperationErrorEnum;
import com.cfpamf.ms.insur.operation.fegin.image.facade.InsuranceImageFacade;
import com.cfpamf.ms.insur.operation.msg.enums.OpMessageReceiverTypeEnum;
import com.cfpamf.ms.insur.operation.msg.pojo.dto.MarkdownMessageTemplate;
import com.cfpamf.ms.insur.operation.msg.pojo.dto.OpMessageRuleGroovyDTO;
import com.cfpamf.ms.insur.operation.msg.pojo.po.OpMessagePush;
import com.cfpamf.ms.insur.operation.msg.pojo.po.OpMessageRule;
import com.cfpamf.ms.insur.operation.msg.pojo.po.OpMessageRuleReceiver;
import com.cfpamf.ms.insur.operation.msg.service.MsgPushService;
import com.cfpamf.ms.insur.operation.msg.util.Html2ImageUtils;
import com.google.common.collect.Maps;
import lombok.AccessLevel;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.function.Supplier;

/**
 * <AUTHOR> 2021/8/30 11:00
 */
@Component("markdownPusher")
@Slf4j
@FieldDefaults(level = AccessLevel.PRIVATE)
public class MarkdownPusher extends AbstractPusher {

    @Autowired
    DingTalkRobotService robotService;
    static final String IMAGE_PARAMS_KEY = "imgParam";

    @Autowired
    InsuranceImageFacade imageFacade;

    @Autowired
    EventBusEngine busEngine;

    @Override
    public void push(OpMessageRule rule, String contextId, boolean delay, List<OpMessageRuleReceiver> opMessageRuleReceivers) {
        if (CollectionUtils.isEmpty(opMessageRuleReceivers)) {
            throw new BusinessException(OperationErrorEnum.MSG_CONFIG_ERROR.getCode(), "没有配置消息接收者！");
        }
        // fork join pool
        // 当前上下文
        final Map<String, Object> context = Maps.newConcurrentMap();
        String messageTemplate = rule.getMessageTemplate();

        final MarkdownMessageTemplate template = JSONObject.parseObject(messageTemplate, MarkdownMessageTemplate.class);
        opMessageRuleReceivers
                .forEach(receiver -> {
                    OpMessageRuleGroovyDTO ruleDto = new OpMessageRuleGroovyDTO();
                    ruleDto.setContext(context);
                    BeanUtils.copyProperties(rule, ruleDto);
                    ruleDto.setReceiver(receiver);
                    //把钉钉群配置的json参数添加到groovy上下文对象中 如区域列表,片区列表,分支列表
                    fillDataToOpMessageRuleGroovyDTO(ruleDto,receiver);
                    String params = receiver.getParams();
                    JSONObject json = new JSONObject();
                    //设置图片性质 动态图或静态图
                    json.put(IMAGE_NATURE,rule.getImageNature());
                    //规则的参数支持
                    if (StringUtils.isNotBlank(ruleDto.getParams())) {
                        JSONObject ps = JSONObject.parseObject(ruleDto.getParams());
                        if (ps.containsKey(IMAGE_PARAMS_KEY)) {
                            json.putAll(ps.getJSONObject(IMAGE_PARAMS_KEY));
                        }
                    }
                    //接收人维度的参数支持 优先级大于规则
                    if (StringUtils.isNotBlank(params)) {
                        JSONObject ps = JSONObject.parseObject(params);
                        if (ps.containsKey(IMAGE_PARAMS_KEY)) {
                            json.putAll(ps.getJSONObject(IMAGE_PARAMS_KEY));
                        }
                    }

                    Map<String, Object> scriptResult = (Map<String, Object>) groovyService.executeForCode(MsgPushService.SCRIPT_TYPE, rule.getRuleCode(), ruleDto);

                    if (MapUtils.isEmpty(scriptResult)) {
                        log.warn("脚本返回结果为空[{}]", scriptResult);
                        return;
                    }
                    String imageContent = Html2ImageUtils.processTemplateInfoString(template.getSingleImageTemplate(), scriptResult);
                    String imageUrl = StringUtils.isNotBlank(imageContent) ? html2ImageAndUpload(json, imageContent) : null;
                    Map<String, Object> markdownParam = new HashMap<>(scriptResult.size() + 1);
                    markdownParam.put("singleImageUrl", imageUrl);
                    markdownParam.putAll(scriptResult);
                    String infoString = Html2ImageUtils.processTemplateInfoString(template.getContentTemplate(), markdownParam);
                    log.info("Html内容:{}",infoString);
                    // 生成markdown 内容
                    if (StringUtils.isEmpty(infoString)) {
                        log.warn("解析 markdown 模板为空[{}]", scriptResult);
                        return;
                    }
                    sendMessage(receiver, infoString, rule.getRuleName());
                });
    }

//    public void pushMember(OpMessageRuleReceiver receiver,Map<String, Object> context,OpMessageRule rule){
//        OpMessageRuleGroovyDTO ruleDto = new OpMessageRuleGroovyDTO();
//        ruleDto.setContext(context);
//        BeanUtils.copyProperties(rule, ruleDto);
//        ruleDto.setReceiver(receiver);
//        //把钉钉群配置的json参数添加到groovy上下文对象中 如区域列表,片区列表,分支列表
//        fillDataToOpMessageRuleGroovyDTO(ruleDto,receiver);
//        String params = receiver.getParams();
//        JSONObject json = new JSONObject();
//        //设置图片性质 动态图或静态图
//        json.put(IMAGE_NATURE,rule.getImageNature());
//        //规则的参数支持
//        if (StringUtils.isNotBlank(ruleDto.getParams())) {
//            JSONObject ps = JSONObject.parseObject(ruleDto.getParams());
//            if (ps.containsKey(IMAGE_PARAMS_KEY)) {
//                json.putAll(ps.getJSONObject(IMAGE_PARAMS_KEY));
//            }
//        }
//        //接收人维度的参数支持 优先级大于规则
//        if (StringUtils.isNotBlank(params)) {
//            JSONObject ps = JSONObject.parseObject(params);
//            if (ps.containsKey(IMAGE_PARAMS_KEY)) {
//                json.putAll(ps.getJSONObject(IMAGE_PARAMS_KEY));
//            }
//        }
//
//        Map<String, Object> scriptResult = (Map<String, Object>) groovyService.executeForCode(MsgPushService.SCRIPT_TYPE, rule.getRuleCode(), ruleDto);
//
//        if (MapUtils.isEmpty(scriptResult)) {
//            log.warn("脚本返回结果为空[{}]", scriptResult);
//            return;
//        }
//        String imageContent = Html2ImageUtils.processTemplateInfoString(template.getSingleImageTemplate(), scriptResult);
//        String imageUrl = StringUtils.isNotBlank(imageContent) ? html2ImageAndUpload(json, imageContent) : null;
//        Map<String, Object> markdownParam = new HashMap<>(scriptResult.size() + 1);
//        markdownParam.put("singleImageUrl", imageUrl);
//        markdownParam.putAll(scriptResult);
//        String infoString = Html2ImageUtils.processTemplateInfoString(template.getContentTemplate(), markdownParam);
//        log.info("Html内容:{}",infoString);
//        // 生成markdown 内容
//        if (StringUtils.isEmpty(infoString)) {
//            log.warn("解析 markdoan 模板为空[{}]", scriptResult);
//            return;
//        }
//        sendMessage(receiver, infoString, rule.getRuleName());
//    }

    @Override
    public void pushAudit(List<OpMessagePush> pushes) {
        throw new IllegalArgumentException("markdown  不支持审核推送");
    }

    private String html2ImageAndUpload(JSONObject pm, String html) {
        pm.put("htmlValue", html);
//        return apiCall(() -> imageFacade.image(pm)).getUrl();
        return apiCall(() -> dynamicsImageService.image(pm)).getUrl();
    }

    private <T> T apiCall(Supplier<CommonResult<T>> api) {
        CommonResult<T> tCommonResult = api.get();
        if (tCommonResult.isSuccess()) {
            return tCommonResult.getData();
        }
        throw new MSBizNormalException(tCommonResult.getErrorCode(), tCommonResult.getMessage());
    }

    private String sendMessage(OpMessageRuleReceiver receiver, String content, String title) {
        if (OpMessageReceiverTypeEnum.CHAT.isMe(receiver.getReceiverType())) {
            //如果接收者配置过机器人，则用机器人发送。
            Boolean withRobot = false;
            if(StringUtils.isNotEmpty(receiver.getRobotToken())&&StringUtils.isNotEmpty(receiver.getRobotTokenSecret())){
                withRobot = true;
            }
            if (withRobot){
                title = Optional.ofNullable(title).orElse("消息推送");
                JSONObject jsonObject = new JSONObject();
                JSONObject json = new JSONObject();
                json.put("title", title);
                json.put("text", content);
                return dingTalkService.sendChatMediaMessageWithGroupRobot(receiver.getRobotToken(),receiver.getRobotTokenSecret(),json);
            }
            //目前仅支持发送到群
            return dingTalkService.sendChatMarkdownMessage(receiver.getReceiver(), content, title);
        } else if (OpMessageReceiverTypeEnum.PERSON.isMe(receiver.getReceiverType())|| OpMessageReceiverTypeEnum.CONTEXT.isMe(receiver.getReceiverType()) ) {
            try {
                return robotService.pushMarkdown2User(Collections.singletonList(receiver.getReceiver()), content, title);
            } catch (BusinessException e) {
                log.info("消息推送失败:{}", JSON.toJSONString(receiver),e);
                //如果是钉钉userid或者钉钉服务器有问题 那就推送userid错误事件 但是不终止程序
                if (Objects.equals(e.getCode(), OperationErrorEnum.DING_TALK_API_ERROR_USERID.getCode())
                        || Objects.equals(e.getCode(), OperationErrorEnum.DING_TALK_API_ERROR.getCode())) {
                    busEngine.publish(new DingTalkUserIdErrorEvent(Collections.singletonList(receiver.getReceiver())));
                    return null;
                } else {
                    throw e;
                }
            }

        }
        throw new UnsupportedOperationException("MarkDown仅支持个人和钉钉群");
    }


}
