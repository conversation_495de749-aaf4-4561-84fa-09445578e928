package com.cfpamf.ms.insur.operation.base.mq;

import com.cfpamf.ms.insur.operation.base.util.RedisUtil;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.connection.CorrelationData;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.UUID;


/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class RabbitMqUtils {

    public final static String CACHE_MQ_MESSAGE_REQUEST_ID_KEY = "mq:requestId:";

    private final static String SPLIT = ":";

    @Autowired
    private RedisUtil redisUtils;

    @Autowired
    @Qualifier("insuranceOperationRabbitTemplate")
    private RabbitTemplate dbcRabbitTemplate;



    /**
     * 推送消息至MQ,
     * 步骤:
     * 1. 将消息体缓存至redis
     * 2. 推送消息至MQ
     *
     * PS: 若消息推送MQ失败, 则基于redis中的缓存, 做补偿, 重新推送, 消息推送数据可能重复, 调用方需保证幂等.
     *
     * @param exchange
     * @param routingKey
     * @param message
     */
    public <T> void send(String exchange, String routingKey, T message) {
        String requestId = CACHE_MQ_MESSAGE_REQUEST_ID_KEY + exchange + SPLIT + routingKey + SPLIT + UUID.randomUUID().toString();

        MqMessageVo<T> mqMessageVo = new MqMessageVo<>();
        mqMessageVo.setExchange(exchange);
        mqMessageVo.setRoutingKey(routingKey);
        mqMessageVo.setPushNum(1);
        mqMessageVo.setMessage(message);
        //缓存消息对象
        redisUtils.set(requestId, mqMessageVo);
        //消息投递
        dbcRabbitTemplate.convertAndSend(exchange, routingKey, mqMessageVo, new CorrelationData(requestId));
    }

    /**
     * 消息重试
     *
     * @param messageVo
     * @param <T>
     */
    public <T> void sendRetry(MqMessageVo messageVo) {
        send(messageVo.getExchange(), messageVo.getRoutingKey(), messageVo.getMessage());
    }

    /**
     * 直接发送消息
     *
     * @param message
     * @param exchange
     * @param routingKey
     * @param <T>
     */
    public <T> void sendMessage(T message, String exchange, String routingKey) {
        dbcRabbitTemplate.convertAndSend(exchange, routingKey, message);
    }

    /**
     * 消息消费手动应答
     *
     * @param isAck
     * @param deliveryTag
     * @param channel
     * @param requeue
     */
    public void manualAcknowledgeMode(boolean isAck, long deliveryTag, Channel channel, boolean requeue) {
        try {
            // 手动应答
            if (isAck) {
                channel.basicAck(deliveryTag, false);
            } else {
                channel.basicNack(deliveryTag, false, requeue);
            }
        } catch (Exception ex) {
            throw new RuntimeException("消息消费手动应答出错。原因：" + ex.getMessage());
        }
    }

    /**
     * 消息消费手动应答
     * 采用reject模式，因为nack虽然有同样的效果，假如消费者之前存在未ack的消息，则会重新进度队列
     * 理论上业务没有BUG，即MQ消息与业务及时ack 应该不会出现上述场景
     *
     * @param isAck
     * @param deliveryTag
     * @param channel
     * @param requeue     true重入队列，false不重入
     */
    public void manualAckAndReject(boolean isAck, long deliveryTag, Channel channel, boolean requeue) {
        try {
            // 手动应答
            if (isAck) {
                channel.basicAck(deliveryTag, false);
            } else {
                channel.basicReject(deliveryTag, requeue);
            }
        } catch (Exception ex) {
            throw new RuntimeException("消息消费手动应答出错。原因：" + ex.getMessage());
        }
    }


}