package com.cfpamf.ms.insur.operation.planbook.form;

import com.cfpamf.ms.insur.operation.planbook.entity.OperationPlanBookInsured;
import com.cfpamf.ms.insur.operation.planbook.entity.OperationPlanBookProposer;
import com.cfpamf.ms.insur.operation.planbook.entity.OperationPlanBookValue;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/12/12 19:25
 * @Version 1.0
 */
@Data
public class OperationPlanBookMakeInfoForm {

    private Integer id;

    private Integer productId;

    private Integer planId;

    /**
     * 客户姓名
     */
    @ApiModelProperty("客户姓名")
    private String customerName;

    /**
     * 客户性别（男：male；女：female）
     */
    @ApiModelProperty("客户性别（男：male；女：female）")
    private String customerSex;

    /**
     * 留言内容
     */
    @ApiModelProperty("留言内容")
    private String leaveNote;

    /**
     * 快照
     */
    @ApiModelProperty("快照")
    private String snapshot;

    @ApiModelProperty(value = "已选中的责任")
    List<OperationPlanBookDutyForm> dutyFormList;

    @ApiModelProperty(value = "已选中的费率因子")
    List<OperationPlanBookFactorForm> factorFormList;

    @ApiModelProperty("被保险人信息")
    private OperationPlanBookInsured person;

    @ApiModelProperty(value = "投保人信息")
    private OperationPlanBookProposer proposer;

    @ApiModelProperty(value = "现金价值")
    private List<OperationPlanBookValue> planBookValueList;

}
