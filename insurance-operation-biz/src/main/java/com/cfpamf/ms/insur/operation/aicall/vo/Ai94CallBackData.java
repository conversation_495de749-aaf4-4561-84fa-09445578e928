package com.cfpamf.ms.insur.operation.aicall.vo;

import io.swagger.annotations.ApiModelProperty;

import java.util.List;

public class Ai94CallBackData {
    @ApiModelProperty(value = "外呼号码", required = false)
    private String number;

    @ApiModelProperty(value = "号码MD5", required = false)
    private String numberMD5;

    @ApiModelProperty(value = "批次号", required = false)
    private String batchId;

    @ApiModelProperty(value = "外呼类型", required = true)
    private Integer callType;

    @ApiModelProperty(value = "用户自定义标签", required = false)
    private String tag;

    @ApiModelProperty(value = "外呼ID", required = true)
    private String callId;

    @ApiModelProperty(value = "任务ID", required = true)
    private Integer taskId;

    @ApiModelProperty(value = "任务名称", required = true)
    private String taskName;

    @ApiModelProperty(value = "AI话术ID", required = false)
    private Integer templateId;

    @ApiModelProperty(value = "AI话术模板名称", required = false)
    private String templateName;

    @ApiModelProperty(value = "外呼状态编码", required = true)
    private Integer statusCode;

    @ApiModelProperty(value = "外呼状态描述", required = true)
    private String statusDescription;

    @ApiModelProperty(value = "转人工状态编码", required = true)
    private Integer transferStatusCode;

    @ApiModelProperty(value = "转人工状态", required = true)
    private String transferStatus;

    @ApiModelProperty(value = "分配坐席ID", required = false)
    private Integer agentId;

    @ApiModelProperty(value = "坐席标签", required = false)
    private String agentTag;

    @ApiModelProperty(value = "坐席分机", required = false)
    private String agentExtension;

    @ApiModelProperty(value = "导入时间", required = true)
    private String importTime;

    @ApiModelProperty(value = "拨号时间", required = true)
    private String callBeginTime;

    @ApiModelProperty(value = "振铃时长", required = true)
    private Integer ringTime;

    @ApiModelProperty(value = "通话接通时间", required = false)
    private String answerTime;

    @ApiModelProperty(value = "AI通话时长", required = true)
    private String speakingTime;

    @ApiModelProperty(value = "AI通话时长(秒)", required = true)
    private Integer speakingDuration;

    @ApiModelProperty(value = "挂断时间", required = true)
    private String hangupTime;

    @ApiModelProperty(value = "对话轮次", required = true)
    private String speakingTurns;

    @ApiModelProperty(value = "人工通话时长", required = true)
    private String agentSpeakingTime;

    @ApiModelProperty(value = "人工通话时长（秒）", required = true)
    private Integer agentSpeakingDuration;

    @ApiModelProperty(value = "意向标签", required = true)
    private String intentTag;

    @ApiModelProperty(value = "意向说明", required = true)
    private String intentDescription;

    @ApiModelProperty(value = "个性标签", required = false)
    private String individualTag;

    @ApiModelProperty(value = "回复关键词", required = false)
    private String keywords;

    @ApiModelProperty(value = "挂机方式", required = true)
    private int hangupType;

    @ApiModelProperty(value = "挂机短信", required = true)
    private String sms;

    @ApiModelProperty(value = "对话录音", required = false)
    private String chatRecord;

    @ApiModelProperty(value = "聊天记录", required = false)
    private List<Ai94CallBackChat> chats;

    @ApiModelProperty(value = "加微", required = false)
    private Integer addWx;

    @ApiModelProperty(value = "加微进度", required = false)
    private String addWxStatus;

    @ApiModelProperty(value = "是否接通重呼", required = true)
    private Integer answerRecall;

    @ApiModelProperty(value = "参数值", required = false)
    private String properties;

    @ApiModelProperty(value = "拦截原因", required = false)
    private String interceptReason;

    @ApiModelProperty(value = "企业id", required = true)
    private Integer companyId;

    @ApiModelProperty(value = "sip编码", required = false)
    private Integer sipCode;

    @ApiModelProperty(value = "转接人工时间", required = false)
    private String transferTime;

    @ApiModelProperty(value = "坐席组ID", required = false)
    private Integer seatsGroupId;

    @ApiModelProperty(value = "坐席组名称", required = false)
    private String seatsGroupName;
}
