package com.cfpamf.ms.insur.operation.base.event;

import com.cfpamf.ms.insur.operation.ai.coze.CozeExecutor;
import com.cfpamf.ms.insur.operation.base.util.RedisUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import groovy.lang.Binding;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 事件注册器 && groovy binding
 *
 * <AUTHOR> 2022/8/11 15:17
 */
@Configuration
@Slf4j
@Order(Integer.MIN_VALUE)
public class ApplicationStartedCommandRunner implements CommandLineRunner {

    @Autowired
    ApplicationContext applicationContext;

    @Autowired
    EventBusEngine busEngine;

    @Autowired
    Binding binding;

    @Autowired
    CozeExecutor cozeExecutor;


    @Override
    public void run(String... args) throws Exception {

        log.info("开始注册event handler");
        Map<String, BaseEventHandler> beans = applicationContext.getBeansOfType(BaseEventHandler.class);

        Collection<BaseEventHandler> values = beans.values();
        //根据name排序注册
        List<BaseEventHandler> collect = values.stream().sorted(Comparator.comparing(a -> a.getClass().getName())).collect(Collectors.toList());
        for (BaseEventHandler eventHandler : collect) {
            busEngine.register(eventHandler);
        }

        log.info("开始赋值 groovy binding");

        Map<String, Object> beanMap = applicationContext.getBeansWithAnnotation(Service.class);
        beanMap.put("redisUtil", applicationContext.getBean(RedisUtil.class));
        //遍历设置所有bean,可以根据需求在循环中对bean做过滤
        for (String beanName : beanMap.keySet()) {
            binding.setVariable(beanName, beanMap.get(beanName));
        }
        binding.setVariable("jacksonObjectMapper", applicationContext.getBean(ObjectMapper.class));
        binding.setVariable("log", log);
    }
}
