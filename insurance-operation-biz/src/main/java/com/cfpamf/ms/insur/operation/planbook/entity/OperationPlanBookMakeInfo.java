package com.cfpamf.ms.insur.operation.planbook.entity;

import com.cfpamf.ms.insur.operation.base.entity.BaseIntegerEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Table;

/**
 * (OperationPlanBookMakeInfo)实体类
 *
 * <AUTHOR>
 * @since 2021-12-12 18:53:11
 */
@Data
@ApiModel()
@Table(name = "operation_plan_book_make_info")
public class OperationPlanBookMakeInfo extends BaseIntegerEntity {

    @Column(name = "product_id")
    private Integer productId;

    @Column(name = "plan_id")
    private Integer planId;

    /**
     * 客户姓名
     */
    @ApiModelProperty("客户姓名")
    @Column(name = "customer_name")
    private String customerName;
    /**
     * 客户性别（男：male；女：female）
     */
    @ApiModelProperty("客户性别")
    @Column(name = "customer_sex")
    private String customerSex;
    /**
     * 留言内容
     */
    @ApiModelProperty("留言内容")
    @Column(name = "leave_note")
    private String leaveNote;
    /**
     * 快照
     */
    @ApiModelProperty("快照")
    @Column(name = "snapshot")
    private String snapshot;

}

