package com.cfpamf.ms.insur.operation.msg.pojo.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @description 区域业绩目标
 * @date 2021/9/1 3:54 下午
 * @Version 1.0
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class RegionGoalDTO {
    @ApiModelProperty("时间")
    LocalDate rptDate;
    @ApiModelProperty("区域名字")
    String regionName;
    @ApiModelProperty("周期")
    String term;
    @ApiModelProperty("产品类型")
    String productClass;
    @ApiModelProperty("业绩目标")
    BigDecimal goal;
    @ApiModelProperty("实际业绩")
    BigDecimal amt;

    @ApiModelProperty("业绩达成率")
    BigDecimal rate;


}
