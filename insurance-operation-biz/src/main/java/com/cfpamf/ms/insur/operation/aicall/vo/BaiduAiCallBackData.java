package com.cfpamf.ms.insur.operation.aicall.vo;

import com.cfpamf.ms.insur.operation.aicall.entity.BaiduCallBackRecord;
import lombok.Data;

import java.util.List;

@Data
public class BaiduAiCallBackData {
    private String sessionId;
    private Long tenantId;
    private String taskId;
    private String taskName;
    private String robotId;
    private Long memberId;
    private String mobile;
    private Integer callTimes;
    private String callerNum;
    private Integer endType;
    private Integer callType;
    private String endTypeReason;
    private String contactUUID;
    private Long fileId;
    private List<BaiduAiCallBackRecord> record;
    private Integer durationTimeLen;
    private Integer ringingTimeLen;
    private Integer talkingTimeLen;
    private Long startTime;
    private Long ringStartTime;
    private Long talkingStartTime;
    private Long endTime;
    private String intent;
    private List<String> action;
    private Boolean isRobotHangup;
    private int transResult;
}
