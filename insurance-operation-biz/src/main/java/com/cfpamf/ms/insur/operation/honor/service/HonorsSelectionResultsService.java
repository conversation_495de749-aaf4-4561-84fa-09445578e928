package com.cfpamf.ms.insur.operation.honor.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cfpamf.common.ms.exception.MSBizNormalException;
import com.cfpamf.ms.insur.operation.fegin.bms.enums.ExcptEnum;
import com.cfpamf.ms.insur.operation.honor.dao.HonorListImportMapper;
import com.cfpamf.ms.insur.operation.honor.dao.HonorSelectionResultsMapper;
import com.cfpamf.ms.insur.operation.honor.dto.HonorIconResultDto;
import com.cfpamf.ms.insur.operation.honor.dto.HonorSelectionResultDto;
import com.cfpamf.ms.insur.operation.honor.dto.*;
import com.cfpamf.ms.insur.operation.honor.enums.EnumHonorLevel;
import com.cfpamf.ms.insur.operation.honor.enums.EnumHonorPeriod;
import com.cfpamf.ms.insur.operation.honor.enums.EnumHonorSelectionType;
import com.cfpamf.ms.insur.operation.honor.po.HonorListImportPo;
import com.cfpamf.ms.insur.operation.honor.po.HonorsSelectionResultsPo;
import com.cfpamf.ms.insur.operation.honor.query.HonorIconResultQuery;
import com.cfpamf.ms.insur.operation.honor.query.HonorSelectionResultQuery;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.groupingBy;
import static java.util.stream.Collectors.toMap;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
@AllArgsConstructor
public class HonorsSelectionResultsService {

    HonorSelectionResultsMapper honorSelectionResultsMapper;

    CalculateHonorResultService calculateHonorResultService;

    HonorListImportMapper honorListImportMapper;

    /**
     * 计算系统评选荣誉结果
     * @param rules 荣誉规则
     */
    public void calculateHonorResults(List<HonorCalculateRule> rules) {
        for (HonorCalculateRule rule : rules) {
            if (!StringUtils.isBlank(rule.getLevel())) {
                //1、执行评选结果计算
                List<HonorsCalculateResults> results = calculateHonorResultService.calculate(rule);

                if (!CollectionUtils.isEmpty(results)) {
                    List<HonorsSelectionResultsPo> poList = results.stream().map(r -> {
                        HonorsSelectionResultsPo po = new HonorsSelectionResultsPo();
                        BeanUtils.copyProperties(r, po);
                        return po;
                    }).collect(Collectors.toList());

                    //2、将结果插入到honor_selection_results表
                    honorSelectionResultsMapper.insertList(poList);
                }
            }
        }
    }

    /**
     * 计算人工导入荣誉结果
     * @param dto dto
     */
    public void calculateHonor(HonorListImportDTO dto, List<HonorListExcelDTO> excelDtoS) {
        //1、删除原有的荣誉评选结果
        honorSelectionResultsMapper.deleteByConfigurationId(dto.getHonorsConfigurationsId());

        //1、执行评选结果计算
        HonorCalculateRule rule = new HonorCalculateRule();
        BeanUtils.copyProperties(dto, rule);
        rule.setId(dto.getHonorsConfigurationsId());
        List<String> codes = initCodes(dto,excelDtoS);
        rule.setCodes(codes);
        List<HonorsCalculateResults> results = calculateHonorResultService.calculateByPeople(rule);

        //2.根据区域名称-分支名称-员工工号分组
        Map<String,HonorsCalculateResults> map = results.stream()
                .collect(toMap(resultDto -> getUuid(dto.getLevel(),resultDto.getAreaCode(),resultDto.getBchCode(),resultDto.getEmpCode())
                        , d -> d));

        List<HonorsSelectionResultsPo> poList = excelDtoS.stream().map(r -> {
            HonorsSelectionResultsPo po = new HonorsSelectionResultsPo();

            HonorsCalculateResults resultDto = map.get(getUuid(dto.getLevel(),r.getAreaCode(),r.getBchCode(),r.getEmpCode()));
            if (Objects.nonNull(resultDto)) {
                BeanUtils.copyProperties(resultDto, po);
            }
            //覆盖指标
            coverNorm(po,r,dto);
            return po;
        }).collect(Collectors.toList());

        //3、保存荣誉清单
        honorSelectionResultsMapper.insertList(poList);
    }

    private void coverNorm(HonorsSelectionResultsPo po, HonorListExcelDTO excelDTO,HonorListImportDTO dto) {
        po.setRankNum(excelDTO.getRankNum());
        //区域、分支、员工信息
        po.setAreaCode(StringUtils.isBlank(excelDTO.getAreaCode())?po.getAreaCode():excelDTO.getAreaCode());
        po.setAreaName(StringUtils.isBlank(excelDTO.getAreaName())?po.getAreaName():excelDTO.getAreaName());
        po.setBchCode(StringUtils.isBlank(excelDTO.getBchCode())?po.getBchCode():excelDTO.getBchCode());
        po.setBchName(StringUtils.isBlank(excelDTO.getBchName())?po.getBchName():excelDTO.getBchName());
        po.setEmpCode(StringUtils.isBlank(excelDTO.getEmpCode())?po.getEmpCode():excelDTO.getEmpCode());
        po.setEmpName(StringUtils.isBlank(excelDTO.getEmpName())?po.getEmpName():excelDTO.getEmpName());
        //指标信息
        if (EnumHonorPeriod.YEAR.getCode().equals(dto.getPeriod())) {
            po.setSyAssessConvertInsuranceAmt(Objects.isNull(excelDTO.getAssessConvertInsuranceAmt())?po.getSyAssessConvertInsuranceAmt():excelDTO.getAssessConvertInsuranceAmt());
            po.setSyInsuranceAmt(Objects.isNull(excelDTO.getInsuranceAmt())?po.getSyInsuranceAmt():excelDTO.getInsuranceAmt());
            po.setSyAssessConvertInsuranceAmtAvg(Objects.isNull(excelDTO.getAssessConvertInsuranceAmtAvg())?po.getSyAssessConvertInsuranceAmtAvg():excelDTO.getAssessConvertInsuranceAmtAvg());
            po.setSyInsuranceAmtAvg(Objects.isNull(excelDTO.getInsuranceAmtAvg())?po.getSyInsuranceAmtAvg():excelDTO.getInsuranceAmtAvg());
            po.setSyInsuranceRetentionRate(Objects.isNull(excelDTO.getInsuranceRetentionRate())?po.getSyInsuranceRetentionRate():excelDTO.getInsuranceRetentionRate());
            po.setSyOfflineLoanInsuranceRate(Objects.isNull(excelDTO.getOfflineLoanInsuranceRate())?po.getSyOfflineLoanInsuranceRate():excelDTO.getOfflineLoanInsuranceRate());
        }

        if (EnumHonorPeriod.QUARTER.getCode().equals(dto.getPeriod())) {
            po.setSqAccessConvertInsuranceAmt(Objects.isNull(excelDTO.getAssessConvertInsuranceAmt())?po.getSqAccessConvertInsuranceAmt():excelDTO.getAssessConvertInsuranceAmt());
            po.setSqInsuranceAmt(Objects.isNull(excelDTO.getInsuranceAmt())?po.getSqInsuranceAmt():excelDTO.getInsuranceAmt());
            po.setSqAssessConvertInsuranceAmtAvg(Objects.isNull(excelDTO.getAssessConvertInsuranceAmtAvg())?po.getSqAssessConvertInsuranceAmtAvg():excelDTO.getAssessConvertInsuranceAmtAvg());
            po.setSqInsuranceAmtAvg(Objects.isNull(excelDTO.getInsuranceAmtAvg())?po.getSqInsuranceAmtAvg():excelDTO.getInsuranceAmtAvg());
            po.setSqInsuranceRetentionRate(Objects.isNull(excelDTO.getInsuranceRetentionRate())?po.getSqInsuranceRetentionRate():excelDTO.getInsuranceRetentionRate());
            po.setSqOfflineLoanInsuranceRate(Objects.isNull(excelDTO.getOfflineLoanInsuranceRate())?po.getSqOfflineLoanInsuranceRate():excelDTO.getOfflineLoanInsuranceRate());
        }

        if (EnumHonorPeriod.MONTH.getCode().equals(dto.getPeriod())) {
            po.setSmAssessConvertInsuranceAmt(Objects.isNull(excelDTO.getAssessConvertInsuranceAmt())?po.getSmAssessConvertInsuranceAmt():excelDTO.getAssessConvertInsuranceAmt());
            po.setSmInsuranceAmt(Objects.isNull(excelDTO.getInsuranceAmt())?po.getSmInsuranceAmt():excelDTO.getInsuranceAmt());
            po.setSmAssessConvertInsuranceAmtAvg(Objects.isNull(excelDTO.getAssessConvertInsuranceAmtAvg())?po.getSmAssessConvertInsuranceAmtAvg():excelDTO.getAssessConvertInsuranceAmtAvg());
            po.setSmInsuranceAmtAvg(Objects.isNull(excelDTO.getInsuranceAmtAvg())?po.getSmInsuranceAmtAvg():excelDTO.getInsuranceAmtAvg());
            po.setSmInsuranceRetentionRate(Objects.isNull(excelDTO.getInsuranceRetentionRate())?po.getSmInsuranceRetentionRate():excelDTO.getInsuranceRetentionRate());
            po.setSmOfflineLoanInsuranceRate(Objects.isNull(excelDTO.getOfflineLoanInsuranceRate())?po.getSmOfflineLoanInsuranceRate():excelDTO.getOfflineLoanInsuranceRate());
        }
        po.setHonorsRulesConfigurationsId(dto.getHonorsConfigurationsId());
        po.setLevel(dto.getLevel());
        po.setPeriod(dto.getPeriod());
    }

    private List<String> initCodes(HonorListImportDTO dto,List<HonorListExcelDTO> excelDtoS) {
        List<String> codes = new ArrayList<>();
        if (EnumHonorLevel.AREA.getCode().equals(dto.getLevel())) {
            codes = excelDtoS.stream().map(HonorListExcelDTO::getAreaCode).collect(Collectors.toList());
        }
        if (EnumHonorLevel.BCH.getCode().equals(dto.getLevel())) {
            codes = excelDtoS.stream().map(HonorListExcelDTO::getBchCode).collect(Collectors.toList());
        }
        if (EnumHonorLevel.EMP.getCode().equals(dto.getLevel())) {
            codes = excelDtoS.stream().map(HonorListExcelDTO::getEmpCode).collect(Collectors.toList());
        }
        return codes;
    }

    /**
     * 根据荣誉评选规则id获取荣誉清单
     * @param query 查询条件
     * @return 荣誉清单
     */
    public PageInfo<HonorsCalculateResults> getResultsByConfigurationId(HonorSelectionResultQuery query) {
        if (query.getPageNo() > 0) {
            PageHelper.startPage(query.getPageNo(), query.getPageSize());
        }
        List<HonorsCalculateResults> list = honorSelectionResultsMapper.getResultsByConfigurationId(query);
        return new PageInfo<>(list);
    }

    /**
     * 根据荣誉对象获取荣誉图标
     * @param query
     * @return 荣誉图标列表
     */
    public List<HonorIconResultDto> getResultsByLevelAndCode(HonorIconResultQuery query) {
        if (query.getIsGlobalView()){
            return honorSelectionResultsMapper.getResultsForGlobalView(query);
        }else {
            if(StringUtils.isBlank(query.getLevel())){
                log.warn("参数错误：{}", JSONObject.toJSONString(query));
                throw new MSBizNormalException(ExcptEnum.PARAMS_ERROR.getCode(),"评选对象不能为空");
            }
            if(StringUtils.isBlank(query.getAreaCode())&&StringUtils.isBlank(query.getBchCode())&&StringUtils.isBlank(query.getEmpCode())){
                log.warn("参数错误：{}", JSONObject.toJSONString(query));
                throw new MSBizNormalException(ExcptEnum.PARAMS_ERROR.getCode(),"评选对象编码不能为空");
            }
            return honorSelectionResultsMapper.getResultsByLevelAndCode(query);
        }
    }
    public List<HonerIconResultPerEmpDTO> getEmpHonorsForBch(HonorIconResultQuery query){
        // 初始化一个空列表，用于存储最终的员工荣誉信息DTO对象
        List<HonerIconResultPerEmpDTO> empDTOs = new ArrayList<>();

        // 通过调用mapper方法，获取员工荣誉信息列表
        List<HonorIconResultDto> list = honorSelectionResultsMapper.getEmpHonorsForBch(query);

        // 将荣誉信息按照员工代码进行分组，以便后续处理
        Map<String,List<HonorIconResultDto>> resultMap = list.stream().collect(groupingBy(HonorIconResultDto::getEmpCode, Collectors.toList()));

        // 遍历分组后的结果，为每个员工创建一个HonerIconResultPerEmpDTO对象，并填充信息
        resultMap.forEach((key,value)->{
            HonerIconResultPerEmpDTO dto = new HonerIconResultPerEmpDTO();
            dto.setEmpCode(key); // 设置员工代码
            dto.setEmpName(value.get(0).getEmpName()); // 设置员工姓名，取第一个元素的姓名，假设所有员工姓名相同

            // 初始化一个空列表，用于存储该员工的荣誉信息
            List<HonorIconResultDto> honors = new ArrayList<>();

            // 过滤出荣誉名称不为空的荣誉信息，添加到该员工的荣誉列表中
            value.forEach(honor->{
                if(StringUtils.isNotBlank(honor.getHonorName())){
                    honors.add(honor);
                }
            });

            dto.setHonors(honors); // 设置该员工的荣誉列表
            empDTOs.add(dto); // 将该员工的荣誉信息DTO添加到最终结果列表中
        });

        return empDTOs.stream()
               .sorted((o1, o2) -> {
                   int size1 = o1.getHonors().size();
                   int size2 = o2.getHonors().size();
                   return Integer.compare(size2, size1);
               })
               .collect(Collectors.toList());
    }

    /**
     * 根据荣誉计算规则查询荣誉在评选中的结果
     * 此方法主要用于查询和处理荣誉计算结果，包括全局视图和单个对象的荣誉排名
     *
     * @param rule 荣誉计算规则对象，定义了查询和计算荣誉所需的规则和条件
     * @return 返回一个封装了荣誉计算结果的HonorResultDto对象，包括所有匹配的荣誉计算结果和可能的单个对象荣誉排名
     */
    public HonorResultDto searchHonorInAction(HonorCalculateRule rule) {
        // 初始化荣誉结果数据传输对象
        HonorResultDto result = new HonorResultDto();
        // 如果荣誉计算规则不是人工评选，则执行计算操作
        if(!EnumHonorSelectionType.PEOPLE.getCode().equals(rule.getSelectionType())){
            // 根据提供的规则查询荣誉计算结果列表
            List<HonorsCalculateResults>  honorsCalculateResults = calculateHonorResultService.searchHonorInAction(rule);
            // 设置查询到的荣誉计算结果到结果传输对象中
            result.setResults(honorsCalculateResults);
            // 如果荣誉计算规则不是全局视图，则额外查询当前对象的荣誉排名
            if(!rule.getIsGlobalView()){
                // 查询单个对象的荣誉排名
                HonorsCalculateResults singleResult = this.getSingleHonorRank(honorsCalculateResults,rule);

                // 将单个对象的荣誉排名设置到结果传输对象中
                result.setCurrentObjectResult(singleResult);
            }
        }
        // 返回封装了荣誉计算结果和可能的单个对象荣誉排名的结果传输对象
        return result;
    }

    /**
     * 计算评选中的入榜差值
     * 本方法旨在为评选活动中的候选人计算其与入榜候选人的差值，以衡量评选的激烈程度
     *
     * @param result 包含评选结果信息的对象，用于计算差值
     */
    public void calcGapForHonorInAction(HonorResultDto result){

        List<HonorsCalculateResults>  tempRankList=  result.getResults().stream().filter(
                it -> {return it.getRankNum() != null && it.getRankNum()<= result.getHonorRulesConfiguration().getSelectionNumber();})
                .collect(Collectors.toList());
        HonorsCalculateResults lastRankResult = tempRankList.get(tempRankList.size()-1);
        // 获取当前候选人排名信息
        HonorsCalculateResults currentObjectRank = result.getCurrentObjectResult();
        // 获取评选指标名称
        String metricName = result.getHonorRulesConfiguration().getHonorNormCode();
        // 如果是多指标评选，则不计算差值
        if(metricName.split(",").length>2){
            return;
        }
        // 如果评选类型为人工评选，则不计算差值
        if(result.getHonorRulesConfiguration().getSelectionType().equals(EnumHonorSelectionType.PEOPLE.getCode())){
            return;
        }
        // 当前候选人排名低于榜末候选人时，计算差值
        if(currentObjectRank.getRankNum() != null && lastRankResult.getRankNum() < currentObjectRank.getRankNum()){
            // 将排名结果对象转换为JSON格式，便于差值计算
            JSONObject lastRankResultJson = (JSONObject)JSON.toJSON(lastRankResult);
            JSONObject currentObjectRankJson = (JSONObject)JSON.toJSON(currentObjectRank);
            // 根据评选指标名称，计算差值
            metricName = getCamelCase(metricName);
            currentObjectRank.setGapValue(lastRankResultJson.getBigDecimal(metricName).subtract(currentObjectRankJson.getBigDecimal(metricName)));
        }
    }


    //_转驼峰
    public String getCamelCase(String str) {
        String[] words = str.split("_");
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < words.length; i++) {
            String word = words[i];
            if (i > 0) {
                sb.append(Character.toUpperCase(word.charAt(0)));
                sb.append(word.substring(1));
            } else {
                sb.append(word);
            }
        }
        return sb.toString();
    }
    /**
     * 根据配置ID查询荣誉结果，专为助手设计。
     *
     * 此方法通过查询荣誉评选结果的数据库记录，并将其转换为更容易使用的DTO（数据传输对象）格式。
     * 它接受一个查询对象作为参数，该对象包含了查询所需的所有条件。
     * 返回一个包含荣誉计算结果的DTO，这些结果根据查询参数进行过滤和计算。
     *
     * @param query 查询条件对象，包含所需荣誉结果的筛选条件。
     * @return HonorResultDto 包含根据查询条件计算得出的荣誉结果的DTO。
     */
    public HonorResultDto getResultsByConfigurationIdForAssistant(HonorCalculateRule query){
        // 初始化返回的荣誉结果DTO
        HonorResultDto result = new HonorResultDto();

        // 根据查询条件查询荣誉评选结果
        List<HonorsSelectionResultsPo> honorsSelectionResultsPos = honorSelectionResultsMapper.getResultsByConfigurationIdForAssistant(query);

        // 将荣誉评选结果转换为荣誉计算结果的列表
        // 如果查询结果为空，初始化一个空列表，避免空指针异常
        List<HonorsCalculateResults> honorsCalculateResultslist = Optional.ofNullable(honorsSelectionResultsPos).orElse(new ArrayList<>()).stream().map(po -> {
            HonorsCalculateResults honorsCalculateResults = new HonorsCalculateResults();
            // 使用BeanUtils复制属性，从选择结果的PO对象到计算结果的DTO对象
            BeanUtils.copyProperties(po, honorsCalculateResults);
            return honorsCalculateResults;
        }).collect(Collectors.toList());
        // 填充头像
        calculateHonorResultService.fillAvatars(query, honorsCalculateResultslist);
        // 如果当前评选对象不是全局视角，获取当前传参对象的排名
        if (!query.getIsGlobalView()){
            //获取当前传参对象的排名
            HonorsCalculateResults currentResults = getSingleHonorRank(honorsCalculateResultslist,query);
            result.setCurrentObjectResult(currentResults);
        }
        // 将转换后的荣誉计算结果设置到返回的DTO中
        result.setResults(honorsCalculateResultslist);
        // 返回包含荣誉计算结果的DTO
        return result;
    }

    /**
     * 根据不同的评选对象级别（区域、分支、员工）获取单个荣誉排名
     *
     * @param honorsCalculateResultslist 荣誉计算结果列表
     * @param query 荣誉计算查询对象，包含评选对象级别、代码、名称等信息
     * @return 单个荣誉排名的计算结果
     */
    private HonorsCalculateResults getSingleHonorRank(List<HonorsCalculateResults> honorsCalculateResultslist, HonorCalculateRule query) {
        HonorsCalculateResults singleHonorRank = null;
        Boolean isImportResult = checkImportResult(query);
        // 处理区域级别荣誉
        if (EnumHonorLevel.AREA.getCode().equals(query.getLevel())) {
            // 如果当前评选对象在结果列表中，直接使用结果列表的数据
            List<HonorsCalculateResults> filterList = honorsCalculateResultslist.stream()
                    .filter(item -> item.getAreaCode().equals(query.getCodes().get(0))).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(filterList)){
                return filterList.get(0);
            }
            // 如果当前评选对象在结果列表中，直接使用结果列表的数据
            singleHonorRank = calculateHonorResultService.getSingleHonorRank(query);
            if(singleHonorRank == null){
                singleHonorRank = new HonorsCalculateResults();
                BeanUtils.copyProperties(query, singleHonorRank);
                singleHonorRank.setAreaCode(query.getCodes().get(0));
                singleHonorRank.setAreaName(query.getNames().get(0));
            }
            // 如果评选类型是人工, 或者人工导入结果，则不设置排名数字
            if(isImportResult || EnumHonorSelectionType.PEOPLE.getCode().equals(query.getSelectionType())){
                singleHonorRank.setRankNum(null);
            }
        }

        // 处理分支级别荣誉
        if (EnumHonorLevel.BCH.getCode().equals(query.getLevel())) {
            // 如果当前评选对象在结果列表中，直接使用结果列表的数据
            List<HonorsCalculateResults> filterList = honorsCalculateResultslist.stream()
                    .filter(item -> item.getBchCode().equals(query.getCodes().get(0))).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(filterList)){
                return filterList.get(0);
            }
            // 如果当前评选对象在结果列表中，直接使用结果列表的数据
            singleHonorRank = calculateHonorResultService.getSingleHonorRank(query);
            if(singleHonorRank == null){
                singleHonorRank = new HonorsCalculateResults();
                BeanUtils.copyProperties(query, singleHonorRank);
                singleHonorRank.setBchCode(query.getCodes().get(0));
                singleHonorRank.setBchName(query.getNames().get(0));
            }
            // 如果评选类型是人工, 或者人工导入结果，则不设置排名数字
            if(isImportResult || EnumHonorSelectionType.PEOPLE.getCode().equals(query.getSelectionType())){
                singleHonorRank.setRankNum(null);
            }
        }

        // 处理员工级别荣誉
        if (EnumHonorLevel.EMP.getCode().equals(query.getLevel())) {
            // 如果当前评选对象在结果列表中，直接使用结果列表的数据
            List<HonorsCalculateResults> filterList = honorsCalculateResultslist.stream()
                    .filter(item -> item.getEmpCode().equals(query.getCodes().get(0))).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(filterList)){
                return filterList.get(0);
            }
            // 如果当前评选对象在结果列表中，直接使用结果列表的数据
            singleHonorRank = calculateHonorResultService.getSingleHonorRank(query);
            if(singleHonorRank == null){
                singleHonorRank = new HonorsCalculateResults();
                BeanUtils.copyProperties(query, singleHonorRank);
                singleHonorRank.setEmpCode(query.getCodes().get(0));
                singleHonorRank.setEmpName(query.getNames().get(0));
            }
            // 如果评选类型是人工, 或者人工导入结果，则不设置排名数字
            if(isImportResult || EnumHonorSelectionType.PEOPLE.getCode().equals(query.getSelectionType())){
                singleHonorRank.setRankNum(null);
            }
            calculateHonorResultService.fillAvatars(query,Collections.singletonList(singleHonorRank));
        }

        return singleHonorRank;
    }

    private Boolean checkImportResult(HonorCalculateRule query) {
        HonorListImportPo honorListImportPo = new HonorListImportPo();
        honorListImportPo.setHonorsConfigurationsId(query.getId());
        int count = honorListImportMapper.selectCount(honorListImportPo);
        return count>0;
    }

    public String getUuid(String level,String regionCode, String bchCode, String empCode) {
        if (EnumHonorLevel.AREA.getCode().equals(level)) {
            return regionCode;
        }
        if (EnumHonorLevel.BCH.getCode().equals(level)) {
            return bchCode;
        }
        if (EnumHonorLevel.EMP.getCode().equals(level)) {
            return empCode;
        }
        return "";
    }
}
