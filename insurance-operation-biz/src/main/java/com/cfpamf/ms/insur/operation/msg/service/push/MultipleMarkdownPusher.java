package com.cfpamf.ms.insur.operation.msg.service.push;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cfpamf.common.ms.exception.MSBizNormalException;
import com.cfpamf.ms.insur.operation.base.event.EventBusEngine;
import com.cfpamf.ms.insur.operation.base.exception.BusinessException;
import com.cfpamf.ms.insur.operation.base.response.CommonResult;
import com.cfpamf.ms.insur.operation.dingtalk.event.DingTalkUserIdErrorEvent;
import com.cfpamf.ms.insur.operation.dingtalk.service.DingTalkRobotService;
import com.cfpamf.ms.insur.operation.fegin.bms.enums.OperationErrorEnum;
import com.cfpamf.ms.insur.operation.fegin.image.facade.InsuranceImageFacade;
import com.cfpamf.ms.insur.operation.msg.enums.OpMessageReceiverTypeEnum;
import com.cfpamf.ms.insur.operation.msg.pojo.dto.MarkdownMessageTemplate;
import com.cfpamf.ms.insur.operation.msg.pojo.dto.OpMessageRuleGroovyDTO;
import com.cfpamf.ms.insur.operation.msg.pojo.po.OpMessagePush;
import com.cfpamf.ms.insur.operation.msg.pojo.po.OpMessageRule;
import com.cfpamf.ms.insur.operation.msg.pojo.po.OpMessageRuleReceiver;
import com.cfpamf.ms.insur.operation.msg.service.MsgPushService;
import com.cfpamf.ms.insur.operation.msg.service.push.model.CacheVo;
import com.cfpamf.ms.insur.operation.msg.util.Html2ImageUtils;
import com.google.common.collect.Maps;
import lombok.AccessLevel;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.task.AsyncTaskExecutor;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalTime;
import java.util.*;
import java.util.function.Supplier;

/**
 * <AUTHOR> 2021/8/30 11:00
 */
@Component("multipleMarkdownPusher")
@Slf4j
@FieldDefaults(level = AccessLevel.PRIVATE)
public class MultipleMarkdownPusher extends AbstractPusher {

    @Autowired
    DingTalkRobotService robotService;
    static final String IMAGE_PARAMS_KEY = "imgParam";

    @Autowired
    InsuranceImageFacade imageFacade;

    @Autowired
    EventBusEngine busEngine;

    @Resource(name = "msg-push-Executor")
    AsyncTaskExecutor msgPushExecutor;

    @Override
    public void push(OpMessageRule rule, String contextId, boolean delay, List<OpMessageRuleReceiver> opMessageRuleReceivers) {

        if (CollectionUtils.isEmpty(opMessageRuleReceivers)) {
            throw new BusinessException(OperationErrorEnum.MSG_CONFIG_ERROR.getCode(), "没有配置消息接收者！");
        }
        // fork join pool
        // 当前上下文
        final Map<String, Object> context = Maps.newConcurrentMap();
        String messageTemplate = rule.getMessageTemplate();

        final MarkdownMessageTemplate template = JSONObject.parseObject(messageTemplate, MarkdownMessageTemplate.class);
        opMessageRuleReceivers
                .forEach(receiver -> {
                    OpMessageRuleGroovyDTO ruleDto = new OpMessageRuleGroovyDTO();
                    ruleDto.setContext(context);
                    BeanUtils.copyProperties(rule, ruleDto);
                    ruleDto.setReceiver(receiver);
                    //把钉钉群配置的json参数添加到groovy上下文对象中 如区域列表,片区列表,分支列表
                    fillDataToOpMessageRuleGroovyDTO(ruleDto,receiver);
                    String params = receiver.getParams();
                    JSONObject json = new JSONObject();

                    //规则的参数支持
                    if (StringUtils.isNotBlank(params)) {
                        JSONObject ps = JSONObject.parseObject(params);
                        if (ps.containsKey(IMAGE_PARAMS_KEY)) {
                            json.putAll(ps.getJSONObject(IMAGE_PARAMS_KEY));
                        }
                    }

                    //接收人维度的参数支持 优先级大于规则
                    if (StringUtils.isNotBlank(ruleDto.getParams())) {
                        JSONObject ps = JSONObject.parseObject(ruleDto.getParams());
                        if (ps.containsKey(IMAGE_PARAMS_KEY)) {
                            json.putAll(ps.getJSONObject(IMAGE_PARAMS_KEY));
                        }
                    }


                    Map<String,Map<String, Object>> scriptResultMap = (Map<String,Map<String, Object>>) groovyService.executeForCode(MsgPushService.SCRIPT_TYPE, rule.getRuleCode(), ruleDto);

                    if (MapUtils.isEmpty(scriptResultMap)) {
                        log.warn("脚本返回结果为空[{}]", scriptResultMap);
                        return;
                    }
                    //采用线程池推送消息
                    msgPushExecutor.submit(() -> pushMsg(scriptResultMap,receiver,contextId,rule,template,json));
                });
    }

    @Override
    public void pushAudit(List<OpMessagePush> pushes) {
        throw new IllegalArgumentException("markdown  不支持审核推送");
    }

    private String html2ImageAndUpload(JSONObject pm, String html) {
        pm.put("htmlValue", html);
        //return apiCall(() -> imageFacade.image(pm)).getUrl();
        return apiCall(() -> dynamicsImageService.image(pm)).getUrl();
    }

    private <T> T apiCall(Supplier<CommonResult<T>> api) {
        CommonResult<T> tCommonResult = api.get();
        if (tCommonResult.isSuccess()) {
            return tCommonResult.getData();
        }
        throw new MSBizNormalException(tCommonResult.getErrorCode(), tCommonResult.getMessage());
    }

    private String sendMessage(OpMessageRuleReceiver receiver, String content, String title,OpMessageRule rule, String contextId,CacheVo imageId,String key) {
        //判断是否需要审核
        if (receiver.needAudit()) {
            int auditCount = getAuditCount(receiver, rule);
            if (auditCount > 0) {
                log.info("之前推送数据未审核！{}", auditCount);
                return null;
            }
            //如果发送到群 需要先上传到oss 走的企业引用开发 发送到个人使用的机器人接口 直接传地址
            saveMultipleAuditingMessagePush(receiver, contextId, imageId.getImageId(), imageId.getCacheUrl(), rule.getRuleCode(),key);
            return null;
        }else {
            //保存待发送的数据
            OpMessagePush push = saveMultipleMessagePushTodo(receiver, imageId.getImageId(), contextId, imageId.getCacheUrl(), rule.getRuleCode(), key);
            String messageId = null;

            if (OpMessageReceiverTypeEnum.CHAT.isMe(receiver.getReceiverType())) {
                //如果接收者配置过机器人，则用机器人发送。
                Boolean withRobot = false;
                if (StringUtils.isNotEmpty(receiver.getRobotToken()) && StringUtils.isNotEmpty(receiver.getRobotTokenSecret())) {
                    withRobot = true;
                }
                if (withRobot) {
                    title = Optional.ofNullable(title).orElse("消息推送");
                    JSONObject jsonObject = new JSONObject();
                    JSONObject json = new JSONObject();
                    json.put("title", title);
                    json.put("text", content);
                    messageId = dingTalkService.sendChatMediaMessageWithGroupRobot(receiver.getRobotToken(), receiver.getRobotTokenSecret(), json);
                    updateByMessageId(push, messageId);
                    log.info("推送数据,接受者："+receiver.getReceiverName()+"\n推送时间："+ LocalTime.now().toString());
                    return messageId;
                }
                //目前仅支持发送到群
                messageId = dingTalkService.sendChatMarkdownMessage(receiver.getReceiver(), content, title);
                updateByMessageId(push, messageId);
                log.info("推送数据,接受者："+receiver.getReceiverName()+"\n推送时间："+ LocalTime.now().toString());
                return messageId;
            } else if (OpMessageReceiverTypeEnum.PERSON.isMe(receiver.getReceiverType())) {
                try {
                    messageId = robotService.pushMarkdown2User(Collections.singletonList(receiver.getReceiver()), content, title);
                    updateByMessageId(push, messageId);
                    log.info("推送数据,接受者："+receiver.getReceiverName()+"\n推送时间："+ LocalTime.now().toString());
                    return messageId;
                } catch (BusinessException e) {
                    //如果是钉钉userid或者钉钉服务器有问题 那就推送userid错误事件 但是不终止程序
                    if (Objects.equals(e.getCode(), OperationErrorEnum.DING_TALK_API_ERROR_USERID.getCode())
                            || Objects.equals(e.getCode(), OperationErrorEnum.DING_TALK_API_ERROR.getCode())) {
                        busEngine.publish(new DingTalkUserIdErrorEvent(Collections.singletonList(receiver.getReceiver())));
                        return null;
                    } else {
                        throw e;
                    }
                }

            }
            throw new UnsupportedOperationException("MarkDown仅支持个人和钉钉群");
        }
    }

    private void pushMsg(Map<String,Map<String, Object>> scriptResultMap,OpMessageRuleReceiver receiver, String contextId, OpMessageRule rule,MarkdownMessageTemplate template,JSONObject json){
        Map<String, Object> scriptResult = null;
        Integer interval = rule.getIntervalMins();
        CacheVo imageId = null;
        String bizCode = null;
        for(Map.Entry<String,Map<String, Object>> entry : scriptResultMap.entrySet()){
            log.info("MultipleMarkdownPusher entry= {}", JSON.toJSONString(entry));
            scriptResult = entry.getValue();
            bizCode = entry.getKey();
            if(StringUtils.isBlank(bizCode)){
                log.warn("bizCode 不能为空 ", bizCode);
                continue;
            }
            if (MapUtils.isEmpty(scriptResult)) {
                log.warn("脚本返回结果为空[{}]", scriptResult);
                continue;
            }
            //groovy生成的参数转html
            String imageContent = Html2ImageUtils.processTemplateInfoString(template.getSingleImageTemplate(), scriptResult);
            log.info("imageContent = {}", imageContent);
            //上传图片
            imageId = uploadImg(json,receiver,imageContent,rule.getImageNature());
            log.info("imageId = {}", JSON.toJSONString(imageId));
            String imageUrl = imageId.getCacheUrl();
            //groovy生成的参数转markdown
            Map<String, Object> markdownParam = new HashMap<>(scriptResult.size() + 1);
            //设置markdown中的图片地址
            markdownParam.put("singleImageUrl", imageUrl);
            markdownParam.putAll(scriptResult);
            String infoString = Html2ImageUtils.processTemplateInfoString(template.getContentTemplate(), markdownParam);
            // 生成markdown 内容
            if (StringUtils.isEmpty(infoString)) {
                log.warn("解析 markdown 模板为空[{}]", scriptResult);
                return;
            }
            //真实发送消息处理
            sendMessage(receiver, infoString, rule.getRuleName(), rule,contextId,imageId, bizCode);
            //每次推送间隔一段时间
            if (interval != null && interval != 0){
                try {
                    Thread.sleep(1000L * 60 * interval);//间隔时间 单位分钟
                } catch (InterruptedException e) {
                    log.error(e.getMessage(),e);
                }
            }
        }

    }
}
