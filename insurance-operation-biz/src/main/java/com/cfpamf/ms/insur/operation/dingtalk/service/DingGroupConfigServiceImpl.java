package com.cfpamf.ms.insur.operation.dingtalk.service;

import com.cfpamf.ms.insur.operation.dingtalk.form.DingGroupConfigForm;
import com.cfpamf.ms.insur.operation.msg.dao.OpMessageRuleReceiverMapper;
import com.cfpamf.ms.insur.operation.msg.pojo.po.OpMessageRuleReceiver;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.apache.ibatis.session.RowBounds;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.cfpamf.ms.insur.operation.dingtalk.entity.DingGroupConfig;
import com.cfpamf.ms.insur.operation.dingtalk.dao.DingGroupConfigMapper;
import tk.mybatis.mapper.entity.Example;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * ;(ding_group_config)表服务实现类
 * <AUTHOR> http://www.chiner.pro
 * @date : 2023-9-14
 */
@Service
public class DingGroupConfigServiceImpl implements DingGroupConfigService{
    public static final String DELETE_SUCCESS = "删除成功";
    public static final String DELETE_FAIL = "删除失败";
    public static final String IN_USED = "该群正在使用中！不能删除";
    @Autowired
    private DingGroupConfigMapper dingGroupConfigMapper;

    @Autowired
    private OpMessageRuleReceiverMapper opMessageRuleReceiverMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    public DingGroupConfig queryById(Long id){
        return dingGroupConfigMapper.selectByPrimaryKey(id);
    }

    /**
     * 分页查询
     *
     * @param dingGroupConfig 筛选条件
     * @param pageRequest 分页对象
     * @return 查询结果
     */
//    public PageInfo<DingGroupConfig> paginQuery(DingGroupConfigForm dingGroupConfigForm) {
//        DingGroupConfig queryParam = new DingGroupConfig();
//        BeanUtils.copyProperties(dingGroupConfigForm,queryParam);
//        PageHelper.startPage(dingGroupConfigForm.getPageNo(), dingGroupConfigForm.getPageSize(), true);
//        Example example = new Example(DingGroupConfig.class);
//        example.createCriteria().
//                andLessThanOrEqualTo("createdTime",dingGroupConfigForm.getEndDate()).
//                andGreaterThanOrEqualTo("createdTime",dingGroupConfigForm.getStartDate());
//        if(dingGroupConfigForm.getGroupName()!=null){
//            example.and().andLike("groupName","%"+dingGroupConfigForm.getGroupName()+"%");
//        }
//        List<DingGroupConfig> list = dingGroupConfigMapper.selectByExample(example);
//        PageInfo<DingGroupConfig> pageInfo= new PageInfo<>(list);
//        return pageInfo;
//    }

    public PageInfo<DingGroupConfig> paginQuery(DingGroupConfigForm dingGroupConfigForm) {
        PageHelper.startPage(dingGroupConfigForm.getPageNo(), dingGroupConfigForm.getPageSize(), true);
        List<DingGroupConfig> list = dingGroupConfigMapper.queryAllByPage(dingGroupConfigForm);
        PageInfo<DingGroupConfig> pageInfo= new PageInfo<>(list);
        return pageInfo;
    }
    /**
     * 新增数据
     *
     * @param dingGroupConfig 实例对象
     * @return 实例对象
     */
    public DingGroupConfig insert(DingGroupConfig dingGroupConfig){
        dingGroupConfigMapper.insert(dingGroupConfig);
        return dingGroupConfig;
    }

    /**
     * 更新数据
     *
     * @param dingGroupConfig 实例对象
     * @return 实例对象
     */
    public DingGroupConfig update(DingGroupConfig dingGroupConfig){
        dingGroupConfig.setUpdatedTime(new Date());
        dingGroupConfigMapper.insertOrUpdate(dingGroupConfig);
        return queryById(dingGroupConfig.getId());
    }

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 是否成功
     */
    public String deleteById(Long id) throws Exception {
        DingGroupConfig dingGroupConfig = new DingGroupConfig();
        dingGroupConfig.setId(id);
        dingGroupConfig = this.dingGroupConfigMapper.selectByPrimaryKey(id);
        if(hasUsed(dingGroupConfig)){
            throw new IllegalArgumentException(IN_USED);
        }
        int total = dingGroupConfigMapper.delete(dingGroupConfig);
        if(total <= 0){
            throw new Exception(DELETE_FAIL);
        }
        return DELETE_SUCCESS;
    }

    @Override
    public Map<String, Object> canDeleteById(Long id) {
        DingGroupConfig dingGroupConfig = new DingGroupConfig();
        dingGroupConfig = this.dingGroupConfigMapper.selectByPrimaryKey(id);
        Map<String, Object> map = new HashMap<>();
        if(hasUsed(dingGroupConfig)){
            map.put("resultMsg",IN_USED);
            map.put("canDelete",false);
        }else{
            map.put("canDelete",true);
        }
        return map;
    }

    private boolean hasUsed(DingGroupConfig dingGroupConfig) {
        Example example = new Example(OpMessageRuleReceiver.class);
        example.createCriteria().andEqualTo("receiver",dingGroupConfig.getGroupId())
                        .andEqualTo("receiverType","chat");
        RowBounds rowBounds = new RowBounds(0,1);
        List<OpMessageRuleReceiver>  opMessageRuleReceivers = opMessageRuleReceiverMapper.selectByExampleAndRowBounds(example,rowBounds);
        return opMessageRuleReceivers.size()>0;
    }
}