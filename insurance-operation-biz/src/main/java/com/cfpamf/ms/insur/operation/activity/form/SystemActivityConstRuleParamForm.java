package com.cfpamf.ms.insur.operation.activity.form;

import com.cfpamf.ms.insur.operation.activity.enums.EnumActivityConstStatRange;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;
import tk.mybatis.mapper.annotation.ColumnType;

/**
 * <AUTHOR> 2022/6/22 17:20
 */
@Data
@ApiModel("手动配置规则参数")
@FieldDefaults(level = AccessLevel.PRIVATE)
public class SystemActivityConstRuleParamForm {

    @ColumnType
    @ApiModelProperty(value = "比较符 > < >= <= < between", example = "between",
            allowableValues = ">,<,<=,>=,=,between")
    String compareSymbol;
    EnumActivityConstStatRange statRange;

    @ApiModelProperty(value = "仅比较符号为between时传数组", example = "\"[123,465]\"")
    String value;
}
