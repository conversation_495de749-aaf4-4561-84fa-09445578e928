package com.cfpamf.ms.insur.operation.pco.dto;

import com.cfpamf.ms.insur.operation.pco.util.CellReader;
import com.cfpamf.ms.insur.operation.pco.util.ExcelField;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import javax.validation.constraints.DecimalMax;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description
 * @Version 1.0
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class PcoLevelAdjustExcelDTO implements Serializable {
    String orderNo;

    @ExcelField("序号")
    String no;

    @NotBlank(message = "pco姓名不能为空")
    @ExcelField("pco姓名")
    String userName;

    @NotNull(message = "pco工号不能为空")
    @ExcelField("pco工号")
    String userId;

    @NotBlank(message = "pco区域不能为空")
    @ExcelField("区域")
    String regionName;

    @NotNull(message = "pco分支不能为空")
    @ExcelField("分支")
    String orgName;

    @NotNull(message = "等级不能为空")
    @ExcelField("等级")
    String pcoLevel;

    @NotNull(message = "pco是否胜任不能为空")
    @ExcelField("pco是否胜任")
    String beQualified;

    @DecimalMin(value = "0",message = "线上考试得分格式错误")
    @DecimalMax(value = "100",message = "线上考试得分不能大于100")
    @ExcelField(value="线上考试得分", excelReder = CellReader.DECIMAL)
    BigDecimal onlineScore;

    @DecimalMin(value = "0",message = "线下综合得分格式错误")
    @DecimalMax(value = "100",message = "线下综合得分不能大于100")
    @ExcelField(value="线下综合得分", excelReder = CellReader.DECIMAL)
    BigDecimal offlineScore;

    @NotNull(message = "季度人均业绩不能为空")
    @DecimalMin(value = "0",message = "季度人均业绩格式错误")
    @ExcelField(value="季度人均业绩", excelReder = CellReader.DECIMAL)
    BigDecimal performance;

    @ExcelField(value="理赔审核及时率", excelReder = CellReader.DECIMAL)
    BigDecimal claimTimelinessRatio;

    @ExcelField("错误信息")
    String errorMsg;

}
