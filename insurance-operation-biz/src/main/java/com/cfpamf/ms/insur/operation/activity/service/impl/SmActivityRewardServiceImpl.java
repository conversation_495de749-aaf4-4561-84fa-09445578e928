package com.cfpamf.ms.insur.operation.activity.service.impl;

import com.alibaba.fastjson.JSON;
import com.cfpamf.common.ms.exception.MSBizNormalException;
import com.cfpamf.ms.insur.operation.activity.dao.SmActivityRewardMapper;
import com.cfpamf.ms.insur.operation.activity.dao.SystemActivityProductRuleMapper;
import com.cfpamf.ms.insur.operation.activity.entity.SmActivityReward;
import com.cfpamf.ms.insur.operation.activity.enums.ConflictRule;
import com.cfpamf.ms.insur.operation.activity.enums.EnumActivityTriggerType;
import com.cfpamf.ms.insur.operation.activity.enums.RewardType;
import com.cfpamf.ms.insur.operation.activity.form.TakeBackRewardForm;
import com.cfpamf.ms.insur.operation.activity.form.ValidActivityProductRuleForm;
import com.cfpamf.ms.insur.operation.reward.dto.RewardDTO;
import com.cfpamf.ms.insur.operation.activity.listener.GrantCouponsOrderMessage;
import com.cfpamf.ms.insur.operation.activity.service.SmActivityRewardService;
import com.cfpamf.ms.insur.operation.activity.vo.SystemActivityProductDetailVo;
import com.cfpamf.ms.insur.operation.activity.vo.SystemActivityProductRuleVo;
import com.cfpamf.ms.insur.operation.base.constant.BusinessConstants;
import com.cfpamf.ms.insur.operation.base.constant.GroovyCodeConstants;
import com.cfpamf.ms.insur.operation.base.service.SystemGroovyService;
import com.cfpamf.ms.insur.operation.base.util.BeanUtil;
import com.cfpamf.ms.insur.operation.reward.service.RewardService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/1/6 16:04
 */
@Slf4j
@Service
public class SmActivityRewardServiceImpl implements SmActivityRewardService {


    @Autowired
    SmActivityRewardMapper smActivityRewardMapper;

    @Autowired
    Map<String, RewardService> rewardServiceMap;

    @Autowired
    SystemActivityProductRuleMapper systemActivityProductRuleMapper;

    @Autowired
    SystemGroovyService systemGroovyService;

    @Override
    public void handlerMessageActivity(GrantCouponsOrderMessage couponsOrderMessage, Map<Long, List<SystemActivityProductDetailVo>> systemActivityMap) {
        List<SmActivityReward> smActivityRewardList = calculationReward(systemActivityMap, couponsOrderMessage);
        if (CollectionUtils.isEmpty(smActivityRewardList)) {
            return;
        }
        smActivityRewardMapper.insertList(smActivityRewardList);

        sendReward(smActivityRewardList);
    }

    @Override
    public void sendReward(List<SmActivityReward> smActivityRewardList) {
        // send reward
        Map<RewardType, List<SmActivityReward>> rewardTypeListMap = smActivityRewardList.stream()
                .collect(Collectors.groupingBy(SmActivityReward::getRewardType));
        for (RewardType rewardType : rewardTypeListMap.keySet()) {
            RewardService rewardService = rewardServiceMap.get(rewardType.name());
            if (Objects.isNull(rewardService)) {
                throw new MSBizNormalException("", rewardType.name() + "该奖励方式暂未实现");
            }
            rewardService.awardByListener(rewardTypeListMap.get(rewardType));
        }
    }

    /**
     * 计算MQ消息奖励
     *
     * @param systemActivityMap
     * @param couponsOrderMessage
     * @return
     */
    public List<SmActivityReward> calculationReward(Map<Long, List<SystemActivityProductDetailVo>> systemActivityMap, GrantCouponsOrderMessage couponsOrderMessage) {
        List<SmActivityReward> result = Lists.newArrayList();
        for (Long saId : systemActivityMap.keySet()) {
            List<SystemActivityProductDetailVo> activityProductDetailList = systemActivityMap.get(saId);
            //获取监听的活动产品id集合
            List<Long> activityProductIdList = activityProductDetailList.stream()
                    .filter(systemActivityProductDetailVo -> Objects.equals(systemActivityProductDetailVo.getTriggerType(), EnumActivityTriggerType.LISTENER.name()))
                    .map(SystemActivityProductDetailVo::getId)
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(activityProductIdList)) {
                continue;
            }
            //获取活动产品规则映射
            Map<Long, List<SystemActivityProductRuleVo>> activityProductRuleMap = systemActivityProductRuleMapper.getBySystemActivityProductIdList(activityProductIdList)
                    .stream()
                    .collect(Collectors.groupingBy(SystemActivityProductRuleVo::getSystemActivityProductId));
            // calculate rewards
            List<SmActivityReward> smActivityRewardList = calculateRewards(couponsOrderMessage, activityProductDetailList, activityProductRuleMap);
            log.info("活动id：{}，订单号：{}，客户经理：{}，活动：{}，奖励：{}", saId, couponsOrderMessage.getFhOrderId(), couponsOrderMessage.getRecommendId(), JSON.toJSONString(activityProductDetailList),JSON.toJSONString(smActivityRewardList));
            result.addAll(smActivityRewardList);
        }
        return result;
    }

    /**
     * 计算奖励
     *
     * @param couponsOrderMessage
     * @param activityProductDetailList
     * @param activityProductRuleMap
     * @return
     */
    @Override
    public List<SmActivityReward> calculateRewards(GrantCouponsOrderMessage couponsOrderMessage, List<SystemActivityProductDetailVo> activityProductDetailList, Map<Long, List<SystemActivityProductRuleVo>> activityProductRuleMap) {
        List<SmActivityReward> result = Lists.newArrayList();
        //获取规则奖励
        Map<SystemActivityProductDetailVo, List<RewardDTO>> rewardMap = Maps.newHashMap();
        activityProductDetailList.stream()
                //计算每个规则的奖励
                .forEach(systemActivityProductDetailVo -> {
                    List<SystemActivityProductRuleVo> activityProductRuleList = activityProductRuleMap.get(systemActivityProductDetailVo.getId());
                    List<RewardDTO> rewardDTO = getReward(couponsOrderMessage, activityProductRuleList, systemActivityProductDetailVo);
                    if (CollectionUtils.isNotEmpty(rewardDTO)) {
                        rewardMap.put(systemActivityProductDetailVo, rewardDTO);
                    }
                });
        if (MapUtils.isEmpty(rewardMap)) {
            return result;
        }
        Set<SystemActivityProductDetailVo> systemActivityProductDetailVos = rewardMap.keySet();
        //根据奖励类型 计算奖励
        Map<RewardType, List<SystemActivityProductDetailVo>> conflictRuleListMap = systemActivityProductDetailVos.stream()
                .collect(Collectors.groupingBy(SystemActivityProductDetailVo::getRewardType));
        log.info("奖励类型：{}",JSON.toJSONString(conflictRuleListMap));
        for (RewardType rewardType : conflictRuleListMap.keySet()) {
            List<SystemActivityProductDetailVo> detailVoList = conflictRuleListMap.getOrDefault(rewardType, Lists.newArrayList());
            List<RewardDTO> rewardDTOList = Lists.newArrayList();
            detailVoList.forEach(
                    systemActivityProductDetailVo -> {
                        rewardDTOList.addAll(rewardMap.get(systemActivityProductDetailVo));
                    }
            );
            List<SmActivityReward> smActivityRewardList = calculationQuantity(rewardDTOList, detailVoList)
                    .stream()
                    .map(rewardDTO -> {
                        return new SmActivityReward(rewardDTO, EnumActivityTriggerType.LISTENER, rewardType, detailVoList.stream().findFirst().get().getSaId());
                    })
                    .collect(Collectors.toList());
            result.addAll(smActivityRewardList);
        }
        return result;
    }


    /**
     * 获取通过规则的产品奖励数量
     * 叠加返回所有
     * 最优求最大奖励
     *
     * @param activityProductDetailPassList
     * @return
     */
    private List<RewardDTO> calculationQuantity(List<RewardDTO> rewardDTOList, List<SystemActivityProductDetailVo> activityProductDetailPassList) {
        List<RewardDTO> result = Lists.newArrayList();
        if (CollectionUtils.isEmpty(activityProductDetailPassList)) {
            return result;
        }
        SystemActivityProductDetailVo systemActivityProductDetailVo = activityProductDetailPassList
                .stream()
                .findFirst()
                .get();
        log.info("活动产品信息：{}",JSON.toJSONString(systemActivityProductDetailVo));
        ConflictRule conflictRule = systemActivityProductDetailVo.getConflictRule();
        switch (conflictRule) {
            case OPTIMAL:
                RewardDTO rewardDTO = rewardDTOList.stream().sorted((u1, u2) -> u2.getProportion().compareTo(u1.getProportion())).findFirst().get();
                result.add(rewardDTO);
                break;
            case OVERLAY:
                result = rewardDTOList;
                break;
            default:
                break;
        }
        return result;
    }

    /**
     * 校验产品规则集合
     *
     * @param couponsOrderMessage
     * @param activityProductRuleList
     * @param systemActivityProductDetailVo
     * @return
     */
    private List<RewardDTO> getReward(GrantCouponsOrderMessage couponsOrderMessage, List<SystemActivityProductRuleVo> activityProductRuleList, SystemActivityProductDetailVo systemActivityProductDetailVo) {
        Long productId = systemActivityProductDetailVo.getProductId();
        //如果是所有产品
        if (productId == BusinessConstants.ALL_PRODUCT_ID) {
            return getReward(activityProductRuleList, couponsOrderMessage);
        }
        //产品和计划匹配
        if (productId.intValue() == couponsOrderMessage.getProductId() && systemActivityProductDetailVo.getPlanIdList().contains(couponsOrderMessage.getPlanId())) {
            return getReward(activityProductRuleList, couponsOrderMessage);
        }
        return Lists.newArrayList();
    }


    /**
     * 获取奖励
     *
     * @param activityProductRuleList
     */
    public List<RewardDTO> getReward(List<SystemActivityProductRuleVo> activityProductRuleList, GrantCouponsOrderMessage couponsOrderMessage) {
        List<RewardDTO> rewardDTOList = Lists.newArrayList();
        for (SystemActivityProductRuleVo systemActivityProductRuleVo : activityProductRuleList) {
            ValidActivityProductRuleForm validActivityProductRuleForm = new ValidActivityProductRuleForm(systemActivityProductRuleVo, couponsOrderMessage);
            Object result = systemGroovyService.executeForCodeByRuleMethod(GroovyCodeConstants.OPERATION_ACTIVITY_GROOVY_TYPE, systemActivityProductRuleVo.getRuleCode(), validActivityProductRuleForm);
            log.info("脚本数据：{}",JSON.toJSONString(result));
            List<RewardDTO> resultList = BeanUtil.castObjectToListBean(result, RewardDTO.class);
            if (CollectionUtils.isNotEmpty(resultList)) {
                resultList.forEach(rewardDTO -> rewardDTO.setSystemActivityProductId(systemActivityProductRuleVo.getSystemActivityProductId()));
                rewardDTOList.addAll(resultList);
            }
        }
        log.info("奖励：{}",JSON.toJSONString(rewardDTOList));
        return rewardDTOList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void takeBackReward(TakeBackRewardForm takeBackRewardForm) {
        Long saId = takeBackRewardForm.getSaId();
        String rewardType = takeBackRewardForm.getRewardType();
        //删除活动奖励
        smActivityRewardMapper.batchSoftDelete(saId, rewardType);
        //回收活动奖励
        RewardService rewardService = rewardServiceMap.get(rewardType);
        if (Objects.isNull(rewardService)) {
            throw new MSBizNormalException("", rewardType + "该奖励方式暂未实现回收奖励");
        }
        rewardService.takeBackReward(saId, true);
    }

    @Override
    public void delete(TakeBackRewardForm takeBackRewardForm) {
        Long saId = takeBackRewardForm.getSaId();
        String rewardType = takeBackRewardForm.getRewardType();
        //删除活动奖励
        smActivityRewardMapper.batchDelete(saId, rewardType);
        //回收活动奖励
        RewardService rewardService = rewardServiceMap.get(rewardType);
        if (Objects.isNull(rewardService)) {
            throw new MSBizNormalException("", rewardType + "该奖励方式暂未实现回收奖励");
        }
        rewardService.takeBackReward(saId, false);
    }
}
