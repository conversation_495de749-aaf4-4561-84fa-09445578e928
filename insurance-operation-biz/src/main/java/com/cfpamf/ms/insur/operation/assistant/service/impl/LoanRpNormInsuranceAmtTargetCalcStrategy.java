package com.cfpamf.ms.insur.operation.assistant.service.impl;

import com.cfpamf.ms.insur.operation.assistant.enums.AssistantTargetEnum;
import com.cfpamf.ms.insur.operation.assistant.service.CalcContext;
import com.cfpamf.ms.insur.operation.assistant.service.TargetCalcStrategy;
import com.cfpamf.ms.insur.operation.assistant.vo.EstimateVo;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;

@Component
@Order(2)
public class LoanRpNormInsuranceAmtTargetCalcStrategy implements TargetCalcStrategy {
    @Override
    public EstimateVo calc(CalcContext context) {
        EstimateVo vo = new EstimateVo();
        BigDecimal value = BigDecimal.ZERO;
        BigDecimal suggest = context.getRenewalTodoRateSuggest();
        //月目标 = ∑当月生成非主营的续保/续期待办保单保费 *40% *40%
        value = context.getSmLoanRenewalShortTodoInsuranceAmt().multiply(BigDecimal.valueOf(0.45)).multiply(suggest);
        if (value.compareTo(BigDecimal.ZERO)<=0){
            value = BigDecimal.ZERO;
        }
        context.setSmLoanRpNormInsuranceAmtTarget(value);
        vo.setEstimateType(AssistantTargetEnum.LOAN_RETENTION_CLASSIC_INSURANCE_AMOUNT);
        vo.setValue(value);
        return vo;
    }
}
