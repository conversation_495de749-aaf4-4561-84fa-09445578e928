package com.cfpamf.ms.insur.operation.visitor.dao;

import com.cfpamf.ms.insur.operation.visitor.vo.VisitorSimpleStatisticsVo;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @date 2021/4/29 15:00
 */
//@Mapper
public interface VisitRecordMapper {

    /**
     * 查看某天的访客信息
     *
     * @param pt        天分区
     * @param jobNumber 用户id
     * @return
     */
    VisitorSimpleStatisticsVo getUserShareVisitorCount(@Param("pt") String pt, @Param("jobNumber") String jobNumber);
}
