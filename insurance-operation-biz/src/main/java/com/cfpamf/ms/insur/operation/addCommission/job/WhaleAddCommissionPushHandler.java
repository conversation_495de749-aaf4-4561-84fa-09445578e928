package com.cfpamf.ms.insur.operation.addCommission.job;

import com.alibaba.fastjson.JSONObject;
import com.cfpamf.ms.insur.operation.addCommission.dao.WhaleAddCommissionDetailItemMapper;
import com.cfpamf.ms.insur.operation.addCommission.dao.WhalePreservationSettlementPushLogMapper;
import com.cfpamf.ms.insur.operation.addCommission.dao.WhaleSettlementPushLogMapper;
import com.cfpamf.ms.insur.operation.addCommission.po.WhaleAddCommissionDetailItem;
import com.cfpamf.ms.insur.operation.addCommission.po.WhalePreservationSettlementPushLog;
import com.cfpamf.ms.insur.operation.addCommission.po.WhaleSettlementPushLog;
import com.cfpamf.ms.insur.operation.addCommission.service.AddCommissionSettlementPushService;
import com.cfpamf.ms.insur.operation.settlement.service.WhaleSettlementBaseService;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 活动job
 * 用于统计活动数据
 *
 * <AUTHOR>
 * @date 2021/12/13 10:30
 */
@Slf4j
@Component
public class WhaleAddCommissionPushHandler {

    @Autowired
    private WhaleAddCommissionDetailItemMapper whaleAddCommissionDetailItemMapper;

    @Autowired
    private WhaleSettlementPushLogMapper whaleSettlementPushLogMapper;

    @Autowired
    private AddCommissionSettlementPushService whaleSettlementBaseService;

    @Autowired
    private WhalePreservationSettlementPushLogMapper whalePreservationSettlementPushLogMapper;


    /**
     * 执行佣金推送结算任务
     */
    @XxlJob("add_commission_push_settlement")
    public void execute() {
        //获取本月月初日期
        String batchNo = LocalDate.now().withDayOfMonth(1).toString();

        //查询是否存在本月待结算的佣金数据
        List<WhaleAddCommissionDetailItem> smAddCommissionDetailItemList = whaleAddCommissionDetailItemMapper.getByBatchNo(batchNo);
        log.info("查询待结算的佣金数据, 结果:{}", JSONObject.toJSONString(smAddCommissionDetailItemList));

        if (CollectionUtils.isNotEmpty(smAddCommissionDetailItemList)) {
            //推送业财
            whaleSettlementBaseService.batchSyncNotify(smAddCommissionDetailItemList);
        }
    }


    /**
     * 执行更新同步通知状态任务
     */
    @XxlJob("add_commission_update_syncNotifyStatus")
    public void executeUpdateSyncNotifyStatus() {

        //查询是否存在本月待结算的佣金数据
        List<WhaleSettlementPushLog> pushLogs = whaleSettlementPushLogMapper.getNotSyncSettlementStatus(0);
        log.info("查询待同步状态的佣金数据, 结果:{}", JSONObject.toJSONString(pushLogs));

        if (CollectionUtils.isNotEmpty(pushLogs)) {
            // 收集所有需要查询的状态请求ID
            List<String> requestIds = pushLogs.stream().map(WhaleSettlementPushLog::getRequestId).collect(Collectors.toList());
            //获取业财处理状态
            whaleSettlementBaseService.queryBatchSyncNotifyStatusByRequestId(requestIds);
        }
    }

    /**
     * 加佣保全推送业财自动补偿
     */
    @XxlJob("add_commission_syncPreservation")
    public void executeUpdateSyncPreservation() {

        //查询未推送的加佣保全记录
        List<WhalePreservationSettlementPushLog> pushLogs = whalePreservationSettlementPushLogMapper.getListByPushState(0);
        log.info("查询待同步的加佣保全数据, 结果:{}", JSONObject.toJSONString(pushLogs));

        if (CollectionUtils.isNotEmpty(pushLogs)) {
            //执行保全处理逻辑
            whaleSettlementBaseService.compensateSyncPreservation(pushLogs);
        }
    }
}
