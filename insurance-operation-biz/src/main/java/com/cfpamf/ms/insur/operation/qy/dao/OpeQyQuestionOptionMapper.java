package com.cfpamf.ms.insur.operation.qy.dao;

import com.cfpamf.ms.insur.operation.base.dao.CommonMapper;
import com.cfpamf.ms.insur.operation.qy.entity.OpeQyQuestionOption;
import org.apache.ibatis.annotations.Mapper;
import tk.mybatis.mapper.entity.Example;

import java.util.List;

/**
 * 企业微信问题目选项 Mapper
 * Created by zhengjing  on 2022-08-05 15:21:46
 *
 * <AUTHOR>
 */
@Mapper
public interface OpeQyQuestionOptionMapper extends CommonMapper<OpeQyQuestionOption> {

    /**
     * 根据问卷id查询
     *
     * @param pagerId
     * @return
     */
    default List<OpeQyQuestionOption> selectByPagerId(Long pagerId) {
        Example example = new Example(OpeQyQuestionOption.class);
        example.createCriteria().andEqualTo("pagerId", pagerId);
        example.orderBy("id");
        return selectByExample(example);
    }
}
