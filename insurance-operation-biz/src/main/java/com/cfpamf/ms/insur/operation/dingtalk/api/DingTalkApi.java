package com.cfpamf.ms.insur.operation.dingtalk.api;

import com.cfpamf.ms.insur.operation.dingtalk.api.model.DingTalkResponse;
import com.cfpamf.ms.insur.operation.dingtalk.api.model.UserGetByMobileRequest;
import com.cfpamf.ms.insur.operation.dingtalk.api.model.UserGetByMobileResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 钉钉sdk 没有的部分api
 *
 * <AUTHOR> 2022/9/29 16:14
 */
@FeignClient(name = "dingTalkApi", url = "https://oapi.dingtalk.com", path = "/topapi/v2")
public interface DingTalkApi {

    /**
     * 通过手机号查询在职员工userid
     *
     * @param token
     * @return
     */
    @PostMapping("/user/getbymobile")
    DingTalkResponse<UserGetByMobileResponse> getUser(@RequestParam("access_token") String token, @RequestBody UserGetByMobileRequest request);


}
