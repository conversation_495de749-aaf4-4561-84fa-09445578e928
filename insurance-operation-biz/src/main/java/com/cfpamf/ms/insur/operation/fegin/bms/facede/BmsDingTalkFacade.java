package com.cfpamf.ms.insur.operation.fegin.bms.facede;


import com.cfpamf.common.ms.result.Result;
import com.cfpamf.ms.bms.facade.vo.DingTalkUserBmsVO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/9/19 14:06
 */
@FeignClient(name = "bms-service-biz", url = "${bms.api.url}", contextId = "bmsDingTalkFacade")
public interface BmsDingTalkFacade {

    @ApiOperation(value = "根据手机号码查询钉钉用户信息")
    @GetMapping({"/dingtalk/users/details/mobiles"})
    Result<List<DingTalkUserBmsVO>> listDingtalkUserDetailByMobiles(@RequestParam("mobiles") String mobiles);

}
