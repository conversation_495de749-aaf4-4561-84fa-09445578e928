package com.cfpamf.ms.insur.operation.aicall.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class AiCallTask {

    /** 任务名称 */
    @ApiModelProperty(name = "任务名称",notes = "")
    private String taskName ;

    /** 外部任务类型 */
    @ApiModelProperty(name = "外部任务类型",notes = "")
    private String taskType ;

    @ApiModelProperty(name = "拨号任务开始日期yyyy-MM-dd",notes = "")
    private String dialStartDate ;
    /** 拨号任务结束日期 */
    @ApiModelProperty(name = "拨号任务结束日期yyyy-MM-dd",notes = "")
    private String dialEndDate ;
    /** 每天开始拨打时间 */
    @ApiModelProperty(name = "每天开始拨打时间",notes = "")
    private String dialStartTime;
    /** 每天结束拨打时间 */
    @ApiModelProperty(name = "每天结束拨打时间",notes = "")
    private String dialEndTime;;
    /** 禁止拨打日期99-节假日；1-周一;2-周二 */
    @ApiModelProperty(name = "禁止拨打日期99-节假日；1-周一",notes = "2-周二")
    private Integer forbidDialDate;
    /** 禁止拨打时间段 */
    @ApiModelProperty(name = "禁止拨打时间段",notes = "")
    private String forbidDialTime;

    /** 重试次数，整数，且 N ≤ 3，默认为空，表示不重试 */
    @ApiModelProperty(name = "重试次数，整数，且 N ≤ 3，默认为空，表示不重试",notes = "")
    private Integer retryTimes ;
    /** 重试间隔时间，单位min，0≤ N ≤720 */
    @ApiModelProperty(name = "重试间隔时间，单位min，0≤ N ≤720",notes = "")
    private Integer retryInterval ;

    /** 号码明细 */
    private List<AiCallTaskDetail> aiCallTaskDetailList;
}
