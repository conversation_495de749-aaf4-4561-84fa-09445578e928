package com.cfpamf.ms.insur.operation.whale.preservation;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
@ApiModel("被保人信息变更")
public class PreservationInsuredBaseInfo implements Serializable {
    @ApiModelProperty("被保人编号")
    private String insuredCode;

    @ApiModelProperty("被保人类型")
    private Integer insuredType;

    @ApiModelProperty("投被保人关系")
    private String insuredRelation;

    @ApiModelProperty("被保人名字")
    private String insuredName;

    @ApiModelProperty("被保人类型")
    private String insuredIdType;

    @ApiModelProperty("被保人证件")
    private String insuredIdCard;

    @ApiModelProperty("被保人性别")
    private Integer insuredGender;

    @ApiModelProperty("被保人盛瑞")
    @JSONField(format = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date insuredBirthday;

    @ApiModelProperty("保全编码")
    String preservationCode;
}
