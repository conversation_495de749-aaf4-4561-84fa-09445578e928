package com.cfpamf.ms.insur.operation.addCommission.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * 保全操作信息
 */
@Data
public class PreservationOperationDetail {

    @ApiModelProperty("保单号")
    private String policyNo;

    @ApiModelProperty("被保人编码")
    private String insuredCode;

    @ApiModelProperty("被保人姓名")
    private String insuredName;

    @ApiModelProperty("被保人证件号")
    private String insuredIdCard;

    @ApiModelProperty("批单号")
    private String endorsementNo;

    @ApiModelProperty("期数")
    private Integer termNum;

    public PreservationOperationDetail(String policyNo,String endorsementNo,String insuredName, String insuredIdCard, String insuredCode) {
        this.endorsementNo = endorsementNo;
        this.policyNo = policyNo;
        this.insuredCode = insuredCode;
        this.insuredIdCard = insuredIdCard;
        this.insuredName = insuredName;
    }

    public PreservationOperationDetail(String policyNo,String endorsementNo,Integer termNum){
        this.policyNo = policyNo;
        this.endorsementNo = endorsementNo;
        this.termNum = termNum;
    }

    public PreservationOperationDetail() {

    }
}
