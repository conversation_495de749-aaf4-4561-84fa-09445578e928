package com.cfpamf.ms.insur.operation.job;

import com.cfpamf.ms.insur.operation.dingtalk.service.DingTalkSwFormService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.time.LocalDate;

/**
 * 同步钉钉智能填表信息
 * <AUTHOR> 2022/9/6 15:17
 */
@Component
@Slf4j
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class DingTalkFormJobHandler {
    DingTalkSwFormService service;

    /**
     * 钉钉智能填表数据同步
     */
    @XxlJob("ding-talk-form")
    public void execute() {
        String s = XxlJobHelper.getJobParam();
        if (StringUtils.isNotBlank(s)) {
            service.syncAllFormDetail(LocalDate.parse(s));
        } else {
            service.syncAllFormDetail(LocalDate.now().minusDays(1L));
        }

    }

}
