package com.cfpamf.ms.insur.operation.aicall.entity;

import com.cfpamf.ms.insur.operation.base.entity.BaseNoUserEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import tk.mybatis.mapper.annotation.ColumnType;

import javax.persistence.Column;
import javax.persistence.Table;
import java.util.Date;

@ApiModel(value = "",description = "")
@Table(name = "baidu_call_back_record")
@Data
public class BaiduCallBackRecord extends BaseNoUserEntity {

    /** 通话唯一标识 */
    @ApiModelProperty(name = "通话唯一标识",notes = "")
    private String sessionId ;

    @ApiModelProperty(name = "外部关联业务主键",notes = "")
    private Long outSourceId ;

    /** 租户唯一标识 */
    @ApiModelProperty(name = "租户唯一标识",notes = "")
    private Long tenantId ;
    /** 任务标识 */
    @ApiModelProperty(name = "任务标识",notes = "")
    private Long taskId ;

    @ApiModelProperty(name = "百度任务编码",notes = "")
    private String taskBatchCode ;
    /** 任务名称 */
    @ApiModelProperty(name = "任务名称",notes = "")
    private String taskName ;
    /** 机器人ID */
    @ApiModelProperty(name = "机器人ID",notes = "")
    private String robotId ;
    /** 外呼机器人名称 */
    @ApiModelProperty(name = "外呼机器人名称",notes = "")
    private String robotName ;
    /** 号码组唯一标识，导入名单后，返回的名单号码组对应标识 */
    @ApiModelProperty(name = "号码组唯一标识，导入名单后，返回的名单号码组对应标识",notes = "")
    private String memberId ;
    /** 被叫号码 */
    @ApiModelProperty(name = "被叫号码",notes = "")
    private String mobile ;
    /** callTimes */
    @ApiModelProperty(name = "callTimes",notes = "")
    private Integer callTimes ;
    /** callTimes */
    @ApiModelProperty(name = "callTimes",notes = "")
    private String callerNum ;
    /** 接通状态，1-已接通 0-未接通 */
    @ApiModelProperty(name = "接通状态，1-已接通 0-未接通",notes = "")
    private Integer endType ;
    /** 呼叫类型，0-首次呼叫；1-重试；2-预约呼叫；3-实时呼叫 */
    @ApiModelProperty(name = "呼叫类型，0-首次呼叫；1-重试；2-预约呼叫；3-实时呼叫",notes = "")
    private Integer callType ;
    /** 未接通原因，详见 endTypeReason 列表 */
    @ApiModelProperty(name = "未接通原因，详见 endTypeReason 列表",notes = "")
    private String endTypeReason ;
    /** 生成通话录音唯一标识,可通过该标识，获取录音 */
    @ApiModelProperty(name = "生成通话录音唯一标识,可通过该标识，获取录音",notes = "")
    @Column(name = "contact_uuid")
    private String contactUUID ;
    /** 文件id，名单导入任务时生成的文件ID */
    @ApiModelProperty(name = "文件id，名单导入任务时生成的文件ID",notes = "")
    private Long fileId ;
    /** 信息收集内容 */
    @ApiModelProperty(name = "信息收集内容",notes = "")
    private String collectInfo ;
    /** 拨号总时长，单位为秒 */
    @ApiModelProperty(name = "拨号总时长，单位为秒",notes = "")
    private Integer durationTimeLen ;
    /** 振铃时长，单位为秒 */
    @ApiModelProperty(name = "振铃时长，单位为秒",notes = "")
    private Integer ringingTimeLen ;
    /** 对话时长，单位为秒 */
    @ApiModelProperty(name = "对话时长，单位为秒",notes = "")
    private Integer talkingTimeLen ;
    /** 呼叫开始时间-Unix时间戳(单位:毫秒) */
    @ApiModelProperty(name = "呼叫开始时间-Unix时间戳(单位:毫秒)",notes = "")
    private Long startTime ;
    /** 振铃开始时间-Unix时间戳(单位:毫秒) */
    @ApiModelProperty(name = "振铃开始时间-Unix时间戳(单位:毫秒)",notes = "")
    private Long ringStartTime ;
    /** 通话开始时间-Unix时间戳(单位:毫秒) */
    @ApiModelProperty(name = "通话开始时间-Unix时间戳(单位:毫秒)",notes = "")
    private Long talkingStartTime ;
    /** 呼叫结束时间-Unix时间戳(单位:毫秒) */
    @ApiModelProperty(name = "呼叫结束时间-Unix时间戳(单位:毫秒)",notes = "")
    private Long endTime ;
    /** 意向，在外呼机器人-流程节点-信息收集，配置key为「意向」，对应的value值 */
    @ApiModelProperty(name = "意向，在外呼机器人-流程节点-信息收集，配置key为「意向」，对应的value值",notes = "")
    private String intent ;
    /** 动作，HUNGUP：挂机 */
    @ApiModelProperty(name = "动作，HUNGUP：挂机",notes = "")
    private String action ;
    /** 是否机器人主动挂机 */
    @ApiModelProperty(name = "是否机器人主动挂机",notes = "")
    private Boolean isRobotHangup ;
    /** 创建人 */
    @ApiModelProperty(name = "创建人",notes = "")
    private String createdBy ;
    /** 创建时间 */
    @ApiModelProperty(name = "创建时间",notes = "")
    private Date createdTime ;
    /** 更新人 */
    @ApiModelProperty(name = "更新人",notes = "")
    private String updatedBy ;
    /** 更新时间 */
    @ApiModelProperty(name = "更新时间",notes = "")
    private Date updatedTime ;
}
