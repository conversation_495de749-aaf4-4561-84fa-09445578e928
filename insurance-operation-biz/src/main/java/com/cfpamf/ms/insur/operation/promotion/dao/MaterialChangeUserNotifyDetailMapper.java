package com.cfpamf.ms.insur.operation.promotion.dao;

import com.cfpamf.ms.insur.operation.base.dao.CommonMapper;
import com.cfpamf.ms.insur.operation.promotion.entity.MaterialChangeNotifyDetailEntity;
import com.cfpamf.ms.insur.operation.promotion.entity.MaterialChangeUserNotifyDetailEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 同步素材圈信息表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-04-29 19:33:41
 */
@Mapper
public interface MaterialChangeUserNotifyDetailMapper extends CommonMapper<MaterialChangeUserNotifyDetailEntity> {

    @Select("select * from material_change_user_notify_detail " +
            " where send_status = 0 and enabled_flag=0 " +
            " order by id asc " +
            " limit #{limitSize} ")
    List<MaterialChangeUserNotifyDetailEntity> listUnSendByLimitSize(@Param("limitSize") Integer limitSize);

}
