package com.cfpamf.ms.insur.operation.pco.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Table;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * <AUTHOR>
 * 2022-09-08
 */
@Data
@Table(name = "pco_schedule")
public class PcoScheduleInfo {
    /**
     * id
     */
    @ApiModelProperty("主键")
    private Integer id;

    /**
     * 区域名称
     */
    @ApiModelProperty("区域名称")
    private String regionName;

    /**
     * 分支
     */
    @ApiModelProperty("分支")
    private String orgName;

    /**
     * 开始时间
     */
    @ApiModelProperty("开始日期")
    private LocalDateTime startDate;

    /**
     * 结束时间
     */
    @ApiModelProperty("结束日期")
    private LocalDateTime endDate;

    /**
     * 标题
     */
    @ApiModelProperty("标题")
    private String title;
    /**
     * 内容
     */
    @ApiModelProperty("内容")
    private String content;
    /**
     * 是否处理 0 已处理 1 未处理
     */
    @ApiModelProperty("是否处理 0 已处理 1 未处理")
    private Integer beDispose;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    String createBy;

    /**
     * 修改人
     */
    @ApiModelProperty(value = "修改人")
    String updateBy;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;
    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;
}
