package com.cfpamf.ms.insur.operation.base.enums;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.experimental.FieldDefaults;

/**
 * <AUTHOR> 2020/12/9 16:57
 */
@Getter
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public enum EnumKeywordSearchType {
    NAME(1, "名字"),
    ID_CARD(2, "证件号"),
    CELLPHONE(3, "手机号");
    final int code;

    final String desc;
}
