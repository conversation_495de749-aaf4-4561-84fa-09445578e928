package com.cfpamf.ms.insur.operation.prospectus.exception;

import com.cfpamf.common.ms.exception.MSBizNormalException;

/**
 * <AUTHOR>
 * @date 2021/5/20 10:13
 */
public class ProspectusBusinessException extends MSBizNormalException {
    public ProspectusBusinessException(String code, String message) {
        super(code, message);
    }

    public final static ProspectusBusinessException PROSPECTUS_PLAN_OR_PRODUCT_NOT_EXIST = new ProspectusBusinessException("prospectus.plan.or.product.not.exist", "计划书对应的计划或者产品不存在");
    public final static ProspectusBusinessException PROSPECTUS_PLAN_NOT_EXIST_PRODUCT_FACTOR = new ProspectusBusinessException("prospectus.plan.not.exist.product.factor", "计划书对应的计划不存在产品因子");
    public final static ProspectusBusinessException PROSPECTUS_PRODUCT_NOT_EXIST_CONFIG = new ProspectusBusinessException("prospectus.product.not.exist.config", "计划书对应的产品未进行计划书配置");
    public final static ProspectusBusinessException PROSPECTUS_NOT_FUND_PRODUCT_FACTOR = new ProspectusBusinessException("prospectus.not.fund.product.factor", "当前投保方案保司无法承保，请修改保障期间、缴费期间或投保计划后重试");
    public final static ProspectusBusinessException PROSPECTUS_NOT_EXIST = new ProspectusBusinessException("prospectus.not.exist", "计划书不存在");
    public final static ProspectusBusinessException PROSPECTUS_FORM_APPLICANT_PARAM_NULL = new ProspectusBusinessException("prospectus.form.applicant.param.null", "计划书投保人信息不能为空");
}
