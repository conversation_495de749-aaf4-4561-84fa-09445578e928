package com.cfpamf.ms.insur.operation.msg.pojo.query;

import com.cfpamf.ms.insur.operation.base.form.PageForm;
import com.cfpamf.ms.insur.operation.msg.pojo.po.OpMessageRule;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;
import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @Date 2021/9/29 17:42
 * @Version 1.0
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class CustomColumnPageQuery extends PageForm {
    @NotBlank
    @ApiModelProperty("规则id")
    String ruleId;

    OpMessageRule rule;


}
