package com.cfpamf.ms.insur.operation.planbook.validate;

import com.cfpamf.ms.insur.operation.planbook.form.OperationPlanBookFactorForm;
import com.cfpamf.ms.insur.operation.planbook.form.OperationPlanBookMakeInfoForm;

import java.math.BigDecimal;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2021/12/20
 * @Version 1.0
 */
public abstract class AbstractPlanBookBaseFormValidator {

    public static final String VALIDATOR_BEAN_NAME_PREFIX = "prospectus_validator_";

    /**
     * 校验的表单
     */
    public OperationPlanBookMakeInfoForm planBookMakeInfoForm;

    public Map<String, String> factorFieldMap;

    public AbstractPlanBookBaseFormValidator setValidProspectusProductFactorForm(
            OperationPlanBookMakeInfoForm prospectusProductFactorForm) {
        this.planBookMakeInfoForm = prospectusProductFactorForm;
        this.factorFieldMap = prospectusProductFactorForm.getFactorFormList().stream().collect(Collectors.toMap(
                OperationPlanBookFactorForm::getFactorField, OperationPlanBookFactorForm::getFactorValue));
        return this;
    }

    /**
     * 保费
     */
    public BigDecimal insuredPremium;

    public AbstractPlanBookBaseFormValidator setInsuranceAmount(BigDecimal insuredPremium) {
        this.insuredPremium = insuredPremium;
        return this;
    }

    /**
     * 校验
     */
    public void valid() {
        //校验保额
        checkInsuredAmount();
        //校验保费
        checkInsuredPremium();
        //校验投保人年龄
        checkInsuredAge();
        checkApplicantAge();
        //年龄约束
        checkAge();
        //保障期间约束
        checkGuarantee();
        //附加险条件约束
        checkAdditionalRisks();
        //缴费年期约束
        checkCoveredYears();
    }

    protected abstract void checkCoveredYears();

    protected abstract void checkInsuredAge();


    /**
     * 校验保费
     */
    public abstract void checkInsuredPremium();

    /**
     * 校验保额
     * 最低保额
     * 最高保额
     */
    public abstract void checkInsuredAmount();

    /**
     * 校验年龄
     * 投保人年龄和被保人年龄
     */
    public void checkAge() {
//        Integer insuredAge = prospectusProductFactorForm.getInsuredAge();
//        Integer applicantAge = prospectusProductFactorForm.getApplicantAge();
//
//        BigDecimal paymentPeriodQuantity = prospectusProductFactorForm.getPaymentPeriodQuantity();
//        if (applicantAge + paymentPeriodQuantity.intValue() > getInsuredPaymentPeriodLimit()) {
//            throw new BusinessException("", "投保人年龄在缴费期满时不能超过70岁");
//
//        }
//
//        if (insuredAge + paymentPeriodQuantity.intValue() > getApplicantPaymentPeriodLimit()) {
//            throw new BusinessException("", "缴费期间满时被保人不能超过65周岁");
//        }
    }

    /**
     * 校验保额
     * 最低保额
     * 最高保额
     */
    public abstract void checkApplicantAge();

//    /**
//     * 获取被保人缴费期限限制
//     *
//     * @return
//     */
//    public abstract Integer getInsuredPaymentPeriodLimit();
//
//    /**
//     * 获取投保人缴费期限限制
//     *
//     * @return
//     */
//    public abstract Integer getApplicantPaymentPeriodLimit();

    /**
     * 保障期间约束
     */
    public abstract void checkGuarantee();

    /**
     * 附加险约束
     */
    public abstract void checkAdditionalRisks();


    public abstract AbstractPlanBookBaseFormValidator copySelf();

}
