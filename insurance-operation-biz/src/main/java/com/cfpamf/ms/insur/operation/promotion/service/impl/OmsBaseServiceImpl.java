package com.cfpamf.ms.insur.operation.promotion.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.cfpamf.common.ms.exception.MSBizNormalException;
import com.cfpamf.ms.insur.operation.fegin.oms.facede.OmsFacade;
import com.cfpamf.ms.insur.operation.fegin.oms.request.MarketSubmitRequest;
import com.cfpamf.ms.insur.operation.fegin.oms.request.MarketingLaunchRequest;
import com.cfpamf.ms.insur.operation.fegin.oms.response.MarketingForwardGroupByDateDTO;
import com.cfpamf.ms.insur.operation.fegin.oms.response.MarketingForwardListQueryOutputDTO;
import com.cfpamf.ms.insur.operation.fegin.oms.response.MarketingLinkDTO;
import com.cfpamf.ms.insur.operation.fegin.oms.response.OmsBaseResponse;
import com.cfpamf.ms.insur.operation.promotion.dto.MarketingForwardListQueryDTO;
import com.cfpamf.ms.insur.operation.promotion.dto.MarketingLaunchDTO;
import com.cfpamf.ms.insur.operation.promotion.service.OmsBaseService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service("omsBaseService")
public class OmsBaseServiceImpl implements OmsBaseService {

    @Autowired
    private OmsFacade omsFacade;

    @Override
    public OmsBaseResponse<Long> marketingSubmit(MarketSubmitRequest marketSubmitRequest) {
        log.info("marketingSubmit req= {}",JSON.toJSONString(marketSubmitRequest));
        OmsBaseResponse<Long> response = null;
        try{
            response = omsFacade.submitNewForInsure(marketSubmitRequest);
            log.info("marketingSubmit res= {}",JSON.toJSONString(response));
        }catch(Exception e){
            log.warn("同步素材异常 req= {}", JSON.toJSONString(marketSubmitRequest));
            throw new MSBizNormalException("", "同步素材异常");
        }
        if(!response.isSuccess()){
            log.warn("同步素材失败 req= {}", JSON.toJSONString(marketSubmitRequest));
            throw new MSBizNormalException("", "同步素材失败");
        }
        return response;
    }

    @Override
    public OmsBaseResponse<Long> marketingLinkSubmit(MarketingLinkDTO saveDTO) {
        log.info("marketingLinkSubmit req= {}",JSON.toJSONString(saveDTO));
        OmsBaseResponse<Long> response = null;
        try{
            response = omsFacade.saveForInsure(saveDTO);
            log.info("marketingLinkSubmit res= {}",JSON.toJSONString(response));
        }catch(Exception e){
            log.warn("创建跳转链接异常 req= {}", JSON.toJSONString(saveDTO));
            throw new MSBizNormalException("", "创建跳转链接异常");
        }
        if (!response.isSuccess()){
            log.warn("创建跳转链接失败 req= {}", JSON.toJSONString(saveDTO));
            throw new MSBizNormalException("", "创建跳转链接失败");
        }
        return response;
    }

    @Override
    public void launch(MarketingLaunchDTO marketingLaunchDTO) {
        try{
            log.info("marketingSubmit req= {}",JSON.toJSONString(marketingLaunchDTO));
            MarketingLaunchRequest request = new MarketingLaunchRequest();
            BeanUtils.copyProperties(marketingLaunchDTO,request);
            omsFacade.launch(request);
        }catch(Exception e){
            log.warn("素材圈素材上架/下架/作废操作异常 req= {}", JSON.toJSONString(marketingLaunchDTO));
            throw new MSBizNormalException("", "素材圈素材上架/下架/作废操作异常");
        }
    }

    @Override
    public OmsBaseResponse<MarketingForwardListQueryOutputDTO> forwardListForInsure(MarketingForwardListQueryDTO marketingForwardListQueryDTO) {
        log.info("forwardListForInsure req= {}",JSON.toJSONString(marketingForwardListQueryDTO));
        OmsBaseResponse<MarketingForwardListQueryOutputDTO> response = null;
        try{
            response = omsFacade.forwardListForInsure(marketingForwardListQueryDTO);
            log.info("marketingSubmit res= {}",JSON.toJSONString(response));
        }catch(Exception e){
            log.warn("forwardListForInsure response= {}",JSON.toJSON(response),e);
            throw new MSBizNormalException("", "获取分享列表异常");
        }
        return response;
    }
}
