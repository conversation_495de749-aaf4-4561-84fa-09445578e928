package com.cfpamf.ms.insur.operation.activity.dao;

import com.cfpamf.ms.insur.operation.activity.entity.SmActivityReward;
import com.cfpamf.ms.insur.operation.base.dao.CommonMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface SmActivityRewardMapper extends CommonMapper<SmActivityReward> {

    /**
     * 获取活动的所有奖励
     *
     * @param saId
     * @return
     */
    default List<SmActivityReward> getBySaId(Long saId) {
        SmActivityReward query = new SmActivityReward();
        query.setSaId(saId);
        query.setEnabledFlag(0);
        return select(query);
    }

    /**
     * 通过主键批量更新活动订单奖励
     *
     * @param list
     */
    void batchUpdateActivityReward(@Param("list") List<SmActivityReward> list);

    /**
     * 批量插入 唯一键重复时修改奖励数量
     *
     * @param list
     * @return
     */
    int insertListDuplicateUpdateProportion(@Param("list") List<SmActivityReward> list);

    /**
     * 批量软删除
     *
     * @param saId
     * @param rewardType
     */
    void batchSoftDelete(@Param("saId") Long saId, @Param("rewardType") String rewardType);


    /**
     * 批量删除
     *
     * @param saId
     * @param rewardType
     */
    void batchDelete(@Param("saId") Long saId, @Param("rewardType") String rewardType);

    void updateProportionBySaId(@Param("saId") Long saId, @Param("proportion") BigDecimal proportion);
}
