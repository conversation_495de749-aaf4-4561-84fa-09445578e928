package com.cfpamf.ms.insur.operation.pk.dao;

import com.cfpamf.ms.insur.operation.pk.pojo.dto.InsIndicators;
import com.cfpamf.ms.insur.operation.pk.pojo.req.IndicatorsRequest;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.util.List;
/**
 * <AUTHOR>
 * @since 2023/8/8 13:55
 */
@Mapper
public interface PkDao {


    /**
     * 员工纬度 标准保费
     *
     * @return
     */
    List<InsIndicators> selectEmpAmount(IndicatorsRequest query);

    /**
     * 分支纬度 标准保费
     *
     * @return
     */
    List<InsIndicators> selectOrgAmount(IndicatorsRequest query);



    /**
     * 区域纬度 标准保费
     *
     * @return
     */
    InsIndicators selectAreaAmount(@Param("startDate") LocalDate startDate, @Param("endDateNextDay") LocalDate endDateNextDay, @Param("orgPath") String orgPath);

}
