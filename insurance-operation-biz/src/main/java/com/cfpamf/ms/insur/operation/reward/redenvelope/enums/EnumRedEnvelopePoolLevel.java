package com.cfpamf.ms.insur.operation.reward.redenvelope.enums;

import java.math.BigDecimal;

/**
 * 红包池等级
 *
 * <AUTHOR>
 * @date 2022/1/9 16:32
 */
public enum EnumRedEnvelopePoolLevel {

    /**
     * 红包一级key
     */
    LEVEL_1("red_envelope_pool_key_level_1"),

    /**
     * 红包二级key
     */
    LEVEL_2("red_envelope_pool_key_level_2"),

    /**
     * 红包三级key
     */
    LEVEL_3("red_envelope_pool_key_level_3"),

    /**
     * 红包四级key
     */
    LEVEL_4("red_envelope_pool_key_level_4"),
    /**
     * 红包四级key
     */
    LEVEL_5("red_envelope_pool_key_level_5"),
    /**
     * 红包四级key
     */
    LEVEL_6("red_envelope_pool_key_level_6"),
    /**
     * 红包四级key
     */
    LEVEL_7("red_envelope_pool_key_level_7");

    /**
     * 红包一档金额 500
     */
    private static BigDecimal RED_ENVELOPE_POOL_KEY_LEVEL_1_AMOUNT = BigDecimal.valueOf(500);
    /**
     * 红包一档金额 800
     */
    private static BigDecimal RED_ENVELOPE_POOL_KEY_LEVEL_2_AMOUNT = BigDecimal.valueOf(800);
    /**
     * 红包一档金额 1500
     */
    private static BigDecimal RED_ENVELOPE_POOL_KEY_LEVEL_3_AMOUNT = BigDecimal.valueOf(1500);
    /**
     * 红包一档金额 2000
     */
    private static BigDecimal RED_ENVELOPE_POOL_KEY_LEVEL_4_AMOUNT = BigDecimal.valueOf(2000);
    /**
     * 红包一档金额 2500
     */
    private static BigDecimal RED_ENVELOPE_POOL_KEY_LEVEL_5_AMOUNT = BigDecimal.valueOf(2500);
    /**
     * 红包一档金额 3000
     */
    private static BigDecimal RED_ENVELOPE_POOL_KEY_LEVEL_6_AMOUNT = BigDecimal.valueOf(3000);
    /**
     * 红包一档金额 5000
     */
    private static BigDecimal RED_ENVELOPE_POOL_KEY_LEVEL_7_AMOUNT = BigDecimal.valueOf(5000);
    String redEnvelopePoolKey;

    EnumRedEnvelopePoolLevel(String redEnvelopePoolKey) {
        this.redEnvelopePoolKey = redEnvelopePoolKey;
    }

    public String getRedEnvelopePoolKey() {
        return redEnvelopePoolKey;
    }

    /**
     * 获取红包池等级
     *
     * @param amount
     * @return
     */
    public static EnumRedEnvelopePoolLevel getByAmount(BigDecimal amount) {
        if (amount.compareTo(RED_ENVELOPE_POOL_KEY_LEVEL_1_AMOUNT) <= 0) {
            return LEVEL_1;
        } else if (amount.compareTo(RED_ENVELOPE_POOL_KEY_LEVEL_2_AMOUNT) <= 0) {
            return LEVEL_2;
        } else if (amount.compareTo(RED_ENVELOPE_POOL_KEY_LEVEL_3_AMOUNT) <= 0) {
            return LEVEL_3;
        } else if (amount.compareTo(RED_ENVELOPE_POOL_KEY_LEVEL_4_AMOUNT) <= 0) {
            return LEVEL_4;
        }else if (amount.compareTo(RED_ENVELOPE_POOL_KEY_LEVEL_5_AMOUNT) <= 0) {
            return LEVEL_5;
        }else if (amount.compareTo(RED_ENVELOPE_POOL_KEY_LEVEL_6_AMOUNT) <= 0) {
            return LEVEL_6;
        } else {
            return LEVEL_7;
        }
    }

}
