package com.cfpamf.ms.insur.operation.fegin.els.response.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date
 */
@Data
public class EventGiftVO {

    @ApiModelProperty("主键")
    private Integer id;

    @ApiModelProperty(value = "奖项名称", required = true)
    private String name;

    @NotNull(message = "奖品名称不能为空")
    @ApiModelProperty(value = "奖品名称", required = true, example = "特斯拉")
    private String giftName;

    @ApiModelProperty(value = "抽奖轮次", required = true)
    private Integer giftTimes;

    @ApiModelProperty(value = "抽奖轮次")
    private Integer currentTimes;

    @ApiModelProperty(value = "单轮中奖个数", required = true)
    private Integer giftCount;

    @ApiModelProperty(value = "奖品描述")
    private String giftMemo;

    @ApiModelProperty(value = "奖品单价")
    private java.math.BigDecimal giftPrice;

    @ApiModelProperty("奖项开奖状态")
    private Integer status;

    @ApiModelProperty("是否已抽完")
    private Boolean end;

    @ApiModelProperty(value = "排序", required = true)
    private Integer sort;

    @ApiModelProperty(value = "奖品照片")
    private String giftPhoto;

    @ApiModelProperty("中奖是否弹幕 0-不弹 1-弹")
    private Integer barrage;

    @ApiModelProperty("当前用户是否中奖")
    private Boolean currentWin;

}
