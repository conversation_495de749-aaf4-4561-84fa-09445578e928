package com.cfpamf.ms.insur.operation.promotion.service.impl;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.cfpamf.ms.insur.operation.promotion.dao.MaterialChangeNotifyDetailMapper;
import com.cfpamf.ms.insur.operation.promotion.dao.MaterialChangeNotifyMapper;
import com.cfpamf.ms.insur.operation.promotion.dto.MarketingListItemMqDTO;
import com.cfpamf.ms.insur.operation.promotion.dto.MarketingListMqDTO;
import com.cfpamf.ms.insur.operation.promotion.entity.MaterialChangeNotifyDetailEntity;
import com.cfpamf.ms.insur.operation.promotion.entity.MaterialChangeNotifyEntity;
import com.cfpamf.ms.insur.operation.promotion.service.MaterialNotifyService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
public class MaterialNotifyServiceImpl implements MaterialNotifyService {

    @Autowired
    private MaterialChangeNotifyMapper materialChangeNotifyMapper;
    @Autowired
    private MaterialChangeNotifyDetailMapper materialChangeNotifyDetailMapper;

    @Transactional(rollbackFor = Exception.class)
    public void saveNotify(MarketingListMqDTO mqDTO){
        log.info("通知信息：{}",JSON.toJSONString(mqDTO));
        MaterialChangeNotifyEntity notifyEntity = new MaterialChangeNotifyEntity();
        notifyEntity.setContent(JSON.toJSONString(mqDTO));
        int ret  = materialChangeNotifyMapper.insertUseGeneratedKeys(notifyEntity);
        log.info("通知信息入库结果：{},数据：{}",ret,JSON.toJSONString(notifyEntity));
        List<MarketingListItemMqDTO> itemDtoList = mqDTO.getMarketingListItemMqDTOList();

        if(CollectionUtils.isNotEmpty(itemDtoList)){
            List<MaterialChangeNotifyDetailEntity> detailEntities = itemDtoList.stream()
                    .map(c->{
                        MaterialChangeNotifyDetailEntity d = new MaterialChangeNotifyDetailEntity();
                        d.setNotifyId(notifyEntity.getId());
                        d.setMarketingId(c.getId());
                        d.setLaunchStatus(c.getLaunchStatus());
                        d.setBizType(c.getBizType());
                        d.setGenerateStatus(0);
                        d.setCreateTime(DateUtil.date());
                        d.setUpdateTime(d.getCreateTime());
                        d.setEnabledFlag(0);
                        return d;
                    }).collect(Collectors.toList());
            log.info("通知信息待入库明细：{}",JSON.toJSONString(detailEntities));
            materialChangeNotifyDetailMapper.insertList(detailEntities);

        }
    }
}
