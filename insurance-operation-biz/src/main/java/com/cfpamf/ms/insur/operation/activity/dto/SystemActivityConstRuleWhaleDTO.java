package com.cfpamf.ms.insur.operation.activity.dto;

import com.alibaba.fastjson.JSON;
import com.cfpamf.ms.insur.operation.activity.entity.SystemActivityConstRuleWhale;
import com.cfpamf.ms.insur.operation.activity.enums.ConflictRule;
import com.cfpamf.ms.insur.operation.activity.form.SystemActivityConstRuleParamForm;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;
import org.apache.commons.lang3.StringUtils;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR> 2022/6/24 16:19
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class SystemActivityConstRuleWhaleDTO extends SystemActivityConstRuleWhale {

    /**
     * els活动编码
     */
    @ApiModelProperty(value = "els活动编码")
    private Integer elsActivityNumber;

    /**
     * els赠送口令
     */
    @ApiModelProperty(value = "els赠送口令")
    private String elsGrantPassword;

    /**
     * 活动区域
     */
    @ApiModelProperty(value = "活动区域")
    private List<String> regionList;
    /**
     * 活动区域
     */
    @ApiModelProperty(value = "活动区域编码", hidden = true)
    private String regions;

    /**
     * 优惠类型 overlay-叠加 optimal-最优
     */
    @ApiModelProperty(value = "优惠类型 OVERLAY-叠加 OPTIMAL-最优")
    private ConflictRule conflictRule;


    /**
     * 初始化产品区域集合
     */
    public List<String> getRegionList() {
        if (StringUtils.isNotBlank(regions)) {
            regionList = JSON.parseArray(regions, String.class);
            return regionList;
        }
        return Collections.emptyList();
    }

    public List<Integer> getPlanOrRiskList() {
        if (StringUtils.isNotBlank(getRiskIds())) {
            return JSON.parseArray(getRiskIds(), Integer.class);
        }
        return Collections.emptyList();
    }

    public List<Integer> getProductIdList() {
        if (StringUtils.isNotBlank(getSellProductIds())) {
            return JSON.parseArray(getSellProductIds(), Integer.class);
        }
        return Collections.emptyList();
    }

    /**
     * 获取规则条件对象
     *
     * @return 条件对象
     */
    public SystemActivityConstRuleParamForm getConstRuleParam() {
        return JSON.parseObject(getParams(), SystemActivityConstRuleParamForm.class);
    }

    /**
     * 是够
     *
     * @return 是否长险
     */
    public boolean isLongIns() {
        return Objects.equals(0, getProductLongInsurance());
    }
}
