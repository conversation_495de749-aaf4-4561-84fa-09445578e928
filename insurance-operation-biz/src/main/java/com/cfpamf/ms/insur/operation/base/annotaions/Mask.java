package com.cfpamf.ms.insur.operation.base.annotaions;

import java.lang.annotation.*;

/**
 * 需要脱敏数据注解
 *
 * <AUTHOR>
 */

@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.FIELD})
@Documented
public @interface Mask {

    /**
     * 数据类型
     *
     * @return
     */
    DataType dataType() default DataType.NULL;

    String ignore() default "";

    /**
     * 数据类型
     * 添加 修改 删除 查询
     */
    enum DataType {

        /**
         * 空
         */
        NULL,

        /**
         * 手机号
         */
        MOBILE,

        /**
         * 邮箱
         */
        EMAIL,

        /**
         * 身份证
         */
        ID_CARD,

        /**
         * 地址
         */
        ADDRESS,

        /**
         * 车牌号
         */
        PLATE_NUM,
        /**
         * 发动机号
         */
        ENGINE_NUM,
        /**
         * 车架号
         */
        VIN,
        /**
         * 银行卡号
         */
        BANK_NO,
        /**
         * 地区
         */
        AREA,
        /**
         * 房产证号
         */
        HOUSE_NO
    }
}
