package com.cfpamf.ms.insur.operation.assistant.service.impl;

import com.cfpamf.common.ms.exception.MSBizNormalException;
import com.cfpamf.ms.insur.operation.assistant.dao.safes.AssistantCalcConfigParamsMapper;
import com.cfpamf.ms.insur.operation.assistant.dao.safes.BizTargetMapper;
import com.cfpamf.ms.insur.operation.assistant.dao.safespg.AdsInsuranceBchMarketingProgressDfpMapper;
import com.cfpamf.ms.insur.operation.assistant.dao.safespg.DwaSafesPhoenixTodoBchMapper;
import com.cfpamf.ms.insur.operation.assistant.entity.safes.AssistantCalcConfigParams;
import com.cfpamf.ms.insur.operation.assistant.entity.safes.BizTarget;
import com.cfpamf.ms.insur.operation.assistant.entity.safespg.AdsInsuranceBchMarketingProgressDfp;
import com.cfpamf.ms.insur.operation.assistant.entity.safespg.DwaSafesPhoenixTodoBch;
import com.cfpamf.ms.insur.operation.assistant.enums.AssistantTargetEnum;
import com.cfpamf.ms.insur.operation.assistant.service.AssistantTargetEstimateService;
import com.cfpamf.ms.insur.operation.assistant.service.CalcContext;
import com.cfpamf.ms.insur.operation.assistant.service.IncrementCalcStrategy;
import com.cfpamf.ms.insur.operation.assistant.service.TargetCalcStrategy;
import com.cfpamf.ms.insur.operation.assistant.vo.EstimateVo;
import com.cfpamf.ms.insur.operation.base.constant.BaseConstants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.YearMonth;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
@Service
public class AssistantTargetEstimateServiceImpl implements AssistantTargetEstimateService {
    private final List<TargetCalcStrategy> targetStrategies;
    private final List<IncrementCalcStrategy> incrementStrategies;
    @Autowired
    AdsInsuranceBchMarketingProgressDfpMapper adsInsuranceBchMarketingProgressDfpMapper;
    @Autowired
    DwaSafesPhoenixTodoBchMapper dwaSafesPhoenixTodoBchMapper;
    @Autowired
    BizTargetMapper bizTargetMapper;

    @Autowired
    AssistantCalcConfigParamsMapper assistantCalcConfigParamsMapper;

    public AssistantTargetEstimateServiceImpl(@Autowired List<TargetCalcStrategy> targetStrategies,  @Autowired List<IncrementCalcStrategy> incrementStrategies) {
        this.targetStrategies = targetStrategies;
        this.incrementStrategies = incrementStrategies;
    }

    /**
     * 目标预估计算
     * @param context
     * @return
     */
    @Override
    public Map<String, BigDecimal> calcTargetEstimate(CalcContext context) {
        buildTargetContext(context);
        List<EstimateVo> targetsEstimate = targetStrategies.stream().map(strategy -> strategy.calc(context))
                .collect(Collectors.toList());
        saveTargetsEstimate(targetsEstimate,context);
        return targetsEstimate.stream().collect(Collectors.toMap(vo->vo.getEstimateType().getValue(), EstimateVo::getValue));
    }

    private void saveTargetsEstimate(List<EstimateVo> targetsEstimate,CalcContext context) {
        Integer year = LocalDate.now().getYear();
        Integer month = LocalDate.now().getMonthValue();
        targetsEstimate.forEach(target -> {
            BizTarget bizTarget = new BizTarget();
            bizTarget.setTargetName(target.getEstimateType().getValue());
            bizTarget.setEffectiveYear(year);
            bizTarget.setEffectiveMonth(month);
            bizTarget.setTargetOwnerCode(context.getBchCode());
            bizTarget.setTargetOwnerType("BRANCH");
            bizTarget.setEnabledFlag(0);
            BizTarget currentTarget = bizTargetMapper.selectOne(bizTarget);
            if (currentTarget==null){
                bizTarget.setTargetValue(target.getValue().doubleValue());
                bizTarget.setCreateTime(new Date());
                bizTarget.setUpdateTime(new Date());
                bizTarget.setUpdateUserId("system");
                bizTarget.setUpdateUserName("system");
                bizTarget.setCreateUserName("system");
                bizTarget.setCreateUserId("system");
                bizTargetMapper.insert(bizTarget);
            }else {
                currentTarget.setTargetValue(target.getValue().doubleValue());
                currentTarget.setTargetValue(target.getValue().doubleValue());
                currentTarget.setUpdateTime(new Date());
                currentTarget.setUpdateUserId("system");
                currentTarget.setUpdateUserName("system");
                bizTargetMapper.updateByPrimaryKey(currentTarget);
            }
        });
    }

    /**
     * 提升值预估计算
     * @param context
     * @return
     */
    @Override
    public Map<String, BigDecimal> calcIncrementEstimate(CalcContext context) {
        buildSuggestContext(context);
        List<EstimateVo> incrementsEstimate = incrementStrategies.stream().map(strategy -> strategy.calc(context))
                .collect(Collectors.toList());

        return incrementsEstimate.stream().collect(Collectors.toMap(vo->vo.getEstimateType().getValue(), EstimateVo::getValue));
    }

    private void buildSuggestContext(CalcContext context) {
        fillConfigParams(context);
        LocalDate yesterday = LocalDate.now().minusDays(1);
        fillMonthMetrics(context, yesterday);
    }

    /**
     * 构建目标计算上下文
     * 本方法旨在为CalcContext实例填充必要的配置参数和上月指标数据
     * 通过调用两个专门的方法fillConfigParams和fillLastMonthMetrics来完成这一任务
     * 确保在进行计算操作前，上下文拥有足够的信息
     *
     * @param context CalcContext类型的对象，代表了计算的上下文环境
     *                包含了进行计算所需的各种参数和历史数据
     */
    private void buildTargetContext(CalcContext context) {
        fillConfigParams(context);
        LocalDate endofMonth = YearMonth.now().minusMonths(1).atEndOfMonth();
        fillMonthMetrics(context, endofMonth);
    }

    /**
     * 填充上个月的业绩指标到上下文对象中
     * 该方法通过查询数据库中上个月的业务数据，将关键的业绩指标提取出来，并设置到CalcContext对象上，
     * 以便在后续的计算或处理流程中使用这些数据
     *
     * @param context 上下文对象，包含了跨多个步骤或方法的共享数据
     */
    private void fillMonthMetrics(CalcContext context,LocalDate date) {

        // 创建一个传输对象实例，用于设置查询参数
        AdsInsuranceBchMarketingProgressDfp t = new AdsInsuranceBchMarketingProgressDfp();
        // 设置查询的日期
        t.setPt(date.format(BaseConstants.FMT_DATE));
        // 设置查询的分支代码，从上下文对象中获取
        t.setBchCode(context.getBchCode());
        DwaSafesPhoenixTodoBch dwaSafesPhoenixTodoBch = new DwaSafesPhoenixTodoBch();
        dwaSafesPhoenixTodoBch.setBchCode(context.getBchCode());
        dwaSafesPhoenixTodoBch.setPt(date.format(BaseConstants.FMT_DATE));


        // 执行查询，获取上个月的分支业绩数据
        AdsInsuranceBchMarketingProgressDfp lastMonthBchDfp = adsInsuranceBchMarketingProgressDfpMapper.selectOneObject(t);
        // 根据BCH代码查询上月的BCH待办事项
        DwaSafesPhoenixTodoBch lastMonthBchTodo = dwaSafesPhoenixTodoBchMapper.queryByBchCode(dwaSafesPhoenixTodoBch);
        // 检查上月的BCH待办事项和DFP指标是否都存在
        if(lastMonthBchDfp==null || lastMonthBchTodo==null){
            // 如果缺少上月的月末指标，则记录警告信息并抛出业务异常
            log.warn("缺少指定日期指标: {}",date.format(BaseConstants.FMT_DATE));
            throw new MSBizNormalException("ASSISTANT001","缺少指定日期指标: "+date.format(BaseConstants.FMT_DATE));
        }
        Integer year = LocalDate.now().getYear();
        Integer month = LocalDate.now().getMonthValue();
        BizTarget bizTarget = new BizTarget();
        bizTarget.setTargetName(AssistantTargetEnum.CLASSIC_INSURANCE_AMOUNT.getValue());
        bizTarget.setEffectiveYear(year);
        bizTarget.setEffectiveMonth(month);
        bizTarget.setTargetOwnerCode(context.getBchCode());
        bizTarget.setTargetOwnerType("BRANCH");
        bizTarget.setEnabledFlag(0);
        BizTarget currentTarget = bizTargetMapper.selectOne(bizTarget);

        // 将查询到的业绩数据设置到上下文对象中
        context.setSyLoanBalanceTarget(BigDecimal.valueOf(lastMonthBchDfp.getSyLoanBalanceTarget()));
        context.setSmNormInsuranceAmtTarget(currentTarget==null?BigDecimal.ZERO : BigDecimal.valueOf(currentTarget.getTargetValue()));
        context.setSmLoanBalance(BigDecimal.valueOf(lastMonthBchDfp.getSmLoanBalance()));
        context.setNmRepaymentPlanAmt(BigDecimal.valueOf(lastMonthBchDfp.getNmRepaymentPlanAmt()));
        context.setSmUnloanNcNormInsuranceAmt(BigDecimal.valueOf(lastMonthBchDfp.getSmUnloanNcNormInsuranceAmt()));
        context.setSmOfflineLoanInsuranceRate(BigDecimal.valueOf(lastMonthBchDfp.getSmOfflineLoanInsuranceRate()));
        context.setSmUnloanRenewalShortTodoInsuranceAmt(BigDecimal.valueOf(lastMonthBchTodo.getSmUnloanRenewShortTodoInsuranceAmt()));
        context.setSmLoanRenewalShortTodoInsuranceAmt(BigDecimal.valueOf(lastMonthBchTodo.getSmLoanRenewShortTodoInsuranceAmt()));
        context.setLoanRenewShortTodoInsuranceAmt(BigDecimal.valueOf(lastMonthBchTodo.getLoanRenewShortTodoAmt()));
        context.setUnloanRenewShortTodoInsuranceAmt(BigDecimal.valueOf(lastMonthBchTodo.getUnloanRenewShortTodoAmt()));
        context.setSmLoanRpNormInsuranceAmt(BigDecimal.valueOf(lastMonthBchDfp.getSmLoanRpNormInsuranceAmt()));
        context.setSmUnloanRpNormInsuranceAmt(BigDecimal.valueOf(lastMonthBchDfp.getSmUnloanRpNormInsuranceAmt()));
    }

    /**
     * 填充配置参数
     * 该方法根据CalcContext中的信息，初始化查询参数，并通过Mapper查询相关配置
     * 主要目的是从配置中提取特定的业务参数，如贷款金额、线下贷款保险费率建议和续保待办利率
     * 并将这些参数转换为业务所需的格式后，设置回CalcContext中
     *
     * @param context 计算上下文，包含计算所需的所有上下文信息
     */
    private void fillConfigParams(CalcContext context) {
        // 初始化查询参数
        AssistantCalcConfigParams queryParams = new AssistantCalcConfigParams();
        // 设置查询参数，使用上下文中获取的bchCode作为计算对象
        queryParams.setCalcObject(context.getBchCode());
        // 设置计算对象类型为"bch"
        queryParams.setCalcObjectType("bch");
        // 通过Mapper查询相关配置
        List<AssistantCalcConfigParams> configParams = assistantCalcConfigParamsMapper.select(queryParams);
        // 过滤出贷款金额相关的配置
        List<AssistantCalcConfigParams> loanAmount = configParams.stream().filter(item -> AssistantTargetEnum.LOAN_AMOUNT.getValue().equals(item.getCalcParamCode()))
                .collect(Collectors.toList());
        // 过滤出线下贷款保险费率建议相关的配置
        List<AssistantCalcConfigParams> offlineLoanInsuranceRateSuggest = configParams.stream().filter(item -> AssistantTargetEnum.OFFLINE_LOAN_INSURANCE_RATE_SUGGEST.getValue().equals(item.getCalcParamCode()))
                .collect(Collectors.toList());
        // 过滤出续保待办利率相关的配置
        List<AssistantCalcConfigParams>  renewalTodoRate = configParams.stream().filter(item -> AssistantTargetEnum.RENEWAL_TODO_RATE.getValue().equals(item.getCalcParamCode())).collect(Collectors.toList());
        // 如果线下贷款保险费率建议配置存在，则将其值设置到上下文中
        if (offlineLoanInsuranceRateSuggest.size()>0){
            context.setSmOfflineLoanInsuranceRateSuggest(BigDecimal.valueOf(offlineLoanInsuranceRateSuggest.get(0).getCalcParamValue()));
        }
        // 如果贷款金额配置存在，则将其值设置到上下文中
        if (loanAmount.size()>0){
            context.setSmLoanAmountTarget(BigDecimal.valueOf(loanAmount.get(0).getCalcParamValue()));
        }
        // 如果续保待办利率配置存在，则将其值设置到上下文中
        if (renewalTodoRate.size()>0){
            context.setRenewalTodoRateSuggest(BigDecimal.valueOf(renewalTodoRate.get(0).getCalcParamValue()));
        }
    }
}
