package com.cfpamf.ms.insur.operation.customer.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * 断保客户
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class CustomerInterruptionSmsDto {

    @NotNull(message = "断保客户不能为空")
    @ApiModelProperty("断保客户id")
    Long customerId;

    @NotNull(message = "投保人手机号码不能为空")
    @ApiModelProperty("投保人手机号码")
    String applicantPhone;

    @NotNull(message = "投保人姓名不能为空")
    @ApiModelProperty("投保人姓名")
    String applicantPersonName;

    @NotNull(message = "被保人姓名不能为空")
    @ApiModelProperty("被保人姓名")
    String insuredPersonName;

    @NotNull(message = "对应险种不能为空")
    @ApiModelProperty("对应险种")
    String riskName;

    @NotNull(message = "短链地址不能为空")
    @ApiModelProperty("短链地址")
    String url;

}
