package com.cfpamf.ms.insur.operation.activity.service.impl;

import com.cfpamf.ms.insur.operation.activity.dao.SmOrderActivityMapper;
import com.cfpamf.ms.insur.operation.activity.entity.SmOrderActivity;
import com.cfpamf.ms.insur.operation.activity.listener.GrantCouponsOrderMessage;
import com.cfpamf.ms.insur.operation.activity.service.SmOrderActivityService;
import com.cfpamf.ms.insur.operation.base.constant.BusinessConstants;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2021/7/14 11:49
 */
@Service
public class SmOrderActivityServiceImpl implements SmOrderActivityService {
    private SmOrderActivityMapper smOrderActivityMapper;
    public static final String MOSAICS = "-";

    public SmOrderActivityServiceImpl(SmOrderActivityMapper smOrderActivityMapper) {
        this.smOrderActivityMapper = smOrderActivityMapper;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveOrderActivity(Map<Long, Integer> activityRewardMap, GrantCouponsOrderMessage couponsOrderMessage) {
        Set<Long> saIdList = activityRewardMap.keySet();
        String recommendId = couponsOrderMessage.getRecommendId();
        String fhOrderId = couponsOrderMessage.getFhOrderId();
        //构建订单活动数据
        List<SmOrderActivity> orderActivityList = Lists.newArrayList();
        for (Long saId : saIdList) {
            SmOrderActivity smOrderActivity = new SmOrderActivity();
            smOrderActivity.setFhOrderId(fhOrderId);
            smOrderActivity.setSaId(saId);
            smOrderActivity.setUserId(recommendId);
            smOrderActivity.setRemark(BusinessConstants.REMARK + fhOrderId);
            smOrderActivity.setTrxNo(getTrxNo(fhOrderId, saId));
            smOrderActivity.setQuantity(activityRewardMap.get(saId));
            smOrderActivity.setState(BusinessConstants.ORDER_ACTIVITY_STATUS_WAIT);
            smOrderActivity.setCreateBy(recommendId);
            smOrderActivity.setUpdateBy(recommendId);
            orderActivityList.add(smOrderActivity);
        }
        //插入数据
        smOrderActivityMapper.insertList(orderActivityList);
    }

    /**
     * 流水号为  fhOrderId+"-"+saIdList
     *
     * @param fhOrderId
     * @param saId
     * @return
     */
    @Override
    public String getTrxNo(String fhOrderId, Long saId) {
        return fhOrderId + MOSAICS + saId;
    }

    @Override
    public String getTrxNo(String fhOrderId, Long saId, int no) {
        return fhOrderId + MOSAICS + saId + MOSAICS + no;
    }

    @Override
    public void insertList(List<SmOrderActivity> orderActivityList) {
        if (CollectionUtils.isNotEmpty(orderActivityList)) {
            smOrderActivityMapper.insertList(orderActivityList);
        }
    }


    @Override
    public List<SmOrderActivity> getNotSendActivityOrder(Long saId) {
        return smOrderActivityMapper.getNotSend(saId);
    }

    @Override
    public List<SmOrderActivity> getSendActivityOrder(Long saId) {
        return smOrderActivityMapper.getBySaId(saId);
    }
}
