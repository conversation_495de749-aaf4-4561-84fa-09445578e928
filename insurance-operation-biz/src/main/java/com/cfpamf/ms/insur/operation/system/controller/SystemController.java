package com.cfpamf.ms.insur.operation.system.controller;

import com.cfpamf.ms.insur.operation.activity.entity.SystemGroovyRule;
import com.cfpamf.ms.insur.operation.base.annotaions.ResponseDecorated;
import com.cfpamf.ms.insur.operation.base.constant.BaseConstants;
import com.cfpamf.ms.insur.operation.base.service.SystemGroovyService;
import com.cfpamf.ms.insur.operation.base.util.HttpRequestUtil;
import com.cfpamf.ms.insur.operation.system.pojo.query.GroovyRuleQuery;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/7/8 10:31
 */
@Slf4j
@Api(value = "Groovy脚本管理", tags = {"Groovy脚本管理"})
@RequestMapping(value = {BaseConstants.ADMIN_VERSION + "/operation/system/groovy"})
@ResponseDecorated
@RestController
public class SystemController {

    @Autowired
    SystemGroovyService groovyService;

    @ApiOperation("分页查询")
    @GetMapping("")
    public PageInfo<SystemGroovyRule> list(GroovyRuleQuery ruleQuery) {

        return groovyService.list(ruleQuery);
    }

    @ApiOperation("列表查询")
    @GetMapping("/_search")
    public List<SystemGroovyRule> list(SystemGroovyRule ruleQuery) {

        return groovyService.list(ruleQuery);
    }

    @ApiOperation("保存或修改")
    @PostMapping
    public SystemGroovyRule update(@RequestBody SystemGroovyRule rule) {
        return groovyService.saveOrUpdate(rule, HttpRequestUtil.getUserId());
    }

    @ApiOperation("保存或修改通过base64编码")
    @PostMapping("/_updateByBaseEncode")
    public SystemGroovyRule updateByBase64(@RequestBody SystemGroovyRule rule) {
        return groovyService.saveOrUpdateForBase64(rule, HttpRequestUtil.getUserId());
    }
}
