package com.cfpamf.ms.insur.operation.prospectus.form;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * 计划书搜索表单
 *
 * <AUTHOR>
 */
@ApiModel(description = "计划书搜索表单")
@Getter
@Setter
public class ProspectusSearchForm {
    @ApiModelProperty("计划书名称")
    private String name;

    /**
     * 页码
     */
    @ApiModelProperty(value = "页码")
    @Min(value = 1, message = "页码从1开始")
    @NotNull(message = "页码不能为空")
    private int pageNo;

    /**
     * 每页记录数
     */
    @ApiModelProperty(value = "每页记录数")
    @Min(value = 1, message = "每页记录数需大于0")
    @NotNull(message = "每页记录数不能为空")
    private int pageSize;

    @ApiModelProperty(value = "用户id", hidden = true)
    private String userId;

}

