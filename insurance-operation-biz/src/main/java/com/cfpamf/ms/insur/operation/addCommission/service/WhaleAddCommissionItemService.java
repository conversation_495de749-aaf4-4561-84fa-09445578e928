package com.cfpamf.ms.insur.operation.addCommission.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cfpamf.common.ms.exception.MSBizNormalException;
import com.cfpamf.common.ms.result.Result;
import com.cfpamf.common.ms.util.DateUtil;
import com.cfpamf.ms.insur.operation.addCommission.dao.WhaleAddCommissionDetailItemMapper;
import com.cfpamf.ms.insur.operation.addCommission.dto.AddCommissionSettlementPushDto;
import com.cfpamf.ms.insur.operation.addCommission.dto.WxCmsSmyVo;
import com.cfpamf.ms.insur.operation.addCommission.dto.WxUserAddCommissionListVo;
import com.cfpamf.ms.insur.operation.addCommission.query.WhaleAddCommissionPushQuery;
import com.cfpamf.ms.insur.operation.addCommission.query.WxAddCommissionQuery;
import com.cfpamf.ms.insur.operation.base.bean.SmyPageInfo;
import com.cfpamf.ms.insur.operation.base.constant.BusinessConstants;
import com.cfpamf.ms.insur.operation.fegin.bms.facede.BmsFacade;
import com.cfpamf.ms.insur.operation.fegin.bms.response.vo.UserDetailVO;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class WhaleAddCommissionItemService {

    WhaleAddCommissionDetailItemMapper whaleAddCommissionDetailItemMapper;

    private BmsFacade bmsFacade;

    public List<AddCommissionSettlementPushDto> getSettlementDataByPage(WhaleAddCommissionPushQuery query) {
        log.info("查询加佣结算数据, 查询参数:{}", JSONObject.toJSONString(query));
        List<AddCommissionSettlementPushDto> list = whaleAddCommissionDetailItemMapper.getByBatchNoAndSaId(query);
        log.info("查询加佣结算数据, 结果:{}", JSONObject.toJSONString(list));
        return list;
    }

    public SmyPageInfo<WxUserAddCommissionListVo, WxCmsSmyVo> pageWxUserAddCommissionListAndSummary(WxAddCommissionQuery query) {
        validWxParam(query);
        if (StringUtils.isEmpty(query.getUserId())) {
            return new SmyPageInfo<>();
        }
        if (query.getPage() > 0) {
            PageHelper.startPage(query.getPage(), query.getSize());
        }
        PageInfo<WxUserAddCommissionListVo> pageInfo = new PageInfo<>(whaleAddCommissionDetailItemMapper.pageAddCommissionByUser(query));
        WxCmsSmyVo smyVo =  getWxUserCostSummary(query);
        SmyPageInfo<WxUserAddCommissionListVo, WxCmsSmyVo> smyPageInfo = new SmyPageInfo<>(pageInfo.getList(),smyVo);
        smyPageInfo.setPageNum(pageInfo.getPageNum());
        smyPageInfo.setSize(pageInfo.getSize());
        smyPageInfo.setTotal(pageInfo.getTotal());
        smyPageInfo.setHasNextPage(pageInfo.isHasNextPage());
        log.info("/user/costListAndSummary 返回数据：{}", JSON.toJSONString(smyPageInfo));
        return smyPageInfo;
    }

    public WxCmsSmyVo getWxUserCostSummary(WxAddCommissionQuery query){
        //验证参数
        validWxParam(query);

        WxCmsSmyVo summary = whaleAddCommissionDetailItemMapper.sumAddCommissionByUserSmy(query);
        if(Objects.isNull(summary)){
            return new WxCmsSmyVo();
        }

        return summary;
    }

    /**
     * 验证并处理参数
     * @param query
     */
    private void validWxParam(WxAddCommissionQuery query){
        if(Objects.isNull(query.getUserId())){
            Result<UserDetailVO> result = bmsFacade.getContextUserDetailForSafes(query.getAuthorization());
            if (!result.isSuccess()) {
                throw new MSBizNormalException(result.getErrorCode(), BusinessConstants.ERROR_PREFIX + result.getErrorMsg());
            }
            UserDetailVO userDetail = result.getData();
            log.info("我的推广费-加佣(分页列表+汇总), 当前登录人信息:{}", JSON.toJSONString(userDetail));
            query.setUserId(userDetail.getJobNumber());
        }
        if(StringUtils.isEmpty(query.getStartTime())){
            query.setStartTime(query.getStartTime() + " 00:00:00");
        }
        if(StringUtils.isEmpty(query.getEndTime())){
            query.setEndTime(query.getEndTime() + " 23:59:59");
        }

        //处理 投保人查询条件
        if (StringUtils.isNotBlank(query.getApplicantName())) {
            //手机号码
            if (isPhone(query.getApplicantName())) {
                query.setApplicantMobile(query.getApplicantName());
                query.setApplicantName(null);
            }else if (Pattern.matches("[\\w\\-]+", query.getApplicantName())) {
                query.setApplicantIdCard(query.getApplicantName());
                query.setApplicantName(null);
            }
        }
        //处理被保人查询条件
        if (StringUtils.isNotBlank(query.getInsuredName())) {
            //手机号码
            if (isPhone(query.getInsuredName())) {
                query.setInsuredMobile(query.getInsuredName());
                query.setInsuredName(null);
            }else if (Pattern.matches("[\\w\\-]+", query.getInsuredName())) {
                query.setInsuredIdCard(query.getInsuredName());
                query.setInsuredName(null);
            }
        }
        log.info("/user/costListAndSummary 查询条件：{}", JSON.toJSONString(query));
    }

    /**
     * 校验是否为手机格式
     * @param str
     * @return
     */
    private boolean isPhone(String str) {
        return str.length() == 11 && Pattern.matches("[0-9]*", str);
    }
}
