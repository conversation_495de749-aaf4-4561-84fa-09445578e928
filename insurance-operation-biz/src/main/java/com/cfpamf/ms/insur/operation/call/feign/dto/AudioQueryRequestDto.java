package com.cfpamf.ms.insur.operation.call.feign.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

/**
 * 录音查询请求DTO
 *
 * <AUTHOR>
 * @date 2025-07-22
 */
@Data
@ApiModel("录音查询请求")
@FieldDefaults(level = AccessLevel.PRIVATE)
public class AudioQueryRequestDto {

    @ApiModelProperty(value = "呼叫SID", example = "call_sid_123456")
    @JsonProperty("CallSid")
    String callSid;
}
