package com.cfpamf.ms.insur.operation.dingtalk.api;

import com.cfpamf.ms.insur.operation.dingtalk.entity.wecat.WechatResult;
import com.cfpamf.ms.insur.operation.dingtalk.entity.wecat.WechatUserInfoPO;
import com.cfpamf.ms.insur.operation.dingtalk.entity.wecat.WechatUserInfoRequest;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

@FeignClient(
        name = "wecat-service",
        url = "${wecat-service.url}"
)
public interface WecatFacade {
    /**
     * 用户批量查询
     *
     * @param var1
     * @return
     */
    @PostMapping({"/wechat/user/getUserInfoByUserIdList"})
    WechatResult<List<WechatUserInfoPO>> getUserInfoByUserIdList(@RequestBody WechatUserInfoRequest var1);
}