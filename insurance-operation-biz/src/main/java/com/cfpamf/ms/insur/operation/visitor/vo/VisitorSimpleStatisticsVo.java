package com.cfpamf.ms.insur.operation.visitor.vo;

import lombok.Getter;
import lombok.Setter;

import java.time.LocalDate;

/**
 * 访客信息简单统计
 *
 * <AUTHOR>
 * @date 2021/4/30 14:28
 */
@Getter
@Setter
public class VisitorSimpleStatisticsVo {
    /**
     * 当日访客
     */
    private int currentDayVisitorCount;
    /**
     * 当日访问量
     */
    private int currentDayVisitCount;
    /**
     * 截至当日与一年的访问量总访问量
     */
    private int totalVisitCount;

    /**
     * 日期
     */
    private LocalDate deadline;


}
