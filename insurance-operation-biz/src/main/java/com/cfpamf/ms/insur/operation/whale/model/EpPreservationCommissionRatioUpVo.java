package com.cfpamf.ms.insur.operation.whale.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: yang<PERSON>lin
 * @create: 2024/5/13 9:48
 * @description: 保单中心佣金信息比例变更Vo
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class EpPreservationCommissionRatioUpVo {
    /**
     * 保全流水号
     */
    private String preservationCode;

    /**
     * `     * 保单中心保单唯一号
     */
    private String contractCode;

    /**
     * 操作类型
     */
    private String opType;

    /**
     * 操作唯一标识
     */
    private String opId;

    /**
     * 变更前结算比例（%）
     */
    private String startSettlementRatio;

    /**
     * 变更前支出比例（%）
     */
    private String startExpenditureRatio;

    /**
     * 变更后结算比例（%）
     */
    private String endSettlementRatio;

    /**
     * 变更后支出比例（%）
     */
    private String endExpenditureRatio;
}
