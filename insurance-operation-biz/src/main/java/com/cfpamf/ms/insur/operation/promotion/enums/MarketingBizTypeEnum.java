package com.cfpamf.ms.insur.operation.promotion.enums;

import com.cfpamf.common.ms.exception.MSBizNormalException;
import com.cfpamf.ms.insur.operation.base.exception.BusinessException;
import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;

@Getter
public enum MarketingBizTypeEnum {

    INSUR(4,"保险系统操作"),
    CREDIT(1,"信贷系统操作"),
    GENERAL(6,"通用系统操作"),
    LIFE(2,"生服系统操作"),
    AGRICULTURE(3,"农服系统操作"),
    WINE(7,"酒水系统操作"),
    POWER(5,"光伏系统操作");

    private Integer bizCode;
    private String bizName;

    MarketingBizTypeEnum(Integer bizCode, String bizName) {
        this.bizCode = bizCode;
        this.bizName = bizName;
    }

    public static MarketingBizTypeEnum decode(Integer bizCode) {
        return Arrays.stream(MarketingBizTypeEnum.values())
                .filter(x -> Objects.equals(x.getBizCode(), bizCode))
                .findFirst().orElseThrow(() -> new MSBizNormalException("", "业务类型不匹配"));
    }
}
