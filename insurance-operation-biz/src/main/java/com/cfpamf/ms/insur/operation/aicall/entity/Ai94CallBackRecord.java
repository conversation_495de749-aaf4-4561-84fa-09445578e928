package com.cfpamf.ms.insur.operation.aicall.entity;

import com.cfpamf.ms.insur.operation.base.entity.BaseNoUserEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Table;
import java.util.Date;

@ApiModel(value = "AI94任务回调记录信息明细表",description = "")
@Table(name="ai94_call_back_record")
@Data
public class Ai94CallBackRecord extends BaseNoUserEntity {
    /** 外呼任务明细Id */
    @ApiModelProperty(name = "外呼任务明细Id",notes = "")
    private Long taskDetailId ;
    /** 目标任务ID */
    @ApiModelProperty(name = "目标任务ID",notes = "")
    private Long taskId ;
    /** 外呼号码 */
    @ApiModelProperty(name = "外呼号码",notes = "")
    private String number ;
    /** 号码MD5 */
    @ApiModelProperty(name = "号码MD5",notes = "")
    private String numberMd5 ;
    /** 批次号 */
    @ApiModelProperty(name = "批次号",notes = "")
    private String batchId ;
    /** 外呼类型 */
    @ApiModelProperty(name = "外呼类型",notes = "")
    private Integer callType ;
    /** 用户自定义标签 */
    @ApiModelProperty(name = "用户自定义标签",notes = "")
    private String tag ;
    /** 外呼ID */
    @ApiModelProperty(name = "外呼ID",notes = "")
    private String callId ;
    /** 任务ID */
    @ApiModelProperty(name = "任务ID",notes = "")
    private String taskBatchCode ;
    /** 任务名称 */
    @ApiModelProperty(name = "任务名称",notes = "")
    private String taskName ;
    /** AI话术ID */
    @ApiModelProperty(name = "AI话术ID",notes = "")
    private Integer templateId ;
    /** AI话术模板名称 */
    @ApiModelProperty(name = "AI话术模板名称",notes = "")
    private String templateName ;
    /** 外呼状态编码 */
    @ApiModelProperty(name = "外呼状态编码",notes = "")
    private Integer statusCode ;
    /** 外呼状态描述 */
    @ApiModelProperty(name = "外呼状态描述",notes = "")
    private String statusDescription ;
    /** 转人工状态编码 */
    @ApiModelProperty(name = "转人工状态编码",notes = "")
    private Integer transferStatusCode ;
    /** 转人工状态 */
    @ApiModelProperty(name = "转人工状态",notes = "")
    private String transferStatus ;
    /** 分配坐席ID */
    @ApiModelProperty(name = "分配坐席ID",notes = "")
    private Integer agentId ;
    /** 坐席标签 */
    @ApiModelProperty(name = "坐席标签",notes = "")
    private String agentTag ;
    /** 坐席分机 */
    @ApiModelProperty(name = "坐席分机",notes = "")
    private String agentExtension ;
    /** 导入时间 */
    @ApiModelProperty(name = "导入时间",notes = "")
    private String importTime ;
    /** 拨号时间 */
    @ApiModelProperty(name = "拨号时间",notes = "")
    private String callBeginTime ;
    /** 振铃时长 */
    @ApiModelProperty(name = "振铃时长",notes = "")
    private Integer ringTime ;
    /** 通话接通时间 */
    @ApiModelProperty(name = "通话接通时间",notes = "")
    private String answerTime ;
    /** AI通话时长 */
    @ApiModelProperty(name = "AI通话时长",notes = "")
    private String speakingTime ;
    /** AI通话时长(秒) */
    @ApiModelProperty(name = "AI通话时长(秒)",notes = "")
    private Integer speakingDuration ;
    /** 挂断时间 */
    @ApiModelProperty(name = "挂断时间",notes = "")
    private String hangupTime ;
    /** 对话轮次 */
    @ApiModelProperty(name = "对话轮次",notes = "")
    private String speakingTurns ;
    /** 人工通话时长 */
    @ApiModelProperty(name = "人工通话时长",notes = "")
    private String agentSpeakingTime ;
    /** 人工通话时长（秒） */
    @ApiModelProperty(name = "人工通话时长（秒）",notes = "")
    private Integer agentSpeakingDuration ;
    /** 意向标签 */
    @ApiModelProperty(name = "意向标签",notes = "")
    private String intentTag ;
    /** 意向说明 */
    @ApiModelProperty(name = "意向说明",notes = "")
    private String intentDescription ;
    /** 个性标签 */
    @ApiModelProperty(name = "个性标签",notes = "")
    private String individualTag ;
    /** 回复关键词 */
    @ApiModelProperty(name = "回复关键词",notes = "")
    private String keywords ;
    /** 挂机方式 */
    @ApiModelProperty(name = "挂机方式",notes = "")
    private Integer hangupType ;
    /** 挂机短信 */
    @ApiModelProperty(name = "挂机短信",notes = "")
    private String sms ;
    /** 对话录音 */
    @ApiModelProperty(name = "对话录音",notes = "")
    private String chatRecord ;
    /** 加微 */
    @ApiModelProperty(name = "加微",notes = "")
    private Integer addWx ;
    /** 加微进度 */
    @ApiModelProperty(name = "加微进度",notes = "")
    private String addWxStatus ;
    /** 是否接通重呼 */
    @ApiModelProperty(name = "是否接通重呼",notes = "")
    private Integer answerRecall ;
    /** 参数值 */
    @ApiModelProperty(name = "参数值",notes = "")
    private String properties ;
    /** 拦截原因 */
    @ApiModelProperty(name = "拦截原因",notes = "")
    private String interceptReason ;
    /** 企业id */
    @ApiModelProperty(name = "企业id",notes = "")
    private Integer companyId ;
    /** sip编码 */
    @ApiModelProperty(name = "sip编码",notes = "")
    private Integer sipCode ;
    /** 转接人工时间 */
    @ApiModelProperty(name = "转接人工时间",notes = "")
    private String transferTime ;
    /** 坐席组ID */
    @ApiModelProperty(name = "坐席组ID",notes = "")
    private Integer seatsGroupId ;
    /** 坐席组名称 */
    @ApiModelProperty(name = "坐席组名称",notes = "")
    private String seatsGroupName ;
    /** 可用标识 */
    @ApiModelProperty(name = "可用标识",notes = "")
    private Integer enabledFlag ;
    /** 创建人 */
    @ApiModelProperty(name = "创建人",notes = "")
    private String createdBy ;
    /** 创建时间 */
    @ApiModelProperty(name = "创建时间",notes = "")
    private Date createdTime ;
    /** 更新人 */
    @ApiModelProperty(name = "更新人",notes = "")
    private String updatedBy ;
    /** 更新时间 */
    @ApiModelProperty(name = "更新时间",notes = "")
    private Date updatedTime ;
}
