package com.cfpamf.ms.insur.operation.activity.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 加佣活动奖励
 *
 * <AUTHOR>
 * @date 2022/7/7 15:55
 */
@Data
@NoArgsConstructor
public class SmActivityRewardAddCommissionDTO extends SmActivityReward {

    @ApiModelProperty("加佣计算金额 险总保费 计划保费")
    BigDecimal targetAmount;

    @ApiModelProperty("加佣金额")
    BigDecimal addAmount;

}
