package com.cfpamf.ms.insur.operation.activity.enums;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.experimental.FieldDefaults;

/**
 * <AUTHOR> 2022/6/23 10:31
 */
@Getter
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public enum EnumActivityConstRewardType {

    /**
     * 比例
     */
    RATIO,
    /**
     * 金额
     */
    VALUE;
}
