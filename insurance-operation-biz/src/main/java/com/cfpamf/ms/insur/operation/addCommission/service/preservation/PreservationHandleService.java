package com.cfpamf.ms.insur.operation.addCommission.service.preservation;


import com.cfpamf.ms.insur.operation.addCommission.dto.AddCommissionSettlementPushDto;
import com.cfpamf.ms.insur.operation.addCommission.dto.PreservationDto;
import com.cfpamf.ms.insur.operation.addCommission.dto.PreservationOperationDto;
import com.cfpamf.ms.insur.operation.addCommission.dto.PreservationSettlementDto;
import com.cfpamf.ms.insur.operation.whale.model.PreservationDetail;
import com.cfpamf.ms.insur.operation.whale.model.WhaleContract;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface PreservationHandleService {
    void pushSettlement(List<AddCommissionSettlementPushDto> pushDtos);

    PreservationSettlementDto doAddCommissionCorrect(PreservationOperationDto operationDto, List<AddCommissionSettlementPushDto> pushDtos, PreservationDto preservationDto);

    void getOperationDto(PreservationOperationDto operationDto, PreservationDto preservationDto);
}
