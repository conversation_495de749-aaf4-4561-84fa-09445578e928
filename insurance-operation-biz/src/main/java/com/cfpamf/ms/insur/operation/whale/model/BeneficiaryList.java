package com.cfpamf.ms.insur.operation.whale.model;


import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

/**
 * <AUTHOR>
 */
@FieldDefaults(level = AccessLevel.PRIVATE)
@Data
public class BeneficiaryList {

    Integer beneficiaryBenefitOrder;

    String beneficiaryIdCard;

    String beneficiaryIdType;

    String beneficiaryMobile;

    String beneficiaryName;

    String beneficiaryRelation;

    String beneficiarySubject;

    Integer benefitRate;


}
