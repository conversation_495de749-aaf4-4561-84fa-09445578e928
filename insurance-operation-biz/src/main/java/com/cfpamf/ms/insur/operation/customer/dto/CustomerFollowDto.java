package com.cfpamf.ms.insur.operation.customer.dto;

import org.springframework.util.StringUtils;
import com.alibaba.fastjson.JSONObject;
import com.cfpamf.ms.insur.operation.aicall.dto.SessionRecordDto;
import com.cfpamf.ms.insur.operation.base.annotaions.Mask;
import com.cfpamf.ms.insur.operation.customer.enums.EnumCustomerFollowType;
import com.cfpamf.ms.insur.operation.customer.enums.EnumNonRenewalReason;
import com.cfpamf.ms.insur.operation.customer.enums.EnumRenewalIntention;
import com.cfpamf.ms.insur.operation.customer.enums.EnumServiceMode;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * 订单续保跟进记录表
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class CustomerFollowDto {

    List<String> type = Arrays.asList("INTERRUPTION","LOAN");

    @ApiModelProperty("客户主键")
    @NotNull(message = "客户不能为空")
    String customerId;

    @ApiModelProperty("证件号")
    @NotBlank(message = "证件号不能为空")
    @Mask(dataType = Mask.DataType.ID_CARD)
    String idNumber;

    @ApiModelProperty("服务方式")
    String serviceMode;

    String serviceModeName;

    public String getServiceModeName() {
        if (StringUtils.isEmpty(serviceMode)){
            return "";
        }
        return EnumServiceMode.dict(serviceMode);
    }

    @ApiModelProperty("意向")
    String intention;

    @ApiModelProperty("任务类型")
    String taskType;

    @ApiModelProperty("跟进类型")
    String followType;

    @ApiModelProperty("跟进类型名称")
    String followTypeName;

    public String getFollowTypeName() {
        if (StringUtils.isEmpty(followType)) {
            return EnumCustomerFollowType.dict(followType);
        }
        return followTypeName;
    }

    @ApiModelProperty("意向-翻译")
    String intentionName;

    public String getIntentionName() {
        if (EnumServiceMode.LOSE_CONTACT.getCode().equals(serviceMode)){
            return EnumRenewalIntention.LOSE_CONTACT.getDesc();
        }
        if (StringUtils.isEmpty(intention) && type.contains(followType)){
            return EnumRenewalIntention.NO_FOLLOW_UP.getDesc();
        }
        return EnumRenewalIntention.dict(intention);
    }

    @ApiModelProperty("不续原因/未转化原因")
    String reason;

    String reasonName;

    @ApiModelProperty("不续原因/未转化原因详细说明")
    String reasonRemark;

    public String getReasonName() {
        if (StringUtils.isEmpty(reason)){
            return "";
        }
        return EnumNonRenewalReason.dict(reason);
    }

    @ApiModelProperty("备注")
    String remark;

    @ApiModelProperty("下次跟进时间")
    LocalDateTime nextTime;

    @ApiModelProperty("状态")
    Integer enabledFlag;

    @ApiModelProperty("跟进人工号")
    @NotBlank(message = "跟进人工号不能为空")
    String followPeople;
    
    @ApiModelProperty("跟进人姓名")
    String followPeopleName;

    @ApiModelProperty("跟进时间")
    LocalDateTime followTime;

    String recordJson;

    @ApiModelProperty(name="语音对话信息")
    List<SessionRecordDto> sessionRecordDtos;

    public List<SessionRecordDto> getSessionRecordDtos(){
        if (!StringUtils.isEmpty(recordJson)){
            return JSONObject.parseArray(recordJson,SessionRecordDto.class);
        }
        return null;
    }

    @ApiModelProperty(name="语音地址")
    String voiceAddress;

    @ApiModelProperty(name="通话时长")
    Integer talkingTimeLen;

    Integer newest;

    @ApiModelProperty("是否继续跟进 0是 1否")
    Integer followState;
}
