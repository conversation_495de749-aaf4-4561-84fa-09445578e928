package com.cfpamf.ms.insur.operation.activity.service.impl;

import com.cfpamf.ms.insur.operation.activity.dao.SmActivityRewardMapper;
import com.cfpamf.ms.insur.operation.activity.dao.SmCommissionDetailMapper;
import com.cfpamf.ms.insur.operation.activity.dto.ActivityConstRuleParam;
import com.cfpamf.ms.insur.operation.activity.dto.SimpleCommissionDetailOrderDTO;
import com.cfpamf.ms.insur.operation.activity.entity.SmActivityReward;
import com.cfpamf.ms.insur.operation.activity.enums.*;
import com.cfpamf.ms.insur.operation.activity.form.SmActivityCommissionQueryForm;
import com.cfpamf.ms.insur.operation.activity.form.SystemActivityConstRuleForm;
import com.cfpamf.ms.insur.operation.activity.form.SystemActivitySearchForm;
import com.cfpamf.ms.insur.operation.activity.listener.GrantCouponsListener;
import com.cfpamf.ms.insur.operation.activity.service.SmActivityRewardConstService;
import com.cfpamf.ms.insur.operation.activity.service.SystemActivityProgrammeService;
import com.cfpamf.ms.insur.operation.activity.vo.SystemActivityProgrammeVo;
import com.cfpamf.ms.insur.operation.base.service.SystemGroovyService;
import com.cfpamf.ms.insur.operation.base.util.LambdaUtils;
import com.cfpamf.ms.insur.operation.reward.service.impl.AddCommissionRewardConstServiceImpl;
import com.github.pagehelper.PageHelper;
import lombok.AccessLevel;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 手动配置的活动奖励类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@FieldDefaults(level = AccessLevel.PRIVATE)
public class SmActivityRewardConstServiceImpl implements SmActivityRewardConstService {

    @Autowired
    SmActivityRewardMapper smActivityRewardMapper;
    @Autowired
    SystemGroovyService systemGroovyService;
    @Autowired
    @Lazy
    SystemActivityProgrammeService activityProgrammeService;
    @Autowired
    SmCommissionDetailMapper detailMapper;
    @Autowired
    AddCommissionRewardConstServiceImpl rewardService;

    public void handleAllConstActivity() {

        // 因为是T+1的模式所以查的时候默认用昨天的这个时刻去判断活动是否有效
        SystemActivitySearchForm searchForm = new SystemActivitySearchForm();
        searchForm.setActiveState(EnumActivityState.IN_ACTIVITY.name());
        searchForm.setRelativeTime(LocalDateTime.now().minusDays(1));
        searchForm.setConfigType(EnumActivityConfigType.CONST.name());
        searchForm.setActivityPlatform("nb");
        List<SystemActivityProgrammeVo> constActivities = activityProgrammeService.getActivityByStateAndRelativeTime(searchForm);
        if (CollectionUtils.isEmpty(constActivities)) {
            log.info("暂无手动配置的活动");
            return;
        }

        //通过规则选择所有产品
        for (SystemActivityProgrammeVo constActivity : constActivities) {
            handleConstActivity(constActivity);
        }
    }

    /**
     * 根据id计算发放活动奖励
     *
     * @param saId
     */
    public void handleConstActivityBySaId(Long saId) {
        SystemActivityProgrammeVo constActivity = activityProgrammeService.detail(saId);
        if (Objects.isNull(constActivity) || constActivity.getConfigType() != EnumActivityConfigType.CONST) {
            log.error("执行了非手动配置的活动的补偿:{}", saId);
            return;
        }
        handleConstActivity(constActivity);
    }

    public void handleConstActivity(SystemActivityProgrammeVo constActivity) {

        List<SmActivityReward> rewards = calculationRewardBySa(null, constActivity);

        if (!CollectionUtils.isEmpty(rewards)) {
            log.info("开始保存奖励数据:{}", rewards.size());
            smActivityRewardMapper.insertListDuplicateUpdateProportion(rewards);

            // 同一个活动
            Map<String, BigDecimal> rewardMap = rewards.stream()
                    .collect(Collectors.toMap(SmActivityReward::getDataId,
                            SmActivityReward::getProportion, BigDecimal::add));
            rewardService.awardByJob(constActivity, rewardMap);
        }

    }

    /**
     * 单个活动订单遍历模式
     *
     * @param constActivity
     */
    public void handleConstActivityOrders(SystemActivityProgrammeVo constActivity) {
        //单个产品的活动集合
        List<Integer> productIds;
        // 如果某个规则是要所有产品的 那就查所有产品
        if (constActivity.getConstRule().getConstRules().stream().anyMatch(s -> CollectionUtils.isNotEmpty(s.getProductIds()))) {
            productIds = Collections.emptyList();
        } else {
            productIds = constActivity.getConstRule().getConstRules().stream()
                    .map(SystemActivityConstRuleForm::getProductIds)
                    .flatMap(Collection::stream).distinct().collect(Collectors.toList());
        }
        int page = 1;
        // 每次计算5万行订单
        int callSize = 50_000;
        List<SimpleCommissionDetailOrderDTO> orders;
        SmActivityCommissionQueryForm queryForm = buildQueryForm(constActivity, productIds);
        do {
            PageHelper.startPage(page++, callSize, false);

            orders = detailMapper.selectActivityOrder(queryForm);
            //然后一单一单计算发放
            SmActivityRewardConstServiceImpl that = (SmActivityRewardConstServiceImpl) AopContext.currentProxy();
            orders.forEach(order -> that.handlerMessageActivity(order, constActivity));
        } while (CollectionUtils.isNotEmpty(orders));

    }

    private SmActivityCommissionQueryForm buildQueryForm(SystemActivityProgrammeVo constActivity, List<Integer> productIds) {
        SmActivityCommissionQueryForm queryForm =
                new SmActivityCommissionQueryForm();
        queryForm.start(constActivity.getStartTime())
                .end(constActivity.getEndTime())
                .productIds(productIds)
                .start(LocalDateTime.now())
                .saId(constActivity.getId()).regionCodes(constActivity.getRegionList());
        if (constActivity.getRegionList().contains(GrantCouponsListener.REGION_ALL)) {
            queryForm.regionCodes(Collections.emptyList());
        }
        return queryForm;
    }

    /**
     * 根据消息处理订单
     *
     * @param order 订单信息
     */
    @Transactional(rollbackFor = Exception.class)
    public void handlerMessageActivity(SimpleCommissionDetailOrderDTO order, SystemActivityProgrammeVo constActivity) {

        //单个活动的奖励
        List<SmActivityReward> saRewards = calculationRewardBySa(order, constActivity);
        sendReward(saRewards, order, constActivity.getId());
    }

    /**
     * 发送奖励
     */
    public void sendReward(List<SmActivityReward> saRewards, SimpleCommissionDetailOrderDTO order, Long saId) {

        if (CollectionUtils.isEmpty(saRewards)) {
            return;
        }
        //如果这个订单有奖励 则保存
        smActivityRewardMapper.insertList(saRewards);

    }

    /**
     * 单个活动的奖励
     *
     * @param order 订单信息
     */
    private List<SmActivityReward> calculationRewardBySa(SimpleCommissionDetailOrderDTO order,
                                                         SystemActivityProgrammeVo constActivity) {
        List<SystemActivityConstRuleForm> rules = constActivity.getConstRule().getConstRules();
        SystemActivityConstRuleForm systemActivityConstRuleDTO = rules.get(0);
        EnumActivityConstJointType jointType = systemActivityConstRuleDTO.getJointType();
        // * 根据规则类型分组 <出单即送,规则列表>
        Map<EnumActivityConstRuleType, List<SystemActivityConstRuleForm>> ruleTypeMap = LambdaUtils.groupBy(rules, SystemActivityConstRuleForm::getRuleType);

        // 每个规则大组的奖励
        List<List<SmActivityReward>> ruleTypeRewards = ruleTypeMap.entrySet().stream()
                .map(ruleTypeVal -> calculateRewardByRuleType(order, ruleTypeVal.getKey(), ruleTypeVal.getValue(), constActivity)).collect(Collectors.toList());

        // 如果条件是并且 当有一个不满足条件时跳过
        if (jointType == EnumActivityConstJointType.AND
                && ruleTypeRewards.stream().anyMatch(CollectionUtils::isEmpty)) {
            return Collections.emptyList();
        }
        //合并所有奖励 (单个活动-不同规则类型)
        return ruleTypeRewards.stream()
                .filter(CollectionUtils::isNotEmpty)
                .flatMap(Collection::stream)
                .peek(reward -> {
                    reward.setSaId(constActivity.getId());
                    reward.setChannelType(EnumActivityTriggerType.JOB);
                    reward.setRewardType(RewardType.ADD_COMMISSION);
                })
                .collect(Collectors.toList());
    }

    /**
     * 执行某个规则类型的
     *
     * @param couponsOrderMessage 订单信息 目前没有了
     * @param key                 规则类型
     * @param value               类型下所有规则
     * @param constActivity       具体活动
     * @return 满足条件的所有的单子
     */
    private List<SmActivityReward> calculateRewardByRuleType(SimpleCommissionDetailOrderDTO couponsOrderMessage,
                                                             EnumActivityConstRuleType key,
                                                             List<SystemActivityConstRuleForm> value,
                                                             SystemActivityProgrammeVo constActivity) {

        log.info("执行规则类型：{}-{}-{}", key, constActivity.getId(), value.size());
        //获取规则奖励
        ActivityConstRuleParam order = new ActivityConstRuleParam()
                .rules(value)
                .ruleType(key)
                .saId(value.get(0).getSaId())
                .order(couponsOrderMessage)
                .constActivity(constActivity)
                .activityPlatform(constActivity.getActivityPlatform());
        return (List<SmActivityReward>) systemGroovyService.executeForCode(EnumGroovyRuleType.OPERATION_ACTIVI_CONST.getCode(), key.name(), order);

    }

}
