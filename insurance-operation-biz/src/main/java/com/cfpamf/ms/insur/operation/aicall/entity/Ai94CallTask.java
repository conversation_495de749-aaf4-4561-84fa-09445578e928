package com.cfpamf.ms.insur.operation.aicall.entity;

import com.cfpamf.ms.insur.operation.base.entity.BaseNoUserEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Table;
import java.util.Date;

@ApiModel(value = "AI94任务信息表",description = "")
@Table(name="ai94_call_task")
@Data
public class Ai94CallTask extends BaseNoUserEntity {

    /** 任务Id */
    @ApiModelProperty(name = "任务Id",notes = "")
    private Long taskId ;
    /** 任务类型 */
    @ApiModelProperty(name = "任务类型",notes = "")
    private String taskType ;
    /** 任务类型 */
    @ApiModelProperty(name = "ai94任务类型",notes = "")
    private Integer ai94TaskType ;
    /** 任务名称，不超过100个字符 */
    @ApiModelProperty(name = "任务名称，不超过100个字符",notes = "")
    private String name ;
    /** 任务启动日期，格式为[年-月-日]，不传则立即外呼 */
    @ApiModelProperty(name = "任务启动日期，格式为[年-月-日]，不传则立即外呼",notes = "")
    private String startTime ;
    /** 话术模板类型，1：单模版；2：智能话术策略模板，默认为1 */
    @ApiModelProperty(name = "话术模板类型，1：单模版；2：智能话术策略模板，默认为1",notes = "")
    private Integer templateType ;
    /** 话术模板id */
    @ApiModelProperty(name = "话术模板id",notes = "")
    private Long templateId ;
    /** 并发数，默认为1，不可超过企业设置最高并发数 */
    @ApiModelProperty(name = "并发数，默认为1，不可超过企业设置最高并发数",notes = "")
    private Integer maxConcurrency ;
    /** 重呼配置，可选值：1，2，3，默认为1；1：不重呼；2：间隔重呼；3：定时重呼 */
    @ApiModelProperty(name = "重呼配置，可选值：1，2，3，默认为1；1：不重呼；2：间隔重呼；3：定时重呼",notes = "")
    private Integer recallType ;
    /** 外呼时间段，格式为二维列表，如[[8.5,12][13.5,19]]，默认为[[8,20]] */
    @ApiModelProperty(name = "外呼时间段，格式为二维列表，如[[8.5,12][13.5,19]]，默认为[[8,20]]",notes = "")
    private String callTime ;
    /** 重呼间隔，当重呼选项为2时，必填；单位为分钟，支持≥1的正整数 */
    @ApiModelProperty(name = "重呼间隔，当重呼选项为2时，必填；单位为分钟，支持≥1的正整数",notes = "")
    private Integer repeatInterval ;
    /** 重呼次数，当重呼选项为2时，必填；支持≥1的正整数 */
    @ApiModelProperty(name = "重呼次数，当重呼选项为2时，必填；支持≥1的正整数",notes = "")
    private Integer repeatCount ;
    /** 重呼时间，当重呼选项为3时，必填；格式为["8:00","9:00"]，多个时间点用逗号隔开 */
    @ApiModelProperty(name = "重呼时间，当重呼选项为3时，必填；格式为[\"8:00\",\"9:00\"]，多个时间点用逗号隔开",notes = "")
    private String repeatTimes ;
    /** 录音地址，当企业开通业务类型是语音通知时，必填 */
    @ApiModelProperty(name = "录音地址，当企业开通业务类型是语音通知时，必填",notes = "")
    private String recordPath ;
    /** 播放间隔时长，当企业开通业务类型是语音通知时，必填；单位为秒，支持输入≥0的正整数 */
    @ApiModelProperty(name = "播放间隔时长，当企业开通业务类型是语音通知时，必填；单位为秒，支持输入≥0的正整数",notes = "")
    private Integer playSleepVal ;
    /** 录音播放次数，当企业开通业务类型是语音通知时，必填；支持输入≥0的正整数 */
    @ApiModelProperty(name = "录音播放次数，当企业开通业务类型是语音通知时，必填；支持输入≥0的正整数",notes = "")
    private Integer playTimes ;
    /** 短信发送规则，约定挂机短信的发送规则 */
    @ApiModelProperty(name = "短信发送规则，约定挂机短信的发送规则",notes = "")
    private String sendSmsPlan ;
    /** 重呼条件，可多选，不传时默认条件全部 */
    @ApiModelProperty(name = "重呼条件，可多选，不传时默认条件全部",notes = "")
    private String repeatReason ;
    /** 挂断回调url地址 */
    @ApiModelProperty(name = "挂断回调url地址",notes = "")
    private String callbackUrl ;
    /** 任务状态 修改任务状态，2：开启；4：停止；修改任务时使用，一般用来启动外呼任务 */
    @ApiModelProperty(name = "任务状态 修改任务状态，2：开启；4：停止；修改任务时使用，一般用来启动外呼任务",notes = "")
    private Integer status ;
    /** 可用标识 */
    @ApiModelProperty(name = "可用标识",notes = "")
    private Integer enabledFlag ;
    /** 创建人 */
    @ApiModelProperty(name = "创建人",notes = "")
    private String createdBy ;
    /** 创建时间 */
    @ApiModelProperty(name = "创建时间",notes = "")
    private Date createdTime ;
    /** 更新人 */
    @ApiModelProperty(name = "更新人",notes = "")
    private String updatedBy ;
    /** 更新时间 */
    @ApiModelProperty(name = "更新时间",notes = "")
    private Date updatedTime ;

    @ApiModelProperty(name = "外部任务id",notes = "")
    private String taskBatchCode;

}
