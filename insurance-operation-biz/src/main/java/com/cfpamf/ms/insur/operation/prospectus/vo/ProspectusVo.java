package com.cfpamf.ms.insur.operation.prospectus.vo;

import com.cfpamf.ms.insur.operation.prospectus.enums.PaymentPeriodUnitEnum;
import com.cfpamf.ms.insur.operation.prospectus.enums.GenderEnum;
import com.cfpamf.ms.insur.operation.prospectus.enums.AgeUnitEnum;
import com.cfpamf.ms.insur.operation.prospectus.enums.GuaranteeUnitEnum;
import com.cfpamf.ms.insur.operation.prospectus.enums.InsuredAmountUnitEnum;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import com.cfpamf.ms.insur.operation.prospectus.form.ProspectusProductFactorForm;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.BeanUtils;

/**
 * 计划书视图对象
 *
 * <AUTHOR>
 */
@ApiModel(description = "计划书视图对象")
@Getter
@Setter
public class ProspectusVo {
    @ApiModelProperty("标识")
    private Long id;

    @ApiModelProperty("计划书名称")
    private String name;
    /**
     * 产品计划
     */
    @ApiModelProperty("计划")
    private Integer plan;

    @ApiModelProperty("客户经理")
    private String customerManager;

    @ApiModelProperty("产品id")
    private Long productId;

    @ApiModelProperty("计划id")
    private Long planId;

    @ApiModelProperty("被保人姓名")
    private String insuredName;

    @ApiModelProperty("被保人年龄")
    private Integer insuredAge;

    @ApiModelProperty("年龄单位")
    private AgeUnitEnum ageUnit;

    @ApiModelProperty("被保人性别")
    private GenderEnum insuredGender;

    @ApiModelProperty("被保人同投报人 0false  1true")
    private Integer insuredIsApplicant;

    @ApiModelProperty("保障期间单位")
    private GuaranteeUnitEnum guaranteeUnit;

    @ApiModelProperty("保障期间")
    private BigDecimal guaranteeQuantity;

    @ApiModelProperty("缴费周期单位")
    private PaymentPeriodUnitEnum paymentPeriodUnit;

    @ApiModelProperty("缴费周期")
    private BigDecimal paymentPeriodQuantity;

    @ApiModelProperty("保费单位")
    private InsuredAmountUnitEnum insuredAmountUnit;

    @ApiModelProperty("保费")
    private BigDecimal insuredAmountQuantity;

    @ApiModelProperty("客户名称")
    private String customerName;

    @ApiModelProperty("封面文件路径")
    private String coverFilePath;

    @ApiModelProperty("是否展示讲解视频")
    private Integer showVideo;

    @ApiModelProperty("是否展示推荐理由")
    private Integer showRecommendationReason;

    @ApiModelProperty("保费")
    private BigDecimal amount;

    @ApiModelProperty("推荐理由")
    private String recommendationReason;

    @ApiModelProperty("讲解视频")
    private String explainVideoFilePath;


    @ApiModelProperty("投报人姓名")
    private String applicantName;

    @ApiModelProperty("投报人年龄")
    private Integer applicantAge;

    @ApiModelProperty("投报人性别")
    private GenderEnum applicantGender;

    @ApiModelProperty("客户性别")
    private GenderEnum customerGender;

    @ApiModelProperty("客户手机号")
    private String customerManagerMobile;

    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDateTime createTime;

    @ApiModelProperty("产品信息")
    private ProspectusProductVo prospectusProductVo;

    /**
     * 获取计划书计算表单
     *
     * @return
     */
    @JsonIgnore
    public ProspectusProductFactorForm getProspectusCalculationPremiumForm() {
        ProspectusProductFactorForm prospectusProductFactorForm = new ProspectusProductFactorForm();
        BeanUtils.copyProperties(this, prospectusProductFactorForm);
        return prospectusProductFactorForm;
    }
}

