package com.cfpamf.ms.insur.operation.assistant.service.impl;

import com.cfpamf.ms.insur.operation.assistant.enums.AssistantTargetEnum;
import com.cfpamf.ms.insur.operation.assistant.service.CalcContext;
import com.cfpamf.ms.insur.operation.assistant.service.TargetCalcStrategy;
import com.cfpamf.ms.insur.operation.assistant.vo.EstimateVo;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;

@Component
@Order(1)
public class LoanNormInsuranceAmtTargetCalcStrategy implements TargetCalcStrategy {
    @Override
    public EstimateVo calc(CalcContext context) {
        EstimateVo vo = new EstimateVo();
        BigDecimal value = BigDecimal.ZERO;
        BigDecimal suggest = context.getSmOfflineLoanInsuranceRateSuggest();
        BigDecimal stardardTarget = new BigDecimal(85);
        //计算公式 X月放款金额目标 =（今年年底余额目标- (X-1)月底余额）÷ (12-X+1)  + X月还款计划额
        //smLoanAmtTarget  X月放款金额目标
        BigDecimal smLoanAmtTarget = getSmLoanAmtTarget(context);
        if(stardardTarget.subtract(context.getSmOfflineLoanInsuranceRate()).compareTo(suggest)>0){
            //计算公式 85-上月交叉销售比≥N，则M=N
            //计算公式 主营保费目标=X月放款金额目标 *（上月交叉销售比+M）/10000 *45%
            value = smLoanAmtTarget.multiply(context.getSmOfflineLoanInsuranceRate().add(suggest)).divide(BigDecimal.valueOf(10000)).multiply(BigDecimal.valueOf(0.45));
        }
        if(stardardTarget.subtract(context.getSmOfflineLoanInsuranceRate()).compareTo(BigDecimal.valueOf(0))<=0){
            //计算公式 85-上月交叉销售比≥N，则M=N
            //主营保费目标=X月放款金额目标 *（上月交叉销售比+M）/10000 *45%
            value = smLoanAmtTarget.multiply(context.getSmOfflineLoanInsuranceRate()).divide(BigDecimal.valueOf(10000)).multiply(BigDecimal.valueOf(0.45));
        }
        if(stardardTarget.subtract(context.getSmOfflineLoanInsuranceRate()).compareTo(BigDecimal.valueOf(0))>0
        && stardardTarget.subtract(context.getSmOfflineLoanInsuranceRate()).compareTo(suggest)<=0){
            //计算公式 0≤85-上月交叉销售比≤N，则M=85-上月交叉销售比
            //主营保费目标=X月放款金额目标 *（上月交叉销售比+M）/10000 *45%
            value = smLoanAmtTarget.multiply(
                        //计算公式 M=85-上月交叉销售比,求（上月交叉销售比+M）
                        context.getSmOfflineLoanInsuranceRate().add(BigDecimal.valueOf(85).subtract(context.getSmOfflineLoanInsuranceRate()))
                    ).divide(BigDecimal.valueOf(10000)).multiply(BigDecimal.valueOf(0.45));
        }
        if (value.compareTo(BigDecimal.ZERO)<=0){
            value = BigDecimal.ZERO;
        }
        context.setSmLoanNormInsuranceAmtTarget(value);
        vo.setEstimateType(AssistantTargetEnum.LOAN_CLASSIC_INSURANCE_AMOUNT);
        vo.setValue(value);
        return vo;
    }

    private BigDecimal getSmLoanAmtTarget(CalcContext context) {
        if (context.getSmLoanAmountTarget()!=null && context.getSmLoanAmountTarget().compareTo(BigDecimal.ZERO)>0){
            return context.getSmLoanAmountTarget();
        }
        LocalDate now = LocalDate.now();
        //(今年年底余额目标- (X-1)月底余额）÷ (12-X+1)  + X月还款计划额
        return context.getSyLoanBalanceTarget().subtract(context.getSmLoanBalance())
                .divide(BigDecimal.valueOf(13).subtract(BigDecimal.valueOf(now.getMonthValue())),2, RoundingMode.HALF_UP)
                .add(context.getNmRepaymentPlanAmt());
    }
}
