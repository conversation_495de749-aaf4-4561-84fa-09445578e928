package com.cfpamf.ms.insur.operation.call.feign.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 呼叫记录查询响应DTO
 *
 * <AUTHOR>
 * @date 2025-07-22
 */
@Data
public class RecordQueryResponseDto {

    @ApiModelProperty("记录")
    @JsonProperty("Records")
    private Records records;

    @ApiModelProperty("数据")
    private Object data;

    @ApiModelProperty("消息")
    private String message;

    @ApiModelProperty("是否成功")
    private Boolean success;

    @Data
    public static class Records {
        @ApiModelProperty("上下文")
        @JsonProperty("Context")
        private String context;

        @ApiModelProperty("当前页")
        @JsonProperty("CurrentPage")
        private Integer currentPage;

        @ApiModelProperty("记录列表")
        @JsonProperty("Items")
        private List<RecordItem> items;

        @ApiModelProperty("每页数量")
        @JsonProperty("ItemsPerPage")
        private Integer itemsPerPage;

        @ApiModelProperty("总记录数")
        @JsonProperty("TotalItems")
        private Integer totalItems;

        @ApiModelProperty("总页数")
        @JsonProperty("TotalPages")
        private Integer totalPages;
    }

    @Data
    public static class RecordItem {
        @ApiModelProperty("呼叫开始时间")
        @JsonProperty("BeginCallTime")
        private LocalDateTime beginCallTime;

        @ApiModelProperty("呼叫SID")
        @JsonProperty("CallSid")
        private String callSid;

        @ApiModelProperty("被叫显号")
        @JsonProperty("CalledDisNum")
        private String calledDisNum;

        @ApiModelProperty("主叫")
        @JsonProperty("Caller")
        private String caller;

        @ApiModelProperty("主叫显号")
        @JsonProperty("CallerDisNum")
        private String callerDisNum;

        @ApiModelProperty("通道")
        @JsonProperty("Channel")
        private String channel;

        @ApiModelProperty("客户编号")
        @JsonProperty("ClientId")
        private String clientId;

        @ApiModelProperty("客户名称")
        @JsonProperty("ClientName")
        private String clientName;

        @ApiModelProperty("客户类型")
        @JsonProperty("ClientType")
        private String clientType;

        @ApiModelProperty("创建时间")
        @JsonProperty("CreateTime")
        private LocalDateTime createTime;

        @ApiModelProperty("通话时长")
        @JsonProperty("Duration")
        private Integer duration;

        @ApiModelProperty("员工编号")
        @JsonProperty("EmployeeId")
        private String employeeId;

        @ApiModelProperty("结束时间")
        @JsonProperty("EndTime")
        private LocalDateTime endTime;

        @ApiModelProperty("主叫号码")
        @JsonProperty("From")
        private String from;

        @ApiModelProperty("ID")
        @JsonProperty("Id")
        private Long id;

        @ApiModelProperty("关键值")
        @JsonProperty("Key")
        private String key;

        @ApiModelProperty("信贷员ID")
        @JsonProperty("LoId")
        private String loId;

        @ApiModelProperty("贷款类型")
        @JsonProperty("LoanType")
        private String loanType;

        @ApiModelProperty("模块")
        @JsonProperty("Module")
        private String module;

        @ApiModelProperty("录音时间")
        @JsonProperty("RecordTime")
        private LocalDateTime recordTime;

        @ApiModelProperty("录音地址")
        @JsonProperty("RecordUrl")
        private String recordUrl;

        @ApiModelProperty("备注")
        @JsonProperty("Remark")
        private String remark;

        @ApiModelProperty("开始时间")
        @JsonProperty("StartTime")
        private LocalDateTime startTime;

        @ApiModelProperty("子ID")
        @JsonProperty("SubId")
        private String subId;

        @ApiModelProperty("被叫号码")
        @JsonProperty("To")
        private String to;

        @ApiModelProperty("用户数据")
        @JsonProperty("UserData")
        private String userData;
    }
}
