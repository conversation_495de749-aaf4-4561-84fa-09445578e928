package com.cfpamf.ms.insur.operation.pco.service;

import com.cfpamf.ms.insur.operation.base.util.AliYunOssUtil;
import com.cfpamf.ms.insur.operation.pco.dao.PcoLevelImportErrorMapper;
import com.cfpamf.ms.insur.operation.pco.dao.PcoLevelImportMapper;
import com.cfpamf.ms.insur.operation.pco.dto.PcoLevelAdjustDTO;
import com.cfpamf.ms.insur.operation.pco.dto.PcoLevelAdjustExcelDTO;
import com.cfpamf.ms.insur.operation.pco.query.PcoLevelImportQuery;
import com.cfpamf.ms.insur.operation.pco.util.ExcelReadUtils;
import com.cfpamf.ms.insur.operation.pco.validation.ValidationResult;
import com.cfpamf.ms.insur.operation.pco.vo.PcoLevelImport;
import com.cfpamf.ms.insur.operation.pco.vo.PcoLevelImportError;
import com.fasterxml.jackson.core.JsonProcessingException;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.YearMonth;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class PcoLevelImportService {
    private PcoLevelImportMapper pcoLevelImportMapper;
    private PcoLevelImportErrorMapper pcoLevelImportErrorMapper;

    /**
     * 插入调整记录明细
     * @param dto
     * @param successCnt
     * @param errorCnt
     * @param operator
     * @param <T>
     * @return
     * @throws JsonProcessingException
     */
    @Transactional(rollbackFor = Exception.class)
    public  <T> PcoLevelImport addImportRecord(PcoLevelAdjustDTO dto, int successCnt, int errorCnt, String operator) throws JsonProcessingException {
        PcoLevelImport res = new PcoLevelImport();
        res.setSuccess(successCnt);
        res.setError(errorCnt);
        res.setFileName(dto.getFileName());
        res.setFileUrl(dto.getFileUrl());
        res.setCreateTime(LocalDateTime.now());
        res.setCreateBy(operator);
        pcoLevelImportMapper.insertUseGeneratedKeys(res);

        return res;
    }

    /**
     * 失败记录处理
     * @param res
     * @param errorDatas
     * @param errors
     * @throws IOException
     */
    public void addImportErrorRecord(PcoLevelImport res, List<PcoLevelAdjustExcelDTO> errorDatas, List<ValidationResult<PcoLevelAdjustExcelDTO>> errors) throws IOException {
        ByteArrayOutputStream os = new ByteArrayOutputStream(8096);
        ExcelReadUtils.write(os, errorDatas);

        String errorUrl = AliYunOssUtil.uploadByBytes(os.toByteArray(), genImportErrorOssPrefix()
                + "/error" + res.getId() + ".xlsx");
        List<PcoLevelImportError> errorLists = errors.stream()
                .map(val -> {
                    PcoLevelImportError error = new PcoLevelImportError();
                    error.setErrorCol("");
                    error.setErrorRow(val.getSource().getNo());
                    error.setErrorMsg(val.getMessage());
                    error.setImportId(res.getId());
                    return error;
                }).collect(Collectors.toList());
        pcoLevelImportErrorMapper.insertList(errorLists);

        PcoLevelImport update = new PcoLevelImport();
        update.setId(res.getId());
        update.setErrorUrl(errorUrl);
        pcoLevelImportMapper.updateByPrimaryKeySelective(update);
        res.setErrorUrl(errorUrl);
    }

    public static String genImportErrorOssPrefix() {
        return "pco/import/error/" + YearMonth.now().toString().replaceAll("-", "")
                + "/" + UUID.randomUUID().toString().replaceAll("-", "");
    }

    public List<PcoLevelImport> list(PcoLevelImportQuery query){
        return pcoLevelImportMapper.list(query);
    }
}
