package com.cfpamf.ms.insur.operation.pk.pojo.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
public class PkMatchOpponentsDto {

    @ApiModelProperty(value = "1.区域 2.分支 3.客户经理 4.督导 5.主任， 必填")
    private String type;

    @ApiModelProperty(value = "匹配人员工编号（客户经理、督导、主任编码）， 必填")
    private String userCode;

    private String userName;

    @ApiModelProperty(value = "匹配人分支编号， 必填")
    private String branchCode;

    private String branchName;

    @ApiModelProperty(value = "匹配人区域编码， 必填")
    private String regionCode;

    private String regionName;

    @ApiModelProperty(value = "指标类型")
    private String indicatorType;

    @ApiModelProperty(value = "指标值")
    private BigDecimal value;


}
