package com.cfpamf.ms.insur.operation.activity.vo;

import com.cfpamf.ms.insur.operation.activity.enums.ConflictRule;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.web.bind.annotation.GetMapping;

import javax.persistence.Column;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;

/**
 * 系统关联活动视图
 *
 * <AUTHOR>
 * @date 2021/7/8 17:22
 */
@ApiModel("系统关联活动")
@Getter
@Setter
public class SystemActivityRefVo {
    /**
     * id
     */
    @ApiModelProperty("id")
    private Long id;
    /**
     * 活动名称
     */
    @ApiModelProperty("活动名称")
    private String activityName;
    /**
     * 活动id
     */
    @ApiModelProperty("活动id")
    private Long systemActivityId;

    /**
     * 关联类型 overlay-叠加 optimal-最优
     */
    @ApiModelProperty(value = "关联类型 overlay-叠加 optimal-最优")
    private ConflictRule conflictRule;
}
