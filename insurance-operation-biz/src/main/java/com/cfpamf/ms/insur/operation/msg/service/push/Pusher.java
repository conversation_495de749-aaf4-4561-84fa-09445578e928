package com.cfpamf.ms.insur.operation.msg.service.push;

import com.cfpamf.ms.insur.operation.msg.pojo.po.OpMessagePush;
import com.cfpamf.ms.insur.operation.msg.pojo.po.OpMessageRule;
import com.cfpamf.ms.insur.operation.msg.pojo.po.OpMessageRuleReceiver;

import java.util.List;

/**
 * <AUTHOR> 2021/8/30 11:00
 */
public interface Pusher {

    /**
     * 消息推送
     *
     * @param rule                   具体规则
     * @param contextId              当前上下文id
     * @param delay                  是否是通过延迟消息
     * @param opMessageRuleReceivers 所有的消息接受者
     */
    void push(OpMessageRule rule, String contextId, boolean delay, List<OpMessageRuleReceiver> opMessageRuleReceivers);

    /**
     * 审核通过 推送消息
     *
     * @param pushes
     */
    void pushAudit(List<OpMessagePush> pushes);

}
