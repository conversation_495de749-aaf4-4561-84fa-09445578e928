package com.cfpamf.ms.insur.operation.addCommission.enums;

import lombok.Getter;

import java.util.stream.Stream;

/**
 * 结算事件触发类型
 *
 * <AUTHOR>
 */
@Getter
public enum SettlementEventTypeEnum {

    /**
     * 结算事件-保单中心
     */
    PERSONAL_NEW_POLICY("保单中心", "settlement.global.policy.personal_new_policy", "新单", "个险新契约", "FIRST_YR_COMM","FIRST_BASIC_COMM"),
    GROUP_NEW_POLICY("保单中心", "settlement.global.policy.group_new_policy", "新单", "团险新契约", "FIRST_YR_COMM","FIRST_BASIC_COMM"),
    RENEWAL_POLICY("保单中心", "settlement.global.policy.renewal", "续投", "保单续投", "FIRST_YR_COMM","FIRST_BASIC_COMM"),
    RENEWAL_TERM_POLICY("保单中心", "settlement.global.policy.renewal_term", "续期", "保单续期", "FIRST_YR_COMM","FIRST_BASIC_COMM"),
    STANDARD_SURRENDER("保单中心", "settlement.global.policy.standard_surrender", "保全", "标准退保", "FIRST_YR_COMM","FIRST_BASIC_COMM"),
    HESITATE_SURRENDER("保单中心", "settlement.global.policy.hesitate_surrender", "保全", "犹豫期退保", "FIRST_YR_COMM","FIRST_BASIC_COMM"),
    PROTOCOL_TERMINATION("保单中心", "settlement.global.policy.protocol_termination", "保全", "协议解约", "FIRST_YR_COMM","FIRST_BASIC_COMM"),
    TERMINATION_PRODUCT("保单中心", "settlement.global.policy.termination_product", "保全", "附加险解约", "FIRST_YR_COMM","FIRST_BASIC_COMM"),
    GROUP_ADD_OR_SUBTRACT("保单中心", "settlement.global.policy.group_add_or_subtract", "保全", "团险增减员", "FIRST_YR_COMM","FIRST_BASIC_COMM"),
    CUSTOMER_MANAGER_CHANGE("保单中心","settlement.global.policy.customer_manager_change","保全","初始推荐人变更","FIRST_YR_COMM","FIRST_BASIC_COMM"),
    CHANNEL_REFERRER_CHANGE("保单中心","settlement.global.policy.channel_referrer_change","保全","团险分单渠道推荐人变更","FIRST_YR_COMM","FIRST_BASIC_COMM"),
    POLICY_INTERRUPT("保单中心","settlement.global.policy.policy_interrupt","保单断保","长险未续回扣","NULL","LONG_NOT_RENEWAL_REBATE_COMM"),
    /**
     * 保单被保人层初始渠道推荐人变更事件(团险渠道推荐人导入)
     */
    POLICY_INSURED_REFERRER_EVENT("保单中心", "settlement.global.policy.insured_referrer_change", "保单被保人层-推荐人变更事件","团险渠道推荐人导入变更","FIRST_YR_COMM","FIRST_BASIC_COMM"),
    /**
     * 保单层初始渠道推荐人变更事件(个险渠道推荐人导入)
     */
    POLICY_REFERRER_EVENT("保单中心", "settlement.global.policy.policy_referrer_change", "保单层-推荐人变更事件","个险渠道推荐人导入变更","FIRST_YR_COMM","FIRST_BASIC_COMM"),

    /**
     * 结算中心冲正
     */
    REVERSAL_AMOUNT("结算系统", "settlement.global.auto.reversal_amount", "系统基础冲正", "系统基础冲正", "NULL","NULL"),
    REVERSAL_XIAOWHALE_MISSED_POLICY("结算系统", "settlement.global.auto.reversal_xiaowhale_missed_policy", "小鲸缺失保单冲正", "小鲸缺失保单冲正", "NULL","NULL"),
    REVERSAL_XIAOWHALE_MISSED("结算系统", "settlement.global.auto.reversal_xiaowhale_missed", "小鲸缺失冲正", "小鲸缺失冲正", "NULL","NULL"),
    AMOUNT_ACCURACY("结算系统", "settlement.global.auto.amount_accuracy", "系统精度冲正", "系统精度冲正", "NULL","NULL"),
    FLOATING_REWARDS("结算系统", "settlement.global.auto.floating_rewards", "浮动奖励冲正", "浮动奖励冲正", "NULL","NULL"),

    MANUAL_CORRECTION("结算系统", "settlement.global.commission.manual_correction", "人工冲正", "人工冲正", "FIRST_YR_COMM","FIRST_BASIC_COMM"),

    PROTOCOL_PRODUCT_PREM_CHANGE("协议管理", "settlement.global.protocol.product_prem_change","费率表变更","费率表变更","NULL","NULL"),
    PROTOCOL_POLICY_PREM_CHANGE("协议管理", "settlement.global.protocol.policy_prem_change","一单一议费率表变更","一单一议费率表变更","NULL","NULL"),

    CONTRACT_PRODUCT_PREM_CHANGE("合约管理", "settlement.global.contract.product_prem_change","费率表变更","费率表变更","NULL","NULL"),
    CONTRACT_POLICY_PREM_CHANGE("合约管理", "settlement.global.contract.policy_prem_change","一单一议费率表变更","一单一议费率表变更","NULL","NULL"),

    /**
     * 支出佣金配置变更事件
     */
    COMMISSION_PRODUCT_PREM_CHANGE("结算系统", "settlement.global.commission.product_prem_change", "费率表变更", "费率表变更", "FIRST_YR_COMM","FIRST_BASIC_COMM"),
    COMMISSION_POLICY_PREM_CHANGE("结算系统", "settlement.global.commission.policy_prem_change", "一单一议费率表变更", "一单一议费率表变更", "FIRST_YR_COMM","FIRST_BASIC_COMM") ,
    AUTO_COST_CALCULATE("结算系统", "settlement.global.commission.AUTO_COST_CALCULATE", "自动结算", "自动结算", "NULL","NULL"),


    CHANGE_POLICY_CODE("保单整合平台", "settlement.global.policy.platform.change_policy_code", "保单号变更", "保单号变更", "FIRST_YR_COMM","FIRST_BASIC_COMM"),
    CHANGE_ENDORSEMENT_NO("保单整合平台", "settlement.global.policy.platform.change_endorsement_no", "批单号变更", "批单号变更", "FIRST_YR_COMM","FIRST_BASIC_COMM"),
    REMOVE_POLICY("保单整合平台", "settlement.global.policy.platform.remove_policy", "删除保单", "删除保单", "FIRST_YR_COMM","FIRST_BASIC_COMM"),
    REMOVE_ENDORSEMENT("保单整合平台", "settlement.global.policy.platform.remove_endorsement", "删除批单", "删除批单", "FIRST_YR_COMM","FIRST_BASIC_COMM"),
    CHANGE_POLICY_PREMIUM("保单整合平台", "settlement.global.policy.platform.change_policy_premium", "保单保费变更", "保单保费变更", "FIRST_YR_COMM","FIRST_BASIC_COMM"),
    CHANGE_ENDORSEMENT_PREMIUM("保单整合平台", "settlement.global.policy.platform.change_endorsement_premium", "批改保费变更", "批改保费变更", "FIRST_YR_COMM","FIRST_BASIC_COMM"),

    CHANGE_RENEWAL_REALITY_TIME("保单整合平台", "settlement.global.policy.platform.change_renewal_reality_time", "续期实收时间变更", "续期实收时间变更", "FIRST_YR_COMM","FIRST_BASIC_COMM"),
    CHANGE_RENEWAL_REALITY_PREMIUM("保单整合平台", "settlement.global.policy.platform.change_renewal_reality_premium", "续期实收金额变更", "续期实收金额变更", "FIRST_YR_COMM","FIRST_BASIC_COMM"),
    CHANGE_RENEWAL_FALLBACK("保单整合平台", "settlement.global.policy.platform.change_renewal_fallback", "续期回退", "续期回退", "FIRST_YR_COMM","FIRST_BASIC_COMM"),

    CHANGE_POLICY_REFERRER("保单整合平台", "settlement.global.policy.platform.change_policy_referrer", "保单渠道推荐人", "保单渠道推荐人", "FIRST_YR_COMM","FIRST_BASIC_COMM"),
    CHANGE_POLICY_AGENT("保单整合平台", "settlement.global.policy.platform.change_policy_agent", "代理人变更", "代理人变更", "FIRST_YR_COMM","FIRST_BASIC_COMM"),

    INSURED_BASE_INFO_CHANGE("保单中心","settlement.global.policy.insured_base_info_change","保全","被保人信息变更","FIRST_YR_COMM","FIRST_BASIC_COMM"),
    ;



    private final String eventSource;

    private final String eventCode;

    private final String eventName;

    private final String eventDesc;

    private final String relationSubject;
    private final String relationCostSubject;

    SettlementEventTypeEnum(String eventSource, String eventCode, String eventName, String eventDesc, String relationSubject, String relationCostSubject) {
        this.eventSource = eventSource;
        this.eventCode = eventCode;
        this.eventName = eventName;
        this.eventDesc = eventDesc;
        this.relationSubject = relationSubject;
        this.relationCostSubject = relationCostSubject;
    }

    /**
     * 获取结算事件触发类型枚举
     *
     * @param eventCode 结算事件编码
     * <AUTHOR>
     * @since 2021/12/4
     */
    /*public static SettlementEventTypeEnum deCode(String eventCode) {
        return Arrays.stream(SettlementEventTypeEnum.values()).filter(x -> x.eventCode.equals(eventCode)).findFirst().orElse(null);
    }*/

    public static String dict(String eventCode) {
        return Stream.of(values())
                .filter(em -> em.getEventCode().equals(eventCode))
                .findFirst().map(SettlementEventTypeEnum::getEventDesc)
                .orElse("未知");
    }
}
