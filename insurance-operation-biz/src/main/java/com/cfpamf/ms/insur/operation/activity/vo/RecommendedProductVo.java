package com.cfpamf.ms.insur.operation.activity.vo;

import com.alibaba.druid.util.StringUtils;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Map;

/**
 * 推荐产品视图对象
 *
 * <AUTHOR>
 * @date 2021/7/8 16:20
 */
@ApiModel("推荐产品")
@Getter
@Setter
public class RecommendedProductVo {
    /**
     * 产品id
     */
    @ApiModelProperty("id")
    private Long id;
    /**
     * 产品名称
     */
    @ApiModelProperty("产品名称")
    private String productName;
    /**
     * 产品分类
     */
    @ApiModelProperty("产品分类")
    private String productType;
    /**
     * 保险公司
     */
    @ApiModelProperty("保险公司")
    private String companyName;
    /**
     * 渠道
     */
    @ApiModelProperty(value = "渠道")
    private String channel;

    /**
     * 产品属性
     */
    @ApiModelProperty(value = "产品属性")
    private String productAttrCode;


    @ApiModelProperty("产品简称")
    private String productShortName;

    /**
     * 摘要
     */
    @ApiModelProperty(value = "摘要")
    private String productFeature;

    /**
     * 列表显示图片地址
     */
    @ApiModelProperty(value = "列表显示图片地址")
    private String thumbnailImageUrl;

    /**
     * 最低起保金额 小数位数0
     */
    @ApiModelProperty(value = "最低起保金额")
    private BigDecimal minAmount;

    /**
     * 销售数量
     */
    @ApiModelProperty(value = "销售数量")
    private Integer saleQty;

    /**
     * 已配置海报
     */
    @ApiModelProperty(value = "已配置海报")
    private Boolean hasPoster;

    @ApiModelProperty(value = "对接类型 1-api 2-H5", example = "2")
    private Integer apiType;

    @ApiModelProperty("h5地址 不能为空")
    private String h5Url;

    /**
     * 排序号
     */
    @ApiModelProperty("排序号")
    private Integer sortNum;

    /**
     * 排序号
     */
    @ApiModelProperty("版本号")
    private Integer version;


    @ApiModelProperty("产品扩展属性 具体 同后台")
    private Map<String,String> attrs;

    @JsonIgnore
    @ApiModelProperty(value = "产品标签join")
    private String productTagsJoin;

    @ApiModelProperty(value = "创建类型 创建类型 个险短险-PERSON_SHORT_INSURANCE  个险长险-PERSON_LONG_INSURANCE 团险-GROUP_INSURANCE 车险CAR_INSURANCE 历史数据缺省类型- DEFAULT_TYP")
    private String createType;

    /**
     * 产品标签
     */
    @ApiModelProperty(value = "产品标签")
    private String[] productTags;

    public String[] getProductTags() {
        if (productTags != null && productTags.length > 0) {
            return productTags;
        }
        return StringUtils.isEmpty(productTagsJoin) ? new String[0] : productTagsJoin.split(",");
    }
}
