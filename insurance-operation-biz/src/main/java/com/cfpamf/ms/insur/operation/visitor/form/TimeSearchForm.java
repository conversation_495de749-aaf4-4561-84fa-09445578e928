package com.cfpamf.ms.insur.operation.visitor.form;

import com.cfpamf.ms.insur.operation.base.form.SortPageForm;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateDeserializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * 时间搜索表单
 *
 * <AUTHOR>
 * @date 2021/4/30 9:48
 */
@Getter
@Setter
public class TimeSearchForm extends SortPageForm {
    /**
     * 开始时间
     */
    @ApiModelProperty("开始时间 'yyyy-MM-dd HH:mm:ss'")
    @JsonDeserialize(using = LocalDateDeserializer.class)
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    @ApiModelProperty("结束时间  'yyyy-MM-dd HH:mm:ss'")
    @JsonDeserialize(using = LocalDateDeserializer.class)
    private LocalDateTime endTime;
}
