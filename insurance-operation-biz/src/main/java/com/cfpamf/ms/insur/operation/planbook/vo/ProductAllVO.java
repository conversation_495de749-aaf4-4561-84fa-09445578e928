package com.cfpamf.ms.insur.operation.planbook.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2021/12/8 15:12
 * @Version 1.0
 */
@Data
public class ProductAllVO {

    private Integer productId;

    @ApiModelProperty("计划")
    private List<ProductPlanVO> plans;

    @ApiModelProperty("费率因子 key为dutyKey ")
    Map<String, List<SysRiskFactorVO>> factors;

    @ApiModelProperty("主保障")
    SmProductRiskVO mainRisk;



}
