
package com.cfpamf.ms.insur.operation.activity.form;

import com.cfpamf.ms.insur.operation.activity.enums.EnumActivityConstJointType;
import com.cfpamf.ms.insur.operation.activity.enums.EnumActivityConstRewardType;
import com.cfpamf.ms.insur.operation.activity.enums.EnumActivityConstRuleType;
import com.cfpamf.ms.insur.operation.base.entity.BaseNoUserEntity;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Objects;

/**
 * Created by zhengjing  on 2022-06-23 10:29:05
 *
 * <AUTHOR>
 */
@ApiModel("营销活动手动配置的规则")
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class SystemActivityConstRuleForm extends BaseNoUserEntity {

    /**
     * 字段名称 活动id
     */
    @ApiModelProperty(value = "活动id", required = true)
    Long saId;
    /**
     * 字段名称 产品是否长险
     */
    @NotNull(message = "产品是否长险不能为空")
    @ApiModelProperty(value = "产品是否长险", required = true)
    Integer productLongInsurance;
    /**
     * 字段名称 产品id数据
     */
    @NotNull(message = "产品id不能为空")
    @ApiModelProperty(value = "产品id 单选是传单一数组",example = "[]", required = true)
    List<Integer> productIds;

    /**
     * 字段名称 产品id数据
     */
    @ApiModelProperty(value = "商品名称")
    List<String> sellProductNames;

    /**
     * 字段名称 长险为险总id数组 短险为计划id数组 为空数组代表所有
     */
    @NotNull(message = "二级配置不能为空")
    @ApiModelProperty(value = "长险为险总id数组 短险为计划id数组 累计保费传类型数组 个人-1 分支-2 区域-3 为空数组代表所有", required = true)
    List<Integer> planOrRiskIds;
    /**
     * 字段名称 长险为险总id数组 短险为计划id数组 为空数组代表所有
     */
    @ApiModelProperty(value = "险种名称数组为空数组代表所有")
    List<String> riskNames;
    /**
     * 字段名称 每单加佣-ORDER 规模加佣-SUM
     */
    @NotNull(message = "加佣规则不能为空")
    @ApiModelProperty(value = "加佣规则 每单加佣-ORDER_PREMIUM  规模保费-SCALE 累计保费-SUM，出单即送-ORDER_PAYED",
            example = "ORDER_PREMIUM", allowableValues = "ORDER_PREMIUM,SCALE,SUM,ORDER_PAYED", required = true)
    EnumActivityConstRuleType ruleType;
    /**
     * 字段名称 规则连接条件 且-AND 或-OR
     */
    @NotNull(message = "规则连接条件不能为空")
    @ApiModelProperty(value = "规则连接条件 且-AND 或-OR", allowableValues = "AND,OR", required = true)
    EnumActivityConstJointType jointType;
    /**
     * 字段名称 加佣奖励类型 加佣比例-RATIO 加佣金额-VALUE
     */
    @NotNull(message = "加佣奖励类型 加佣比例-RATIO 加佣金额-VALUE 不能为空")
    @ApiModelProperty(value = "加佣奖励类型 加佣比例-RATIO 加佣金额-VALUE ", required = true)
    EnumActivityConstRewardType rewardType;
    /**
     * 字段名称 加佣具体值
     */
    @NotNull(message = "加佣具体值不能为空")
    @ApiModelProperty(value = "加佣具体值", required = true)
    java.math.BigDecimal quantity;
    /**
     * 字段名称 参数-条件参数
     */
    @ApiModelProperty(value = "参数-条件参数")
    SystemActivityConstRuleParamForm params;

    @JsonIgnore
    public boolean isLongIns() {
        return Objects.equals(0, getProductLongInsurance());
    }
}

