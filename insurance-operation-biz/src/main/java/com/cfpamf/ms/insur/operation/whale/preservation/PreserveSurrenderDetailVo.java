package com.cfpamf.ms.insur.operation.whale.preservation;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "退保明细信息", description = "退保明细信息")
public class PreserveSurrenderDetailVo implements Serializable {
    @ApiModelProperty("被保人信息")
    private List<PreserveSurrenderInsuredVo> insuredList;

    @ApiModelProperty("险种信息")
    private List<PreserveSurrenderProductVo> productList;
}
