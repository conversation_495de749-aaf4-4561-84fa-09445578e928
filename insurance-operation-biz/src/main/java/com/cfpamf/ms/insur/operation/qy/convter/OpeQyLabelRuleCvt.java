package com.cfpamf.ms.insur.operation.qy.convter;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.cfpamf.ms.insur.operation.qy.entity.OpeQyLabelRule;
import com.cfpamf.ms.insur.operation.qy.form.OpeQyLabelRuleForm;
import com.cfpamf.ms.insur.operation.qy.form.OpeQyLabelRuleParams;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> 2022/6/23 10:42
 */
@Mapper(imports = {JSONObject.class,
        JSONArray.class,
        OpeQyLabelRuleParams.class})
public interface OpeQyLabelRuleCvt {

    OpeQyLabelRuleCvt INS = Mappers.getMapper(OpeQyLabelRuleCvt.class);

    /**
     * 表单对象转换成po
     *
     * @param s
     * @return
     */
    @Mappings({
            @Mapping(target = "labelRule", expression = "java(JSONObject.toJSONString(s.getLabelRule()))")
    })
    OpeQyLabelRule ruleFrom2Po(OpeQyLabelRuleForm s);

    /**
     * 列表转化
     *
     * @param fs
     * @return
     */
    default List<OpeQyLabelRule> ruleFrom2Po(List<OpeQyLabelRuleForm> fs) {
        return fs.stream().map(this::ruleFrom2Po).collect(Collectors.toList());
    }

    /**
     * 表单对象转换成po
     *
     * @param s
     * @return
     */
    @Mappings({
            @Mapping(target = "labelRule", expression = "java(JSONObject.parseObject(s.getLabelRule(), OpeQyLabelRuleParams.class))")
    })
    OpeQyLabelRuleForm questionPo2Form(OpeQyLabelRule s);

    default List<OpeQyLabelRuleForm> questionPo2Form(List<OpeQyLabelRule> fs) {
        return fs.stream().map(this::questionPo2Form).collect(Collectors.toList());
    }

}
