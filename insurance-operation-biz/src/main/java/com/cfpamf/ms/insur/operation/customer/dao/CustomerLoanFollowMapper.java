package com.cfpamf.ms.insur.operation.customer.dao;

import com.cfpamf.ms.insur.operation.base.dao.CommonMapper;
import com.cfpamf.ms.insur.operation.customer.dto.CustomerInterruptionFollowDto;
import com.cfpamf.ms.insur.operation.customer.po.CustomerLoanFollowPo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface CustomerLoanFollowMapper extends CommonMapper<CustomerLoanFollowPo> {

    /**
     * 更新以往最新记录为0
     *
     * @param customerId
     */
    void updateNewest(@Param("customerId") Long customerId);

    /**
     * 查询客户标签
     *
     * @param customerId
     */
    Integer getLoanCustomerRemark(@Param("customerId") String customerId);

    void insertFollow(@Param("customerId") Integer customerId, @Param("idNumber") String idNumber, @Param("accountTime") LocalDateTime accountTime);

    /**
     * 日复盘异业客户跟进记录
     * @param empCode 工号
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return
     */
    List<CustomerInterruptionFollowDto> queryLoanFollowListRetrospective(@Param("empCode") String empCode, @Param("startDate") LocalDate startDate, @Param("endDate") LocalDate endDate);
}
