package com.cfpamf.ms.insur.operation.fegin.oms.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Date;

@Data
@ApiModel("素材分享分组（按日期）")
public class MarketingForwardGroupByDateDTO {
    @ApiModelProperty("转发id")
    private Long forwardId;
    @ApiModelProperty("素材主键id")
    private Long marketingId;
    @ApiModelProperty(value = "日期")
    private String date;
    @ApiModelProperty(value = "年")
    private Integer year;
    @ApiModelProperty(value = "月")
    private Integer month;
    @ApiModelProperty(value = "日")
    private Integer day;
    @ApiModelProperty(value = "分享内容类型")
    private Integer sourceType;



    @ApiModelProperty(value = "素材标题")
    private String marketingTopic;
    @ApiModelProperty(value = "素材图片")
    private String marketingImg;


    @ApiModelProperty(value = "员工姓名")
    private String managerName;
    @ApiModelProperty(value = "员工工号")
    private String managerJobNumber;
    @ApiModelProperty(value = "员工头像")
    private String managerAvatar;
    @ApiModelProperty(value = "员工分支名称")
    private String managerBranchName;


    @ApiModelProperty(value = "访问次数")
    private Integer viewCount;

    @ApiModelProperty(value = "是否为日期首条")
    private boolean firstInDateFlag;

    @ApiModelProperty(value = "是否为日期首条")
    private boolean todayFlag;

    @ApiModelProperty(value = "转发时间")
    private String forwardTimeStr;

    @ApiModelProperty(value = "文件类型（0 未知无  1 图片 2 视频）")
    private Integer fileType;

    @ApiModelProperty(value = "商品编号")
    private String goodsNo;

    @ApiModelProperty("主题枚举")
    private Integer topicType;

    @ApiModelProperty("缩略图地址")
    private String thumbnailUrl;

    @ApiModelProperty("一级分类编码")
    private String lv1CategoryCode;
    @ApiModelProperty("二级分类编码")
    private String lv2CategoryCode;
}

