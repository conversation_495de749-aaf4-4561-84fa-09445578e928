package com.cfpamf.ms.insur.operation.prospectus.controller;

import com.cfpamf.ms.insur.operation.base.annotaions.ResponseDecorated;
import com.cfpamf.ms.insur.operation.prospectus.api.ProspectusProductFactorApi;
import com.cfpamf.ms.insur.operation.prospectus.form.ProspectusProductFactorForm;
import com.cfpamf.ms.insur.operation.prospectus.service.ProspectusProductFactorService;
import com.cfpamf.ms.insur.operation.prospectus.vo.FactorOptionVo;
import com.cfpamf.ms.insur.operation.prospectus.vo.PlanCoverageAmountVo;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/5/26 11:05
 */
@ResponseDecorated
@RestController
public class ProspectusProductFactorController implements ProspectusProductFactorApi {
    private ProspectusProductFactorService prospectusProductFactorService;

    public ProspectusProductFactorController(ProspectusProductFactorService prospectusProductFactorService) {
        this.prospectusProductFactorService = prospectusProductFactorService;
    }

    @Override
    public List<FactorOptionVo> getProductFactor(Long productId) {
        return prospectusProductFactorService.getProductFactor(productId);
    }

    @Override
    public BigDecimal calculationPremium(ProspectusProductFactorForm prospectusProductFactorForm) {
        return prospectusProductFactorService.calculationPremium(prospectusProductFactorForm);
    }

    @Override
    public Map<Integer, List<PlanCoverageAmountVo>> getPlanCoverageAmountList(Long productId) {
        return prospectusProductFactorService.getPlanCoverageAmountList(productId);
    }
}
