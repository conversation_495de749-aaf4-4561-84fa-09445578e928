package com.cfpamf.ms.insur.operation.dingtalk;

import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * <AUTHOR> 2022/9/23 11:45
 */
@Data
@ConfigurationProperties("dingtalk.robot")
@FieldDefaults(level = AccessLevel.PRIVATE)
public class DingTalkRobotProperties {
    String agentId;

    String appKey;

    String appSecret;
}
