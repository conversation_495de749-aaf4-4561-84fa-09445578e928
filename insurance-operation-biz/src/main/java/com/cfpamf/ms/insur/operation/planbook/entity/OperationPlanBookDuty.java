package com.cfpamf.ms.insur.operation.planbook.entity;

import com.cfpamf.ms.insur.operation.base.entity.BaseIntegerEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Table;
import java.math.BigDecimal;

/**
 * operation_plan_book_duty
 *
 * <AUTHOR>
@Data
@ApiModel(value = "generate.OperationPlanBookDuty")
@Table(name = "operation_plan_book_duty")
public class OperationPlanBookDuty extends BaseIntegerEntity {

    /**
     * 计划书id
     */
    @ApiModelProperty(value = "计划书id")
    @Column(name = "plan_book_id")
    private Integer planBookId;

    /**
     * 责任key
     */
    @ApiModelProperty(value = "责任key")
    @Column(name = "duty_key")
    private String dutyKey;

    /**
     * 责任编码
     */
    @ApiModelProperty(value = "责任编码")
    @Column(name = "duty_code")
    private String dutyCode;

    /**
     * 版本
     */
    @ApiModelProperty(value = "责任版本")
    @Column(name = "duty_version")
    private Integer dutyVersion;

    /**
     * 险种编码
     */
    @ApiModelProperty(value = "险种编码")
    @Column(name = "risk_code")
    private String riskCode;

    /**
     * 险种key
     */
    @ApiModelProperty(value = "险种key")
    @Column(name = "risk_key")
    private String riskKey;

    /**
     * 责任试算价格
     */
    @ApiModelProperty(value = "责任试算价格")
    @Column(name = "price")
    private BigDecimal price;

    /**
     * 责任试算基数
     */
    @ApiModelProperty(value = "责任试算基数")
    @Column(name = "amount")
    private BigDecimal amount;

}