package com.cfpamf.ms.insur.operation.order.dao;

import com.cfpamf.ms.insur.operation.base.dao.CommonMapper;
import com.cfpamf.ms.insur.operation.order.dto.SmBaseOrderVO;
import com.cfpamf.ms.insur.operation.order.dto.SmOrderInsured;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface SmOrderMapper  extends CommonMapper<SmOrderInsured> {

    /**
     * 查询订单渠道基础信息信息
     *
     * @param orderId
     * @return
     */
    SmBaseOrderVO getBaseOrderInfoByOrderId(@Param("orderId") String orderId);
}
