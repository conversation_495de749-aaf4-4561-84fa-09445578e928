package com.cfpamf.ms.insur.operation.visitor.vo;

import com.alibaba.fastjson.JSON;
import com.cfpamf.ms.insur.operation.visitor.vo.VisitProductRecordVo;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 访客记录
 *
 * <AUTHOR>
 * @date 2021/4/29 16:20
 */
@Data
public class VisitorRecordVo extends BaseVisitorVo {

    /**
     * 访问次数
     */
    private Integer count;

    /**
     * 是否为意向客户
     */
    private Boolean potentialCustomer;

    /**
     * 上一次访问产品记录
     */
    private VisitProductRecordVo lastVisitProductRecord;

}
