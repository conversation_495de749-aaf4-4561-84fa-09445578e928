package com.cfpamf.ms.insur.operation.promotion.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/3/22 14:28
 * @ClassName
 */
@Data
@ApiModel(value = "MarketingLaunchDTO",description = "素材上架/下架/作废dto")
public class MarketingLaunchDTO {

    @ApiModelProperty(value = "主键列表", required = true)
    @NotNull(message = "主键主键列表不能为空！")
    private List<Long> idList;
    @ApiModelProperty(value = "上架操作类型 1-上架 0-下架 -2-作废")
    @NotNull(message = "上架操作类型不能为空！")
    private Integer launchOperation;

    @ApiModelProperty(value = "发布方式 0-立即发布 1-定时发布", required = true, example = "0")
    private Integer launchType;

    @ApiModelProperty("定时发布时间 yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime launchTime;

    @ApiModelProperty(value = "发布范围类型方式 0-所有员工 1-按条件筛选", required = true, example = "0")
    private Integer launchRange;

    @ApiModelProperty(value = "发布范围员工工号list")
    private List<String> launchUserList;

    @ApiModelProperty(value = "设置提醒（0：无需提醒，1：需要提醒）")
    private Integer remindType;

    @ApiModelProperty(value = "提醒方式：push（0：未选择，1：选择）")
    private Integer remindPush;

    @ApiModelProperty(value = "提醒方式：企业微信群（0：未选择，1：选择）")
    private Integer remindWechat;

    @ApiModelProperty(value = "提醒方式：企业微信群（0：未选择，1：选择）")
    private Integer remind;

    @ApiModelProperty(value = "发送方式（1：立即发送:2：定时发送）")
    private Integer sendType;

    @ApiModelProperty(value = "发送时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH")
    private LocalDateTime sendDate;

    @ApiModelProperty(value = "push发送人数")
    private Integer remindCountPush;

    @ApiModelProperty(value = "企业微信发送人数")
    private Integer remindCountWechat;

    @ApiModelProperty(value = "push手机号码")
    private List<String> pushMobileList;

    @ApiModelProperty(value = "企业微信用户")
    private List<String> wechatUserIdList;

    @ApiModelProperty(value = "企业微信群关键字")
    private List<String> keyWords;

    @ApiModelProperty(value = "备注")
    private  String remark;
}
