package com.cfpamf.ms.insur.operation.base.service;


import com.cfpamf.ms.insur.operation.base.exception.BusinessException;
import com.cfpamf.ms.insur.operation.fegin.bms.enums.ExcptEnum;
import com.google.common.collect.ImmutableList;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.stereotype.Service;
import redis.clients.jedis.Jedis;

import java.util.Collections;

/**
 * redis分布式锁
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class RedisLockTemplate {

    /**
     * lock key prefix
     */
    public static final String LOCK_PREFIX = "insurance_operation_redis_lock_";
    public static final String REDIS_EXECUTE_SUCCESS_RETURN = "OK";

    /**
     * RedisTemplate
     */
    @Autowired
    private RedisTemplate redisTemplate;

    /**
     * tryLock Non-blocking
     *
     * @param key
     * @param seconds
     * @return
     */
    public boolean tryLock(String key, long seconds) {
        String lockKey = getLockKey(key);
        boolean result = false;
        String status = (String) redisTemplate.execute((RedisCallback<String>) redisConnection -> {
            Jedis jedis = (Jedis) redisConnection.getNativeConnection();
            return jedis.set(lockKey, "1", "nx", "ex", seconds);
        });
        if (REDIS_EXECUTE_SUCCESS_RETURN.equals(status)) {
            result = true;
        }
        log.info("分布式锁 tryLock key={} seconds={} result={}", key, seconds, result);
        return result;
    }

    /**
     * lock with blocking
     *
     * @param key
     * @param seconds
     * @return
     */
    public boolean lock(String key, long seconds) {
        long timeoutAt = System.currentTimeMillis() + seconds * 1000;
        while (System.currentTimeMillis() < timeoutAt) {
            if (tryLock(key, seconds)) {
                return true;
            }
        }
        log.error("分布式锁 lock key={} seconds={} fail=！！！！！", key, seconds);
        throw new BusinessException(ExcptEnum.DLOCK_GET_ERROR);
    }

    /**
     * lock with blocking
     *
     * @param key
     * @param seconds
     * @return
     */
    public boolean lock(String key, long seconds, String errmsg) {
        long timeoutAt = System.currentTimeMillis() + seconds * 1000;
        while (System.currentTimeMillis() < timeoutAt) {
            if (tryLock(key, seconds)) {
                return true;
            }
        }
        log.error("分布式锁 lock key={} seconds={} fail=！！！！！", key, seconds);
        throw new BusinessException(ExcptEnum.DLOCK_GET_ERROR.getCode(), errmsg);
    }

    /**
     * unLock
     *
     * @param key
     */
    @SuppressWarnings("unchecked")
    public void unLock(String key) {
        ImmutableList<String> keys = ImmutableList.of(StringUtils.join(Collections.singleton(getLockKey(key)), ""));
        String script = "if redis.call('get', KEYS[1]) == ARGV[1] then return redis.call('del', KEYS[1]) else return 0 end";
        redisTemplate.execute(new DefaultRedisScript<>(script, String.class), keys, 1);
        log.info("分布式锁 unLock key={}", key);
    }

    /**
     * getLockKey
     *
     * @param key
     * @return
     */
    private String getLockKey(String key) {
        return LOCK_PREFIX + key;
    }
}
