package com.cfpamf.ms.insur.operation.honor.validation.level;

import com.cfpamf.ms.insur.operation.honor.dto.HonorListExcelDTO;
import com.cfpamf.ms.insur.operation.honor.validation.HonorValidationResult;
import lombok.AccessLevel;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * 评选对象-分支 校验规则
 * <AUTHOR>
 */
@Component("bchValidation")
@Slf4j
@FieldDefaults(level = AccessLevel.PRIVATE)
public class BchValidation implements HonorLevelValidation{
    @Override
    public HonorValidationResult validate(HonorListExcelDTO excelDto) {
        HonorValidationResult result = new HonorValidationResult();
        if(StringUtils.isBlank(excelDto.getBchName()) || StringUtils.isBlank(excelDto.getBchCode())){
            result.addMessage("分支名称/分支编码不能为空");
        }
        return result;
    }
}
