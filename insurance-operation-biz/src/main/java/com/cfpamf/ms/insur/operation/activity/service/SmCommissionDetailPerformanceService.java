package com.cfpamf.ms.insur.operation.activity.service;

import com.cfpamf.ms.insur.operation.activity.dao.SmCommissionDetailPerformanceMapper;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

/**
 * 实收业绩
 *
 * <AUTHOR> 2022/8/26 11:46
 */
@Service
@Slf4j
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class SmCommissionDetailPerformanceService {

    SmCommissionDetailPerformanceMapper mapper;

    /**
     * 实收业绩-车险
     */
    public void performanceAuto(LocalDateTime start, LocalDateTime end) {
        log.info("实收业绩-车险 {},{}", start, end);
        int policy = mapper.insertAutoPolicy(start, end);

        //修改边界值按100%计入实收
        int boundary = mapper.insertAutoPolicyBoundaryValue(start, end);

        int cancel = mapper.insertAutoPolicyCancel(start, end);
        log.info("实收业绩-车险 {},{} policy {} cancel {} boundary {}", start, end, policy, cancel, boundary);
    }

    /**
     * 实收业绩-非车险
     */
    public void performanceNotCx(LocalDateTime start, LocalDateTime end) {
        log.info("实收业绩-非车险{},{}", start, end);
        int i = mapper.insertPolicyNotCx(start, end);
        log.info("实收业绩-非车险 {},{} policy {}", start, end, i);
    }

}
