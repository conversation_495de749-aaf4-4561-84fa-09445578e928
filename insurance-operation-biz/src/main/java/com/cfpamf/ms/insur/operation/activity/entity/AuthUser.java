package com.cfpamf.ms.insur.operation.activity.entity;

import java.io.Serializable;
import java.util.Date;

import com.cfpamf.ms.insur.operation.base.entity.BaseEntity;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Table;

/**
 * auth_user
 * <AUTHOR>
@Data
@Getter
@Setter
@Table(name = "auth_user")
public class AuthUser extends BaseEntity {

    /**
     * 区域
     */
    private String regionName;

    /**
     * 机构
     */
    private String organizationName;

    /**
     * 员工工号
     */
    private String userId;

    /**
     * 员工姓名
     */
    private String userName;

    /**
     * 员工手机号
     */
    private String userMobile;

    private String userType;

    /**
     * 微信openId
     */
    private String wxOpenId;

    private String wxNickName;

    /**
     * 微信头像Url
     */
    private String wxImgUrl;

    /**
     * 登录过期时间
     */
    private Date expireTime;

    /**
     * 登录sessionId
     */
    private String loginId;



    /**
     * 同步批次号
     */
    private String batchNo;

    private String organizationFullName;

    private String orgPath;

    private String postCode;

    private String postName;

    private String status;

    private String regionCode;

    private String orgCode;

    private String hrOrgId;

    private String userIdCard;

    private Integer agentId;

    private String agentTopUserId;

    private Byte realVerify;

    private String userEmail;

    /**
     * 任职类型
     */
    private Byte serviceType;

    /**
     * 切换组织时间
     */
    private Date switchTime;

    /**
     * 任职唯一识别编码
     */
    private String jobCode;

    /**
     * hr用户id
     */
    private Integer hrUserId;

    /**
     * bms用户id
     */
    private Integer bmsUserId;

    /**
     * 主工号
     */
    private String mainJobNumber;

    /**
     * 推荐吗
     */
    private String bizCode;
}