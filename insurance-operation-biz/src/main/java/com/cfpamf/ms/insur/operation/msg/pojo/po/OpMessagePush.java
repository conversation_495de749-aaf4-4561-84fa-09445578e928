
package com.cfpamf.ms.insur.operation.msg.pojo.po;

import com.cfpamf.ms.insur.operation.base.entity.BaseNoUserEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import javax.persistence.Table;

/**
 * Created by zhengjing  on 2021-07-15 18:19:36
 *
 * <AUTHOR>
 */
@ApiModel("营销工具-消息推送记录")
@Table(name = "op_message_push")
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class OpMessagePush extends BaseNoUserEntity {

    /**
     * 字段名称 规则id
     */
    @ApiModelProperty(value = "规则id")
    Long messageRuleId;

    String contextId;
    /**
     * 字段名称 消息id
     */
    @ApiModelProperty(value = "消息id")
    String messageId;
    /**
     * 字段名称 推送内容
     */
    @ApiModelProperty(value = "推送内容")
    String content;
    /**
     * 字段名称 接受者类型 chat employee
     */
    @ApiModelProperty(value = "接受者类型 chat employee")
    String receiverType;
    /**
     * 字段名称 接受者 类型为群时
     */
    @ApiModelProperty(value = "接受者 类型为群时")
    String receiver;

    @ApiModelProperty(value = "接受者名称")
    String receiverName;
    @ApiModelProperty(value = "业务编码")
    String bizCode;
    @ApiModelProperty(value = "规则编码")
    String ruleCode;

    String messageCache;

    @ApiModelProperty("状态0-未推送 1-已推送 2-待审核F 3-推送失败")
    Integer state;
}

