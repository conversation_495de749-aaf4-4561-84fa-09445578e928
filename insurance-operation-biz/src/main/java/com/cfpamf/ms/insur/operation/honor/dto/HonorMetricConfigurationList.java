package com.cfpamf.ms.insur.operation.honor.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

/**
 * <AUTHOR>
 * 荣誉规则指标列表
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class HonorMetricConfigurationList {
    @ApiModelProperty(name="评选对象 area:区域，bch：分支，emp：个人")
    String level;

    @ApiModelProperty(name="评选周期 year:年度，quarter：季度，month：月")
    String period;

    @ApiModelProperty(name="指标名称")
    String metricName;

    @ApiModelProperty(name="指标编码")
    String metricCode;
}
