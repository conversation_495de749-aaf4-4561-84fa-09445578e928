package com.cfpamf.ms.insur.operation.pk.pojo.resp;

import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class IndicatorsResponse {

    @ApiModelProperty(value = "1.区域 2.分支 3.客户经理 4.督导， 必填")
    String type;

    @ApiModelProperty(value = "客户经理、督导为工号，分支以及区域为分支以及区域编码 必填")
    String code;

    @ApiModelProperty("名字")
    String name;
    @ApiModelProperty(value = "战力指标 必填")
    List<IndicatorsVO> indicatorsVOList;
}