package com.cfpamf.ms.insur.operation.customer.service;

import com.cfpamf.ms.insur.operation.customer.dao.CustomerLoanFirstFollowMapper;
import com.cfpamf.ms.insur.operation.customer.dao.CustomerLoanFirstMapper;
import com.cfpamf.ms.insur.operation.customer.dto.CustomerFollowDto;
import com.cfpamf.ms.insur.operation.customer.po.CustomerLoanFirstFollowPo;
import com.cfpamf.ms.insur.operation.customer.po.CustomerLoanFirstPo;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/5/8 16:58
 */
@Service
@Slf4j
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class CustomerLoanFirstService {

    CustomerLoanFirstMapper customerLoanFirstMapper;

    CustomerLoanFirstFollowMapper customerLoanFirstFollowMapper;

    public CustomerLoanFirstPo getLoanFirstCustomerDetail(String loanCustId) {
        CustomerLoanFirstPo po = new CustomerLoanFirstPo();
        po.setLoanCustId(loanCustId);
        return customerLoanFirstMapper.selectOne(po);
    }

    public List<CustomerFollowDto> getLoanFirstFollowByLoanCustId(String loanCustId) {
        return customerLoanFirstFollowMapper.selectLoanFirstFollowByLoan(loanCustId);
    }

    public void insertFromDwd() {
        customerLoanFirstMapper.insertFromDwd();
    }
}
