package com.cfpamf.ms.insur.operation.activity.controller;

import com.cfpamf.ms.insur.operation.activity.form.ExportForm;
import com.cfpamf.ms.insur.operation.activity.service.ExportService;
import com.cfpamf.ms.insur.operation.base.annotaions.ResponseDecorated;
import com.cfpamf.ms.insur.operation.base.constant.BaseConstants;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * <AUTHOR>
 * @Date 2022/6/15 09:50
 * @Version 1.0
 */
@Slf4j
@Api(value = "达标活动接口", tags = {"达标活动奖励管理接口"})
@RequestMapping(value = {BaseConstants.ADMIN_VERSION + "/operation/activity/excel"})
@ResponseDecorated
@RestController
public class ExcelExportController {
    @Autowired
    private ExportService exportService;

    @PostMapping("/custom/export")
    public void export(@RequestBody ExportForm exportForm, HttpServletResponse httpServletResponse) throws IOException {
        exportService.export(exportForm, httpServletResponse);
    }
}
