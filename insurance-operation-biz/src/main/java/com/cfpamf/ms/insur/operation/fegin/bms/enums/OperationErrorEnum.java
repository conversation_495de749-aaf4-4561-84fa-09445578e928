package com.cfpamf.ms.insur.operation.fegin.bms.enums;

import com.cfpamf.common.ms.enums.MSEnum;
import com.cfpamf.ms.insur.operation.base.constant.BaseConstants;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.experimental.FieldDefaults;

/**
 * 系统自定义异常
 *
 * <AUTHOR>
 */
@Getter
@FieldDefaults(level = AccessLevel.PRIVATE)
@AllArgsConstructor
public enum OperationErrorEnum implements MSEnum {

    /**
     * 钉钉api异常
     */
    DING_TALK_API_ERROR(BaseConstants.ERROR_CODE_THIRD_API_PREFIX + "010", "钉钉api调用失败"),
    DING_TALK_API_ERROR_USERID(BaseConstants.ERROR_CODE_THIRD_API_PREFIX + "011", "钉钉api调用失败userid错误"),

    /**
     * 钉钉api异常
     */
    WX_API_ERROR(BaseConstants.ERROR_CODE_THIRD_API_PREFIX + "002", "微信api调用失败"),

    /**
     * 数据库数据不存在
     */
    DB_DATE_NOT_EXISTS(BaseConstants.ERROR_CODE_DB_PREFIX + "001", "数据库数据不存在"),

    /**
     * 模板处理
     */
    TEMPLATE_PROC_ERROR(BaseConstants.ERROR_CODE_MSG_PREFIX + "001", "模板数据处理失败"),

    MSG_CONFIG_ERROR(BaseConstants.ERROR_CODE_MSG_PREFIX + "002", "配置数据异常"),
    SERVICE(BaseConstants.ERROR_CODE_MSG_PREFIX + "999", "未知异常"),
    ;

    /**
     * 编码
     */
    String code;

    /**
     * 名称
     */
    String msg;

}
