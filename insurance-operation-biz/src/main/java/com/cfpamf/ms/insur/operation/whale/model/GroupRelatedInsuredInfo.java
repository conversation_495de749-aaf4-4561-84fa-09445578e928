package com.cfpamf.ms.insur.operation.whale.model;

import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description
 * @date 2023/6/25 9:45 上午
 * @Version 1.0
 */
@FieldDefaults(level = AccessLevel.PRIVATE)
@Data
public class GroupRelatedInsuredInfo extends InsuredBaseInfo{
    /**
     * 与第一被保人关系
     */
    private String firstInsuredRelation;
    /**
     * 险种编码
     */
    private String productCode;
    /**
     * 保费
     */
    private BigDecimal premium;

}
