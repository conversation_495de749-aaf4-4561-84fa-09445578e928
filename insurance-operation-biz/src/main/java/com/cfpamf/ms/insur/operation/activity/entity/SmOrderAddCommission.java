package com.cfpamf.ms.insur.operation.activity.entity;

import com.cfpamf.ms.insur.operation.activity.enums.EnumCommissionType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;
import lombok.Data;

import javax.persistence.*;

/**
 * sm_order_add_commission
 * <AUTHOR>
@ApiModel(value="订单加佣表")
@Table(name = "sm_order_add_commission")
@Data
public class SmOrderAddCommission implements Serializable {

    /**
     * 订单Id
     */
    @ApiModelProperty(value="订单Id")
    private String fhOrderId;

    /**
     * 加佣类型 ACTIVITY-活动加佣
     */
    @ApiModelProperty(value="加佣类型 ACTIVITY-活动加佣")
    @Column(name = "commission_type")
    @Enumerated(EnumType.STRING)
    private EnumCommissionType commissionType;

    /**
     * 加佣比例
     */
    @ApiModelProperty(value="加佣比例")
    private BigDecimal commissionProportion;

    /**
     * 数据标识 如活动id
     */
    @ApiModelProperty(value="数据标识 如活动id")
    private String dataId;

    private Integer enabledFlag;

    /**
     * 主键id
     */
    @Id
    @OrderBy("desc")
    protected Integer id;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private LocalDateTime createTime;
}