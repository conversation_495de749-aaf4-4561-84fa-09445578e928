package com.cfpamf.ms.insur.operation.customer.dao;

import com.cfpamf.ms.insur.operation.base.dao.CommonMapper;
import com.cfpamf.ms.insur.operation.customer.dto.CustomerInterruptionFollowDto;
import com.cfpamf.ms.insur.operation.customer.po.CustomerInterruptionFollowPo;
import com.cfpamf.ms.insur.operation.customer.po.CustomerInterruptionPo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface CustomerInterruptionFollowMapper extends CommonMapper<CustomerInterruptionFollowPo> {

    /**
     * 更新以往最新记录为0
     * @param customerId
     */
    void updateNewest(@Param("customerId") String customerId);

    /**
     * 根据客户获取续保跟进记录
     * @param customerId
     * @return
     */
    List<CustomerInterruptionFollowDto> selectByCustomer(@Param("customerId") String customerId);
    /**
     * 根据客户获取续保跟进记录(包括AI外呼跟进)
     * @param customerId
     * @return
     */
    List<CustomerInterruptionFollowDto> selectByCustomerIncludeAI(@Param("customerId") String customerId);

    /**
     * 新增跟进记录
     * @param list
     */
    void insertFollow(@Param("list") List<CustomerInterruptionPo> list);

    List<CustomerInterruptionFollowDto> queryInterruptionFollowListRetrospective(@Param("empCode") String empCode, @Param("startDate") String startDate);

    List<CustomerInterruptionFollowDto> queryRenewalTermFollowListRetrospective(@Param("empCode") String empCode, @Param("startDate") String startDate);
}
