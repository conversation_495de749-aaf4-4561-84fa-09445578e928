package com.cfpamf.ms.insur.operation.customer.po;

import com.cfpamf.ms.insur.operation.base.po.BasePO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import javax.persistence.Table;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * 客户断保跟进记录表
 */
@Data
@Table(name = "customer_interruption_follow")
@FieldDefaults(level = AccessLevel.PRIVATE)
public class CustomerInterruptionFollowPo extends BasePO {
    @ApiModelProperty(name="客户Id")
    String customerId;

    @ApiModelProperty("证件号")
    String idNumber;

    @ApiModelProperty(name="服务方式")
    String serviceMode;

    @ApiModelProperty(name="意向")
    String intention;

    @ApiModelProperty(name="不续原因")
    String reason;
    @ApiModelProperty(name="不续说明")
    String reasonRemark;
    @ApiModelProperty(name="备注")
    String remark;

    @ApiModelProperty(name="订单号")
    LocalDateTime nextTime;

    @ApiModelProperty(name="是否最新记录")
    Integer newest;

    @ApiModelProperty(name="跟进人")
    String followPeople;

    @ApiModelProperty(name="跟进时间")
    LocalDateTime followTime;
}
