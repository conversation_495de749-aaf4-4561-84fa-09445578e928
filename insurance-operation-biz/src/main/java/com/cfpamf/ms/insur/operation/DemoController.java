package com.cfpamf.ms.insur.operation;

import com.cfpamf.ms.insur.operation.base.helper.GroovyHelper;
import com.cfpamf.ms.insur.operation.base.service.SystemGroovyService;
import com.cfpamf.ms.insur.operation.job.SystemActivityProgrammeJobHandler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

@SuppressWarnings("ALL")
@RestController
public class DemoController {

    @Autowired
    @Lazy
    private SystemGroovyService systemGroovyService;
    @Autowired
    GroovyHelper helper;
    String script="package groovy.activity\n" +
            "\n" +
            "import groovy.transform.Field\n" +
            "import com.google.common.collect.Lists\n" +
            "import com.google.common.collect.Maps\n" +
            "\n" +
            "@Field List MERGE_ORG_LIST = Arrays.asList(\"凉城\", \"丰镇\", \"镶黄旗\", \"化德\", \"东乌旗\", \"西乌旗\", \"多伦\", \"蓝旗\", \"白旗\", \"太仆寺\", \"苏尼特左旗\", \"二连浩特\", \"阿巴嘎旗\", \"锡林浩特\");\n" +
            "\n" +
            "List<Map<String, Object>> execute(String params) {\n" +
            "\t\n" +
            "    Map<String,Long> sumMap = summaryAchievement(); \n" +
            "    log.warn(\"统计数据查询：{}\",sumMap)\n" +
            "    String sql = \"\"\" \n" +
            "    \t \n" +
            "        select \n" +
            "            t.conversion_amount as premium,\n" +
            "            t.order_id as orderId,\n" +
            "            t.policy_no as policyNo, \n" +
            "            t.insured_id_number as idCard,\n" +
            "            t.plan_id as planId,\n" +
            "            t.risk_id as riskId,\n" +
            "            t.term_num as termNum,\n" +
            "            t.policy_status as policyStatus,\n" +
            "            t.commission_user_id as userId,\n" +
            "            u.regionName,\n" +
            "            u.organizationName,\n" +
            "            u.orgCode \n" +
            "        from sm_commission_detail t \n" +
            "        left join sm_plan p on t.plan_id=p.id\n" +
            "        left join auth_user u on t.commission_user_id=u.userId\n" +
            "        where p.productId=494\n" +
            "        and orgCode is not null\n" +
            "        and regionName!='总部'  \n" +
            "        and t.account_time >= str_to_date('2022-06-01', '%Y-%m-%d')\n" +
            "        and t.account_time < str_to_date('2022-07-01', '%Y-%m-%d') \n" +
            "        and SUBSTRING_INDEX(t.order_id,'_',1) \n" +
            "        in(\n" +
            "            select  `fhOrderId` \n" +
            "                    from sm_order b \n" +
            "                    left join sm_commission_detail c on b.fhOrderId=c.order_id\n" +
            "                    where \tb.productId = 494\n" +
            "                    and \tb.`payStatus` = 2\n" +
            "                    and \tcase when b.paymentTime=null then c.create_time else b.paymentTime end >= str_to_date('2022-06-01', '%Y-%m-%d')\n" +
            "                    and \tcase when b.paymentTime=null then c.create_time else b.paymentTime end < str_to_date('2022-07-01', '%Y-%m-%d')\n" +
            "                    and \tINSTR (b.fhOrderId ,'_') =0\n" +
            "        )\n" +
            "            \"\"\" \n" +
            "    List<Map<String,Object>> itemMap = msgStatService.selectMaps(sql)\n" +
            "    log.warn(\"客户经理业绩数据查询结果：{}\",itemMap)\n" +
            "    List<Map<String, Object>> result = Lists.newArrayList()\n" +
            "    for (Map<String, Object> map : itemMap) {\n" +
            "    \tString orgName=map.get(\"organizationName\")\n" +
            "        String regionName = map.get(\"regionName\")\n" +
            "        String key = orgName+\"-\"+regionName\n" +
            "        log.warn(\"所属区域：{}\",regionName)\n" +
            "        if(MERGE_ORG_LIST.contains(orgName)){\n" +
            "        \tlog.warn(\"合并机构被过滤：{}\",orgName) \n" +
            "        \tcontinue\n" +
            "        }\n" +
            "        Integer prop = (Integer)sumMap.get(key)\n" +
            "        log.warn(\"获取佣金信息：{},{}\",key,prop) \n" +
            "        if(prop!=null){\n" +
            "        \t\n" +
            "            String orderId  =  (String)map.get(\"orderId\")\n" +
            "            String policyNo = (String)map.get(\"policyNo\") \n" +
            "            String idCard   =   (String)map.get(\"idCard\")\n" +
            "            String planId   =   (String)map.get(\"planId\")\n" +
            "            String riskId   =   (String)map.get(\"riskId\")\n" +
            "            String termNum  =  (String)map.get(\"termNum\")\n" +
            "            String userId   =   (String)map.get(\"userId\") \n" +
            "            String policyStatus = (String)map.get(\"policyStatus\")\n" +
            "        \tString dataId = orderId+'|'+policyNo+'|'+idCard+'|'+planId+'|'+riskId+'|' +termNum+'|' +policyStatus\n" +
            "        \tMap<String,Object> resMap =new HashMap()\n" +
            "            resMap.put(\"dataId\",dataId)\n" +
            "            resMap.put(\"uuid\",dataId) \n" +
            "            resMap.put(\"proportion\",prop) \n" +
            "            result.add(resMap)  \n" +
            "        }\n" +
            "        String orderId  =  (String)map.get(\"orderId\")\n" +
            "    }\n" +
            "    return result \n" +
            "}\n" +
            "\t\n" +
            "// 按分支机构维度统计业绩\n" +
            "private Map<String,Long> summaryAchievement(){\n" +
            "  \tString sql = '''\n" +
            "        \tselect \n" +
            "\tsum(premium) as premium,\n" +
            "\ta.regionName,\n" +
            "\ta.organizationName,\n" +
            "\to.staff\n" +
            " from (\n" +
            "\t\t\t\t\tselect \n" +
            "\t\t\t\t\t\tcase when t.conversion_amount is null and t.policy_status=4 then t.amount*-1  \n" +
            "\t\t\t\t\t\twhen t.conversion_amount is null and t.policy_status=1 then t.amount\n" +
            "\t\t\t\t\t\telse t.conversion_amount end as premium,\n" +
            "\t\t\t\t\t\tt.commission_user_id,\n" +
            "\t\t\t\t\t\tu.regionName,\n" +
            "\t\t\t\t\t\tu.organizationName,\n" +
            "\t\t\t\t\t\tu.orgCode,\n" +
            "\t\t\t\t\t\tt.policy_status\n" +
            "\t\t\t\t\tfrom sm_commission_detail t \n" +
            "\t\t\t\t\tleft join sm_plan p on t.plan_id=p.id\n" +
            "\t\t\t\t\tleft join auth_user u on t.commission_user_id=u.userId\n" +
            "\t\t\t\t\twhere p.productId=494\n" +
            "\t\t\t\t\tand orgCode is not null\n" +
            "\t\t\t\t\tand regionName!='总部' \n" +
            "\t\t\t\t\tand t.account_time >= str_to_date('2022-06-01', '%Y-%m-%d')\n" +
            "        \t\t\tand t.account_time < str_to_date('2022-07-01', '%Y-%m-%d') \n" +
            "\t\t\t\t\tand SUBSTRING_INDEX(t.order_id,'_',1) \n" +
            "\t\t\t\t\tin(\n" +
            "\t\t\t\t\t\tselect  `fhOrderId` \n" +
            "\t\t\t\t\t\t\t\tfrom sm_order b \n" +
            "\t\t\t\t\t\t\t\tleft join sm_commission_detail c on b.fhOrderId=c.order_id\n" +
            "\t\t\t\t\t\t\t\twhere \tb.productId = 494\n" +
            "\t\t\t\t\t\t\t\tand \tb.`payStatus` = 2\n" +
            "\t\t\t\t\t\t\t\tand \tcase when b.paymentTime=null then c.create_time else b.paymentTime end >= str_to_date('2022-06-01', '%Y-%m-%d')\n" +
            "\t\t\t\t\t\t\t\tand \tcase when b.paymentTime=null then c.create_time else b.paymentTime end < str_to_date('2022-07-01', '%Y-%m-%d')\n" +
            "\t\t\t\t\t\t\t\tand \tINSTR (b.fhOrderId ,'_') =0\n" +
            "\t\t\t\t\t)\n" +
            ") a left join system_organization_staff o \n" +
            "on a.organizationName=o.org_name and a.regionName=o.region_name and o.year=2022 and o.month=6\n" +
            "group by a.regionName,a.organizationName\n" +
            "        '''\n" +
            "    List<Map> list = msgStatService.selectMaps(sql) \n" +
            "    log.info(\"分支机构业绩数据:{}\",list)\n" +
            "    Map<String,Long> awardMap = new HashMap()\n" +
            "  \tfor(Map<String,Object> map:list){\n" +
            "  \t\tLong premium = (Long)map.get(\"premium\")\n" +
            "  \t\tInteger staff = (Long)map.get(\"staff\")\n" +
            "      \tString regionName =(String)map.get(\"regionName\")\n" +
            "      \tString orgName=(String)map.get(\"organizationName\")\n" +
            "      \tString key = orgName+\"-\"+regionName\n" +
            "      \tdouble avg=0\n" +
            "      \tif(staff!=null&&staff>0){\n" +
            "      \t\tavg = premium*1.0/staff\n" +
            "      \t}\n" +
            "      \tif(avg>=5){\n" +
            "      \t\tawardMap.put(key,5)\n" +
            "      \t}\n" +
            "    }\n" +
            "    log.info(\"分支机构加佣奖励:{}\",awardMap)\n" +
            "    return awardMap;\n" +
            "}";


    @GetMapping("/run/script")
    public Object runScript(){
        String ruleCode = "temp";
        String unionRuleCode = "20220613:tempScript";

        helper.cacheScript(script, unionRuleCode);
        return helper.executeForCache(unionRuleCode, null, "execute");
    }

    @Autowired
    private SystemActivityProgrammeJobHandler handler;

    @GetMapping("/run/job")
    public void runJob(){
        handler.execute();
    }
}
