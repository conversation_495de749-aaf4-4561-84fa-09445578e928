package com.cfpamf.ms.insur.operation.customer.service;

import com.cfpamf.common.ms.exception.MSBizNormalException;
import com.cfpamf.ms.insur.operation.customer.dao.CustomerInterruptionMapper;
import com.cfpamf.ms.insur.operation.customer.dto.CustomerInterruptionSmsDto;
import com.cfpamf.ms.insur.operation.customer.dto.CustomerPolicySimpleDto;
import com.cfpamf.ms.insur.operation.customer.po.CustomerInterruptionPo;
import com.cfpamf.ms.insur.operation.phoenix.service.PhoenixMessageService;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.stereotype.Service;

import javax.validation.Valid;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.BinaryOperator;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/5/8 16:58
 */
@Service
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class CustomerInterruptionService {

    CustomerInterruptionMapper customerInterruptionMapper;

    PhoenixMessageService messageService;

    public void sendSms2Applicant(@Valid CustomerInterruptionSmsDto dto) {
        messageService.sendMessage(dto.getApplicantPhone(),dto.getCustomerId(), dto.getInsuredPersonName(), dto.getRiskName(),
                dto.getUrl());
    }

    public void sendSms3Applicant(@Valid CustomerInterruptionSmsDto dto) {
        messageService.sendSms3Message(dto.getApplicantPhone(),dto.getCustomerId(), dto.getInsuredPersonName(), dto.getRiskName(),
                dto.getUrl());
    }

    public Map<String, CustomerPolicySimpleDto> getCustomerInterruption(Long customerId) {

        CustomerInterruptionPo po = new CustomerInterruptionPo();
        po.setCustomerId(customerId);
        CustomerInterruptionPo dbData = customerInterruptionMapper.selectOne(po);
        if (Objects.isNull(dbData)) {
            throw new MSBizNormalException("400", "断保客户信息不存在" + customerId);
        }

        List<CustomerPolicySimpleDto> customerPolicies = customerInterruptionMapper.selectByInsuredIdNumber(dbData.getIdNumber());

        return findNearByProduct(customerPolicies);
    }

    public Map<String, CustomerPolicySimpleDto> findNearByProduct(List<CustomerPolicySimpleDto> customerPolicies) {
        return customerPolicies.stream()
                .collect(Collectors.toMap(CustomerPolicySimpleDto::getProductType, policy -> policy,
                        BinaryOperator.maxBy(Comparator.comparing(CustomerPolicySimpleDto::getInterruptionTime))));
    }

    /**
     * 根据数仓表处理
     */
    public void initInterruption() {

        customerInterruptionMapper.insertInterruption();
    }
}
