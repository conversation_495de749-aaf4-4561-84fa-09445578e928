package com.cfpamf.ms.insur.operation.promotion.enums;

import com.cfpamf.common.ms.exception.MSBizNormalException;
import com.cfpamf.ms.insur.operation.base.exception.BusinessException;
import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;

@Getter
public enum MarketingOptSystemEnum {

    INSURE("insure","保险系统操作")

    ;
    private String systemCode;

    private String systemName;

    MarketingOptSystemEnum(String systemCode, String systemName) {
        this.systemCode = systemCode;
        this.systemName = systemName;
    }

    public static MarketingOptSystemEnum decode(String systemCode) {
        return Arrays.stream(MarketingOptSystemEnum.values())
                .filter(x -> Objects.equals(x.getSystemCode(), systemCode))
                .findFirst().orElseThrow(() -> new MSBizNormalException("", "系统类型不匹配"));
    }
}
