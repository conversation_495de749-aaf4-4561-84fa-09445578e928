package com.cfpamf.ms.insur.operation.order.dao;

import com.cfpamf.ms.insur.operation.base.dao.CommonMapper;
import com.cfpamf.ms.insur.operation.customer.dto.CustomerDto;
import com.cfpamf.ms.insur.operation.order.dto.SmOrderApplicant;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
@Mapper
public interface SmOrderApplicantMapper extends CommonMapper<SmOrderApplicant> {

    CustomerDto getIdNumberByOrderId(@Param("orderId")String fhOrderId);
}
