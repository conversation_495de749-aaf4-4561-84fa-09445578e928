package com.cfpamf.ms.insur.operation.order.dto;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 订单基础信息
 *
 * <AUTHOR>
 **/
@Data
public class SmBaseOrderVO {

    /**
     * 渠道
     */
    private String channel;

    /**
     * 订单Id
     */
    private String fhOrderId;

    /**
     * 产品Id
     */
    private Integer productId;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 产品属性：团险｜个险｜雇主险
     */
    private String productAttrCode;

    /**
     * 计划Id
     */
    private Integer planId;

    /**
     * 单价
     */
    private String qty;

    /**
     * 订单总金额
     */
    private BigDecimal totalAmount;

    /**
     * 提交时间
     */
    private Date submitTime;

    /**
     * 保险开始时间
     */
    private Date startTime;

    /**
     * 保险结束时间
     */
    private Date endTime;

    /**
     * 投保单号
     */
    private String appNo;

    /**
     * 保单号
     */
    private String policyNo;

    /**
     * 批改单号
     */
    private String endorsementNo;

    /**
     * 支付Id
     */
    private String payId;

    /**
     * 支付URL
     */
    private String payUrl;

    /**
     * 支付状态
     */
    private String payStatus;

    /**
     * 订单状态
     */
    private String orderState;

    /**
     * 支付时间
     */
    private Date paymentTime;

    /**
     * 订单创建时间
     */
    private Date createTime;

    /**
     * 子渠道 乡助微服务（xiangzhu）/CAPP（capp）
     */
    private String subChannel;

    /**
     * 客户信息是否已经抽取过
     */
    private Boolean extractFlag;

    /**
     * 订单提成Id
     */
    private Integer commissionId;

    private String recommendId;

    /**
     * 计划见费出单(seeFee)/非见费出单(nonSeefee)
     */
    private String orderOutType;

    @ApiModelProperty("第一个被保人的id")
    private Integer insuredId;

    @ApiModelProperty("订单类型【")
    private Integer orderType;

    @ApiModelProperty("原待续保订单编码")
    private String renewOrderId;

    @ApiModelProperty("微信openId")
    private String wxOpenId;




    /**
     * 管护负责人
     */
    private String customerAdminId;

    /**
     * 管护负责人
     */
    private String customerAdminMainJobNumber;

    /**
     * 管护负责人
     */
    private String customerAdminOrgCode;


    /**
     *
     */
    private String customerAdminJobCode;

    /**
     *
     */
    private String recommendOrgCode;

    /**
     *
     */
    private String recommendJobCode;

    /**
     *
     */
    private String recommendMainJobNumber;
}
