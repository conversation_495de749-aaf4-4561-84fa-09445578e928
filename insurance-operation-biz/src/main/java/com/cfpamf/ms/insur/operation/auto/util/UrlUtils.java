package com.cfpamf.ms.insur.operation.auto.util;

import cn.hutool.core.codec.Base64Encoder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;

@Slf4j
public class UrlUtils {

    private UrlUtils(){}

    /**
     * 处理重定向URL
     *
     * 该方法主要用于处理和格式化重定向的URL，确保用户在进行身份验证后能够返回正确的页面
     * 它通过对最终的重定向URL进行编码和格式化，以确保安全性和正确性
     *
     * @param backJumpUrl 微信登录验证URL，用于构建最终的跳转链接
     * @param backIndexUrl 返回页面的URL模板，用于在跳转链接中嵌入编码后的重定向URL和sId
     * @param sId 会话ID，用于标识特定的会话
     * @param state 请求的状态信息，目前未在本方法中使用 一般为空字符串
     * @param redirectUrl 原始的重定向URL，需要经过编码处理后才能嵌入到跳转链接中
     * @return 返回格式化并编码后的最终跳转URL
     */
    public static String redirectUrlHandle(String backJumpUrl,String backIndexUrl,String sId,String state,String redirectUrl){
        log.info("backJumpUrl= {}, backIndexUrl= {}, sId= {}, state= {}, redirectUrl= {}",backJumpUrl,backIndexUrl,sId,state,redirectUrl);
        String rdl = Base64Encoder.encode(redirectUrl.getBytes());
        String rdlEncodeString = null;
        try {
            rdlEncodeString = URLEncoder.encode(
                    rdl,
                    StandardCharsets.UTF_8.name()
            );
        } catch (UnsupportedEncodingException e) {
            //在编码过程中发生异常时记录错误日志
            log.error("redirectUrlHandle error redirectUrl= {}",redirectUrl,e);
        }
        //构建初始跳转地址，将Base64编码后的跳转地址和sId嵌入到模板中
        String realRedirectUrl = String.format(backIndexUrl, rdlEncodeString, sId);
        //用于存储URL编码后的跳转地址
        String realRedirectUrlEncodeString = null;
        //尝试对真实的跳转地址进行URL编码，以确保地址中的特殊字符被正确处理
        try {
            realRedirectUrlEncodeString = URLEncoder.encode(
                    realRedirectUrl,
                    StandardCharsets.UTF_8.name()
            );
        } catch (UnsupportedEncodingException e) {
            //在编码过程中发生异常时记录错误日志
            log.error("redirectUrlHandle error redirectUrl= {}",redirectUrl,e);
        }
        //最终构建跳转URL，将编码后的跳转地址和空字符串格式化到返回跳转URL模板中
        String finalUrl = String.format(backJumpUrl, realRedirectUrlEncodeString,state);
        log.info("redirectUrlHandle finalUrl= {}",finalUrl);
        return finalUrl;
    }

}
