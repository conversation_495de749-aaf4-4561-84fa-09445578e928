package com.cfpamf.ms.insur.operation.aicall.entity;

import com.cfpamf.ms.insur.operation.base.entity.BaseNoUserEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Table;
import java.io.Serializable;
import java.util.Date;

/**
 * ;
 * <AUTHOR> http://www.chiner.pro
 * @date : 2023-10-31
 */
@ApiModel(value = "",description = "")
@Table(name = "call_task")
@Data
public class CallTask extends BaseNoUserEntity implements Serializable,Cloneable{
    /** 任务名称 */
    @ApiModelProperty(name = "任务名称",notes = "")
    private String taskName ;

    /** 任务类型 */
    @ApiModelProperty(name = "任务类型",notes = "")
    private String taskType ;
    /** 任务批次号 */
    @ApiModelProperty(name = "任务批次号",notes = "")
    private String taskBatchCode ;
    /** Ai供应商提供者 */
    @ApiModelProperty(name = "Ai供应商提供者",notes = "")
    private String aiProvider ;
    /** 创建人 */
    @ApiModelProperty(name = "创建人",notes = "")
    private String createdBy ;
    /** 创建时间 */
    @ApiModelProperty(name = "创建时间",notes = "")
    private Date createdTime ;
    /** 更新人 */
    @ApiModelProperty(name = "更新人",notes = "")
    private String updatedBy ;
    /** 更新时间 */
    @ApiModelProperty(name = "更新时间",notes = "")
    private Date updatedTime ;

}