package com.cfpamf.ms.insur.operation.prospectus.form;

import java.math.BigDecimal;

import com.cfpamf.ms.insur.operation.prospectus.enums.PaymentPeriodUnitEnum;

import java.util.Objects;

import com.cfpamf.ms.insur.operation.prospectus.enums.GuaranteeUnitEnum;
import com.cfpamf.ms.insur.operation.prospectus.enums.InsuredAmountUnitEnum;

import com.cfpamf.ms.insur.operation.prospectus.enums.*;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 计划书表单
 *
 * <AUTHOR>
 */
@ApiModel(description = "计划书表单")
@Getter
@Setter
public class ProspectusForm {
    @ApiModelProperty("产品id")
    @NotNull(message = "产品id不能为空")
    private Long productId;

    @ApiModelProperty("计划")
    @NotNull(message = "计划不能为空")
    private Integer plan;

    @ApiModelProperty("计划书名称")
    @NotBlank(message = "计划书名称不能为空")
    private String name;

    @ApiModelProperty("被保人姓名")
    @NotBlank(message = "被保人姓名不能为空")
    private String insuredName;

    @ApiModelProperty("被保人年龄")
    @NotNull(message = "被保人年龄不能为空")
    @Min(value = 0, message = "年龄必须大于0")
    private Integer insuredAge;

    @ApiModelProperty("年龄单位")
    @NotNull(message = "年龄单位不能为空")
    private AgeUnitEnum ageUnit;

    @ApiModelProperty("被保人性别")
    @NotNull(message = "被保人性别不能为空")
    private GenderEnum insuredGender;

    @ApiModelProperty("被保人同投报人 0false  1true")
    @NotNull(message = "被保人同投报人标识不能为空")
    private Integer insuredIsApplicant;

    @ApiModelProperty("投报人姓名")
    private String applicantName;

    @ApiModelProperty("投报人年龄")
    @Min(value = 0, message = "年龄必须大于等于0")
    private Integer applicantAge;

    @ApiModelProperty("投报人性别")
    private GenderEnum applicantGender;

    @ApiModelProperty("保障期间单位")
    @NotNull(message = "保障期间单位不能为空")
    private GuaranteeUnitEnum guaranteeUnit;

    @ApiModelProperty("保障期间")
    @NotNull(message = "保障期间不能为空")
    private BigDecimal guaranteeQuantity;

    @ApiModelProperty("缴费周期单位")
    @NotNull(message = "缴费周期单位不能为空")
    private PaymentPeriodUnitEnum paymentPeriodUnit;

    @ApiModelProperty("缴费周期")
    @NotNull(message = "缴费周期不能为空")
    private BigDecimal paymentPeriodQuantity;

    @ApiModelProperty("保费单位")
    @NotNull(message = "保费单位不能为空")
    private InsuredAmountUnitEnum insuredAmountUnit;

    @ApiModelProperty("保费")
    @NotNull(message = "保费不能为空")
    private BigDecimal insuredAmountQuantity;

    @ApiModelProperty("客户名称")
    @NotBlank(message = "客户名称不能为空")
    private String customerName;

    @ApiModelProperty("客户性别")
    @NotNull(message = "客户性别不能为空")
    private GenderEnum customerGender;

    @ApiModelProperty("封面文件路径")
    @NotBlank(message = "封面文件路径不能为空")
    private String coverFilePath;

    @ApiModelProperty("是否展示讲解视频 0 false 1true")
    @NotNull(message = "是否展示讲解视频不能为空")
    private Integer showVideo;

    @ApiModelProperty("是否展示推荐理由  0 false 1true")
    @NotNull(message = "是否展示推荐理由不能为空")
    private Integer showRecommendationReason;

    /**
     * 校验投保人参数是否合法
     *
     * @return
     */
    public boolean applicantParamLegal() {
        return insuredIsApplicant == 0 && (StringUtils.isBlank(applicantName) || Objects.isNull(applicantAge) || Objects.isNull(applicantGender));
    }

    /**
     * 获取计划书计算表单
     *
     * @return
     */
    @JsonIgnore
    public ProspectusProductFactorForm getProspectusProductFactorForm() {
        ProspectusProductFactorForm prospectusProductFactorForm = new ProspectusProductFactorForm();
        BeanUtils.copyProperties(this, prospectusProductFactorForm);
        return prospectusProductFactorForm;
    }
}

