package com.cfpamf.ms.insur.operation.msg.service;

import com.cfpamf.ms.insur.operation.base.annotaions.DataSourceReadOnly;
import com.cfpamf.ms.insur.operation.base.constant.CacheKeyConstants;
import com.cfpamf.ms.insur.operation.msg.dao.OrderMapper;
import com.cfpamf.ms.insur.operation.msg.pojo.query.StataRankQuery;
import com.cfpamf.ms.insur.operation.msg.pojo.vo.StatRankVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.jdbc.core.ColumnMapRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> 2021/7/15 19:57
 */
@Service
@Slf4j
public class MsgStatService {

    @Autowired
    OrderMapper orderMapper;

    @Qualifier("safeJdbcTemplate")
    @Autowired
    JdbcTemplate jdbcTemplate;

    @Qualifier("dwJdbcTemplate")
    @Autowired
    JdbcTemplate dwTemplate;

    @Qualifier("dwminerJdbcTemplate")
    @Autowired
    JdbcTemplate dwMinerTemplate;


    @Qualifier("safespgJdbcTemplate")
    @Autowired
    JdbcTemplate safespgTemplate;

    @Qualifier("mpbusinessJdbcTemplate")
    @Autowired
    JdbcTemplate mpbusinessTemplate;


    /**
     * 根据产品统计
     *
     * @param query
     * @return
     */
    @DataSourceReadOnly
    public List<StatRankVO> selectRankByProduct(StataRankQuery query) {
        return orderMapper.selectRankByProduct(query);
    }

    /**
     * 直接查询sql
     *
     * @param sql
     * @return
     */
    @DataSourceReadOnly
    public List<Map<String, Object>> selectMaps(String sql) {
        log.info(sql);
        List<Map<String, Object>> maps = jdbcTemplate.queryForList(sql);
        log.info("res=>{}", maps.size());
        return maps;
    }

    /**
     * 直接查询sql
     *
     * @param setValueSql
     * @param querySql
     * @return
     */
    public List<Map<String, Object>> executeSql(String setValueSql, String querySql) {
        jdbcTemplate.execute(setValueSql);
        return jdbcTemplate.queryForList(querySql);
    }

    @DataSourceReadOnly
    public Map<String, Object> selectMap(String sql) {
        log.info(sql);
        Map<String, Object> res = jdbcTemplate.queryForMap(sql);
        log.info("res=>{}", res);
        return res;
    }


    public Map<String, Object> selectDwMap(String sql) {

        return dwTemplate.queryForMap(sql);
    }

    /**
     * 查询数仓数组
     *
     * @param sql
     * @return
     */
    public List<Map<String, Object>> selectDwMaps(String sql) {
        return dwTemplate.queryForList(sql);
    }

    /**
     * 缓存数据
     *
     * @param sql
     * @return
     */
    @Cacheable(cacheNames = CacheKeyConstants.MSG_QUERY_CACHE, key = "'DwMaps:'+#sql")
    public List<Map<String, Object>> selectDwMapsCache(String sql) {
        return dwTemplate.queryForList(sql);
    }

    public Map<String, Object> selectDwMinerMap(String sql) {
        Collection<Map<String, Object>> results = dwMinerTemplate.query(sql, new ColumnMapRowMapper());
        if (CollectionUtils.isEmpty(results)) {
            return null;
        } else {
            return results.iterator().next();
        }
    }

    /**
     * 查询数仓数组
     *
     * @param sql
     * @return
     */
    public List<Map<String, Object>> selectDwMinerMaps(String sql) {
        return dwMinerTemplate.queryForList(sql);
    }


    /**
     * 查询保险pg
     *
     * @param sql
     * @return
     */

    public Map<String, Object> selectSafesPgMap(String sql) {
        Collection<Map<String, Object>> results = safespgTemplate.query(sql, new ColumnMapRowMapper());
        if (CollectionUtils.isEmpty(results)) {
            return null;
        } else {
            return results.iterator().next();
        }
    }

    /**
     * 查询保险pg数组
     *
     * @param sql
     * @return
     */
    public List<Map<String, Object>> selectSafesPgMaps(String sql) {
        return safespgTemplate.queryForList(sql);
    }

    /**
     * 查询保险pg数组
     *
     * @param sql
     * @return
     */
    public List<Map<String, Object>> selectMpbusinessMaps(String sql) {
        return mpbusinessTemplate.queryForList(sql);
    }

}
