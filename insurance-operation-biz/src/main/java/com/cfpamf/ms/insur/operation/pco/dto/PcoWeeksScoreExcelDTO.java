package com.cfpamf.ms.insur.operation.pco.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import com.cfpamf.ms.insur.operation.pco.util.LocalDateTimeConverter;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * 2022-09-06
 * PCO周评分实体类
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
@ContentRowHeight(18)
@HeadRowHeight(20)
@ColumnWidth(30)
public class PcoWeeksScoreExcelDTO {

    /**
     * 区域名称
     */
    @ApiModelProperty("区域名称")
    @ExcelProperty("区域名称")
    private String regionName;

    /**
     * 分支
     */
    @ApiModelProperty("分支")
    @ExcelProperty("分支")
    private String orgName;

    /**
     * 员工名称
     */
    @ApiModelProperty("员工名称")
    @ExcelProperty("员工名称")
    private String userName;

    /**
     * 员工工号
     */
    @ApiModelProperty("员工工号")
    @ExcelProperty("员工工号")
    private String userId;

    /**
     * 开始时间
     */
    @ApiModelProperty("开始日期")
    @ExcelProperty(value="开始日期",converter = LocalDateTimeConverter.class)
    private LocalDateTime startDate;

    /**
     * 结束时间
     */
    @ApiModelProperty("结束日期")
    @ExcelProperty(value="结束日期",converter = LocalDateTimeConverter.class)
    private LocalDateTime endDate;

    /**
     * 周自评得分
     */
    @ApiModelProperty("周自评得分")
    @ExcelProperty("周自评得分")
    private Integer selfScore;

    /**
     * 调整后得分
     */
    @ApiModelProperty("调整后得分")
    @ExcelProperty("调整后得分")
    private Integer adjustScore;

    /**
     * 是否胜任
     */
    @ApiModelProperty("是否胜任 0-完全胜任 1-基本胜任 2-不胜任")
    @ExcelProperty("是否胜任")
    private String beQualified;

    /**
     * pco级别
     */
    @ApiModelProperty("PCO级别 C级 B级 A级 S级")
    @ExcelProperty("PCO级别")
    private String pcoLevel;

}
