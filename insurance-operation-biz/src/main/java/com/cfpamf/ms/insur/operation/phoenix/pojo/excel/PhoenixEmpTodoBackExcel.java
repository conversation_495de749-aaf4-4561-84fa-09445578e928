package com.cfpamf.ms.insur.operation.phoenix.pojo.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import com.cfpamf.common.ms.exception.MSBizNormalException;
import com.cfpamf.ms.insur.operation.pco.util.LocalDateTimeConverter;
import com.cfpamf.ms.insur.operation.phoenix.pojo.vo.PhoenixEmpTodoBackVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.beanutils.PropertyUtils;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
@ContentRowHeight(18)
@HeadRowHeight(20)
@ColumnWidth(20)
public class PhoenixEmpTodoBackExcel {


    @ExcelProperty("客户姓名")
    String customerName;

    @ExcelProperty(value = "是否报案")
    String claim;
    @ExcelProperty(value = "是否赔付")
    String finishClaim;
    @ExcelProperty(value = "生成任务时间" ,converter = LocalDateTimeConverter.class)
    @ApiModelProperty("生成任务时间")
    LocalDateTime createTime;

    @ExcelProperty(value = "计划完成时间" , converter = LocalDateTimeConverter.class)
    @ApiModelProperty("计划完成时间")
    LocalDateTime planTime;
    @ExcelProperty(value = "实际完成时间" , converter = LocalDateTimeConverter.class)
    @ApiModelProperty("实际完成时间")
    LocalDateTime finishTime;
    @ExcelProperty("任务类型")
    @ApiModelProperty("业务类型")
    String bizType1;

    /**
     *
     */
    @ExcelProperty("任务状态")
    @ApiModelProperty("任务状态")
    String state1;

    @ExcelProperty("管护人姓名")
    String customerAdminName;
    @ExcelProperty("区域")
    String regionName;
    @ExcelProperty("机构")
    String organizationName;
    @ExcelProperty("机器人意向")
    String intent;

    public void copyProperties(PhoenixEmpTodoBackVo source) {
        try {
            PropertyUtils.copyProperties(this, source);
        } catch (Exception e) {
            throw new MSBizNormalException("", "对象复制失败");
        }
    }
}
