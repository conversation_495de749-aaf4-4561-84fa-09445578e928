package com.cfpamf.ms.insur.operation.base.entity;

import lombok.Data;

import javax.persistence.*;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @Date 2021/12/14 17:02
 * @Version 1.0
 */
@Data
public class BaseIntegerEntity {
    /**
     * 创建人
     */
    @Column(name = "create_by")
    protected String createBy;

    /**
     * 上次更新人
     */
    @Column(name = "update_by")
    protected String updateBy;

    /**
     * 主键id
     */
    @Id
    @GeneratedValue(generator="JDBC")
    @OrderBy("desc")
    protected Integer id;

    /**
     * 是否删除
     */
    @Column(name = "enabled_flag")
    protected Integer enabledFlag = 0;
    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private LocalDateTime createTime;
    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private LocalDateTime updateTime;
}
