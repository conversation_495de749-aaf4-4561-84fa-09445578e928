package com.cfpamf.ms.insur.operation.phoenix.pojo.vo;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cfpamf.ms.insur.operation.aicall.dto.SessionRecordDto;
import com.cfpamf.ms.insur.operation.aicall.enums.AIIntentEnums;
import com.cfpamf.ms.insur.operation.phoenix.enums.EnumTodoBizType;
import com.cfpamf.ms.insur.operation.phoenix.enums.EnumTodoState;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2023/5/6 15:37
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class PhoenixEmpTodoVO {

    private static final String IG_FIELD = "idNumber";
    @ApiModelProperty("工号")
    String jobNumber;

    @ApiModelProperty("文案")
    String todoMsg;

    Long phoenixId;

    @ApiModelProperty("完成时间")
    LocalDateTime finishTime;

    @ApiModelProperty("保单生效时间")
    LocalDateTime startTime;

    @ApiModelProperty("保单失效时间")
    LocalDateTime invalidTime;

    @ApiModelProperty("保单续保开始时间")
    LocalDateTime renewalStartTime;

    @ApiModelProperty("保单续保结束时间")
    LocalDateTime renewalEndTime;

    @ApiModelProperty("同类型保单数量")
    Integer sameTypePolicyCount;

    @ApiModelProperty("保费")
    BigDecimal premium;

    @ApiModelProperty("长期险应缴时间")
    LocalDateTime dueTime;

    Integer graceDays;

    @ApiModelProperty("断保时间")
    LocalDateTime lastInterruptionTime;

    @ApiModelProperty("计划完成时间")
    LocalDateTime planTime;

    @ApiModelProperty("业务类型")
    EnumTodoBizType bizType;

    @ApiModelProperty("状态")
    EnumTodoState state;

    @ApiModelProperty("扩展属性")
    String todoProperty;

    @ApiModelProperty("备注:状态")
    String remark;

    String policyNo;

    @ApiModelProperty("机器人意向")
    String intent;

    @ApiModelProperty("机器人意向名称")
    String intentName;

    @ApiModelProperty("是否主营业务:0=非主营，1=主营")
    Integer isLoanFlag;

    @ApiModelProperty("是否在贷")
    Integer currentLoan;

    @ApiModelProperty(name = "语音地址")
    String voiceAddress;

    @ApiModelProperty(name = "通话时长")
    Integer talkingTimeLen;

    @ApiModelProperty("任务类型")
    String taskType;

    String recordJson;

    @ApiModelProperty(name = "语音对话信息")
    List<SessionRecordDto> sessionRecordDtos;

    @ApiModelProperty("是否理赔")
    Integer isClaim;

    @ApiModelProperty("是否赔付")
    Integer isFinishClaim;

    @ApiModelProperty("产品名称")
    String productName;

    @ApiModelProperty("续保可能性评估")
    String renewalIntentionEval;

    public String getIntentName() {
        if (!com.alibaba.druid.util.StringUtils.isEmpty(intent)) {
            return AIIntentEnums.dict(intent);
        }
        return "";
    }


    /**
     * 是否主营
     *
     * @return
     */
    public String getLoanFlag() {
        if (Objects.equals(isLoanFlag,0 )) {
            return "非主营";
        }
        return "主营";
    }
    public List<SessionRecordDto> getSessionRecordDtos() {
        if (!org.springframework.util.StringUtils.isEmpty(recordJson)) {
            return JSONObject.parseArray(recordJson, SessionRecordDto.class);
        }
        return null;
    }

    /**
     * 删除敏感字段
     *
     * @return
     */
    public String getTodoProperty() {
        if (StringUtils.isBlank(todoProperty)) {
            return todoProperty;
        }
        JSONObject jsonObject = JSON.parseObject(todoProperty);
        if (jsonObject.containsKey(IG_FIELD)) {
            jsonObject.remove(IG_FIELD);
        }
        return jsonObject.toJSONString();
    }



}
