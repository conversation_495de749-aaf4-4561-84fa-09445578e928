package com.cfpamf.ms.insur.operation.activity.dao;

import com.cfpamf.ms.insur.operation.activity.entity.SmOrderAddCommission;
import com.cfpamf.ms.insur.operation.activity.vo.OrderAddCommissionProportionVo;
import com.cfpamf.ms.insur.operation.base.dao.CommonMapper;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.entity.Example;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Mapper
public interface SmOrderAddCommissionMapper extends CommonMapper<SmOrderAddCommission> {

    /**
     * 通过数据id 和 加佣类型获取加佣集合
     *
     * @param dataId
     * @param type
     * @return
     */
    List<SmOrderAddCommission> getByDataIdAndType(@Param("dataId") String dataId, @Param("type") String type);


    /**
     * 通过数据id 和 加佣类型软删除加佣集合
     *
     * @param dataId
     * @param type
     * @return
     */
    void softDeleteByDataIdAndType(@Param("dataId") String dataId, @Param("type") String type);

    /**
     * 通过数据id 和 加佣类型删除加佣集合
     *
     * @param dataId
     * @param type
     * @return
     */
    void deleteByDataIdAndType(@Param("dataId") String dataId, @Param("type") String type);

    /**
     * 批量更新加佣活动奖励
     *
     * @param list
     */
    void batchUpdate(@Param("list") List<SmOrderAddCommission> list);

    /**
     * 订单更新加佣
     *
     * @param orderAddCommissionProportionList
     */
    void updateOrderAddCommissionProportion(@Param("orderAddCommissionProportionList") List<OrderAddCommissionProportionVo> orderAddCommissionProportionList);

    /**
     * 通过订单id集合查找订单加佣奖励
     *
     * @param fhOrderIdList
     * @return
     */
    default List<SmOrderAddCommission> getByFhOrderIdList(List<String> fhOrderIdList) {
        Example example = new Example(SmOrderAddCommission.class);
        example.createCriteria().andEqualTo("enabledFlag", 0).andIn("fhOrderId", fhOrderIdList);
        return selectByExample(example);
    }

    /**
     * 通过订单id集合查找订单加佣map
     *
     * @param fhOrderIdList
     * @return
     */
    default List<OrderAddCommissionProportionVo> getCommissionMapByFhOrderIdList(List<String> fhOrderIdList) {
        if (CollectionUtils.isEmpty(fhOrderIdList)) {
            return Lists.newArrayList();
        }
        Map<String, BigDecimal> map = getByFhOrderIdList(fhOrderIdList).stream()
                .collect(Collectors.toMap(SmOrderAddCommission::getFhOrderId, SmOrderAddCommission::getCommissionProportion, BigDecimal::add));

        return fhOrderIdList.stream()
                .map(fhOrderId -> {
                    BigDecimal addCommissionProportion = map.getOrDefault(fhOrderId, BigDecimal.ZERO);
                    OrderAddCommissionProportionVo orderAddCommissionProportionVo = new OrderAddCommissionProportionVo();
                    orderAddCommissionProportionVo.setFhOrderId(fhOrderId);
                    orderAddCommissionProportionVo.setAddCommissionProportion(addCommissionProportion);
                    return orderAddCommissionProportionVo;
                })
                .collect(Collectors.toList());


    }
}