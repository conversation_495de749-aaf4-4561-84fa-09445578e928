package com.cfpamf.ms.insur.operation.reward.addcommission.service;

import com.cfpamf.ms.insur.operation.auth.DataAuth;
import org.springframework.util.StringUtils;
import com.cfpamf.ms.insur.operation.base.constant.BaseConstants;
import com.cfpamf.ms.insur.operation.base.enums.EnumDictionaryType;
import com.cfpamf.ms.insur.operation.base.enums.EnumKeywordSearchType;
import com.cfpamf.ms.insur.operation.base.service.DataAuthService;
import com.cfpamf.ms.insur.operation.base.util.CommonUtil;
import com.cfpamf.ms.insur.operation.base.util.HttpRequestUtil;
import com.cfpamf.ms.insur.operation.customer.dto.WxInterruptionCustomerDto;
import com.cfpamf.ms.insur.operation.customer.service.DictionaryService;
import com.cfpamf.ms.insur.operation.order.dao.SmOrderItemMapper;
import com.cfpamf.ms.insur.operation.order.dto.SmOrderItem;
import com.cfpamf.ms.insur.operation.pco.dto.PcoWeeksScoreExcelDTO;
import com.cfpamf.ms.insur.operation.pco.enums.PcoLevelEnum;
import com.cfpamf.ms.insur.operation.pco.enums.PcoQualifiedlEnum;
import com.cfpamf.ms.insur.operation.pco.query.WeeksScoreListQuery;
import com.cfpamf.ms.insur.operation.pco.vo.PcoWeeksScoreVo;
import com.cfpamf.ms.insur.operation.reward.addcommission.dao.SmAddCommissionDetailMapper;
import com.cfpamf.ms.insur.operation.reward.addcommission.dto.SmAddCommissionListDto;
import com.cfpamf.ms.insur.operation.reward.addcommission.dto.SmAddCommissionListExcelDto;
import com.cfpamf.ms.insur.operation.reward.addcommission.entity.SmAddCommissionDetail;
import com.cfpamf.ms.insur.operation.reward.addcommission.query.SmAddCommissionQuery;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.function.Function;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.toMap;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class SmAddCommissionService {

    private DictionaryService dictionaryService;

    private SmOrderItemMapper smOrderItemMapper;

    private SmAddCommissionDetailMapper smAddCommissionDetailMapper;

    private DataAuthService dataAuthService;

    /**
     * 加佣列表查询
     * @param smAddCommissionQuery
     * @return
     */
    public List<SmAddCommissionListDto> listAddCommission(SmAddCommissionQuery smAddCommissionQuery){
        //1.初始化查询参数
        initOrderQuery(smAddCommissionQuery);
        List<SmAddCommissionListDto> smAddCommissionList = smAddCommissionDetailMapper.listAddCommission(smAddCommissionQuery);

        if (!CollectionUtils.isEmpty(smAddCommissionList)) {
            //设置加佣比例
            setOrderListCommissionProportion(smAddCommissionList);
            handleCode(smAddCommissionList);
            setEndorsementNo(smAddCommissionList);
        }

        return smAddCommissionList;
    }

    /**
     * 设置团险批减单的加佣比例（需找到原始承保单加佣比例做扣除）
     *
     * @param smAddCommissionList
     */
    private void setOrderListCommissionProportion(List<SmAddCommissionListDto> smAddCommissionList) {
        List<String> insuredIdNumbers = smAddCommissionList.stream()
                .map(SmAddCommissionListDto::getInsuredIdNumber)
                .distinct()
                .collect(Collectors.toList());
        List<SmAddCommissionDetail> commissionDetails = smAddCommissionDetailMapper.getByIdNumberList(insuredIdNumbers);

        //团险批减单处理佣金比例
        smAddCommissionList.forEach(smOrderListVO -> {
            BigDecimal addCommissionAmount = smOrderListVO.getAddCommissionAmount();
            if (addCommissionAmount != null && addCommissionAmount.compareTo(BigDecimal.ZERO) == 0
                    && Objects.equals(smOrderListVO.getAppStatus(), BaseConstants.POLICY_STATUS_CANCEL_SUCCESS)) {
                //团险批减单按实际加佣比例显示
                if (smOrderListVO.getCreateTime() != null) {
                    List<SmAddCommissionDetail> details = commissionDetails.stream().filter(x -> (x.getCreateTime().compareTo(LocalDateTime.ofInstant(smOrderListVO.getCreateTime().toInstant(), ZoneId.systemDefault())) < 0
                                    && x.getOrderId().split("_")[0].equals(smOrderListVO.getFhOrderId().split("_")[0])
                                    && x.getInsuredIdNumber().equals(smOrderListVO.getInsuredIdNumber())))
                            .collect(Collectors.toList())
                            .stream()
                            .sorted(Comparator.comparing(SmAddCommissionDetail::getCreateTime))
                            .limit(1).collect(Collectors.toList());
                    if (!CollectionUtils.isEmpty(details)) {
                        addCommissionAmount = smOrderListVO.getTotalAmount().multiply(details.get(0).getProportion()).divide(new BigDecimal(100), BigDecimal.ROUND_HALF_UP);
                    }
                }
            }

            smOrderListVO.setAddCommissionAmount(addCommissionAmount);
        });
    }

    /**
     * 初始化查询参数
     * @param query
     */
    public void initOrderQuery(SmAddCommissionQuery query) {
        DataAuth dataAuth = dataAuthService.dataAuthByToken(HttpRequestUtil.getToken());
        query.setRegionName(dataAuth.getRegionName());
        query.setRegionCode(dataAuth.getRegionCode());
        query.setOrgName(dataAuth.getOrgName());
        query.setOrgCode(dataAuth.getOrgCode());
        query.setUserId(dataAuth.getUserId());
        query.setCreateDateStart(CommonUtil.getStartTimeOfDay(query.getCreateDateStart()));
        query.setCreateDateEnd(CommonUtil.getEndTimeOfDay(query.getCreateDateEnd()));
        query.setAccountDateStart(CommonUtil.getStartTimeOfDay(query.getAccountDateStart()));
        query.setAccountDateEnd(CommonUtil.getEndTimeOfDay(query.getAccountDateEnd()));

        //处理 投保人查询条件
        if (org.apache.commons.lang3.StringUtils.isNotBlank(query.getApplicantName())) {
            //手机号码
            if (isPhone(query.getApplicantName())) {
                query.setApplicantType(EnumKeywordSearchType.CELLPHONE.getCode());
            }
            //证件号码
            else if (Pattern.matches("[\\w\\-]+", query.getApplicantName())) {
                query.setApplicantType(EnumKeywordSearchType.ID_CARD.getCode());
            } else {
                query.setApplicantType(EnumKeywordSearchType.NAME.getCode());
            }
        } else {
            query.setApplicantName(null);
        }
        //处理被保人查询条件
        if (org.apache.commons.lang3.StringUtils.isNotBlank(query.getInsuredName())) {
            //手机号码
            if (isPhone(query.getInsuredName())) {
                query.setInsuredType(EnumKeywordSearchType.CELLPHONE.getCode());
            }
            //证件号码
            else if (Pattern.matches("[\\w\\-]+", query.getInsuredName())) {
                query.setInsuredType(EnumKeywordSearchType.ID_CARD.getCode());
            } else {
                query.setInsuredType(EnumKeywordSearchType.NAME.getCode());
            }
        } else {
            query.setInsuredName(null);
        }
        if (StringUtils.isEmpty(query.getProductAttrCode())) {
            query.setProductAttrCode(null);
        }
    }

    /**
     * 校验是否为手机格式
     * @param str
     * @return
     */
    private boolean isPhone(String str) {
        return str.length() == 11 && Pattern.matches("[0-9]*", str);
    }

    /**
     * 码值处理
     *
     * @param orderVos
     */
    private void handleCode(List<SmAddCommissionListDto> orderVos) {
        Map<String, String> mapByType = dictionaryService.getMapByType(EnumDictionaryType.PRODUCT_GROUP.getCode());
        orderVos.forEach(o -> {
            if (org.apache.commons.lang3.StringUtils.isNotBlank(o.getProductType())) {
                o.setProductTypeName(mapByType.get(o.getProductType()));
            }
        });
    }

    private void setEndorsementNo(List<SmAddCommissionListDto> orderVos) {
        if (CollectionUtils.isEmpty(orderVos)) {
            return;
        }
        List<String> policyNoList = orderVos.stream().map(SmAddCommissionListDto::getPolicyNo).collect(Collectors.toList());
        List<SmOrderItem> orderItems = smOrderItemMapper.listByPolicyList(policyNoList);

        if (CollectionUtils.isEmpty(orderItems)) {
            return;
        }
        Map<String, SmOrderItem> policyMap = orderItems.stream().collect(toMap(SmOrderItem::getPolicyNo, Function.identity(), (x, y) -> y));

        orderVos.forEach(
                item -> {
                    if (Objects.nonNull(item.getPolicyNo()) && item.getPolicyNo().contains("_")) {
                        item.setEndorsementNo(item.getPolicyNo());
                        String originPolicyNo = Optional.ofNullable(policyMap.get(item.getPolicyNo())).map(SmOrderItem::getThPolicyNo).orElse(item.getPolicyNo());
                        item.setPolicyNo(
                                StringUtils.isEmpty(originPolicyNo) ? item.getPolicyNo() : originPolicyNo
                        );
                    }
                }
        );
    }

    /**
     * 短险加佣列表导出
     * @param query
     * @return
     */
    public List<SmAddCommissionListExcelDto> downloadAddCommissionList(SmAddCommissionQuery query){
        List<SmAddCommissionListDto> smAddCommissionList = listAddCommission(query);
        List<SmAddCommissionListExcelDto> excelDtos = smAddCommissionList.stream()
                .map(x->{
                    SmAddCommissionListExcelDto excelDTO = new SmAddCommissionListExcelDto();
                    BeanUtils.copyProperties(x, excelDTO);
                    return excelDTO;
                }).collect(Collectors.toList());
        return excelDtos;
    }
}
