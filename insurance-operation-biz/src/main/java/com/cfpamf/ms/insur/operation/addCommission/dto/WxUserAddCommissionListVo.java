package com.cfpamf.ms.insur.operation.addCommission.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
@ApiModel("微信端24版我的推广费加费列表")
public class WxUserAddCommissionListVo {
    /**
     * 业务记账时间
     */
    @ApiModelProperty("业务记账时间")
    private LocalDateTime businessAccountTime;
    /**
     * 合同号
     */
    @ApiModelProperty("合同号")
    private String contractCode;
    /**
     * 保单号
     */
    @ApiModelProperty("保单号")
    private String policyNo;
    /**
     * 险种编码
     */
    @ApiModelProperty("险种编码")
    private String productCode;
    /**
     * 产品名称
     */
    @ApiModelProperty("产品名称")
    private String productName;

    /**
     * 活动名称
     */
    @ApiModelProperty("活动名称")
    private String activityName;
    /**
     * 续期期数
     */
    @ApiModelProperty("续期期数")
    private Integer renewalPeriod;
    /**
     * 保单保费
     */
    @ApiModelProperty("保单保费")
    private BigDecimal policyPremium;
    /**
     * 佣金
     */
    @ApiModelProperty("加佣金额")
    private BigDecimal addCommissionAmount;
    /**
     * 投保人
     */
    @ApiModelProperty("投保人")
    private String applicantName;

    /**
     * 被保人
     */
    @ApiModelProperty("被保人")
    private String insuredName;
    /**
     * 长短险标志
     */
    @ApiModelProperty("长短险标志 0短险 1长险")
    private String longShortFlag;

    /**
     * 整村推进 0否 1是
     */
    @ApiModelProperty("整村推进 0否 1是")
    private String ruralProxyFlag;

    /**
     * 订单类型 0普通订单 1分销订单（是否分销单 0否 1是）
     */
    @ApiModelProperty("订单类型 0普通订单 1分销订单")
    private String distributionFlag;


}
