package com.cfpamf.ms.insur.operation.activity.controller;

import com.cfpamf.ms.insur.operation.activity.form.TakeBackRewardForm;
import com.cfpamf.ms.insur.operation.activity.service.SmActivityRewardService;
import com.cfpamf.ms.insur.operation.activity.temp.form.BrushNumberForm;
import com.cfpamf.ms.insur.operation.base.annotaions.ResponseDecorated;
import com.cfpamf.ms.insur.operation.base.constant.BaseConstants;
import com.cfpamf.ms.insur.operation.log.annotaions.SystemLog;
import com.cfpamf.ms.insur.operation.log.enums.LogActionType;
import com.cfpamf.ms.insur.operation.reward.service.impl.CouponDeliveryRewardServiceImpl;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * <AUTHOR>
 * @date 2022/3/7 10:39
 */
@Slf4j
@Api(value = "活动奖励管理接口", tags = {"活动奖励管理接口"})
@RequestMapping(value = {BaseConstants.ADMIN_VERSION + "/operation/activity/reward"})
@ResponseDecorated
@RestController
public class SmActivityRewardController {
    @Autowired
    private CouponDeliveryRewardServiceImpl couponDeliveryRewardService;
    @Autowired
    SmActivityRewardService smActivityRewardService;

    /**
     * 回收活动奖励
     *
     * @param takeBackRewardForm
     * @return
     */
    @ApiOperation("回收活动奖励")
    @PostMapping("/soft/takeBack")
    @SystemLog(descrption = "回收活动奖励", module = "活动奖励", actionType = LogActionType.DELETE)
    public void takeBackReward(@RequestBody TakeBackRewardForm takeBackRewardForm) {
        smActivityRewardService.takeBackReward(takeBackRewardForm);
    }

    /**
     * 删除活动奖励
     *
     * @param takeBackRewardForm
     * @return
     */
    @ApiOperation("删除活动奖励")
    @PostMapping("/takeBack")
    @SystemLog(descrption = "回收活动奖励", module = "活动奖励", actionType = LogActionType.DELETE)
    public void delete(@RequestBody TakeBackRewardForm takeBackRewardForm) {
        smActivityRewardService.delete(takeBackRewardForm);
    }


    /**
     * 发送失败的券触发补偿
     *
     * @param brushNumberForm
     */
    @PostMapping("/compensate/send_coupons")
    public void compensateSendCoupons(@RequestBody @Valid BrushNumberForm brushNumberForm) {
        couponDeliveryRewardService.compensateSendCoupons(brushNumberForm);
    }
}
