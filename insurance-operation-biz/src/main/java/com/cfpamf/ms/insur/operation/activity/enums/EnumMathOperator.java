package com.cfpamf.ms.insur.operation.activity.enums;

import com.alibaba.fastjson.JSONArray;
import com.cfpamf.ms.insur.operation.activity.exception.SystemActivityBusinessException;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.experimental.FieldDefaults;
import org.apache.commons.collections.ComparatorUtils;

import java.math.BigDecimal;
import java.util.*;

/**
 * <AUTHOR> 2022/6/29 11:28
 */
@AllArgsConstructor
@Getter
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public enum EnumMathOperator {

    /**
     * 大于等于
     */
    GT(">=") {
        @Override
        public boolean execute(Object params, Object actualVal) {

            return COMPARATOR.compare(actualVal, params) >= 0;
        }
    },
    /**
     * 大于
     */
    G(">") {
        @Override
        public boolean execute(Object params, Object actualVal) {

            return COMPARATOR.compare(actualVal, params) > 0;
        }
    },
    /**
     * 小于等于
     */
    LT("<=") {
        @Override
        public boolean execute(Object params, Object actualVal) {

            return COMPARATOR.compare(actualVal, params) <= 0;
        }
    },
    L("<") {
        @Override
        public boolean execute(Object params, Object actualVal) {

            return COMPARATOR.compare(actualVal, params) < 0;
        }
    },
    T("=") {
        @Override
        public boolean execute(Object params, Object actualVal) {

            return COMPARATOR.compare(actualVal, params) == 0;
        }
    },
    BETWEEN("between") {
        @Override
        public boolean execute(Object params, Object actualVal) {
            List<BigDecimal> bigDecimals = JSONArray.parseArray(params.toString(), BigDecimal.class);
            Iterator<BigDecimal> iterator = bigDecimals.iterator();
            return LT.execute(actualVal, iterator.next())
                    && GT.execute(actualVal, iterator.next());
        }
    };

    String expression;

    /**
     * 转换成BigDecimal再比较
     */
    private static final Comparator COMPARATOR = ComparatorUtils.transformedComparator(ComparatorUtils.naturalComparator(), a -> new BigDecimal(a.toString()));

    public static EnumMathOperator valueOfExpression(String expression) {
        return Arrays.stream(values()).filter(s -> s.expression.equals(expression))
                .findFirst().orElseThrow(() -> SystemActivityBusinessException.buildValidError("不支持的表达式:" + expression));
    }

    /**
     * 执行运输 返回判断结果
     *
     * @param params
     * @param actualVal
     */
    public abstract boolean execute(Object params, Object actualVal);
}
