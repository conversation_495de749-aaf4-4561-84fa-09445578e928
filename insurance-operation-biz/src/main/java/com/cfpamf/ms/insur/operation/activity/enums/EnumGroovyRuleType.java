package com.cfpamf.ms.insur.operation.activity.enums;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.experimental.FieldDefaults;

/**
 * <AUTHOR> 2022/6/28 17:23
 */
@Getter
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public enum EnumGroovyRuleType {

    /**
     * PCO 积分计算
     */
    PCO_FORM_SCOPE("PCO_FORM_SCOPE"),
    /**
     * 活动脚本
     */
    OPERATION_ACTIVI("OPERATION_ACTIVI"),
    /**
     * 活动手动配置脚本
     */
    OPERATION_ACTIVI_CONST("ACTIVI_CONST"),
    /**
     * 推送脚本
     */
    MSG("msg");

    String code;
}

