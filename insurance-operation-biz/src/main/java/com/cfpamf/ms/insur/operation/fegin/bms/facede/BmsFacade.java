package com.cfpamf.ms.insur.operation.fegin.bms.facede;


import com.cfpamf.common.ms.result.Result;
import com.cfpamf.ms.bms.facade.query.PostTimeWindowQuery;
import com.cfpamf.ms.bms.facade.vo.DingTalkUserJointBmsVO;
import com.cfpamf.ms.bms.facade.vo.FdSimpleUserVO;
import com.cfpamf.ms.bms.facade.vo.PostVO;
import com.cfpamf.ms.insur.operation.fegin.bms.constants.BmsConstantCore;
import com.cfpamf.ms.insur.operation.fegin.bms.request.FdOrganizationQuery;
import com.cfpamf.ms.insur.operation.fegin.bms.response.dto.ElementTreeNodeDTO;
import com.cfpamf.ms.insur.operation.fegin.bms.response.vo.*;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 */
@FeignClient(name = "bms-service-biz", url = "${bms.api.url}")
public interface BmsFacade {
    /**
     * 获取当前登录用户基本信息、任职信息、岗位和角色信息（保险专用 勿动）
     *
     * @param authorization
     * @return
     */
    @ApiOperation(value = "获取当前登录用户基本信息、任职信息、岗位和角色信息（保险专用 勿动）", notes = "/user/detail/safes")
    @GetMapping(value = "/user/detail/safes")
    Result<UserDetailVO> getContextUserDetailForSafes(@RequestHeader(BmsConstantCore.JWT_KEY_AUTH) String authorization);

    /**
     * 获取登录用户的按钮权限列表
     *
     * @param authorization
     * @param postIds
     * @param systemId
     * @return
     */
    @GetMapping(value = "/user/permissions")
    Result<List<ModuleVO>> getAuthPermissions(@RequestHeader(BmsConstantCore.JWT_KEY_AUTH) String authorization, @RequestParam("systemId") Integer systemId, @RequestParam("postIds") int[] postIds);

    /**
     * 获取区域分支树
     *
     * @param authorization
     * @param includeHeadOrg
     * @return
     */
    @ApiOperation(value = "获取区域分支树")
    @GetMapping(value = "/org/getAreaBranchTree")
    Result<List<ElementTreeNodeDTO>> getAreaBranchTree(@RequestHeader(BmsConstantCore.JWT_KEY_AUTH) String authorization, @RequestParam("includeHeadOrg") boolean includeHeadOrg);

    /**
     * 获取组织机构管理人员信息
     *
     * @param dto
     * @return
     */
    @ApiOperation("获取组织机构管理人员信息")
    @PostMapping(value = "/org/list/detail")
    Result<List<FdOrgEmployeeVO>> getOrganizationInfos(@RequestBody FdOrganizationQuery dto);

    /**
     * 获取区域分支树信息
     *
     * @param includeHeadOrg
     * @return
     */
    @GetMapping(value = "/org/regions")
    Result<List<FdOrgVO>> getAllRegionBranches(@RequestParam(value = "includeHeadOrg") boolean includeHeadOrg);

    /**
     * 获取整个组织机构树
     *
     * @param authorization
     * @return
     */
    @ApiOperation(value = "获取整个组织机构树", notes = "/org/getOrgTree")
    @GetMapping(value = "/org/getOrgTree")
    Result<List<ElementTreeNodeDTO>> getOrganizationTree(@RequestHeader(BmsConstantCore.JWT_KEY_AUTH) String authorization);

    /**
     * 根据hrOrgId获取组织机构详情
     *
     * @param hrOrgId
     * @param authorization
     * @return
     */
    @GetMapping(value = "/org/getOrganizationByHrOrgId/{hrOrgId}")
    Result<OrganizationVO> getOrganizationByHrOrgId(@RequestHeader(BmsConstantCore.JWT_KEY_AUTH) String authorization, @PathVariable(value = "hrOrgId") int hrOrgId);

    /**
     * 判断是否是区域办公室
     *
     * @param authorization
     * @param orgId
     * @return
     */
    @ApiOperation(value = "判断是否是区域办公室", notes = "/org/isAreaOffice?orgId=xxx")
    @GetMapping(value = "/org/isAreaOffice")
    Result<Boolean> checkOrgIsAreaOffice(@RequestHeader(BmsConstantCore.JWT_KEY_AUTH) String authorization, @RequestParam(value = "orgId") Integer orgId);

    /**
     * 根据orgCode获取当前机构信息,如果当前机构是区域管理部则返回对应的区域信息，其他不变
     *
     * @param orgCode
     * @return
     */
    @ApiOperation(value = "根据orgCode获取当前机构信息,如果当前机构是区域管理部则返回对应的区域信息，其他不变")
    @GetMapping(value = "/org/getBizOrgBaseVOByOrgCode")
    Result<OrganizationBaseVO> getBizOrgBaseVoByOrgCode(@RequestParam("orgCode") String orgCode);

    /**
     * 根据hrOrgId所对应的级别获取对应下的所有子孙的分支信息
     *
     * @param authorization
     * @param hrOrgIds
     * @return
     */
    @ApiOperation(value = "根据hrOrgId所对应的级别获取对应下的所有子孙的分支信息", notes = "/org/listBranchesByHrOrgIds?hrOrgIds=xxx,xxx")
    @GetMapping(value = "/org/listBranchesByHrOrgIds")
    Result<List<OrganizationBaseVO>> listBranchesByHrOrgIds(@RequestHeader(BmsConstantCore.JWT_KEY_AUTH) String authorization, @RequestParam(value = "hrOrgIds") List<Integer> hrOrgIds);

    /**
     * 根据hrParentId获取对应下的所有子孙的分支信息
     *
     * @param authorization
     * @param hrParentId
     * @return
     */
    @ApiOperation(value = "根据hrParentId获取对应下的所有子孙的分支信息", notes = "/org/{hrParentId}/branches")
    @GetMapping(value = "/org/{hrParentId}/branches")
    Result<List<OrganizationBaseVO>> listBranchesByHrParentId(@RequestHeader(BmsConstantCore.JWT_KEY_AUTH) String authorization, @PathVariable(value = "hrParentId") Integer hrParentId);

    @ApiOperation(value = "根据orgCode获取当前机构信息,如果当前机构是区域办公室/总部事业部则返回对应的区域信息/分支事业部，其他不变", notes = "/org/getBizOrgBaseVOByOrgCode?orgCode=xxx")
    @GetMapping(value = "/org/getBizOrgBaseVOByOrgCode")
    Result<OrganizationBaseVO> getBizOrgBaseVOByOrgCode(@RequestParam("orgCode") String orgCode);

    @ApiOperation(value = "获取岗位信息分页列表", notes = "/post/timewindow/search")
    @PostMapping(value = "/post/timewindow/search")
    Result<PageInfo<PostVO>> findPostTimeWindow(@RequestBody PostTimeWindowQuery query);

    @ApiOperation(value = "根据BMS中的userId列表查询用户在钉钉中的详细信息列表", notes = "/user/dingtalk/users/details/userIdList?userIdList=1,2,3")
    @GetMapping(value = "/user/dingtalk/users/details/userIdList")
    Result<List<DingTalkUserJointBmsVO>> listDingTalkUserJointBmsVOByBmsUserIdList(@RequestHeader(
            com.cfpamf.ms.bms.facade.constants.BmsConstantCore.JWT_KEY_AUTH) String authorization, @RequestParam("userIdList") List<Integer> userIdList);

    /**
     * 通过手机号查询用户职位信息
     */
    @GetMapping(value = "/user/jobPost")
    Result<List<FdSimpleUserVO>> getUserJobPost(@RequestParam("mobileNo") String mobileNo);





}
