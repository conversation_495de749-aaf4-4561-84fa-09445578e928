package com.cfpamf.ms.insur.operation.claim.job;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cfpamf.ms.insur.operation.aicall.AiCallProvider;
import com.cfpamf.ms.insur.operation.aicall.exceptions.CreateCallTaskException;
import com.cfpamf.ms.insur.operation.aicall.exceptions.UpdateCallTaskStatusException;
import com.cfpamf.ms.insur.operation.aicall.vo.AiCallTask;
import com.cfpamf.ms.insur.operation.aicall.vo.AiCallTaskDetail;
import com.cfpamf.ms.insur.operation.aicall.vo.CreateCallTaskReturn;
import com.cfpamf.ms.insur.operation.claim.dao.PolicyNoticeTaskDao;
import com.cfpamf.ms.insur.operation.claim.dto.PolicyNoticeTask;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;

@Slf4j
@Component
public class ClaimNoticeJob {
    @Autowired
    PolicyNoticeTaskDao policyNoticeTaskDao;
    @Autowired
    AiCallProvider aiCallProvider;

    @XxlJob("claim-notice")
    public void notice() {
        JSONObject jsonObject = Optional.ofNullable(JSONObject.parseObject(XxlJobHelper.getJobParam())).orElse(new JSONObject());
        log.info("当前任务参数：" + XxlJobHelper.getJobParam());
        XxlJobHelper.log("当前任务参数：" + XxlJobHelper.getJobParam());
        String pt = jsonObject.getString("pt");
        Integer limit = jsonObject.getInteger("limit");
        if (StringUtils.isBlank(pt) ||pt.length()!=8){
            //生成pt，去昨天日期，并格式化为yyyyMMdd，例如20250226
            pt = LocalDate.now().minusDays(1).format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        }
        if (limit == null){
            limit = 200;
        }
        log.info("当前任务日期 pt：" + pt);
        XxlJobHelper.log("当前任务日期 pt：" + pt);
        log.info("当前限制拨打任务数 limit：" + limit);
        XxlJobHelper.log("当前限制拨打任务数 limit：" + limit);
        List<PolicyNoticeTask> list = policyNoticeTaskDao.queryPolicyNoticeTaskList(pt,limit);

        List<AiCallTaskDetail> detailList = new ArrayList<>();
        for (int i = 0, j = 1; i < list.size(); i++, j++) {

            AiCallTaskDetail detail = new AiCallTaskDetail();
            Map<String,String> map = new HashMap<>();
            detail.setMobile(list.get(i).getApplicantMobile());
            detail.setOutSourceId(list.get(i).getId().intValue());
            detail.setExtJson(list.get(i).getPolicyNo());
            map.put("客户姓名",list.get(i).getApplicantName());
            map.put("日期",list.get(i).getRiskTime());
            map.put("出险类型",list.get(i).getRiskTypeDesc());
            detail.setVarMap(map);
            detailList.add(detail);
        }
        if (detailList.size() == 0){
            log.info("当前日期没有需要通知的保单");
            XxlJobHelper.log("当前日期没有需要通知的保单");
            return;
        }
        AiCallTask aiCallTask = new AiCallTask();
        aiCallTask.setTaskType("CLAIM_NOTICE");
        aiCallTask.setTaskName("投保语音通知");
        aiCallTask.setAiCallTaskDetailList(detailList);
        log.info("电话任务请求参数：" + JSON.toJSONString(aiCallTask));
        XxlJobHelper.log("电话任务请求参数：" + JSON.toJSONString(aiCallTask));
        try{
            CreateCallTaskReturn result = aiCallProvider.createAiCallTask(aiCallTask);
        }catch (Exception e){
            log.error("电话任务失败: " + e.getMessage());
        }

    }
}
