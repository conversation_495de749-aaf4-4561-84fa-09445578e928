package com.cfpamf.ms.insur.operation.honor.service;

import com.cfpamf.ms.insur.operation.honor.dao.HonorMetricConfigurationMapper;
import com.cfpamf.ms.insur.operation.honor.dto.HonorMetricConfigurationList;
import com.cfpamf.ms.insur.operation.honor.query.HonorMetricConfigurationQuery;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class HonorsMetricConfigurationService {

    HonorMetricConfigurationMapper honorMetricConfigurationMapper;

    /**
     * 获取荣誉规则配置分页列表
     * @return HonorRulesConfigurationList
     */
    public List<HonorMetricConfigurationList> getMetricConfigurationList(HonorMetricConfigurationQuery query) {
        return honorMetricConfigurationMapper.list(query);
    }

}
