package com.cfpamf.ms.insur.operation.job;

import com.cfpamf.ms.insur.operation.aitraining.service.ScrmService;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2023/4/28 13:53
 */
@Slf4j
@Component
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class ScrmJobHandler {


    ScrmService scrmService;

    /**
     * 推送每日
     */
    @XxlJob("send-ai-training-test-card")
    public void sendAiTrainingTestCard() {
        scrmService.sendCardMsg();
    }
}
