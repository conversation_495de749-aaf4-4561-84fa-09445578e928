package com.cfpamf.ms.insur.operation.call.feign.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

/**
 * 录音查询响应DTO
 *
 * <AUTHOR>
 * @date 2025-07-22
 */
@Data
@ApiModel("录音查询响应")
@FieldDefaults(level = AccessLevel.PRIVATE)
public class AudioQueryResponseDto {

    @ApiModelProperty(value = "录音响应数据", notes = "录音详细信息")
    @JsonProperty("TelecomAudioResponseDto")
    TelecomAudioResponse telecomAudioResponseDto;

    @ApiModelProperty(value = "数据", notes = "额外数据")
    Object data;

    @ApiModelProperty(value = "消息", example = "录音查询成功")
    String message;

    @ApiModelProperty(value = "是否成功", example = "true")
    Boolean success;

    @Data
    @ApiModel("录音响应详情")
    @FieldDefaults(level = AccessLevel.PRIVATE)
    public static class TelecomAudioResponse {
        @ApiModelProperty(value = "信息", example = "录音获取成功")
        @JsonProperty("Info")
        String info;

        @ApiModelProperty(value = "录音时长", example = "00:05:00")
        @JsonProperty("RecordDuration")
        String recordDuration;

        @ApiModelProperty(value = "录音时间", example = "2025-07-22 10:35:00")
        @JsonProperty("RecordTime")
        String recordTime;

        @ApiModelProperty(value = "录音地址", example = "http://example.com/record/123456.mp3")
        @JsonProperty("RecordUrl")
        String recordUrl;

        @ApiModelProperty(value = "结果", example = "SUCCESS")
        @JsonProperty("Result")
        String result;
    }
}
