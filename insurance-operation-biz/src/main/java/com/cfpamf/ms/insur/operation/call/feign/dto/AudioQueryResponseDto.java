package com.cfpamf.ms.insur.operation.call.feign.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 录音查询响应DTO
 *
 * <AUTHOR>
 * @date 2025-07-22
 */
@Data
public class AudioQueryResponseDto {

    @ApiModelProperty("录音响应数据")
    @JsonProperty("TelecomAudioResponseDto")
    private TelecomAudioResponse telecomAudioResponseDto;

    @ApiModelProperty("数据")
    private Object data;

    @ApiModelProperty("消息")
    private String message;

    @ApiModelProperty("是否成功")
    private Boolean success;

    @Data
    public static class TelecomAudioResponse {
        @ApiModelProperty("信息")
        @JsonProperty("Info")
        private String info;

        @ApiModelProperty("录音时长")
        @JsonProperty("RecordDuration")
        private String recordDuration;

        @ApiModelProperty("录音时间")
        @JsonProperty("RecordTime")
        private String recordTime;

        @ApiModelProperty("录音地址")
        @JsonProperty("RecordUrl")
        private String recordUrl;

        @ApiModelProperty("结果")
        @JsonProperty("Result")
        private String result;
    }
}
