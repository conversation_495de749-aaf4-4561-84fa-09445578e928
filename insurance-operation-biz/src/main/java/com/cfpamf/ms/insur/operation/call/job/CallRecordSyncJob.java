package com.cfpamf.ms.insur.operation.call.job;

import com.cfpamf.ms.insur.operation.call.service.CallFollowMatchService;
import com.cfpamf.ms.insur.operation.call.service.CallRecordSyncService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * 呼叫记录同步定时任务
 *
 * <AUTHOR>
 * @date 2025-07-22
 */
@Component
@Slf4j
@AllArgsConstructor
public class CallRecordSyncJob {

    private final CallRecordSyncService callRecordSyncService;
    private final CallFollowMatchService callFollowMatchService;

    /**
     * 同步呼叫记录信息
     * 每5分钟执行一次
     */
    @Scheduled(fixedRate = 5 * 60 * 1000)
    public void syncCallRecords() {
        try {
            log.info("开始执行呼叫记录同步定时任务");
            callRecordSyncService.syncCallRecords();
            log.info("呼叫记录同步定时任务执行完成");
        } catch (Exception e) {
            log.error("呼叫记录同步定时任务执行失败", e);
        }
    }

    /**
     * 匹配呼叫记录与跟进记录
     * 每10分钟执行一次
     */
    @Scheduled(fixedRate = 10 * 60 * 1000)
    public void matchCallWithFollowRecords() {
        try {
            log.info("开始执行呼叫记录匹配定时任务");
            callFollowMatchService.matchCallWithFollowRecords();
            log.info("呼叫记录匹配定时任务执行完成");
        } catch (Exception e) {
            log.error("呼叫记录匹配定时任务执行失败", e);
        }
    }
}
