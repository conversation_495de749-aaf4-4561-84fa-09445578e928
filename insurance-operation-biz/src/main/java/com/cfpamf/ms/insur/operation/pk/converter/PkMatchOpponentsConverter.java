package com.cfpamf.ms.insur.operation.pk.converter;

import com.cfpamf.ms.insur.operation.pk.pojo.dto.PkMatchOpponentsDto;
import com.cfpamf.ms.insur.operation.pk.pojo.resp.IndicatorsVO;
import com.cfpamf.ms.insur.operation.pk.pojo.resp.PkMatchOpponentsResponse;
import lombok.experimental.UtilityClass;

@UtilityClass
public class PkMatchOpponentsConverter {

    public static PkMatchOpponentsResponse convertToResponse(PkMatchOpponentsDto dto) {
        if (dto == null) {
            return null;
        }

        PkMatchOpponentsResponse response = new PkMatchOpponentsResponse();
        response.setType(dto.getType());
        response.setUserCode(dto.getUserCode());
        response.setBranchCode(dto.getBranchCode());
        response.setRegionCode(dto.getRegionCode());

        response.setUserName(dto.getUserName());
        response.setBranchName(dto.getBranchName());
        response.setRegionName(dto.getRegionName());
        // Assuming IndicatorsVO has a constructor or setters to set indicatorType and value
        IndicatorsVO indicatorsVO = new IndicatorsVO();
        indicatorsVO.setType(dto.getIndicatorType());
        indicatorsVO.setValue(dto.getValue());
        
        response.setRecipientIndicatorsVO(indicatorsVO);

        return response;
    }

    public static PkMatchOpponentsDto convertToDto(PkMatchOpponentsResponse response) {
        if (response == null) {
            return null;
        }

        PkMatchOpponentsDto dto = new PkMatchOpponentsDto();
        dto.setType(response.getType());
        dto.setUserCode(response.getUserCode());
        dto.setBranchCode(response.getBranchCode());
        dto.setRegionCode(response.getRegionCode());
        // Assuming you can get indicatorType and value from IndicatorsVO
        if(response.getRecipientIndicatorsVO() != null) {
            dto.setIndicatorType(response.getRecipientIndicatorsVO().getType());
            dto.setValue(response.getRecipientIndicatorsVO().getValue());
        }

        return dto;
    }
}
