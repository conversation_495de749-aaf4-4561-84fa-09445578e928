package com.cfpamf.ms.insur.operation.assistant.entity.safes;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 保险助手测算计算因子配置;
 * <AUTHOR> 赵小凯
 * @date : 2024-8-28
 */
@ApiModel(value = "保险助手测算计算因子配置",description = "")
@Data
public class AssistantCalcConfigParams implements Serializable,Cloneable{
    /** 主键 */
    @ApiModelProperty(name = "主键",notes = "")
    private Integer id ;
    /** 计算对象 */
    @ApiModelProperty(name = "计算对象",notes = "")
    private String calcObject ;
    /** 计算对象类型;三种类型area, bch, emp */
    @ApiModelProperty(name = "计算对象类型",notes = "三种类型area, bch, emp")
    private String calcObjectType ;
    /** 参数名称 */
    @ApiModelProperty(name = "参数名称",notes = "")
    private String calcParamName ;
    /** 参数编码 */
    @ApiModelProperty(name = "参数编码",notes = "")
    private String calcParamCode ;
    /** 参数值 */
    @ApiModelProperty(name = "参数值",notes = "")
    private Double calcParamValue ;
    /** 是否可用 */
    @ApiModelProperty(name = "是否可用",notes = "")
    private Integer enabledFlag ;
    /** 创建人 */
    @ApiModelProperty(name = "创建人",notes = "")
    private String createBy ;
    /** 创建时间 */
    @ApiModelProperty(name = "创建时间",notes = "")
    private Date createTime ;
    /** 更新人 */
    @ApiModelProperty(name = "更新人",notes = "")
    private String updateBy ;
    /** 更新时间 */
    @ApiModelProperty(name = "更新时间",notes = "")
    private Date updateTime ;

}