package com.cfpamf.ms.insur.operation.msg.pojo.po;

import com.cfpamf.ms.insur.operation.base.entity.BaseNoUserEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import javax.persistence.Column;
import javax.persistence.Table;
import java.math.BigInteger;

/**
 * <AUTHOR>
 */
@ApiModel
@Table(name = "ding_talk_user")
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class DingTalkUser extends BaseNoUserEntity {


    @ApiModelProperty("批次号")
    @Column(name = "batch_no")
    String batchNo;

    @ApiModelProperty("岗位名称")
    @Column(name = "post_name")
    String postName;

    @ApiModelProperty("用户名称")
    String userName;
    @ApiModelProperty("工号")
    @Column(name = "job_number")
    String jobNumber;

    @ApiModelProperty("手机号码")
    @Column(name = "mobile")
    String mobile;

    @ApiModelProperty("手机号码")
    @Column(name = "avatar")
    String avatar;

    @ApiModelProperty("dingTalkUserId 推送消息使用")
    @Column(name = "ding_talk_user_id")
    String dingTalkUserId;

    @ApiModelProperty("dingId")
    @Column(name = "ding_id")
    String dingId;

    @ApiModelProperty("dingUserId")
    @Column(name = "ding_user_id")
    BigInteger dingUserId;

}
