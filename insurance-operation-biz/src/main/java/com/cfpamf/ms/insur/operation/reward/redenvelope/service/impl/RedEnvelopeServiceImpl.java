package com.cfpamf.ms.insur.operation.reward.redenvelope.service.impl;

import com.alibaba.fastjson.JSON;
import com.cfpamf.common.ms.exception.MSBizNormalException;
import com.cfpamf.common.ms.exception.MSException;
import com.cfpamf.ms.bms.facade.exception.BizNormalException;
import com.cfpamf.ms.insur.operation.activity.dao.SmCommissionDetailMapper;
import com.cfpamf.ms.insur.operation.activity.dao.SystemActivityProductMapper;
import com.cfpamf.ms.insur.operation.activity.dao.SystemActivityProgrammeMapper;
import com.cfpamf.ms.insur.operation.activity.dto.RedEnvelopCommissionDetailDTO;
import com.cfpamf.ms.insur.operation.activity.entity.SystemActivityProgramme;
import com.cfpamf.ms.insur.operation.activity.listener.GrantCouponsOrderMessage;
import com.cfpamf.ms.insur.operation.activity.service.SmActivityRewardService;
import com.cfpamf.ms.insur.operation.activity.vo.SystemActivityProductDetailVo;
import com.cfpamf.ms.insur.operation.base.constant.BusinessConstants;
import com.cfpamf.ms.insur.operation.base.constant.CharConstants;
import com.cfpamf.ms.insur.operation.base.constant.RedEnvelopeConstants;
import com.cfpamf.ms.insur.operation.base.helper.BmsHelper;
import com.cfpamf.ms.insur.operation.base.service.RedisLockTemplate;
import com.cfpamf.ms.insur.operation.msg.dao.OrderMapper;
import com.cfpamf.ms.insur.operation.pco.entity.Dictionary;
import com.cfpamf.ms.insur.operation.reward.redenvelope.dao.SystemRedEnvelopeMapper;
import com.cfpamf.ms.insur.operation.reward.redenvelope.helper.RedEnvelopePool;
import com.cfpamf.ms.insur.operation.reward.redenvelope.po.entity.SystemRedEnvelope;
import com.cfpamf.ms.insur.operation.reward.redenvelope.po.vo.RedEnvelopeOrderItemVO;
import com.cfpamf.ms.insur.operation.reward.redenvelope.po.vo.RedEnvelopeVo;
import com.cfpamf.ms.insur.operation.reward.redenvelope.service.RedEnvelopeService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/1/6 17:23
 */
@Slf4j
@Service
public class RedEnvelopeServiceImpl implements RedEnvelopeService {
    @Autowired
    SystemRedEnvelopeMapper systemRedEnvelopeMapper;
    @Autowired
    RedisLockTemplate redisLockTemplate;
    @Autowired
    SystemActivityProgrammeMapper systemActivityProgrammeMapper;
    @Value("${redenvelope.gracePeriod}")
    private Long redEnvelopeGracePeriodDay = 0L;
    @Autowired
    RedEnvelopePool redEnvelopePool;
    @Autowired
    BmsHelper bmsHelper;
    @Autowired
    SystemActivityProductMapper systemActivityProductMapper;
    @Autowired
    SmActivityRewardService smActivityRewardService;
    @Autowired
    OrderMapper orderMapper;

    @Autowired
    SmCommissionDetailMapper smCommissionDetailMapper;

    @Override
    public List<RedEnvelopeVo> getRedEnvelope(Long saId, String userId) {
        if (Objects.isNull(saId)) {
            log.info("活动id去bms中获取");
            saId = getBmsSaId();
        }
        if (Objects.isNull(saId)) {
            return Lists.newArrayList();
        }

        //获取红包对应订单信息
        List<RedEnvelopeOrderItemVO> redEnvelopeOrderItemVos = systemRedEnvelopeMapper.getOrderBySaIdAndUserId(saId,userId);

        Map<String,List<RedEnvelopeOrderItemVO>> map = systemRedEnvelopeMapper.getCancelOrderItem().stream().collect(
                Collectors.groupingBy(RedEnvelopeOrderItemVO::getThPolicyNo));

        List<String> dataIds = new ArrayList<>();

        //记录有退保记录的订单
        for (RedEnvelopeOrderItemVO redEnvelopeOrderItemVO:redEnvelopeOrderItemVos){
            List<RedEnvelopeOrderItemVO> redEnvelopeOrderItemVoList = map.get(redEnvelopeOrderItemVO.getThPolicyNo());
            if (CollectionUtils.isNotEmpty(redEnvelopeOrderItemVoList)){
                if (redEnvelopeOrderItemVoList.stream().anyMatch(
                        x->x.getIdNumber().equals(redEnvelopeOrderItemVO.getIdNumber())
                                && x.getOrderIdIndex().compareTo(redEnvelopeOrderItemVO.getOrderIdIndex())>0
                                && x.getCreateTime().isAfter(redEnvelopeOrderItemVO.getCreateTime()))){
                    dataIds.add(redEnvelopeOrderItemVO.getFhOrderId());
                }
            }
        }

        //过滤有退保记录的订单
        List<SystemRedEnvelope> redEnvelopeList = systemRedEnvelopeMapper.getBySaIdAndUserId(saId, userId)
                .stream().filter(x->!dataIds.contains(x.getDataId())).collect(Collectors.toList());

        return redEnvelopeList.stream()
                .map(this::convertToRedEnvelopeVo)
                .collect(Collectors.toList());
    }

    /**
     * 去bms获取红包活动id配置
     *
     * @return
     */
    private Long getBmsSaId() {
        Dictionary redEnvelopeConfig = systemRedEnvelopeMapper.selectConfigs(BusinessConstants.BMS_DICTIONARY_RED_ENVELOPE_SA_ID, BusinessConstants.SA_ID);
        if (Objects.isNull(redEnvelopeConfig)) {
            log.info("暂无红包id活动配置!");
            return null;
        }
        return Long.valueOf(redEnvelopeConfig.getName());
    }

    /**
     * 实体转vo
     *
     * @param systemRedEnvelope
     * @return
     */
    private RedEnvelopeVo convertToRedEnvelopeVo(SystemRedEnvelope systemRedEnvelope) {
        if (Objects.isNull(systemRedEnvelope)) {
            return null;
        }
        RedEnvelopeVo redEnvelopeVo = new RedEnvelopeVo();
        BeanUtils.copyProperties(systemRedEnvelope, redEnvelopeVo);
        return redEnvelopeVo;
    }


    @Override
    public RedEnvelopeVo detail(Long id, String userId) {
        SystemRedEnvelope systemRedEnvelope = systemRedEnvelopeMapper.selectByPrimaryKey(id);
        //如果红包为空或者活动所属用户不属于当前用户id 返回空
        if (Objects.isNull(systemRedEnvelope) || !Objects.equals(systemRedEnvelope.getUserId(), userId)) {
            return null;
        }
        return convertToRedEnvelopeVo(systemRedEnvelope);
    }

    @Override
    public RedEnvelopeVo openRedEnvelop(Long id, String userId) {
        log.info("拆红包 start userId:{},id:{}", userId, id);
        SystemRedEnvelope systemRedEnvelope = systemRedEnvelopeMapper.selectByPrimaryKey(id);
        //如果红包为空或者活动所属用户不属于当前用户id 返回空
        if (Objects.isNull(systemRedEnvelope) || !Objects.equals(systemRedEnvelope.getUserId(), userId)) {
            throw new MSException("", "非法闯入请求");
        }
        //判断红包是否已经拆除
        if (Objects.equals(systemRedEnvelope.getSendState(), RedEnvelopeConstants.OPEN_STATE)) {
            log.warn("红包id{}，已经拆除。", id);
            return convertToRedEnvelopeVo(systemRedEnvelope);
        }
        //判断红包活动是否还存在
        Long saId = systemRedEnvelope.getSaId();
        SystemActivityProgramme systemActivityProgramme = systemActivityProgrammeMapper.selectByPrimaryKey(saId);
        if (Objects.isNull(systemActivityProgramme)) {
            throw new MSBizNormalException("", "红包活动已不存在！");
        }
        //判断红包是否已经失效
        Dictionary dictionary = systemRedEnvelopeMapper.selectConfigs(BusinessConstants.RED_ENVELOPE_DEADLINE,BusinessConstants.DEADLINE);
        if (Objects.isNull(dictionary)){
            throw new MSBizNormalException("", "红包失效日期配置出错!");
        }
        LocalDateTime deadLine = LocalDateTime.parse(dictionary.getName());
        if (LocalDateTime.now().isAfter(deadLine)) {
            throw new MSBizNormalException("", "活动已结束，红包已失效");
        }

        String openRedEnvelopLockKey = getOpenRedEnvelopLockKey(userId, id);
        boolean lock = redisLockTemplate.lock(openRedEnvelopLockKey, 3L, "该红包正在打开中，请稍后重试。");
        if (lock) {
            try {
                BigDecimal redEnvelopeAmount = redEnvelopePool.getRedEnvelopeAmount(systemRedEnvelope);
                systemRedEnvelopeMapper.updateAmountAndStateByIdAndState(id, RedEnvelopeConstants.NOT_OPEN_STATE, redEnvelopeAmount);
            } catch (BizNormalException normalException) {
                throw normalException;
            } catch (Exception e) {
                log.error("拆红包出错了，userId:{},id:{}", userId, id, e);
                throw new MSException("", "拆红包出错了", e);
            } finally {
                redisLockTemplate.unLock(openRedEnvelopLockKey);
            }
        }
        SystemRedEnvelope newestSystemRedEnvelope = systemRedEnvelopeMapper.selectByPrimaryKey(id);
        return convertToRedEnvelopeVo(newestSystemRedEnvelope);
    }

    /**
     * 获取拆红包的分布式锁key
     *
     * @param userId
     * @param id
     * @return
     */
    public String getOpenRedEnvelopLockKey(String userId, Long id) {
        return RedEnvelopeConstants.OPEN_RED_ENVELOPE_LOCK__KEY_PREFIX + CharConstants.LINE + userId + CharConstants.LINE + id;
    }

    @Override
    public void triggerCalculation(String orderId, List<String> orderIdList) {
        Long saId = getBmsSaId();
        Map<Long, List<SystemActivityProductDetailVo>> systemActivityMap = systemActivityProductMapper.getById(saId.intValue())
                .stream()
                .collect(Collectors.groupingBy(SystemActivityProductDetailVo::getSaId));
        //单个订单计算
        GrantCouponsOrderMessage couponsOrderMessage = orderMapper.getOrderMessage(orderId);
        if (Objects.nonNull(couponsOrderMessage)) {
            log.info("手动触发单个订单红包计算");
            smActivityRewardService.handlerMessageActivity(couponsOrderMessage, systemActivityMap);
        }
        //批量触发订单红包计算
        if (CollectionUtils.isNotEmpty(orderIdList)) {
            List<GrantCouponsOrderMessage> orderMessageList = orderMapper.getOrderMessageList(orderIdList);
            if (CollectionUtils.isNotEmpty(orderMessageList)) {
                log.info("手动触发多个订单红包计算");
                for (GrantCouponsOrderMessage grantCouponsOrderMessage : orderMessageList) {
                    smActivityRewardService.handlerMessageActivity(grantCouponsOrderMessage, systemActivityMap);
                }
            }
        }
    }

    @Override
    public List<String> sendRedEnvelop(GrantCouponsOrderMessage couponsOrderMessage) {
        String orderId = couponsOrderMessage.getFhOrderId();
        //查询当前订单是否为退保
        String appStatus = orderMapper.getOrderStatus(orderId);
        String status = "4";
        BigDecimal upper = new BigDecimal(5000);
        BigDecimal floor = new BigDecimal(200);

        List<String> orderIds = new ArrayList<>();
        if (StringUtils.isNotEmpty(appStatus) && appStatus.equals(status)){
            List<RedEnvelopCommissionDetailDTO> items = smCommissionDetailMapper.selectByOrderId(orderId);

            //新契约、批增数据
            List<RedEnvelopCommissionDetailDTO> underwriteItemLists = items.stream()
                    .filter(
                            x -> !x.getPolicyStatus().equals(status)
                    ).collect(Collectors.toList());
            Map<String,List<RedEnvelopCommissionDetailDTO>> map = underwriteItemLists
                    .stream().collect(Collectors.groupingBy(RedEnvelopCommissionDetailDTO::getOrderId));
            for (String key:map.keySet()){
                List<RedEnvelopCommissionDetailDTO> underwriteItems = map.get(key);
                BigDecimal totalAmount = BigDecimal.ZERO;
                for (RedEnvelopCommissionDetailDTO smOrderItem:underwriteItems) {
                    List<RedEnvelopCommissionDetailDTO> refundItems = items.stream().filter(x -> x.getPolicyStatus().equals(status)
                            && x.getIdNumber().equals(smOrderItem.getIdNumber())
                            && x.getOrderIdIndex()> smOrderItem.getOrderIdIndex()).collect(Collectors.toList());


                    if (CollectionUtils.isNotEmpty(refundItems)) {
                        List<RedEnvelopCommissionDetailDTO> latestRefundItems = refundItems
                                .stream().sorted(Comparator.comparing(RedEnvelopCommissionDetailDTO::getOrderIdIndex))
                                .limit(1).collect(Collectors.toList());
                        totalAmount = totalAmount.add(smOrderItem.getTotalAmount().subtract(latestRefundItems.get(0).getTotalAmount()));
                    }else{
                        totalAmount = totalAmount.add(smOrderItem.getTotalAmount());
                    }
                }
                if (totalAmount.compareTo(floor) >= 0 && totalAmount.compareTo(upper)<0){
                    orderIds.add(key);
                }
            }
        }
        log.info("订单数据：{}", JSON.toJSONString(orderIds));
        return orderIds;

    }


}
