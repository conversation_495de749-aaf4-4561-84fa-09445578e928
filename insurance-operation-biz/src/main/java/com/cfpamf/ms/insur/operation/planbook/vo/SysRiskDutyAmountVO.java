package com.cfpamf.ms.insur.operation.planbook.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2021/12/8 15:07
 * @Version 1.0
 */
@Data
public class SysRiskDutyAmountVO {

    /**
     * 字段名称 版本号
     */
    @ApiModelProperty(value = "责任id", required = true)
    Integer riskDutyId;
    /**
     * 字段名称 规则参数 {\"baseAmount\":1000}
     */
    @ApiModelProperty(value = "规则参数 {\"baseAmount\":1000}")
    String params;
    /**
     * 字段名称 规则  预留
     */
    @ApiModelProperty(value = "规则  预留")
    String rule;
    /**
     * 字段名称 类型 1-数值 2-区间 3-公式
     */
    @ApiModelProperty(value = "类型 1-数值 2-区间 3-公式", required = true)
    Integer amountType;
    /**
     * 字段名称 计算值
     */
    @ApiModelProperty(value = "计算值")
    String amount;
    /**
     * 字段名称 显示值
     */
    @ApiModelProperty(value = "显示值")
    String amountNotice;
}
