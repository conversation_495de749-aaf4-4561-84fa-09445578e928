package com.cfpamf.ms.insur.operation.whale.model;

import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2023/6/25 9:36 上午
 * @Version 1.0
 */
@FieldDefaults(level = AccessLevel.PRIVATE)
@Data
public class GroupInsuredInfo extends InsuredBaseInfo{


    /**
     * 连带被保险人列表
     */
    private List<GroupRelatedInsuredInfo> relatedInsuredList;
}
