package com.cfpamf.ms.insur.operation.whale.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;


/**
 * 小鲸对象
 *
 * <AUTHOR>
 */
@FieldDefaults(level = AccessLevel.PRIVATE)
@Data
public class WhaleContract {

    /**
     * 投保人信息
     */
    ApplicantInfo applicantInfo;

    /**
     * 渠道信息
     */
    ChannelInfo channelInfo;

    /**
     * 合同基础信息
     */
    ContractBaseInfo contractBaseInfo;

    ContractExtendInfo contractExtendInfo;

    @ApiModelProperty("[非团险]-被保人列表")
    List<InsuredInfoList> insuredInfoList;

    @ApiModelProperty("[团险]-被保人列表")
    List<GroupInsuredInfo> groupInsuredInfoList;

    String policyProductType;

    Integer policyType;

    List<ProductInfoList> productInfoList;

    /**
     * h5链接中自定义的回调参数
     */
    String customField;
    /**
     * 数据来源 10：保单在小鲸导入
     */
    private Integer dataSource;

    public BigDecimal sumAmount() {
        return insuredInfoList.stream()
                .map(InsuredInfoList::getProductInfoList)
                .flatMap(Collection::stream)
                .map(ProductInfoList::getCoverage)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    public BigDecimal sumPremium() {
        return insuredInfoList.stream()
                .map(InsuredInfoList::getProductInfoList)
                .flatMap(Collection::stream)
                .map(ProductInfoList::getPremium)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }


}
