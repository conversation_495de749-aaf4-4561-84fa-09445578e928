package com.cfpamf.ms.insur.operation.reward.addcommission.dao;

import com.cfpamf.ms.insur.operation.activity.vo.AddCommissionProportionVo;
import com.cfpamf.ms.insur.operation.base.dao.CommonMapper;
import com.cfpamf.ms.insur.operation.reward.addcommission.entity.SmAddCommissionDetailItem;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.entity.Example;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Mapper
public interface SmAddCommissionDetailItemMapper extends CommonMapper<SmAddCommissionDetailItem> {

    /**
     * 通过dataid和类型查找
     *
     * @param dataId
     * @param type
     * @return
     */
    default List<SmAddCommissionDetailItem> getByDataIdAndType(String dataId, String type) {
        Example example = new Example(SmAddCommissionDetailItem.class);
        example.createCriteria().andEqualTo("dataId", dataId)
                .andEqualTo("commissionType",type)
                .andEqualTo("enabledFlag", 0);
        return selectByExample(example);
    }

    /**
     * 批量更新加佣比例
     *
     * @param updateAddCommissionList
     */
    void batchUpdateProportion(List<SmAddCommissionDetailItem> updateAddCommissionList);

    /**
     * 通过uuid 集合获取加佣
     *
     * @param uuidList
     * @return
     */
    default List<SmAddCommissionDetailItem> getByUuIdList(List<String> uuidList) {
        if (CollectionUtils.isEmpty(uuidList)) {
            return Collections.emptyList();
        }
        Example example = new Example(SmAddCommissionDetailItem.class);
        example.createCriteria().andIn("uuid", uuidList).andEqualTo("enabledFlag", 0);
        return selectByExample(example);
    }
    /**
     * 通过uuid 集合获取加佣
     *
     * @param uuidList
     * @return
     */
    default List<SmAddCommissionDetailItem> getByUuIdListAndSaId(List<String> uuidList,Long saId) {
        if (CollectionUtils.isEmpty(uuidList)) {
            return Collections.emptyList();
        }
        Example example = new Example(SmAddCommissionDetailItem.class);
        example.createCriteria().andIn("uuid", uuidList)
                .andEqualTo("dataId",saId)
                .andEqualTo("enabledFlag", 0);

        return selectByExample(example);
    }

    /**
     * 软删除加佣
     *
     * @param dataId
     * @param type
     */
    void softDeleteByDataIdAndType(@Param("dataId") String dataId, @Param("type") String type);

    /**
     * 删除加佣
     * @param dataId
     * @param type
     */
    void deleteByDataIdAndType(@Param("dataId") String dataId, @Param("type") String type);

    /**
     * 通过uuid集合获取订单加佣
     * @param uuidList
     * @return
     */
   default List<AddCommissionProportionVo> getCommissionMapByFhOrderIdList(List<String> uuidList){
       if (CollectionUtils.isEmpty(uuidList)) {
           return Lists.newArrayList();
       }
       Map<String, BigDecimal> map = getByUuIdList(uuidList).stream()
               .collect(Collectors.toMap(SmAddCommissionDetailItem::getUuid, SmAddCommissionDetailItem::getProportion, BigDecimal::add));

       return uuidList.stream()
               .map(uuid -> {
                   BigDecimal addCommissionProportion = map.getOrDefault(uuid, BigDecimal.ZERO);
                   AddCommissionProportionVo addCommissionProportionVo = new AddCommissionProportionVo();
                   addCommissionProportionVo.setDataId(uuid);
                   addCommissionProportionVo.setAddCommissionProportion(addCommissionProportion);
                   return addCommissionProportionVo;
               })
               .collect(Collectors.toList());
   }

    int updateProportionByDataId(@Param("dataId") Long dataId, @Param("type") String type, @Param("propotrion") BigDecimal propotrion);
}
