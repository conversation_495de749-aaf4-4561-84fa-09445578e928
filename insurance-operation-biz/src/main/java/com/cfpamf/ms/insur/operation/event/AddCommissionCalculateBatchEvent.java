package com.cfpamf.ms.insur.operation.event;

import com.cfpamf.ms.insur.operation.base.event.BaseEvent;
import com.cfpamf.ms.insur.operation.reward.addcommission.entity.SmAddCommissionDetail;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Collection;

/**
 * 批量进行加佣金额计算事件
 *
 * <AUTHOR>
 * @date 2022/5/6 16:48
 */
@Getter
@AllArgsConstructor
public class AddCommissionCalculateBatchEvent implements BaseEvent {

    Collection<SmAddCommissionDetail> smAddCommissionDetailList;
}
