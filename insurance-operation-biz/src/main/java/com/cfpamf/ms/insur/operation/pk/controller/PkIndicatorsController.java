package com.cfpamf.ms.insur.operation.pk.controller;

import com.cfpamf.ms.insur.operation.base.annotaions.ResponseDecorated;
import com.cfpamf.ms.insur.operation.pk.pojo.req.IndicatorsRequest;
import com.cfpamf.ms.insur.operation.pk.pojo.req.PkMatchOpponentsRequest;
import com.cfpamf.ms.insur.operation.pk.pojo.resp.IndicatorsResponse;
import com.cfpamf.ms.insur.operation.pk.pojo.resp.PageResp;
import com.cfpamf.ms.insur.operation.pk.pojo.resp.PkMatchOpponentsResponse;
import com.cfpamf.ms.insur.operation.pk.service.PkIndicatorsQueryService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/8/15 16:32
 */
@RestController
@RequestMapping("pk-indicators")
@Api(tags = "PkIndicators 接口")
@AllArgsConstructor
@ResponseDecorated
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class PkIndicatorsController {
    PkIndicatorsQueryService pkIndicatorsQueryService;

    @PostMapping("/match-opponents")
    @ApiOperation(value = "Match Opponents", notes = "This endpoint is used to match opponents.")
    public PageResp<PkMatchOpponentsResponse> matchOpponents(
            @ApiParam(value = "PK Match Opponents Request object", required = true) @RequestBody PkMatchOpponentsRequest request) {
        return pkIndicatorsQueryService.matchOpponents(request);
    }

    @PostMapping("/history-indicators")
    @ApiOperation(value = "Query PK History Indicators", notes = "This endpoint is used to query PK history indicators.")
    public List<IndicatorsResponse> queryPkHistoryIndicators(
            @ApiParam(value = "Indicators Request object", required = true) @RequestBody IndicatorsRequest indicatorsRequest) {
        return pkIndicatorsQueryService.queryPkHistoryIndicators(indicatorsRequest);
    }

    @PostMapping("/real-time-indicators")
    @ApiOperation(value = "Query PK Real Time Indicators", notes = "This endpoint is used to query PK real-time indicators.")
    public List<IndicatorsResponse> queryPkRealTimeIndicators(
            @ApiParam(value = "Indicators Request object", required = true) @RequestBody IndicatorsRequest indicatorsRequest) {
        return pkIndicatorsQueryService.queryPkRealTimeIndicators(indicatorsRequest);
    }

}
