package com.cfpamf.ms.insur.operation.msg.pojo.vo;

import com.cfpamf.ms.insur.operation.msg.pojo.po.OpMessageRule;
import com.cfpamf.ms.insur.operation.msg.pojo.po.OpMessageRuleReceiver;
import lombok.AccessLevel;
import lombok.Data;
import lombok.ToString;
import lombok.experimental.FieldDefaults;

import java.util.List;

/**
 * <AUTHOR> 2021/8/9 11:44
 */
@Data
@ToString(callSuper = true)
@FieldDefaults(level = AccessLevel.PRIVATE)
public class OpMessageRuleVO extends OpMessageRule {

    List<OpMessageRuleReceiver> receiver;
}
