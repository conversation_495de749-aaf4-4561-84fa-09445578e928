package com.cfpamf.ms.insur.operation.pk.pojo.req;

import com.cfpamf.ms.insur.operation.pk.pojo.resp.IndicatorsVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 实时的业绩指标mq推送
 * <AUTHOR>
 */
@Data
public class RealIndicatorsMqVO {

	@ApiModelProperty(value = "员工名称")
    private String userName;

    @ApiModelProperty(value = "员工编码")
    private String userCode;

    @ApiModelProperty(value = "分支名称")
    private String branchName;

    @ApiModelProperty(value = "分支编码")
    private String branchCode;

    @ApiModelProperty(value = "指标")
    private IndicatorsVO indicatorsVO;

    @ApiModelProperty(value = "发生时间")
    private LocalDateTime time;

    @ApiModelProperty(value = "业务幂等")
    private String idempotent;
}
