package com.cfpamf.ms.insur.operation.dingtalk.service;

import com.cfpamf.ms.insur.operation.dingtalk.form.DingGroupConfigForm;
import com.github.pagehelper.PageInfo;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import com.cfpamf.ms.insur.operation.dingtalk.entity.DingGroupConfig;

import java.util.List;
import java.util.Map;

/**
 * ;(ding_group_config)表服务接口
 * <AUTHOR> http://www.chiner.pro
 * @date : 2023-9-14
 */
public interface DingGroupConfigService{
    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    DingGroupConfig queryById(Long id);
    /**
     * 分页查询
     *
     * @param dingGroupConfigForm 筛选条件
     * @return 查询结果
     */
    PageInfo<DingGroupConfig> paginQuery(DingGroupConfigForm dingGroupConfigForm);
    /**
     * 新增数据
     *
     * @param dingGroupConfig 实例对象
     * @return 实例对象
     */
    DingGroupConfig insert(DingGroupConfig dingGroupConfig);
    /**
     * 更新数据
     *
     * @param dingGroupConfig 实例对象
     * @return 实例对象
     */
    DingGroupConfig update(DingGroupConfig dingGroupConfig);
    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 是否成功
     */
    String deleteById(Long id) throws Exception;

    Map<String, Object> canDeleteById(Long id);
}