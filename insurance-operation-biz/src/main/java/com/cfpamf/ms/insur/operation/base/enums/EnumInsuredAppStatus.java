package com.cfpamf.ms.insur.operation.base.enums;

import com.cfpamf.ms.insur.operation.base.constant.BaseConstants;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.experimental.FieldDefaults;

import java.util.Objects;

/**
 * <AUTHOR> 2020/6/28 15:23
 */
@AllArgsConstructor
@Getter
@FieldDefaults(level = AccessLevel.PRIVATE)
public enum EnumInsuredAppStatus {
    /**
     * 退保成功
     */
    CANCEL_SUCCESS(BaseConstants.POLICY_STATUS_CANCEL_SUCCESS, "退保成功"),
    /**
     * 承保成功
     */
    SUCCESS(BaseConstants.POLICY_STATUS_SUCCESS, "承保成功"),
    /**
     * 处理中
     */
    PROCESS(BaseConstants.POLICY_STATUS_PROCESS, "处理中");
    final String code;

    final String desc;

    /**
     * 通过描述获取投保状态编码
     *
     * @return
     */
    public static String getCodeByDesc(String desc) {
        for (EnumInsuredAppStatus value : EnumInsuredAppStatus.values()) {
            if (Objects.equals(value.getDesc(), desc)) {
                return value.getCode();
            }
        }
        return null;
    }

    /**
     * 通过描述获取投保状态编码
     *
     * @return
     */
    public static String getDescByCode(String code) {
        for (EnumInsuredAppStatus value : EnumInsuredAppStatus.values()) {
            if (Objects.equals(value.getCode(), code)) {
                return value.getDesc();
            }
        }
        return null;
    }
}
