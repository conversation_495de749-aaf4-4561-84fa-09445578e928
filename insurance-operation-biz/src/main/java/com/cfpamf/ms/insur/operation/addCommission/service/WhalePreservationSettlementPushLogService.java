package com.cfpamf.ms.insur.operation.addCommission.service;

import com.alibaba.fastjson.JSONObject;
import com.cfpamf.ms.insur.operation.addCommission.dao.WhalePreservationSettlementPushLogMapper;
import com.cfpamf.ms.insur.operation.addCommission.dao.WhaleSettlementPushLogMapper;
import com.cfpamf.ms.insur.operation.addCommission.dto.PreservationDto;
import com.cfpamf.ms.insur.operation.addCommission.po.WhalePreservationSettlementPushLog;
import com.cfpamf.ms.insur.operation.addCommission.po.WhaleSettlementPushLog;
import com.cfpamf.ms.insur.operation.settlement.dto.BatchSyncNotifyDto;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class WhalePreservationSettlementPushLogService {

    WhalePreservationSettlementPushLogMapper whalePreservationSettlementPushLogMapper;

    public WhalePreservationSettlementPushLog insertPushLog(PreservationDto preservationDto) {
        WhalePreservationSettlementPushLog push = new WhalePreservationSettlementPushLog();
        push.setRequestId(preservationDto.getRequestId());
        push.setPushState(0);
        PreservationDto dto = new PreservationDto();
        BeanUtils.copyProperties(preservationDto, dto);
        dto.setContract(null);
        dto.setPreservationDetail(null);
        push.setRequestData(JSONObject.toJSONString(dto));
        log.info("保全事件推送记录：{}", push);
        whalePreservationSettlementPushLogMapper.insert(push);
        return push;
    }

    public void updatePushState(WhalePreservationSettlementPushLog pushLog) {
        whalePreservationSettlementPushLogMapper.updatePushState(pushLog);
    }
}
