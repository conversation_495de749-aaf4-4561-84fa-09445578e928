package com.cfpamf.ms.insur.operation.msg.service;

import com.alibaba.fastjson.JSON;
import com.cfpamf.ms.insur.operation.msg.dao.OpMessagePushMapper;
import com.cfpamf.ms.insur.operation.msg.enums.EnumMessagePushState;
import com.cfpamf.ms.insur.operation.msg.pojo.dto.OpMessageRuleGroovyDTO;
import com.cfpamf.ms.insur.operation.msg.pojo.po.OpMessagePush;
import com.cfpamf.ms.insur.operation.msg.pojo.po.OpMessageRuleReceiver;
import com.cfpamf.ms.insur.operation.msg.pojo.query.OpMessagePushQuery;
import com.cfpamf.ms.insur.operation.msg.pojo.vo.OpMessagePushVO;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.AccessLevel;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2021/9/2 16:36
 * @Version 1.0
 */
@Service
@FieldDefaults(level = AccessLevel.PRIVATE)
@Slf4j
public class OpMessagePushService {

    @Resource
    OpMessagePushMapper messagePushMapper;


    public PageInfo<OpMessagePushVO> pageQueryMessageReceiver(OpMessagePushQuery pushQuery) {
        //查询分页数据
        PageInfo<OpMessagePushVO> pageInfo = PageHelper.startPage(pushQuery.getPageNo(), pushQuery.getPageSize())
                .doSelectPageInfo(() -> messagePushMapper.selectPage(pushQuery));
        return pageInfo;
    }

    /**
     * 适用于实际不需要推送,写入不需要推送的数据避免下次再遇到推送
     * @param bizCode
     * @param ruleDto
     * @return
     */
    public boolean saveNoPushData(String bizCode, OpMessageRuleGroovyDTO ruleDto){
        log.info("!!!!!saveNoPushData lt= {}; ruleDto= {}", bizCode,JSON.toJSONString(ruleDto));
        if(StringUtils.isBlank(bizCode)){
            //todo

        }
        OpMessageRuleReceiver receiver = ruleDto.getReceiver();
        OpMessagePush push = new OpMessagePush();
        push.setMessageId(null);
        push.setContextId(null);
        push.setReceiver(receiver.getReceiverName());
        push.setReceiverType(receiver.getReceiverType());
        push.setReceiverName(receiver.getReceiverName());
        push.setMessageRuleId(receiver.getMessageRuleId());
        push.setContent(null);
        push.setMessageCache(null);
        push.setBizCode(bizCode);
        push.setRuleCode(ruleDto.getRuleCode());
        push.setState(EnumMessagePushState.TODO.getCode());
        return messagePushMapper.insertUseGeneratedKeys(push) > 0;
    }

}
