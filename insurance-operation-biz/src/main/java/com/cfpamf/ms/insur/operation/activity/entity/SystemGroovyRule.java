package com.cfpamf.ms.insur.operation.activity.entity;

import com.cfpamf.ms.insur.operation.base.entity.BaseEntity;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Table;

/**
 * system_groovy_rule
 * <AUTHOR>
@Data
@Table(name = "system_groovy_rule")
@NoArgsConstructor
public class
SystemGroovyRule  extends BaseEntity {

    /**
     * 类型 ACTIVITY:活动
     */
    private String type;

    /**
     * 规则名称
     */
    private String name;
    /**
     * 方法
     */
    private String method;

    /**
     * 代码
     */
    private String groovyCode;

    /**
     * 编码
     */
    private String code;

    /**
     * 描述
     */
    @Column(name = "`describe`")
    private String describe;

    public SystemGroovyRule(String type, String code) {
        this.type = type;
        this.code = code;
    }
}
