package com.cfpamf.ms.insur.operation.customer.convertor;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.cfpamf.ms.insur.operation.customer.dto.CustomerInterruptionFollowDto;
import com.cfpamf.ms.insur.operation.customer.po.CustomerInterruptionFollowPo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;


/**
 * <AUTHOR>
 */
@Mapper(imports = {JSONObject.class, JSONArray.class, JSON.class})
public interface InterruptionFollowConverter {
    InterruptionFollowConverter INS = Mappers.getMapper(InterruptionFollowConverter.class);


    /**
     * DTO转PO
     *
     * @param dto
     * @return
     */
    @Mappings({
            @Mapping(target = "newest", defaultValue = "1"),
            @Mapping(target = "enabledFlag", defaultValue = "0")
    })
    CustomerInterruptionFollowPo dtoToPo(CustomerInterruptionFollowDto dto);
}
