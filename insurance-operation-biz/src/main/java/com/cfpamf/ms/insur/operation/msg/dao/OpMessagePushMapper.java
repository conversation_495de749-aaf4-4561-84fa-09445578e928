package com.cfpamf.ms.insur.operation.msg.dao;

import com.cfpamf.ms.insur.operation.base.dao.CommonMapper;
import com.cfpamf.ms.insur.operation.msg.pojo.po.OpMessagePush;
import com.cfpamf.ms.insur.operation.msg.pojo.query.OpMessagePushQuery;
import com.cfpamf.ms.insur.operation.msg.pojo.vo.OpMessagePushVO;
import org.apache.ibatis.annotations.Mapper;
import tk.mybatis.mapper.common.IdsMapper;

import java.util.List;

/**
 * 消息推送记录
 *
 * <AUTHOR> 2021/7/7 13:45
 */
@Mapper
public interface OpMessagePushMapper extends CommonMapper<OpMessagePush>, IdsMapper<OpMessagePush> {

    /**
     * 分页查询
     *
     * @param params 查询参数
     * @return
     */
    List<OpMessagePushVO> selectPage(OpMessagePushQuery params);

}
