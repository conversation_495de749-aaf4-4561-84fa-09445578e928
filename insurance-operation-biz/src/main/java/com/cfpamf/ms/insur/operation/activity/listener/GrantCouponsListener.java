package com.cfpamf.ms.insur.operation.activity.listener;

import com.alibaba.fastjson.JSON;
import com.cfpamf.ms.insur.operation.activity.dao.AuthUserMapper;
import com.cfpamf.ms.insur.operation.activity.dao.OperationActivityOrderMessageMapper;
import com.cfpamf.ms.insur.operation.activity.dao.SystemActivityProductMapper;
import com.cfpamf.ms.insur.operation.activity.entity.AuthUser;
import com.cfpamf.ms.insur.operation.activity.enums.ActivityType;
import com.cfpamf.ms.insur.operation.activity.service.SmActivityRewardService;
import com.cfpamf.ms.insur.operation.activity.vo.SystemActivityProductDetailVo;
import com.cfpamf.ms.insur.operation.base.constant.BusinessConstants;
import com.cfpamf.ms.insur.operation.base.mq.RabbitMqUtils;
import com.google.common.collect.Maps;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.amqp.core.ExchangeTypes;
import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;

import javax.validation.Valid;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * TODO 活动通过监听来实现自动发券加入job后需要重新设计 job可实现包括listener方式的所有活动方案 listener是否还需要？
 *
 * <AUTHOR>
 * @date 2021/7/12 15:28
 */
@Component
@Slf4j
public class GrantCouponsListener {

    public static String REGION_ALL = "ALL";
    @Autowired
    RabbitMqUtils rabbitMqUtils;

    @Autowired
    SystemActivityProductMapper systemActivityProductMapper;

    @Autowired
    AuthUserMapper authUserMapper;

    @Autowired
    OperationActivityOrderMessageMapper operationActivityOrderMessageMapper;

    @Autowired
    SmActivityRewardService smActivityRewardService;


    @RabbitListener(bindings = @QueueBinding(
            value = @Queue(value = "${operation-mq.queue}", durable = "true"),
            exchange = @Exchange(value = "${operation-mq.exchange}", type = ExchangeTypes.FANOUT, durable = "true")), containerFactory = "insuranceOperationFactory")
    public void grantCoupons(@Payload @Valid GrantCouponsOrderMessage couponsOrderMessage, @Header(AmqpHeaders.DELIVERY_TAG) long deliveryTag, Channel channel) {
        try {
            log.info("消息消费，消息内容{}", JSON.toJSONString(couponsOrderMessage));
            //持久化消息
            operationActivityOrderMessageMapper.insert(couponsOrderMessage.toOperationActivityOrderMessage());
            handlerMessage(couponsOrderMessage);
        } finally {
            rabbitMqUtils.manualAcknowledgeMode(true, deliveryTag, channel, false);
        }
    }

    public void handlerMessage(GrantCouponsOrderMessage couponsOrderMessage) {

        String fhOrderId = couponsOrderMessage.getFhOrderId();
        //获取当前订单可用的活动集合映射关系
        Map<Long, List<SystemActivityProductDetailVo>> systemActivityMap = getSystemActivityMap(couponsOrderMessage);
        if (MapUtils.isEmpty(systemActivityMap)) {
            log.info("订单id:{}暂没参加任何活动", fhOrderId);
            return;
        }
        //计算活动奖励
        smActivityRewardService.handlerMessageActivity(couponsOrderMessage, systemActivityMap);
    }

    /**
     * 获取当前订单可用的活动集合映射关系
     *
     * @param couponsOrderMessage
     * @return
     */
    private Map<Long, List<SystemActivityProductDetailVo>> getSystemActivityMap(GrantCouponsOrderMessage couponsOrderMessage) {

        //获取订单推荐人的区域信息
        AuthUser recommendUser = authUserMapper.getByUserId(couponsOrderMessage.getRecommendId());
        if (Objects.isNull(recommendUser)) {
            log.warn("当前订单推荐人不存在{}-{}", couponsOrderMessage.getFhOrderId(), couponsOrderMessage.getRecommendId());
            return Maps.newHashMap();
        }
        Date paymentTime = couponsOrderMessage.getPaymentTime();
        Integer productId = couponsOrderMessage.getProductId();
        Integer planId = couponsOrderMessage.getPlanId();
        //获取当S前订单产品的所有启用活动信息集合
        List<SystemActivityProductDetailVo> systemActivityProductList = systemActivityProductMapper.getByListenerProductIdAndPayTime(productId, paymentTime);
        if (CollectionUtils.isEmpty(systemActivityProductList)) {
            return null;
        }

        //获取在活动区域的活动信息集合映射关系
        Map<Long, List<SystemActivityProductDetailVo>> systemActivityMap = systemActivityProductList.stream()
                .filter(systemActivityProductDetailVo -> {
                    //过滤掉非活动区域的活动
                    systemActivityProductDetailVo.initRegionListByRegions();
                    List<String> regionList = systemActivityProductDetailVo.getRegionList();
                    return regionList.contains(recommendUser.getRegionCode()) || regionList.contains(REGION_ALL);
                })
                //活动产品包含订单产品
                .filter(systemActivityProductDetailVo -> {
                    return systemActivityProductDetailVo.getProductId() == BusinessConstants.ALL_PRODUCT_ID || systemActivityProductDetailVo.getPlanIdList().contains(planId);
                })
                //过滤掉普通活动
                .filter(systemActivityProductDetailVo -> !ActivityType.NORMAL.equals(systemActivityProductDetailVo.getType()))
                .collect(Collectors.groupingBy(SystemActivityProductDetailVo::getSaId));
        return systemActivityMap;
    }

}
