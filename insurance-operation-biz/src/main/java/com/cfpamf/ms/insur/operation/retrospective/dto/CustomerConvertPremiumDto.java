package com.cfpamf.ms.insur.operation.retrospective.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class CustomerConvertPremiumDto implements Serializable {
    private static final long serialVersionUID = 8079079484856289084L;
    @ApiModelProperty("身份证号码")
    private String idCard;

    @ApiModelProperty(name="标准保费")
    BigDecimal conversionAmt;

    @ApiModelProperty("是否转化客户")
    boolean conversionPerson;

    public BigDecimal getConversionAmt() {
        if(conversionAmt == null){
            return BigDecimal.ZERO;
        }
        return conversionAmt;
    }

    public void setConversionAmt(BigDecimal conversionAmt) {
        this.conversionAmt = conversionAmt;
    }
}
