package com.cfpamf.ms.insur.operation.base.config;


import com.cfpamf.ms.insur.operation.base.helper.BmsHelper;
import com.cfpamf.ms.insur.operation.base.util.SpringFactoryUtil;
import com.cfpamf.ms.insur.operation.fegin.bms.response.vo.ModuleVO;
import com.cfpamf.ms.insur.operation.fegin.bms.response.vo.UserDetailVO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 当前用户ThreadLocal
 *
 * <AUTHOR>
public class ThreadUserHolder {

    /**
     * 当前登录用户权限
     */
    public final static ThreadLocal<List<ModuleVO>> USER_MODULES_TL = new ThreadLocal<List<ModuleVO>>() {
        @Override
        protected synchronized List<ModuleVO> initialValue() {
            return SpringFactoryUtil.getBean(BmsHelper.class).getContextUserPermissions();
        }
    };

    /**
     * 当前等路用户信息
     */
    public final static ThreadLocal<UserDetailVO> USER_DETAIL_TL = new ThreadLocal<UserDetailVO>() {
        @Override
        protected synchronized UserDetailVO initialValue() {
            UserDetailVO contextUserDetail = SpringFactoryUtil.getBean(BmsHelper.class).getContextUserDetail();

            if (contextUserDetail != null) {
                //过滤非保险的角色
                List<UserDetailVO.UserRoleVO> roleList = contextUserDetail.getRoleList();
                if (!CollectionUtils.isEmpty(roleList)) {
                    contextUserDetail.setRoleList(roleList.stream().filter(role -> StringUtils.startsWith(role.getRoleName(), "保险")).collect(Collectors.toList()));
                }
            }
            return contextUserDetail;
        }
    };
}
