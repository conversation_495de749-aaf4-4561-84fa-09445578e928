package com.cfpamf.ms.insur.operation.reward.redenvelope.po.entity;

import com.cfpamf.ms.insur.operation.base.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * system_red_envelope
 *
 * <AUTHOR>
@ApiModel(value = "系统红包表")
@Data
@Table(name = "system_red_envelope")
public class SystemRedEnvelope extends BaseEntity {

    /**
     * 用户id
     */
    @ApiModelProperty(value = "用户id")
    private String userId;

    /**
     * 发送状态1：未拆 2：已拆
     */
    @ApiModelProperty(value = "状态1：未拆 2：已拆")
    private String sendState;

    /**
     * 金额
     */
    @ApiModelProperty(value = "金额")
    private BigDecimal amount;

    /**
     * 数据标识 如订单号，保单号，用户id
     */
    @ApiModelProperty(value = "数据标识 如订单号，保单号，用户id")
    private String dataId;

    /**
     * 数据类型 如USER_ID,ORDER_ID，POLICY_NO
     */
    @ApiModelProperty(value = "数据类型 如USER_ID,ORDER_ID,POLICY_NO")
    private String dataType;

    @ApiModelProperty(value = "活动id")
    private Long saId;

    /**
     * 发送时间
     */
    @ApiModelProperty(value = "发送时间")
    private LocalDateTime sendTime;

}