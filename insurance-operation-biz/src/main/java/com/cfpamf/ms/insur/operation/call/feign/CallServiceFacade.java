package com.cfpamf.ms.insur.operation.call.feign;

import com.cfpamf.ms.insur.operation.call.feign.dto.*;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 第三方呼叫服务Feign客户端
 *
 * <AUTHOR>
 * @date 2025-07-22
 */
@FeignClient(name = "call-service", url = "${call-service.url}")
public interface CallServiceFacade {

    /**
     * 呼叫接口
     *
     * @param request 呼叫请求
     * @return 呼叫响应
     */
    @PostMapping("/call")
    CallServiceResponseDto call(@RequestBody CallServiceRequestDto request);

    /**
     * 呼叫记录查询
     *
     * @param request 查询请求
     * @return 查询响应
     */
    @PostMapping("/Record/query")
    RecordQueryResponseDto queryRecord(@RequestBody RecordQueryRequestDto request);

    /**
     * 呼叫录音查询
     *
     * @param request 录音查询请求
     * @return 录音查询响应
     */
    @PostMapping("/Telecom/Audio")
    AudioQueryResponseDto queryAudio(@RequestBody AudioQueryRequestDto request);
}
