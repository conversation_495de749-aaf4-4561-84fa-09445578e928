package com.cfpamf.ms.insur.operation.base.util;

import org.springframework.util.StringUtils;
import com.cfpamf.ms.bms.facade.util.JwtHelper;
import com.cfpamf.ms.bms.facade.vo.JwtUserInfo;
import com.cfpamf.ms.insur.operation.base.constant.BaseConstants;
import com.google.common.collect.Maps;
import org.springframework.core.env.Environment;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.Enumeration;
import java.util.Map;

/**
 * 获取http request 参数信息
 *
 * <AUTHOR>
 */
public class HttpRequestUtil {

    public static final String UNKNOWN = "unknown";
    private static JwtHelper jwtHelper = SpringFactoryUtil.getBean(JwtHelper.class);

    public HttpRequestUtil() {
    }
    private static boolean localEnv() {
        Environment env = SpringFactoryUtil.getBean(Environment.class);
        if (env != null) {
            String activeProfile = env.getProperty("spring.profiles.active");
            return "dev".equals(activeProfile) || "local".equals(activeProfile);
//                    || org.apache.commons.lang.StringUtils.isBlank(activeProfile);
        }
        return false;
    }
    /**
     * 获取token
     *
     * @return
     */
    public static String getToken() {
        HttpServletRequest request = getRequest();
        if (request == null) {
            return null;
        }
        String token = request.getHeader(BaseConstants.API_AUTH_NAME);
        if (StringUtils.isEmpty(token)) {
            token = request.getParameter(BaseConstants.API_AUTH_NAME);
        }
        return token;
    }

    /**
     * 获取微信openid
     *
     * @return
     */
    public static String getWxOpenId() {
        HttpServletRequest request = getRequest();
        String openId = request.getHeader(BaseConstants.WX_HTTP_HEAD_OPENID);
        if (StringUtils.isEmpty(openId)) {
            openId = request.getParameter(BaseConstants.WX_HTTP_HEAD_OPENID);
        }
        return openId;
    }

    /**
     * 获取http 请求头 user-agent
     *
     * @return
     */
    public static String getUserAgent() {
        return getRequest().getHeader("user-agent");
    }

    /**
     * 获取http 请求头 referer
     *
     * @return
     */
    public static String getReferer() {
        return getRequest().getHeader("referer");
    }

    /**
     * 获取requestUrl
     *
     * @return
     */
    public static String getRequestUri() {
        HttpServletRequest request = getRequest();
        if (request != null) {
            return getRequest().getRequestURI();
        }
        return "";
    }

    /**
     * 获取request请求所有参数
     *
     * @return
     */
    public static Map<String, String> getParameterMap() {
        Map<String, String> params = Maps.newHashMap();
        HttpServletRequest request = getRequest();
        if (request == null) {
            return params;
        }
        Enumeration<String> enums = getRequest().getParameterNames();
        while (enums.hasMoreElements()) {
            String paraName = enums.nextElement();
            params.put(paraName, getRequest().getParameter(paraName));
        }
        return params;
    }

    /**
     * 获取request请求所有参数
     *
     * @param request
     * @return
     */
    public static Map<String, String> getParameterMap(HttpServletRequest request) {
        Map<String, String> params = Maps.newHashMap();
        Enumeration<String> enums = request.getParameterNames();
        while (enums.hasMoreElements()) {
            String paraName = enums.nextElement();
            params.put(paraName, request.getParameter(paraName));
        }
        return params;
    }

    /**
     * 获取客户端Id
     *
     * @param request
     * @return
     */
    public static String getIpAddr(HttpServletRequest request) {
        String ip = request.getHeader("x-forwarded-for");
        if (ip == null || ip.length() == 0 || UNKNOWN.equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || UNKNOWN.equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || UNKNOWN.equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_CLIENT_IP");
        }
        if (ip == null || ip.length() == 0 || UNKNOWN.equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_X_FORWARDED_FOR");
        }
        if (ip == null || ip.length() == 0 || UNKNOWN.equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        String[] ips = ip.split(",");
        if (ips.length > 0) {
            ip = ips[ips.length - 1];
        }
        return org.apache.commons.lang3.StringUtils.trim(ip);
    }

    /**
     * 获取当前请求主体
     *
     * @return
     */
    public static HttpServletRequest getRequest() {
        RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
        if (requestAttributes != null) {
            return ((ServletRequestAttributes) requestAttributes).getRequest();
        }
        return null;
    }

    /**
     * 获取用户Id
     *
     * @return
     */
    @SuppressWarnings({"unchecked", "rawtypes"})
    public static String getUserId() {
        if(localEnv()){
        }
        String token = getToken();
        if (token == null) {
            return null;
        }
        JwtUserInfo jwtUserInfo = jwtHelper.getUserFromToken(token);
        return jwtUserInfo.getJobNumber();
    }

    public static JwtUserInfo getJwtUserInfo() {
        String token = getToken();
        if (token == null) {
            return null;
        }
        return jwtHelper.getUserFromToken(token);
    }
}
