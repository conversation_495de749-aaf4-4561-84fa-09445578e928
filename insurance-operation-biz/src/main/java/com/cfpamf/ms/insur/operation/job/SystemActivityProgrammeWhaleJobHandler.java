package com.cfpamf.ms.insur.operation.job;

import com.cfpamf.common.ms.exception.MSBizNormalException;
import com.cfpamf.ms.insur.operation.activity.dao.WhaleActivityRewardMapper;
import com.cfpamf.ms.insur.operation.activity.dto.ActivityConstRuleParam;
import com.cfpamf.ms.insur.operation.activity.entity.WhaleActivityReward;
import com.cfpamf.ms.insur.operation.activity.enums.*;
import com.cfpamf.ms.insur.operation.activity.service.SmOrderActivityService;
import com.cfpamf.ms.insur.operation.activity.service.SystemActivityProgrammeService;
import com.cfpamf.ms.insur.operation.activity.vo.SystemActivityProductRuleVo;
import com.cfpamf.ms.insur.operation.activity.vo.SystemActivityProductVo;
import com.cfpamf.ms.insur.operation.activity.vo.SystemActivityProgrammeVo;
import com.cfpamf.ms.insur.operation.addCommission.dao.WhaleAddCommissionDetailItemMapper;
import com.cfpamf.ms.insur.operation.base.constant.GroovyCodeConstants;
import com.cfpamf.ms.insur.operation.base.service.SystemGroovyService;
import com.cfpamf.ms.insur.operation.base.util.BeanUtil;
import com.cfpamf.ms.insur.operation.msg.service.MsgStatService;
import com.cfpamf.ms.insur.operation.reward.dto.RewardDTO;
import com.cfpamf.ms.insur.operation.reward.service.RewardService;
import com.cfpamf.ms.insur.operation.reward.service.impl.AddCommissionRewardConstWhaleServiceImpl;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.TemporalAdjusters;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 活动job
 * 用于统计活动数据
 *
 * <AUTHOR>
 * @date 2021/12/13 10:30
 */
@Slf4j
@Component
public class SystemActivityProgrammeWhaleJobHandler {
    @Autowired
    MsgStatService msgStatService;

    @Autowired
    SmOrderActivityService smOrderActivityService;

    @Autowired
    SystemActivityProgrammeService systemActivityProgrammeService;

    @Autowired
    @Lazy
    private SystemGroovyService systemGroovyService;

    @Autowired
    private WhaleActivityRewardMapper whaleActivityRewardMapper;

    @Autowired
    Map<String, RewardService> rewardServiceMap;

    @Autowired
    private WhaleAddCommissionDetailItemMapper whaleAddCommissionDetailItemMapper;

    /**
     * 新增活动奖励
     */
    private List<WhaleActivityReward> addSmActivityRewardList;
    /**
     * 跟新活动奖励
     */
    private List<WhaleActivityReward> updateSmActivityRewardList;

    @Autowired
    AddCommissionRewardConstWhaleServiceImpl rewardService;

    @XxlJob("insurance-operation-system-activity-programme_whale")
    public void execute() {
        addSmActivityRewardList = Lists.newArrayList();
        updateSmActivityRewardList = Lists.newArrayList();
        List<SystemActivityProgrammeVo> activityProgrammeList = systemActivityProgrammeService.getSystemActivityProgrammeByState(EnumActivityState.IN_ACTIVITY);
        if (CollectionUtils.isEmpty(activityProgrammeList)) {
            log.info("暂无活动");
            return;
        }
        //过滤掉普通活动类型
        activityProgrammeList = activityProgrammeList.stream()
                .filter(systemActivityProductDetailVo -> !ActivityType.NORMAL.equals(systemActivityProductDetailVo.getType()))
                .filter(systemActivityProductDetailVo -> !ActivityType.RED_ENVELOPE.equals(systemActivityProductDetailVo.getType()))
                .filter(systemActivityProductDetailVo -> !ActivityType.GOAL_ACHIEVE.equals(systemActivityProductDetailVo.getType()))
                //过滤掉手动配置的
                .filter(systemActivityProductDetailVo -> !EnumActivityConfigType.CONST.equals(systemActivityProductDetailVo.getConfigType()))
                .filter(systemActivityProductDetailVo -> "xj".equals(systemActivityProductDetailVo.getActivityPlatform()))
                .collect(Collectors.toList());
        log.info("活动数量:{}", activityProgrammeList.size());
        //遍历活动
        for (SystemActivityProgrammeVo systemActivityProgrammeVo : activityProgrammeList) {
            // 如果是加佣活动，计算前先把当前活动奖励更新成0
            if (ActivityType.ADD_COMMISSION.name().equals(systemActivityProgrammeVo.getType().name())) {
                whaleActivityRewardMapper.updateProportionBySaId(systemActivityProgrammeVo.getId(), BigDecimal.ZERO);
            }
            //计算活动奖励
            calculationActivityReward(systemActivityProgrammeVo);
        }
        //更新活动奖励记录
        syncActivityReward();
        //对比已发送活动奖励 进行差值再次进行发送
        //遍历活动
        for (SystemActivityProgrammeVo systemActivityProgrammeVo : activityProgrammeList) {
            Long saId = systemActivityProgrammeVo.getId();
            List<WhaleActivityReward> rewardList = whaleActivityRewardMapper.getBySaId(saId);
            if (CollectionUtils.isEmpty(rewardList)) {
                continue;
            }
            Map<String, BigDecimal> dataIdMap = calculationConflict(rewardList, systemActivityProgrammeVo.getConflictRule());
            Map<String, BigDecimal> dataIdAmountMap = calculationConflictAmount(rewardList, systemActivityProgrammeVo.getConflictRule());
            RewardType rewardType = systemActivityProgrammeVo.getSystemActivityProductWhaleList().stream().findFirst().get().getRewardType();
            RewardService rewardService = rewardServiceMap.get(rewardType.name());
            if (Objects.isNull(rewardService)) {
                throw new MSBizNormalException("", rewardType.name() + "该奖励方式暂未实现");
            }

            // 更新之前 把当前活动更新成0 防止多发
            whaleAddCommissionDetailItemMapper.updateProportionByDataId(systemActivityProgrammeVo.getId(),
                    EnumCommissionType.ACTIVITY.name()
                    , BigDecimal.ZERO);

            //奖励发送
            rewardService.awardByJobV2(systemActivityProgrammeVo, dataIdMap, dataIdAmountMap);
        }
    }

    /**
     * 计算当前活动的最终奖励
     *
     * @param rewardList
     */
    private Map<String, BigDecimal> calculationConflict(List<WhaleActivityReward> rewardList, ConflictRule conflictRule) {
        Map<String, BigDecimal> result = Maps.newHashMap();
        //当前活动通过数据id标识分组
        Map<String, List<WhaleActivityReward>> dataIdActivityRewardMap = rewardList.stream()
                .collect(Collectors.groupingBy(WhaleActivityReward::getDataId));

        for (String dataId : dataIdActivityRewardMap.keySet()) {
            //遍历每个数据id的奖励集合
            List<WhaleActivityReward> smActivityRewardList = dataIdActivityRewardMap.get(dataId);
            List<BigDecimal> proportionList = smActivityRewardList.stream()
                    //通过活动产品规则id集合分组
                    .collect(Collectors.groupingBy(WhaleActivityReward::getSystemActivityProductId))
                    .values()
                    .stream()
                    //叠加每个活动产品规则id的奖励
                    .map(list -> list.stream()
                            .map(WhaleActivityReward::getProportion)
                            .filter(Objects::nonNull)
                            .reduce(BigDecimal.ZERO, BigDecimal::add))
                    .collect(Collectors.toList());
            BigDecimal proportion;
            switch (conflictRule) {
                case OVERLAY:
                    proportion = proportionList.stream().filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
                    break;
                case OPTIMAL:
                    //默认的叠加方式：OPTIMAL 最优
                default:
                    proportion = proportionList.stream().max((x1, x2) -> x1.compareTo(x2)).get();
                    break;
            }
            result.put(dataId, proportion);
        }
        return result;
    }

    private Map<String, BigDecimal> calculationConflictAmount(List<WhaleActivityReward> rewardList, ConflictRule conflictRule) {
        Map<String, BigDecimal> result = Maps.newHashMap();
        //当前活动通过数据id标识分组
        Map<String, List<WhaleActivityReward>> dataIdActivityRewardMap = rewardList.stream()
                .collect(Collectors.groupingBy(WhaleActivityReward::getDataId));

        for (String dataId : dataIdActivityRewardMap.keySet()) {
            //遍历每个数据id的奖励集合
            List<WhaleActivityReward> smActivityRewardList = dataIdActivityRewardMap.get(dataId);
            List<BigDecimal> amountList = smActivityRewardList.stream()
                    //通过活动产品规则id集合分组
                    .collect(Collectors.groupingBy(WhaleActivityReward::getSystemActivityProductId))
                    .values()
                    .stream()
                    //叠加每个活动产品规则id的奖励
                    .map(list -> list.stream()
                            .map(WhaleActivityReward::getAmount)
                            .filter(Objects::nonNull)
                            .reduce(BigDecimal.ZERO, BigDecimal::add))
                    .collect(Collectors.toList());
            BigDecimal amount;
            switch (conflictRule) {
                case OVERLAY:
                    amount = amountList.stream().filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
                    break;
                case OPTIMAL:
                    //默认的叠加方式：OPTIMAL 最优
                default:
                    amount = amountList.stream().max((x1, x2) -> x1.compareTo(x2)).get();
                    break;
            }
            result.put(dataId, amount);
        }
        return result;
    }
    /**
     * 同步活动奖励
     */
    private void syncActivityReward() {
        //新增活动奖励
        if (CollectionUtils.isNotEmpty(addSmActivityRewardList)) {
            whaleActivityRewardMapper.insertListDuplicateUpdateProportion(addSmActivityRewardList);
            addSmActivityRewardList.clear();
        }
    }

    /**
     * 计算活动奖励
     *
     * @param systemActivityProgrammeVo
     */
    private void calculationActivityReward(SystemActivityProgrammeVo systemActivityProgrammeVo) {
        //遍历活动产品规则
        List<SystemActivityProductVo> systemActivityProductList = systemActivityProgrammeVo.getSystemActivityProductWhaleList();
        Long saId = systemActivityProgrammeVo.getId();

        //按月兑换的活动，活动时间处理为当月
        initActivityTime(systemActivityProgrammeVo);

        String batchNo = rewardService.getBatchNo(systemActivityProgrammeVo);


        //获取当前活动的数据通过活动产品规则id分组
        List<WhaleActivityReward> rewardList = whaleActivityRewardMapper.getBySaId(saId);
        Map<Long, List<WhaleActivityReward>> activityProductRewardMap = rewardList.stream()
                .collect(Collectors.groupingBy(WhaleActivityReward::getSystemActivityProductId));

        for (SystemActivityProductVo systemActivityProductVo : systemActivityProductList) {
            //执行脚本判断当前活动规则是否为JOB触发类型
            if (Objects.equals(systemActivityProductVo.getTriggerType(), EnumActivityTriggerType.JOB.name())) {
                log.info("计算活动saId:{}的活动奖励", saId);

                //获取规则奖励
                ActivityConstRuleParam order = new ActivityConstRuleParam()
                        .saId(saId)
                        .constActivity(systemActivityProgrammeVo)
                        .activityPlatform(systemActivityProgrammeVo.getActivityPlatform())
                        .productRule(systemActivityProductVo.getSystemActivityProductRuleList().get(0));
                calculationActivityProductReward(systemActivityProgrammeVo.getType(), activityProductRewardMap, systemActivityProductVo,order,batchNo);
            }
        }


    }

    /**
     * 初始化活动区间
     * @param systemActivityProgrammeVo 活动信息
     */
    private void initActivityTime(SystemActivityProgrammeVo systemActivityProgrammeVo) {
        if (EnumActivityRewardMechanism.monthly.name().equals(systemActivityProgrammeVo.getRewardMechanism())) {
            LocalDateTime dateTime = LocalDate.now().minusDays(1).atStartOfDay().with(TemporalAdjusters.firstDayOfMonth());
            LocalDateTime startTime = dateTime.isBefore(systemActivityProgrammeVo.getStartTime()) || dateTime.isEqual(systemActivityProgrammeVo.getStartTime())
                    ? systemActivityProgrammeVo.getStartTime()
                    : LocalDate.now().atStartOfDay().with(TemporalAdjusters.firstDayOfMonth());
            LocalDateTime endDateTime = LocalDate.now().minusDays(1).withDayOfMonth(1).plusMonths(1).atStartOfDay();
            LocalDateTime endTime = endDateTime.isAfter(systemActivityProgrammeVo.getEndTime()) || endDateTime.isEqual(systemActivityProgrammeVo.getEndTime())
                    ? systemActivityProgrammeVo.getEndTime()
                    : LocalDate.now().minusDays(1).withDayOfMonth(1).plusMonths(1).atStartOfDay();
            systemActivityProgrammeVo.setStartTime(startTime);
            systemActivityProgrammeVo.setEndTime(endTime);
            log.info("活动时间初始化：{}", systemActivityProgrammeVo);
        }
    }

    /**
     * 计算活动产品奖励
     *
     * @param activityProductRewardMap
     * @param systemActivityProductVo
     */
    private void calculationActivityProductReward(ActivityType type, Map<Long, List<WhaleActivityReward>> activityProductRewardMap, SystemActivityProductVo systemActivityProductVo,ActivityConstRuleParam order,String batchNo) {
        Long activityProductRewardId = systemActivityProductVo.getId();
        Long saId = systemActivityProductVo.getSaId();
        List<SystemActivityProductRuleVo> systemActivityProductRuleList = systemActivityProductVo.getSystemActivityProductRuleList();
        //执行脚本判断当前活动规则是否为JOB触发类型
        if (!Objects.equals(systemActivityProductVo.getTriggerType(), EnumActivityTriggerType.JOB.name())) {
            return;
        }

        //获取当前活动的奖励数据
        List<WhaleActivityReward> activityRewardList = activityProductRewardMap.getOrDefault(activityProductRewardId, Lists.newArrayList());
        //通过数据id 和活动奖励的映射关系
        Map<String, WhaleActivityReward> rewardMap = activityRewardList.stream()
                .collect(Collectors.toMap(WhaleActivityReward::getDataId, Function.identity()));

        //执行规则获取执行结果
        SystemActivityProductRuleVo systemActivityProductRuleVo = systemActivityProductRuleList.get(0);

        int offset = 0;
        int size = 3000;
        order.offset(offset);
        order.size(size);
        Object result = systemGroovyService.executeForCodeByRuleMethod(GroovyCodeConstants.OPERATION_ACTIVITY_GROOVY_TYPE, systemActivityProductRuleVo.getRuleCode(), order);
        List<RewardDTO> rewardDTOList = BeanUtil.castObjectToListBean(result, RewardDTO.class);

        while(CollectionUtils.isNotEmpty(rewardDTOList)) {
            if (CollectionUtils.isEmpty(rewardDTOList)) {
                return;
            }

            for (RewardDTO rewardDTO : rewardDTOList) {
                String dataId = rewardDTO.getDataId();
                WhaleActivityReward whaleActivityReward = rewardMap.get(dataId);
                buildReward(type, systemActivityProductVo, rewardDTO, whaleActivityReward,batchNo);
            }

            offset += size;
            order.offset(offset);
            result = systemGroovyService.executeForCodeByRuleMethod(GroovyCodeConstants.OPERATION_ACTIVITY_GROOVY_TYPE, systemActivityProductRuleVo.getRuleCode(), order);
            rewardDTOList = BeanUtil.castObjectToListBean(result, RewardDTO.class);
        }
    }

    /**
     * 奖励对象转换
     *
     * @param systemActivityProductVo
     * @param rewardDTO               当前脚本跑的奖励对象
     * @param whaleActivityReward        历史脚本跑的奖励对象
     */
    private void buildReward(ActivityType type,
                             SystemActivityProductVo systemActivityProductVo,
                             RewardDTO rewardDTO,
                             WhaleActivityReward whaleActivityReward,
                             String batchNo) {
        Long activityProductRewardId = systemActivityProductVo.getId();
        Long saId = systemActivityProductVo.getSaId();
        RewardType rewardType = systemActivityProductVo.getRewardType();
        BigDecimal proportion = rewardDTO.getProportion();
        if (Objects.isNull(whaleActivityReward)) {
            //如果当前没有活动奖励
            WhaleActivityReward addActivityReward = new WhaleActivityReward(rewardDTO, EnumActivityTriggerType.JOB, rewardType, saId, activityProductRewardId,rewardDTO.getAmount(),batchNo);
            //新增奖励
            addSmActivityRewardList.add(addActivityReward);
        } else {
            BigDecimal rewardProportion = whaleActivityReward.getProportion();
            whaleActivityReward.setBatchNo(batchNo);
            /**
             * 加佣奖励直接覆盖原有的奖励数据；
             * 其他类型逻辑不变，有疑问可跟刘奇隆&李雪沟通需求；
             */
            if (Objects.equals(ActivityType.ADD_COMMISSION, type)) {
                whaleActivityReward.setProportion(proportion);
                whaleActivityReward.setAmount(rewardDTO.getAmount());
                addSmActivityRewardList.add(whaleActivityReward);
            } else {
                //如果有活动奖励 并且奖励数量当前奖励数量
                if (proportion.compareTo(rewardProportion) > 0) {
                    //修改奖励数量
                    whaleActivityReward.setProportion(proportion);
                    //更新奖励
                    addSmActivityRewardList.add(whaleActivityReward);
                }
            }
        }
    }
}
