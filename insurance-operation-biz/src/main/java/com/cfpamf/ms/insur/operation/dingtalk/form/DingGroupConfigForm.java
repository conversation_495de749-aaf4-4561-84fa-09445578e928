package com.cfpamf.ms.insur.operation.dingtalk.form;

import com.cfpamf.ms.insur.operation.base.form.PageForm;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Id;
import javax.persistence.OrderBy;
import java.util.Date;

@Data
@ApiModel("钉钉群配置查询参数")
public class DingGroupConfigForm extends PageForm {

    /**
     * 主键id
     */
    @Id
    @OrderBy("desc")
    Long id;
    @ApiModelProperty("群id")
    private String groupId ;
    /** 群名称 */
    @ApiModelProperty("群名称")
    private String groupName ;
    /** 说明 */
    @ApiModelProperty("说明")
    private String remark ;
    /** 创建人 */
    @ApiModelProperty("创建人")
    private String createdBy ;
    /** 创建时间 */
    @ApiModelProperty("创建时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date createdTime ;
    /** 更新人 */
    @ApiModelProperty("更新人")
    private String updatedBy ;
    /** 更新时间 */
    @ApiModelProperty("更新时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date updatedTime ;
    @ApiModelProperty("查询开始时间 yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date startDate;
    @ApiModelProperty("查询结束时间 yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date endDate;
}
