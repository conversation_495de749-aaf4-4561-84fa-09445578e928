package com.cfpamf.ms.insur.operation.addCommission.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class PreservationSettlementDto {
    /**
     * 字段名称 流水号
     */
    @ApiModelProperty(value = "流水号")
    String requestId;

    @ApiModelProperty(value = "结算明细")
    List<AddCommissionSettlementPushDto> settlementPushDtos;
}
