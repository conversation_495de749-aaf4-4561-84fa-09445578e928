package com.cfpamf.ms.insur.operation.phoenix.pojo.domain;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * <AUTHOR>
 */
@Data
public class PhoenixAiCallTaskDetail {

    /** 外部关联业务主键 */
    @ApiModelProperty(name = "外部关联业务主键",notes = "")
    private Integer outSourceId ;

    /** 调用方的业务随路数据, 字符串，百度侧原文回传 */
    @ApiModelProperty(name = "调用方的业务随路数据, 字符串，百度侧原文回传",notes = "")
    private String extJson ;
    /** 被叫号码 */
    @ApiModelProperty(name = "被叫号码",notes = "")
    private String mobile ;
    @ApiModelProperty(name = "任务类型",notes = "")
    private String bizType ;
    @ApiModelProperty(name = "保险产品名称",notes = "")
    private String productName ;
    @ApiModelProperty(name = "称呼（男：先生，女：女士）",notes = "")
    private String salutation ;
    @ApiModelProperty(name = "客户名称",notes = "")
    private String customerName ;
    @ApiModelProperty(name = "保单失效日期",notes = "")
    private String endTime ;
    @ApiModelProperty(name = "产品类型",notes = "")
    private String productType ;

}
