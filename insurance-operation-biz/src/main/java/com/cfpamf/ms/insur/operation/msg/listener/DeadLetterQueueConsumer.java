package com.cfpamf.ms.insur.operation.msg.listener;

import com.cfpamf.ms.insur.operation.msg.config.RabbitMqMsgQueueConfig;
import com.cfpamf.ms.insur.operation.msg.service.MsgPushService;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.converter.Jackson2JsonMessageConverter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR> 2021/7/14 10:56
 */
@Slf4j
@Component
public class DeadLetterQueueConsumer {

    @Autowired
    Jackson2JsonMessageConverter converter;
    @Autowired
    MsgPushService service;

    @RabbitListener(queues = RabbitMqMsgQueueConfig.DELAYED_QUEUE_NAME, containerFactory = "insuranceOperationFactory")
    public void receive(Message message, Channel channel) throws IOException {
        log.info("开始消费延迟消息：{}", message);
        final String messageId = message.getMessageProperties().getMessageId();
        final Object o = converter.fromMessage(message);
        try {
            CompletableFuture.runAsync(() -> {
                try {
                    service.pushMessage((Long) o, messageId, true, true);
                } catch (Exception e) {
                    log.error("推送消息失败{}", messageId, e);
                }

            });
        } catch (Exception e) {
            log.error("消费延迟消息失败", e);
        } finally {
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);

        }
    }
}
