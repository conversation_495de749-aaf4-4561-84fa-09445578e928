package com.cfpamf.ms.insur.operation.msg.enums;

import com.cfpamf.ms.bms.facade.exception.BizException;
import com.cfpamf.ms.insur.operation.fegin.bms.enums.ExcptEnum;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.experimental.FieldDefaults;

import java.util.stream.Stream;

/**
 * <AUTHOR> 2021/7/15 17:06
 */
@Getter
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public enum OpMessageContextEnum {

    /**
     * 区域负责人+保险区域督导
     */
    REGION_ADMIN("region_admin", "区域"),
    /**
     * 机构负责人
     */
    ORG_ADMIN("org_admin", "分支"),
    REGION_SUPERVISOR("region_supervisor", "保险区域督导"),
    /**
     * 推对应结构的数据
     */
    ORG_PCO("org_pco", "渠道PCO"),

    ORG_DIRECTOR("org_director", "机构主任"),

    ORG_DEPUTY_DIRECTOR("org_deputy_director", "机构副主任"),

    ORG_ASSISTANT_DIRECTOR("org_assistant_director", "主任助理"),

    /**
     * 所有非总部员工
     */
    ALL_AREA("all_area", "所有非总部员工"),
    /**
     * 客户经理
     */
    CUSTOMER_ADMIN("customer_admin", "客户经理"),
    /**
     * 片区负责人
     */
    DISTRICT_ADMIN("district_admin", "片区负责人");
    ;

    String code;

    String desc;


    public boolean isMe(String code) {
        return this.code.equals(code);
    }

    public static OpMessageContextEnum valueOfCode(String code) {
        return Stream.of(values()).filter(en -> en.code.equals(code)).findAny()
                .orElseThrow(() -> new BizException(ExcptEnum.PARAMS_ERROR.getCode(), code + "场景不存在"));
    }

}
