package com.cfpamf.ms.insur.operation.honor.po;

import com.cfpamf.ms.insur.operation.base.po.BasePO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import javax.persistence.Table;

/**
 * <AUTHOR>
 * 客户断保跟进记录表
 */
@Data
@Table(name = "honor_metric_configuration")
@FieldDefaults(level = AccessLevel.PRIVATE)
public class HonorsMetricConfigurationPo extends BasePO {

    @ApiModelProperty(name="评选对象 area:区域，bch：分支，emp：个人")
    String level;

    @ApiModelProperty(name="评选周期 year:年度，quarter：季度，month：月")
    String period;

    @ApiModelProperty(name="指标名称")
    String metricName;

    @ApiModelProperty(name="指标编码")
    String metricCode;

    @ApiModelProperty("创建人")
    String createBy;

    @ApiModelProperty("修改人")
    String updateBy;
}
