package com.cfpamf.ms.insur.operation.fegin.bms.response.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * @author: <PERSON><PERSON><PERSON><PERSON>
 * @date: 2020/8/10 14:56
 * @description:
 */
@Data
public class BmsOrgDTO implements Serializable {

    private String batchNo;
    private Integer orgId;
    private Integer hrOrgId;
    private String orgCode;
    private String orgName;
    private String shortName;
    private Integer hrParentId;
    private String treePath;
    private Integer treeLevel;
    private Integer personInChargeId;
    private String personInChargeName;
    private Integer hrbpId;
    private String areaOrgCode;
    private Integer zoneOrgId;

    /**
     *
     * @return
     */
    @Override
    public String toString() {
        return "BmsOrgDTO(batchNo=" + this.getBatchNo() + ", orgId=" + this.getOrgId() + ", hrOrgId=" + this.getHrOrgId() + ", orgCode=" + this.getOrgCode() + ", orgName=" + this.getOrgName() + ", shortName=" + this.getShortName() + ", hrParentId=" + this.getHrParentId() + ", treePath=" + this.getTreePath() + ", treeLevel=" + this.getTreeLevel() + ", personInChargeId=" + this.getPersonInChargeId() + ", personInChargeName=" + this.getPersonInChargeName() + ", hrbpId=" + this.getHrbpId() + ", areaOrgCode=" + this.getAreaOrgCode() + ", zoneOrgId=" + this.getZoneOrgId() + ")";
    }
}
