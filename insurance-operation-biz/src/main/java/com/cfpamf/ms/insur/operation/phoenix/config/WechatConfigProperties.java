package com.cfpamf.ms.insur.operation.phoenix.config;

import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * <AUTHOR>
 * @since 2023/3/29 10:28
 */
@Data
@ConfigurationProperties("wechat")
@FieldDefaults(level = AccessLevel.PRIVATE)
public class WechatConfigProperties {

    /**
     * app id
     */
    String appId;

    /**
     * 密钥
     */
    String appSecret;


    /**
     * 模版消息-渠道通用模板
     */
    String tempMessageChannelCommon;

    String followMessage;
    /**
     * 模版消息-AI高意向跟进通知
     */
    String tempMessageAiFollow;

    /**
     * 断保客户待办跳转地址
     */
    String  todoInterruptionUrl;
    String frontDomain;

    /**
     * 断保客户详情
     */
    String frontCustomerDetailUrl;
    /**
     * 续投保单详情
     */
    String frontRenewDetailUrl;
    /**
     * 断保客户详情
     */
    String customerDetailUrl;
    /**
     * 车险费率配置详情
     */
    String carPromotionFee;
    /**
     * 续投保单详情
     */
    String renewDetailUrl;

    String apiDomain;

    /**
     * 异业客户详情
     */
    String customerLoanDetailUrl;

    /**
     * 重定向登录地址URL
     */
    String backIndexUrl;

    /**
     * 微信授权地址
     */
    String backJumpUrl;

    String materialChangeMessage;

    String frontMaterialDetailUrl;

    String materialDetailUrl;

}
