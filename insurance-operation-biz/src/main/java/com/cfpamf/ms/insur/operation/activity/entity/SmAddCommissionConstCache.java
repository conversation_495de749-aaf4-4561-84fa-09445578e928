package com.cfpamf.ms.insur.operation.activity.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;


/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/7/4 15:03
 */
@ApiModel(value = "手动配置加佣")
@Table(name = "sm_add_commission_const_cache")
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class SmAddCommissionConstCache implements Serializable {

    @Id
    Long id;

    @ApiModelProperty("活动id")
    Long saId;

    String orderId;

    String dataId;
}
