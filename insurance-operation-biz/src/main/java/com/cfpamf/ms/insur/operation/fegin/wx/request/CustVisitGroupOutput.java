package com.cfpamf.ms.insur.operation.fegin.wx.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class CustVisitGroupOutput {

    @ApiModelProperty(value = "总条数")
    private Integer total;

    @ApiModelProperty(value = "页码")
    private Integer pageNum;

    @ApiModelProperty(value = "一页多少条数据")
    private Integer pageSize;

    private List<CustVisitGroupInfo> list;
}
