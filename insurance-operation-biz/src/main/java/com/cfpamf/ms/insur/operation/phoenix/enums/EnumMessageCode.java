package com.cfpamf.ms.insur.operation.phoenix.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @datetime 2023/2/16 13:47
 */
@Getter
@AllArgsConstructor
public enum EnumMessageCode {


    /**
     * 业务方：保险
     * 业务场景：断保客户通知
     * 业务类型：SafesCustomerInterruption
     * 业务描述：断保客户通知
     * 消息类型 sms
     * 短信签名：小鲸向海
     * 模板内容： 尊敬的客户，您好！ {personName}的{riskName}保障已过期，守护您的美好生活，建议您尽快重获保障，也可以联系您的服务顾问哦～投保产品链接：${url}
     */
    SAFES_CUSTOMER_INTERRUPTION("SafesCustomerInterruption", 1),
    HOME_PROPERTY_INSURANCE("HomePropertyInsurance", 1),
    GROUP_ACCIDENT_INSURANCE("GroupAccidentInsurance", 1),
    LIABILITY_INSURANCE("LiabilityInsurance", 1),
    MEDICAL_INSURANCE("MedicalInsurance", 1),
    ILLNESS_INSURANCE("IllnessInsurance", 1),;
    String code;

    /**
     * 1 短信 2 钉钉 3组合
     */
    Integer type;
}
