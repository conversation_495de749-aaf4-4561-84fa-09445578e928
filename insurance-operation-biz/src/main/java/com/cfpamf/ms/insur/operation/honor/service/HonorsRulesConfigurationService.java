package com.cfpamf.ms.insur.operation.honor.service;

import com.cfpamf.common.ms.exception.MSBizNormalException;
import com.cfpamf.ms.insur.operation.base.util.AliYunOssUtil;
import com.cfpamf.ms.insur.operation.base.util.HttpRequestUtil;
import com.cfpamf.ms.insur.operation.honor.dto.*;
import com.cfpamf.ms.insur.operation.honor.query.HonorRulesConfigurationQuery;
import com.cfpamf.ms.insur.operation.pco.util.DownloadUtil;
import com.cfpamf.ms.insur.operation.pco.util.ExcelReadUtils;
import com.cfpamf.ms.insur.operation.pco.validation.ValidationResult;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import com.cfpamf.ms.insur.operation.honor.dao.HonorRulesConfigurationMapper;
import com.cfpamf.ms.insur.operation.honor.po.HonorsRulesConfigurationPo;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.io.ByteArrayOutputStream;
import java.time.YearMonth;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class HonorsRulesConfigurationService {

    HonorRulesConfigurationMapper honorRulesConfigurationMapper;

    ObjectMapper objectMapper;

    HonorValidationService honorValidationService;

    HonorListImportService honorListImportService;

    HonorsSelectionResultsService honorsSelectionResultsService;

    /**
     * 添加续保服务跟进记录
     * @param dto 跟进Dto
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.NESTED)
    public HonorRulesConfigurationDto saveOrUpdateHonorRules(HonorRulesConfigurationDto dto) {
        String userId = HttpRequestUtil.getUserId();
        //主键为空则进行新增，否则进行更新
        HonorsRulesConfigurationPo po = new HonorsRulesConfigurationPo();
        if (Objects.isNull(dto.getId())) {
            BeanUtils.copyProperties(dto,po);
            po.setCreateBy(userId);
            po.setUpdateBy(userId);
            po.setState(0);
            honorRulesConfigurationMapper.insertConfiguration(po);
            dto.setId(po.getId());
        } else {
            BeanUtils.copyProperties(dto,po);
            po.setUpdateBy(userId);
            honorRulesConfigurationMapper.updateByPrimaryKey(po);
        }
        return dto;
    }

    /**
     * 获取荣誉规则配置详情
     * @param id 主键
     * @return HonorRulesConfigurationDto
     */
    public HonorRulesConfigurationDto getRulesDetailById(Integer id) {
        HonorsRulesConfigurationPo po = honorRulesConfigurationMapper.selectByPrimaryKey(id);
        HonorRulesConfigurationDto dto = new HonorRulesConfigurationDto();
        BeanUtils.copyProperties(po,dto);
        return dto;
    }

    /**
     * 获取荣誉规则配置分页列表
     * @param query 查询dto
     * @return HonorRulesConfigurationList
     */
    public PageInfo<HonorRulesConfigurationList> getRulesConfigurationListByPage(HonorRulesConfigurationQuery query) {
        if (query.getPageNo() > 0) {
            PageHelper.startPage(query.getPageNo(), query.getPageSize());
        }
        List<HonorRulesConfigurationList> pcoLevelChangeRecords = honorRulesConfigurationMapper.list(query);
        return new PageInfo<>(pcoLevelChangeRecords);
    }

    /**
     * 修改荣誉规则可用状态为1
     * @param id 主键
     */
    public void deleteRuleById(Integer id) {
        HonorsRulesConfigurationPo po = honorRulesConfigurationMapper.selectByPrimaryKey(id);
        if (!Objects.isNull(po)) {
            po.setEnabledFlag(1);
            honorRulesConfigurationMapper.updateByPrimaryKeySelective(po);
        }
    }

    /**
     * 荣誉清单导入
     * @param dto 导入参数
     * @return HonorListImportResultDto
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.NESTED)
    public HonorListImportResultDto honorListImport(HonorListImportDTO dto){
        String userId = HttpRequestUtil.getUserId();
        HonorListImportResultDto resultDto = new HonorListImportResultDto();
        try {
            List<HonorListExcelDTO> excelDtoS = ExcelReadUtils
                    .readWorkbookByStream(
                            DownloadUtil.downloadByUrl(dto.getFileUrl()), HonorListExcelDTO.class
                            , 9, false);

            if (CollectionUtils.isEmpty(excelDtoS)) {
                return resultDto;
            }
            //错误信息要还原 把导入 列表复制一份
            List<HonorListExcelDTO> excelClones = objectMapper.convertValue(excelDtoS, new TypeReference<List<HonorListExcelDTO>>() {
            });
            //复制错误数据 并赋值错误信息
            List<HonorListExcelDTO> errorDatas = new ArrayList<>();
            StringBuilder errorMsg = new StringBuilder();
            List<ValidationResult<HonorListExcelDTO>> validationResults = honorValidationService.valid(excelDtoS,dto);

            for (int i = 0; i < validationResults.size(); i++) {
                ValidationResult<HonorListExcelDTO> honorValidationResult = validationResults.get(i);
                if (!honorValidationResult.isSuccess()) {
                    HonorListExcelDTO error = excelClones.get(i);
                    error.setErrorMsg(honorValidationResult.getMessage());
                    errorDatas.add(error);
                    errorMsg.append(honorValidationResult.getMessage()).append(";");
                }
            }

            //有错误记录，直接返回错误信息
            if (!CollectionUtils.isEmpty(errorDatas)) {
                ByteArrayOutputStream os = new ByteArrayOutputStream(8096);
                ExcelReadUtils.write(os, errorDatas);
                String errorUrl = AliYunOssUtil.uploadByBytes(os.toByteArray(), genImportErrorOssPrefix()
                        + "/error.xlsx");
                resultDto.setErrUrl(errorUrl);
                resultDto.setErrorMsg(errorMsg.toString());
                return resultDto;
            }

            honorsSelectionResultsService.calculateHonor(dto,excelDtoS);

            //添加导入记录
            honorListImportService.addImportRecord(dto, userId);

            //更新荣誉规则配置的文件地址
            HonorsRulesConfigurationPo po = new HonorsRulesConfigurationPo();
            po.setId(dto.getHonorsConfigurationsId());
            po.setFileName(dto.getFileName());
            po.setFileUrl(dto.getFileUrl());
            honorRulesConfigurationMapper.updateByPrimaryKeySelective(po);
        } catch (Exception e) {
            throw new MSBizNormalException("400", "导入失败"+e.getMessage());
        }
        return resultDto;
    }


    public static String genImportErrorOssPrefix() {
        return "honorList/import/error/" + YearMonth.now().toString().replaceAll("-", "")
                + "/" + UUID.randomUUID().toString().replaceAll("-", "");
    }

}
