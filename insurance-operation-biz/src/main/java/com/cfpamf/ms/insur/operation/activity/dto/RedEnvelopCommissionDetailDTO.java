package com.cfpamf.ms.insur.operation.activity.dto;

import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR> 2022/7/4 14:11
 * 红包活动提成明细DTO
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class RedEnvelopCommissionDetailDTO {

    String orderId;

    Integer orderIdIndex;

    Integer productId;

    LocalDateTime createTime;

    String idNumber;

    BigDecimal totalAmount;

    String policyStatus;

    String policyNo;

}
