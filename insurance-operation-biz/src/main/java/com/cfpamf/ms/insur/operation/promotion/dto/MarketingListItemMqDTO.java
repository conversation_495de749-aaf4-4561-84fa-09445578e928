package com.cfpamf.ms.insur.operation.promotion.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class MarketingListItemMqDTO implements Serializable {

    @ApiModelProperty(value = "主表id")
    public Long id;

    @ApiModelProperty(value = "上架状态 0-待上架 1-上架中 2-已上架 -1-下架 -2-已作废")
    private Integer launchStatus;

    @ApiModelProperty(value = "业务类型 信贷、生服、农服、保险、光伏、通用")
    private Integer bizType;
}
