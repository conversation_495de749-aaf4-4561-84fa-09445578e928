package com.cfpamf.ms.insur.operation.planbook.form;

import com.cfpamf.ms.insur.operation.base.form.PageForm;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @Date 2021/12/15 14:40
 * @Version 1.0
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
@ApiModel("计划书分页查询参数")
public class PlanBookPageQuery extends PageForm {

    @ApiModelProperty("计划书名称")
    String planBookName;

    @ApiModelProperty("计划书制作开始时间")
    LocalDateTime startTime;

    @ApiModelProperty("计划书制作结束时间")
    LocalDateTime endTime;

    String userId;

}
