package com.cfpamf.ms.insur.operation.activity.dao;

import com.cfpamf.ms.insur.operation.activity.temp.entity.TempProductRepurchaseActivity;
import com.cfpamf.ms.insur.operation.base.dao.CommonMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface TempProductRepurchaseActivityMapper extends CommonMapper<TempProductRepurchaseActivity> {

    /**
     * 初始化时间短
     *
     * @param startTime
     * @param endTime
     */
    public void initDateTimeData(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);

    /**
     * 获取结束时间查询
     *
     * @param startTime
     * @return
     */
    List<LocalDateTime> getEndTimeQuery(@Param("startTime") LocalDateTime startTime);

    /**
     * 查询
     * @param startTime
     * @param endTime
     * @return
     */
    List<TempProductRepurchaseActivity> get(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);
}