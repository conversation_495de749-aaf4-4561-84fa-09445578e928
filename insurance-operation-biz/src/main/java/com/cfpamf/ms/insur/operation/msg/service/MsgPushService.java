package com.cfpamf.ms.insur.operation.msg.service;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSON;
import com.cfpamf.common.ms.exception.MSBizNormalException;
import com.cfpamf.ms.insur.operation.base.exception.BusinessException;
import com.cfpamf.ms.insur.operation.base.helper.BmsHelper;
import com.cfpamf.ms.insur.operation.base.helper.LockHelper;
import com.cfpamf.ms.insur.operation.base.response.CommonResult;
import com.cfpamf.ms.insur.operation.base.service.SystemGroovyService;
import com.cfpamf.ms.insur.operation.base.util.AliYunOssUtil;
import com.cfpamf.ms.insur.operation.base.util.DownLoadUtil;
import com.cfpamf.ms.insur.operation.base.util.LambdaUtils;
import com.cfpamf.ms.insur.operation.dingtalk.service.DingTalkRobotService;
import com.cfpamf.ms.insur.operation.fegin.bms.enums.ExcptEnum;
import com.cfpamf.ms.insur.operation.fegin.bms.enums.OperationErrorEnum;
import com.cfpamf.ms.insur.operation.fegin.image.facade.InsuranceImageFacade;
import com.cfpamf.ms.insur.operation.fegin.image.service.DynamicsImageService;
import com.cfpamf.ms.insur.operation.msg.config.RabbitMqMsgQueueConfig;
import com.cfpamf.ms.insur.operation.msg.dao.OpMessagePushMapper;
import com.cfpamf.ms.insur.operation.msg.dao.OpMessagePushPoolMapper;
import com.cfpamf.ms.insur.operation.msg.dao.OpMessageRuleMapper;
import com.cfpamf.ms.insur.operation.msg.dao.OpMessageRuleReceiverMapper;
import com.cfpamf.ms.insur.operation.msg.enums.EnumMessageRulePoolState;
import com.cfpamf.ms.insur.operation.msg.enums.EnumReceiverAuditState;
import com.cfpamf.ms.insur.operation.msg.enums.ImageNatureEnum;
import com.cfpamf.ms.insur.operation.msg.enums.OpMessageReceiverTypeEnum;
import com.cfpamf.ms.insur.operation.msg.pojo.dto.OpMessageRuleDTO;
import com.cfpamf.ms.insur.operation.msg.pojo.dto.OpMessageRuleGroovyDTO;
import com.cfpamf.ms.insur.operation.msg.pojo.dto.Push2UserDTO;
import com.cfpamf.ms.insur.operation.msg.pojo.po.OpMessagePush;
import com.cfpamf.ms.insur.operation.msg.pojo.po.OpMessagePushPool;
import com.cfpamf.ms.insur.operation.msg.pojo.po.OpMessageRule;
import com.cfpamf.ms.insur.operation.msg.pojo.po.OpMessageRuleReceiver;
import com.cfpamf.ms.insur.operation.msg.pojo.query.OpMessageRuleQuery;
import com.cfpamf.ms.insur.operation.msg.service.push.Pusher;
import com.cfpamf.ms.insur.operation.msg.service.push.model.CacheVo;
import com.cfpamf.ms.insur.operation.msg.util.Html2ImageUtils;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.scheduling.support.CronSequenceGenerator;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.validation.Valid;
import java.io.IOException;
import java.time.*;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Supplier;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.joining;
import static java.util.stream.Collectors.toList;

/**
 * <AUTHOR> 2021/7/14 11:43
 */
@Service
@Slf4j
public class MsgPushService {


    public static final String SCRIPT_TYPE = "msg";
    @Autowired
    OpMessageRuleMapper ruleMapper;

    @Autowired
    SystemGroovyService groovyService;


    @Autowired
    DingTalkService dingTalkService;

    @Autowired
    OpMessageRuleReceiverMapper receiverMapper;

    @Autowired
    OpMessagePushMapper pushMapper;

    @Autowired
    InsuranceImageFacade imageFacade;

    @Autowired
    DynamicsImageService dynamicsImageService;

    /**
     * 消息队列
     */
    @Autowired
    RabbitTemplate rabbitTemplate;

    @Autowired
    ObjectMapper objectMapper;

    @Autowired
    OpMessagePushPoolMapper pushPoolMapper;

    @Autowired
    BmsHelper helper;
    @Autowired
    LockHelper lockHelper;

    @Autowired
    ApplicationContext context;


    @Autowired
    EmpPostService empPostService;

    @Autowired
    MsgPushContextService contextService;
    Map<String, Pusher> pusherMap = new ConcurrentHashMap<>();
    private static final String PREVIEW_UPLOAD_OSS_KEY = "%s-%d-%s";
    /**
     * 最小执行时间
     */
    static final long NEXT_MIN_MILLIS = 1000L;
    /**
     * 预览数量
     */
    static final int PREVIEW_NUMS = 5;

    @Autowired
    private AuthUserDingTalkService authUserDingTalkService;

    @Autowired
    DingTalkRobotService robotService;

    /**
     * 找到对应的发布器
     *
     * @param msgType
     * @return
     */
    public Pusher findPusher(String msgType) {
        if (pusherMap.isEmpty()) {
            synchronized (this) {
                pusherMap.putAll(context.getBeansOfType(Pusher.class));
            }
        }
        Pusher pusher = pusherMap.get(msgType + "Pusher");
        if (Objects.isNull(pusher)) {
            throw new UnsupportedOperationException("改消息类型暂不支持:" + msgType);
        }
        return pusher;
    }

    /**
     * 列表查询
     *
     * @param query
     * @return
     */
    public PageInfo<OpMessageRule> listConfig(OpMessageRuleQuery query) {

        final OpMessageRule queryPo = new OpMessageRule();
        queryPo.setRuleName(query.getRuleName());
        queryPo.setRuleCode(query.getRuleCode());
        return PageHelper.startPage(query.getPageNo(), query.getPageSize())
                .doSelectPageInfo(() -> ruleMapper.select(queryPo));
    }

    public OpMessageRuleDTO detail(Long id) {

        final OpMessageRuleDTO dto = new OpMessageRuleDTO();
        final OpMessageRule opMessageRule = ruleMapper.selectByPrimaryKeyMustExists(id);
        dto.setRule(opMessageRule);
        dto.setReceivers(receiverMapper.selectByRuleId(id));
        return dto;
    }

    /**
     * 保存推送配置 并计算下一次执行的时间
     *
     * @param dto
     * @param operator
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveConfig(@Valid OpMessageRuleDTO dto, String operator) {

        final OpMessageRule rule = dto.getRule();
        final boolean isUpdate = Objects.nonNull(rule.getId());
        if (!isUpdate) {
            rule.setState(1);
            rule.setCreateBy(operator);
        }
        rule.setUpdateBy(operator);
        if(rule.getImageNature() == null){
            rule.setImageNature(ImageNatureEnum.STATIC.getCode());
        }
        ruleMapper.insertOrUpdate(rule);

        dto.getReceivers().forEach(receiver ->receiver.setMessageRuleId(rule.getId()));

        final OpMessageRuleReceiver delete = new OpMessageRuleReceiver();
        delete.setMessageRuleId(rule.getId());
        receiverMapper.delete(delete);
        receiverMapper.insertList(dto.getReceivers());

        if (Boolean.TRUE.equals(dto.getAutoPush()) && !initNextPush(rule)) {
            log.warn("初始化首次执行已结束{}", rule);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateConfig(OpMessageRule rule, String update) {
        if (Objects.isNull(rule.getId())) {
            throw new BusinessException(OperationErrorEnum.MSG_CONFIG_ERROR);
        }
        rule.setUpdateBy(update);
        ruleMapper.insertOrUpdate(rule);
    }

    /**
     * 保存消息接受者
     *
     * @param ruleId
     * @param receivers
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveReceivers(final Long ruleId, List<OpMessageRuleReceiver> receivers) {
        //校验id
        ruleMapper.selectByPrimaryKeyMustExists(ruleId);

        receivers.forEach(receiver -> receiver.setMessageRuleId(ruleId));
        final OpMessageRuleReceiver delete = new OpMessageRuleReceiver();
        delete.setMessageRuleId(ruleId);
        receiverMapper.delete(delete);
        receiverMapper.insertList(receivers);
    }


    public boolean initNextPush(OpMessageRule rule) {
        return initNextPush(rule, false);
    }

    /**
     * 计算下一次时间 并发送mq消息
     *
     * @param rule
     */
    public boolean initNextPush(OpMessageRule rule, boolean isForce) {

        OpMessagePushPool pushPool = pushPoolMapper.selectByRuleInited(rule.getId());

        if (Objects.nonNull(pushPool) && isForce) {
            OpMessagePushPool pool = new OpMessagePushPool();
            pool.setId(pushPool.getId());
            pool.setState(EnumMessageRulePoolState.PUSH_FORCE.getCode());
            pushPoolMapper.updateByPrimaryKeySelective(pool);
        } else if (Objects.nonNull(pushPool)) {
            log.warn("已存在未消费的消息");
            return false;
        }
        final Instant configStart = rule.getStartDate().atStartOfDay().atZone(ZoneOffset.systemDefault()).toInstant();
        final Instant now = Instant.now();
        Instant starDate = configStart.isAfter(now) ? configStart : now;
        final Date date = cronNextDate(rule.getCronRule(), Date.from(starDate));

        final Instant execute = date.toInstant();
        final long between = ChronoUnit.MILLIS.between(Instant.now(), execute);
        //如果下次执行的时间大于1秒
        if (between > NEXT_MIN_MILLIS && (isNotEnd(execute, rule.getEndDate()))) {
            //保存缓存信息
            final OpMessagePushPool pool = new OpMessagePushPool();
            pool.setState(EnumMessageRulePoolState.INITED.getCode());
            pool.setMessageRuleId(rule.getId());
            pool.setExecTime(LocalDateTime.ofInstant(execute, ZoneId.systemDefault()));
            pool.setContextId("");
            pool.setRemark("");
            pushPoolMapper.insertOrUpdate(pool);
            return true;
        } else {
            log.info("推送过于频繁 或已经截止了");
        }
        return false;
    }

    /**
     * 下次执行时间是否 未结束
     *
     * @param execTime 下次执行时间
     * @param endDate  结束时间
     * @return
     */
    public boolean isNotEnd(Instant execTime, LocalDate endDate) {
        if (Objects.isNull(endDate)) {
            return true;
        }
        final Instant dateTime = endDate.plusDays(1L).atStartOfDay().atZone(ZoneOffset.systemDefault()).toInstant();
        return dateTime.isAfter(execTime);
    }

    /**
     * 调试脚本文件
     *
     * @param script
     * @return
     */
    public String debugGroovy(String script, Long ruleId) {
        final OpMessageRule rule = ruleMapper.selectByPrimaryKeyMustExists(ruleId);
        return (String) groovyService.executeTemp(script, rule);
    }

    /**
     * 调试模板文件
     *
     * @param ftlContent
     * @param params
     * @return
     * @throws Exception
     */
    public String debugTemp(String ftlContent, Map<String, Object> params) throws Exception {

        return Html2ImageUtils.processTemplateInfoString(ftlContent, params);
    }

    public void initNextPush(Long ruleId) {
        initNextPush(ruleId, false);
    }
    /**
     * 计算下一次推送的时间 并推送消息到队列
     *
     * @param ruleId
     */
    public void initNextPush(Long ruleId,boolean isForce) {
        final OpMessageRule rule = ruleMapper.selectByPrimaryKeyMustExists(ruleId);
        if (!rule.isActive()) {
            log.warn("{}:{}已经停用了", rule.getId(), rule.getRuleName());
            return;
        }
        boolean b = initNextPush(rule, isForce);
        log.warn("生成推送数据结果 :{}", b);
    }

    public void pushMessage(Long ruleId, boolean autoNext) throws IOException {
        pushMessage(ruleId, "Auto-" + System.currentTimeMillis(), autoNext, false);

    }

    /**
     * 推送消息
     *
     * @throws IOException
     */
    public void pushMessage(Long ruleId, String contextId, boolean autoNext, boolean deplay) {

        //锁住
        lockHelper.lockRun("ins:ope:pushMessage:" + ruleId, () -> {
            log.info("开始推送消息{} {}", ruleId, contextId);
            final OpMessageRule rule = ruleMapper.selectByPrimaryKeyMustExists(ruleId);
            if (!rule.isActive()) {
                log.warn("{}已经停用了", rule);
                return;
            }
            try {
                pushMessageCore(rule, contextId, deplay);
            }catch (Exception e){
                log.error("消息推送任务执行失败:{},{}",rule,contextId,e);
            }
            //计算下一次时间 并推送消息
            if (autoNext) {
                MsgPushService that = (MsgPushService) AopContext.currentProxy();
                that.initNextPush(rule);
            }
        });

    }


    /**
     * @param pushes   需要推送的消息
     * @param nextAuto 下次推送自动过
     */
    public void pushAuditMessage(List<Long> pushes, boolean nextAuto) {

        if (CollectionUtils.isEmpty(pushes)) {
            return;
        }
        List<OpMessagePush> opMessagePushes = pushMapper.selectByIds(pushes.stream().map(String::valueOf).collect(joining(",")));

        //根据规则分组
        Map<Long, List<OpMessagePush>> pushMap = LambdaUtils.groupBy(opMessagePushes, OpMessagePush::getMessageRuleId);

        List<OpMessageRule> opMessageRules = ruleMapper.selectByIds(pushMap.keySet().stream().map(String::valueOf).collect(joining(",")));

        opMessageRules.forEach(rule -> {
            findPusher(rule.getMessageType()).pushAudit(pushMap.get(rule.getId()));
            receiverMapper.updateAuditState(rule.getId(), EnumReceiverAuditState.AUTO.getCode());
        });
    }

    /**
     * 推送核心逻辑
     *
     * @param rule
     * @param contextId
     * @param delay
     */
    public void pushMessageCore(OpMessageRule rule, String contextId, boolean delay) {
        //
        List<OpMessageRuleReceiver> opMessageRuleReceivers = receiverMapper.selectByRuleId(rule.getId());

        if (CollectionUtils.isEmpty(opMessageRuleReceivers)) {
            throw new MSBizNormalException(ExcptEnum.SYS_CONFIG_ERROR_900002.getCode(), "消费者不能为空");
        }
        //消费者转换
        List<OpMessageRuleReceiver> receivers = opMessageRuleReceivers.stream()
                .map(dbReceiver -> {
                    if (OpMessageReceiverTypeEnum.CHAT.isMe(dbReceiver.getReceiverType())
                            || OpMessageReceiverTypeEnum.PERSON.isMe(dbReceiver.getReceiverType())) {
                        return Collections.singletonList(dbReceiver);
                    } else if (OpMessageReceiverTypeEnum.POST.isMe(dbReceiver.getReceiverType())) {
                        //如果是推送到岗位就从bms查询当前岗位人员的钉钉用户
                        return empPostService.postNameCvt(rule, dbReceiver, dbReceiver.getReceiverName());
                    } else if (OpMessageReceiverTypeEnum.CONTEXT.isMe(dbReceiver.getReceiverType())) {
                        return contextService.contextParse(rule, dbReceiver);
                    } else if (OpMessageReceiverTypeEnum.CHAT_CONTEXT.isMe(dbReceiver.getReceiverType())) {
                        //该推送类型会推送到目标群,开发配置时需要注意"钉钉和场景"这种类型不能与其他推送方式混合配置,
                        //该功能是开发人员使用并已告知其他开发人员,后续会加个配置注意事项文档特别说明
                        return contextService.contextParse(rule, dbReceiver);
                    }
                    return Collections.<OpMessageRuleReceiver>emptyList();
                }).flatMap(Collection::stream).collect(toList());

        //执行规则
        log.info("消息接受者去重后:{}-{}-{}", rule.getId(), rule.getMessageType(), receivers.size());
        findPusher(rule.getMessageType()).push(rule, contextId, delay, receivers);
        //如果是延迟消息的推送 就标记
        if (delay) {
            updatePushState(rule.getId(), contextId);
        }
    }

    /**
     * 将推送池的数据标记成已推送
     *
     * @param ruleId
     * @param contextId
     */
    private void updatePushState(Long ruleId, String contextId) {
        //这里只能修改 执行时间 <= now 的数据
        log.info("updatePushState:{} {}", ruleId, contextId);
        final OpMessagePushPool dbData = pushPoolMapper.selectByRuleInitedAndBefore(ruleId);
        if (Objects.nonNull(dbData)) {
            OpMessagePushPool update = new OpMessagePushPool();
            update.setId(dbData.getId());
            update.setContextId(contextId);
            update.setState(EnumMessageRulePoolState.PUSHED.getCode());
            pushPoolMapper.updateByPrimaryKeySelective(update);
        } else {
            log.warn("updatePushState:{} {} 数据库数据不存在", ruleId, contextId);
        }
    }

    /**
     * 发送延迟消息
     *
     * @param message
     * @param deplay
     */
    public void sendDelayMessage(Object message, Integer deplay) {
        log.info("sendDelayMessage {}:{}", deplay, message);
        rabbitTemplate.convertAndSend(RabbitMqMsgQueueConfig.DELAYED_MSG_EXCHANGE_NAME,
                RabbitMqMsgQueueConfig.DELAYED_ROUTING_KEY, message,
                a -> {
                    a.getMessageProperties().setDelay(deplay);
                    a.getMessageProperties().setMessageId(UUID.randomUUID().toString());
                    return a;
                });

    }

    /**
     * 发送消息
     *
     *
     */
    public void sendMessage() {
        final List<OpMessagePushPool> rules = pushPoolMapper.selectEnableRules();
        log.info("需发送mq的推送列表:{}", JSONObject.toJSONString(rules));
        List<OpMessagePushPool> filterList = Optional.ofNullable(rules).orElse( Collections.emptyList()).stream().filter(
                rule -> rule.getExecTime().isBefore(LocalDateTime.now())
        ).collect(toList());

        if (!CollectionUtils.isEmpty(filterList)) {
            pushPoolMapper.updatePushMqStateByIds(filterList.stream().map(OpMessagePushPool::getId).collect(toList()));
            filterList.forEach(rule -> sendDelayMessage(rule.getMessageRuleId(), 0));
        }
    }

    /**
     * 保存推送记录
     *
     * @param receiver
     * @param content
     * @param messageId
     * @param contextId
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveMessagePush(OpMessageRuleReceiver receiver, String content, String messageId, String contextId) {
        final OpMessagePush push = new OpMessagePush();
        push.setMessageId(messageId);
        push.setContextId(contextId);
        push.setReceiver(receiver.getReceiver());
        push.setReceiverType(receiver.getReceiverType());
        push.setMessageRuleId(receiver.getMessageRuleId());
        push.setContent(content);
        pushMapper.insertUseGeneratedKeys(push);
    }

    /**
     * 发送图片
     *
     * @param receiver
     * @param imageMediaId
     * @return
     */
    public String sendImage(OpMessageRuleReceiver receiver, String imageMediaId) {
        if (OpMessageReceiverTypeEnum.CHAT.isMe(receiver.getReceiverType())) {
            //目前仅支持发送到群
            return dingTalkService.sendChatMediaMessage(receiver.getReceiver(), imageMediaId);
        }
        throw new UnsupportedOperationException("目前仅支持发送图片消息到群");
    }

    /**
     * 计算cron表达式的下五次执行时间
     *
     * @param cron
     * @param startDate
     * @return
     */
    public static List<Date> cronNextDateFive(String cron, Date startDate) {
        final CronSequenceGenerator generator = new CronSequenceGenerator(cron);
        Date start = Objects.isNull(startDate) ? new Date() : startDate;

        List<Date> nexts = Lists.newArrayListWithCapacity(PREVIEW_NUMS);
        for (int i = 0; i < PREVIEW_NUMS; i++) {
            start = generator.next(start);
            nexts.add(start);
        }
        return nexts;
    }

    public static Date cronNextDate(String cron, Date startDate) {
        final CronSequenceGenerator generator = new CronSequenceGenerator(cron);
        return generator.next(startDate);
    }

    public void clearCache(String code) {
        groovyService.cleanCache(SCRIPT_TYPE, code);
    }

    public void clearCacheByType(String type, String code) {
        groovyService.cleanCache(type, code);
    }

    public List<OpMessageRule> queryByGroovyCode(String groovyCode) {
        return ruleMapper.selectListByCode(groovyCode);
    }

    public String retainTemplatePreviewUrl(OpMessageRuleGroovyDTO params) {

        String html = (String) groovyService.executeForCode(MsgPushService.SCRIPT_TYPE, params.getRuleCode(), params);
        // 生成图片
        final byte[] bytes = Html2ImageUtils.html2Image(html);

        String s = AliYunOssUtil.uploadByBytes(
                bytes,
                String.format(
                        PREVIEW_UPLOAD_OSS_KEY,
                        "PREVIEW",
                        System.currentTimeMillis(),
                        UUID.randomUUID().toString().replaceAll("-", "")
                )
        );
        log.info("上传预览图片地址为：{}", s);
        return s;
    }

    /**
     * 推送用户钉钉消息
     * @param push2UserDTO
     */
    public void push2User(Push2UserDTO push2UserDTO){
        Map<String, String> userIdMap = authUserDingTalkService.getJobNumberDingUserIdMap(Lists.newArrayList(push2UserDTO.getJobNumbers()));
        if(org.springframework.util.CollectionUtils.isEmpty(userIdMap)){
            return;
        }
        Set<String> userIds = userIdMap.values().stream().collect(Collectors.toSet());
        String processQueryKey = robotService.pushMarkdown2User(Lists.newArrayList(userIds),push2UserDTO.getContent(),push2UserDTO.getTitle());
        log.info("processQueryKey= 【{}】; request= 【{}】",processQueryKey, JSON.toJSONString(processQueryKey));
        if(StringUtils.isBlank(processQueryKey)){
            throw new MSBizNormalException(ExcptEnum.PARAMS_ERROR.getCode(),"推送用户钉钉消息失败");
        }
    }

    public void pushImg2User(Push2UserDTO push2UserDTO){
        Map<String, String> userIdMap = authUserDingTalkService.getJobNumberDingUserIdMap(Lists.newArrayList(push2UserDTO.getJobNumbers()));
        if(org.springframework.util.CollectionUtils.isEmpty(userIdMap)){
            return;
        }
        Set<String> userIds = userIdMap.values().stream().collect(Collectors.toSet());
        log.info("pushMessageTo: "+JSONObject.toJSONString(userIds));
        String processQueryKey = robotService.pushImg2User(new ArrayList<>(userIds),push2UserDTO.getImageUrl());
        log.info("processQueryKey= 【{}】; request= 【{}】",processQueryKey, JSON.toJSONString(processQueryKey));
        if(StringUtils.isBlank(processQueryKey)){
            throw new MSBizNormalException(ExcptEnum.PARAMS_ERROR.getCode(),"推送用户钉钉消息失败");
        }
    }

    public CacheVo getImageId(String imageParams, String html,String imageNature) {
        JSONObject json = new JSONObject();
        if(imageParams != null){
            JSONObject ps = JSONObject.parseObject(imageParams);
            if (ps.containsKey("imgParam")) {
                json.putAll(ps.getJSONObject("imgParam"));
            }
        }
        json.put("htmlValue", html);
        json.put("imageNature", imageNature);
        //String url = apiCall(() -> imageFacade.image(json)).getUrl();
        String url = apiCall(() -> dynamicsImageService.image(json)).getUrl();
        byte[] bytes = DownLoadUtil.downloadByUrlToByte(url);
        log.info("图片缓存到oss成功{}", url);
        return new CacheVo(dingTalkService.uploadImage(bytes), url);
    }

    public String parseTemplate(String templateContent, Object model) {
        return Html2ImageUtils.processTemplateInfoString(templateContent, model);
    }

    public String pushImageMsg2User(Push2UserDTO push2UserDTO){
        String templateParams = push2UserDTO.getTemplateParams();
        JSONObject templateParamsJson = new JSONObject();
        templateParamsJson.put("data",templateParams);
        String templateResult = this.parseTemplate(push2UserDTO.getTemplate(), templateParamsJson);
        log.info("htmlResult: {}",templateResult);
        CacheVo vo = this.getImageId(push2UserDTO.getImageNature(),push2UserDTO.getImageParams(), templateResult);
        push2UserDTO.setMsgType("image");
        push2UserDTO.setImageUrl(vo.getImageId());
        this.pushImg2User(push2UserDTO);
        return "success";
    }
    private <T> T apiCall(Supplier<CommonResult<T>> api) {
        CommonResult<T> tCommonResult = api.get();
        if (tCommonResult.isSuccess()) {
            return tCommonResult.getData();
        }
        throw new MSBizNormalException(tCommonResult.getErrorCode(), tCommonResult.getMessage());
    }

    /**
     * 重置消息状态
     * @param id
     */
    public void initMessage(Integer id) {
        OpMessagePushPool messagePool =  pushPoolMapper.selectByPrimaryKey(id);
        if(messagePool==null){
            log.info("任务消息不存在:{}",id);
            return;
        }
        int r = pushPoolMapper.initPushState(id);
        log.info("任务消息更新完成:{},{}",id,r);
    }
}
