package com.cfpamf.ms.insur.operation.phoenix.service;

import com.alibaba.fastjson.JSON;
import com.cfpamf.common.ms.exception.MSBizNormalException;
import com.cfpamf.ms.insur.common.feign.push.TemplateMessageFacade;
import com.cfpamf.ms.insur.common.feign.push.enums.ReceiverTypeEnum;
import com.cfpamf.ms.insur.common.feign.push.request.NormalBizMessageTemplateReq;
import com.cfpamf.ms.insur.operation.phoenix.enums.EnumMessageCode;
import com.google.common.collect.Maps;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.HashMap;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @since 2023/5/6 09:30
 */
@Service
@Slf4j
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class PhoenixMessageService {

    TemplateMessageFacade facade;

    StringRedisTemplate redisTemplate;


    private static final String REDIS_KEY_PREFIX_CUST = "insur:operation:phoenix:interruption:sms:";

    private static final Long MAX_SMS_PHONE = 20L;
    private static final Long MAX_SMS_RISK = 2L;

    public void sendMessage(String phone, Long customerId, String custName, String riskName, String url) {

        //  控制单人单险种每天只能发送2条
        Long increment = Optional.ofNullable(redisTemplate.opsForValue().increment(REDIS_KEY_PREFIX_CUST + phone + ":total")).orElse(0L);
        if (increment > MAX_SMS_PHONE) {
            throw new MSBizNormalException("400", "该手机号已发送20次短信，无法继续发送！");
        }
        Long riskIncrement = Optional.ofNullable(redisTemplate.opsForValue().increment(REDIS_KEY_PREFIX_CUST  + customerId + ":" + riskName)).orElse(0L);

        if (riskIncrement > MAX_SMS_RISK) {
            throw new MSBizNormalException("400", "您今天已给客户发过2次短信了哦~~");
        }
        LocalDateTime endOfToDay = LocalDate.now().plusDays(1L).atStartOfDay();
        long between = ChronoUnit.SECONDS.between(endOfToDay, LocalDateTime.now());
        redisTemplate.expire(REDIS_KEY_PREFIX_CUST + phone, between, TimeUnit.SECONDS);

        HashMap<String, String> params = Maps.newHashMapWithExpectedSize(3);
        params.put("personName", custName);
        params.put("riskName", riskName);
        params.put("url", url);
        sendSms(phone, params, EnumMessageCode.SAFES_CUSTOMER_INTERRUPTION);
    }

    public void sendSms3Message(String phone, Long customerId, String custName, String riskName, String url) {

        //  控制单人单险种每天只能发送2条
        Long increment = Optional.ofNullable(redisTemplate.opsForValue().increment(REDIS_KEY_PREFIX_CUST + phone + ":total")).orElse(0L);
        if (increment > MAX_SMS_PHONE) {
            throw new MSBizNormalException("400", "该手机号已发送20次短信，无法继续发送！");
        }
        Long riskIncrement = Optional.ofNullable(redisTemplate.opsForValue().increment(REDIS_KEY_PREFIX_CUST  + customerId + ":" + riskName)).orElse(0L);

        if (riskIncrement > MAX_SMS_RISK) {
            throw new MSBizNormalException("400", "您今天已给客户发过2次短信了哦~~");
        }
        LocalDateTime endOfToDay = LocalDate.now().plusDays(1L).atStartOfDay();
        long between = ChronoUnit.SECONDS.between(endOfToDay, LocalDateTime.now());
        redisTemplate.expire(REDIS_KEY_PREFIX_CUST + phone, between, TimeUnit.SECONDS);

        HashMap<String, String> params = Maps.newHashMapWithExpectedSize(1);
        params.put("url", url);
        sendSms3(phone, params, riskName);
    }

    private void sendSms3(String mobile, HashMap<String, String> params, String riskName) {
        try {
            log.info("发送短信{} {} {}", mobile, riskName, JSON.toJSONString(params));
            NormalBizMessageTemplateReq normalBizMessageTemplateReq = new NormalBizMessageTemplateReq();
            normalBizMessageTemplateReq.setReceiver(mobile);
            normalBizMessageTemplateReq.setReceiverTypeEnum(ReceiverTypeEnum.CUSTMER_MOBILE);
            normalBizMessageTemplateReq.setBizType(riskName);
            normalBizMessageTemplateReq.setBizParams(params);
            facade.bizTemplateSend(normalBizMessageTemplateReq);
        } catch (Exception e) {
            log.error("发送短信{}失败{}  {}", riskName, mobile, JSON.toJSONString(params), e);
        }
    }

    /**
     * 发送短信
     *
     * @param mobile 手机号码
     * @param params 模板参数
     * @param code   业务编码
     */
    private void sendSms(String mobile, HashMap<String, String> params, EnumMessageCode code) {
        try {
            log.info("发送短信{} {} {}", mobile, code, JSON.toJSONString(params));
            NormalBizMessageTemplateReq normalBizMessageTemplateReq = new NormalBizMessageTemplateReq();
            normalBizMessageTemplateReq.setReceiver(mobile);
            normalBizMessageTemplateReq.setReceiverTypeEnum(ReceiverTypeEnum.CUSTMER_MOBILE);
            normalBizMessageTemplateReq.setBizType(code.getCode());
            normalBizMessageTemplateReq.setBizParams(params);
            facade.bizTemplateSend(normalBizMessageTemplateReq);
        } catch (Exception e) {
            log.error("发送短信{}失败{}  {}", code, mobile, JSON.toJSONString(params), e);
        }
    }
}
