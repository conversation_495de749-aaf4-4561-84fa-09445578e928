package com.cfpamf.ms.insur.operation.pco.query;

import com.cfpamf.ms.insur.operation.base.form.DataAuthPageForm;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import java.time.LocalDate;

/**
 * <AUTHOR> 2022/9/8 14:30
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class PcoDayFormQuery extends DataAuthPageForm {

    LocalDate startDate;

    LocalDate endDate;

    @ApiModelProperty("pco job code")
    String jobCode;

    String orderBy;
}
