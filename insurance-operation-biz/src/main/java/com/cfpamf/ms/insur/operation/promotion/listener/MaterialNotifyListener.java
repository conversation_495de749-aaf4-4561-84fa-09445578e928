package com.cfpamf.ms.insur.operation.promotion.listener;


import com.alibaba.fastjson.JSON;
import com.cfpamf.ms.insur.operation.base.mq.RabbitMqUtils;
import com.cfpamf.ms.insur.operation.promotion.dto.MarketingListMqDTO;
import com.cfpamf.ms.insur.operation.promotion.dto.MaterialMessage;
import com.cfpamf.ms.insur.operation.promotion.service.MaterialNotifyService;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.ExchangeTypes;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.validation.Valid;

@Component
@Slf4j
public class MaterialNotifyListener {
    @Resource
    private RabbitMqUtils rabbitMqUtils;

    @Autowired
    private MaterialNotifyService materialNotifyService;
    @RabbitListener(
            bindings = @QueueBinding(value = @Queue(value = "${marketing-info.queue}", durable = "true"),
                    exchange = @Exchange(value = "${marketing-info.exchange}", type = ExchangeTypes.FANOUT)
            ),
            containerFactory = "insuranceOperationFactory")
    public void consume(@Payload MaterialMessage msg, @Header(AmqpHeaders.DELIVERY_TAG) long deliveryTag,
                        Channel channel) {
        try{
            //String content = new String(msg.getBody());
            log.info("接收到素材变更通知:{}", JSON.toJSONString(msg));
            //MarketingListMqDTO mqDTO = JSON.parseObject(content,MarketingListMqDTO.class);
            MarketingListMqDTO mqDTO = msg.getBody();
            materialNotifyService.saveNotify(mqDTO);
            log.info("素材变更通知入库完成");
        } catch(Exception e) {
            log.error("接收到素材变更通知异常: {}", e);
        } finally {
            rabbitMqUtils.manualAcknowledgeMode(true, deliveryTag, channel, false);
        }

    }
}
