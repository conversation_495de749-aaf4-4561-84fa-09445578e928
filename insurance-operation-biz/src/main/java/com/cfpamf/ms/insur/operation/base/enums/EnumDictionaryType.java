package com.cfpamf.ms.insur.operation.base.enums;

import com.cfpamf.ms.insur.operation.base.annotaions.Dictionary;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.experimental.FieldDefaults;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR> 2020/7/14 10:05
 */
@Getter
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
@Dictionary
public enum EnumDictionaryType {

    //产品
    PRODUCT_GROUP("productGroup", "产品分类"),
    PRICE_FACTOR("priceFactor", "价格因素"),
    PRODUCT_TYPE("productType", "商品种类"),
    CHANNEL("channel", "产品渠道"),
    CONFIG("config", "系统配置"),
    PRODUCT_ATTR("productAttr", "产品属性"),
    //人管
    HR_MARRIAGE("hr_marriage", "婚姻状况"),
    HR_POLITICS_STATUS("hr_politics_status", "政治面貌"),
    HR_EDUCATION("hr_education", "文化程度"),
    HR_ID_TYPE("hr_id_type", "证件类型"),
    HR_SEX("hr_sex", "性别"),
    HR_NATION("hr_nation", "民族"),

    ;

    final String code;
    final String desc;

    /**
     * 获取所有人管相关的 type码值
     *
     * @return
     */
    public static List<String> getAllHrType() {
        return Arrays.asList(HR_MARRIAGE.code,
                HR_POLITICS_STATUS.code,
                HR_EDUCATION.code,
                HR_ID_TYPE.code,
                HR_SEX.code,
                HR_NATION.code);
    }
}
