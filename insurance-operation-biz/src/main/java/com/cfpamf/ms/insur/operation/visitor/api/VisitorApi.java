package com.cfpamf.ms.insur.operation.visitor.api;

import com.cfpamf.ms.insur.operation.visitor.form.ProductVisitRecordSearchForm;
import com.cfpamf.ms.insur.operation.visitor.form.TimeSearchForm;
import com.cfpamf.ms.insur.operation.visitor.vo.*;
import com.github.pagehelper.PageInfo;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 访客api
 *
 * <AUTHOR>
 * @date 2021/4/30 14:53
 */
@RequestMapping("/operation/visitor")
public interface VisitorApi {
    /**
     * 查找访客栏数据概览
     *
     * @param deadlineString
     * @return
     */
    @GetMapping(value = "/simple/statistics/{deadline}")
    VisitorSimpleStatisticsVo getVisitorSimpleStatistics(@PathVariable("deadline") String deadlineString);

    /**
     * 时间搜索访客信息
     *
     * @param timeSearchForm
     * @return
     */
    @PostMapping(value = "/time/search")
    PageInfo<VisitorRecordVo> searchVisitorRecord(@RequestBody @Valid TimeSearchForm timeSearchForm);

    /**
     * 获取访客详情
     *
     * @param wxOpenId
     * @return
     */
    @GetMapping(value = "/detail/{wxOpenId}")
    VisitorDetailVo getVisitorDetail(@PathVariable String wxOpenId);

    /**
     * 查找访客查看产品列表
     *
     * @return
     */
    @GetMapping("/product")
    List<ProductVisitVo> findProductVisitRecord();

    /**
     * 产品访客详情
     *
     * @param productVisitRecordSearchForm
     * @return
     */
    @PostMapping("/product/search")
    PageInfo<VisitorProductRecordVo> searchVisitorProductRecord(@RequestBody @Valid ProductVisitRecordSearchForm productVisitRecordSearchForm);
}
