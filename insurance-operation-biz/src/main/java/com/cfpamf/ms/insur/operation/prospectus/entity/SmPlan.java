package com.cfpamf.ms.insur.operation.prospectus.entity;

import com.cfpamf.ms.insur.operation.base.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.Date;

/**
 * sm_plan
 *
 * <AUTHOR>
@Table(name = "sm_plan")
@Getter
@Setter
@ApiModel("产品计划")
public class SmPlan extends BaseEntity {


    /**
     * 内部产品Id
     */
    @Column(name = "productId")
    @ApiModelProperty("产品id")
    private Long productId;

    /**
     * 计划名称
     */
    @Column(name = "productName")
    @ApiModelProperty("计划名")
    private String planName;

    /**
     * 计划描述
     */
    @ApiModelProperty("计划描述")
    private String description;

    /**
     * 微信下单时传的第三方产品Id
     */
    @Column(name = "fhProductId")
    @ApiModelProperty("微信下单时传的第三方产品Id")
    private String fhProductId;

    /**
     * 计划见费出单(seeFee)/非见费出单(nonSeefee)
     */
    @Column(name = "planOrderOutType")
    @ApiModelProperty("计划见费出单(seeFee)/非见费出单(nonSeefee)")
    private String planOrderOutType;

}