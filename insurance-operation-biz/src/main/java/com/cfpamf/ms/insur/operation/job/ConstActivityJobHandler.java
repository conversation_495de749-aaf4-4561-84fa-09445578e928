package com.cfpamf.ms.insur.operation.job;

import com.cfpamf.ms.insur.operation.activity.service.impl.SmActivityRewardConstServiceImpl;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 手动规则job
 * <AUTHOR>
 */
@Slf4j
@Component
public class ConstActivityJobHandler {

    @Autowired
    SmActivityRewardConstServiceImpl constService;

    /**
     * 手动配置脚本
     */
    @XxlJob("const-activity-programme")
    public void execute() {
        String s = XxlJobHelper.getJobParam();
        if (StringUtils.isNotBlank(s)) {
            constService.handleConstActivityBySaId(Long.valueOf(s));
        } else {
            constService.handleAllConstActivity();
        }

    }


}
