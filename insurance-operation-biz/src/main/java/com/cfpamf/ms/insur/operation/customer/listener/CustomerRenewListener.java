package com.cfpamf.ms.insur.operation.customer.listener;

import com.alibaba.fastjson.JSON;
import com.cfpamf.ms.insur.operation.base.mq.RabbitMqUtils;
import com.cfpamf.ms.insur.operation.customer.dto.PolicyInfoNotifyMessage;
import com.cfpamf.ms.insur.operation.customer.service.CustomerCustFirstService;
import com.cfpamf.ms.insur.operation.customer.service.CustomerRenewService;
import com.rabbitmq.client.Channel;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.ExchangeTypes;
import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;

import javax.validation.Valid;

/**
 * <AUTHOR>
 * 通过监听处理断保客户激活逻辑
 */
@Component
@Slf4j
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PUBLIC, makeFinal = true)
public class CustomerRenewListener {

    CustomerRenewService customerRenewService;

    RabbitMqUtils rabbitMqUtils;

    CustomerCustFirstService customerCustFirstService;

    @RabbitListener(bindings = @QueueBinding(
            value = @Queue(value = "${policy-info.queue}", durable = "true"),
            exchange = @Exchange(value = "${policy-info.exchange}", type = ExchangeTypes.FANOUT, durable = "true")), containerFactory = "insuranceOperationFactory")
    void grantCoupons(@Payload @Valid PolicyInfoNotifyMessage policyInfoNotifyMessage, @Header(AmqpHeaders.DELIVERY_TAG) long deliveryTag, Channel channel) {
        try {
            log.info("消息消费，消息内容{}", JSON.toJSONString(policyInfoNotifyMessage));
            //持久化消息
            customerRenewService.handleMessage(policyInfoNotifyMessage);
            //A类客户转化
            customerCustFirstService.handleMessage(policyInfoNotifyMessage);
        } finally {
            rabbitMqUtils.manualAcknowledgeMode(true, deliveryTag, channel, false);
        }
    }
}
