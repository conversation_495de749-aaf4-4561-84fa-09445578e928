package com.cfpamf.ms.insur.operation.activity.entity;

import com.cfpamf.ms.insur.operation.activity.enums.EnumActivityTriggerType;
import com.cfpamf.ms.insur.operation.activity.enums.RewardType;
import com.cfpamf.ms.insur.operation.reward.dto.RewardDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * sm_activity_reward
 *
 * <AUTHOR>
@ApiModel(value = "活动奖励表")
@Table(name = "sm_activity_reward")
@Data
@NoArgsConstructor
public class SmActivityReward {


    /**
     * 数据标识：加佣为：订单Id,送券为：用户id 红包为：用户id
     */
    @ApiModelProperty(value = "奖励对象数据标识：加佣为：订单Id,送券为：用户id 红包：用户id")
    private String dataId;

    /**
     * 渠道类型 JOB-任务 LISTEN-监听
     */
    @ApiModelProperty(value = "渠道类型 JOB-任务 LISTEN-监听")
    @Enumerated(EnumType.STRING)
    @Column(name = "channel_type")
    private EnumActivityTriggerType channelType;

    /**
     * 奖励类型 ADD_COMMISSION-加佣 COUPON_DELIVERY-送券
     */
    @ApiModelProperty(value = "奖励类型 ADD_COMMISSION-加佣 COUPON_DELIVERY-送券")
    @Enumerated(EnumType.STRING)
    @Column(name = "reward_type")
    private RewardType rewardType;

    /**
     * uuid 为数据类型和数据标识生成的唯一id dataType-dataId 如POLICY-AI12331231
     */
    @ApiModelProperty("uuid")
    String uuid;
    /**
     * 奖励数量
     */
    @ApiModelProperty(value = "奖励数量")
    private BigDecimal proportion;

    /**
     * 活动Id
     */
    @ApiModelProperty(value = "活动Id")
    private Long saId;

    /**
     * 营销活动产品规则id
     */
    @ApiModelProperty(value = "营销活动产品规则id")
    private Long systemActivityProductId;

    private Integer enabledFlag;
    /**
     * 主键id
     */
    @Id
    @OrderBy("desc")
    protected Integer id;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private LocalDateTime createTime;


    public SmActivityReward(RewardDTO rewardDTO, EnumActivityTriggerType channelType, RewardType rewardType, Long saId) {
        this.dataId = rewardDTO.getDataId();
        this.channelType = channelType;
        this.rewardType = rewardType;
        this.proportion = rewardDTO.getProportion();
        this.saId = saId;
        this.systemActivityProductId = rewardDTO.getSystemActivityProductId();
        this.createTime = LocalDateTime.now();
        this.uuid = rewardDTO.getUuid();
        this.enabledFlag = 0;
    }

    public SmActivityReward(RewardDTO rewardDTO, EnumActivityTriggerType job, RewardType rewardType, Long saId, Long systemActivityProductId) {
        this.dataId = rewardDTO.getDataId();
        this.channelType = job;
        this.rewardType = rewardType;
        this.proportion = rewardDTO.getProportion();
        this.saId = saId;
        this.systemActivityProductId = systemActivityProductId;
        this.createTime = LocalDateTime.now();
        this.uuid = rewardDTO.getUuid();
        this.enabledFlag = 0;
    }
}