package com.cfpamf.ms.insur.operation.customer.dto;

import com.cfpamf.common.ms.util.DateUtil;
import com.cfpamf.ms.insur.operation.base.constant.BaseConstants;
import com.github.pagehelper.Page;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Objects;

/**
 * <AUTHOR> 2022/12/14 11:54
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
@ApiModel
public class CustomerPolicyDto {
    private static final long serialVersionUID = 5223281975751145874L;
    /**
     * orderId
     */
    @ApiModelProperty(value = "orderId")
    Integer policyId;

    /**
     * 订单号
     */
    @ApiModelProperty(value = "订单号")
    String fhOrderId;

    /**
     * 投保产品名称
     */
    @ApiModelProperty(value = "投保产品名称")
    String productName;

    /**
     * 投保计划名称
     */
    @ApiModelProperty(value = "投保计划名称")
    String planName;

    /**
     * 投保人姓名
     */
    @ApiModelProperty(value = "投保人姓名")
    String applicantPersonName;

    /**
     * 被保人姓名
     */
    @ApiModelProperty(value = "被保人姓名")
    String insuredPersonName;

    /**
     * 订单金额
     */
    @ApiModelProperty(value = "订单金额")
    BigDecimal totalAmount;

    /**
     * 订单状态
     */
    @ApiModelProperty(value = "订单状态")
    String payStatus;

    /**
     * 保单状态
     */
    @ApiModelProperty(value = "保单状态")
    String appStatus;

    /**
     * 订单创建日期
     */
    @ApiModelProperty(value = "订单创建日期")
    String createTime;

    /**
     * 保险开始时间
     */
    @ApiModelProperty(value = "保险开始时间")
    String startTime;

    /**
     * 保险结束时间（%Y-%m-%d）
     */
    @ApiModelProperty(value = "保险结束时间")
    String endTime;

    /**
     * 保险结束时间（%Y-%m-%d %H:%i:%s）
     */
    @ApiModelProperty(value = "保险结束时间")
    String fullEndTime;

    /**
     * 保单号
     */
    @ApiModelProperty(value = "保单号")
    String policyNo;

    @ApiModelProperty("显示状态")
    String showStatusName;

    @ApiModelProperty("管护客户经理")
    String customerAdmin;

    /**
     * 保单下载地址
     */
    @ApiModelProperty(value = "保单下载地址")
    String downloadURL;

    public String getAppStatus() {
        if (Objects.equals(appStatus, BaseConstants.POLICY_STATUS_SUCCESS)
                && DateUtil.parseDate(endTime, DateUtil.CN_YEAR_MONTH_DAY_FORMAT).compareTo(new Date()) < 0) {
            return BaseConstants.POLICY_STATUS_INVALID;
        }
        return appStatus;
    }

    BigDecimal amount;

    String productType;

    String productTypeName;
}
