package com.cfpamf.ms.insur.operation.activity.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 微信活动列表VO
 * <AUTHOR>
 */
@Data
@ApiModel
public class WxActivitySimpleVo {

    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private Integer saId;

    /**
     * 活动标题
     */
    @ApiModelProperty("活动标题")
    private String title;

    /**
     * 列表图片
     */
    @ApiModelProperty("列表图片")
    private String imageUrl;

    /**
     * 活动时间From
     */
    @ApiModelProperty("活动时间From")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    /**
     * 活动时间To
     */
    @ApiModelProperty("活动时间To")
    private Date endTime;

    @ApiModelProperty(value = "背景图")
    private String backgroundImageUrl;

    @ApiModelProperty(value = "活动类型 NORMAL-普通活动 COMMISSION-加佣 TICKET-抽奖券 RED_ENVELOPE红包")
    private String type;
}
