package com.cfpamf.ms.insur.operation.xj.service.impl;

import cn.hutool.http.HttpUtil;
import com.cfpamf.ms.insur.operation.base.util.Base64Util;
import com.cfpamf.ms.insur.operation.fegin.wx.request.WxaCodeCreateInput;
import com.cfpamf.ms.insur.operation.phoenix.api.model.WhaleResp;
import com.cfpamf.ms.insur.operation.xj.service.WhalePublicApiService;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;

@Service
@Slf4j
public class WhalePublicApiServiceImpl implements WhalePublicApiService {
    @Autowired
    private WhalePublicApiBaseService whalePublicApiBaseService;

    @Override
    public String wxaCodeCreateByPage(String appId, WxaCodeCreateInput wxaCodeCreateInput) {
        String fileUrl = whalePublicApiBaseService.wxaCodeCreateByPage(appId, wxaCodeCreateInput);
        // 获取文件后缀
        String suffix = fileUrl.substring(fileUrl.lastIndexOf(".")+1);
        //获取文件字节码
        byte[] fileBytes = HttpUtil.downloadBytes(fileUrl);
        String base64Cde = Base64Util.encryptBase64(fileBytes);
        return getBase64Suffix(suffix) + base64Cde;
    }

    /**
     * 获取文件base64编码
     * @param suffix
     * @return
     */
    private String getBase64Suffix(String suffix){
        suffix = suffix.toLowerCase();
        if("png".equals(suffix)){
            return "data:image/png;base64,";
        }else if("jpeg".equals(suffix)){
            return "data:image/jpeg;base64,";
        }else if("jpg".equals(suffix)){
            return "data:image/jpg;base64,";
        }else if("gif".equals(suffix)){
            return "data:image/gif;base64,";
        }else{
            //todo 异常处理
            return null;
        }
    }
}
