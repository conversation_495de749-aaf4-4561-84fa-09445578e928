package com.cfpamf.ms.insur.operation.auto.po;

import com.cfpamf.ms.insur.operation.base.po.BasePO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;
import tk.mybatis.mapper.annotation.KeySql;

import javax.persistence.Id;
import javax.persistence.OrderBy;
import javax.persistence.Table;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * 客户断保跟进记录表
 */
@Data
@Table(name = "system_auto_rate_config")
@FieldDefaults(level = AccessLevel.PRIVATE)
public class SystemAutoRateConfigPo extends BasePO {

    @Id
    @KeySql(useGeneratedKeys = true)
    protected Integer id;

    @ApiModelProperty(name="产品主键")
    Integer productId;

    @ApiModelProperty(name="备注")
    String remark;

    @ApiModelProperty(name="创建时间")
    LocalDateTime createTime;

    @ApiModelProperty(name="创建人")
    String createBy;

    @ApiModelProperty(name="修改时间")
    LocalDateTime updateTime;

    @ApiModelProperty(name="修改人")
    String updateBy;
}
