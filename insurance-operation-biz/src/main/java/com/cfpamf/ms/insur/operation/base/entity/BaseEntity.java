package com.cfpamf.ms.insur.operation.base.entity;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.OrderBy;
import java.time.LocalDateTime;

/**
 * 实体基类
 *
 * <AUTHOR>
 * @date 2021/5/18 19:35
 */
@Getter
@Setter
public class BaseEntity {
    /**
     * 创建人
     */
    @Column(name = "create_by")
    protected String createBy;

    /**
     * 上次更新人
     */
    @Column(name = "update_by")
    protected String updateBy;

    /**
     * 主键id
     */
    @Id
    @OrderBy("desc")
    protected Long id;

    /**
     * 是否删除
     */
    @Column(name = "enabled_flag")
    protected Integer enabledFlag = 0;
    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private LocalDateTime createTime;
    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private LocalDateTime updateTime;
}
