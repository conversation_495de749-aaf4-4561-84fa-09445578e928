package com.cfpamf.ms.insur.operation.phoenix.pojo.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class PhoenixEmpTodoTabCountVo {

    @ApiModelProperty("工号")
    String jobNumber;

    @ApiModelProperty("Tab1：近七日内到期任务数")
    int redTab;

    @ApiModelProperty("Tab3：已进入宽限期的待办任务数")
    int yellowTab;

    @ApiModelProperty("Tab2：当月到期任务数")
    int currentMonthDue;

    @ApiModelProperty("Tab4：已预约当日跟进任务数")
    int reserveCurrentDay;
}
