package com.cfpamf.ms.insur.operation.promotion.service;

import com.cfpamf.ms.insur.operation.fegin.oms.request.MarketSubmitRequest;
import com.cfpamf.ms.insur.operation.fegin.oms.response.MarketingForwardGroupByDateDTO;
import com.cfpamf.ms.insur.operation.fegin.oms.response.MarketingForwardListQueryOutputDTO;
import com.cfpamf.ms.insur.operation.fegin.oms.response.MarketingLinkDTO;
import com.cfpamf.ms.insur.operation.fegin.oms.response.OmsBaseResponse;
import com.cfpamf.ms.insur.operation.promotion.dto.MarketingForwardListQueryDTO;
import com.cfpamf.ms.insur.operation.promotion.dto.MarketingLaunchDTO;
import org.springframework.web.bind.annotation.*;

public interface OmsBaseService {

    /**
     * 同步素材圈新增操作
     * @param marketSubmitRequest
     * @return
     */
    OmsBaseResponse<Long> marketingSubmit(MarketSubmitRequest marketSubmitRequest);

    /**
     * 素材圈创建素材圈链接新增操作
     * @param saveDTO
     * @return
     */
    OmsBaseResponse<Long> marketingLinkSubmit(MarketingLinkDTO saveDTO);

    void launch(@RequestBody MarketingLaunchDTO marketingLaunchDTO);

    OmsBaseResponse<MarketingForwardListQueryOutputDTO> forwardListForInsure(MarketingForwardListQueryDTO marketingForwardListQueryDTO);
}
