
package com.cfpamf.ms.insur.operation.msg.pojo.po;

import com.cfpamf.ms.insur.operation.base.entity.BaseNoUserEntity;
import com.cfpamf.ms.insur.operation.msg.enums.EnumReceiverAuditState;
import com.cfpamf.ms.insur.operation.msg.enums.OpMessageReceiverTypeEnum;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import javax.persistence.Table;
import javax.validation.constraints.NotNull;

/**
 * Created by zhengjing  on 2021-07-15 18:19:44
 *
 * <AUTHOR>
 */
@ApiModel("营销工具-消息推送接收人配置")
@Table(name = "op_message_rule_receiver")
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class OpMessageRuleReceiver extends BaseNoUserEntity {

    /**
     * 字段名称 规则id
     */
    @ApiModelProperty(value = "规则id")
    Long messageRuleId;
    /**
     * 字段名称 接受者类型 chat employee
     */
    @NotNull(message = "接受者类型不能为空")
    @ApiModelProperty(value = "接受者类型 chat employee")
    String receiverType;
    /**
     * 字段名称 接受者 类型为群时
     */
    @ApiModelProperty(value = "接受者 类型为群时")
    String receiver;

    @ApiModelProperty(value = "接受者 类型为群时")
    String receiverName;

    @ApiModelProperty("额外参数 ")
    String params;

    @ApiModelProperty("状态 1-自动推送 2-审核推送")
    Integer auditState;

    @ApiModelProperty("群机器人验签")
    String robotTokenSecret;

    @ApiModelProperty("群机器人token")
    String robotToken;

    @ApiModelProperty("钉钉群配置的场景Id 仅receiverType为chatAndContext时必填")
    String groupContextId;
//    @ApiModelProperty("图片性质 动态图dynamics 静态图static,默认 static")
//    String imageNature ="static";

    @JsonIgnore
    public boolean needAudit() {
        return EnumReceiverAuditState.AUDIT.isMe(auditState);
    }

    /**
     * 是否发发送给个人
     * @return
     */
    @JsonIgnore
    public boolean isPerson() {
        return OpMessageReceiverTypeEnum.PERSON.isMe(receiverType);
    }
}

