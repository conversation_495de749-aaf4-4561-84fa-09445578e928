package com.cfpamf.ms.insur.operation.activity.dao;

import com.cfpamf.ms.insur.operation.activity.entity.SystemActivityProgramme;
import com.cfpamf.ms.insur.operation.activity.form.SystemActivitySearchForm;
import com.cfpamf.ms.insur.operation.activity.form.WxActivitySearchForm;
import com.cfpamf.ms.insur.operation.activity.vo.SystemActivityProgrammeVo;
import com.cfpamf.ms.insur.operation.activity.vo.WxActivitySimpleVo;
import com.cfpamf.ms.insur.operation.base.dao.CommonMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface SystemActivityProgrammeMapper extends CommonMapper<SystemActivityProgramme> {

    /**
     * 通过活动id查找活动实体
     *
     * @param idList
     * @return
     */
    List<SystemActivityProgramme> getByIdList(@Param("idList") Collection<Long> idList);

    /**
     * 搜索活动方案
     *
     * @param searchForm
     * @return
     */
    List<SystemActivityProgrammeVo> search(SystemActivitySearchForm searchForm);

    /**
     * 搜索活动方案 判断活动时间使用相对时间
     * @param searchForm
     * @return
     */
    List<SystemActivityProgrammeVo> searchByLazy(SystemActivitySearchForm searchForm);

    /**
     * 搜索活动方案
     *
     * @param id
     * @return
     */
    SystemActivityProgrammeVo getById(Long id);

    /**
     * 修改活动状态
     *
     * @param id
     * @param activityFlag
     */
    void updateActivityFlag(@Param("id") Long id, @Param("activityFlag") Integer activityFlag);

    /**
     * 搜索微信活动列表
     *
     * @param wxActivitySearchForm
     * @return
     */
    List<WxActivitySimpleVo> getWxActivityList(WxActivitySearchForm wxActivitySearchForm);

    Long getLastId();

    void insertCopyProgramme(SystemActivityProgramme systemActivityProgramme);
}
