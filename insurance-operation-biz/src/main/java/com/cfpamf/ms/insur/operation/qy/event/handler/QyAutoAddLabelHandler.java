package com.cfpamf.ms.insur.operation.qy.event.handler;

import com.cfpamf.ms.insur.operation.base.event.BaseEventHandler;
import com.cfpamf.ms.insur.operation.qy.event.OpeQyPagerEvent;
import com.cfpamf.ms.insur.operation.qy.service.OpeQyLabelRuleService;
import com.google.common.eventbus.Subscribe;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 企微自动打标签
 *
 * <AUTHOR>
 * @date 2022/8/11 14:19
 */
@Slf4j
@Component
public class QyAutoAddLabelHandler implements BaseEventHandler {

    @Autowired
    OpeQyLabelRuleService labelRuleService;

    /**
     * 计算加佣
     *
     * @param event
     * @return
     */
    @Subscribe
    public void handlerEvent(OpeQyPagerEvent event) {
        labelRuleService.autoLabel(event.getPagerId(), event.getExternalUserid());
    }

}
