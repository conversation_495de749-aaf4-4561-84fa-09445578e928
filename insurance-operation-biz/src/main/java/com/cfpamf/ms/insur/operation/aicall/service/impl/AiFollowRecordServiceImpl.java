package com.cfpamf.ms.insur.operation.aicall.service.impl;

import com.cfpamf.ms.insur.operation.aicall.service.AiFollowRecordService;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class AiFollowRecordServiceImpl implements AiFollowRecordService {

}
