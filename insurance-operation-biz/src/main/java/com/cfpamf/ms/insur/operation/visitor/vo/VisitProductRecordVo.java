package com.cfpamf.ms.insur.operation.visitor.vo;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 产品访问记录
 *
 * <AUTHOR>
 * @date 2021/4/29 16:15
 */
@Data
public class VisitProductRecordVo {

    /**
     * 产品id
     */
    private Long productId;
    /**
     * 产品名
     */
    private String productName;
    /**
     * 产品类型
     */
    private String productType;
    /**
     * 访问时间
     */
    private LocalDateTime visitTime;

}
