package com.cfpamf.ms.insur.operation.prospectus.vo;


import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 计划保障项目VO
 *
 * <AUTHOR>
 */
@Data
@ApiModel
public class PlanCoverageAmountVo {
    /**
     * 计划
     */
    Integer plan;
    /**
     * 保险种类（main主险
     */
    @ApiModelProperty("保险种类（main主险")
    private String cvgType;

    /**
     * 责任类型（100身故 110残疾 120身故/残疾 130重疾 140中症 150轻症 160前症 170特定疾病 180住院医疗 190门诊医疗 200住院津贴 210投保人豁免 220被保人豁免 230生存金 240祝寿金/满期金 250养老金 260教育金 270特色责任 280附加服务 290增值服务 300家财 500其他）
     * responsibility
     */
    @ApiModelProperty("责任类型（100身故 110残疾 120身故/残疾 130重疾 140中症 150轻症 160前症 170特定疾病 180住院医疗 190门诊医疗 200住院津贴 210投保人豁免 220被保人豁免 230生存金 240祝寿金/满期金 250养老金 260教育金 270特色责任 280附加服务 290增值服务 300家财 500其他）")
    private Integer cvgRespType;

    /**
     * productId
     */
    @JsonIgnore
    @ApiModelProperty(hidden = true)
    private Integer productId;

    /**
     * spcId
     */
    @JsonIgnore
    @ApiModelProperty(hidden = true)
    private Integer spcId;

    /**
     * 保障项目
     */
    @ApiModelProperty(hidden = true)
    private String cvgItemName;


    /**
     * 保障项目编码
     */
    private String cvgCode;

    /**
     * 计划Id
     */
    @ApiModelProperty("计划Id")
    private Long planId;

    /**
     * 计划名称
     */
    @ApiModelProperty("计划名称")
    private String planName;

    /**
     * 保障金额
     */
    @ApiModelProperty("'保障金额")
    private BigDecimal cvgAmount;

    /**
     * 保障说明
     */
    @ApiModelProperty("'保障显示值")
    private String cvgNotice;

    /**
     * 保障项目详情
     */
    @ApiModelProperty("保障项目详情")
    private String cvgNameDetail;

}
