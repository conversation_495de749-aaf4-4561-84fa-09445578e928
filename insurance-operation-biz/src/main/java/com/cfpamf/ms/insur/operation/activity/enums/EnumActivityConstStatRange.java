package com.cfpamf.ms.insur.operation.activity.enums;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.experimental.FieldDefaults;

/**
 * 统计方式
 *
 * <AUTHOR> 2022/6/23 10:31
 */
@Getter
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public enum EnumActivityConstStatRange {

    /**
     * 全部
     */
    ALL,
    /**
     * 统计当前出单人所有业绩
     */
    PERSON,
    /**
     * 统计当前出单人所在区域
     */
    REGION,
    /**
     * 统计当前出单人所在机构
     */
    ORG;
}
