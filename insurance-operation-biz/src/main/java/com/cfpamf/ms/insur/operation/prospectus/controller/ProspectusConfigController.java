package com.cfpamf.ms.insur.operation.prospectus.controller;

import com.cfpamf.ms.insur.operation.base.annotaions.ResponseDecorated;
import com.cfpamf.ms.insur.operation.prospectus.api.ProspectusConfigApi;
import com.cfpamf.ms.insur.operation.prospectus.form.ProspectusConfigForm;
import com.cfpamf.ms.insur.operation.prospectus.form.ProspectusConfigSearchForm;
import com.cfpamf.ms.insur.operation.prospectus.form.ProspectusProductSearchForm;
import com.cfpamf.ms.insur.operation.prospectus.service.ProspectusConfigService;
import com.cfpamf.ms.insur.operation.prospectus.vo.ProspectusConfigVo;
import com.cfpamf.ms.insur.operation.prospectus.vo.ProspectusProductVo;
import com.github.pagehelper.PageInfo;
import org.springframework.web.bind.annotation.RestController;

/**
 * 计划书配置控制类
 *
 * <AUTHOR>
 * @date 2021/5/19 14:59
 */
@RestController
@ResponseDecorated
public class ProspectusConfigController implements ProspectusConfigApi {

    private ProspectusConfigService prospectusConfigService;

    public ProspectusConfigController(ProspectusConfigService prospectusConfigService) {
        this.prospectusConfigService = prospectusConfigService;
    }


    @Override
    public void save(ProspectusConfigForm prospectusConfigForm) {
        prospectusConfigService.save(prospectusConfigForm);
    }

    @Override
    public void update(Long id, ProspectusConfigForm prospectusConfigForm) {
        prospectusConfigService.update(id, prospectusConfigForm);
    }

    @Override
    public ProspectusConfigVo getById(Long id) {
        return prospectusConfigService.getById(id);
    }

    @Override
    public void delete(Long id) {
        prospectusConfigService.delete(id);
    }

    @Override
    public PageInfo<ProspectusConfigVo> search(ProspectusConfigSearchForm prospectusConfigSearchForm) {
        return prospectusConfigService.search(prospectusConfigSearchForm);
    }


    @Override
    public ProspectusConfigVo getByProductId(Long productId) {
        return prospectusConfigService.getByProductId(productId);
    }

    @Override
    public PageInfo<ProspectusProductVo> searchProspectusProduct(ProspectusProductSearchForm prospectusProductSearchForm) {
        return prospectusConfigService.searchProspectusProduct(prospectusProductSearchForm);
    }
}
