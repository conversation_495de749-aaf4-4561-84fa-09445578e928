package com.cfpamf.ms.insur.operation.honor.controller;


import com.cfpamf.ms.insur.operation.base.annotaions.ResponseDecorated;
import com.cfpamf.ms.insur.operation.base.constant.BaseConstants;
import com.cfpamf.ms.insur.operation.base.util.ValidatorUtils;
import com.cfpamf.ms.insur.operation.honor.dto.*;
import com.cfpamf.ms.insur.operation.honor.query.HonorMetricConfigurationQuery;
import com.cfpamf.ms.insur.operation.honor.query.HonorRulesConfigurationQuery;
import com.cfpamf.ms.insur.operation.honor.query.HonorSelectionResultQuery;
import com.cfpamf.ms.insur.operation.honor.service.HonorsMetricConfigurationService;
import com.cfpamf.ms.insur.operation.honor.service.HonorsRulesConfigurationService;
import com.cfpamf.ms.insur.operation.honor.service.HonorsSelectionResultsService;
import com.cfpamf.ms.insur.operation.job.CalculateHonorResultsJobHandler;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.*;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * 断保客户管理
 *
 * <AUTHOR>
 * @date 2024/7/02 16:04
 */
@Slf4j
@Api(value = "荣誉规则管理", tags = {"荣誉规则管理"})
@RequestMapping(value = {BaseConstants.ADMIN_VERSION + "/honor"})
@ResponseDecorated
@RestController
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class HonorController {

    HonorsRulesConfigurationService honorsRulesConfigurationService;

    HonorsMetricConfigurationService honorsMetricConfigurationService;

    CalculateHonorResultsJobHandler calculateHonorResultsJobHandler;

    HonorsSelectionResultsService honorsSelectionResultsService;

    @PostMapping("/saveOrUpdate/rules")
    @ApiOperation("新增或修改荣誉规则配置")
    public HonorRulesConfigurationDto saveOrUpdateHonorRules(@RequestBody HonorRulesConfigurationDto dto) {
        // 1 基础校验
        ValidatorUtils.validateParam(dto);
        return honorsRulesConfigurationService.saveOrUpdateHonorRules(dto);
    }

    /**
     * 获取荣誉配置规则详情
     *
     * @param id
     * @return
     */
    @ApiOperation(value = "获取荣誉配置规则详情")
    @GetMapping("/rules/detail/{id:\\d+}")
    public HonorRulesConfigurationDto getRulesDetailById(@PathVariable Integer id) {
        return honorsRulesConfigurationService.getRulesDetailById(id);
    }

    /**
     * 删除荣誉规则配置
     *
     * @param id 主键
     */
    @ApiOperation(value = "删除荣誉规则配置")
    @GetMapping("/rules/delete/{id:\\d+}")
    public void deleteRuleById(@PathVariable Integer id) {
        honorsRulesConfigurationService.deleteRuleById(id);
    }

    /**
     * 获取荣誉规则配置列表
     *
     * @param query
     * @return
     */
    @ApiOperation(value = "获取荣誉规则配置列表")
    @PostMapping("/rulesConfigurationList")
    public PageInfo<HonorRulesConfigurationList> getRulesConfigurationListByPage(@RequestBody HonorRulesConfigurationQuery query) {
        return honorsRulesConfigurationService.getRulesConfigurationListByPage(query);
    }

    /**
     * 获取荣誉规则配置列表
     *
     * @param query
     * @return
     */
    @ApiOperation(value = "获取荣誉规则配置指标")
    @PostMapping("/metricConfigurationList")
    public List<HonorMetricConfigurationList> getMetricConfigurationList(@RequestBody HonorMetricConfigurationQuery query) {
        return honorsMetricConfigurationService.getMetricConfigurationList(query);
    }

    @ApiOperation(value = "执行荣誉评选结果计算job")
    @PostMapping("/calculate/job")
    public void calculateJob() {
       calculateHonorResultsJobHandler.execute();
    }

    /**
     * 获取荣誉评选清单
     *
     * @param query 查询条件
     * @return 分页列表
     */
    @ApiOperation(value = "获取荣誉评选清单")
    @PostMapping("/rules/selectionResult")
    public PageInfo<HonorsCalculateResults> getResultsByConfigurationId(@RequestBody HonorSelectionResultQuery query) {
        return honorsSelectionResultsService.getResultsByConfigurationId(query);
    }

    /**
     * 荣誉清单导入
     *
     * @param dto 导入参数
     * @return
     */
    @ApiOperation(value = "荣誉清单导入")
    @PostMapping("/import")
    public HonorListImportResultDto honorListImport(@RequestBody HonorListImportDTO dto){
        //基础校验
        ValidatorUtils.validateParam(dto);
        return honorsRulesConfigurationService.honorListImport(dto);
    }
}
