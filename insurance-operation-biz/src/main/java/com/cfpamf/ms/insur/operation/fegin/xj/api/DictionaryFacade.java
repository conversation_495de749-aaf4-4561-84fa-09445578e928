package com.cfpamf.ms.insur.operation.fegin.xj.api;

import com.cfpamf.ms.insur.operation.xj.query.WxDataQuery;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 */
@FeignClient(name = "insurance-operation", url = "${insurance-operation.url}", path = "/whale/notice")
public interface DictionaryFacade {

    /**
     * 发送公众号通知
     *
     *
     *
     */
    @ApiOperation(value = "发送公众号通知")
    @PostMapping("")
    public void notice(@RequestBody WxDataQuery query);
}