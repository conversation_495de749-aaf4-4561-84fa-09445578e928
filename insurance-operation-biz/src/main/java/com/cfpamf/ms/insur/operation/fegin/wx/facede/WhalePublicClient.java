package com.cfpamf.ms.insur.operation.fegin.wx.facede;

import com.cfpamf.ms.insur.operation.fegin.wx.request.*;
import com.cfpamf.ms.insur.operation.phoenix.api.model.WhaleResp;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@FeignClient(url = "${xiao-whale.api.domain}", value = "whalePublicClient")
public interface WhalePublicClient {

    /**
     * 获取微信二维码链接
     *
     * @param appId   微信小程序AppId
     * @param wxaCodeCreateInput
     * @return com.mpolicy.common.result.Result<java.lang.String> 微信二维码跳转链接
     * <AUTHOR>
     * @since 2023/4/17
     */
    @ApiOperation("获取微信二维码链接")
    @RequestMapping(value = "/public-api/wx/customer/{appId}/wxaCodeCreateByPage", method = RequestMethod.POST)
    WhaleResp<String> wxaCodeCreateByPage(@PathVariable @ApiParam(name = "appId", value = "appId") String appId, @RequestBody WxaCodeCreateInput wxaCodeCreateInput);
}
