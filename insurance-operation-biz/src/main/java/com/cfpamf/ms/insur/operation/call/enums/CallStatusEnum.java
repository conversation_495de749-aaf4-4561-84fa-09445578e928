package com.cfpamf.ms.insur.operation.call.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 呼叫状态枚举
 *
 * <AUTHOR>
 * @date 2025-07-22
 */
@Getter
@AllArgsConstructor
public enum CallStatusEnum {

    /**
     * 初始化
     */
    INIT(0, "初始化"),

    /**
     * 呼叫中
     */
    CALLING(1, "呼叫中"),

    /**
     * 呼叫成功
     */
    SUCCESS(2, "呼叫成功"),

    /**
     * 呼叫失败
     */
    FAILED(3, "呼叫失败");

    /**
     * 状态码
     */
    private final Integer code;

    /**
     * 状态描述
     */
    private final String desc;

    /**
     * 根据状态码获取枚举
     *
     * @param code 状态码
     * @return 枚举值
     */
    public static CallStatusEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (CallStatusEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }

    /**
     * 根据状态码获取描述
     *
     * @param code 状态码
     * @return 状态描述
     */
    public static String getDescByCode(Integer code) {
        CallStatusEnum status = getByCode(code);
        return status != null ? status.getDesc() : "未知状态";
    }
}
