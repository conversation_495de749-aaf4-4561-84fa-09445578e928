package com.cfpamf.ms.insur.operation.base.po;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Id;
import javax.persistence.OrderBy;

/**
 * <AUTHOR> 2020/2/13 18:25
 */
@Getter
@Setter
public class BasePO {

    @Id
    @OrderBy("desc")
    protected Integer id;

    /**
     * 是否删除
     */
    @ApiModelProperty(hidden = true)
    protected Integer enabledFlag = 0;
}
