package com.cfpamf.ms.insur.operation.pco.enums;

import java.util.Arrays;

/**
 * <AUTHOR>
 */

public enum PcoLevelEnum {

    /**
     *S
     */
    SLEVEL("S级", "S"),
    /**
     *A
     */
    ALEVEL("A级", "A"),
    /**
     *B
     */
    BLEVEL("B级", "B"),
    /**
     *C
     */
    CLEVEL("C级", "C");


    /**
     * 值
     */
    private final String value;

    /**
     * 名称
     */
    private final String name;

    PcoLevelEnum(String name, String value) {
        this.value = value;
        this.name = name;
    }

    public String getValue() {
        return value;
    }

    public String getName() {
        return name;
    }

    /**
     * <p>
     * 根据等级名称获取等级枚举
     * </p>
     *
     * @param name 等级名称
     */
    public static PcoLevelEnum deName(String name) {
        return Arrays.stream(PcoLevelEnum.values()).filter(x -> x.name.equals(name)).findFirst().orElse(null);
    }

    /**
     * <p>
     * 根据等级枚举获取等级名称
     * </p>
     *
     * @param value 等级枚举
     */
    public static PcoLevelEnum deValue(String value) {
        return Arrays.stream(PcoLevelEnum.values()).filter(x -> x.value.equals(value)).findFirst().orElse(null);
    }
}
