package com.cfpamf.ms.insur.operation.customer.service;

import com.cfpamf.ms.insur.operation.customer.convertor.InterruptionFollowConverter;
import com.cfpamf.ms.insur.operation.customer.dao.CustomerInterruptionFollowMapper;
import com.cfpamf.ms.insur.operation.customer.dao.CustomerLoanFollowMapper;
import com.cfpamf.ms.insur.operation.customer.dao.CustomerMapper;
import com.cfpamf.ms.insur.operation.customer.dto.CustomerInterruptionFollowDto;
import com.cfpamf.ms.insur.operation.customer.enums.EnumRenewalIntention;
import com.cfpamf.ms.insur.operation.customer.enums.EnumServiceMode;
import com.cfpamf.ms.insur.operation.customer.po.CustomerInterruptionFollowPo;
import com.cfpamf.ms.insur.operation.customer.po.CustomerLoanFollowPo;
import com.cfpamf.ms.insur.operation.phoenix.enums.EnumTodoBizType;
import com.cfpamf.ms.insur.operation.phoenix.service.PhoenixEmpTodoService;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class CustomerInterruptionFollowService {

    CustomerInterruptionFollowMapper customerBreakFollowMapper;

    PhoenixEmpTodoService phoenixEmpTodoService;

    CustomerMapper customerMapper;

    CustomerLoanFollowMapper customerLoanFollowMapper;
    /**
     * 添加续保服务跟进记录
     * @param dto 跟进Dto
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.NESTED)
    public void saveRenewalFollowRecord(CustomerInterruptionFollowDto dto) {
        //更新以往的跟进记录最新状态为0
        customerBreakFollowMapper.updateNewest(dto.getCustomerId());
        CustomerInterruptionFollowPo po = InterruptionFollowConverter.INS.dtoToPo(dto);
        po.setIdNumber(customerMapper.getIdNumberByCustomerId(dto.getCustomerId()));
        customerBreakFollowMapper.insert(po);

        //初始化排序字段
        int followSortNo = initFollowSortNo(dto);

        //更新待办为已完成
        phoenixEmpTodoService.finishTodo(EnumTodoBizType.INTERRUPTION, dto.getIdNumber(), "跟进",followSortNo,dto.getIntention());
    }

    /**
     * 初始化排序字段
     * @param dto 跟进Dto
     */
    private Integer initFollowSortNo(CustomerInterruptionFollowDto dto) {
        int followSortNo;
        if (EnumRenewalIntention.HIGH.getCode().equals(dto.getIntention())) {
            followSortNo = -20;
        } else if (EnumRenewalIntention.MEDIAN.getCode().equals(dto.getIntention())) {
            followSortNo = -10;
        } else if (EnumRenewalIntention.LOSE_CONTACT.getCode().equals(dto.getIntention())) {
            followSortNo = 50;
        } else {
            followSortNo = 999;
        }
        return followSortNo;
    }

    /**
     * 根据客户获取跟进记录列表
     * @param customerId 客户主键
     * @return List
     */
    public List<CustomerInterruptionFollowDto> selectByCustomer(String customerId){
        return customerBreakFollowMapper.selectByCustomer(customerId);
    }
    /**
     * 根据客户获取跟进记录列表（包括AI跟进）
     * @param customerId 客户主键
     * @return List
     */
    public List<CustomerInterruptionFollowDto> selectByCustomerIncludeAI(String customerId){
        List<CustomerInterruptionFollowDto> list = new ArrayList<>();
        return customerBreakFollowMapper.selectByCustomerIncludeAI(customerId);
    }


    public void saveLoanFollowRecord(CustomerLoanFollowPo dto) {
        //更新以往的跟进记录最新状态为0
        customerLoanFollowMapper.updateNewest(dto.getCustomerId());
        dto.setIdNumber(customerMapper.getLoanIdNumberByCustomerId(dto.getCustomerId()));
        dto.setNewest(1);
        customerLoanFollowMapper.insert(dto);

        //更新待办为已完成
        if(!EnumServiceMode.LOSE_CONTACT.getCode().equals(dto.getIntention())) {
            phoenixEmpTodoService.finishTodo(EnumTodoBizType.LOAN, dto.getIdNumber(), "跟进");
        }
    }
}
