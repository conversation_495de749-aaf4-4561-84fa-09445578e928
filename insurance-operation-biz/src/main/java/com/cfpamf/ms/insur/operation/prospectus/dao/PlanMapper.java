package com.cfpamf.ms.insur.operation.prospectus.dao;


import com.cfpamf.ms.insur.operation.activity.vo.PlanSimpleVo;
import com.cfpamf.ms.insur.operation.base.dao.CommonMapper;
import com.cfpamf.ms.insur.operation.prospectus.entity.SmPlan;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface PlanMapper extends CommonMapper<SmPlan> {
    /**
     * 通过id查找
     *
     * @param id
     * @return
     */
    SmPlan getById(@Param("id") Long id);

    /**
     * 通过id查找
     *
     * @param idList
     * @return
     */
    List<PlanSimpleVo> getByIdList(@Param("idList") List<Long> idList);
}