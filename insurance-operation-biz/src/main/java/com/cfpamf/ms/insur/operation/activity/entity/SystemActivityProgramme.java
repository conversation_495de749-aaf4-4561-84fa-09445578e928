package com.cfpamf.ms.insur.operation.activity.entity;


import com.cfpamf.ms.insur.operation.activity.enums.ActivityObject;
import com.cfpamf.ms.insur.operation.activity.enums.ActivityType;
import com.cfpamf.ms.insur.operation.activity.enums.ConflictRule;
import com.cfpamf.ms.insur.operation.activity.enums.EnumActivityConfigType;
import com.cfpamf.ms.insur.operation.base.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.Table;
import java.time.LocalDateTime;

/**
 * 系统活动方案表
 * system_activity_programme
 *
 * <AUTHOR>
@ApiModel(value = "系统活动方案表")
@Table(name = "system_activity_programme")
@Data
public class SystemActivityProgramme extends BaseEntity {

    /**
     * 活动标题
     */
    @ApiModelProperty(value = "活动标题")
    private String title;

    /**
     * 活动类型 normal-普通活动 commission-加佣 ticket-抽奖券
     */
    @ApiModelProperty(value = "活动类型 NORMAL-普通活动 ADD_COMMISSION-加佣 COUPON_DELIVERY-抽奖券 RED_ENVELOPE-红包")
    @Enumerated(value = EnumType.STRING)
    @Column(name = "type")
    private ActivityType type;

    /**
     * 活动对象
     */
    @ApiModelProperty(value = "活动对象 STAFF_MANAGER-员工+客户经理 USER-普通用户 VILLAGE_AGENT-村代")
    @Enumerated(value = EnumType.STRING)
    @Column(name = "activity_object")
    private ActivityObject activityObject;
    /**
     * 优惠类型 overlay-叠加 optimal-最优
     */
    @ApiModelProperty(value = "优惠类型 OVERLAY-叠加 OPTIMAL-最优")
    @Enumerated(value = EnumType.STRING)
    @Column(name = "conflictRule")
    private ConflictRule conflictRule;

    @ApiModelProperty(value = "配置类型 GROOVY-脚本 CONST-手动",
            allowableValues = "GROOVY,CONST", example = "GROOVY")
    @Column(name = "config_type")
    private EnumActivityConfigType configType;

    /**
     * 列表图片
     */
    @ApiModelProperty(value = "列表图片")
    @Column(name = "imageUrl")
    private String imageUrl;

    @ApiModelProperty(value = "背景图")
    @Column(name = "background_image_url")
    private String backgroundImageUrl;
    /**
     * 活动时间From
     */
    @ApiModelProperty(value = "活动时间From")
    @Column(name = "startTime")
    private LocalDateTime startTime;

    /**
     * 活动时间To
     */
    @ApiModelProperty(value = "活动时间To")
    @Column(name = "endTime")
    private LocalDateTime endTime;

    /**
     * 活动介绍
     */
    @ApiModelProperty(value = "活动介绍")
    private String content;

    /**
     * 活动区域
     */
    @ApiModelProperty(value = "活动区域")
    private String regions;

    /**
     * 推荐产品
     */
    @ApiModelProperty(value = "推荐产品")
    private String products;

    /**
     * 启用标记
     */
    @ApiModelProperty(value = "启用标记 0不启用 1启用  默认启用")
    @Column(name = "activeFlag")
    private Integer activeFlag;

    /**
     * els活动编码
     */
    @ApiModelProperty(value = "els活动编码")
    private Integer elsActivityNumber;

    /**
     * els赠送口令
     */
    @ApiModelProperty(value = "els赠送口令")
    private String elsGrantPassword;

    /**
     * 开启次数
     */
    @ApiModelProperty(value = "开启次数")
    private Integer openCount;

    /**
     * 活动平台
     */
    @ApiModelProperty(value = "活动平台：nb农保,xj小鲸(后续都默认平台为小鲸)")
    private String activityPlatform;

    @ApiModelProperty(value = "兑换机制:monthly按月兑现，end活动结束兑现")
    private String rewardMechanism;

}
