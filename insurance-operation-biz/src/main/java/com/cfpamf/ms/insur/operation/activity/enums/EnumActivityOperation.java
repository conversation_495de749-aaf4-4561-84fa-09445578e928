package com.cfpamf.ms.insur.operation.activity.enums;

import com.google.common.collect.Lists;

import java.util.List;

/**
 * 活动操作
 *  发布 RELEASE 暂停 SUSPEND 作废 ABOLISH 继续 CONTINUE
 *
 * <AUTHOR>
 * @date 2021/11/24 17:19
 */
public enum EnumActivityOperation {
    //发布
    RELEASE(Lists.newArrayList(EnumActivityState.UNPUBLISHED), 1),
    // 暂停
    SUSPEND(Lists.newArrayList(EnumActivityState.IN_ACTIVITY), 2),
    // 作废
    ABOLISH(Lists.newArrayList(EnumActivityState.IN_ACTIVITY, EnumActivityState.SUSPEND), 3),
    // 删除
    DELETE(Lists.newArrayList(EnumActivityState.UNPUBLISHED, EnumActivityState.NOT_START), 3),
    // 继续
    CONTINUE(Lists.newArrayList(EnumActivityState.SUSPEND), 1);

    /**
     * 前置活动状态
     */
    List<EnumActivityState> operationState;

    /**
     * 下一步活动标识 0未发布 1已发布 2已暂停 3已作废
     *
     * @param operationState
     */
    Integer nextActiveFlag;

    EnumActivityOperation(List<EnumActivityState> operationState, Integer nextActiveFlag) {
        this.operationState = operationState;
        this.nextActiveFlag = nextActiveFlag;
    }

    /**
     * 校验活动操作是否合理
     *
     * @param enumActivityState
     * @return
     */
    public boolean checkActivityOperation(EnumActivityState enumActivityState) {
        return operationState.contains(enumActivityState);
    }

    public Integer getNextActiveFlag() {
        return nextActiveFlag;
    }
}
