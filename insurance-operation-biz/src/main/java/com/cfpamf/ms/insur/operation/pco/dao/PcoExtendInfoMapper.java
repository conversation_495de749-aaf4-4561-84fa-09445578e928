package com.cfpamf.ms.insur.operation.pco.dao;

import com.cfpamf.ms.insur.operation.base.dao.CommonMapper;
import com.cfpamf.ms.insur.operation.pco.entity.PcoExtendInfo;
import com.cfpamf.ms.insur.operation.pco.vo.UserVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR> 2022/8/31 10:58
 */
@Mapper
public interface PcoExtendInfoMapper extends CommonMapper<PcoExtendInfo> {
    /**
     * 查询缺少钉钉信息的id
     *
     * @return
     */
    List<String> selectMissInfoDingUser();

    /**
     * 根据岗位查询用户信息
     * @param keyword
     * @param postCode
     * @return
     */
    List<UserVo> selectUserByPost(@Param("keyword") String keyword, @Param("postCode") String postCode);

    int updateJobCode();

    int updateExtendInfo(@Param("list")List<PcoExtendInfo> list);
}
