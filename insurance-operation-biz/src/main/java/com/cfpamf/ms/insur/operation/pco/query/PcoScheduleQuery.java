package com.cfpamf.ms.insur.operation.pco.query;

import com.cfpamf.ms.insur.operation.base.form.DataAuthPageForm;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class PcoScheduleQuery extends DataAuthPageForm {

    /**
     * 开始时间
     */
    @ApiModelProperty("开始日期")
    private LocalDateTime startDate;

    /**
     * 结束时间
     */
    @ApiModelProperty("结束日期")
    private LocalDateTime endDate;

    /**
     * 是否处理 0 已处理 1 未处理
     */
    @ApiModelProperty("是否处理 0 已处理 1 未处理")
    private Integer beDispose;

    /**
     * 页码
     */
    @ApiModelProperty(value = "页码 从1开始")
    private int page;

    /**
     * 每页条数
     */
    @ApiModelProperty(value = "每页条数")
    private int size = 20;
}
