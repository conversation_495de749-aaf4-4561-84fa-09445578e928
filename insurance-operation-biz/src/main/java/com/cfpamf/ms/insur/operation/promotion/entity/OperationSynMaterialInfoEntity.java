package com.cfpamf.ms.insur.operation.promotion.entity;

import lombok.*;

import java.util.Date;
import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.OrderBy;
import javax.persistence.Table;

/**
 * 同步素材圈信息表
 *
 * <AUTHOR>
 * @date 2024-04-29 19:33:41
 */
@Data
@Table(name = "operation_syn_material_info")
public class OperationSynMaterialInfoEntity{

	@Id
	@OrderBy("desc")
	protected Integer id;

	/**
	 * 素材编码
	 */
	@Column(name = "material_code")
	private String materialCode;
	/**
	 * 素材来源系统编码
	 */
	@Column(name = "source_system_code")
	private String sourceSystemCode;
	/**
	 * 素材分类: 1.理赔,2.时事热点,3.产品,4.理念科普
	 */
	@Column(name = "material_category")
	private Integer materialCategory;
	/**
	 * 素材类型: 1.视频,2.图片,3.文字
	 */
	@Column(name = "material_type")
	private Integer materialType;
	/**
	 * 素材级别:一级分类和二级分类编码拼接
	 */
	@Column(name = "material_level_code")
	private String materialLevelCode;
	/**
	 * 同步素材圈系统返回Id
	 */
	@Column(name = "material_response_id")
	private Long materialResponseId;
	/**
	 * 创建素材圈跳转链接系统返回Id
	 */
	@Column(name = "material_link_id")
	private Long materialLinkId;
	/**
	 * 当前素材状态 1.同步成功且上架 ,2.下架
	 */
	@Column(name = "material_status")
	private Integer materialStatus;
	/**
	 * 产品编码
	 */
	@Column(name = "product_code")
	private String productCode;
	/**
	 * 产品类型
	 */
	@Column(name = "product_type")
	private String productType;
	/**
	 * 逻辑删除 0:存在;1:删除
	 */
	@Column(name = "deleted")
	private Integer deleted;
	/**
	 * 创建人
	 */
	@Column(name = "create_user")
	private String createUser;
	/**
	 * 创建时间
	 */
	@Column(name = "create_time")
	private Date createTime;
	/**
	 * 修改人
	 */
	@Column(name = "update_user")
	private String updateUser;
	/**
	 * 修改时间
	 */
	@Column(name = "update_time")
	private Date updateTime;
}
