package com.cfpamf.ms.insur.operation.claim.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class PolicyNoticeTask {
    private Long id;
    private String policyNo;
    private String applicantName;
    private String applicantMobile;
    private String policyProductType;
    private String mainProductName;
    private String policyStatus;
    private Date orderTime;
    private Date approvedTime;
    private Date enforceTime;
    private Date insuredPeriodEndTime;
    private String insuredPeriodType;
    private Integer insuredPeriod;
    private Integer status;
    private String applicantIdCard;
    private String applicantIdType;
    private BigDecimal premiumTotal;
    private String taskType;
    private String claimNo;
    private String finishTime;
    private BigDecimal payMoney;
    private String riskTypeDesc;
    private String riskTime;

}
