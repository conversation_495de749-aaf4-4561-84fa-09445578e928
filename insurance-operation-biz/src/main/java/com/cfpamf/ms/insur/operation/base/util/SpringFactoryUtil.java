package com.cfpamf.ms.insur.operation.base.util;

import groovy.lang.Binding;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;


/**
 * spring bean获取bean对象
 *
 * <AUTHOR>
 **/
@Configuration
@Slf4j
public class SpringFactoryUtil implements ApplicationContextAware {

    /**
     * Spring应用上下文环境
     */
    private static ApplicationContext applicationContext;


    /**
     * 获取对象
     *
     * @param clazz
     * @return Object
     * @throws BeansException
     */
    public static <T> T getBean(Class<T> clazz) throws BeansException {
        return applicationContext.getBean(clazz);
    }

    /**
     * 获取对象
     *
     * @param alias
     * @return Object
     * @throws BeansException
     */
    public static <T> T getBean(String alias, Class<T> clazz) throws BeansException {
        return applicationContext.getBean(alias, clazz);
    }

    /**
     * 实现ApplicationContextAware接口的回调方法。设置上下文环境
     *
     * @param applicationContext
     */
    @Override
    public void setApplicationContext(ApplicationContext applicationContext) {
        SpringFactoryUtil.applicationContext = applicationContext;
    }


    /**
     * 构造
     *
     * @return
     */
    @Primary
    @Bean
    public Binding groovyBinding() {

        return new Binding();
    }
}
