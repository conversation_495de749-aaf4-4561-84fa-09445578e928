package com.cfpamf.ms.insur.operation.base.helper;

import com.cfpamf.common.ms.exception.MSBizNormalException;
import com.cfpamf.common.ms.exception.MSException;
import com.cfpamf.ms.insur.operation.fegin.bms.enums.ExcptEnum;
import com.cfpamf.ms.insur.operation.fegin.bms.enums.OperationErrorEnum;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;

/**
 * <AUTHOR> 2021/8/26 14:44
 */
@Component
@Slf4j
public class LockHelper {

    @Autowired
    RedissonClient client;

    public void lockRun(String key, Runnable run) {
        try {
            lockRun(key, run, 5, 30);
        } catch (InterruptedException e) {
            throw new MSException(OperationErrorEnum.SERVICE, e);
        }
    }

    public <T> T lockSupplier(String key, Supplier<T> run) {
        try {
            return lockRun(key, run, 5, 30);
        } catch (InterruptedException e) {
            throw new MSException(OperationErrorEnum.SERVICE, e);
        }
    }

    /**
     * @param key
     * @param run
     * @param wait        尝试锁的等待时间
     * @param leaseSecond 最长多少秒就自动释放锁
     */
    public void lockRun(String key, Runnable run, int wait, int leaseSecond) throws InterruptedException {
        RLock lock = client.getLock(key);
        boolean lockRes = lock.tryLock(wait, leaseSecond, TimeUnit.SECONDS);
        try {
            if (lockRes) {
                run.run();
            } else {
                log.warn("尝试[锁失败{}", key);
            }

        } catch (Exception e) {
            throw e;
        } finally {
            if (lockRes && lock.isLocked()) {
                lock.unlock();
            }
        }
    }

    public <T> T lockRun(String key, Supplier<T> run, int wait, int leaseSecond) throws InterruptedException {
        RLock lock = client.getLock(key);
        boolean lockRes = lock.tryLock(wait, leaseSecond, TimeUnit.SECONDS);
        try {
            if (lockRes) {
                return run.get();
            } else {
                throw new MSBizNormalException(ExcptEnum.DLOCK_GET_ERROR);
            }

        } catch (Exception e) {
            throw e;
        } finally {
            if (lockRes) {
                lock.unlock();
            }
        }
    }

}
