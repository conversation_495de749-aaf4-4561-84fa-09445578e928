package com.cfpamf.ms.insur.operation.visitor.vo;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 产品访问
 *
 * <AUTHOR>
 * @date 2021/4/30 11:01
 */
@Data
public class ProductVisitVo {
    /**
     * 产品id
     */
    private Long productId;
    /**
     * 产品名
     */
    private String productName;
    /**
     * 产品类型
     */
    private String productType;
    /**
     * 产品图片
     */
    private String productImagUrl;
    /**
     * 分享时间
     */
    private LocalDateTime shareTime;
    /**
     * 访问人数
     */
    private int visitorCount;
    /**
     * 访问次数
     */
    private int visitCount;

    /**
     * 访客信息集合
     */
    private List<BaseVisitorVo> visitorList;
}
