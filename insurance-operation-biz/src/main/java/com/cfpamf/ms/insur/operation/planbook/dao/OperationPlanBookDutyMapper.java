package com.cfpamf.ms.insur.operation.planbook.dao;


import com.cfpamf.ms.insur.operation.base.dao.CommonMapper;
import com.cfpamf.ms.insur.operation.planbook.entity.OperationPlanBookDuty;
import org.apache.ibatis.annotations.Mapper;
import tk.mybatis.mapper.entity.Example;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface OperationPlanBookDutyMapper extends CommonMapper<OperationPlanBookDuty> {

    /**
     * 根据计划书id删除数据
     *
     * @param planBookId
     * @return
     */
    default Integer deleteByPlanBookId(Integer planBookId) {
        Example example = new Example(OperationPlanBookDuty.class);
        example.createCriteria().andEqualTo("planBookId", planBookId);
        return deleteByExample(example);
    }

    /**
     * 查询计划书制作责任配置信息
     * @param planBookId
     * @return
     */
    default List<OperationPlanBookDuty> queryPlanBookDutyListByPlanBookId(Integer planBookId) {
        Example example = new Example(OperationPlanBookDuty.class);
        example.createCriteria().andEqualTo("planBookId", planBookId).andEqualTo("enabledFlag", 0);
        return selectByExample(example);
    }


}