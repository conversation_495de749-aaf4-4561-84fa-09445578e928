package com.cfpamf.ms.insur.operation.aicall.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.cfpamf.ms.insur.operation.aicall.dao.Ai94CallBackRecordDetailMapper;
import com.cfpamf.ms.insur.operation.aicall.dao.Ai94CallBackRecordMapper;
import com.cfpamf.ms.insur.operation.aicall.dao.Ai94CallTaskDetailMapper;
import com.cfpamf.ms.insur.operation.aicall.dao.Ai94CallTaskMapper;
import com.cfpamf.ms.insur.operation.aicall.entity.Ai94CallBackRecord;
import com.cfpamf.ms.insur.operation.aicall.entity.Ai94CallBackRecordDetail;
import com.cfpamf.ms.insur.operation.aicall.entity.Ai94CallTask;
import com.cfpamf.ms.insur.operation.aicall.entity.Ai94CallTaskDetail;
import com.cfpamf.ms.insur.operation.aicall.event.AiCallBackEvent;
import com.cfpamf.ms.insur.operation.aicall.event.BackRecord;
import com.cfpamf.ms.insur.operation.aicall.service.CallBackServer;
import com.cfpamf.ms.insur.operation.aicall.vo.CallBackVo;
import com.cfpamf.ms.insur.operation.aicall.vo.request.Ai94CallBackVo;
import com.cfpamf.ms.insur.operation.base.event.EventBusEngine;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


import java.util.ArrayList;
import java.util.List;

@Component("Ai94CallBack")
@Slf4j(topic = "Ai94CallBackServer")
public class Ai94CallBackServer implements CallBackServer {
    @Autowired
    Ai94CallTaskDetailMapper ai94CallTaskDetailMapper;

    @Autowired
    Ai94CallTaskMapper ai94CallTaskMapper;

    @Autowired
    Ai94CallBackRecordMapper ai94CallBackRecordMapper;

    @Autowired
    Ai94CallBackRecordDetailMapper ai94CallBackRecordDetailMapper;
    @Autowired
    EventBusEngine eventBusEngine;

    @Override
    public CallBackVo processCallBack(String resultString) {
        JSONObject resultJson = JSONObject.parseObject(resultString);
        return this.processCallBack(resultJson);
    }

    @Override
    public CallBackVo processCallBack(JSONObject resultJson) {
        String taskBatchCode = resultJson.getString("taskId");
        String batchId = resultJson.getString("batchId");
        String tag = resultJson.getString("tag");
        String callId = resultJson.getString("callId");
        //查询当前外呼任务是否已有回调。如果已经存在回调记录则不处理回调信息直接返回成功
        Ai94CallBackRecord temp = new Ai94CallBackRecord();
        temp.setTaskBatchCode(taskBatchCode);
        temp.setBatchId(batchId);
        temp.setTag(tag);
        temp.setCallId(callId);
        List<Ai94CallBackRecord> result = this.ai94CallBackRecordMapper.select(temp);
        if(CollectionUtils.isNotEmpty(result)){
            Ai94CallBackVo callBackVo = new Ai94CallBackVo();
            callBackVo.setStatus(200);
            callBackVo.setMessage("success");
            return callBackVo;
        }
        //获取当前回调本地任务明细详情
        Ai94CallTaskDetail ai94CallTaskDetail = new Ai94CallTaskDetail();
        ai94CallTaskDetail.setTaskBatchCode(taskBatchCode);
        ai94CallTaskDetail.setTaskMemberId(batchId);
        ai94CallTaskDetail.setOutSourceId(Long.valueOf(tag));
        ai94CallTaskDetail = ai94CallTaskDetailMapper.selectOne(ai94CallTaskDetail);
        //获取当前回调本地任务详情
        Ai94CallTask ai94CallTask = new Ai94CallTask();
        ai94CallTask.setTaskId(ai94CallTaskDetail.getTaskId());
        ai94CallTask = ai94CallTaskMapper.selectOne(ai94CallTask);
        //构建回调记录
        Ai94CallBackRecord ai94CallBackRecord = JSONObject.parseObject(resultJson.toJSONString(),Ai94CallBackRecord.class);
        ai94CallBackRecord.setId(null);//回调的时候会出现 id  需要置空
        ai94CallBackRecord.setTaskBatchCode(taskBatchCode);
        ai94CallBackRecord.setTaskId(ai94CallTaskDetail.getTaskId());
        ai94CallBackRecord.setTaskDetailId(ai94CallTaskDetail.getTaskDetailId());
        this.ai94CallBackRecordMapper.insertOrUpdate(ai94CallBackRecord);
        //构建对话明细记录
        List<BackRecord> records = new ArrayList<>();
        if(resultJson.getJSONArray("chats")!=null){

            resultJson.getJSONArray("chats").forEach(item->{
                JSONObject itemJson = (JSONObject) item;
                Ai94CallBackRecordDetail ai94CallBackRecordDetail = JSONObject.parseObject(itemJson.toJSONString(), Ai94CallBackRecordDetail.class);
                ai94CallBackRecordDetail.setRecordId(ai94CallBackRecord.getId());
                this.ai94CallBackRecordDetailMapper.insertOrUpdate(ai94CallBackRecordDetail);
                buildcallBackEventRecords(records,ai94CallBackRecordDetail);
            });
        }
        //发布回调内部事件
        AiCallBackEvent aiCallBackEvent = buildCallBackEvent(ai94CallBackRecord, resultJson, ai94CallTaskDetail );
        aiCallBackEvent.setTaskId(ai94CallTask.getTaskId());
        aiCallBackEvent.setOutSourceId(ai94CallTaskDetail.getOutSourceId());
        aiCallBackEvent.setTaskType(ai94CallTask.getTaskType());
        aiCallBackEvent.setRecords(records);
        eventBusEngine.publish(aiCallBackEvent);

        Ai94CallBackVo ai94CallBackVo = new Ai94CallBackVo();
        ai94CallBackVo.setStatus(200);
        ai94CallBackVo.setMessage("ok");
        return ai94CallBackVo;
    }

    private void buildcallBackEventRecords(List<BackRecord> records, Ai94CallBackRecordDetail ai94CallBackRecordDetail) {
        BackRecord backRecord = new BackRecord();
        backRecord.setRole("0".equals(ai94CallBackRecordDetail.getFromNumber())?"speech":"voice");
        backRecord.setContextText(ai94CallBackRecordDetail.getContent());
        backRecord.setStart(ai94CallBackRecordDetail.getCreateTime());
        records.add(backRecord);
    }

    private AiCallBackEvent buildCallBackEvent(Ai94CallBackRecord ai94CallBackRecord, JSONObject resultJson,Ai94CallTaskDetail  ai94CallTaskDetail) {
        AiCallBackEvent aiCallBackEvent = new AiCallBackEvent();
        aiCallBackEvent.setIntent(ai94CallBackRecord.getIntentTag());
        aiCallBackEvent.setOutSourceId(ai94CallTaskDetail.getOutSourceId());
        aiCallBackEvent.setContactUUID(ai94CallBackRecord.getId().toString());
        aiCallBackEvent.setMobile(ai94CallBackRecord.getNumber());
        aiCallBackEvent.setTalkingTimeLen(ai94CallBackRecord.getSpeakingDuration());
        aiCallBackEvent.setEndType(resultJson.getInteger("statusCode")==1?1:0);
        aiCallBackEvent.setEndTypeReason(resultJson.getString("statusDescription"));
        aiCallBackEvent.setIndividualTag(ai94CallBackRecord.getIndividualTag());
        return aiCallBackEvent;
    }

}
