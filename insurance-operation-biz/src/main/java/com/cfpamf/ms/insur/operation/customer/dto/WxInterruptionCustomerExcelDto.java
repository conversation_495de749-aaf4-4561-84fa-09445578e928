package com.cfpamf.ms.insur.operation.customer.dto;

import org.springframework.util.StringUtils;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import com.cfpamf.common.ms.exception.MSBizNormalException;
import com.cfpamf.ms.insur.operation.base.annotaions.Mask;
import com.cfpamf.ms.insur.operation.customer.enums.EnumRenewalIntention;
import com.cfpamf.ms.insur.operation.pco.util.LocalDateTimeConverter;
import com.cfpamf.ms.insur.operation.phoenix.pojo.vo.PhoenixEmpTodoBackVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.beanutils.PropertyUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
@ContentRowHeight(18)
@HeadRowHeight(20)
@ColumnWidth(20)
public class WxInterruptionCustomerExcelDto {

    /**
     * 客户姓名
     */
    @ExcelProperty("客户姓名")
    String customerName;

    @ExcelProperty("主")
    @ApiModelProperty(value = "是否信贷客户 0：否 1：是")
    String loan1;

    /**
     * 是否信贷客户
     */
    @ExcelProperty("在")
    @ApiModelProperty(value = "是否在贷客户")
    String currentLoan1;
    /**
     * 客户手机号
     */
    @ExcelProperty("客户手机号")
    @Mask(dataType = Mask.DataType.MOBILE)
    String cellPhone;

    @ExcelProperty(value = "断保时间",converter = LocalDateTimeConverter.class)
    @ApiModelProperty("断保时间")
    LocalDateTime lastInterruptionTime;

    @ExcelProperty("跟进情况")
    @ApiModelProperty(value = "跟进情况 noFollowUp:未跟进、high:高意向,median:中意向, low:低意向,none:无意向,lose_contact:联系不上")
    String intention;

    @ExcelProperty("断保天数")
    @ApiModelProperty(value = "断保天数")
    Integer days;

    @ExcelProperty(value = "最近跟进时间",converter = LocalDateTimeConverter.class)
    @ApiModelProperty("最近跟进时间")
    LocalDateTime followTime;

    @ExcelProperty("管护人姓名")
    @ApiModelProperty("管护人姓名")
    String lastCustomerAdminName;

    /**
     * 区域
     */

    @ExcelProperty("区域")
    @ApiModelProperty(value = "区域")
    String regionName;

    /**
     * 机构
     */

    @ExcelProperty("机构")
    @ApiModelProperty(value = "机构")
    String orgName;


public void copyProperties(WxInterruptionCustomerDto source) {
    try {
        PropertyUtils.copyProperties(this, source);
    } catch (Exception e) {
        throw new MSBizNormalException("", "对象复制失败");
    }
}

}
