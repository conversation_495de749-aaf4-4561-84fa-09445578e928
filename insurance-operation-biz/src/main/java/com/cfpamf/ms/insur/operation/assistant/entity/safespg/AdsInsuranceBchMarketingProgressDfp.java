package com.cfpamf.ms.insur.operation.assistant.entity.safespg;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Id;
import java.io.Serializable;

/**
 * 保险助手分支集市;
 * <AUTHOR> 王浩
 * @date : 2024-2-28
 */
@ApiModel(value = "保险助手分支集市")
@Data
public class AdsInsuranceBchMarketingProgressDfp implements Serializable,Cloneable{

    @ApiModelProperty("分支名称")
    private String bchName;

    @ApiModelProperty("分支编码")
    private String bchCode;

    @ApiModelProperty("片区名称")
    private String districtName;

    @ApiModelProperty("片区编码")
    private String districtCode;

    @ApiModelProperty("区域名称")
    private String areaName;

    @ApiModelProperty("区域编码")
    private String areaCode;

    @ApiModelProperty("当月标准保费")
    private Double smAssessConvertInsuranceAmt;

    @ApiModelProperty("当年标准保费")
    private Double syAssessConvertInsuranceAmt;

    @ApiModelProperty("当月标准保费目标")
    private Double smAssessConvertInsuranceAmtTarget;

    @ApiModelProperty("当年标准保费目标")
    private Double syAssessConvertInsuranceAmtTarget;

    @ApiModelProperty("当月标准保费达成率")
    private Double smAssessConvertInsuranceAmtAchieveRate;

    @ApiModelProperty("当年标准保费达成率")
    private Double syAssessConvertInsuranceAmtAchieveRate;

    @ApiModelProperty("当月异业保费配比")
    private Double smOfflineLoanInsuranceRate;

    @ApiModelProperty("当年异业保费配比")
    private Double syOfflineLoanInsuranceRate;

    @ApiModelProperty("当月异业保费配比目标")
    private Double smOfflineLoanInsuranceRateTarget;

    @ApiModelProperty("当年异业保费配比目标")
    private Double syOfflineLoanInsuranceRateTarget;

    @ApiModelProperty("当月留存率")
    private Double smInsuranceRetentionRate;

    @ApiModelProperty("当年留存率")
    private Double syInsuranceRetentionRate;

    @ApiModelProperty("当月留存率目标")
    private Double smInsuranceRetentionRateTarget;

    @ApiModelProperty("当年留存率目标")
    private Double syInsuranceRetentionRateTarget;

    @ApiModelProperty("当月信贷客户留存率")
    private Double smLoanInsuranceRetentionRate;

    @ApiModelProperty("当年信贷客户留存率")
    private Double syLoanInsuranceRetentionRate;

    @ApiModelProperty("当月非贷客户留存率")
    private Double smUnloanInsuranceRetentionRate;

    @ApiModelProperty("当年非贷客户留存率")
    private Double syUnloanInsuranceRetentionRate;

    @ApiModelProperty("当月信贷客户转化率")
    private Double smLoanCustTransRate;

    @ApiModelProperty("当年信贷客户转化率")
    private Double syLoanCustTransRate;

    @ApiModelProperty("上月标准保费")
    private Double lmAssessConvertInsuranceAmt;

    @ApiModelProperty("分区")
    private String pt;

    /**
     * 年度余额目标。
     */
    @ApiModelProperty("年度余额目标")
    private Double syLoanBalanceTarget;

    /**
     * 下月还款计划金额。
     */
    @ApiModelProperty("下月还款计划金额")
    private Double nmRepaymentPlanAmt;

    /**
     * 月底贷款余额。
     */
    @ApiModelProperty("月底贷款余额")
    private Double smLoanBalance;

    /**
     * 月度主营标准保费。
     */
    @ApiModelProperty("月度主营标准保费")
    private Double smLoanNormInsuranceAmt;

    /**
     * 月度主营标准保费目标。
     */
    @ApiModelProperty("月度主营标准保费目标")
    private Double smLoanNormInsuranceAmtTarget;

    /**
     * 月度主营规模保费。
     */
    @ApiModelProperty("月度主营规模保费")
    private Double smLoanInsuranceAmt;
    /**
     * 月度主营规模保费目标。
     */
    @ApiModelProperty("月度主营规模保费目标")
    private Double smLoanInsuranceAmtTarget;

    /**
     * 月度非主营留存标准保费。
     */
    @ApiModelProperty("月度非主营留存标准保费")
    private Double smUnloanRpNormInsuranceAmt;

    /**
     * 月度非主营留存规模保费。
     */
    @ApiModelProperty("月度非主营留存规模保费")
    private Double smUnloanRpInsuranceAmt;

    /**
     * 月度非主营留存标准保费目标。
     */
    @ApiModelProperty("月度非主营留存标准保费目标")
    private Double smUnloanRpNormInsuranceAmtTarget;

    /**
     * 月度非主营留存规模保费目标。
     */
    @ApiModelProperty("月度非主营留存规模保费目标")
    private Double smUnloanRpInsuranceAmtTarget;

    /**
     * 月度主营留存标准保费。
     */
    @ApiModelProperty("月度主营留存标准保费")
    private Double smLoanRpNormInsuranceAmt;

    /**
     * 月度主营留存规模保费。
     */
    @ApiModelProperty("月度主营留存规模保费")
    private Double smLoanRpInsuranceAmt;

    /**
     * 月度主营留存标准保费目标。
     */
    @ApiModelProperty("月度主营留存标准保费目标")
    private Double smLoanRpNormInsuranceAmtTarget;

    /**
     * 月度主营留存规模保费目标。
     */
    @ApiModelProperty("月度主营留存规模保费目标")
    private Double smLoanRpInsuranceAmtTarget;

    /**
     * 月度非主营拓新标准保费。
     */
    @ApiModelProperty("月度非主营拓新标准保费")
    private Double smUnloanNcNormInsuranceAmt;

    /**
     * 月度非主营拓新规模保费。
     */
    @ApiModelProperty("月度非主营拓新规模保费")
    private Double smUnloanNcInsuranceAmt;

    /**
     * 月度非主营拓新标准保费目标。
     */
    @ApiModelProperty("月度非主营拓新标准保费目标")
    private Double smUnloanNcNormInsuranceAmtTarget;

    /**
     * 月度非主营拓新规模保费目标。
     */
    @ApiModelProperty("月度非主营拓新规模保费目标")
    private Double smUnloanNcInsuranceAmtTarget;

    /**
     * 当月生成非主营的续保待办保单保费
     */
    @ApiModelProperty("当月生成非主营的续保待办保单保费")
    private Double smUnloanRenewShortTodoInsuranceAmt;

    /**
     * 当月生成主营的续保待办保单保费
     */
    @ApiModelProperty("当月生成主营的续保待办保单保费")
    private Double smLoanRenewShortTodoInsuranceAmt;

    @ApiModelProperty("截止当前非贷续保待办保单金额")
    private Double unloanRenewShortTodoInsuranceAmt;

    @ApiModelProperty("截止当前信贷续保待办保单金额")
    private Double LoanRenewShortTodoInsuranceAmt;
}
