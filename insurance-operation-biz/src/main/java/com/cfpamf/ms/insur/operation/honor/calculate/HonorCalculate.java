package com.cfpamf.ms.insur.operation.honor.calculate;

import com.cfpamf.ms.insur.operation.honor.dto.HonorCalculateRule;
import com.cfpamf.ms.insur.operation.honor.dto.HonorsCalculateResults;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface HonorCalculate {
    List<HonorsCalculateResults> calculate(HonorCalculateRule rule);
    List<HonorsCalculateResults> searchHonorInAction(HonorCalculateRule rule);
    List<HonorsCalculateResults> calculateByPeople(HonorCalculateRule rule);
    List<HonorsCalculateResults> searchObjectHonorRank(HonorCalculateRule rule);
}
