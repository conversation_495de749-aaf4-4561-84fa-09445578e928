package com.cfpamf.ms.insur.operation.pco.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.cfpamf.ms.insur.operation.dingtalk.entity.DingTalkSwFormInstanceDetail;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import java.util.List;

/**
 * <AUTHOR> 2022/9/8 15:05
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class PcoDayFormVo {

    @ExcelProperty
    @ApiModelProperty("提交人")
    String submitUserName;


    @ApiModelProperty("提交时间")
    java.time.LocalDateTime submitTime;

    @ApiModelProperty("工号")
    String jobNumber;


    @ApiModelProperty("区域名称")
    String regionName;

    /**
     * 分支
     */
    @ApiModelProperty("分支")
    String orgName;

    @ExcelProperty
    @ApiModelProperty("表单编码")
    String formCode;

    @ApiModelProperty("星期")
    String week;

    @ApiModelProperty("userid")
    String submitUserId;

    @ApiModelProperty("是否填写")
    String isWrite;

    @ApiModelProperty("日期")
    java.time.LocalDate formDate;


    @ApiModelProperty("表单实例id")
    String formInstanceId;

    @ApiModelProperty("单日自评分数")
    Integer scope;


    @ApiModelProperty("表单详情")
    List<DingTalkSwFormInstanceDetail> forms;


}
