package com.cfpamf.ms.insur.operation.addCommission.dao;

import com.cfpamf.ms.insur.operation.addCommission.po.WhaleAddCommissionDetail;
import com.cfpamf.ms.insur.operation.addCommission.po.WhalePreservationSettlementPushLog;
import com.cfpamf.ms.insur.operation.base.dao.CommonMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.entity.Example;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface WhalePreservationSettlementPushLogMapper extends CommonMapper<WhalePreservationSettlementPushLog> {

    void updatePushState(@Param("pushLog") WhalePreservationSettlementPushLog pushLog);

    default List<WhalePreservationSettlementPushLog> getListByPushState(int i) {
        Example example = new Example(WhalePreservationSettlementPushLog.class);
        example.createCriteria()
                .andEqualTo("pushState",i);
        return selectBy<PERSON>xample(example);
    }
}
