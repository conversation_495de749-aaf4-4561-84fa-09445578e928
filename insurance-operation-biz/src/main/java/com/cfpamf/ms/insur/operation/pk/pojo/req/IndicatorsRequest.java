package com.cfpamf.ms.insur.operation.pk.pojo.req;

import com.cfpamf.ms.insur.operation.base.constant.BaseConstants;
import com.cfpamf.ms.insur.operation.pk.consts.PkConst;
import com.cfpamf.ms.insur.operation.pk.enums.EnumIndicatorType;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.time.YearMonth;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class IndicatorsRequest {


    @NotNull(message = "pk指标类型不能为空")
    @ApiModelProperty(value = "1.区域 2.分支 3.客户经理 4.督导， 必填")
    String type;

    @NotEmpty(message = "查询编码不能为空")
    @ApiModelProperty(value = "多个编码列表，客户经理、督导为工号，分支以及区域为分支以及区域编码 必填")
    List<String> codeList;

    @NotEmpty(message = "查询指标类型为空")
    @ApiModelProperty(value = "查询指标类型列表")
    List<String> indicatorTypeList;

    @NotNull(message = "PK开始时间不能为空")
    @ApiModelProperty(value = "开始日期： 2023-08-01")
    LocalDate startDate;

    @NotNull(message = "PK结束时间不能为空")
    @ApiModelProperty(value = "结束日期 ： 2023-08-03")
    LocalDate endDate;

    @JsonIgnore
    public boolean isArea() {
        return Objects.equals(type, PkConst.PK_TYPE_AREA);
    }


    @JsonIgnore
    public boolean isOrg() {
        return Objects.equals(type, PkConst.PK_TYPE_ORG);
    }

    @JsonIgnore
    public boolean isEmp() {
        return Objects.equals(type, PkConst.PK_TYPE_EMP);
    }

    @JsonIgnore
    public boolean isNear3Month() {
        YearMonth preMonth = YearMonth.now().minusMonths(1L);
        YearMonth pre3Month = preMonth.minusMonths(2L);
        LocalDate end = preMonth.atEndOfMonth();
        LocalDate start = pre3Month.atDay(1);
        return start.isEqual(startDate)
                && end.isEqual(endDate);
    }

    /**
     * 是否有人均相关指标
     *
     * @return
     */
    @JsonIgnore
    public boolean hasAvg() {
        return
                indicatorTypeList.stream().anyMatch(s -> EnumIndicatorType.valueOfCode(s).isAvg());
    }

    @JsonIgnore
    public String getStartDateStr() {
        return BaseConstants.FMT_DATE.format(startDate);
    }

    @JsonIgnore
    public String getEndDateStr() {
        return BaseConstants.FMT_DATE.format(endDate);
    }

    @JsonIgnore
    public LocalDate getEndDateNextDay() {
        return endDate.plusDays(1L);
    }

    @JsonIgnore
    public boolean startIsToday() {
        return Objects.equals(LocalDate.now(),startDate);
    }

}