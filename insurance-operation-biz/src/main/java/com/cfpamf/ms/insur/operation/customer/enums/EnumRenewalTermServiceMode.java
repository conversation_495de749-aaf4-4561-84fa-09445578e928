package com.cfpamf.ms.insur.operation.customer.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @Date 2023/04/19 15:37
 * @Version 1.0
 */
@AllArgsConstructor
@Getter
public enum EnumRenewalTermServiceMode {

    /**
     * 联系方式：0未知 1 微信联系 2电话联系 3实地面访 4 无法联系
     */
    LOSE_CONTACT0("0","联系不上"),
    WECHAT("1","微信联系"),
    PHONE("2","电话联系"),
    OFFLINE_VISIT("3","实地面访"),
    LOSE_CONTACT("4","无法联系");

    String code;
    String desc;
    public static String dict(String code) {
        if(Objects.isNull(code)){
            return "";
        }
        return Stream.of(values())
                .filter(em -> em.getCode().equals(code))
                .findFirst().map(EnumRenewalTermServiceMode::getDesc)
                .orElse("");
    }
}
