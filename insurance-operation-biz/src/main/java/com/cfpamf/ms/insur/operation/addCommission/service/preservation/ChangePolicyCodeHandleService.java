package com.cfpamf.ms.insur.operation.addCommission.service.preservation;

import com.alibaba.fastjson.JSONObject;
import com.cfpamf.ms.insur.operation.addCommission.convertor.AddCommissionConvert;
import com.cfpamf.ms.insur.operation.addCommission.dao.*;
import com.cfpamf.ms.insur.operation.addCommission.dto.*;
import com.cfpamf.ms.insur.operation.addCommission.enums.CommissionSceneTypeEnum;
import com.cfpamf.ms.insur.operation.addCommission.po.WhaleAddCommissionDetail;
import com.cfpamf.ms.insur.operation.addCommission.po.WhaleAddCommissionDetailItem;
import com.cfpamf.ms.insur.operation.addCommission.po.WhaleAddCommissionSettlementPush;
import com.cfpamf.ms.insur.operation.base.util.DateUtils;
import com.cfpamf.ms.insur.operation.whale.model.PreservationDetail;
import com.cfpamf.ms.insur.operation.whale.model.WhaleContract;
import lombok.AccessLevel;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component("changePolicyCodePreservation")
@FieldDefaults(level = AccessLevel.PRIVATE)
@Slf4j
public class ChangePolicyCodeHandleService extends AbstractPreservationHandleService{

    @Autowired
    WhaleAddCommissionDetailItemMapper whaleAddCommissionDetailItemMapper;
    @Autowired
    WhaleAddCommissionDetailMapper whaleAddCommissionDetailMapper;
    @Autowired
    WhaleAddCommissionDetailItemBackUpsMapper whaleAddCommissionDetailItemBackUpsMapper;
    @Autowired
    WhaleAddCommissionDetailBackUpsMapper whaleAddCommissionDetailBackUpsMapper;
    @Autowired
    WhaleAddCommissionSettlementPushMapper whaleAddCommissionSettlementPushMapper;

    @Override
    public PreservationSettlementDto doAddCommissionCorrect(PreservationOperationDto operationDto, List<AddCommissionSettlementPushDto> pushDtos, PreservationDto preservationDto) {
        log.info("保单号/批单号变更加佣记录处理,operationDto:{},pushDto:{}", JSONObject.toJSONString(operationDto), JSONObject.toJSONString(pushDtos));
        PreservationOperationDetail after = operationDto.getAfter();
        //1、历史单批单号变更，加佣记录作废
        List<WhaleAddCommissionDetailItem> whaleAddCommissionDetailItems = pushDtos.stream().map(dto->{
            WhaleAddCommissionDetailItem whaleAddCommissionDetailItem = AddCommissionConvert.INS.cvtItem(dto);
            whaleAddCommissionDetailItem.setSettlementState(3);
            return whaleAddCommissionDetailItem;
        }).collect(Collectors.toList());

        String requestId = preservationDto.getRequestId();
        List<AddCommissionSettlementPushDto> resultPushDtos = new ArrayList<>();

        //2、初始化新保单号加佣记录
        pushDtos.forEach(dto->{
            WhaleAddCommissionDetailItem detailItem = AddCommissionConvert.INS.cvtItem(dto);
            detailItem.setId(null);
            detailItem.setPolicyNo(StringUtils.isEmpty(after.getPolicyNo()) ? detailItem.getPolicyNo() : after.getPolicyNo());
            detailItem.setAcceptanceState(!Objects.isNull(dto.getSettlementState()) && dto.getSettlementState()==0 ? 0 : 1);
            detailItem.setRequestId(!Objects.isNull(dto.getSettlementState()) && dto.getSettlementState()==0 ? "" :requestId);
            detailItem.setSettlementState(!Objects.isNull(dto.getSettlementState()) && dto.getSettlementState()==0 ? 0 : 1);
            detailItem.setEndorsementNo(StringUtils.isEmpty(after.getEndorsementNo()) ? detailItem.getEndorsementNo() : after.getEndorsementNo());
            detailItem.setDataIndex(detailItem.getPolicyNo()+"|"+detailItem.getEndorsementNo()+"|"+detailItem.getInsuredCode()+"|"+detailItem.getSellProductCode()+"|"+detailItem.getRiskCode()+"|"+detailItem.getTermNum()+"|"+detailItem.getProductStatus());
            detailItem.setUuid(detailItem.getPolicyNo()+"|"+detailItem.getEndorsementNo()+"|"+detailItem.getInsuredCode()+"|"+detailItem.getSellProductCode()+"|"+detailItem.getRiskCode()+"|"+detailItem.getTermNum()+"|"+detailItem.getProductStatus()+"|"+detailItem.getDataId());
            whaleAddCommissionDetailItems.add(detailItem);

            AddCommissionSettlementPushDto pushDto = AddCommissionConvert.INS.cvtPushDto(detailItem,dto);
            pushDto.setEventTypeCode(operationDto.getSettlementEventType().getEventCode());
            pushDto.setAccountTime(DateUtils.toDateString(new Date()));
            pushDto.setApplyTime(DateUtils.toDateString(new Date()));
            pushDto.setPreservationEffectTime(DateUtils.toDateString(new Date()));
            resultPushDtos.add(pushDto);
        });
        //结算状态都变更为已作废
        pushDtos.forEach(dto->{
            dto.setInitialSettlementState(dto.getSettlementState());
            dto.setSettlementState(3);
        });
        resultPushDtos.addAll(pushDtos);
        log.info("保单号/批单号变更加佣明细：{}", JSONObject.toJSONString(whaleAddCommissionDetailItems));

        ChangePolicyCodeHandleService me = (ChangePolicyCodeHandleService) AopContext.currentProxy();

        //3、初始化汇总记录比例
        List<WhaleAddCommissionDetail> detailList = convertDetailPo(whaleAddCommissionDetailItems);

        //4.根据原结算状态过滤核算中的数据（核算中的额数据不需要推送业财）
        List<AddCommissionSettlementPushDto> result = resultPushDtos.stream()
                .filter(dto->!Objects.isNull(dto.getSettlementState()) && dto.getInitialSettlementState()!=0)
                .collect(Collectors.toList());

        me.transactional(() -> {
            //备份数据
            whaleAddCommissionDetailItemBackUpsMapper.insertBackUps(whaleAddCommissionDetailItems,"保单号/批单号变更");
            //备份汇总表数据
            whaleAddCommissionDetailBackUpsMapper.insertBackUps(whaleAddCommissionDetailItems,"保单号/批单号变更");
            //更新加佣记录
            whaleAddCommissionDetailItemMapper.batchUpdateProportionPreservation(whaleAddCommissionDetailItems);
            //更新对应uuid的汇总比例
            whaleAddCommissionDetailMapper.batchUpdate(detailList);

            //插入业财结算推送数据明细
            if (!CollectionUtils.isEmpty(result)) {
                List<WhaleAddCommissionSettlementPush> settlementPushes = resultPushDtos.stream().map(dto -> {
                    WhaleAddCommissionSettlementPush settlementPush = new WhaleAddCommissionSettlementPush();
                    BeanUtils.copyProperties(dto, settlementPush);
                    settlementPush.setRequestId(requestId);
                    return settlementPush;
                }).collect(Collectors.toList());
                whaleAddCommissionSettlementPushMapper.insertList(settlementPushes);
            }
        });

        //封装推送业财入参
        PreservationSettlementDto settlementDto = new PreservationSettlementDto();
        settlementDto.setRequestId(requestId);
        settlementDto.setSettlementPushDtos(result);
        log.info("保单号/批单号变更加佣记录推送数据：{}", JSONObject.toJSONString(settlementDto));
        //推送业财
        return settlementDto;
    }

    @Override
    public void getOperationDto(PreservationOperationDto operationDto, PreservationDto preservationDto) {
        CommissionRedoDTO commissionRedoDTO = preservationDto.getCommissionRedoDTO();
        operationDto.setSettlementEventType(cvtSettlementEventType(commissionRedoDTO));
        PreservationOperationDetail before = new PreservationOperationDetail();
        PreservationOperationDetail after = new PreservationOperationDetail();

        if (CommissionSceneTypeEnum.ENDORSEMENT_NO.getCode().equals(commissionRedoDTO.getCommissionSceneTypeEnum())) {
            before.setEndorsementNo(commissionRedoDTO.getBeforeFiledValue());
            after.setEndorsementNo(commissionRedoDTO.getAfterFiledValue());
        } else {
            before.setPolicyNo(commissionRedoDTO.getBeforeFiledValue());
            after.setPolicyNo(commissionRedoDTO.getAfterFiledValue());
        }

        operationDto.setBefore(before);
        operationDto.setAfter(after);
    }

    /**
     * 单独一个事务
     *
     * @param runnable
     */
    @Transactional(rollbackFor = Exception.class)
    public void transactional(Runnable runnable) {
        runnable.run();
    }
}
