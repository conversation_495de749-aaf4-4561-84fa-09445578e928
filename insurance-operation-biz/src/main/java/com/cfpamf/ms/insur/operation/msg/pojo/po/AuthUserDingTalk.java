package com.cfpamf.ms.insur.operation.msg.pojo.po;

import com.cfpamf.ms.insur.operation.base.entity.BaseNoUserEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

/**
 * <AUTHOR>
 */
@ApiModel
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class AuthUserDingTalk extends BaseNoUserEntity {


    @ApiModelProperty("用户名称")
    String employeeName;

    String dingUserId;

    @ApiModelProperty("工号")
    String jobNumber;

    @ApiModelProperty("unionId")
    String unionId;


}
