package com.cfpamf.ms.insur.operation.settlement.config;

import feign.RequestInterceptor;
import feign.RequestTemplate;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 */
@Configuration
public class WhaleFeignInterceptor implements RequestInterceptor {
    @Override
    public void apply(RequestTemplate template) {
        template.header("deviceType", "miniapp");
        template.header("appName", "chongho-miniapp");
        template.header("appVersion", "1.0.0");
        template.header("timeStamp", ""+System.currentTimeMillis());
    }
}
