package com.cfpamf.ms.insur.operation.activity.entity;


import com.cfpamf.ms.insur.operation.base.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Table;

/**
 * system_activity_product_rule
 *
 * <AUTHOR>
@Table(name = "system_activity_product_rule_whale")
@ApiModel(value = "营销活动奖励产品规则")
@Data
public class SystemActivityProductRuleWhale extends BaseEntity {


    /**
     * 营销活动奖励产品id
     */

    @ApiModelProperty(value = "营销活动奖励产品id")
    private Long systemActivityProductId;

    /**
     * 比较类型
     */

    @ApiModelProperty(value = "比较类型")
    private String ruleCode;

    /**
     * "比较符"
     */

    @ApiModelProperty(value = "比较符")
    private String compareSymbol;

    /**
     * 参数
     */
    @ApiModelProperty(value = "参数")
    private String params;
}