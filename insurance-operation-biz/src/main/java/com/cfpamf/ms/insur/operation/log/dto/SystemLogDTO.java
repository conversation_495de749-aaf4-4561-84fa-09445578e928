package com.cfpamf.ms.insur.operation.log.dto;

import lombok.Builder;
import lombok.Data;

/**
 * 日志对象DTO
 *
 * <AUTHOR>
 **/
@Data
@Builder
public class SystemLogDTO {

    /**
     * 用户Id
     */
    private String userId;

    /**
     * 登录IP
     */
    private String userIp;

    /**
     * 操作种类
     */
    private String actionType;

    /**
     * 模块名称
     */
    private String moduleName;

    /**
     * 操作描述
     */
    private String description;

    /**
     * 调用方法名称
     */
    private String method;

    /**
     * 调用URL
     */
    private String url;

    /**
     * 调用方法参数
     */
    private String parameters;

    /**
     * 操作结果
     */
    private Integer result;

    /**
     * 错误消息
     */
    private String message;

    /**
     * 耗时
     */
    private Long costTime;
}
