package com.cfpamf.ms.insur.operation.msg.pojo.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import java.math.BigDecimal;

/**
 * <AUTHOR> 2021/7/14 14:25
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class StatRankVO {


    @ApiModelProperty("排名名称")
    String rankTargetName;

    @ApiModelProperty("排名")
    Integer rank;

    String userName;

    String userId;

    String orgName;

    @ApiModelProperty("区域名字")
    String regionName;

    @ApiModelProperty("保费")
    BigDecimal amts;

    @ApiModelProperty("差额")
    BigDecimal exp;

    @ApiModelProperty("单量")
    Long cts;

    @ApiModelProperty("月度人均保费")
    BigDecimal avgPeople;
}
