package com.cfpamf.ms.insur.operation.phoenix.pojo.vo;


import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cfpamf.ms.insur.operation.aicall.dto.SessionRecordDto;
import com.cfpamf.ms.insur.operation.aicall.enums.AIIntentEnums;
import com.cfpamf.ms.insur.operation.pco.util.LocalDateTimeConverter;
import com.cfpamf.ms.insur.operation.phoenix.enums.EnumTodoBizType;
import com.cfpamf.ms.insur.operation.phoenix.enums.EnumTodoState;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Data
public class PhoenixEmpTodoBackVo {

    private static final String IG_FIELD = "idNumber";
    @ExcelProperty("客户姓名")
    String customerName;

    @ExcelProperty(value = "是否报案", converter = LocalDateTimeConverter.class)
    String claim;
    @ExcelProperty(value = "是否赔付", converter = LocalDateTimeConverter.class)
    String finishClaim;
    @ExcelProperty(value = "生成任务时间", converter = LocalDateTimeConverter.class)

    Long phoenixId;

    @ExcelProperty(value = "生成任务时间" ,converter = LocalDateTimeConverter.class)
    @ApiModelProperty("生成任务时间")
    LocalDateTime createTime;
    @ExcelProperty(value = "实际完成时间", converter = LocalDateTimeConverter.class)
    @ApiModelProperty("实际完成时间")
    LocalDateTime finishTime;
    @ExcelProperty(value = "计划完成时间", converter = LocalDateTimeConverter.class)
    @ApiModelProperty("计划完成时间")
    LocalDateTime planTime;
    @ExcelProperty("业务类型")
    @ApiModelProperty("业务类型")
    String bizType;
    @ApiModelProperty("业务类型中文")
    String bizType1;
    /**
     *
     */
    @ExcelProperty("任务状态")
    @ApiModelProperty("任务状态")
    String state;
    @ApiModelProperty("任务状态中文")
    String state1;
    @ExcelProperty("管护人姓名")
    String customerAdminName;
    @ExcelProperty("管护人工号")
    String customerAdminId;
    @ExcelProperty("区域")
    String regionName;
    @ExcelProperty("机构")
    String organizationName;

    String todoProperty;

    String orderId;

    String policyNo;

    @ApiModelProperty(name = "语音地址")
    String voiceAddress;

    @ApiModelProperty(name = "通话时长")
    Integer talkingTimeLen;

    @ApiModelProperty("机器人意向")
    String intent;

    @ApiModelProperty("机器人意向名称")
    String intentName;


    Integer isClaim;

    Integer isFinishClaim;


    /**
     * 是否理赔
     *
     * @return
     */
    public String getClaim() {
        if (Objects.equals(isClaim, 1)) {
            return "是";
        }
        return "否";
    }

    /**
     * 是否赔付
     *
     * @return
     */
    public String getFinishClaim() {
        if (Objects.equals(isFinishClaim, 1)) {
            return "是";
        }
        return "否";
    }

    public String getIntentName() {
        if (!org.springframework.util.StringUtils.isEmpty(intent)) {
            return AIIntentEnums.dict(intent);
        }
        return "";
    }

    @ApiModelProperty("任务类型")
    String taskType;

    String recordJson;

    @ApiModelProperty(name = "语音对话信息")
    List<SessionRecordDto> sessionRecordDtos;

    public List<SessionRecordDto> getSessionRecordDtos() {
        if (!org.springframework.util.StringUtils.isEmpty(recordJson)) {
            return JSONObject.parseArray(recordJson, SessionRecordDto.class);
        }
        return null;
    }

    /**
     * 删除敏感字段
     *
     * @return
     */
    public String getTodoProperty() {
        if (StringUtils.isBlank(todoProperty)) {
            return todoProperty;
        }
        JSONObject jsonObject = JSON.parseObject(todoProperty);
        if (jsonObject.containsKey(IG_FIELD)) {
            jsonObject.remove(IG_FIELD);
        }
        return jsonObject.toJSONString();
    }

    public String getBizType1() {
        if ((EnumTodoBizType.INTERRUPTION.name()).equals(bizType)) {
            bizType1 = "断保";
        } else if ((EnumTodoBizType.RENEW_SHORT.name()).equals(bizType)) {
            bizType1 = "续保";
        } else if ((EnumTodoBizType.RENEW_LONG.name()).equals(bizType)) {
            bizType1 = "续期";
        } else if ((EnumTodoBizType.LOAN.name()).equals(bizType)) {
            bizType1 = "A类大额客户";
        } else if ((EnumTodoBizType.LOAN_FIRST.name()).equals(bizType)) {
            bizType1 = "A类客户转化";
        }
        return bizType1;
    }

    public String getState1() {
        if ((EnumTodoState.TODO.name()).equals(state)) {
            state1 = "未完成";
        } else if ((EnumTodoState.DONE.name()).equals(state)) {
            state1 = "已完成";
        } else if ((EnumTodoState.TIMEOUT.name()).equals(state)) {
            state1 = "逾期";
        }
        return state1;
    }
}
