package com.cfpamf.ms.insur.operation.call.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 一键呼叫响应DTO
 *
 * <AUTHOR>
 * @date 2025-07-22
 */
@Data
public class CallResponseDto {

    @ApiModelProperty("呼叫记录ID")
    private Long callRecordId;

    @ApiModelProperty("呼叫SID")
    private String callSid;

    @ApiModelProperty("呼叫状态：0-初始化，1-呼叫中，2-呼叫成功，3-呼叫失败")
    private Integer callStatus;

    @ApiModelProperty("消息")
    private String message;

    @ApiModelProperty("是否成功")
    private Boolean success;
}
