package com.cfpamf.ms.insur.operation.call.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

/**
 * 一键呼叫响应DTO
 *
 * <AUTHOR>
 * @date 2025-07-22
 */
@Data
@ApiModel("一键呼叫响应")
@FieldDefaults(level = AccessLevel.PRIVATE)
public class CallResponseDto {

    @ApiModelProperty(value = "呼叫记录ID", example = "1001")
    Integer callRecordId;

    @ApiModelProperty(value = "呼叫SID", example = "call_sid_123456")
    String callSid;

    @ApiModelProperty(value = "呼叫状态", example = "1", notes = "0-初始化，1-呼叫中，2-呼叫成功，3-呼叫失败")
    Integer callStatus;

    @ApiModelProperty(value = "呼叫状态描述", example = "呼叫中")
    String callStatusDesc;

    @ApiModelProperty(value = "消息", example = "呼叫发起成功")
    String message;

    @ApiModelProperty(value = "是否成功", example = "true")
    Boolean success;

    /**
     * 获取呼叫状态描述
     */
    public String getCallStatusDesc() {
        if (callStatus == null) {
            return "未知";
        }
        switch (callStatus) {
            case 0:
                return "初始化";
            case 1:
                return "呼叫中";
            case 2:
                return "呼叫成功";
            case 3:
                return "呼叫失败";
            default:
                return "未知状态";
        }
    }
}
