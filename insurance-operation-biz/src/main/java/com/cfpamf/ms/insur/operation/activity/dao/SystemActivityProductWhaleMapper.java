package com.cfpamf.ms.insur.operation.activity.dao;

import com.cfpamf.ms.insur.operation.activity.entity.SystemActivityProduct;
import com.cfpamf.ms.insur.operation.activity.entity.SystemActivityProductWhale;
import com.cfpamf.ms.insur.operation.activity.form.SystemActivityConstProductSceneQueryForm;
import com.cfpamf.ms.insur.operation.activity.vo.SystemActivityProductDetailVo;
import com.cfpamf.ms.insur.operation.activity.vo.SystemActivityProductRuleVo;
import com.cfpamf.ms.insur.operation.activity.vo.SystemActivityProductVo;
import com.cfpamf.ms.insur.operation.base.dao.CommonMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * 系统活动产品数据层接口
 *
 * <AUTHOR>
 */
@Mapper
public interface SystemActivityProductWhaleMapper extends CommonMapper<SystemActivityProductWhale> {

    /**
     * 通过系统活动id集合查找对应的产品
     *
     * @param systemActivityIdList
     * @return
     */
    List<SystemActivityProductVo> getBySystemActivityIdList(@Param("systemActivityIdList") List<Long> systemActivityIdList);

    /**
     * 通过系统活动id集合查找对应的产品
     *
     * @param systemActivityId
     * @return
     */
    List<SystemActivityProduct> getBySystemActivityId(@Param("systemActivityId") Long systemActivityId);

    /**
     * 通过活动id软删除关联活动产品
     * @param systemActivityId
     */
    void softDeleteBySystemActivityId(@Param("systemActivityId") Long systemActivityId);

    /**
     * 通过产品id集合查找对应的活动产品
     *
     * @param productId
     * @param payTime
     * @return
     */
    List<SystemActivityProductDetailVo> getByListenerProductIdAndPayTime(@Param("productId") Integer productId, @Param("payTime") Date payTime);

    /**
     * 通过产品id集合查找对应的活动产品
     *
     * @param id
     * @return
     */
    List<SystemActivityProductDetailVo> getById(@Param("id") Integer id);

    Integer searchProductActivity(SystemActivityConstProductSceneQueryForm constProductForm);

    List<SystemActivityProductWhale> selectCopyProduct(@Param("id") Long id);

    Long getLastId();
}
