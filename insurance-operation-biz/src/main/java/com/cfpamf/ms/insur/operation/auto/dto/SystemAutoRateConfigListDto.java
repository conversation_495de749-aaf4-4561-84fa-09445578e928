package com.cfpamf.ms.insur.operation.auto.dto;

import com.cfpamf.ms.insur.operation.base.po.BasePO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * 客户断保跟进记录表
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class SystemAutoRateConfigListDto extends BasePO {
    @ApiModelProperty(name="产品主键")
    Integer productId;

    @ApiModelProperty(name="车险推广费配置id")
    Integer configId;

    @ApiModelProperty(name="版本号")
    Integer version;

    @ApiModelProperty(name="产品名称")
    String productName;

    @ApiModelProperty(name="渠道")
    String channel;

    @ApiModelProperty(name="保险公司名称")
    String companyName;

    @ApiModelProperty(name="备注")
    String remark;

    @ApiModelProperty(name="修改时间")
    LocalDateTime updateTime;

    @ApiModelProperty(name="修改人")
    String updateBy;
}
