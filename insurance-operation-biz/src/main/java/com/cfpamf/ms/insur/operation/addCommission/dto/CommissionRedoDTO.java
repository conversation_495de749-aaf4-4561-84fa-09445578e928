package com.cfpamf.ms.insur.operation.addCommission.dto;

import com.cfpamf.ms.insur.operation.addCommission.enums.CommissionRedoTypeEnum;
import com.cfpamf.ms.insur.operation.addCommission.enums.CommissionSceneTypeEnum;
import com.cfpamf.ms.insur.operation.addCommission.enums.EnumGovernBusiness;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import java.math.BigDecimal;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description
 * @date 2022/3/26 1:32 上午
 * @Version 1.0
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class CommissionRedoDTO {
    /**
     * 字段名称 订单id
     */
    @ApiModelProperty(value = "订单id")
    String orderId;

    /**
     * 字段名称 保单号
     */
    @ApiModelProperty(value = "期数")
    Integer termNum;

    @ApiModelProperty(value = "原字段值")
    String beforeFiledValue;

    @ApiModelProperty(value = "变更后字段值")
    String afterFiledValue;

    @ApiModelProperty(value = "变更字段信息")
    String commissionSceneTypeEnum;

    @ApiModelProperty(value = "刷数场景")
    String enumGovernBusiness;

    @ApiModelProperty(value = "操作类型")
    String operationType;

    @ApiModelProperty(value = "保单状态")
    String policyStatus;

    @ApiModelProperty(value = "被保人证件号")
    String insuredIdNumber;

    @ApiModelProperty(value = "保单号")
    String policyNo;
    @ApiModelProperty(value = "批单号")
    String endorsementNo;

    /**
     * 字段名称 佣金类型
     */
    @ApiModelProperty(value = "佣金类型 1 结算佣金 2 支付佣金 3 折算佣金")
    Integer commissionType;

    /**
     * 字段名称 佣金比例
     */
    @ApiModelProperty(value = "佣金比例")
    BigDecimal commissionRate;



}
