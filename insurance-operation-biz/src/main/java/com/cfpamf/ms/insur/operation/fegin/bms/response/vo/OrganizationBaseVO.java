package com.cfpamf.ms.insur.operation.fegin.bms.response.vo;

import lombok.Data;

import java.io.Serializable;

/**
 * @author:老K
 * @description:组织机构简易视图
 * @date:2018-08-30 16:55
 **/
@Data
public class OrganizationBaseVO implements Serializable {

    private static final long serialVersionUID = 937325654230250027L;

    private String batchNo;

    private Integer orgId;

    private Integer hrOrgId;

    private String orgCode;

    private String orgName;

    private String shortName;

    private Integer hrParentId;

    private String treePath;

    private Integer treeLevel;

    /**
     * 负责人北森用户Id
     */
    private Integer personInChargeId;

    /**
     * 负责人姓名
     */
    private String personInChargeName;

    /**
     * 区域分支hrbp
     */
    private Integer hrbpId;

    /**
     * 所属机构编号 --区域办公室属性
     */
    private String areaOrgCode;

    /**
     * 所属机构id-片区属性
     */
    private Integer zoneOrgId;
}
