package com.cfpamf.ms.insur.operation.base.response;


import com.cfpamf.common.ms.exception.MSException;
import com.cfpamf.common.ms.exception.MSNONeedRetryException;
import com.cfpamf.ms.bms.facade.exception.BizException;
import com.cfpamf.ms.insur.operation.base.exception.BusinessException;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.catalina.connector.ClientAbortException;
import org.springframework.validation.BindException;
import org.springframework.validation.BindingResult;
import org.springframework.web.HttpMediaTypeNotSupportedException;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.multipart.support.MissingServletRequestPartException;

import javax.validation.ConstraintViolationException;
import java.io.PrintWriter;
import java.io.StringWriter;
import java.util.Objects;

/**
 * rest api返回包装类
 *
 * <AUTHOR>
 */
@Data
@Slf4j
public class CommonResult<T> {

    /**
     * 默认成功code
     */
    public static final String SUCCESS_CODE = "0";

    /**
     * 默认成功message
     */
    public static final String SUCCESS_MESSAGE = "success";

    /**
     * 默认失败code
     */
    public static final String FAIL_CODE = "999";

    /**
     * 默认失败message
     */
    public static final String FAIL_MESSAGE = "接口异常";

    /**
     * 授权失败code
     */
    public static final String AUTH_FAIL_CODE = "401";

    /**
     * 授权失败message
     */
    public static final String AUTH_FAIL_MESSAGE = "授权失败请登录重试";

    /**
     * code
     */
    private String code;

    /**
     * message
     */
    private String message;

    /**
     * message
     */
    private String errorCode;

    /**
     * 异常
     */
    @JsonIgnore
    private Throwable throwable;

    /**
     * data
     */
    private T data;

    /**
     * 默认成功CommonResult
     */
    public static <T> CommonResult<T> successResult(T v) {
        CommonResult<T> result = new CommonResult<>();
        result.setCode(SUCCESS_CODE);
        result.setMessage(SUCCESS_MESSAGE);
        result.setData(v);
        return result;
    }

    public static CommonResult failResult(MSException e) {
        CommonResult result = new CommonResult();
        result.setCode(e.getErrorCode());
        result.setMessage(e.getMessage());
        result.setThrowable(e);
        return result;
    }

    /**
     * 默认失败CommonResult
     */
    public static CommonResult failResult() {
        CommonResult result = new CommonResult<>();
        result.setCode(CommonResult.FAIL_CODE);
        result.setMessage(FAIL_MESSAGE);
        return result;
    }

    public static CommonResult failResult(MSNONeedRetryException e) {
        CommonResult result = new CommonResult();
        result.setCode(e.getErrorCode());
        result.setMessage(e.getMessage());
        result.setThrowable(e);
        return result;
    }

    /**
     * 默认失败CommonResult
     */
    public static CommonResult failResult(Exception e) {
        CommonResult result = new CommonResult<>();
        result.setCode(CommonResult.FAIL_CODE);
        result.setMessage(FAIL_MESSAGE);
        result.setThrowable(e);
        return result;
    }

    /**
     * 授权失败CommonResult
     */
    public static CommonResult authFailResult() {
        CommonResult result = new CommonResult<>();
        result.setCode(CommonResult.AUTH_FAIL_CODE);
        result.setMessage(AUTH_FAIL_MESSAGE);
        return result;
    }

    /**
     * BizException构造CommonResult
     */
    public static CommonResult failResult(BizException e) {
        CommonResult result = new CommonResult<>();
        result.setCode("" + e.getCode());
        result.setMessage(e.getMessage());
        result.setThrowable(e);
        return result;
    }

    /**
     * BusinessException构造CommonResult
     */
    public static CommonResult failResult(BusinessException e) {
        CommonResult result = new CommonResult<>();
        result.setCode(FAIL_CODE);
        result.setMessage(e.getMessage());
        result.setErrorCode(e.getCode());
        result.setThrowable(e);
        return result;
    }


    /**
     * 调用是否成功
     *
     * @return
     */
    public boolean isSuccess() {
        return Objects.equals(code, SUCCESS_CODE);
    }

    /**
     * 异常堆栈字符串
     *
     * @return
     */
    public String getException() {
        if (throwable != null) {
            StringWriter stringWriter = new StringWriter();
            throwable.printStackTrace(new PrintWriter(stringWriter));
            return stringWriter.toString();
        }
        return null;
    }


    public static CommonResult of(MissingServletRequestPartException exception) {
        CommonResult result = new CommonResult<>();
        result.message = exception.getMessage();
        result.code = FAIL_CODE;
        return result;
    }


    public static CommonResult of(MSException exception) {
        CommonResult result = new CommonResult<>();
        result.errorCode = exception.getErrorCode();
        result.message = exception.getMessage();
        return result;
    }

    public static CommonResult of(MSNONeedRetryException exception) {
        CommonResult result = new CommonResult<>();
        result.code = exception.getErrorCode();
        result.errorCode = exception.getErrorCode();
        result.message = exception.getMessage();
        return result;
    }

    public static CommonResult of(HttpMediaTypeNotSupportedException exception) {
        CommonResult result = new CommonResult<>();
        result.code = FAIL_CODE;
        result.errorCode = FAIL_CODE;
        result.message = exception.getMessage();
        return result;
    }

    public static CommonResult of(ClientAbortException exception) {
        CommonResult result = new CommonResult<>();
        result.code = FAIL_CODE;
        result.errorCode = FAIL_CODE;
        result.message = exception.getMessage();
        return result;
    }

    public static CommonResult of(HttpRequestMethodNotSupportedException exception) {
        CommonResult result = new CommonResult<>();
        result.code = FAIL_CODE;
        result.errorCode = FAIL_CODE;
        result.message = exception.getMessage();
        return result;
    }


    public static CommonResult of(BusinessException businessException) {
        CommonResult result = new CommonResult<>();
        result.errorCode = businessException.getCode();
        result.message = businessException.getMessage();
        return result;
    }

    public static CommonResult of(ConstraintViolationException exception) {
        CommonResult result = new CommonResult<>();
        result.code = FAIL_CODE;
        // 异常消息可能存在多组
        StringBuilder stringBuilder = new StringBuilder(128);
        exception.getConstraintViolations().forEach(item -> stringBuilder.append(item.getMessage()).append("; "));
        result.message = stringBuilder.toString();
        return result;
    }

    public static CommonResult of(BindException exception) {
        CommonResult result = new CommonResult<>();
        result.code = FAIL_CODE;
        BindingResult bindingResult = exception.getBindingResult();
        StringBuilder stringBuilder = new StringBuilder(128);
        if (bindingResult.hasErrors()) {
            bindingResult.getFieldErrors().forEach(error -> stringBuilder.append(error.getDefaultMessage()).append("; "));
        }
        result.message = stringBuilder.toString();
        return result;
    }

    public static CommonResult of(MethodArgumentNotValidException exception) {
        CommonResult result = new CommonResult<>();
        result.code = FAIL_CODE;
        BindingResult bindingResult = exception.getBindingResult();
        StringBuilder stringBuilder = new StringBuilder(128);
        if (bindingResult.hasErrors()) {
            bindingResult.getFieldErrors().forEach(error -> stringBuilder.append(error.getDefaultMessage()).append("; "));
        }
        result.message = stringBuilder.toString();
        return result;
    }

    public void setFail() {
        this.setCode("999999");
        this.setMessage("系统繁忙");
    }

}
