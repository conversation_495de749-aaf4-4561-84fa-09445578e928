package com.cfpamf.ms.insur.operation.fegin.customer.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

import java.util.List;

/**
 * 客户详细信息
 * <AUTHOR> on 2018/3/8.
 */
@Data
@ApiModel
public class CustDetailVo {

    /**客户id*/
    @ApiModelProperty(value = "客户id")
    @NotBlank(message = "客户id不能为空")
    private String custId;

    /**定位地址*/
    @ApiModelProperty(value = "定位地址")
    private String address;

    /**所在省*/
    @ApiModelProperty(value = "所在省")
    private String province;

    /**所在省*/
    @ApiModelProperty(value = "所在省")
    private String provinceName;

    /**所在市*/
    @ApiModelProperty(value = "所在市")
    private String city;

    /**所在市*/
    @ApiModelProperty(value = "所在市")
    private String cityName;

    /**所在区/县*/
    @ApiModelProperty(value = "所在区/县")
    private String county;

    /**所在区/县*/
    @ApiModelProperty(value = "所在区/县")
    private String countyName;

    /**所在乡镇*/
    @ApiModelProperty(value = "所在乡镇")
    private String town;

    /**所在乡镇*/
    @ApiModelProperty(value = "所在乡镇")
    private String townName;

    /**所在村*/
    @ApiModelProperty(value = "所在村")
    private String village;

    /**所在村名称*/
    @ApiModelProperty(value = "所在村名称")
    private String villageName;

    /**还款日*/
    @ApiModelProperty(value = "还款日")
    private String repayDay;

    /**所属行业*/
    @ApiModelProperty(value = "所属行业")
    private String industry;

    /**所属行业*/
    @ApiModelProperty(value = "行业细分")
    private String[] subIndustry;

    /**职业*/
    @ApiModelProperty(value = "职业")
    private String occupation;

    /**学历*/
    @ApiModelProperty(value = "学历")
    private String education;

    /**学位*/
    @ApiModelProperty(value = "学位")
    private String degree;

    /**婚姻状况*/
    @ApiModelProperty(value = "婚姻状况")
    private String marriage;

    /**月收入*/
    @ApiModelProperty(value = "月收入")
    private String monthIncome;

    /**工作电话*/
    @ApiModelProperty(value = "工作电话")
    private String workingPhone;

    /**工作地址*/
    @ApiModelProperty(value = "工作地址")
    private String workingAddress;

    /**供养人数*/
    @ApiModelProperty(value = "供养人数")
    private Integer supportNum;

    /**户籍地址*/
    @ApiModelProperty(value = "户籍地址")
    private String registerAddress;

    /**居住地址*/
    @ApiModelProperty(value = "居住地址")
    private String liveAddress;

    /**身份证地址*/
    @ApiModelProperty(value = "身份证地址")
    private String idNoAddress;

    /**身份证颁证机关*/
    @ApiModelProperty(value = "身份证颁证机关")
    private String idNoAward;

    /**身份证有效期(起)*/
    @ApiModelProperty(value = "身份证有效期(起)")
    private String idNoStartDate;

    /**身份证有效期(止)*/
    @ApiModelProperty(value = "身份证有效期(止)*")
    private String idNoEndDate;

    /**所属分支编号*/
    @ApiModelProperty(value = "所属分支编号")
    private String loanBranch;

    /**所属分支名称*/
    @ApiModelProperty(value = "所属分支名称")
    private String loanBranchName;

    /**管护信贷员编号*/
    @ApiModelProperty(value = "管护信贷员编号")
    private String loanManager;

    /**管护信贷员姓名*/
    @ApiModelProperty(value = "管护信贷员姓名")
    private String loanManagerName;

    /**推荐信贷员编号*/
    @ApiModelProperty(value = "推荐信贷员编号")
    private String recommended;

    /**推荐信贷员姓名*/
    @ApiModelProperty(value = "推荐信贷员姓名")
    private String recommendedName;

    /**推荐信贷员所在分支*/
    @ApiModelProperty(value = "推荐信贷员所在分支")
    private String recommendedBch;

    /**推荐信贷员所在分支名称*/
    @ApiModelProperty(value = "推荐信贷员所在分支名称")
    private String recommendedBchName;

    /**信贷客户id*/
    @ApiModelProperty(value = "信贷客户id")
    private String loanCustId;

    /**民族*/
    @ApiModelProperty(value = "民族")
    private String ethnicGroup;

    /**家庭人数*/
    @ApiModelProperty(value = "家庭人数")
    private Integer familyNum;

    /**家庭电话*/
    @ApiModelProperty(value = "家庭电话")
    private String familyTel;

    /**家庭电话区号*/
    @ApiModelProperty(value = "家庭电话区号")
    private String familyTelZone;

    /**劳动力数*/
    @ApiModelProperty(value = "劳动力数")
    private Integer labourNum;

    /**性别*/
    @ApiModelProperty(value = "性别")
    private String sex;

    /**出生年月日*/
    @ApiModelProperty(value = "出生年月日")
    private String birthDay;

    /**居民类型  01-农户 02-非农户*/
    @ApiModelProperty(value = "居民类型  01-农户 02-非农户")
    private String residentType;

    /**所属中心*/
    @ApiModelProperty(value = "所属中心")
    private String center;

    /**
     * 贫困性质  01-贫困户  02-非贫困户
     * @see NaturePovertyEnum#getStsCode()
     */
    @ApiModelProperty(value = "贫困性质")
    private String naturePoverty;

    /**户别类型*/
    @ApiModelProperty(value = "户别类型")
    private String houseHold;

    /**有无房产*/
    @ApiModelProperty(value = "有无房产")
    private Boolean hasHouse;

    /**房产证编号*/
    @ApiModelProperty(value = "房产证编号")
    private String houseCert;

    /**本地居住时间(月份)*/
    @ApiModelProperty(value = "本地居住时间(月份)")
    private Long residentPeriod;

    /**是否本地居住 Y:是 N:否*/
    @ApiModelProperty(value = "是否本地居住 Y:是 N:否")
    private String isLocal;

    /**本地居住起始时间*/
    @ApiModelProperty(value = "本地居住起始时间")
    private String localResidentStart;

    /**居住状况*/
    @ApiModelProperty(value = "居住状况")
    private String residentStatus;

    /**邮箱*/
    @ApiModelProperty(value = "邮箱")
    private String email;

    /**QQ*/
    @ApiModelProperty(value = "QQ")
    private String qq;

    /**收入来源*/
    @ApiModelProperty(value = "收入来源")
    private List<String> incomeSource;

    /**新老客户*/
    @ApiModelProperty(value = "新老客户")
    private String customerStatus;

    /**是否重点关注*/
    @ApiModelProperty("是否重点关注")
    private Boolean isFocus;

    /**失联状态*/
    @ApiModelProperty("失联状态")
    private String contactSts;

    @ApiModelProperty(value = "微信好友数")
    private Integer wechatFriendNum;

    @ApiModelProperty(value = "子女人数")
    private Integer childNum;
}
