package com.cfpamf.ms.insur.operation.aicall.vo.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class Ai94ImportTaskCustomer {
    @ApiModelProperty(value = "请求ID，具有唯一性，用来做请求幂等处理，一个请求id有效期10分钟。不传则不做幂等处理", required = false)
    private String requestId;

    @ApiModelProperty(value = "任务ID，决策系统中的任务编号", required = true)
    private Integer taskId;

    @ApiModelProperty(value = "外呼客户信息列表", required = true)
    private List<Ai94Customer> customers;

    @ApiModelProperty(value = "失败返回类型，可选值：0，1，默认1。如导入的数据中有部分验证不通过数据：0表示导入验证成功的，返回失败的数据；1表示全部数据（包括验证成功及失败的）都不导入", required = false)
    private Integer failReturn;
}
