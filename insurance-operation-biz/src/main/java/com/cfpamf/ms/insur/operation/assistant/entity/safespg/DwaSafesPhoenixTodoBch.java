package com.cfpamf.ms.insur.operation.assistant.entity.safespg;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@ApiModel(value = "保险助手分支待办集市")
@Data
public class DwaSafesPhoenixTodoBch implements Serializable,Cloneable {
    /**  */
    @ApiModelProperty(name = "",notes = "")
    private String bchName ;
    /**  */
    @ApiModelProperty(name = "",notes = "")
    private String bchCode ;
    /**  */
    @ApiModelProperty(name = "",notes = "")
    private String districtName ;
    /**  */
    @ApiModelProperty(name = "",notes = "")
    private String districtCode ;
    /**  */
    @ApiModelProperty(name = "",notes = "")
    private String areaCode ;
    /**  */
    @ApiModelProperty(name = "",notes = "")
    private String areaName ;
    /**  */
    @ApiModelProperty(name = "",notes = "")
    private Integer syInterruptionTodoCnt ;
    /**  */
    @ApiModelProperty(name = "",notes = "")
    private Integer syRenewShortTodoCnt ;
    /**  */
    @ApiModelProperty(name = "",notes = "")
    private Double syInterruptionTodoFollowRate ;
    /**  */
    @ApiModelProperty(name = "",notes = "")
    private Double syRenewShortTodoFollowRate ;
    /**  */
    @ApiModelProperty(name = "",notes = "")
    private Double syInterruptionTodoConversionRate ;
    /**  */
    @ApiModelProperty(name = "",notes = "")
    private Double syRenewShortTodoConversionRate ;
    /**  */
    @ApiModelProperty(name = "",notes = "")
    private Double syInterruptionTodoConversionAmt ;
    /**  */
    @ApiModelProperty(name = "",notes = "")
    private Double syRenewShortTodoConversionAmt ;
    /**  */
    @ApiModelProperty(name = "",notes = "")
    private String pt ;
    /** 当年断保待办跟进数 */
    @ApiModelProperty(name = "当年断保待办跟进数",notes = "")
    private Integer syInterruptionTodoFollowCnt ;
    /** 当年断保待办激活数 */
    @ApiModelProperty(name = "当年断保待办激活数",notes = "")
    private Integer syInterruptionTdoFollowCust ;
    /** 当年续保待办跟进数 */
    @ApiModelProperty(name = "当年续保待办跟进数",notes = "")
    private Integer syTdoShortFollowCnt ;
    /** 当年续保待办激活数 */
    @ApiModelProperty(name = "当年续保待办激活数",notes = "")
    private Integer syTdoShortFollowPolicy ;
    /** 当月生成非主营的续保待办保单保费 */
    @ApiModelProperty(name = "当月生成非主营的续保待办保单保费",notes = "")
    private Double smUnloanRenewShortTodoInsuranceAmt ;
    /** 当月生成非主营的续保待办保单数 */
    @ApiModelProperty(name = "当月生成非主营的续保待办保单数",notes = "")
    private Integer smUnloanRenewShortTodoInsuranceCnt ;
    /** 当月生成主营的续保待办保单保费 */
    @ApiModelProperty(name = "当月生成主营的续保待办保单保费",notes = "")
    private Double smLoanRenewShortTodoInsuranceAmt ;
    /** 当月生成主营的续保待办保单数 */
    @ApiModelProperty(name = "当月生成主营的续保待办保单数",notes = "")
    private Integer smLoanRenewShortTodoInsuranceCnt ;
    /** 截止当前非贷续保待办保单金额 */
    @ApiModelProperty(name = "截止当前非贷续保待办保单金额",notes = "")
    private Double unloanRenewShortTodoInsuranceAmt ;
    /** 截止当前非贷续保待办保单数 */
    @ApiModelProperty(name = "截止当前非贷续保待办保单数",notes = "")
    private Integer unloanRenewShortTodoInsuranceCnt ;
    /** 截止当前信贷续保待办保单金额 */
    @ApiModelProperty(name = "截止当前信贷续保待办保单金额",notes = "")
    private Double loanRenewShortTodoInsuranceAmt ;
    /** 截止当前信贷续保待办保单数 */
    @ApiModelProperty(name = "截止当前信贷续保待办保单数",notes = "")
    private Integer loanRenewShortTodoInsuranceCnt ;
    /** 截止当前未完成的非主营续保待办数 */
    @ApiModelProperty(name = "截止当前未完成的非主营续保待办数",notes = "")
    private Integer unloanRenewShortTodoCnt ;
    /** 截止当前未完成的主营续保待办数 */
    @ApiModelProperty(name = "截止当前未完成的主营续保待办数",notes = "")
    private Integer loanRenewShortTodoCnt ;
    /** 截止当前未完成的非主营续保保费 */
    @ApiModelProperty(name = "截止当前未完成的非主营续保保费",notes = "")
    private Double unloanRenewShortTodoAmt ;
    /** 截止当前未完成的主营续保待办保费 */
    @ApiModelProperty(name = "截止当前未完成的主营续保待办保费",notes = "")
    private Double loanRenewShortTodoAmt ;

}
