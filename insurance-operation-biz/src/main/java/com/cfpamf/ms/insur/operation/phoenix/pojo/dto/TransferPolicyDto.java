package com.cfpamf.ms.insur.operation.phoenix.pojo.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 已续保订单视图对象
 *
 * <AUTHOR>
 * @date 2021/5/13 16:13
 */
@Data
public class TransferPolicyDto {

    @ApiModelProperty("产品名称")
    private String productName;

    @ApiModelProperty("保单号")
    private String policyNo;

    @ApiModelProperty("订单号")
    private String fhOrderId;

    @ApiModelProperty("产品id")
    private Integer productId;

    @ApiModelProperty("计划id")
    private Integer planId;

    @ApiModelProperty("被保人身份证")
    private String idNumber;
    @ApiModelProperty("交单时间（即支付时间）")
    private Date paymentTime;
    @ApiModelProperty("生效时间")
    private Date startTime;
    private BigDecimal totalAmount;
    private String submitTime;
}
