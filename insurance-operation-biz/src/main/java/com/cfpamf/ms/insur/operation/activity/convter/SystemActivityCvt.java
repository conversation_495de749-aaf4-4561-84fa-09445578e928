package com.cfpamf.ms.insur.operation.activity.convter;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.cfpamf.ms.insur.operation.activity.entity.SystemActivityConstRule;
import com.cfpamf.ms.insur.operation.activity.entity.SystemActivityConstRuleWhale;
import com.cfpamf.ms.insur.operation.activity.form.SystemActivityConstRuleForm;
import com.cfpamf.ms.insur.operation.activity.form.SystemActivityConstRuleParamForm;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> 2022/6/23 10:42
 */
@Mapper(imports = {JSONObject.class, JSONArray.class, SystemActivityConstRuleParamForm.class})
public interface SystemActivityCvt {

    SystemActivityCvt INS = Mappers.getMapper(SystemActivityCvt.class);

    /**
     * 表单对象转换成po
     *
     * @param s
     * @return
     */
    @Mappings({
            @Mapping(target = "planOrRiskIds", expression = "java(s.getPlanOrRiskIds().toString())"),
            @Mapping(target = "productIds", expression = "java(s.getProductIds().toString())"),
            @Mapping(target = "params", expression = "java(JSONObject.toJSONString(s.getParams()))")
    })
    SystemActivityConstRule from2Po(SystemActivityConstRuleForm s);

    /**
     * 列表转化
     *
     * @param fs
     * @return
     */
    default List<SystemActivityConstRule> from2Po(List<SystemActivityConstRuleForm> fs) {
        return fs.stream().map(this::from2Po).collect(Collectors.toList());
    }

    /**
     * po对象转换成Form
     *
     * @param s
     * @return
     */
    @Mappings({
            @Mapping(target = "planOrRiskIds", expression = "java(JSONArray.parseArray(s.getPlanOrRiskIds(), Integer.class))"),
            @Mapping(target = "productIds", expression = "java(JSONArray.parseArray(s.getProductIds(), Integer.class))"),
            @Mapping(target = "params", expression = "java( JSONObject.parseObject(s.getParams(), SystemActivityConstRuleParamForm.class))")
    })
    SystemActivityConstRuleForm po2Form(SystemActivityConstRule s);

    default List<SystemActivityConstRuleForm> po2Form(List<SystemActivityConstRule> ts) {
        return ts.stream().map(this::po2Form).collect(Collectors.toList());
    }

    /**
     * 表单对象转换成po
     *
     * @param s
     * @return
     */
    @Mappings({
            @Mapping(target = "riskIds", expression = "java(s.getPlanOrRiskIds().toString())"),
            @Mapping(target = "riskNames", expression = "java(JSONObject.toJSONString(s.getRiskNames()))"),
            @Mapping(target = "sellProductIds", expression = "java(s.getProductIds().toString())"),
            @Mapping(target = "sellProductNames", expression = "java(JSONObject.toJSONString(s.getSellProductNames()))"),
            @Mapping(target = "params", expression = "java(JSONObject.toJSONString(s.getParams()))")
    })
    SystemActivityConstRuleWhale from2WhalePo(SystemActivityConstRuleForm s);

    /**
     * 列表转化
     *
     * @param fs
     * @return
     */
    default List<SystemActivityConstRuleWhale> from2WhalePo(List<SystemActivityConstRuleForm> fs) {
        return fs.stream().map(this::from2WhalePo).collect(Collectors.toList());
    }

    /**
     * po对象转换成Form
     *
     * @param s
     * @return
     */
    @Mappings({
            @Mapping(target = "planOrRiskIds", expression = "java(JSONArray.parseArray(s.getRiskIds(), Integer.class))"),
            @Mapping(target = "riskNames", expression = "java(JSONArray.parseArray(s.getRiskNames(), String.class))"),
            @Mapping(target = "productIds", expression = "java(JSONArray.parseArray(s.getSellProductIds(), Integer.class))"),
            @Mapping(target = "sellProductNames", expression = "java(JSONArray.parseArray(s.getSellProductNames(), String.class))"),
            @Mapping(target = "params", expression = "java( JSONObject.parseObject(s.getParams(), SystemActivityConstRuleParamForm.class))")
    })
    SystemActivityConstRuleForm po2WhaleForm(SystemActivityConstRuleWhale s);

    default List<SystemActivityConstRuleForm> po2WhaleForm(List<SystemActivityConstRuleWhale> ts) {
        return ts.stream().map(this::po2WhaleForm).collect(Collectors.toList());
    }
}
