package com.cfpamf.ms.insur.operation.customer.dao;

import com.cfpamf.ms.insur.operation.customer.dto.CustomerFollowDto;
import com.cfpamf.ms.insur.operation.customer.dto.CustomerFollowCntDto;
import com.cfpamf.ms.insur.operation.customer.dto.CustomerPolicyDto;
import com.cfpamf.ms.insur.operation.customer.dto.WxCmsSmyDto;
import com.cfpamf.ms.insur.operation.customer.dto.WxInterruptionCustomerDto;
import com.cfpamf.ms.insur.operation.customer.query.CustomerFollowCntQuery;
import com.cfpamf.ms.insur.operation.customer.query.CustomerPolicyQuery;
import com.cfpamf.ms.insur.operation.customer.query.WxInterruptionCustomerQuery;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface CustomerMapper{

    /**
     * 查询断保客户列表
     * @param query
     * @return
     */
    List<WxInterruptionCustomerDto> listBreakCustomer(WxInterruptionCustomerQuery query);
    /**
     * 查询后台断保客户列表
     * @param query
     * @return
     */
    List<WxInterruptionCustomerDto> listBackBreakCustomer(WxInterruptionCustomerQuery query);

    /**
     * 查询客户所有的单子
     *
     * @param query
     * @return
     */
    List<CustomerPolicyDto> listAppPolicyListAll(CustomerPolicyQuery query);

    /**
     * 查询客户所有的单子
     *
     * @param query
     * @return
     */
    WxCmsSmyDto listAppPolicyCount(CustomerPolicyQuery query);

    /**
     * 查询微信用户断保客户数
     * @param userId
     * @param userType 用户类型 1：已断保，2：已激活
     * @return
     */
    Integer getInterruptionQty(@Param("userId") String userId,@Param("userType") Integer userType);

    /**
     * 根据客户id获取证件号
     * @param customerId
     * @return
     */
    String getIdNumberByCustomerId(@Param("customerId") String customerId);

    /**
     * 根据客户id获取证件号
     * @param customerId
     * @return
     */
    String getLoanIdNumberByCustomerId(@Param("customerId") Long customerId);

    List<CustomerFollowDto> selectInterruptionFollowByCustomer(@Param("customerId") String customerId);
    List<CustomerFollowDto> selectAiFollowByCustomer(@Param("customerId") String customerId);
    List<CustomerFollowDto> selectLoanFollowByCustomer(@Param("customerId") String customerId);
    List<CustomerFollowDto> selectLoanFirstFollowByCustomer(@Param("customerId") String customerId);

    List<WxInterruptionCustomerDto> listCustomerByIdNumberList(@Param("customerIdNumberList") List<String> customerIdNumberList);

    List<CustomerFollowCntDto> listInterruptionFollowCntByJobNumbers(@Param("query") CustomerFollowCntQuery query);
    List<CustomerFollowCntDto> listInterruptionConversionByJobNumbers(@Param("query") CustomerFollowCntQuery query);
    List<CustomerFollowCntDto> listRenewShortFollowCntByJobNumbers(@Param("query") CustomerFollowCntQuery query);
    List<CustomerFollowCntDto> listRenewShortConversionByJobNumbers(@Param("query") CustomerFollowCntQuery query);
    List<CustomerFollowCntDto> listRenewLongFollowCntByJobNumbers(@Param("query") CustomerFollowCntQuery query);
    List<CustomerFollowCntDto> listRenewLongConversionByJobNumbers(@Param("query") CustomerFollowCntQuery query);
}
