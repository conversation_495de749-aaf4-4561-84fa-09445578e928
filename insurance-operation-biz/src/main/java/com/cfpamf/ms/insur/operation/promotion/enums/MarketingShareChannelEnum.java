package com.cfpamf.ms.insur.operation.promotion.enums;

import com.cfpamf.common.ms.exception.MSBizNormalException;
import com.cfpamf.ms.insur.operation.base.exception.BusinessException;
import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;

@Getter
public enum MarketingShareChannelEnum {
    INSURE_HELP("insureHelp","保险助手")
    ;

    /**
     * 分享渠道编码
     */
    String shareChannelCode;
    /**
     * 分享渠道名称
     */
    String shareChannelName;

    MarketingShareChannelEnum(String shareChannelCode, String shareChannelName) {
        this.shareChannelCode = shareChannelCode;
        this.shareChannelName = shareChannelName;
    }

    public static MarketingShareChannelEnum decode(String shareChannelCode) {
        return Arrays.stream(MarketingShareChannelEnum.values())
                .filter(x -> Objects.equals(x.getShareChannelCode(), shareChannelCode))
                .findFirst().orElseThrow(() -> new MSBizNormalException("", "分享渠道不匹配"));
    }
}
