package com.cfpamf.ms.insur.operation.fegin.customer.response;

import com.cfpamf.ms.insur.operation.fegin.customer.enums.CredentialsStsEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 客户基本信息
 * <AUTHOR> on 2018/3/8.
 */
@Data
@ApiModel
public class CustBaseInfoVo implements Serializable {

    /**客户id*/
    @ApiModelProperty(value = "客户id")
    private Long id;

    /**客户编号*/
    @ApiModelProperty(value = "客户编号")
    private String custNo;

    /**客户姓名*/
    @ApiModelProperty(value = "客户姓名")
    private String custName;

    /**证件类型 01-身份证*/
    @ApiModelProperty(value = "证件类型 01-身份证")
    private String idType;

    /**证件号码*/
    @ApiModelProperty(value = "证件号码")
    private String idNo;

    /**手机号*/
    @ApiModelProperty(value = "手机号")
    private String mobile;

    /**密码*/
    @ApiModelProperty(value = "密码")
    private String password;

    /**是否实名认证 01-已实名认证  02-未实名认证*/
    @ApiModelProperty(value = "是否实名认证 01-已实名认证  02-未实名认证")
    private String isReal;

    /**状态 01-有效  02-无效*/
    @ApiModelProperty(value = "状态 01-有效  02-无效")
    private String sts;

    /**证件状态*/
    @ApiModelProperty(value = "证件状态 PENDING-待审核  PASS-审核通过  REJECT-审核不通过  DOUBT-可疑")
    private CredentialsStsEnum credentialsSts;

    @ApiModelProperty(value = "创建时间")
    private String createTime;

    @ApiModelProperty(value = "更新时间")
    private String updateTime;

    /**客户详细信息*/
    @ApiModelProperty(value = "客户详细信息")
    private CustDetailVo custDetail;

}
