package com.cfpamf.ms.insur.operation.base.constant;

import io.swagger.models.auth.In;

import java.time.format.DateTimeFormatter;

/**
 * <AUTHOR>
 */
public class BaseConstants {

    /**
     * 保险整合BMS api不需要前缀
     * 后端管理接口前缀
     */
    public final static String ADMIN_VERSION = "/back";


    /**
     * 企业微信
     */
    public final static String QY_VERSION = "/qy";


    /**
     * 放权接口
     */
    public final static String PUB_VERSION = "/pub";
    /**
     * 微信接口前缀
     */
    public final static String WX_VERSION = "/wx";

    /**
     * 小额保险按区域统计报表文件名
     */
    public final static String SM_TEMPLATE_FILE_PATH = "template";
    /**
     * authorization
     */
    public final static String API_AUTH_NAME = "authorization";

    /**
     * authorization
     */
    public final static String WX_HTTP_HEAD_AUTH = "authorization";

    /**
     * openId
     */
    public final static String WX_HTTP_HEAD_OPENID = "openId";

    /**
     * 根机构hrOrgId
     */
    public final static int BMS_ROOT_ROOT_HR_ORG_ID = 900105153;
    /**
     * 总部职能部门根path
     */
    public final static String BMS_ROOT_HEAD_HR_ORG_ID_PATH = "900105153/282717";

    /**
     * 总部职能部门根节点文本
     */
    public final static String BMS_ROOT_HEAD_HR_ORG_TEXT = "总部";


    /**
     * 用户种类-微信
     */
    public final static String USER_TYPE_WEIXIN = "weixin";
    /**
     * 用户种类-员工
     */
    public final static String USER_TYPE_EMPLOYEE = "employee";
    /**
     * 用户种类-代理人
     */
    public final static String USER_TYPE_AGENT = "agent";

    public final static String REPORT_DEFAULT_START_DATE = "2021-01-01";


    public final static String CACHE_KEY_SPLIT = ":";
    public final static String ERROR_CODE_PREFIX = "154";

    public final static String ERROR_CODE_THIRD_API_PREFIX = ERROR_CODE_PREFIX + "001";

    public final static String ERROR_CODE_DB_PREFIX = ERROR_CODE_PREFIX + "008";
    public final static String ERROR_CODE_MSG_PREFIX = ERROR_CODE_PREFIX + "002";
    public final static String ERROR_CODE_PARAM_PREFIX = ERROR_CODE_PREFIX + "400";

    public final static DateTimeFormatter FMT_DATE = DateTimeFormatter.ofPattern("yyyyMMdd");

    /**
     * 保单状态-无
     */
    public final static String POLICY_STATUS_BLANK = "-2";
    /**
     * 保单状态-失效
     */
    public final static String POLICY_STATUS_INVALID = "-1";
    /**
     * 保单状态-承保失败
     */
    public final static String POLICY_STATUS_FAIL = "0";
    /**
     * 保单状态-承保成功
     */
    public final static String POLICY_STATUS_SUCCESS = "1";
    /**
     * 保单状态-处理中
     */
    public final static String POLICY_STATUS_PROCESS = "2";
    /**
     * 保单状态-退保失败
     */
    public final static String POLICY_STATUS_CANCEL_FAIL = "3";
    /**
     * 保单状态-退保成功
     */
    public final static String POLICY_STATUS_CANCEL_SUCCESS = "4";
    /**
     * 保单状态-部分承保失败
     */
    public final static String POLICY_STATUS_PART_FAIL = "5";

    public final static String ORDER_STATUS_PAYED = "2";

    /**
     * 订单状态-已智能核保（完成了问卷调查）
     */
    public final static String ORDER_STATUS_CHECKED = "-10";

    /**
     * 订单状态-进行中（待核保）
     */
    public final static String ORDER_STATUS_TO_ORDER = "-1";
    /**
     * 客户激活状态--已断保
     */
    public final static String CUSTOMER_UN_RENEW = "UN_RENEW";
    /**
     * 客户激活状态--已激活
     */
    public final static String CUSTOMER_RENEW = "RENEW";
    /**
     * 客户转化状态--已转化
     */
    public final static Integer CUSTOMER_CONVERSION = 1;
    /**
     * 客户转化状态--未转化conversion_state
     */
    public final static Integer CUSTOMER_UN_CONVERSION = 0;
    /**
     * 订单类型----普通订单
     */
    public final static String ORDER_TYPE_0 = "0";

    // 第三方回调
    public static final String OPEN_API = "thirdCallback/public/";

    /**
     * 后端管理接口前缀(旧)
     */
    public static final String API_VERSION_1 = "/api/v1";

    /**
     * 钉钉第三方回调
     */
    public static final String OPEN_API_DING_TALK = "dingTalk";

    public static final String UTF_8_CODE = "UTF-8";
}
