package com.cfpamf.ms.insur.operation.call.entity;

import com.cfpamf.ms.insur.operation.base.po.BasePO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import javax.persistence.Table;
import java.time.LocalDateTime;

/**
 * 呼叫记录表
 *
 * <AUTHOR>
 * @date 2025-07-22
 */
@Data
@Table(name = "call_record")
@FieldDefaults(level = AccessLevel.PRIVATE)
public class CallRecordPo extends BasePO {

    @ApiModelProperty("保单号")
    String policyNo;

    @ApiModelProperty("客户手机号")
    String customerPhone;

    @ApiModelProperty("客户经理工号")
    String employeeId;

    @ApiModelProperty("客户经理姓名")
    String employeeName;

    @ApiModelProperty("客户经理手机号")
    String employeePhone;

    @ApiModelProperty("客户编号")
    String clientId;

    @ApiModelProperty("客户名称")
    String clientName;

    @ApiModelProperty("呼叫SID")
    String callSid;

    @ApiModelProperty("呼叫状态：0-初始化，1-呼叫中，2-呼叫成功，3-呼叫失败")
    Integer callStatus;

    @ApiModelProperty("呼叫开始时间")
    LocalDateTime beginCallTime;

    @ApiModelProperty("呼叫结束时间")
    LocalDateTime endCallTime;

    @ApiModelProperty("通话时长（秒）")
    Integer duration;

    @ApiModelProperty("录音地址")
    String recordUrl;

    @ApiModelProperty("录音时长")
    String recordDuration;

    @ApiModelProperty("录音获取状态：0-未获取，1-已获取，2-获取失败")
    Integer recordStatus;

    @ApiModelProperty("录音获取时间")
    LocalDateTime recordTime;

    @ApiModelProperty("跟进记录匹配状态：0-未匹配，1-已匹配")
    Integer followMatchStatus;

    @ApiModelProperty("匹配的跟进记录ID")
    Long followRecordId;

    @ApiModelProperty("备注")
    String remark;

    @ApiModelProperty("呼叫失败原因")
    String failReason;
}
