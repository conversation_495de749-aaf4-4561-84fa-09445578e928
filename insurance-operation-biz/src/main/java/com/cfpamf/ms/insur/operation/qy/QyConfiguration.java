package com.cfpamf.ms.insur.operation.qy;

import me.chanjar.weixin.cp.api.WxCpService;
import me.chanjar.weixin.cp.api.impl.WxCpServiceImpl;
import me.chanjar.weixin.cp.config.WxCpConfigStorage;
import me.chanjar.weixin.cp.config.impl.WxCpRedissonConfigImpl;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

/**
 * <AUTHOR> 2022/8/4 10:25
 */
@Configuration
@EnableConfigurationProperties(QyProperties.class)
public class QyConfiguration {


    @Bean
    public WxCpConfigStorage whaleWxCpConfig(QyProperties properties, RedissonClient redissonClient) {
        WxCpRedissonConfigImpl res = new WxCpRedissonConfigImpl(redissonClient);
        // 将配置得值赋值
        res.setCorpId(properties.getCorpId());
        res.setAgentId(properties.getAgentId());
        res.setCorpSecret(properties.getCorpSecret());
        return res;
    }

    @Primary
    @Bean
    public WxCpService whaleWxCpService(@Qualifier("whaleWxCpConfig") WxCpConfigStorage wxCpConfigStorage) {
        WxCpServiceImpl wxCpService = new WxCpServiceImpl();
        wxCpService.setWxCpConfigStorage(wxCpConfigStorage);
        return wxCpService;
    }
}
