package com.cfpamf.ms.insur.operation.customer.po;

import com.cfpamf.ms.insur.operation.base.po.BasePO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import javax.persistence.Table;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * 异业客户保单记录表
 */
@Data
@Table(name = "customer_loan_policy")
@FieldDefaults(level = AccessLevel.PRIVATE)
public class CustomerLoanPolicyPo extends BasePO {

    @ApiModelProperty(name="证件号码")
    String idNumber;

    @ApiModelProperty(name="客户名称")
    String customerName;

    @ApiModelProperty(name="保单号")
    String policyNo;

    @ApiModelProperty(name="订单号")
    String fhOrderId;

    @ApiModelProperty(name="保单状态")
    String policyState;
    @ApiModelProperty(name="是否退保")
    Integer cancelFlag;
    @ApiModelProperty(name="退保时间")
    LocalDateTime cancelTime;

    @ApiModelProperty(name="订单总保费")
    BigDecimal totalAmount;
    @ApiModelProperty(name="保费")
    BigDecimal amount;

    @ApiModelProperty(name="管控人")
    String customerAdmin;


    @ApiModelProperty(name="是否被保人 0:是 1:否")
    Integer insur;

    @ApiModelProperty(name="转化员工")
    String convertEmp;

    @ApiModelProperty(value = "转化时间")
    LocalDateTime convertTime;
}
