package com.cfpamf.ms.insur.operation.phoenix.pojo.po;

import com.cfpamf.ms.insur.operation.base.entity.BaseNoUserEntity;
import com.cfpamf.ms.insur.operation.phoenix.enums.EnumPhoenixWxMsgWxState;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import javax.persistence.Column;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;

/**
 * <AUTHOR> 2022/12/12 14:46
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class PhoenixWxMsg extends BaseNoUserEntity {

    String jobNumber;

    String openId;

    String templateId;

    @Enumerated(EnumType.STRING)
    @Column(name = "state")
    EnumPhoenixWxMsgWxState state;
}
