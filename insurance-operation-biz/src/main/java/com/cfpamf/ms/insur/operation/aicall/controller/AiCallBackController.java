package com.cfpamf.ms.insur.operation.aicall.controller;

import com.alibaba.fastjson.JSONObject;
import com.cfpamf.ms.insur.operation.aicall.AiCallBackProvider;
import com.cfpamf.ms.insur.operation.aicall.vo.CallBackVo;
import com.cfpamf.ms.insur.operation.base.annotaions.ResponseDecorated;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.io.InputStream;
import java.util.Enumeration;

@Slf4j
@Api(tags = "Ai外呼回调")
@RestController
@RequestMapping("/AiCallBack")
public class AiCallBackController {


    @Autowired
    AiCallBackProvider aiCallBackProvider;

    @ApiOperation("外呼回调接口")
    @PostMapping("{provider}/callback")
    public CallBackVo aiCallBack(HttpServletRequest request, @PathVariable String provider) throws IOException {
        log.info(provider);
        String result = this.getRequestPostStr(request);
        log.info("call back param："+result);
        return this.aiCallBackProvider.processCallBack(result,provider);
    }

    private byte[] getRequestPostBytes(HttpServletRequest request)
            throws IOException {
        int contentLength = request.getContentLength();
        if(contentLength<0){
            return null;
        }
        byte buffer[] = new byte[contentLength];
        for (int i = 0; i < contentLength;) {

            int readlen = request.getInputStream().read(buffer, i,
                    contentLength - i);
            if (readlen == -1) {
                break;
            }
            i += readlen;
        }
        return buffer;
    }

    private String getRequestPostStr(HttpServletRequest request)
            throws IOException {
        byte buffer[] = getRequestPostBytes(request);
        String charEncoding = request.getCharacterEncoding();
        if (charEncoding == null) {
            charEncoding = "UTF-8";
        }
        String result = new String(buffer, charEncoding);
        if(StringUtils.isBlank(result.trim())){
            Enumeration<String> enu = request.getParameterNames();
            JSONObject paramsobj = new JSONObject();
            while (enu.hasMoreElements()) {
                log.info("enu.hasMoreElements(): "+enu.hasMoreElements());
                String paraName = (String) enu.nextElement();
                paramsobj.put(paraName, request.getParameter(paraName));
            }
            result = paramsobj.toJSONString();
        }
        return result;
    }

}
