package com.cfpamf.ms.insur.operation.honor.dto;

import org.springframework.util.StringUtils;
import com.cfpamf.ms.insur.operation.honor.enums.EnumHonorLevel;
import com.cfpamf.ms.insur.operation.honor.enums.EnumHonorPeriod;
import com.cfpamf.ms.insur.operation.honor.enums.EnumHonorSelectionType;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import javax.validation.constraints.NotBlank;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Objects;

/**
 * <AUTHOR>
 * 订单续保跟进记录表
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class HonorRulesConfigurationDto {
    @ApiModelProperty(name="主键，修改时传递")
    Integer id;

    @ApiModelProperty(name="年度")
    @NotBlank(message = "年度不能为空")
    String year;

    @ApiModelProperty("荣誉称号")
    @NotBlank(message = "荣誉称号不能为空")
    String honorName;

    @ApiModelProperty(name="评选对象 area:区域，bch：分支，emp：个人")
    @NotBlank(message = "评选对象不能为空")
    String level;

    @ApiModelProperty(name="评选对象名称")
    String levelName;

    public String getLevelName() {
        if (StringUtils.isEmpty(level)){
            return "";
        }
        return EnumHonorLevel.dict(level);
    }

    @ApiModelProperty(name="评选开始时间")
    LocalDate startTime;

    @ApiModelProperty(name="评选结束时间")
    LocalDate endTime;

    @ApiModelProperty(name="评选周期 year:年度，quarter：季度，month：月")
    @NotBlank(message = "评选周期不能为空")
    String period;

    @ApiModelProperty(name="评选周期名称")
    String periodName;

    public String getPeriodName() {
        if (StringUtils.isEmpty(period)){
            return "";
        }
        return EnumHonorPeriod.dict(period);
    }

    @ApiModelProperty(name="评选方式 people:人工，system：系统")
    String selectionType;

    @ApiModelProperty(name="评选方式名称")
    String selectionName;

    public String getSelectionName() {
        if (StringUtils.isEmpty(selectionType)){
            return "";
        }
        return EnumHonorSelectionType.dict(selectionType);
    }

    @ApiModelProperty(name="脚本规则code")
    String ruleCode;

    @ApiModelProperty(name="评选指标名称（按勾选顺序传递）")
    String honorNormName;

    @ApiModelProperty(name="评选指标编码（按勾选顺序传递）")
    String honorNormCode;

    @ApiModelProperty(name="评选数量")
    Integer selectionNumber;

    @ApiModelProperty(name="预评选数量")
    Integer preSelectionNumber;

    @ApiModelProperty(name="已获得图标")
    String obtainedIcon;

    @ApiModelProperty(name="未获得图标")
    String unObtainedIcon;

    @ApiModelProperty(name="评选规则")
    String honorRuleContent;

    @ApiModelProperty(name="状态 0:未开始，1：评选中，2：已结束")
    Integer state;

    @ApiModelProperty(name="荣誉清单文件名称")
    String fileName;

    @ApiModelProperty(name="荣誉清单文件地址")
    String fileUrl;

    @ApiModelProperty("最后操作人")
    String operationBy;

    @ApiModelProperty("最后操作时间")
    LocalDateTime operationTime;

    @ApiModelProperty("荣誉榜单类型 ranking：风云榜")
    String honorType = "ranking";
}
