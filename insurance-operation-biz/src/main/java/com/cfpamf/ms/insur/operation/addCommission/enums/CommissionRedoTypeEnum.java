package com.cfpamf.ms.insur.operation.addCommission.enums;

import com.cfpamf.ms.insur.operation.base.annotaions.Dictionary;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.experimental.FieldDefaults;

import java.util.Arrays;
import java.util.Objects;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 */
@Getter
@FieldDefaults(level = AccessLevel.PRIVATE)
@Dictionary(desc = "佣金刷数变更场景枚举")
public enum CommissionRedoTypeEnum {
    DELETE("delete","删除佣金"),
    DELETE_ENDORSEMENT_NO("deleteEndorsementNo","删除批单"),
    UPDATE("update","修改佣金（保单号等）"),
    UPDATE_COMMISSION_RATE("updateCommissionRate","修改佣金比例"),
    REDO_COMMISSION("redoCommission","重算短险佣金"),
    REDO_TERM_COMMISSION("redoTermCommission","重算续期佣金");
    ;
    String code;
    String desc;

    CommissionRedoTypeEnum(String code, String desc){
        this.code = code;
        this.desc = desc;
    }

    public boolean isMe(String code) {
        return Objects.equals(code, this.code);
    }

    public static String dict(String code) {
        return Stream.of(values()).filter(s -> s.code.equals(code))
                .findAny().map(CommissionRedoTypeEnum::getDesc).orElse("");
    }

    /**
     * 根据编码获取理保全项目枚举
     *
     * @param code 材料编码
     * @return CommissionRedoTypeEnum
     */
    public static CommissionRedoTypeEnum decode(String code) {
        return Arrays.stream(CommissionRedoTypeEnum.values())
                .filter(x -> x.code.equals(code))
                .findFirst()
                .orElse(null);
    }
}
