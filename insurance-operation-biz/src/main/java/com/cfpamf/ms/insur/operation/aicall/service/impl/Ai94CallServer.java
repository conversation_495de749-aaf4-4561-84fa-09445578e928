package com.cfpamf.ms.insur.operation.aicall.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.cfpamf.ms.insur.operation.aicall.AesUtils;
import com.cfpamf.ms.insur.operation.aicall.dao.*;
import com.cfpamf.ms.insur.operation.aicall.entity.*;
import com.cfpamf.ms.insur.operation.aicall.exceptions.EncryptException;
import com.cfpamf.ms.insur.operation.aicall.vo.AiCallTask;
import com.cfpamf.ms.insur.operation.aicall.vo.AiCallTaskDetail;
import com.cfpamf.ms.insur.operation.aicall.vo.request.*;
import com.cfpamf.ms.insur.operation.base.dao.CommonMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import tk.mybatis.mapper.entity.Example;

import java.text.SimpleDateFormat;
import java.util.*;

@Component("Ai94Call")
@Slf4j(topic = "BaiduCallServer")
public class Ai94CallServer extends AbstractCallServer<Ai94AddTask, Ai94ImportTaskCustomer>{
    @Autowired
    AiProviderConfigurationMapper aiProviderConfigurationMapper;

    @Autowired
    Ai94CallTaskMapper ai94CallTaskMapper;

    @Autowired
    Ai94CallTaskDetailMapper ai94calltaskdetailMapper;
    @Autowired
    CallTaskMapper callTaskMapper;
    @Autowired
    CallTaskDetailMapper callTaskDetailMapper;

    @Autowired
    private CommonMapper<Ai94CallTaskDetail> ai94CallTaskDetailMapper;

    @Autowired
    Ai94CallBackRecordMapper ai94CallBackRecordMapper;


    @Override
    public String getRecordFileUrl(String contactUuid) {
        Ai94CallBackRecord ai94CallBackRecord = ai94CallBackRecordMapper.selectByPrimaryKey(Long.valueOf(contactUuid));
        JSONObject returnResult= new JSONObject();
        if (ai94CallBackRecord!=null){
            returnResult.put("recordUrl",ai94CallBackRecord.getChatRecord());
            returnResult.put("downloadUrl",ai94CallBackRecord.getChatRecord());
            return returnResult.toJSONString();
        }
        return null;
    }

    @Override
    protected Ai94AddTask buildCallTask(AiCallTask aiCallTask, int i, RobortConfiguration robortConfiguration) {
        Ai94AddTask ai94AddTask =new Ai94AddTask();
        //设置 ai94AddTask所有属性
        ai94AddTask.setName(aiCallTask.getTaskName()+"_"+i);
        ai94AddTask.setCallTime(Arrays.asList(Arrays.asList(9.0f, 12.0f),Arrays.asList(14.0f, 20.0f)));
        ai94AddTask.setMaxConcurrency(1);
        ai94AddTask.setStartTime(aiCallTask.getDialStartTime());
        ai94AddTask.setTaskType(String.valueOf(1));
        //ai94AddTask.setTemplateId(Long.valueOf(robortConfiguration.getRobotId()));
        ai94AddTask.setTaskId(Long.valueOf(robortConfiguration.getRobotId()));
        return ai94AddTask;
}

    @Override
    protected String createTask(Ai94AddTask dialTask, String taskType) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("data",new JSONObject());
        jsonObject.getJSONObject("data").put("taskId",dialTask.getTaskId());
        String taskBatchCode = dialTask.getTaskId().toString();
//        String result = postObject(null,dialTask,"/v1/task/addTask");
//        String returnString = dialWithResponse(result);
//        if(StringUtils.isBlank(returnString)){ //如果在百度创建任务失败直接返回不创建本地任务记录。
//            return returnString;
//        }

        CallTask callTask =new CallTask();
        callTask.setTaskType(taskType);
        callTask.setTaskName(dialTask.getName());
        callTask.setAiProvider("Ai94Call");
        callTask.setCreatedTime(new Date());
        callTask.setUpdatedTime(new Date());
        callTask.setTaskBatchCode(taskBatchCode);
        callTaskMapper.insertOrUpdate(callTask);
        dialTask.setTaskId(callTask.getId());
        Ai94CallTask ai94CallTask = new Ai94CallTask();
        BeanUtils.copyProperties(dialTask,ai94CallTask);
        ai94CallTask.setTaskId(callTask.getId());
        ai94CallTask.setTaskType(taskType);
        ai94CallTask.setTaskBatchCode(taskBatchCode);
        ai94CallTask.setAi94TaskType(Integer.valueOf(dialTask.getTaskType()));//1:批量-AI外呼-不转人工，2:批量-语音通知，6：新语音通知（22年10月后入驻的企业的默认语音通知类型）
        this.ai94CallTaskMapper.insertOrUpdate(ai94CallTask);
        return jsonObject.toJSONString();
    }

    private String dialWithResponse(String result) {
        if(StringUtils.isBlank(result)){
            return null;
        }else{
            JSONObject jsonObject = JSONObject.parseObject(result);
            if(jsonObject.containsKey("code")){
                if(jsonObject.getInteger("code")==200){
                    return jsonObject.toJSONString();
                }else{
                    return null;
                }
            }else{
                return null;
            }
        }
    }

    @Override
    protected void updateLocalTask(Ai94AddTask dialTask, String taskBatchCode) {
//        CallTask callTask = new CallTask();
//        callTask.setId(Long.valueOf(dialTask.getTaskId()));
//        callTask.setTaskBatchCode(taskBatchCode);
//        this.callTaskMapper.updateByPrimaryKeySelective(callTask);
//        Ai94CallTask ai94CallTask = new Ai94CallTask();
//        ai94CallTask.setTaskBatchCode(taskBatchCode);
//        Example example = new Example(BaiduCallTask.class);
//        example.createCriteria().andEqualTo("taskId",Long.valueOf(dialTask.getTaskId()));
//        this.ai94CallTaskMapper.updateByExampleSelective(ai94CallTask,example);

    }

    @Override
    protected String importPhone(Ai94ImportTaskCustomer importPhone, Ai94AddTask dialTask) {
        importPhone.getCustomers().forEach(
                ai94Customer -> { saveTaskDetail(importPhone, dialTask, ai94Customer); }
        );
        String result = this.postObject(new HashMap<>(),importPhone,"/v1/task/importTaskCustomer");
        String returnString = dialWithResponse(result);
        return returnString;
    }

    private void saveTaskDetail(Ai94ImportTaskCustomer importPhone, Ai94AddTask dialTask, Ai94Customer ai94Customer) {
        CallTaskDetail callTaskDetail = new CallTaskDetail();
        callTaskDetail.setTaskId(dialTask.getTaskId());
        callTaskDetail.setMobile(ai94Customer.getNumber());
        callTaskDetail.setTaskBatchCode(importPhone.getTaskId().toString());
        callTaskDetail.setCreatedTime(new Date());
        callTaskDetail.setUpdatedTime(new Date());
        callTaskDetailMapper.insertOrUpdate(callTaskDetail);
        Ai94CallTaskDetail ai94CallTaskDetail = new Ai94CallTaskDetail();
        BeanUtils.copyProperties(ai94Customer,ai94CallTaskDetail);
        ai94CallTaskDetail.setTaskBatchCode(importPhone.getTaskId().toString());
        ai94CallTaskDetail.setTaskDetailId(callTaskDetail.getId());
        ai94CallTaskDetail.setTaskId(dialTask.getTaskId());
        ai94CallTaskDetail.setOutSourceId(Long.valueOf(ai94Customer.getExtJson()));
        this.ai94CallTaskDetailMapper.insertOrUpdate(ai94CallTaskDetail);
    }

    @Override
    protected Ai94ImportTaskCustomer buildImportPhone(List<List<AiCallTaskDetail>> partitions, int i, String taskBatchCode) {
        Ai94ImportTaskCustomer importPhone = new Ai94ImportTaskCustomer();
        importPhone.setTaskId(Integer.valueOf(taskBatchCode));
        importPhone.setRequestId(UUID.randomUUID().toString());
        List<Ai94Customer> customerInfos = new ArrayList<>();
        importPhone.setCustomers(customerInfos);
        importPhone.setFailReturn(0);//可选值：0，1。如导入的数据中有部分验证不通过数据：0表示导入验证成功的，返回失败的数据；1表示全部数据（包括验证成功及失败的）都不导入；
        partitions.get(i).forEach(item->{
            Ai94Customer customerInfo = new Ai94Customer();
            customerInfo.setTag(item.getOutSourceId().toString());
            customerInfo.setNumber(item.getMobile());
            customerInfo.setProperties(item.getVarMap());
            customerInfo.setExtJson(item.getOutSourceId().toString());
            customerInfos.add(customerInfo);
        });
        return importPhone;
    }

    @Override
    protected void updateLocalTaskDetail(Ai94AddTask dialTask, String importPhoneResult) {
        JSONObject jsonObject = JSONObject.parseObject(importPhoneResult);
        if(jsonObject.containsKey("code")){
            if(jsonObject.getInteger("code")==200){
                String batchId = jsonObject.getString("batchId");
                Example example = new Example(Ai94CallTaskDetail.class);
                example.createCriteria().andEqualTo("taskId",Long.valueOf(dialTask.getTaskId()));
                Ai94CallTaskDetail ai94CallTaskDetail = new Ai94CallTaskDetail();
                ai94CallTaskDetail.setTaskMemberId(batchId);
                this.ai94calltaskdetailMapper.updateByExampleSelective(ai94CallTaskDetail,example);
            }
        }

    }

    @Override
    protected String updateTaskStatus(String taskBatchCode) {
        Ai94AddTask ai94AddTask = new Ai94AddTask();
        ai94AddTask.setTaskId(Long.valueOf(taskBatchCode));
        String result = postObject(null,ai94AddTask,"/v1/task/editTask");
        return dialWithResponse(result);
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        AiProviderConfiguration queryParam = new AiProviderConfiguration();
        queryParam.setAiProvider("Ai94Call");
        this.aiProviderConfiguration=aiProviderConfigurationMapper.selectOne(queryParam);
    }

    private String getToken(String timestamp) throws EncryptException, IllegalArgumentException {
        // 获取加密token
        String secret = "cbc1ea39afa88efb6829de0c80037943";  // 秘钥
        String key = AesUtils.encrypt(secret, timestamp + "#" + secret);  // 使用秘钥对timestamp和secret进行加密
        Map<String, String> header = new HashMap<>();  // 请求头
        header.put("x-timestamp", timestamp);  // 设置请求头中的时间戳
        header.put("x-ciphertext", key);  // 设置请求头中的加密结果
        Map<String, String> param = new HashMap<>();  // 请求参数
        param.put("cropId", "0a41ca2020ef5223");  // 设置请求参数中的作物ID
        String result = super.getObject(header, param, "https://opengateway.c1f.cc/authority/accessToken");  // 发送请求并获取结果
        JSONObject jsonResult = JSONObject.parseObject(result);  // 将结果转化为JSON对象
        if (jsonResult.getString("status") != null && jsonResult.getString("status").equals("200")) {  // 判断结果中的状态是否为200
            return jsonResult.getString("data");  // 返回结果中的数据
        } else {
            throw new IllegalArgumentException("获取token 失败");  // 抛出异常，提示获取token失败
        }
    }

    @Override
    protected String getObject(Map<String,String> header, Map<String,String> param, String method) {
        try {
            header = buildCommonHeader(header);
            return super.getObject(header,param,method);
        } catch (EncryptException|IllegalArgumentException e) {
            log.error(e.getMessage(),e);
        }
        return null;
    }

    @Override
    protected String postObject(Map<String,String> header, Object param, String method) {
        try {
            header = buildCommonHeader(header);
            return super.postObject(header,param,method);
        } catch (EncryptException|IllegalArgumentException e) {
            log.error(e.getMessage(),e);
        }
        return null;
    }

    private Map<String, String> buildCommonHeader(Map<String, String> header) throws EncryptException {
        header = Optional.ofNullable(header).orElse(new HashMap<>());
        header.put("x-access-token",this.getToken(formatTokenDate(new Date())));
        return header;
    }
    private String formatTokenDate(Date tokenDate){
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        return sdf.format(tokenDate);
    }
}
