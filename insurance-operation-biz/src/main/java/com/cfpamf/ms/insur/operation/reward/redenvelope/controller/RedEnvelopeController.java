package com.cfpamf.ms.insur.operation.reward.redenvelope.controller;

import com.cfpamf.ms.insur.operation.activity.listener.GrantCouponsListener;
import com.cfpamf.ms.insur.operation.base.annotaions.ResponseDecorated;
import com.cfpamf.ms.insur.operation.base.annotaions.WxTokenAuth;
import com.cfpamf.ms.insur.operation.base.constant.BaseConstants;
import com.cfpamf.ms.insur.operation.base.util.HttpRequestUtil;
import com.cfpamf.ms.insur.operation.reward.redenvelope.po.vo.RedEnvelopeVo;
import com.cfpamf.ms.insur.operation.reward.redenvelope.service.RedEnvelopeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 红包奖励
 *
 * <AUTHOR>
 * @date 2022/1/4 18:13
 */
@Slf4j
@Api(value = "红包奖励接口", tags = {"红包奖励接口"})
@RequestMapping(value = {BaseConstants.WX_VERSION + "/operation/reward/red_envelope"})
@ResponseDecorated
@RestController
public class RedEnvelopeController {

    @Autowired
    RedEnvelopeService redEnvelopeService;

    /**
     * 获取用户活动红包
     *
     * @return
     */
    @GetMapping("/user")
    @ApiOperation("获取用户活动红包集合")
    @WxTokenAuth
    public List<RedEnvelopeVo> getRedEnvelope(@RequestParam(required = false) @ApiParam("活动id，不传的话去BMS红包活动id配置中找第一个") Long saId) {
        String userId = HttpRequestUtil.getUserId();
        return redEnvelopeService.getRedEnvelope(saId,userId);
    }

    /**
     * 获取用户活动红包
     *
     * @param id
     * @return
     */
    @GetMapping("/{id}")
    @ApiOperation("获取红包详情")
    @WxTokenAuth
    public RedEnvelopeVo detail(@PathVariable Long id) {
        String userId = HttpRequestUtil.getUserId();
        return redEnvelopeService.detail(id, userId);
    }

    /**
     * 拆红包
     *
     * @param id
     * @return
     */
    @PostMapping("/open/{id}")
    @ApiOperation("拆红包")
    @WxTokenAuth
    public RedEnvelopeVo openRedEnvelop(@PathVariable Long id) {
        String userId = HttpRequestUtil.getUserId();
        return redEnvelopeService.openRedEnvelop(id, userId);
    }

    /**
     * 触发订单红包计算
     *
     * @param orderId
     * @return
     */
    @PostMapping("/trigger/calculation/{orderId}")
    @ApiOperation(value = "触发订单红包计算",hidden = true)
    @WxTokenAuth
    public void triggerCalculation(@PathVariable String orderId,@RequestBody(required = false) List<String> orderIdList) {
         redEnvelopeService.triggerCalculation(orderId , orderIdList);
    }

}
