package com.cfpamf.ms.insur.operation.assistant.controller;

import com.cfpamf.ms.insur.operation.assistant.service.AssistantTargetEstimateService;
import com.cfpamf.ms.insur.operation.assistant.service.CalcContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.util.Map;

@RestController
@RequestMapping("/assistant/estimate")
public class AssistantTargetEstimateController {
    @Autowired
    AssistantTargetEstimateService assistantTargetEstimateService;

    @RequestMapping(value = "/target", produces = "application/json", method = RequestMethod.POST)
    public Map<String, BigDecimal> calcTargetEstimate(@RequestParam String bchCode)
    {
        CalcContext context = new CalcContext();
        context.setBchCode(bchCode);
        return assistantTargetEstimateService.calcTargetEstimate(context);
    }

    @RequestMapping(value = "/increment", produces = "application/json", method = RequestMethod.POST)
    public Map<String, BigDecimal> calcIncrementEstimate(@RequestParam String bchCode)
    {
        CalcContext context = new CalcContext();
        context.setBchCode(bchCode);
        return assistantTargetEstimateService.calcIncrementEstimate(context);
    }
}
