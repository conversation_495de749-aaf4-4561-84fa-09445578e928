package com.cfpamf.ms.insur.operation.reward.redenvelope.helper;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cfpamf.common.ms.exception.MSBizNormalException;
import com.cfpamf.common.ms.exception.MSException;
import com.cfpamf.ms.insur.operation.base.constant.BusinessConstants;
import com.cfpamf.ms.insur.operation.base.constant.CharConstants;
import com.cfpamf.ms.insur.operation.base.constant.RedEnvelopeConstants;
import com.cfpamf.ms.insur.operation.base.helper.BmsHelper;
import com.cfpamf.ms.insur.operation.base.service.RedisLockTemplate;
import com.cfpamf.ms.insur.operation.msg.dao.OrderMapper;
import com.cfpamf.ms.insur.operation.pco.entity.Dictionary;
import com.cfpamf.ms.insur.operation.reward.redenvelope.dao.SystemRedEnvelopeMapper;
import com.cfpamf.ms.insur.operation.reward.redenvelope.enums.EnumRedEnvelopePoolLevel;
import com.cfpamf.ms.insur.operation.reward.redenvelope.po.entity.SystemRedEnvelope;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;


/**
 * 红包池
 * <p>
 * TODO 配置化 生成红包池 暂无配置入口 开门红包活动固定红包配置
 *
 * <AUTHOR>
 * @date 2022/1/9 15:42
 */
@Component
@Slf4j
public class RedEnvelopePool {

    public static String DATA_TYPE_POLICY_NO = "POLICY_NO";
    public static String DATA_TYPE_ORDER_ID = "ORDER_ID";
    @Autowired
    RedisLockTemplate redisLockTemplate;

    /**
     * RedisTemplate
     */
    @Autowired
    private RedisTemplate<String, String> redisTemplate;
    @Autowired
    OrderMapper orderMapper;
    @Autowired
    BmsHelper bmsHelper;

    @Autowired
    SystemRedEnvelopeMapper systemRedEnvelopeMapper;


    /**
     * 获取红包金额
     * 只支持通过保单或者订单金额进行红包金额计算
     *
     * @param systemRedEnvelope
     * @return
     */
    public BigDecimal getRedEnvelopeAmount(SystemRedEnvelope systemRedEnvelope) {

        String dataId = systemRedEnvelope.getDataId();
        String dataType = systemRedEnvelope.getDataType();
        BigDecimal amount;
        //如果dataType 是ORDER_ID是团险订单 POLICY_NO是个险订单
        if (Objects.equals(DATA_TYPE_POLICY_NO, dataType)) {
            amount = orderMapper.getPolicyAmount(dataId);
        } else if (Objects.equals(DATA_TYPE_ORDER_ID, dataType)) {
            amount = orderMapper.getOrderAmount(dataId);
            BigDecimal orderAmount = new BigDecimal(5000);
            //金额大于5000的要扣除第一笔退保的金额
            if (amount.compareTo(orderAmount)>0){
                amount = amount.subtract(orderMapper.getCancelOrderAmount(dataId));
            }
        } else {
            throw new MSBizNormalException("", "该红包的数据类型，暂不支持。");
        }
        EnumRedEnvelopePoolLevel envelopePoolLevel = EnumRedEnvelopePoolLevel.getByAmount(amount);
        if (Objects.isNull(envelopePoolLevel)) {
            throw new MSBizNormalException("", "该红包异常，请联系工作人员确认。");
        }
        String redEnvelopePoolKey = envelopePoolLevel.getRedEnvelopePoolKey();
        String redEnvelopeAmountLockKey = getRedEnvelopeAmountLockKey(redEnvelopePoolKey);
        boolean lock = redisLockTemplate.lock(redEnvelopeAmountLockKey, 3L, "拆红包人数过多，请稍后重试。");
        if (lock) {
            try {
                //如果不存在key 初始化红包池
                if (!redisTemplate.hasKey(redEnvelopePoolKey)) {
                    initRedEnvelopePool(envelopePoolLevel);
                }
                Long size = redisTemplate.opsForList().size(redEnvelopePoolKey);
                //如果红包池已经抽取完 初始化红包池
                if (Objects.isNull(size) || size == 0) {
                    size = initRedEnvelopePool(envelopePoolLevel);
                }
                //获取红包池随机索引的红包金额
                int index = new Random().nextInt(size.intValue());
                String amountString = redisTemplate.opsForList().index(redEnvelopePoolKey, index);
                //移除一个红包金额
                redisTemplate.opsForList().remove(redEnvelopePoolKey, 1, amountString);
                log.info("红包id:{} 获取金额：{}", systemRedEnvelope.getId(), amountString);
                return new BigDecimal(amountString);
            } catch (Exception e) {
                e.printStackTrace();
                log.error("获取红包金额错误 红包信息：{}", JSON.toJSONString(systemRedEnvelope));
                throw new MSException("", "获取红包金额错误!");
            } finally {
                redisLockTemplate.unLock(redEnvelopeAmountLockKey);
            }
        }
        throw new MSBizNormalException("", "拆红包人数过多，请稍后重试。");
    }

    public String getRedEnvelopeAmountLockKey(String redEnvelopePoolKey) {
        return RedEnvelopeConstants.RED_ENVELOPE_POOL_LOCK__KEY_PREFIX + CharConstants.LINE + redEnvelopePoolKey;
    }

    /**
     * 初始化红包池
     *
     * @param envelopePoolLevel
     */
    private Long initRedEnvelopePool(EnumRedEnvelopePoolLevel envelopePoolLevel) {
        List<String> poolInitList = getRedEnvelopePoolDataList(envelopePoolLevel.getRedEnvelopePoolKey());
        redisTemplate.delete(envelopePoolLevel.getRedEnvelopePoolKey());
        redisTemplate.opsForList().rightPushAll(envelopePoolLevel.getRedEnvelopePoolKey(), poolInitList);
        return (long) poolInitList.size();
    }

    /**
     * 获取红包池数据结构
     * 通过红包池的key 去bms字段中查询
     *
     * @param redEnvelopePoolKey
     * @return
     */
    public List<String> getRedEnvelopePoolDataList(String redEnvelopePoolKey) {
        Dictionary redEnvelopeConfig = systemRedEnvelopeMapper.selectConfigs(BusinessConstants.BMS_DICTIONARY_OPERATION_RED_ENVELOPE_POOL, redEnvelopePoolKey);
        if (Objects.isNull(redEnvelopeConfig)) {
            throw new MSBizNormalException("", "红包池配置出错!");
        }
        JSONObject redEnvelopeConfigMap = JSON.parseObject(redEnvelopeConfig.getName());
        List<String> redEnvelopePoolDataList = Lists.newArrayList();
        //初始化红包池数据结构
        for (String amount : redEnvelopeConfigMap.keySet()) {
            Integer count = (Integer) redEnvelopeConfigMap.get(amount);
            for (int i = 0; i < count; i++) {
                redEnvelopePoolDataList.add(amount);
            }
        }
        Collections.shuffle(redEnvelopePoolDataList);
        return redEnvelopePoolDataList;
    }
}
