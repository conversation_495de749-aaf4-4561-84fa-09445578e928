package com.cfpamf.ms.insur.operation.customer.service;

import com.alibaba.excel.EasyExcel;
import com.cfpamf.common.ms.util.DateUtil;
import com.cfpamf.ms.common.insur.BizException;
import com.cfpamf.ms.insur.operation.activity.dao.SmCommissionDetailMapper;
import com.cfpamf.ms.insur.operation.base.bean.SmyPageInfo;
import com.cfpamf.ms.insur.operation.base.constant.BaseConstants;
import com.cfpamf.ms.insur.operation.base.helper.WxCheckAuthorityHelper;
import com.cfpamf.ms.insur.operation.base.service.DataAuthService;
import com.cfpamf.ms.insur.operation.base.util.CommonUtil;
import com.cfpamf.ms.insur.operation.base.vo.WxSessionVO;
import com.cfpamf.ms.insur.operation.customer.dao.CustomerInterruptionFollowMapper;
import com.cfpamf.ms.insur.operation.customer.dao.CustomerInterruptionPolicyMapper;
import com.cfpamf.ms.insur.operation.customer.dao.CustomerLoanFirstFollowMapper;
import com.cfpamf.ms.insur.operation.customer.dao.CustomerMapper;
import com.cfpamf.ms.insur.operation.customer.dto.*;
import com.cfpamf.ms.insur.operation.customer.query.CustomerConversionQuery;
import com.cfpamf.ms.insur.operation.customer.po.CustomerLoanFirstFollowPo;
import com.cfpamf.ms.insur.operation.customer.query.CustomerFollowCntQuery;
import com.cfpamf.ms.insur.operation.customer.query.CustomerPolicyQuery;
import com.cfpamf.ms.insur.operation.customer.query.WxInterruptionCustomerQuery;
import com.cfpamf.ms.insur.operation.phoenix.enums.EnumTodoBizType;
import com.cfpamf.ms.insur.operation.phoenix.service.PhoenixEmpTodoService;
import com.cfpamf.ms.insur.operation.phoenix.enums.EnumTodoBizType;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.util.StringUtil;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class CustomerService {

    CustomerMapper customerMapper;

    WxCheckAuthorityHelper wxCheckAuthorityHelper;

    CustomerInterruptionPolicyMapper customerInterruptionPolicyMapper;

    DataAuthService dataAuthService;

    SmCommissionDetailMapper smCommissionDetailMapper;

    CustomerLoanFirstFollowMapper customerLoanFirstFollowMapper;

    PhoenixEmpTodoService phoenixEmpTodoService;

    /**
     * 查询用户微信我的保险用户
     *
     * @param query
     * @return
     */
    public PageInfo<WxInterruptionCustomerDto> getBreakCustomerListByPage(WxInterruptionCustomerQuery query) {
        WxSessionVO session = wxCheckAuthorityHelper.checkAuthority(query.getOpenId(), query.getAuthorization());
        query.setUserId(session.getUserId());
        if (!Objects.isNull(query.getPageNo())) {
            PageHelper.startPage(query.getPageNo(), query.getPageSize());
        }
        endDatePlus(query);
        PageInfo<WxInterruptionCustomerDto> pageInfo = new PageInfo<>(customerMapper.listBreakCustomer(query));
        return pageInfo;
    }

    /**
     * 查询客户保单数据
     *
     * @param query
     * @return
     */
    public SmyPageInfo<CustomerPolicyDto, WxCmsSmyDto> getPolicyNoAll(CustomerPolicyQuery query) {
        WxSessionVO session = wxCheckAuthorityHelper.checkAuthority(query.getOpenId(), query.getAuthorization());

        return getPolicyNoAllWithoutWx(query);
    }

    /**
     * 查询客户保单数据,与{@link #getPolicyNoAll(CustomerPolicyQuery)}相比，不校验微信相关的逻辑
     *
     * @param query
     * @return
     */
    public SmyPageInfo<CustomerPolicyDto, WxCmsSmyDto> getPolicyNoAllWithoutWx(CustomerPolicyQuery query) {
        WxCmsSmyDto smy = new WxCmsSmyDto();

        if (query.getPage() > 0) {
            PageHelper.startPage(query.getPage(), query.getSize());
        }
        List<CustomerPolicyDto> apps = customerMapper.listAppPolicyListAll(query);

        if (CollectionUtils.isNotEmpty(apps)) {
            BigDecimal sumAmount = BigDecimal.ZERO;
            for (CustomerPolicyDto pl : apps) {
                String showStatusName = "";
                if (StringUtil.isNotEmpty(pl.getPayStatus()) && StringUtil.isNotEmpty(pl.getAppStatus()) && StringUtil.isNotEmpty(pl.getFullEndTime())) {
                    //映射显示状态
                    showStatusName = CommonUtil.getShowStatusName(pl.getPayStatus(), pl.getAppStatus(), DateUtil.parseDate(pl.getFullEndTime(), DateUtil.CN_LONG_FORMAT));
                }
                pl.setShowStatusName(showStatusName);

                if (Objects.equals(pl.getPayStatus(), BaseConstants.ORDER_STATUS_PAYED)
                        && DateUtil.getEndOfDay(DateUtil.parseDate(pl.getEndTime(), DateUtil.CN_YEAR_MONTH_DAY_FORMAT)).compareTo(new Date()) < 0) {
                    pl.setAppStatus(BaseConstants.POLICY_STATUS_INVALID);
                }
            }
            ;

            smy = customerMapper.listAppPolicyCount(query);
        }
        return new SmyPageInfo<>(apps, smy);
    }

    /**
     * 查询客户保单数据-日复盘用
     *
     * @param query
     * @return
     */
    public SmyPageInfo<CustomerPolicyDto, WxCmsSmyDto> getPolicyNoAllRetrospective(CustomerPolicyQuery query) {
        WxCmsSmyDto smy = new WxCmsSmyDto();

        if (query.getPage() > 0) {
            PageHelper.startPage(query.getPage(), query.getSize());
        }
        List<CustomerPolicyDto> apps = customerMapper.listAppPolicyListAll(query);

        if (CollectionUtils.isNotEmpty(apps)) {
            BigDecimal sumAmount = BigDecimal.ZERO;
            for (CustomerPolicyDto pl : apps) {
                String showStatusName = "";
                if (StringUtil.isNotEmpty(pl.getPayStatus()) && StringUtil.isNotEmpty(pl.getAppStatus()) && StringUtil.isNotEmpty(pl.getFullEndTime())) {
                    //映射显示状态
                    showStatusName = CommonUtil.getShowStatusName(pl.getPayStatus(), pl.getAppStatus(), DateUtil.parseDate(pl.getFullEndTime(), DateUtil.CN_LONG_FORMAT));
                }
                pl.setShowStatusName(showStatusName);

                if (Objects.equals(pl.getPayStatus(), BaseConstants.ORDER_STATUS_PAYED)
                        && DateUtil.getEndOfDay(DateUtil.parseDate(pl.getEndTime(), DateUtil.CN_YEAR_MONTH_DAY_FORMAT)).compareTo(new Date()) < 0) {
                    pl.setAppStatus(BaseConstants.POLICY_STATUS_INVALID);
                }
            }
            ;

            smy = customerMapper.listAppPolicyCount(query);
        }
        return new SmyPageInfo<>(apps, smy);
    }

    /**
     * 断保客户数据汇总
     *
     * @param openId
     * @param authorization
     * @return
     */
    public WxCmsSmyDto dataSummarization(String openId, String authorization) {
        WxSessionVO session = wxCheckAuthorityHelper.checkAuthority(openId, authorization);
        WxCmsSmyDto dto = customerInterruptionPolicyMapper.getQtyByRenewEmp(session.getUserId());
        WxCmsSmyDto result = Objects.isNull(dto) ? new WxCmsSmyDto() : dto;
        result.setInterruptionQty(customerMapper.getInterruptionQty(session.getUserId(), 1));
        result.setRenewQty(customerMapper.getInterruptionQty(session.getUserId(), 2));
        return result;
    }

    /**
     * 查询用户我的断保客户(后台)
     *
     * @param query
     * @return
     */
    public PageInfo<WxInterruptionCustomerDto> getBackCustomerListByPage(WxInterruptionCustomerQuery query) {
        if (query.getPageNo() > 0) {
            PageHelper.startPage(query.getPageNo(), query.getPageSize());
        }
        dataAuthService.dataAuth(query);
        endDatePlus(query);
        PageInfo<WxInterruptionCustomerDto> pageInfo = new PageInfo<>(customerMapper.listBackBreakCustomer(query));
        return pageInfo;
    }

    public void downloadCustomerList(WxInterruptionCustomerQuery query, HttpServletResponse response) {
        String interruption = "1";
        query.setPageNo(0);
        List<WxInterruptionCustomerDto> list = new ArrayList<>();
        if (getBackCustomerListByPage(query) != null) {
            list = getBackCustomerListByPage(query).getList();
        }
        if (Objects.equals(query.getUserType(), interruption)) {
            downloadCustomerInterruptionList(list, response);
        } else {
            downloadCustomerRenewList(list, response);
        }
    }

    private void downloadCustomerRenewList(List<WxInterruptionCustomerDto> list, HttpServletResponse response) {
        List<WxRenewCustomerExcelDto> list1 = new ArrayList<>(list.size());
        for (WxInterruptionCustomerDto wxInterruptionCustomerDto : list) {
            WxRenewCustomerExcelDto wxRenewCustomerExcelDto = new WxRenewCustomerExcelDto();
            wxRenewCustomerExcelDto.copyProperties(wxInterruptionCustomerDto);
            copyCustomerExcelDto(wxRenewCustomerExcelDto, wxInterruptionCustomerDto);
            list1.add(wxRenewCustomerExcelDto);
        }
        try (OutputStream os = response.getOutputStream()) {
            String fileName = URLEncoder.encode("激活客户列表" + new SimpleDateFormat("yyyyMMdd").format(new Date()) + ".xlsx", StandardCharsets.UTF_8.name());
            response.setHeader("Content-disposition", "attachment; filename=" + fileName);
            response.setContentType("application/octet-stream");
            EasyExcel.write(response.getOutputStream(), WxRenewCustomerExcelDto.class)
                    .sheet("激活客户列表").doWrite(list1);
        } catch (Exception e) {
            throw new BizException("文件下载失败", e);
        }
    }

    private void downloadCustomerInterruptionList(List<WxInterruptionCustomerDto> list, HttpServletResponse response) {
        List<WxInterruptionCustomerExcelDto> list1 = new ArrayList<>(list.size());
        for (WxInterruptionCustomerDto wxInterruptionCustomerDto : list) {
            String originalPhone = wxInterruptionCustomerDto.getCellPhone();
            String maskedPhone = maskSensitiveData(originalPhone);
            wxInterruptionCustomerDto.setCellPhone(maskedPhone);
        }
        for (WxInterruptionCustomerDto wxInterruptionCustomerDto : list) {
            WxInterruptionCustomerExcelDto wxInterruptionCustomerExcelDto = new WxInterruptionCustomerExcelDto();
            wxInterruptionCustomerExcelDto.copyProperties(wxInterruptionCustomerDto);
            wxInterruptionCustomerExcelDto.setCellPhone(wxInterruptionCustomerDto.getCellPhone());
            wxInterruptionCustomerExcelDto.setLastCustomerAdminName(wxInterruptionCustomerDto.getLastCustomerAdminName() + "-" + wxInterruptionCustomerDto.getLastCustomerAdmin());
            wxInterruptionCustomerExcelDto.setLoan1(wxInterruptionCustomerDto.getLoan() == 1 ? "是" : "否");
            wxInterruptionCustomerExcelDto.setCurrentLoan1(wxInterruptionCustomerDto.getCurrentLoan() == 1 ? "是" : "否");
            list1.add(wxInterruptionCustomerExcelDto);
        }
        try (OutputStream os = response.getOutputStream()) {
            String fileName = URLEncoder.encode("断保客户列表" + new SimpleDateFormat("yyyyMMdd").format(new Date()) + ".xlsx", StandardCharsets.UTF_8.name());
            response.setHeader("Content-disposition", "attachment; filename=" + fileName);
            response.setContentType("application/octet-stream");
            EasyExcel.write(response.getOutputStream(), WxInterruptionCustomerExcelDto.class)
                    .sheet("断保客户列表").doWrite(list1);
        } catch (Exception e) {
            throw new BizException("文件下载失败", e);
        }
    }

    private void copyCustomerExcelDto(WxRenewCustomerExcelDto wxRenewCustomerExcelDto, WxInterruptionCustomerDto wxInterruptionCustomerDto) {
        wxRenewCustomerExcelDto.setCellPhone(maskSensitiveData(wxInterruptionCustomerDto.getCellPhone()));
        wxRenewCustomerExcelDto.setRenewEmpName(wxInterruptionCustomerDto.getRenewEmpName() + "-" + wxInterruptionCustomerDto.getRenewEmp());
        wxRenewCustomerExcelDto.setLoan1(wxInterruptionCustomerDto.getLoan() == 1 ? "是" : "否");
        wxRenewCustomerExcelDto.setCurrentLoan1(wxInterruptionCustomerDto.getCurrentLoan() == 1 ? "是" : "否");
    }

    private String maskSensitiveData(String data) {
        if (data == null || data.isEmpty()) {
            return data;
        }

        int length = data.length();
        if (length <= 7) {
            return data;
        }

        // 保留前3位和后4位，中间用*替代
        StringBuilder maskedData = new StringBuilder();
        maskedData.append(data.substring(0, 3));
        for (int i = 3; i < length - 4; i++) {
            maskedData.append("*");
        }
        maskedData.append(data.substring(length - 4));

        return maskedData.toString();
    }


    /**
     * 如果查询时间不为空 则往后推一天
     *
     * @param end
     */
    private void endDatePlus(WxInterruptionCustomerQuery end) {
        if (Objects.isNull(end)) {
            return;
        }
        endPlus(WxInterruptionCustomerQuery::getEndDate, WxInterruptionCustomerQuery::setEndDate, end);
        endPlus(WxInterruptionCustomerQuery::getRenewEndDate, WxInterruptionCustomerQuery::setRenewEndDate, end);
    }

    private void endPlus(Function<WxInterruptionCustomerQuery, LocalDate> getter,
                         BiConsumer<WxInterruptionCustomerQuery, LocalDate> setter, WxInterruptionCustomerQuery query) {
        if (Objects.nonNull(getter.apply(query))) {
            setter.accept(query, getter.apply(query).plusDays(1L));

        }
    }

    /**
     * A类客户跟进
     * @param dto 跟进dto
     */
    public void saveLoanFirstFollowRecord(CustomerLoanFirstFollowPo dto) {
        //更新以往的跟进记录最新状态为0
        customerLoanFirstFollowMapper.updateNewest(dto.getLoanCustId());
        customerLoanFirstFollowMapper.insert(dto);

        //更新待办为已完成
        if(dto.getFollowState().equals(0)) {
            phoenixEmpTodoService.finishTodo(EnumTodoBizType.LOAN_FIRST, dto.getLoanCustId(), "跟进");
        }
    }

    /**
     * 根据客户获取跟进记录列表（包括AI跟进）
     * @param customerId 客户主键
     * @return List
     */
    public List<CustomerFollowDto> selectFollowByCustomer(String customerId){
        List<CustomerFollowDto> list = new ArrayList<>();
        list.addAll(customerMapper.selectInterruptionFollowByCustomer(customerId));
        list.addAll(customerMapper.selectAiFollowByCustomer(customerId));
        list.addAll(customerMapper.selectLoanFollowByCustomer(customerId));
        list.addAll(customerMapper.selectLoanFirstFollowByCustomer(customerId));
        if (!CollectionUtils.isEmpty(list)) {
            List<CustomerFollowDto> sortList = list.stream()
                    .sorted(Comparator.comparing(CustomerFollowDto::getFollowTime).reversed())
                    .collect(Collectors.toList());
            return sortList;
        }
        return new ArrayList<>();
    }

    public List<CustomerFollowCntDto> getCustomerFollowCnt(CustomerFollowCntQuery query) {
        if (EnumTodoBizType.INTERRUPTION.name().equals(query.getBizType().name())) {
            return getInterruptionProcess(query);
        } else if (EnumTodoBizType.RENEW_SHORT.name().equals(query.getBizType().name())) {
            return getRenewShortProcess(query);
        } else if (EnumTodoBizType.RENEW_LONG.name().equals(query.getBizType().name())) {
            return getRenewLongProcess(query);
        }
        return new ArrayList<>();
    }

    /**
     * 获取续期跟进指标
     * @param query 查询参数
     * @return CustomerFollowCntDto
     */
    private List<CustomerFollowCntDto> getRenewLongProcess(CustomerFollowCntQuery query) {
        //1.获取当日激活指标
        List<CustomerFollowCntDto> conversionLists = customerMapper.listRenewLongConversionByJobNumbers(query);
        //2.获取当日跟进指标
        List<CustomerFollowCntDto> followLists = customerMapper.listRenewLongFollowCntByJobNumbers(query);

        //3.合并输出list
        return megergedList(conversionLists, followLists);
    }

    /**
     * 获取续保跟进指标
     * @param query 查询参数
     * @return CustomerFollowCntDto
     */
    private List<CustomerFollowCntDto> getRenewShortProcess(CustomerFollowCntQuery query) {
        //1.获取当日激活指标
        List<CustomerFollowCntDto> conversionLists = customerMapper.listRenewShortConversionByJobNumbers(query);
        //2.获取当日跟进指标
        List<CustomerFollowCntDto> followLists = customerMapper.listRenewShortFollowCntByJobNumbers(query);

        //3.合并输出list
        return megergedList(conversionLists, followLists);
    }

    /**
     * 获取断保跟进指标
     * @param query 查询参数
     * @return CustomerFollowCntDto
     */
    private List<CustomerFollowCntDto> getInterruptionProcess(CustomerFollowCntQuery query) {
        //1.获取当日激活指标
        List<CustomerFollowCntDto> conversionLists = customerMapper.listInterruptionConversionByJobNumbers(query);
        //2.获取当日跟进指标
        List<CustomerFollowCntDto> followLists = customerMapper.listInterruptionFollowCntByJobNumbers(query);

        //3.合并输出list
        return megergedList(conversionLists, followLists);
    }

    public List<CustomerConversionDto> getCustomerConversion(CustomerConversionQuery query) {
        return smCommissionDetailMapper.sumConversionAmtByUser(query);
    }

    private List<CustomerFollowCntDto> megergedList(List<CustomerFollowCntDto> conversionLists, List<CustomerFollowCntDto> followLists) {
        List<CustomerFollowCntDto> mergedList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(conversionLists) || CollectionUtils.isNotEmpty(followLists)) {
            mergedList.addAll(followLists);
            mergedList.addAll(conversionLists);
        }
        return mergedList.stream()
                .collect(Collectors.groupingBy(CustomerFollowCntDto::getEmpCode))
                .values().stream()
                .map(list -> list.stream()
                        .reduce((emp1, emp2) -> {CustomerFollowCntDto dto = new CustomerFollowCntDto();
                            dto.setEmpCode(StringUtils.isBlank(emp1.getEmpCode())?emp2.getEmpCode():emp1.getEmpCode());
                            dto.setBizType(StringUtils.isBlank(emp1.getBizType())?emp2.getBizType():emp1.getBizType());
                            dto.setEmpName(StringUtils.isBlank(emp1.getEmpName())?emp2.getEmpName():emp1.getEmpName());
                            dto.setBchCode(StringUtils.isBlank(emp1.getBchCode())?emp2.getBchCode():emp1.getBchCode());
                            dto.setBchName(StringUtils.isBlank(emp1.getBchName())?emp2.getBchName():emp1.getBchName());
                            dto.setFollowCnt(emp1.getFollowCnt());
                            dto.setConversionAmt(emp2.getConversionAmt());
                            dto.setConversionCnt(emp2.getConversionCnt());
                            return dto;}))
                .map(employee -> employee.orElseThrow(IllegalStateException::new))
                .collect(Collectors.toList());
    }

    public List<PolicyConversionAmtDto> getPolicyListForConversionAmt(List<String> employeeIdList, LocalDateTime startDate, LocalDateTime endDate){
        return smCommissionDetailMapper.getPolicyListForConversionAmt(employeeIdList, startDate, endDate);
    }
}
