package com.cfpamf.ms.insur.operation.aicall.vo.request;

import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class ImportPhone {
    private String taskId;
    private int secretType;
    private int secretId;
    private String plainText;
    private String cipherText;
    private List<CustomerInfo> customerInfoList;

    public String getTaskId() {
        return taskId;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }

    public int getSecretType() {
        return secretType;
    }

    public void setSecretType(int secretType) {
        this.secretType = secretType;
    }

    public int getSecretId() {
        return secretId;
    }

    public void setSecretId(int secretId) {
        this.secretId = secretId;
    }

    public String getPlainText() {
        return plainText;
    }

    public void setPlainText(String plainText) {
        this.plainText = plainText;
    }

    public String getCipherText() {
        return cipherText;
    }

    public void setCipherText(String cipherText) {
        this.cipherText = cipherText;
    }

    public List<CustomerInfo> getCustomerInfoList() {
        return customerInfoList;
    }

    public void setCustomerInfoList(List<CustomerInfo> customerInfoList) {
        this.customerInfoList = customerInfoList;
    }
}