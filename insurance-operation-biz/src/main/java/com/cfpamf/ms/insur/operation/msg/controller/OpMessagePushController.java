package com.cfpamf.ms.insur.operation.msg.controller;

import com.cfpamf.ms.insur.operation.base.annotaions.ResponseDecorated;
import com.cfpamf.ms.insur.operation.msg.pojo.query.OpMessagePushQuery;
import com.cfpamf.ms.insur.operation.msg.pojo.vo.OpMessagePushVO;
import com.cfpamf.ms.insur.operation.msg.service.OpMessagePushService;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Date 2021/9/2 17:06
 * @Version 1.0
 */
@RestController
@RequestMapping("pushLog")
@Api("营销工具-推送日志查询")
@ResponseDecorated
public class OpMessagePushController {
    @Resource
    private OpMessagePushService pushService;

    @PostMapping("/page/query")
    @ApiModelProperty("日志分页查询")
    public PageInfo<OpMessagePushVO> pageQueryMessageLog(@RequestBody OpMessagePushQuery pushQuery) {
        return pushService.pageQueryMessageReceiver(pushQuery);
    }

}

