package com.cfpamf.ms.insur.operation.customer.dto;

import org.springframework.util.StringUtils;
import com.alibaba.excel.annotation.ExcelProperty;
import com.cfpamf.ms.insur.operation.base.annotaions.Mask;
import com.cfpamf.ms.insur.operation.customer.enums.EnumRenewalIntention;
import com.cfpamf.ms.insur.operation.pco.util.LocalDateTimeConverter;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 微信用户列表信息
 *
 * <AUTHOR>
 **/
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class WxInterruptionCustomerDto {

    /**
     * 客户Id
     */
    String customerId;

    /**
     * 客户姓名
     */

    String customerName;

    /**
     * 客户证件类型
     */
    String idType;

    /**
     * 客户身份证号码
     */
    @Mask(dataType = Mask.DataType.ID_CARD)
    String idNumber;

    /**
     * 客户生日
     */
    String birthday;

    /**
     * 客户手机号
     */

    @Mask(dataType = Mask.DataType.MOBILE)
    String cellPhone;

    /**
     * email
     */
    @Mask(dataType = Mask.DataType.EMAIL)
    String email;

    /**
     * 居住地址
     */
    @Mask(dataType = Mask.DataType.AREA)
    String address;

    /**
     * 是否信贷客户
     */
    @ApiModelProperty(value = "是否在贷客户 0：否 1：是")
    Integer currentLoan;

    String area;

    @Mask(dataType = Mask.DataType.AREA)
    String areaName;

    @ApiModelProperty("断保时间")
    LocalDateTime lastInterruptionTime;

    @ApiModelProperty("最近跟进时间")
    LocalDateTime followTime;

    @ApiModelProperty("激活时间")
    LocalDateTime renewTime;

    @ApiModelProperty("激活保单")
    Integer renewPolicyCount;
    @ApiModelProperty("管护人")
    String lastCustomerAdmin;

    @ApiModelProperty("管护人姓名")
    String lastCustomerAdminName;
    @ApiModelProperty("激活人")
    String renewEmp;
    @ApiModelProperty("激活人姓名")
    String renewEmpName;

    /**
     * 区域
     */
    @ApiModelProperty(value = "区域")
    String regionName;

    /**
     * 机构
     */
    @ApiModelProperty(value = "机构")
    String orgName;

    @ApiModelProperty("激活保费")
    BigDecimal renewAmount;

    /**
     * 跟进情况
     */
    @ApiModelProperty(value = "跟进情况 noFollowUp:未跟进、high:高意向,median:中意向, low:低意向,none:无意向,lose_contact:联系不上")
    String intention;

    public String getIntention() {
        if (StringUtils.isEmpty(intention)){
            return EnumRenewalIntention.NO_FOLLOW_UP.getDesc();
        }
        return EnumRenewalIntention.dict(intention);
    }

    @ApiModelProperty(value = "断保天数")
    Integer days;

    @ApiModelProperty(value = "是否信贷客户 0：否 1：是")
    Integer loan;

}
