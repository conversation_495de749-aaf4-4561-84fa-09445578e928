package com.cfpamf.ms.insur.operation.planbook.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/12/8 11:24
 * @Version 1.0
 */
@Data
public class SmPlanRiskVO {

    @ApiModelProperty(value = "计划id")
    private Integer planId;

    /**
     * 险种id
     */
    @ApiModelProperty(value = "险种id")
    private Integer sysRiskId;
    /**
     * 字段名称 险种名称
     */
    @ApiModelProperty(value = "险种名称", required = true)
    String riskName;
    /**
     * 字段名称 险种编码
     */
    @ApiModelProperty(value = "险种编码", required = true)
    String riskCode;
    /**
     * 险种标识 uuid
     */
    @ApiModelProperty(value = "险种标识 uuid ")
    private String riskKey;

    /**
     * 字段名称 险种类型 1-主险 2-附加险
     */
    @ApiModelProperty(value = "险种类型 1-主险 2-附加险", required = true, example = "1")
    private Integer riskType;

    @ApiModelProperty("类型名称")
    private String riskTypeName;

    @ApiModelProperty("是否豁免条款 0-否 1-是")
    private String exempt;

    @ApiModelProperty("豁免主体 1-投保人 2-被保人 3 投保人+被保人")
    private String exemptBody;

    @ApiModelProperty("险种分类 大分类/ 小分类 方式隔离 cx/xjc")
    private String riskClassify;

    @ApiModelProperty("支付方式 1-趸交 2-年缴 3-月缴[1,2,3]")
    private String payWay;

    /**
     * 字段名称 缴费年期 [\"20Y\",\"30Y\"]
     */
    @ApiModelProperty(value = "缴费年期 [\"20Y\",\"30Y\"]", required = true)
    String coveredYears;

    /**
     * 字段名称 保障期限 [\"20Y\",\"<70Y\",\"ALL\"]
     */
    @ApiModelProperty(value = "保障期限 20Y 20年 <70Y 至70周岁 ALL 终身  eg. [\"20Y\",\"<70Y\",\"ALL\"]", required = true)
    String validPeriod;

    /**
     * 字段名称 犹豫期
     */
    @ApiModelProperty(value = "犹豫期")
    Integer hesitationPeriod;
    /**
     * 字段名称 等待期
     */
    @ApiModelProperty(value = "等待期")
    Integer waitingPeriod;

    /**
     * 字段名称 宽限期
     */
    @ApiModelProperty(value = "宽限期")
    Integer gracePeriod;

    /**
     * 字段名称 受益人 0代表法定受益人 大于0代表指定x个 [0,3]
     */
    @ApiModelProperty(value = "受益人 0代表法定受益人 大于0代表指定x个 [0,3]")
    String beneficiary;

    @ApiModelProperty("额外参数 {\"isRevival\":true,\"revival\":\"2Y\"}")
    String riskParams;

    @ApiModelProperty("计划险种责任")
    private List<SmPlanRiskDutyVO> planRiskDutyList;

    private Integer version;

}
