package com.cfpamf.ms.insur.operation.planbook.controller;

import com.cfpamf.ms.insur.operation.base.annotaions.ResponseDecorated;
import com.cfpamf.ms.insur.operation.base.constant.BaseConstants;
import com.cfpamf.ms.insur.operation.planbook.form.OperationPlanBookMakeInfoForm;
import com.cfpamf.ms.insur.operation.planbook.form.OperationPlanBookSettingForm;
import com.cfpamf.ms.insur.operation.planbook.form.PlanBookPageQuery;
import com.cfpamf.ms.insur.operation.planbook.service.OperationPlanBookMakeInfoService;
import com.cfpamf.ms.insur.operation.planbook.service.PlanBookService;
import com.cfpamf.ms.insur.operation.planbook.validate.AbstractPlanBookBaseFormValidator;
import com.cfpamf.ms.insur.operation.planbook.vo.*;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2021/12/8 20:44
 * @Version 1.0
 */
@Slf4j
@Api(value = "计划书接口", tags = {"计划书接口"})
@RequestMapping(value = {BaseConstants.ADMIN_VERSION + "/operation/wx/plan/book"})
@ResponseDecorated
@RestController
public class PlanBookController {

    @Resource
    private PlanBookService planBookService;

    @Resource
    private OperationPlanBookMakeInfoService makeInfoService;

    @Autowired
    private Map<String, AbstractPlanBookBaseFormValidator> validatorMap;

    @ApiOperation("计划书制作信息")
    @GetMapping("/search/{productId}")
    public ProductAllVO queryByProductId(@PathVariable("productId") Integer productId) {
        return planBookService.queryWXProductFormInfo(productId);
    }


    @ApiOperation("保存计划书,返回保存后的计划书id")
    @PostMapping("/addPlanBook")
    public Integer insertPlanBook(@RequestBody OperationPlanBookMakeInfoForm planBookForm) {
        if (planBookForm.getId() == null) {
            return makeInfoService.addMakePlanBook(planBookForm);
        }
        return makeInfoService.editPlanBook(planBookForm);
    }

    @ApiOperation("更新计划书")
    @PostMapping("/editPlanBook")
    public void editPlanBook(@RequestBody OperationPlanBookMakeInfoForm planBookForm) {
        makeInfoService.editPlanBook(planBookForm);
    }

    @ApiOperation("保存计划书配置文件")
    @PostMapping("/addPlanBookSetting")
    public void addPlanBookSetting(@RequestBody OperationPlanBookSettingForm planBookSettingForm) {
        if (planBookSettingForm.getId() == null) {
            makeInfoService.addPlanBookSetting(planBookSettingForm);
        } else {
            makeInfoService.editPlanBookSetting(planBookSettingForm);
        }
    }

    @ApiOperation("编辑计划书配置文件")
    @PostMapping("/editPlanBookSetting")
    public void editPlanBookSetting(@RequestBody OperationPlanBookSettingForm planBookSettingForm) {
        makeInfoService.editPlanBookSetting(planBookSettingForm);
    }

    @ApiOperation("计划书预览")
    @GetMapping("/editPlanBookSetting/{id}")
    public PlanBookPreviewVO planBookPreview(@PathVariable("id") Integer planBookId) {
        return planBookService.previewPlanBook(planBookId);
    }

    @ApiOperation("病种详情,传责任id")
    @GetMapping("/view/duty/disease/{id}")
    public List<RiskDutyDiseaseVO> viewDutyDisease(@PathVariable("id") Integer dutyId,
                                                   @RequestParam(value = "keyword", required = false) String keyword) {
        return planBookService.queryDutyDiseaseByDutyId(dutyId, keyword);
    }

    @ApiOperation("分页列表")
    @PostMapping("/page/query")
    public PageInfo<PlanBookPageVO> queryPlanBook(@RequestBody PlanBookPageQuery query) {
        return planBookService.queryPlanBook(query);
    }

    @ApiOperation("删除计划书")
    @PostMapping("/deletePlanBook")
    public Integer logicDeletePlanBook(Integer planBookId) {
        return makeInfoService.logicDelData(planBookId);
    }


    @ApiOperation("计划书个人统计")
    @GetMapping("/person/statistic")
    public PlanBookPersonStatistic queryPersonPlanBookStatistic() {
        return planBookService.queryPersonPlanBookStatistic();
    }


    @ApiOperation("计划书条件校验")
    @PostMapping("/validate")
    public void planPlanBookValidate(@RequestBody OperationPlanBookMakeInfoForm planBookForm) {
        validatorMap.get("planBookValidator" + planBookForm.getProductId())
                .copySelf()
                .setValidProspectusProductFactorForm(planBookForm)
                .valid();
    }

}
