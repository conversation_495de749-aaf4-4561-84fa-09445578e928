package com.cfpamf.ms.insur.operation.claim.service;

import com.cfpamf.ms.insur.operation.base.helper.LockHelper;
import com.cfpamf.ms.insur.operation.claim.dao.ClaimPushMonthDao;
import com.cfpamf.ms.insur.operation.claim.po.SmClaimPushMonth;
import com.cfpamf.ms.insur.operation.dingtalk.service.DingTalkRobotService;
import com.cfpamf.ms.insur.operation.msg.service.AuthUserDingTalkService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2024/3/11
 * @Version 1.0
 */
@Service
@Slf4j
public class ClaimPushServiceImpl {

    @Autowired
    private ClaimPushMonthDao claimPushMonthDao;

    @Autowired
    private DingTalkRobotService dingTalkRobotService;

    @Autowired
    private AuthUserDingTalkService authUserDingTalkService;

    @Autowired
    LockHelper lockHelper;

    public void sendClaimDataByMonth(String month) throws InterruptedException {

        lockHelper.lockRun(
                month
                , () -> {
                    List<SmClaimPushMonth> claimPushMonthList = claimPushMonthDao.listByMonth(month, "month");

                    if (CollectionUtils.isEmpty(claimPushMonthList)) {
                        return;
                    }

                    Map<String, String> jobNumberMap =
                            authUserDingTalkService.getJobNumberDingUserIdMap(
                                    claimPushMonthList.stream()
                                            .map(SmClaimPushMonth::getSendTo)
                                            .distinct().collect(Collectors.toList())
                            );

                    for (SmClaimPushMonth pushMonth : claimPushMonthList) {
                        try{
                            if (
                                    Objects.nonNull(jobNumberMap)
                                            && jobNumberMap.containsKey(pushMonth.getSendTo())

                            ) {
                                String contentFormat = "### %s年度%s月%s理赔情况  \n  " +
                                        "### 已赔付案件：%s笔  \n  " +
                                        "### 拒赔案件：%s笔  \n  " +
                                        "### 合计赔付金额：%s元  \n " +
                                        "### [赔付明细请点击链接](%s)";

                                String content = String.format(
                                        contentFormat
                                        ,
                                        pushMonth.getMonth().substring(0, 4)
                                        ,
                                        pushMonth.getMonth().substring(5, 7)
                                        ,
                                        Objects.equals(
                                                pushMonth.getSentType(),
                                                "director"
                                        ) ? pushMonth.getRegionName() + "-" + pushMonth.getOrganizationName() : pushMonth.getRegionName()
                                        ,
                                        pushMonth.getPayedNum()
                                        ,
                                        pushMonth.getRejectNum()
                                        ,
                                        StringUtils.isEmpty(pushMonth.getPayMoney()) ? "0" : pushMonth.getPayMoney()
                                        ,
                                        pushMonth.getSendUrl()
                                );

                                String taskId = dingTalkRobotService.pushMarkdown2User(
                                        Collections.singletonList(jobNumberMap.get(pushMonth.getSendTo()))
//                                        "16297740062764115071476331555972"
                                        , content, "理赔每月汇总消息推送");

                                log.info("taskId={}, pushId={}", taskId,  pushMonth);

                                claimPushMonthDao.updateById(pushMonth.getId(), "1");
                            } else {
                                log.warn(pushMonth.getSendTo() + "理赔数据推送消息发送失败");
                            }
                        } catch(Exception e) {
                            claimPushMonthDao.updateById(pushMonth.getId(), "2");
                            log.warn("异常-", e);
                        }
                    }
                }
                , 1
                , 300
        );


    }



    public void sendClaimDataByWeek(String week) throws InterruptedException {

        lockHelper.lockRun(
                week + "week"
                , () -> {
                    List<SmClaimPushMonth> claimPushMonthList = claimPushMonthDao.listByMonth(week, "week");

                    if (CollectionUtils.isEmpty(claimPushMonthList)) {
                        return;
                    }

                    Map<String, String> jobNumberMap =
                            authUserDingTalkService.getJobNumberDingUserIdMap(
                                    claimPushMonthList.stream()
                                            .map(SmClaimPushMonth::getSendTo)
                                            .distinct().collect(Collectors.toList())
                            );

                    for (SmClaimPushMonth pushMonth : claimPushMonthList) {
                        try{
                            if (
                                    Objects.nonNull(jobNumberMap)
                                            && jobNumberMap.containsKey(pushMonth.getSendTo())

                            ) {
                                String contentFormat = "以下是截至上周日未结案的重大案件，及上周拒赔案件；\n " +
                                        "### [点击查看详细清单](%s)";

                                String content = String.format(
                                        contentFormat,
                                        pushMonth.getSendUrl()
                                );

                                String taskId = dingTalkRobotService.pushMarkdown2User(
                                        Collections.singletonList(jobNumberMap.get(pushMonth.getSendTo()))
//                                        "16297740062764115071476331555972"
                                        , content, "重大案件、拒赔案件每周推送");

                                log.info("taskId={}, pushId={}", taskId,  pushMonth);

                                claimPushMonthDao.updateById(pushMonth.getId(), "1");
                            } else {
                                log.warn(pushMonth.getSendTo() + "理赔数据推送消息发送失败");
                            }
                        } catch(Exception e) {
                            claimPushMonthDao.updateById(pushMonth.getId(), "2");
                            log.warn("异常-", e);
                        }
                    }
                }
                , 1
                , 300
        );


    }

}
