package com.cfpamf.ms.insur.operation.activity.dao;

import com.cfpamf.ms.insur.operation.activity.entity.SmAchieveEmpCount;
import com.cfpamf.ms.insur.operation.base.dao.CommonMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/5/12 15:13
 * @Version 1.0
 */
@Mapper
public interface SmAchieveActivityMapper extends CommonMapper<SmAchieveEmpCount> {

    default List<SmAchieveEmpCount> queryByOrganizationName(String organizationName, String regionName) {
        SmAchieveEmpCount condition = new SmAchieveEmpCount();
        condition.setOrganizationName(organizationName);
        condition.setRegionName(regionName);
        return select(condition);
    }

}
