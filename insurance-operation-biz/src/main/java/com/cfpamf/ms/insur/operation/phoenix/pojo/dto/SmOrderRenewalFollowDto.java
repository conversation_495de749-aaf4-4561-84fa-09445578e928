package com.cfpamf.ms.insur.operation.phoenix.pojo.dto;

import com.alibaba.fastjson.JSONObject;
import com.cfpamf.ms.insur.operation.aicall.enums.AIIntentEnums;
import com.cfpamf.ms.insur.operation.auto.enums.EnumNonRenewalReason;
import com.cfpamf.ms.insur.operation.customer.enums.EnumServiceMode;
import com.cfpamf.ms.insur.operation.phoenix.enums.EnumRenewalIntention;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;
import org.apache.commons.lang3.StringUtils;

import javax.validation.constraints.NotBlank;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * 订单续保跟进记录表
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class SmOrderRenewalFollowDto {
    @ApiModelProperty("订单号")
    @NotBlank(message = "订单号不能为空")
    String orderId;

    @ApiModelProperty("表单号")
    @NotBlank(message = "保单号不能为空")
    String policyNo;

    @ApiModelProperty("转保保单号")
    String transferPolicyNo;
    @ApiModelProperty("转保订单号")
    String transferOrderId;
    @ApiModelProperty("产品id")
    private Integer productId;
    @ApiModelProperty("计划id")
    private Integer planId;
    @ApiModelProperty("转保被保人证件号")
    String idNumber;

    @ApiModelProperty("服务方式")
    String serviceMode;

    String serviceModeName;

    public String getServiceModeName() {
        if (StringUtils.isEmpty(serviceMode)){
            return "";
        }
        return EnumServiceMode.dict(serviceMode);
    }

    @ApiModelProperty("意向")
    String intention;

    @ApiModelProperty("机器人意向")
    String intent;

    @ApiModelProperty("机器人意向名称")
    String intentName;

    public String getIntentName() {
        if (!StringUtils.isEmpty(intent)) {
            return AIIntentEnums.dict(intent);
        }
        return "";
    }

    @ApiModelProperty("意向-翻译")
    String intentionName;

    public String getIntentionName() {
        if (EnumServiceMode.LOSE_CONTACT.getCode().equals(serviceMode)){
            return EnumRenewalIntention.LOSE_CONTACT.getDesc();
        }
        if (StringUtils.isEmpty(intention)){
            return EnumRenewalIntention.NO_FOLLOW_UP.getDesc();
        }
        return EnumRenewalIntention.dict(intention);
    }

    @ApiModelProperty("不续原因")
    String reason;

    String reasonName;

    String reasonRemark;

    @ApiModelProperty("考虑原因")
    String considerReason;
    @ApiModelProperty("考虑原因-其他说明")
    String considerRemark;

    public String getReasonName() {
        if (StringUtils.isEmpty(reason)){
            return "";
        }
        return EnumNonRenewalReason.dict(reason);
    }

    @ApiModelProperty("备注")
    String remark;

    @ApiModelProperty("下次跟进方式")
    String nextMethod;


    String nextMethodMethodName;
    public String getNextMethodName() {
        if (StringUtils.isEmpty(nextMethod)){
            return "";
        }
        return EnumServiceMode.dict(nextMethod);
    }


    @ApiModelProperty("订单号")
    LocalDateTime nextTime;

    @ApiModelProperty("是否最新记录")
    Integer newest;

    @ApiModelProperty("状态")
    Integer enabledFlag;

    @ApiModelProperty("跟进人工号")
    @NotBlank(message = "跟进人工号不能为空")
    String followPeople;
    
    @ApiModelProperty("跟进人姓名")
    String followPeopleName;

    String recordJson;



    @ApiModelProperty(name="语音地址")
    String voiceAddress;

    @ApiModelProperty(name="通话时长")
    Integer talkingTimeLen;

    @ApiModelProperty("跟进时间")
    LocalDateTime followTime;
}
