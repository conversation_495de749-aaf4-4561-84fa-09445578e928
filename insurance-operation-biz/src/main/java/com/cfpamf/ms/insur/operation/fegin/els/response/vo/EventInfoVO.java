package com.cfpamf.ms.insur.operation.fegin.els.response.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date
 */
@Data
public class EventInfoVO {

    @ApiModelProperty(value = "", required = true)
    private Integer id;

    @ApiModelProperty(value = "活动名称", required = true)
    private String name;

    @ApiModelProperty("活动类型 1-年会 2-悦悦下分支")
    private String eventType;

    @ApiModelProperty(value = "活动开始日期", required = true)
    private java.time.LocalDate startTime;

    @ApiModelProperty(value = "活动结束日期", required = true)
    private java.time.LocalDate endTime;

    @ApiModelProperty(value = "活动举办范围标志 1.全公司 2.其他", required = true)
    private Integer rangeType;

    @ApiModelProperty(value = "活动举办部门，多个部门用逗号分隔")
    private String rangeDept;

    @ApiModelProperty(value = "活动举办部门名称")
    private String rangeDeptName;


    @ApiModelProperty(value = "抽奖规则 0.不抽奖1.奖券 2.人头")
    private Integer giftRule;

    @ApiModelProperty(value = "是否购买发放(0 否，1 是)")
    private Integer isBuy;


    @ApiModelProperty(value = "限制人数")
    private Integer personCount;


    @ApiModelProperty(value = "奖票单价")
    private java.math.BigDecimal price;

    @ApiModelProperty(value = "是否自动赠送")
    private Integer isAutoGift;

    @ApiModelProperty(value = "赠送规则备注")
    private String giftRulesRemark;


    @ApiModelProperty(value = "报名部门，多个部门用逗号分隔")
    private String signDept;


    @ApiModelProperty(value = "报名部门名称")
    private String signDeptName;


    @ApiModelProperty(value = "报名开始时间")
    private java.time.LocalDate signStartTime;


    @ApiModelProperty(value = "报名截止时间")
    private java.time.LocalDate signEndTime;


    @ApiModelProperty(value = "状态 1.草稿 2.已发布（发布之后不能修改） 3.进行中 44.已下架", required = true)
    private Integer status;

    @ApiModelProperty(value = "活动图片配置")
    private String eventUrl;

    @ApiModelProperty("活动详情")
    private String eventDetail;


    @ApiModelProperty("排序")
    private Integer sort;

    @ApiModelProperty("奖项")
    private List<EventGiftVO> eventGiftVOList;

}
