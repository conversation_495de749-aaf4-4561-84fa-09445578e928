package com.cfpamf.ms.insur.operation.activity.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2021/7/6 16:33
 */
@ApiModel("活动产品规则视图对象")
@Getter
@Setter
public class SystemActivityProductRuleVo {
    /**
     * id
     */
    @ApiModelProperty(value = "id")
    private Long id;

    /**
     * 系统活动产品id
     */
    @ApiModelProperty(value = "系统活动产品id")
    private Long systemActivityProductId;
    /**
     * 规则码
     */
    @ApiModelProperty(value = "规则码")
    private String ruleCode;

    /**
     * 比较符
     */
    @ApiModelProperty(value = "比较符 GREATER_THAN_OR_EQUAL-大于等于 LESS_THAN_OR_EQUAL-小于等于 IMMEDIATELY_PRESENT 立刻赠送")
    private String compareSymbol;

    /**
     * 参数
     */
    @ApiModelProperty(value = "参数")
    private String params;
}
