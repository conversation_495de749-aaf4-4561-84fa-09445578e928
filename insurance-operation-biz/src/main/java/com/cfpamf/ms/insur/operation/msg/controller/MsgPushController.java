package com.cfpamf.ms.insur.operation.msg.controller;

import com.cfpamf.ms.bms.facade.vo.PostVO;
import com.cfpamf.ms.insur.operation.base.annotaions.ResponseDecorated;
import com.cfpamf.ms.insur.operation.base.util.HttpRequestUtil;
import com.cfpamf.ms.insur.operation.base.util.ValidatorUtils;
import com.cfpamf.ms.insur.operation.fegin.bms.facede.BmsFacade;
import com.cfpamf.ms.insur.operation.msg.pojo.dto.*;
import com.cfpamf.ms.insur.operation.msg.pojo.po.OpMessageRule;
import com.cfpamf.ms.insur.operation.msg.pojo.po.OpMessageRuleReceiver;
import com.cfpamf.ms.insur.operation.msg.pojo.query.BmsPostPageQuery;
import com.cfpamf.ms.insur.operation.msg.pojo.query.CustomColumnPageQuery;
import com.cfpamf.ms.insur.operation.msg.pojo.query.OpMessageRuleQuery;
import com.cfpamf.ms.insur.operation.msg.service.CustomColumnService;
import com.cfpamf.ms.insur.operation.msg.service.EmpPostService;
import com.cfpamf.ms.insur.operation.msg.service.MsgPushService;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> 2021/7/15 20:13
 */
@RestController
@RequestMapping("msg")
@Api("营销工具-消息推送")
@ResponseDecorated
public class MsgPushController {

    @Autowired
    MsgPushService pushService;

    @Autowired
    EmpPostService postService;
    @Autowired
    CustomColumnService customColumnService;

    @PostMapping("/config")
    @ApiModelProperty("配置规则参数")
    public void saveConfig(@RequestBody OpMessageRuleDTO rule) {
        ValidatorUtils.validateParam(rule);
        pushService.saveConfig(rule, HttpRequestUtil.getUserId());
    }

    @GetMapping("/config")
    @ApiModelProperty("查询")
    public PageInfo<OpMessageRule> listConfig(OpMessageRuleQuery query) {
        return pushService.listConfig(query);
    }


    @GetMapping("/config/{id}")
    @ApiModelProperty("查询详情")
    public OpMessageRuleDTO detail(@PathVariable("id") Long id) {
        return pushService.detail(id);
    }

    @PutMapping("/config")
    @ApiModelProperty("配置规则参数")
    public void updateConfig(@RequestBody OpMessageRule rule) {
        pushService.updateConfig(rule, HttpRequestUtil.getUserId());
    }

    @PostMapping("/config/{ruleId}/receiver")
    @ApiOperation("保存消息接受者")
    public void saveReceivers(@PathVariable("ruleId") Long ruleId,
                              @RequestBody List<OpMessageRuleReceiver> receivers) {
        pushService.saveReceivers(ruleId, receivers);
    }

    @ApiOperation("强制推送一次消息")
    @PostMapping("/push/{ruleId}")
    public void pushMessage(@PathVariable("ruleId") Long ruleId,
                            @RequestParam("autoNext") boolean autoNext) throws IOException {
        pushService.pushMessage(ruleId, autoNext);
    }

    @ApiOperation("计算下一次")
    @PostMapping("/push/initNextPush")
    public void ruleId(@RequestParam("ruleId") Long ruleId) throws IOException {
        pushService.initNextPush(ruleId);
    }
    @ApiOperation("计算下一次")
    @PostMapping("/push/initNextPushForce")
    public void nextForce(@RequestParam("ruleId") Long ruleId) throws IOException {
        pushService.initNextPush(ruleId,true);
    }

    @ApiOperation("清楚脚本缓存")
    @PostMapping("/clear/groovy")
    public void clearGroovyCache(@RequestParam("code") String code) throws IOException {
        pushService.clearCache(code);
    }

    @ApiOperation(value = "调试脚本", notes = "传入脚本以及规则编码 返回对应值")
    @PostMapping("/debug/script")
    public String debugScript(String script, Long ruleId) {
        return pushService.debugGroovy(script, ruleId);
    }

    @ApiOperation(value = "调试模板 ", notes = "传入模板内容以及参数 返回对应处理过的html")
    @PostMapping("/debug/freemarker")
    public String debugFreemarker(@RequestBody MsfDebugFtlDTO ftlDTO) throws Exception {
        return pushService.debugTemp(ftlDTO.getFtlContent(), ftlDTO.getParams());
    }

    @Resource
    private BmsFacade bmsFacade;

    @PostMapping("/page/bmsPost")
    @ApiOperation(value = "获取岗位信息分页列表", notes = "传入时间区间，获取对应的值")
    public PageInfo<PostVO> findPostTimeWindow(@RequestBody @Valid BmsPostPageQuery query) {
        return bmsFacade.findPostTimeWindow(query.from()).getData();
    }

    @ApiModelProperty("同步岗位信息")
    @PostMapping("/sync/emp")
    public int syncPostInfo(@RequestParam String postName) {
        return postService.syncPostToday(postName);

    }

    @ApiOperation(value = "根据groovy编码模版文件列表", notes = "根据grooy脚本的编号，获取对应的模版列表")
    @PostMapping("/query/groovyCode")
    public List<OpMessageRule> queryByGroovyCode(String groovyCode) {
        return pushService.queryByGroovyCode(groovyCode);
    }

    @PostMapping("/template/preview")
    @ApiModelProperty("获取模板图片预览地址")
    public Map<String, String> previewUrl(@RequestBody OpMessageRuleGroovyDTO ruleGroovyDTO) {
        HashMap<String, String> result = new HashMap<>(1);
        result.put("url", pushService.retainTemplatePreviewUrl(ruleGroovyDTO));
        return result;
    }

    @PostMapping("/audit/push")
    @ApiModelProperty("推送审核消息")
    public void auditPush(@RequestBody List<Long> pushIds,
                          @RequestParam(value = "nextAuto") boolean nextAuto) {
        pushService.pushAuditMessage(pushIds, nextAuto);
    }

    @PostMapping("/page/customColumn")
    @ApiModelProperty("获取自定义列分页列表")
    public OrgCustomColumnWrapperDTO getColumnList(@RequestBody CustomColumnPageQuery query){
        return customColumnService.pageQueryCustom(query);
    }


    @ApiOperation("清楚脚本缓存")
    @PostMapping("/clear/groovy/type")
    public void clearGroovyCache(@RequestParam("type") String type, @RequestParam("code") String code) {
        pushService.clearCacheByType(type, code);
    }

    @ApiOperation("修正消息的状态(消息推送失败是，数据状态会挂起，阻塞任务)")
    @GetMapping("/init-message")
    public void initMessage(@RequestParam("id") Integer id) {
        pushService.initMessage(id);
    }
}
