package com.cfpamf.ms.insur.operation.aicall.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Map;

@Data
public class AiCallTaskDetail {

    /** 外部关联业务主键 */
    @ApiModelProperty(name = "外部关联业务主键",notes = "")
    private Integer outSourceId ;

    /** 调用方的业务随路数据, 字符串，百度侧原文回传 */
    @ApiModelProperty(name = "调用方的业务随路数据, 字符串，百度侧原文回传",notes = "")
    private String extJson ;
    /** 被叫号码 */
    @ApiModelProperty(name = "被叫号码",notes = "")
    private String mobile ;
    /** 名单的变量var map，名单外呼时需要用到的变量 */
    @ApiModelProperty(name = "导入电话变量",notes = "")
    private Map<String, String> varMap ;


}
