package com.cfpamf.ms.insur.operation.base.annotaions;

import java.lang.annotation.*;

/**
 * <AUTHOR> 2020/4/8 10:31
 */
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface Dictionary {

    /**
     * 字典key channel
     *
     * @return
     */
    String value() default "";

    /**
     * 编码字段名
     *
     * @return
     */
    String codeKey() default "code";

    /**
     * 描述的字段名
     *
     * @return
     */
    String descKey() default "desc";

    /**
     * 字典描述
     *
     * @return
     */
    String desc() default "";
}
