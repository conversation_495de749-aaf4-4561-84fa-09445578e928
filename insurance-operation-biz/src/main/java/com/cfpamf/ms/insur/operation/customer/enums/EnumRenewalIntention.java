package com.cfpamf.ms.insur.operation.customer.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @Date 2023/04/19 15:37
 * @Version 1.0
 */
@AllArgsConstructor
@Getter
public enum EnumRenewalIntention {

    /**
     * 意向 high:高意向,median:中意向, low:低意向,none:无意向,lose_contact:联系不上
     */
    //断保跟进
    HIGH("high","高意向"),
    MEDIAN("median","中意向"),
    LOW("low","低意向"),
    LOSE_CONTACT("lose_contact","联系不上"),
    NONE("none","无意向"),
    NO_FOLLOW_UP("noFollowUp","未跟进"),
    RENEWED("renewed","已激活"),

    //外呼跟进
    TAG_H("TAG_H","高意向"),
    TAG_M("TAG_M","中意向"),
    TAG_L("TAG_L","低意向"),
    TAG_N("TAG_N","无意向"),

    //A类客户转化
    CUST("CUST","已转化");

    String code;
    String desc;
    public static String dict(String code) {
        if(Objects.isNull(code)){
            return "";
        }
        return Stream.of(values())
                .filter(em -> em.getCode().equals(code))
                .findFirst().map(EnumRenewalIntention::getDesc)
                .orElse("");
    }
}
