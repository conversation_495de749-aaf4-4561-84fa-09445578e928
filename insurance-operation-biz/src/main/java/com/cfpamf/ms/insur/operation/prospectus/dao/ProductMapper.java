package com.cfpamf.ms.insur.operation.prospectus.dao;

import com.cfpamf.ms.insur.operation.activity.vo.RecommendedProductVo;
import com.cfpamf.ms.insur.operation.base.dao.CommonMapper;
import com.cfpamf.ms.insur.operation.prospectus.entity.SmProduct;
import com.cfpamf.ms.insur.operation.prospectus.vo.ProductAttrVo;
import com.cfpamf.ms.insur.operation.prospectus.vo.ProductClauseVo;
import com.cfpamf.ms.insur.operation.prospectus.vo.ProspectusProductVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/5/18 20:43
 */
@Mapper
public interface ProductMapper extends CommonMapper<SmProduct> {


    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    SmProduct getById(@Param("id") Long id);

    /**
     * 通过id集合获取产品集合
     *
     * @param idList
     * @return
     */
    List<SmProduct> getByIdList(@Param("idList") List<Long> idList);

    /**
     * 查询产品销售区域列表
     *
     * @param orgName
     * @return
     */
    List<Long> listProductSalesOrgByOrgPath(@Param("orgName") String orgName);

    /**
     * 通过id获取产品信息
     *
     * @param id
     * @return
     */
    ProspectusProductVo getProspectusProductById(@Param("id") Long id);

    /**
     * 查询产品保险条款列表
     *
    * @param productId
     * @return
     */
    List<ProductClauseVo> getProductClausesByProductId(@Param("id")Long productId);

    /**
     * 通过产品id获取产品扩展属性
     * @param productId
     * @return
     */
    List<ProductAttrVo> getProductAttrVoByProductId(@Param("productId")Long productId);


    /**
     * 通过id集合获取推荐产品集合
     *
     * @param idList
     * @return
     */
    List<RecommendedProductVo> getRecommendedProductByIdList(@Param("idList") List<Long> idList);

    /**
     * 查询微信最产品列表(包含团险)
     * @param salesProductIds
     * @return
     */
    List<RecommendedProductVo> listWxProductsWithGroup(@Param("salesProductIds") List<Long> salesProductIds);
}
