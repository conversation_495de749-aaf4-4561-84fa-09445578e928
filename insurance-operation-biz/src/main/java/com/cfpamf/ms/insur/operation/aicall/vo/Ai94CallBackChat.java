package com.cfpamf.ms.insur.operation.aicall.vo;

import io.swagger.annotations.ApiModelProperty;

public class Ai94CallBackChat {
    @ApiModelProperty(value = "说话号码", required = true)
    private String fromNumber;

    @ApiModelProperty(value = "说话内容", required = true)
    private String content;

    @ApiModelProperty(value = "说话时间", required = true)
    private String createTime;

    @ApiModelProperty(value = "节点名称/知识库名称", required = false)
    private String chatName;
}
