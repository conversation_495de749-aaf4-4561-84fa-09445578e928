package com.cfpamf.ms.insur.operation.whale.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @author: yang<PERSON><PERSON>
 * @create: 2024-03-04 14:07
 * @description: 渠道推荐人变更保全项
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ChannelRecommenderChangeItemVo {
    /**
     * 变更对应的批单号
     */
    private String endorsementNo;
    /**
     * 当前变跟选中的被保人人信息
     */
    private List<ChannelRecommenderChangeItemDetailVo> itemDetailVos;
}
