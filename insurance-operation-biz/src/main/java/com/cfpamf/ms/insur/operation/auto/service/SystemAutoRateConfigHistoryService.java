package com.cfpamf.ms.insur.operation.auto.service;

import com.alibaba.fastjson.JSONObject;
import com.cfpamf.ms.insur.operation.auto.dao.*;
import com.cfpamf.ms.insur.operation.auto.dto.SystemAutoRateConfigDetailDto;
import com.cfpamf.ms.insur.operation.auto.dto.SystemAutoRateConfigDto;
import com.cfpamf.ms.insur.operation.auto.dto.SystemAutoRateConfigFileDto;
import com.cfpamf.ms.insur.operation.auto.dto.SystemAutoRateConfigListDto;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class SystemAutoRateConfigHistoryService {

    SystemAutoRateConfigHistoryMapper systemAutoRateConfigHistoryMapper;

    SystemAutoRateConfigDetailHistoryMapper systemAutoRateConfigDetailHistoryMapper;

    /**
     * 根据产品ID获取历史版本
     * @param productId 产品主键
     * @return SystemAutoRateConfigListDto
     */
    public List<SystemAutoRateConfigListDto> getConfigVersionByProductId(Integer productId) {
        return systemAutoRateConfigHistoryMapper.selectByProductId(productId);
    }

    /**
     * 获取历史版本详情
     * @param productId 产品主键
     * @param version 版本号
     * @return SystemAutoRateConfigDetailDto
     */
    public SystemAutoRateConfigDto getHistoryDetail(Integer productId, Integer version) {
        SystemAutoRateConfigDto dto = systemAutoRateConfigHistoryMapper.selectByProductIdAndVersion(productId,version);

        if (Objects.nonNull(dto)) {
            List<SystemAutoRateConfigDetailDto> detailDtos = systemAutoRateConfigDetailHistoryMapper.selectByHistoryConfigId(dto.getId());
            for (SystemAutoRateConfigDetailDto detailDto : detailDtos) {
                if (!StringUtils.isEmpty(detailDto.getPromotionPolicy())){
                    detailDto.setFileDtoList(JSONObject.parseArray(detailDto.getPromotionPolicy(), SystemAutoRateConfigFileDto.class));
                }
            }
            dto.setDetailDtos(detailDtos);
        }
        return dto;
    }
}
