package com.cfpamf.ms.insur.operation.dingtalk.entity;

import com.cfpamf.ms.insur.operation.base.entity.BaseNoUserEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

/**
 * <AUTHOR> 2022/8/31 10:56
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class DingTalkSwForm extends BaseNoUserEntity {


    @ApiModelProperty("表单id")
    String formCode;
    @ApiModelProperty("表单名称")
    String formName;
    @ApiModelProperty("表单备注")
    String formMemo;
    @ApiModelProperty("表单设置")
    String formSetting;
    @ApiModelProperty("")
    String formCreator;
}
