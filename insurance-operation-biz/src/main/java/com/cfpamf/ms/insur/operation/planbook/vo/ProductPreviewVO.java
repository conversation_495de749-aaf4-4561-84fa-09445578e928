package com.cfpamf.ms.insur.operation.planbook.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/12/13 15:45
 * @Version 1.0
 */
@Data
public class ProductPreviewVO {

    @ApiModelProperty("产品封面背景图url")
    private String headImageUrl;

    @ApiModelProperty("视频封面背景图url")
    private String videoCoverImageUrl;

    @ApiModelProperty("视频链接url")
    private String videoUrl;

    @ApiModelProperty("推荐理由，从计划书配置中获取")
    private String recommendReason;

    @ApiModelProperty("产品条款")
    private List<ProductClauseVO> clauses;

    @ApiModelProperty("客户经理姓名")
    private String customerManagerName;

    @ApiModelProperty("客户经理手机号")
    private String customerManagerPhoneNumber;

    @ApiModelProperty("客户经理工号")
    private String userId;

    @ApiModelProperty("客户经理头像url")
    private String customerWXHeadUrl;

    @ApiModelProperty("产品id")
    private Integer productId;

    @ApiModelProperty("对接方式 1-API 2-H5")
    private Integer apiType;

    @ApiModelProperty("对接渠道")
    private String channel;

    @ApiModelProperty("h5对接地址")
    private String h5Url;

    @ApiModelProperty("产品属性：团险，个险")
    private String productAttrCode;

    @ApiModelProperty("创建类型 个险短险-PERSON_SHORT_INSURANCE  个险长险-PERSON_LONG_INSURANCE 团险-GROUP_INSURANCE 车险CAR_INSURANCE")
    private String createType;

    @ApiModelProperty("产品名称")
    private String productName;

    @ApiModelProperty("产品简称")
    private String productShortName;


}
