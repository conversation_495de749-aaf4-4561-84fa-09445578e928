package com.cfpamf.ms.insur.operation.customer.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @Date 2023/04/19 15:37
 * @Version 1.0
 */
@AllArgsConstructor
@Getter
public enum EnumRenewalFollowIntention {

    /**
     * 结果：意向 willing:愿意续保,consider:考虑, unwilling:不愿续保,loseContact:联系不上
     */
    WILLING("willing","愿意续保"),
    CONSIDER("consider","考虑"),
    RENEWED("renewed","已续保"),
    TRANSFER("transfer","考虑"),
    UNWILLING("unwilling","不愿续保"),
    LOSE_CONTACT("loseContact","联系不上");

    String code;
    String desc;
    public static String dict(String code) {
        if(Objects.isNull(code)){
            return "";
        }
        return Stream.of(values())
                .filter(em -> em.getCode().equals(code))
                .findFirst().map(EnumRenewalFollowIntention::getDesc)
                .orElse("");
    }
}
