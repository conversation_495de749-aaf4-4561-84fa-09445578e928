
package com.cfpamf.ms.insur.operation.msg.pojo.dto;

import com.cfpamf.ms.insur.operation.msg.pojo.po.OpMessageRuleReceiver;
import lombok.AccessLevel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldDefaults;

import java.util.Map;

/**
 * Created by zhengjing  on 2021-07-15 18:19:44
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@FieldDefaults(level = AccessLevel.PRIVATE)
public class OpMessageRuleReceiverGroovyDTO extends OpMessageRuleReceiver {

    Map<String, Object> otherParams;

}

