package com.cfpamf.ms.insur.operation.customer.controller;

import com.cfpamf.ms.insur.operation.base.annotaions.MaskMethod;
import com.cfpamf.ms.insur.operation.base.annotaions.ResponseDecorated;
import com.cfpamf.ms.insur.operation.base.bean.SmyPageInfo;
import com.cfpamf.ms.insur.operation.base.constant.BaseConstants;
import com.cfpamf.ms.insur.operation.base.util.ValidatorUtils;
import com.cfpamf.ms.insur.operation.customer.dto.*;
import com.cfpamf.ms.insur.operation.customer.po.CustomerLoanFirstFollowPo;
import com.cfpamf.ms.insur.operation.customer.po.CustomerLoanFirstPo;
import com.cfpamf.ms.insur.operation.customer.po.CustomerLoanFollowPo;
import com.cfpamf.ms.insur.operation.customer.query.CustomerConversionQuery;
import com.cfpamf.ms.insur.operation.customer.query.CustomerFollowCntQuery;
import com.cfpamf.ms.insur.operation.customer.query.CustomerPolicyQuery;
import com.cfpamf.ms.insur.operation.customer.query.WxInterruptionCustomerQuery;
import com.cfpamf.ms.insur.operation.customer.service.*;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.*;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;


/**
 * 断保客户管理
 *
 * <AUTHOR>
 * @date 2023/4/19 16:04
 */
@Slf4j
@Api(value = "客户管理", tags = {"客户管理"})
@RequestMapping(value = {BaseConstants.ADMIN_VERSION + "/customer"})
@ResponseDecorated
@RestController
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class CustomerController {

    CustomerInterruptionFollowService customerInterruptionFollowService;

    CustomerService customerService;

    CustomerRenewService customerRenewService;

    CustomerInterruptionService customerInterruptionService;

    CustomerLoanService loanService;

    CustomerLoanFirstService customerLoanFirstService;

    CustomerCustFirstService customerCustFirstService;

    @PostMapping("/interruption/saveFollowRecord")
    @ApiOperation("添加跟进记录")
    public void saveRenewalFollowRecord(@RequestBody CustomerInterruptionFollowDto dto) {
        // 1 基础校验
        ValidatorUtils.validateParam(dto);
        customerInterruptionFollowService.saveRenewalFollowRecord(dto);
    }

    @ApiModelProperty("断保客户的产品类型纬度的投保人信息 key-是产品类型")
    @GetMapping("/interruption/applicantInterruptionMap")
    public Map<String, CustomerPolicySimpleDto> getApplicantInterruptionMap(@RequestParam("customerId") Long customerId) {
        return customerInterruptionService.getCustomerInterruption(customerId);
    }


    @ApiModelProperty("发送短信")
    @PostMapping("/interruption/sendSms2Applicant")
    public void sendSms2Applicant(@RequestBody CustomerInterruptionSmsDto dto) {
        customerInterruptionService.sendSms2Applicant(dto);
    }

    @ApiModelProperty("不同产品发送不同短信")
    @ApiOperation(value = "不同产品发送不同短信", tags = "CORE")
    @PostMapping("/interruption/sendSms3Applicant")
    public void sendSms3Applicant(@RequestBody CustomerInterruptionSmsDto dto) {
        customerInterruptionService.sendSms3Applicant(dto);
    }

    /**
     * 查询客户跟进记录
     *
     * @param customerId
     * @return
     */
    @ApiOperation(value = "获取客户跟进记录")
    @GetMapping("/interruption/followList/{customerId:\\d+}")
    public List<CustomerInterruptionFollowDto> getFollowListByCustomer(@PathVariable String customerId) {
        return customerInterruptionFollowService.selectByCustomer(customerId);
    }

    /**
     * 查询客户跟进记录
     *
     * @param customerId
     * @return
     */
    @ApiOperation(value = "获取客户跟进记录(包括AI跟进)")
    @GetMapping("/interruption/followListIncludeAI/{customerId:\\d+}")
    public List<CustomerFollowDto> getFollowListByCustomerIncludeAI(@PathVariable String customerId) {
        return customerService.selectFollowByCustomer(customerId);
    }

    /**
     * 获取A类客户详情
     *
     * @param loanCustId 信贷客户编码
     * @return CustomerLoanFirstPo
     */
    @ApiOperation(value = "获取A类客户详情")
    @GetMapping("/loanFirst/detail")
    public CustomerLoanFirstPo getLoanFirstCustomerDetail(@RequestParam String loanCustId) {
        return customerLoanFirstService.getLoanFirstCustomerDetail(loanCustId);
    }

    /**
     * 获取A类客户跟进记录
     *
     * @param loanCustId 信贷客户编码
     * @return List<CustomerLoanFirstFollowPo>
     */
    @ApiOperation(value = "获取A类客户跟进记录")
    @GetMapping("/loanFirst/follow")
    public List<CustomerFollowDto> getLoanFirstFollow(@RequestParam String loanCustId) {
        return customerLoanFirstService.getLoanFirstFollowByLoanCustId(loanCustId);
    }

    /**
     * 查询用户我的断保客户
     *
     * @param query
     * @return
     */
    @ApiOperation(value = "查询用户我的断保客户")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "openId", value = "微信openId", required = false, dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "authorization", value = "授权token", required = true, dataType = "string", paramType = "query")
    })
    @GetMapping("/interruptionList")
    public PageInfo<WxInterruptionCustomerDto> getWxBreakCustomerListByPage(WxInterruptionCustomerQuery query) {
        return customerService.getBreakCustomerListByPage(query);
    }

    /**
     * 查询用户我的断保客户(后台)
     *
     * @param query
     * @return
     */
    @ApiOperation(value = "查询用户我的断保客户（后台）")
    @PostMapping("/back/interruptionList")
    @MaskMethod
    public PageInfo<WxInterruptionCustomerDto> getBreakCustomerListByPage(@RequestBody WxInterruptionCustomerQuery query) {
        return customerService.getBackCustomerListByPage(query);
    }

    /**
     * 导出用户我的断保客户(后台)
     *
     * @param query
     * @return
     */
    @ApiOperation(value = "导出用户我的断保客户（后台）")
    @GetMapping("/back/interruptionList/download")
    @MaskMethod
    public void downloadCustomerList(WxInterruptionCustomerQuery query, HttpServletResponse response) {
        customerService.downloadCustomerList(query, response);
    }

    /**
     * 查询用户历史保单
     *
     * @param query
     * @return
     */
    @ApiOperation(value = "查询用户历史保单")
    @GetMapping("/policy")
    public SmyPageInfo<CustomerPolicyDto, WxCmsSmyDto> getPolicyHistoryList(CustomerPolicyQuery query) {
        query.setQueryHistory(Boolean.TRUE);
        return customerService.getPolicyNoAll(query);
    }


    /**
     * 查询用户历史保单
     *
     * @param query
     * @return
     */
    @ApiOperation(value = "查询用户历史保单-日复盘用")
    @GetMapping("/policy/retrospective")
    public SmyPageInfo<CustomerPolicyDto, WxCmsSmyDto> getPolicyHistoryListRetrospective(CustomerPolicyQuery query) {
        query.setQueryHistory(Boolean.TRUE);
        return customerService.getPolicyNoAllRetrospective(query);
    }

    /**
     * 在保险业务助手中查询用户历史保单
     *
     * @param query
     * @return
     */
    @ApiOperation(value = "在保险业务助手中查询用户历史保单")
    @GetMapping("/biz/helper/policy")
    public SmyPageInfo<CustomerPolicyDto, WxCmsSmyDto> getPolicyHistoryList(CustomerPolicyQuery query, @RequestHeader("authorization") String authorization) {
        query.setQueryHistory(Boolean.TRUE);
        return customerService.getPolicyNoAllWithoutWx(query);
    }

    /**
     * 查询用户我的断保客户
     *
     * @return
     */
    @ApiOperation(value = "断保客户数据汇总")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "openId", value = "微信openId", required = false, dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "authorization", value = "授权token", required = true, dataType = "string", paramType = "query")
    })
    @GetMapping("/dataSummarization")
    @MaskMethod
    public WxCmsSmyDto dataSummarization(@RequestParam(required = false) String openId, @RequestParam(required = true) String authorization) {
        return customerService.dataSummarization(openId, authorization);
    }

    /**
     * 激活客户
     *
     * @param message
     * @return
     */
    @ApiOperation(value = "断保客户激活")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "openId", value = "微信openId", required = true, dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "authorization", value = "授权token", required = true, dataType = "string", paramType = "query")
    })
    @GetMapping("/renewCustomer")
    @MaskMethod
    public void renewCustomer(PolicyInfoNotifyMessage message) {
        customerRenewService.handleMessage(message);
    }

    @PostMapping("/loan/saveLoanFollowRecord")
    @ApiOperation("异业转化添加跟进记录")
    public void saveLoanFollowRecord(@RequestBody CustomerLoanFollowPo dto) {
        // 1 基础校验
        ValidatorUtils.validateParam(dto);
        customerInterruptionFollowService.saveLoanFollowRecord(dto);
    }

    @PostMapping("/loan/saveLoanAFollowRecord")
    @ApiOperation("A类转化添加跟进记录")
    public void saveLoanFirstFollowRecord(@RequestBody CustomerLoanFirstFollowPo dto) {
        // 1 基础校验
        ValidatorUtils.validateParam(dto);
        customerService.saveLoanFirstFollowRecord(dto);
    }

    @GetMapping("/loan/getLoanCustomerRemark")
    @ApiOperation("获得异业转化客户标签")
    public Integer getLoanCustomerRemark(@RequestParam String customerId) {
        return customerRenewService.getLoanCustomerRemark(customerId);
    }

    @ApiModelProperty("查询订单")
    @GetMapping("/validOrder")
    public CustomerLoanValidOrderDTO validOrder(String idNumber) {
        return loanService.validOrder(idNumber);
    }

    @GetMapping("/loanFirst/cust")
    @ApiOperation("A类客户转化")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "openId", value = "微信openId", required = true, dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "authorization", value = "授权token", required = true, dataType = "string", paramType = "query")
    })
    public void loanFirstCustomerCust(PolicyInfoNotifyMessage message) {
        customerCustFirstService.handleMessage(message);
    }



    @PostMapping("/getCustomerFollowCnt")
    @ApiOperation("获取客户跟进数")
    public List<CustomerFollowCntDto> getCustomerFollowCnt(@RequestBody CustomerFollowCntQuery query) {
        return customerService.getCustomerFollowCnt(query);
    }

    @PostMapping("/getCustomerConversion")
    @ApiOperation("获取客户业绩指标")
    public List<CustomerConversionDto> getCustomerConversion(@RequestBody CustomerConversionQuery query) {
        return customerService.getCustomerConversion(query);
    }

    @PostMapping("/getPolicyListForConversionAmt")
    @ApiOperation("获取客户保单标准保费")
    public List<PolicyConversionAmtDto> getPolicyListForConversionAmt(@RequestBody CustomerConversionQuery query) {
        return customerService.getPolicyListForConversionAmt(query.getEmpCodes(),query.getStartDate(),query.getEndDate());
    }
}
