package com.cfpamf.ms.insur.operation.auto.dao;

import com.cfpamf.ms.insur.operation.auto.dto.SystemAutoRateConfigDto;
import com.cfpamf.ms.insur.operation.auto.dto.SystemAutoRateConfigListDto;
import com.cfpamf.ms.insur.operation.auto.po.SystemAutoRateConfigHistoryPo;
import com.cfpamf.ms.insur.operation.auto.po.SystemAutoRateConfigPo;
import com.cfpamf.ms.insur.operation.base.dao.CommonMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface SystemAutoRateConfigHistoryMapper extends CommonMapper<SystemAutoRateConfigHistoryPo> {
    @Select("select ifNull(max(version),0) max_version from system_auto_rate_config_history where product_id = #{productId}")
    Integer getVersion(Integer productId);

    /**
     * 根据产品Id查询历史版本
     * @param productId 产品id
     * @return SystemAutoRateConfigListDto
     */
    List<SystemAutoRateConfigListDto> selectByProductId(@Param("productId") Integer productId);
    /**
     * 根据产品Id查询历史版本
     * @param productId 产品id
     * @return SystemAutoRateConfigListDto
     */
    SystemAutoRateConfigDto selectByProductIdAndVersion(@Param("productId") Integer productId, @Param("version") Integer version);
}
