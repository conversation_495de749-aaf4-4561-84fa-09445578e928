package com.cfpamf.ms.insur.operation.activity.service.impl;

import com.cfpamf.ms.insur.operation.activity.dao.WhaleActivityRewardMapper;
import com.cfpamf.ms.insur.operation.activity.dao.SmCommissionDetailMapper;
import com.cfpamf.ms.insur.operation.activity.dto.ActivityConstRuleParam;
import com.cfpamf.ms.insur.operation.activity.dto.SimpleCommissionDetailOrderDTO;
import com.cfpamf.ms.insur.operation.activity.entity.WhaleActivityReward;
import com.cfpamf.ms.insur.operation.activity.enums.*;
import com.cfpamf.ms.insur.operation.activity.form.SystemActivityConstRuleForm;
import com.cfpamf.ms.insur.operation.activity.form.SystemActivitySearchForm;
import com.cfpamf.ms.insur.operation.activity.service.SmActivityRewardConstService;
import com.cfpamf.ms.insur.operation.activity.service.SystemActivityProgrammeService;
import com.cfpamf.ms.insur.operation.activity.vo.SystemActivityProgrammeVo;
import com.cfpamf.ms.insur.operation.addCommission.dao.WhaleAddCommissionDetailItemMapper;
import com.cfpamf.ms.insur.operation.base.service.SystemGroovyService;
import com.cfpamf.ms.insur.operation.base.util.LambdaUtils;
import com.cfpamf.ms.insur.operation.reward.service.impl.AddCommissionRewardConstWhaleServiceImpl;
import lombok.AccessLevel;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 手动配置的活动奖励类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@FieldDefaults(level = AccessLevel.PRIVATE)
public class WhaleActivityRewardConstServiceImpl implements SmActivityRewardConstService {

    @Autowired
    WhaleActivityRewardMapper smActivityRewardWhaleMapper;
    @Autowired
    SystemGroovyService systemGroovyService;
    @Autowired
    @Lazy
    SystemActivityProgrammeService activityProgrammeService;
    @Autowired
    SmCommissionDetailMapper detailMapper;
    @Autowired
    AddCommissionRewardConstWhaleServiceImpl rewardService;

    @Autowired
    private WhaleAddCommissionDetailItemMapper whaleAddCommissionDetailItemMapper;

    public void handleAllConstActivity() {
        // 因为是T+1的模式所以查的时候默认用昨天的这个时刻去判断活动是否有效
        SystemActivitySearchForm searchForm = new SystemActivitySearchForm();
        searchForm.setActiveState(EnumActivityState.IN_ACTIVITY.name());
        searchForm.setRelativeTime(LocalDateTime.now().minusDays(1));
        searchForm.setConfigType(EnumActivityConfigType.CONST.name());
        searchForm.setActivityPlatform("xj");
        List<SystemActivityProgrammeVo> constActivities = activityProgrammeService.getActivityByStateAndRelativeTime(searchForm);
        if (CollectionUtils.isEmpty(constActivities)) {
            log.info("暂无手动配置的活动");
            return;
        }

        //通过规则选择所有产品
        for (SystemActivityProgrammeVo constActivity : constActivities) {
            handleConstActivity(constActivity);
        }
    }

    /**
     * 根据id计算发放活动奖励
     *
     * @param saId
     */
    public void handleConstActivityBySaId(Long saId) {
        SystemActivityProgrammeVo constActivity = activityProgrammeService.detail(saId);
        if (Objects.isNull(constActivity) || constActivity.getConfigType() != EnumActivityConfigType.CONST) {
            log.error("执行了非手动配置的活动的补偿:{}", saId);
            return;
        }
        handleConstActivity(constActivity);
    }

    public void handleConstActivity(SystemActivityProgrammeVo constActivity) {
        //初始化活动区间
        initDate(constActivity);

        // 更新之前 把当前活动更新成0 防止多发
        whaleAddCommissionDetailItemMapper.updateProportionByDataId(constActivity.getId(),
                EnumCommissionType.ACTIVITY.name()
                , BigDecimal.ZERO);

        int offset = 0;
        int size = 3000;

        List<WhaleActivityReward> rewards = calculationRewardBySa(null, constActivity,offset,size);

        while (CollectionUtils.isNotEmpty(rewards)) {
            if (!CollectionUtils.isEmpty(rewards)) {
                //发放奖励
                grantReward(constActivity,rewards);
                offset += size;
                rewards = calculationRewardBySa(null, constActivity,offset,size);
            }
        }

    }

    /**
     * 发放奖励
     * @param constActivity 活动信息
     * @param rewards 奖励列表
     */
    @Transactional(rollbackFor = Exception.class)
    private void grantReward(SystemActivityProgrammeVo constActivity,List<WhaleActivityReward> rewards) {
        log.info("开始保存奖励数据:{}", rewards.size());
        smActivityRewardWhaleMapper.insertListDuplicateUpdateProportion(rewards);

        // 同一个活动
        Map<String, BigDecimal> rewardMap = rewards.stream()
                .collect(Collectors.toMap(WhaleActivityReward::getDataId,
                        WhaleActivityReward::getProportion, BigDecimal::add));
        Map<String, BigDecimal> rewardAmountMap = rewards.stream()
                .collect(Collectors.toMap(WhaleActivityReward::getDataId,
                        WhaleActivityReward::getAmount, BigDecimal::add));
        rewardService.awardByJob(constActivity, rewardMap, rewardAmountMap);
    }

    private void initDate(SystemActivityProgrammeVo constActivity) {
        //按月兑换的活动，活动时间处理为当月
        if (EnumActivityRewardMechanism.monthly.name().equals(constActivity.getRewardMechanism())) {
            LocalDateTime startTime = LocalDate.now().minusDays(1).atStartOfDay().with(TemporalAdjusters.firstDayOfMonth()).isBefore(constActivity.getStartTime())
                    ? constActivity.getStartTime()
                    : LocalDate.now().atStartOfDay().with(TemporalAdjusters.firstDayOfMonth());
            LocalDateTime endTime = LocalDate.now().withDayOfMonth(1).plusMonths(1).atStartOfDay().isAfter(constActivity.getEndTime())
                    ? constActivity.getEndTime()
                    : LocalDate.now().withDayOfMonth(1).plusMonths(1).atStartOfDay();
            constActivity.setStartTime(startTime);
            constActivity.setEndTime(endTime);
        }
    }

    /**
     * 单个活动的奖励
     *
     * @param order 订单信息
     */
    private List<WhaleActivityReward> calculationRewardBySa(SimpleCommissionDetailOrderDTO order,
                                                            SystemActivityProgrammeVo constActivity,Integer offset,Integer size) {
        List<SystemActivityConstRuleForm> rules = constActivity.getConstRuleWhale().getConstRules();
        SystemActivityConstRuleForm systemActivityConstRuleDTO = rules.get(0);
        EnumActivityConstJointType jointType = systemActivityConstRuleDTO.getJointType();
        // * 根据规则类型分组 <出单即送,规则列表>
        Map<EnumActivityConstRuleType, List<SystemActivityConstRuleForm>> ruleTypeMap = LambdaUtils.groupBy(rules, SystemActivityConstRuleForm::getRuleType);

        // 每个规则大组的奖励
        List<List<WhaleActivityReward>> ruleTypeRewards = ruleTypeMap.entrySet().stream()
                .map(ruleTypeVal -> calculateRewardByRuleType(order, ruleTypeVal.getKey(), ruleTypeVal.getValue(), constActivity, offset,size)).collect(Collectors.toList());

        // 如果条件是并且 当有一个不满足条件时跳过
        if (jointType == EnumActivityConstJointType.AND
                && ruleTypeRewards.stream().anyMatch(CollectionUtils::isEmpty)) {
            return Collections.emptyList();
        }
        //合并所有奖励 (单个活动-不同规则类型)
        return ruleTypeRewards.stream()
                .filter(CollectionUtils::isNotEmpty)
                .flatMap(Collection::stream)
                .peek(reward -> {
                    reward.setSaId(constActivity.getId());
                    reward.setChannelType(EnumActivityTriggerType.JOB);
                    reward.setRewardType(RewardType.ADD_COMMISSION);
                })
                .collect(Collectors.toList());
    }

    /**
     * 执行某个规则类型的
     *
     * @param couponsOrderMessage 订单信息 目前没有了
     * @param key                 规则类型
     * @param value               类型下所有规则
     * @param constActivity       具体活动
     * @return 满足条件的所有的单子
     */
    private List<WhaleActivityReward> calculateRewardByRuleType(SimpleCommissionDetailOrderDTO couponsOrderMessage,
                                                                EnumActivityConstRuleType key,
                                                                List<SystemActivityConstRuleForm> value,
                                                                SystemActivityProgrammeVo constActivity,
                                                                Integer offset,
                                                                Integer size) {

        log.info("执行规则类型：{}-{}-{}", key, constActivity.getId(), value.size());
        //获取规则奖励
        ActivityConstRuleParam order = new ActivityConstRuleParam()
                .rules(value)
                .ruleType(key)
                .saId(value.get(0).getSaId())
                .order(couponsOrderMessage)
                .constActivity(constActivity)
                .activityPlatform(constActivity.getActivityPlatform())
                .offset(offset)
                .size(size);
        return (List<WhaleActivityReward>) systemGroovyService.executeForCode(EnumGroovyRuleType.OPERATION_ACTIVI_CONST.getCode(), key.name(), order);

    }

}
