
package com.cfpamf.ms.insur.operation.qy.entity;

import com.cfpamf.ms.insur.operation.base.entity.BaseNoUserEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import javax.persistence.Column;
import javax.persistence.Table;
import javax.validation.constraints.NotNull;

/**
 * Created by zhengjing  on 2022-08-05 15:21:46
 *
 * <AUTHOR>
 */
@ApiModel("企业微信问卷题目")
@Table(name = "ope_qy_question")
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class OpeQyQuestion extends BaseNoUserEntity {

    /**
     * 字段名称 问卷id
     */
    @Column(name = "pager_id")
    @NotNull(message = "问卷id不能为空")
    @ApiModelProperty(value = "问卷id", required = true)
    Long pagerId;

    @ApiModelProperty("分组")
    String questionGroup;

    /**
     * 字段名称 问题类型 1-单选 2-多选 3-问答
     */
    @Column(name = "question_type")
    @NotNull(message = "问题类型 1-单选 2-多选 3-问答不能为空")
    @ApiModelProperty(value = "问题类型 1-单选 2-多选 3-问答", required = true)
    Integer questionType;
    /**
     * 字段名称 问题描述
     */
    @Column(name = "question")
    @NotNull(message = "问题描述不能为空")
    @ApiModelProperty(value = "问题描述", required = true)
    String question;
    /**
     * 字段名称 问题参数
     */
    @Column(name = "params")
    @NotNull(message = "问题参数不能为空")
    @ApiModelProperty(value = "问题参数", required = true, example = "\"{\"}")
    String params;
}

