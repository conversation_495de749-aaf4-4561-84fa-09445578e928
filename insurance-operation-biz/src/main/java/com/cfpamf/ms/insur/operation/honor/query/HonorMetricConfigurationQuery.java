package com.cfpamf.ms.insur.operation.honor.query;

import com.cfpamf.ms.insur.operation.base.form.PageForm;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

/**
 * <AUTHOR> 2022/9/8 14:52
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class HonorMetricConfigurationQuery{
    @ApiModelProperty(name="评选对象 area:区域，bch：分支，emp：个人")
    String level;

    @ApiModelProperty(name="评选周期 year:年度，quarter：季度，month：月")
    String period;

    @ApiModelProperty(name="指标编码")
    String metricCode;
}
