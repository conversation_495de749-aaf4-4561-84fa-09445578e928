package com.cfpamf.ms.insur.operation.dingtalk.dao;

import com.cfpamf.ms.insur.operation.base.dao.CommonMapper;
import com.cfpamf.ms.insur.operation.dingtalk.entity.DingTalkSwFormInstance;
import com.cfpamf.ms.insur.operation.pco.entity.Dictionary;
import com.cfpamf.ms.insur.operation.pco.query.PcoDayFormQuery;
import com.cfpamf.ms.insur.operation.pco.vo.PcoDayFormVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR> 2022/8/31 10:58
 */
@Mapper
public interface DingTalkSwFormInstanceMapper extends CommonMapper<DingTalkSwFormInstance> {
    /**
     * 插入当key重复做修改操作
     *
     * @param list
     * @return
     */
    int insertListOrUpdate(@Param("list") List<DingTalkSwFormInstance> list);

    /**
     * 计算分数
     * @param insIds
     * @return
     */
    int updateScope(@Param("insIds") List<String> insIds);

    /**
     * pco 表单数据查询
     * @param query
     * @return
     */
    List<PcoDayFormVo> selectPcoForms(PcoDayFormQuery query);


    /**
     * 查询题目缓存
     * @return
     */
    List<Dictionary> selectLabels();
}
