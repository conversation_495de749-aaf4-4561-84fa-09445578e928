package com.cfpamf.ms.insur.operation.activity.dao;

import com.cfpamf.ms.insur.operation.activity.entity.SystemActivityRef;
import com.cfpamf.ms.insur.operation.activity.vo.SystemActivityRefVo;
import com.cfpamf.ms.insur.operation.base.dao.CommonMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface SystemActivityRefMapper extends CommonMapper<SystemActivityRef> {
    /**
     * 通过活动id查找关联活动
     *
     * @param systemActivityId
     * @return
     */
    List<SystemActivityRefVo> getBySystemActivityId(@Param("systemActivityId") Long systemActivityId);

    /**
     * 通过活动id软删除关联活动关系
     * @param systemActivityId
     */
    void softDeleteBySystemActivityId(@Param("systemActivityId") Long systemActivityId);

    void insertCopyRef(@Param("id") Long id, @Param("currentUserId") String currentUserId,@Param("Id") Long Id);
}