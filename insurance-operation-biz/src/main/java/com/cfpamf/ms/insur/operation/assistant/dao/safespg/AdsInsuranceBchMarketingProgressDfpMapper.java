package com.cfpamf.ms.insur.operation.assistant.dao.safespg;

import com.cfpamf.ms.insur.operation.assistant.entity.safespg.AdsInsuranceBchMarketingProgressDfp;
import org.apache.ibatis.annotations.Mapper;
import tk.mybatis.mapper.common.BaseMapper;

import java.util.List;

@Mapper
public interface AdsInsuranceBchMarketingProgressDfpMapper extends BaseMapper<AdsInsuranceBchMarketingProgressDfp> {
    public AdsInsuranceBchMarketingProgressDfp selectOneObject(AdsInsuranceBchMarketingProgressDfp adsInsuranceBchMarketingProgressDfp);

    public List<AdsInsuranceBchMarketingProgressDfp> selectAllBch(String pt);
}
