package com.cfpamf.ms.insur.operation.base.mq;


import com.cfpamf.ms.insur.operation.base.util.RedisUtil;
import com.cfpamf.ms.insur.operation.base.util.SpringFactoryUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.AcknowledgeMode;
import org.springframework.amqp.core.AmqpAdmin;
import org.springframework.amqp.rabbit.config.SimpleRabbitListenerContainerFactory;
import org.springframework.amqp.rabbit.connection.CachingConnectionFactory;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.core.RabbitAdmin;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.amqp.support.converter.ContentTypeDelegatingMessageConverter;
import org.springframework.amqp.support.converter.Jackson2JsonMessageConverter;
import org.springframework.amqp.support.converter.MessageConverter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.amqp.RabbitProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;


/**
 * <AUTHOR>
 */
@Configuration
@Data
@Slf4j
public class RabbitMqConfig {

    @Autowired
    private RedisUtil redisUtils;

    @Autowired
    private RabbitProperties rabbitProperties;



    /**
     * MQ发送方配置
     *
     * @param dbcConnectionFactory
     * @return*/

    @Bean(name = "insuranceOperationRabbitTemplate")
    @Primary
    public RabbitTemplate insuranceOperationRabbitTemplate(@Qualifier("insuranceOperationConnectionFactory") ConnectionFactory dbcConnectionFactory) {
        // 若使用return-callback, 必须设置publisherReturns为true
        RabbitTemplate rabbitTemplate = new RabbitTemplate(dbcConnectionFactory);
        // 使用return-callback, 必须设置mandatory为true
        rabbitTemplate.setMandatory(true);
        rabbitTemplate.setMessageConverter(producerJackson2MessageConverter());

        // 如果消息没有到exchange, 则confirm回调, ack=false; 如果消息到达exchange, 则confirm回调, ack=true
        rabbitTemplate.setConfirmCallback((correlationData, ack, cause) -> {

            if (ack) {
                log.info("消息确认到达exchange!");
                redisUtils.remove(correlationData.getId());
            } else {
                log.error("消息推送MQ失败, 请检查MQ是否异常, 消息ID: {}", correlationData.getId());
            }
        });

        // 如果exchange到queue成功, 则不回调return; 如果exchange到queue失败, 则回调return(需设置mandatory=true, 否则不会回调, 消息就丢了)
        rabbitTemplate.setReturnCallback((message, replyCode, replyText, exchange, routingKey) -> {
            log.error("消息推送MQ队列失败, 请检查路由键是否准确, 交换机:{}, 路由键:{}", exchange, routingKey);
            // 消息反序列化
            Object messageObj = new Jackson2JsonMessageConverter().fromMessage(message);

            // 休眠3s
            try {
                Thread.sleep(3000);
            } catch (InterruptedException e) {
                log.error("休眠失败", e);
            }
            //从上下文获取BEAN
            RabbitMqUtils rabbitMqUtils = SpringFactoryUtil.getBean("rabbitMqUtils", RabbitMqUtils.class);
            // 重试
            rabbitMqUtils.send(exchange, routingKey, messageObj);
        });

        return rabbitTemplate;
    }

    @Bean(name = "insuranceOperationConnectionFactory")
    @Primary
    public ConnectionFactory insuranceOperationConnectionFactory() {
        return this.buildConnectionFactory(rabbitProperties.getHost(), rabbitProperties.getPort(),
                rabbitProperties.getUsername(), rabbitProperties.getPassword(), "/");
    }

    private ConnectionFactory buildConnectionFactory(String host, int port, String username, String password,
                                                     String virtualHost) {
        CachingConnectionFactory connectionFactory = new CachingConnectionFactory();
        connectionFactory.setHost(host);
        connectionFactory.setPort(port);
        connectionFactory.setUsername(username);
        connectionFactory.setPassword(password);
        connectionFactory.setVirtualHost(virtualHost);
        return connectionFactory;
    }

    /**
     * 消费方配置
     *
     * @param dbcConnectionFactory
     * @param messageConverter
     * @return
     * */

    @Bean(name = "insuranceOperationFactory")
    public SimpleRabbitListenerContainerFactory insuranceOperationFactory(@Qualifier("insuranceOperationConnectionFactory") ConnectionFactory dbcConnectionFactory, MessageConverter messageConverter) {
        SimpleRabbitListenerContainerFactory factory = new SimpleRabbitListenerContainerFactory();
        factory.setConnectionFactory(dbcConnectionFactory);
        factory.setMessageConverter(messageConverter);
        // 设置应答模式为手动
        factory.setAcknowledgeMode(AcknowledgeMode.MANUAL);
        return factory;
    }

    @Bean
    public MessageConverter messageConverter() {
        return new ContentTypeDelegatingMessageConverter(new Jackson2JsonMessageConverter());
    }

    @Bean
    public Jackson2JsonMessageConverter producerJackson2MessageConverter() {
        return new Jackson2JsonMessageConverter();
    }

    @Bean
    public AmqpAdmin insuranceOperationAmqpAdmin(@Qualifier("insuranceOperationConnectionFactory") ConnectionFactory connectionFactory) {
        return new RabbitAdmin(connectionFactory);
    }



}