package com.cfpamf.ms.insur.operation.fegin.customer.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang.StringUtils;

/**
 * 贫困户
 * <AUTHOR> on 2018/3/14.
 */
@AllArgsConstructor
@Getter
public enum NaturePovertyEnum {

    /**贫困户*/
    POVERTY("01","贫困户"),
    /**非贫困户*/
    NON_POVERTY("02","非贫困户");

    private String stsCode;

    private String stsName;

    /**
     * 根据code获取枚举
     * @param code
     * @return
     */
    public static NaturePovertyEnum getEnumByCode(String code){
        for(NaturePovertyEnum custCreditStsEnum : NaturePovertyEnum.values()){
            if(StringUtils.equals(code, custCreditStsEnum.getStsCode())){
                return custCreditStsEnum;
            }
        }
        return null;
    }
}
