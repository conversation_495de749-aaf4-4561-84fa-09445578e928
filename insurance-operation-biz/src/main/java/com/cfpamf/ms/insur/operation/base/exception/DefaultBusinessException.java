package com.cfpamf.ms.insur.operation.base.exception;

import com.cfpamf.common.ms.exception.MSBizNormalException;

/**
 * Title: 默认异常信息与编码实现类
 * Build: 2020-01-06 18:19:58
 *
 * <AUTHOR>
 */
public class DefaultBusinessException extends MSBizNormalException {

    public static final DefaultBusinessException PARAM_IS_INVALID = new DefaultBusinessException("param.is.invalid", "参数无效");
    public static final DefaultBusinessException PARAM_IS_NULL = new DefaultBusinessException("param.is.null", "参数为空");
    public static final DefaultBusinessException PARAM_TYPE_ERROR = new DefaultBusinessException("param.type.error", "参数类型错误");
    public static final DefaultBusinessException PARAM_NOT_COMPLETE = new DefaultBusinessException("param.not.complete", "参数缺失");
    public static final DefaultBusinessException PARAM_NOT_JSON_FORMAT = new DefaultBusinessException("param.not.json.format", "参数非JSON格式");

    public static final DefaultBusinessException SYSTEM_UNKNOWN_ERROR = new DefaultBusinessException("system.unknown.error", "系统未知错误");
    public static final DefaultBusinessException SYSTEM_INNER_ERROR = new DefaultBusinessException("system.inner.error", "系统繁忙，请稍后重试");

    public static final DefaultBusinessException DATA_NOT_FOUND = new DefaultBusinessException("data.not.found", "数据未找到");
    public static final DefaultBusinessException DATA_WRONG = new DefaultBusinessException("data.wrong", "数据有误");
    public static final DefaultBusinessException DATA_ALREADY_EXISTED = new DefaultBusinessException("data.already.existed", "数据已存在");

    public static final DefaultBusinessException INTERFACE_INNER_INVOKE_ERROR = new DefaultBusinessException("interface.inner.invoke.error", "内部系统接口调用异常");
    public static final DefaultBusinessException INTERFACE_OUTER_INVOKE_ERROR = new DefaultBusinessException("interface.outer.invoke.error", "外部系统接口调用异常");
    public static final DefaultBusinessException INTERFACE_FORBIDDEN_ACCESS = new DefaultBusinessException("interface.forbidden.access", "该接口禁止访问");
    public static final DefaultBusinessException INTERFACE_ADDRESS_INVALID = new DefaultBusinessException("interface.address.invalid", "接口地址无效");
    public static final DefaultBusinessException INTERFACE_REQUEST_TIMEOUT = new DefaultBusinessException("interface.request.timeout", "接口请求超时");
    public static final DefaultBusinessException INTERFACE_EXCEED_LOAD = new DefaultBusinessException("interface_exceed.load", "接口负载过高");

    public static final DefaultBusinessException NO_PERMISSION_ACCESS = new DefaultBusinessException("no.permission.access", "无权访问");

    public DefaultBusinessException(String code, String message) {
        super(code, message);
    }
}
