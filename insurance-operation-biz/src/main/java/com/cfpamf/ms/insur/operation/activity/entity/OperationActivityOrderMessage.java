package com.cfpamf.ms.insur.operation.activity.entity;

import com.cfpamf.ms.insur.operation.activity.listener.GrantCouponsOrderMessage;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import lombok.Data;
import org.springframework.beans.BeanUtils;

import javax.persistence.Id;
import javax.persistence.OrderBy;
import javax.persistence.Table;

/**
 * operation_activity_order_message
 *
 * <AUTHOR>
@ApiModel(value = "com.cfpamf.ms.insur.operation.activity.entity.OperationActivityOrderMessage产品险种配置表")
@Data
@Table(name = "operation_activity_order_message")
public class OperationActivityOrderMessage extends GrantCouponsOrderMessage {
    /**
     * 主键id
     */
    @Id
    @OrderBy("desc")
    private Long id;
}