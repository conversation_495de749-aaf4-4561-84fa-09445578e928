package com.cfpamf.ms.insur.operation.fegin.hr.api;

import com.cfpamf.ms.insur.operation.fegin.hr.req.DingUserInfoReq;
import com.cfpamf.ms.insur.operation.fegin.hr.vo.DingUserInfoVO;
import com.cfpamf.ms.insur.operation.fegin.hr.vo.HrRes;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/5/26 11:14
 */
@FeignClient(name = "hrms-biz", url = "${hrms-biz.url}")
//@FeignClient(name = "hrms-biz", url = "http://hrms-biz.tsg.cfpamf.com")
public interface HrFeignClient {

    /**
     * 根据员工工号/员工钉钉userId列表获取员工信息
     *
     * @return
     */
    @PostMapping("/hrms/dingtalk/getDingUserInfoList")
    HrRes<List<DingUserInfoVO>> getDingUserInfoList(@RequestBody DingUserInfoReq req);
}
