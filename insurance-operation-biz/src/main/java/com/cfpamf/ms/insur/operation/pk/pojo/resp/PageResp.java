package com.cfpamf.ms.insur.operation.pk.pojo.resp;

import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/8/11 16:37
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class PageResp<T> {

    protected List<T> records;
    protected long total;
    protected long size;
    protected long current;
    protected boolean optimizeCountSql;
    protected boolean isSearchCount;
    protected boolean hitCount;
    protected String countId;
    protected Long maxLimit;
}
