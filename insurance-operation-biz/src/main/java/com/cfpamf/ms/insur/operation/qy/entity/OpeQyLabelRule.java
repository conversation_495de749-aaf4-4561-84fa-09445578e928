
package com.cfpamf.ms.insur.operation.qy.entity;

import com.cfpamf.ms.insur.operation.base.entity.BaseNoUserEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import javax.persistence.Column;
import javax.persistence.Table;
import javax.validation.constraints.NotNull;

/**
 * Created by zhengjing  on 2022-08-05 15:21:46
 *
 * <AUTHOR>
 */
@ApiModel("企业客户标签规则")
@Table(name = "ope_qy_label_rule")
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class OpeQyLabelRule extends BaseNoUserEntity {

    /**
     * 字段名称 问卷id
     */
    @Column(name = "pager_id")
    @NotNull(message = "问卷id不能为空")
    @ApiModelProperty(value = "问卷id", required = true)
    Long pagerId;


    String labelId;

    String labelName;
    /**
     * 字段名称 问题参数
     */
    @NotNull(message = "问题参数不能为空")
    @ApiModelProperty(value = "问题参数", required = true, example = "\"{\"}")
    String labelRule;
}

