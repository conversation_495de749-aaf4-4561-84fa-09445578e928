package com.cfpamf.ms.insur.operation.call.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 一键呼叫请求DTO
 *
 * <AUTHOR>
 * @date 2025-07-22
 */
@Data
public class CallRequestDto {

    @ApiModelProperty(value = "保单号", required = true)
    @NotBlank(message = "保单号不能为空")
    private String policyNo;

    @ApiModelProperty(value = "客户手机号", required = true)
    @NotBlank(message = "客户手机号不能为空")
    private String customerPhone;

    @ApiModelProperty(value = "客户编号")
    private String clientId;

    @ApiModelProperty(value = "客户名称")
    private String clientName;

    @ApiModelProperty(value = "备注")
    private String remark;
}
