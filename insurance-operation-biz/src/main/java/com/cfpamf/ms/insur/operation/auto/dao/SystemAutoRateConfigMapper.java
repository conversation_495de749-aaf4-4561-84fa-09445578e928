package com.cfpamf.ms.insur.operation.auto.dao;

import com.cfpamf.ms.insur.operation.auto.dto.SystemAutoRateConfigDto;
import com.cfpamf.ms.insur.operation.auto.dto.SystemAutoRateConfigListDto;
import com.cfpamf.ms.insur.operation.auto.po.SystemAutoRateConfigPo;
import com.cfpamf.ms.insur.operation.auto.query.SystemAutoRateConfigQuery;
import com.cfpamf.ms.insur.operation.base.dao.CommonMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface SystemAutoRateConfigMapper extends CommonMapper<SystemAutoRateConfigPo> {

    /**
     * 查询车险费率配置分页列表
     * @param query 查询条件
     * @return SystemAutoRateConfigDto
     */
    List<SystemAutoRateConfigListDto> listByParam(SystemAutoRateConfigQuery query);

    /**
     * 根据产品id获取车险费率配置
     * @return SystemAutoRateConfigDto
     */
    List<SystemAutoRateConfigListDto> listByProductId(@Param("productId") Integer productId, @Param("id") Integer id);

    SystemAutoRateConfigDto selectByProductId(Integer productId);
}
