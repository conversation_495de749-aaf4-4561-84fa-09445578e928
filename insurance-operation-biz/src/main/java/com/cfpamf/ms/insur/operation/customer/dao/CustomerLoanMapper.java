package com.cfpamf.ms.insur.operation.customer.dao;

import com.cfpamf.ms.insur.operation.base.dao.CommonMapper;
import com.cfpamf.ms.insur.operation.customer.po.CustomerLoanPo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Mapper
public interface CustomerLoanMapper extends CommonMapper<CustomerLoanPo> {

    /**
     * 初始化断保客户表
     *
     * @return
     */
    int insertFromDwd();

    /**
     * 通过证件号码查询客户信息 S
     *
     * @param idNumber
     * @return
     */
    default CustomerLoanPo selectByIdNumber(String idNumber) {
        CustomerLoanPo query = new CustomerLoanPo();
        query.setIdNumber(idNumber);
        return selectOne(query);
    }

    /**
     * 统计团险单量
     *
     * @param licenseNum
     * @return
     */
    int countValidGroup(@Param("licenseNum") String licenseNum);

    /**
     * 统统计未失效医疗险单量
     *
     * @param idNumber
     * @return
     */
    int countValidMedical(@Param("idNumber") String idNumber);

    /**
     * 统计未失效重疾险单量
     *
     * @param idNumber
     * @return
     */
    int countValidIllness(@Param("idNumber") String idNumber);

    void updateByIbNumber(@Param("insuredIdNumber") String insuredIdNumber, @Param("conversionTime") LocalDateTime conversionTime
            , @Param("conversionEmp") String conversionEmp, @Param("conversionOrderId") String conversionOrderId);
}
