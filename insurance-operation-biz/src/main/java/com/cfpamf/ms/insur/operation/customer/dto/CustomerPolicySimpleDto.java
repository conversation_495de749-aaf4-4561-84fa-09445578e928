package com.cfpamf.ms.insur.operation.customer.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.experimental.FieldDefaults;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2023/5/13 13:50
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class CustomerPolicySimpleDto {

    String productType;

    @ApiModelProperty("投保人名字")
    String personName;

    @ApiModelProperty("被保人名字")
    String insuredName;

    String cellPhone;

    String fhOrderId;

    LocalDateTime interruptionTime;

    public String getProductType() {
        return Objects.isNull(productType) ? "" : productType;
    }
}
