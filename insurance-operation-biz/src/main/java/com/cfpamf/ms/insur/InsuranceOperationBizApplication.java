package com.cfpamf.ms.insur;

import feign.Logger;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.web.client.RestTemplate;

/**
 * <AUTHOR>
 */
@SpringBootApplication()
@EnableAspectJAutoProxy(exposeProxy = true)
@EnableFeignClients("com.cfpamf.ms")
@ComponentScan(basePackages = {"com.cfpamf.ms", "com.cfpamf.ms.insur"})
@EnableCaching
@EnableAsync
public class InsuranceOperationBizApplication {

    public static void main(String[] args) {
        SpringApplication.run(InsuranceOperationBizApplication.class, args);
    }


    @Bean
    public RestTemplate restTemplate() {
        SimpleClientHttpRequestFactory requestFactory = new SimpleClientHttpRequestFactory();
        requestFactory.setBufferRequestBody(false);
        return new RestTemplate(requestFactory);
    }

    /**
     * 记录feign请求与响应
     *
     * @return
     */
    @Bean
    Logger.Level feignLoggerLevel() {
        return Logger.Level.FULL;
    }
}
