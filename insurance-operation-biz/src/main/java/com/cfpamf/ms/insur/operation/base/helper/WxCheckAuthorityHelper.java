package com.cfpamf.ms.insur.operation.base.helper;

import org.springframework.util.StringUtils;
import com.alibaba.fastjson.JSON;
import com.cfpamf.ms.insur.operation.base.exception.BusinessException;
import com.cfpamf.ms.insur.operation.base.util.HttpRequestUtil;
import com.cfpamf.ms.insur.operation.base.util.RedisUtil;
import com.cfpamf.ms.insur.operation.base.vo.WxSessionVO;
import com.cfpamf.ms.insur.operation.fegin.bms.constants.BmsConstantCore;
import com.cfpamf.ms.insur.operation.fegin.bms.enums.ExcptEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;


/**
 * : 微信公共类
 *
 * @author: zhangnayi
 **/
@Slf4j
@Component
public class WxCheckAuthorityHelper {

    /**
     * redis 缓存
     */
    @Autowired
    protected RedisUtil<String, String> redisUtil;

    /**
     * 验证微信用户权限是从微信菜单过来
     *
     * @param wxOpenId
     * @param authorization
     */
    public WxSessionVO checkAuthority(String wxOpenId, String authorization) {

        if (StringUtils.isEmpty(wxOpenId)&&StringUtils.isEmpty(authorization)) {
            log.warn("微信用户token失效 wxOpenId={}, authorization={}", wxOpenId, authorization);
            log.warn("微信用户token失效 user-agent={}, referer={}", HttpRequestUtil.getUserAgent(), HttpRequestUtil.getReferer());
            throw new BusinessException(ExcptEnum.INVALID_TOKEN_501019);
        }
        WxSessionVO session = null;
        if(!StringUtils.isEmpty(wxOpenId)){
            session = getWxSession(wxOpenId);
        }
        if(session == null && !StringUtils.isEmpty(authorization)){
            session = getWxSession(BmsConstantCore.USER_TOKEN +authorization); //新老模式token 缓存兼容
        }
        if (session == null || !Objects.equals(session.getAuthorization(), authorization)) {
            log.warn("微信用户token失效 wxOpenId={}, authorization={}, session={}", wxOpenId, authorization, JSON.toJSONString(session));
            log.warn("微信用户token失效 user-agent= {}, referer={}", HttpRequestUtil.getUserAgent(), HttpRequestUtil.getReferer());
            throw new BusinessException(ExcptEnum.INVALID_TOKEN_501019);
        }
        log.info("获取session用户信息|{}", session);
        return session;
    }

    /**
     * 获取微信session
     *
     * @param wxOpenId
     * @return
     */
    public WxSessionVO getWxSession(String wxOpenId) {
        String json = redisUtil.get(wxOpenId);
        if (!StringUtils.isEmpty(json)) {
            return JSON.parseObject(json, WxSessionVO.class);
        }
        return null;
    }

}
