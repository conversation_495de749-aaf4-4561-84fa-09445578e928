package com.cfpamf.ms.insur.operation.planbook.convertor;

import com.cfpamf.ms.insur.operation.planbook.entity.OperationPlanBookDuty;
import com.cfpamf.ms.insur.operation.planbook.entity.OperationPlanBookFactor;
import com.cfpamf.ms.insur.operation.planbook.entity.OperationPlanBookMakeInfo;
import com.cfpamf.ms.insur.operation.planbook.entity.OperationPlanBookSetting;
import com.cfpamf.ms.insur.operation.planbook.form.OperationPlanBookDutyForm;
import com.cfpamf.ms.insur.operation.planbook.form.OperationPlanBookFactorForm;
import com.cfpamf.ms.insur.operation.planbook.form.OperationPlanBookMakeInfoForm;
import com.cfpamf.ms.insur.operation.planbook.form.OperationPlanBookSettingForm;
import com.cfpamf.ms.insur.operation.planbook.vo.PlanBookMakeInfoSettingVO;
import com.cfpamf.ms.insur.operation.planbook.vo.PlanBookMakeInfoVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/12/12 19:50
 * @Version 1.0
 */
@Mapper
public interface PlanBookConvertor {
    PlanBookConvertor CNT = Mappers.getMapper(PlanBookConvertor.class);

    OperationPlanBookMakeInfo toPlanBook(OperationPlanBookMakeInfoForm planBookMakeInfoForm);

    OperationPlanBookSetting toPlanBookSetting(OperationPlanBookSettingForm settingForm);

    OperationPlanBookDuty toPlanBookDuty(OperationPlanBookDutyForm planBookDutyForm);

    List<OperationPlanBookDuty> toPlanBookDuty(List<OperationPlanBookDutyForm> planBookDutyForm);

    OperationPlanBookFactor toPlanBookFactor(OperationPlanBookFactor factorForm);

    List<OperationPlanBookFactor> toPlanBookFactorList(List<OperationPlanBookFactorForm> factorForm);

    PlanBookMakeInfoSettingVO toPlanBookSettingVO(OperationPlanBookSetting planBookSetting);

    PlanBookMakeInfoVO toPlanBookMakeInfoVO(OperationPlanBookMakeInfo planBookInfo);

}
