package com.cfpamf.ms.insur.operation.activity.entity;

import com.cfpamf.ms.insur.operation.base.entity.BaseEntity;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

/**
 * <AUTHOR>
 * @Date 2022/5/12 15:14
 * @Version 1.0
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class SmAchieveEmpCount extends BaseEntity {

    String regionName;

    String organizationName;

    String count;

    String month;

    String organizationCode;

    String regionCode;



}
