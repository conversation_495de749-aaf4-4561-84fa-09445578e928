package com.cfpamf.ms.insur.operation.prospectus.api;

import com.cfpamf.ms.insur.operation.prospectus.form.ProspectusConfigForm;
import com.cfpamf.ms.insur.operation.prospectus.form.ProspectusConfigSearchForm;
import com.cfpamf.ms.insur.operation.prospectus.form.ProspectusProductSearchForm;
import com.cfpamf.ms.insur.operation.prospectus.vo.ProspectusConfigVo;
import com.cfpamf.ms.insur.operation.prospectus.vo.ProspectusProductVo;
import com.github.pagehelper.PageInfo;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * <AUTHOR>
 * @date 2021/5/19 14:54
 */
@RequestMapping("/back/operation/prospectus/config")
public interface ProspectusConfigApi {

    /**
     * 保存计划书配置
     *
     * @param prospectusConfigForm
     */
    @PostMapping("")
    void save(@RequestBody @Valid ProspectusConfigForm prospectusConfigForm);

    /**
     * 编辑计划书配置
     *
     * @param prospectusConfigForm
     * @param id
     * @return
     */
    @PutMapping("/{id:\\d+}")
    void update(@PathVariable Long id, @RequestBody @Valid ProspectusConfigForm prospectusConfigForm);

    /**
     * 查看计划书配置详情
     *
     * @param id
     * @return
     */
    @GetMapping("/{id:\\d+}")
    ProspectusConfigVo getById(@PathVariable Long id);

    /**
     * 查看计划书配置详情
     *
     * @param productId
     * @return
     */
    @GetMapping("/product/{productId:\\d+}")
    ProspectusConfigVo getByProductId(@PathVariable Long productId);

    /**
     * 删除计划书配置
     *
     * @param id
     */
    @DeleteMapping("/{id:\\d+}")
    void delete(@PathVariable Long id);

    /**
     * 搜索计划书配置详情
     *
     * @param prospectusConfigSearchForm
     * @return
     */
    @PostMapping("/search")
    PageInfo<ProspectusConfigVo> search(@RequestBody @Valid ProspectusConfigSearchForm prospectusConfigSearchForm);


    /**
     * 搜索计划书产品
     *
     * @param prospectusProductSearchForm
     * @return
     */
    @PostMapping("/product/search")
    public PageInfo<ProspectusProductVo> searchProspectusProduct(@RequestBody @Valid ProspectusProductSearchForm prospectusProductSearchForm);

}
