package com.cfpamf.ms.insur.operation.fegin.customer.enums;

import com.cfpamf.cmis.common.base.OpenApiCode;
import com.cfpamf.cmis.common.exception.ResultException;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 证件状态
 * <AUTHOR>
 */
@AllArgsConstructor
@Getter
public enum CredentialsStsEnum {

    /**
     * 待审核
     */
    PENDING("01","待审核"),
    /**
     * 审核通过
     */
    PASS("02","审核通过"),
    /**
     * 审核不通过
     */
    REJECT("03","审核不通过"),
    /**
     * 可疑
     */
    DOUBT("04","可疑");

    private String code;

    private String name;

    public static CredentialsStsEnum getCode(String code){
        for (CredentialsStsEnum credentialsStsEnum : values()){
            if(credentialsStsEnum.getCode().equals(code)){
                return credentialsStsEnum;
            }
        }
        throw new ResultException("参数错误",OpenApiCode.FAILED_CODE);
    }
}
