package com.cfpamf.ms.insur.operation.activity.vo;

import com.alibaba.fastjson.JSON;
import com.cfpamf.ms.insur.operation.activity.enums.*;
import com.cfpamf.ms.insur.operation.activity.form.SystemActivityConstRuleWrapperForm;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.google.common.collect.Lists;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

/**
 * 活动方案视图对象
 *
 * <AUTHOR>
 * @date 2021/7/7 15:39
 */
@ApiModel("活动方案视图对象")
@Getter
@Setter
public class SystemActivityProgrammeVo {

    /**
     * id
     */
    @ApiModelProperty(value = "id")
    private Long id;

    /**
     * 活动编号
     */
    @ApiModelProperty(value = "活动编号")
    private String activityCode;
    /**
     * 活动标题
     */
    @ApiModelProperty(value = "活动标题")

    private String title;

    /**
     * 活动类型 normal-普通活动 commission-加佣 ticket-抽奖券  RED_ENVELOPE-红包
     */
    @ApiModelProperty(value = "活动类型 NORMAL-普通活动 COMMISSION-加佣 TICKET-抽奖券 RED_ENVELOPE红包")
    private ActivityType type;
    /**
     * 活动对象
     */
    @ApiModelProperty(value = "活动对象 STAFF_MANAGER-员工+客户经理 USER-普通用户 VILLAGE_AGENT-村代")
    private ActivityObject activityObject;

    @ApiModelProperty(value = "配置类型 GROOVY-脚本 CONST-手动",
            allowableValues = "GROOVY,CONST", example = "GROOVY")
    private EnumActivityConfigType configType;

    @ApiModelProperty(value = "背景图")
    private String backgroundImageUrl;
    /**
     * 活动区域
     */
    @ApiModelProperty(value = "活动区域")
    private List<String> regionList;
    /**
     * 活动区域
     */
    @ApiModelProperty(value = "活动区域编码", hidden = true)
    private String regions;
    /**
     * 活动时间From
     */
    @ApiModelProperty(value = "活动时间From")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;

    /**
     * 活动时间To
     */
    @ApiModelProperty(value = "活动时间To")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;

    /**
     * 最后配置人
     */
    @ApiModelProperty(value = "最后配置人")
    private String lastConfigUser;
    /**
     * 最后配置时间
     */
    @ApiModelProperty(value = "最后配置时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    /**
     * 启用标记
     */
    @ApiModelProperty(value = "0未发布 1已发布 2已暂停 3已作废")
    private Integer activeFlag;

    /**
     * 活动状态
     */
    @ApiModelProperty(value = "活动状态")
    private String activeState;

    public void calculationActiveState() {
        EnumActivityState enumActivityState = EnumActivityState.getActivityStateByCodeAndTime(startTime, endTime, activeFlag);
        if (Objects.nonNull(enumActivityState)) {
            activeState = enumActivityState.name();
        }
    }

    /**
     * 活动介绍
     */
    @ApiModelProperty(value = "活动介绍")
    private String content;

    /**
     * 列表图片
     */
    @ApiModelProperty(value = "列表图片")
    private String imageUrl;

    /**
     * 优惠类型 overlay-叠加 optimal-最优
     */
    @ApiModelProperty(value = "优惠类型 OVERLAY-叠加 OPTIMAL-最优")
    private ConflictRule conflictRule;

    /**
     * 推荐产品
     */
    @ApiModelProperty(value = "推荐产品")
    private List<Integer> productList;

    /**
     * 推荐产品
     */
    @ApiModelProperty(value = "推荐产品")
    private String products;
    /**
     * 推荐产品视图对象
     */
    @ApiModelProperty("推荐产品视图对象")
    private List<RecommendedProductVo> recommendedProductList;

    /**
     * els活动编码
     */
    @ApiModelProperty(value = "els活动编码")
    private Integer elsActivityNumber;

    /**
     * els赠送口令
     */
    @ApiModelProperty(value = "els赠送口令")
    private String elsGrantPassword;

    /**
     * 关联活动
     */
    @ApiModelProperty(value = "关联活动")
    private List<SystemActivityRefVo> systemActivityRefList;

    /**
     * 活动产品以及规则奖励
     */
    @ApiModelProperty(value = "活动产品以及规则奖励")
    private List<SystemActivityProductVo> systemActivityProductList;
    /**
     * 活动产品以及规则奖励
     */
    @ApiModelProperty(value = "活动产品以及规则奖励(小鲸)")
    private List<SystemActivityProductVo> systemActivityProductWhaleList;

    /**
     * 开启次数
     */
    @ApiModelProperty(value = "开启次数")
    private Integer openCount;

    @ApiModelProperty(value="活动平台")
    private String activityPlatform;

    @ApiModelProperty(value = "兑换机制:monthly按月兑现，end活动结束兑现")
    private String rewardMechanism;

    @ApiModelProperty("手动配置的奖励规则")
    private SystemActivityConstRuleWrapperForm constRule;
    @ApiModelProperty("手动配置的奖励规则(小鲸)")
    private SystemActivityConstRuleWrapperForm constRuleWhale;

    /**
     * 初始化产品区域集合
     */
    public void initRegionListByRegions() {
        if (StringUtils.isNotBlank(regions)) {
            regionList = JSON.parseArray(regions, String.class);
        }
    }

    /**
     * 获取推荐产品id集合
     */
    @JsonIgnore
    public List<Long> getProductIdList() {
        if (StringUtils.isNotBlank(products)) {
            return JSON.parseArray(products, Long.class);
        }
        return Lists.newArrayList();
    }
}
