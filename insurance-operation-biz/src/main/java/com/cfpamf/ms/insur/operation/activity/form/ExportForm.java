package com.cfpamf.ms.insur.operation.activity.form;

import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2022/6/15 09:53
 * @Version 1.0
 */
@Data
public class ExportForm {

    /**
     * 规则编码
     */
    private String ruleCode;

    /**
     * 导出字段
     */
    private List<ExportField> exportFieldList;

    /**
     * 接收动态参数
     */
    private Map<String, Object> extraParams;

    private String fileName;

    @Data
    public static class ExportField {

        String fieldName;

        String field;

    }
}

