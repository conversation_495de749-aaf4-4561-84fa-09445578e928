package com.cfpamf.ms.insur.operation.aicall.controller;

import com.cfpamf.ms.insur.operation.aicall.AiCallProvider;
import com.cfpamf.ms.insur.operation.base.annotaions.ResponseDecorated;
import com.cfpamf.ms.insur.operation.base.constant.BaseConstants;
import io.swagger.annotations.*;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.Map;


/**
 * Ai跟进记录
 *
 * <AUTHOR>
 * @date 2023/11/03 11:04
 */
@Slf4j
@Api(value = "Ai跟进记录", tags = {"Ai跟进记录"})
@RequestMapping(value = {BaseConstants.ADMIN_VERSION + "/aiFollow"})
@ResponseDecorated
@RestController
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class AiFollowRecordController {

    AiCallProvider aiCallProvider;

    @PostMapping("/getRecordFile")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "contactUuid", value = "获取录音文件id", required = true, dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "taskType", value = "业务类型", required = true, dataType = "string", paramType = "query")
    })
    @ApiOperation("获取录音文件")
    public Map<String,String> saveRenewalFollowRecord(@RequestParam("contactUuid") String contactUuid,@RequestParam("taskType") String taskType) {
        return aiCallProvider.getRecordFile(contactUuid,taskType);
    }
}
