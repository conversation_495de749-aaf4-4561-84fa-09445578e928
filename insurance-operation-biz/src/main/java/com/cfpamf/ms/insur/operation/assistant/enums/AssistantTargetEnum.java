package com.cfpamf.ms.insur.operation.assistant.enums;

import lombok.Getter;

@Getter
public enum AssistantTargetEnum{
    CLASSIC_INSURANCE_AMOUNT("CLASSIC_INSURANCE_AMOUNT","标准保费","保险助手保准保费总目标"),
    LOAN_CLASSIC_INSURANCE_AMOUNT("LOAN_CLASSIC_INSURANCE_AMOUNT","主营相关标准保费","保险助手主营相关标准保费子目标"),
    UNLOAN_CLASSIC_INSURANCE_AMOUNT("UNLOAN_CLASSIC_INSURANCE_AMOUNT","非主营相关标准保费","保险助手非主营相关标准保费子目标"),
    RETENTION_RATE("RETENTION_RATE","留存率","保险助手留存率总目标"),
    UNLOAN_NC_CLASSIC_INSURANCE_AMOUNT("UNLOAN_NC_CLASSIC_INSURANCE_AMOUNT","保险助手非主营拓新标准保费子目标","保险助手非主营拓新标准保费子目标"),
    LOAN_RETENTION_CLASSIC_INSURANCE_AMOUNT("LOAN_RETENTION_CLASSIC_INSURANCE_AMOUNT","留存标保","保险助手主营留存标准保费目标"),
    UNLOAN_RETENTION_CLASSIC_INSURANCE_AMOUNT("UNLOAN_RETENTION_CLASSIC_INSURANCE_AMOUNT","留存标保","保险助手非主营留存标准保费目标"),
    INSURANCE_AMOUNT_RATE("INSURANCE_AMOUNT_RATE","交叉销售比","保险助手规模保费总目标"),

    //交叉销售比提升值
    OFFLINE_LOAN_INSURANCE_RATE_SUGGEST("OFFLINE_LOAN_INSURANCE_RATE_SUGGEST","交叉销售比提升值","保险助手交叉销售比提升值"),
    //续保待办捞回率
    RENEWAL_TODO_RATE("RENEWAL_TODO_RATE","续保待办捞回率","保险助手续保待办捞回率总目标"),
    //本月预计放款金额
    LOAN_AMOUNT("LOAN_AMOUNT","本月预计放款金额","保险助手本月预计放款金额总目标"),
    ;
    private String value;
    private String name;
    private String desc;

    AssistantTargetEnum(String value, String name, String desc) {
        this.value = value;
        this.name = name;
        this.desc = desc;
    }

}
