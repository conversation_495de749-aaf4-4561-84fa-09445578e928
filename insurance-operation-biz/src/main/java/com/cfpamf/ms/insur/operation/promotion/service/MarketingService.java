package com.cfpamf.ms.insur.operation.promotion.service;

import com.cfpamf.ms.insur.operation.base.response.CommonResult;
import com.cfpamf.ms.insur.operation.fegin.wx.request.CustVisitGroupListQueryInput;
import com.cfpamf.ms.insur.operation.fegin.wx.request.CustVisitGroupOutput;
import com.cfpamf.ms.insur.operation.promotion.dto.MarketingInfoDTO;
import com.cfpamf.ms.insur.operation.promotion.dto.PutDownMarketingDTO;
import org.springframework.web.bind.annotation.RequestParam;

public interface MarketingService {

    Long addMaterial(MarketingInfoDTO dto);

    void putDownMaterial(PutDownMarketingDTO putDownMarketingDTO);

    CustVisitGroupOutput queryCustVisitGroupList(CustVisitGroupListQueryInput custVisitGroupListQueryInput);
}
