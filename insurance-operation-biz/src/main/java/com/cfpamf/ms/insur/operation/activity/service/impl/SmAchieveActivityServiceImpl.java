package com.cfpamf.ms.insur.operation.activity.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelReader;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.read.metadata.ReadSheet;
import com.alibaba.fastjson.JSONObject;
import com.cfpamf.common.ms.util.DateUtil;
import com.cfpamf.ms.insur.operation.activity.dao.AuthUserMapper;
import com.cfpamf.ms.insur.operation.activity.dao.SmAchieveActivityMapper;
import com.cfpamf.ms.insur.operation.activity.entity.AuthUser;
import com.cfpamf.ms.insur.operation.activity.entity.SmAchieveEmpCount;
import com.cfpamf.ms.insur.operation.activity.entity.SmActivityReward;
import com.cfpamf.ms.insur.operation.activity.form.AchieveActivityParams;
import com.cfpamf.ms.insur.operation.activity.service.SmAchieveActivityService;
import com.cfpamf.ms.insur.operation.activity.service.SystemActivityProgrammeService;
import com.cfpamf.ms.insur.operation.activity.vo.AchieveSummaryVO;
import com.cfpamf.ms.insur.operation.activity.vo.AchieveTableVO;
import com.cfpamf.ms.insur.operation.activity.vo.SystemActivityProductVo;
import com.cfpamf.ms.insur.operation.activity.vo.SystemActivityProgrammeVo;
import com.cfpamf.ms.insur.operation.base.constant.BusinessConstants;
import com.cfpamf.ms.insur.operation.base.constant.CacheKeyConstants;
import com.cfpamf.ms.insur.operation.base.constant.GroovyCodeConstants;
import com.cfpamf.ms.insur.operation.base.helper.BmsHelper;
import com.cfpamf.ms.insur.operation.base.service.SystemGroovyService;
import com.cfpamf.ms.insur.operation.base.util.DownLoadUtil;
import com.cfpamf.ms.insur.operation.base.util.HttpRequestUtil;
import com.cfpamf.ms.insur.operation.base.util.RedisUtil;
import com.cfpamf.ms.insur.operation.base.util.SpringFactoryUtil;
import com.cfpamf.ms.insur.operation.reward.service.RewardService;
import com.google.common.collect.Lists;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2022/5/9 10:18
 * @Version 1.0
 */
@Service("GOAL_ACHIEVE")
public class SmAchieveActivityServiceImpl implements SmAchieveActivityService, RewardService {

    /**
     * "ACHIEVE_ACTIVITY_%s_%s"  ACHIEVE_ACTIVITY_{活动id}_{日期精确到天}
     */
    public static final String CACHE_ACHIEVE_ACTIVITY = "ACHIEVE_ACTIVITY_%s_%s";

    @Autowired
    private SystemGroovyService systemGroovyService;

    @Autowired
    @Lazy
    private SystemActivityProgrammeService activityProgrammeService;

    @Autowired
    private AuthUserMapper authUserMapper;

    @Autowired
    private RedisUtil<String, String> redisUtil;

    @Autowired
    private SmAchieveActivityMapper achieveActivityMapper;

    @Autowired
    private BmsHelper bmsHelper;

    @Override
    @Cacheable(cacheNames = CacheKeyConstants.GOAL_ACHIEVE_CACHE, key = "'summaryData'+#params.regionName + '-' + #params.organizationName + '-' + #params.userId")
    public AchieveSummaryVO querySummaryData(AchieveActivityParams params) {

        String ruleCode = params.getRuleCode();
        params.setQueryList(false);
        AchieveSummaryVO result = new AchieveSummaryVO();

        if(!isRegion(params.getOrganizationName(), params.getRegionName()) || !isValid(params)) {
            result.setOrgName(params.getOrganizationName());
            return result;
        }

        Object summary = systemGroovyService.executeForCode(GroovyCodeConstants.OPERATION_ACTIVITY_GROOVY_TYPE, ruleCode, params);

        Optional<JSONObject> summaryDataOpt = Optional.ofNullable(summary).map(i -> JSONObject.parseObject(JSONObject.toJSONString(i)));
        Optional<JSONObject> personDataOpt = summaryDataOpt.map(jsonObject -> jsonObject.getJSONArray("personData")).filter(jsonArray -> !jsonArray.isEmpty())
                .map(jsonArray -> jsonArray.getJSONObject(0));
        Optional<JSONObject> orgDataOpt = summaryDataOpt.map(jsonObject -> jsonObject.getJSONArray("orgData")).filter(jsonArray -> !jsonArray.isEmpty())
                .map(jsonArray -> jsonArray.getJSONObject(0));
        Optional<String> goalAmountOpt = summaryDataOpt.map(jsonObject -> jsonObject.getString("goalAmount"));

        result.setUserName(personDataOpt.map(i -> i.getString("userName")).orElse(null));
        result.setPersonAmount(personDataOpt.map(i -> i.getString("amount")).orElse(null));
        result.setOrgAmount(orgDataOpt.map(i -> i.getString("amount")).orElse(null));
        result.setOrgName(orgDataOpt.map(i -> i.getString("organizationName")).orElse(params.getOrganizationName()));
        result.setOrgCount(orgDataOpt.map(i -> i.getString("count")).map(Integer::valueOf).orElse(null));
        result.setGoalAmount(goalAmountOpt.orElse(null));
        return result;
    }

    @Override
    public void handlerParams(AchieveActivityParams params) {
        AuthUser user = authUserMapper.getByUserId(HttpRequestUtil.getUserId());

        SystemActivityProgrammeVo activityProgrammeVo = activityProgrammeService.detail(params.getActivityId());
        if (Objects.isNull(activityProgrammeVo) || Objects.isNull(user)) {
            return;
        }
        params.setActivityState(activityProgrammeVo.getActiveState());
        params.setOrganizationName(user.getOrganizationName());
        params.setRegionName(user.getRegionName());
        SystemActivityProductVo activityProductVo = activityProgrammeVo.getSystemActivityProductList().get(0);
        params.setRuleCode(activityProductVo.getSystemActivityProductRuleList().get(0).getRuleCode());
        params.setStartTime(activityProgrammeVo.getStartTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        params.setEndTime(activityProgrammeVo.getEndTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        params.setUserId(HttpRequestUtil.getUserId());
    }

    @Override
    public List<AchieveTableVO> queryTableData(AchieveActivityParams params) {

        if(!isRegion(params.getOrganizationName(), params.getRegionName()) || !isValid(params)) {
            return null;
        }

        String ruleCode = params.getRuleCode();
        params.setQueryList(true);
        Object summary = systemGroovyService.executeForCode(GroovyCodeConstants.OPERATION_ACTIVITY_GROOVY_TYPE, ruleCode, params);
        Optional<JSONObject> summaryDataOpt = Optional.ofNullable(summary).map(i -> JSONObject.parseObject(JSONObject.toJSONString(i)));
        String goalAmountOpt = summaryDataOpt.map(jsonObject -> jsonObject.getString("goalAmount")).orElse(null);

        return summaryDataOpt.map(i -> i.getJSONArray("tableData")).map(
                jsonArray -> jsonArray.toJavaList(AchieveTableVO.class)
        ).map(list -> list.stream().peek(i -> i.setGoalAmount(goalAmountOpt))
                .sorted((t1, t2) -> new BigDecimal( StringUtils.isEmpty(t2.getAmount()) ? "0" : t2.getAmount()).compareTo(new BigDecimal(StringUtils.isEmpty(t1.getAmount()) ? "0" : t1.getAmount())))
                .collect(Collectors.toList())).orElse(null);
    }

    @Override
    public void editPopUp(String activityId, String state) {

        String redisKey = getCurrentKey(activityId);
        if (StringUtils.isEmpty(HttpRequestUtil.getUserId())) {
            return;
        }
        redisUtil.hmSet(redisKey, HttpRequestUtil.getUserId(), state);
        if (Objects.equals(redisUtil.getExpire(redisKey), -1L)) {
            redisUtil.expire(redisKey, 24L * 60 * 60);
        }
    }

    @Override
    public AchieveSummaryVO queryOrgData(AchieveActivityParams params) {
        AchieveSummaryVO result = new AchieveSummaryVO();
        Boolean canPop = isRegion(params.getOrganizationName(), params.getRegionName()) && Objects.equals(getPopUp(String.valueOf(params.getActivityId())), "1");
        Boolean validActivity = isValid(params);
        if (canPop && validActivity) {
           result = SpringFactoryUtil.getBean(SmAchieveActivityServiceImpl.class).queryPopOrgData(params);
        }
        result.setCanPopUp(canPop && validActivity);
        return result;
    }

    @Cacheable(cacheNames = CacheKeyConstants.GOAL_ACHIEVE_CACHE, key = "'popOrgData-'+#params.regionName + '-' + #params.organizationName")
    public AchieveSummaryVO queryPopOrgData(AchieveActivityParams params) {
        AchieveSummaryVO result = new AchieveSummaryVO();
        params.setQueryOrg(Boolean.TRUE);
        String ruleCode = params.getRuleCode();
        Object summary = systemGroovyService.executeForCode(GroovyCodeConstants.OPERATION_ACTIVITY_GROOVY_TYPE, ruleCode, params);
        Optional<JSONObject> summaryDataOpt = Optional.ofNullable(summary).map(i -> JSONObject.parseObject(JSONObject.toJSONString(i)));
        Optional<JSONObject> orgDataOpt = summaryDataOpt.map(jsonObject -> jsonObject.getJSONArray("orgData")).filter(jsonArray -> !jsonArray.isEmpty())
                .map(jsonArray -> jsonArray.getJSONObject(0));


        result.setOrgName(orgDataOpt.map(i -> i.getString("organizationName")).orElse(params.getOrganizationName()));
        result.setOrgCount(orgDataOpt.map(i -> i.getString("count")).map(Integer::valueOf).orElse(null));
        result.setOrgAmount(orgDataOpt.map(i -> i.getString("amount")).orElse(null));
        Optional<String> goalAmountOpt = summaryDataOpt.map(jsonObject -> jsonObject.getString("goalAmount"));
        result.setGoalAmount(goalAmountOpt.orElse(null));
        return result;
    }

    @Override
    public String getPopUp(String activityId) {
        String redisKey = getCurrentKey(activityId);
        if (StringUtils.isNotEmpty(HttpRequestUtil.getUserId())) {
            return Optional.ofNullable(redisUtil.hmGet(redisKey, HttpRequestUtil.getUserId())).map(String::valueOf).orElseGet(() -> "1");
        }
        return "0";
    }

    public Boolean isRegion(String organizationName, String regionName) {
        if (StringUtils.isEmpty(organizationName) || StringUtils.isEmpty(regionName)) {
            return Boolean.FALSE;
        }
        List<SmAchieveEmpCount> achieveActivities = achieveActivityMapper.queryByOrganizationName(organizationName, regionName);
        if (CollectionUtils.isNotEmpty(achieveActivities)) {
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

    private String getCurrentKey(String activityId) {
        return String.format(CACHE_ACHIEVE_ACTIVITY, activityId, LocalDateTime.now().format(DateTimeFormatter.ofPattern(DateUtil.CN_YEAR_MONTH_DAY_FORMAT)));
    }

    @Override
    public String getAcId() {
        List<String> dic = bmsHelper.getValidDictionaryItemCodesByTypeCode(BusinessConstants.BMS_ACHIEVE_ACTIVITY_SA_ID);
        if (CollectionUtils.isEmpty(dic)) {
            return null;
        }
        return dic.get(0);
    }


    @Override
    public void importFile(String fileUrl) {
        ExcelReader excelReader = null;
        try{
            InputStream inputStream = DownLoadUtil.downloadByUrl(fileUrl);
            excelReader = EasyExcel.read(inputStream).build();

            //导入前先删除数据
            SmAchieveEmpCount achieveEmpCount = new SmAchieveEmpCount();
            achieveEmpCount.setEnabledFlag(0);
            achieveActivityMapper.delete(achieveEmpCount);

            List<AuthUser> authUsers = authUserMapper.distinctRegion();

            Map<String, String> orgMap = authUsers.stream()
                    .filter(i -> Objects.nonNull(i) && StringUtils.isNotEmpty(i.getOrganizationName()) && StringUtils.isNotEmpty(i.getOrgCode())).collect(Collectors.toMap(
                            AuthUser::getOrganizationName,
                            AuthUser::getOrgCode,
                            (t1, t2) -> t1
                    ));
            Map<String, String> regionMap = authUsers.stream()
                    .filter(i -> Objects.nonNull(i) && StringUtils.isNotEmpty(i.getRegionName()) && StringUtils.isNotEmpty(i.getRegionCode())).collect(Collectors.toMap(
                            AuthUser::getRegionName,
                            AuthUser::getRegionCode,
                            (t1, t2) -> t1
                    ));
            ReadSheet readSheet0 = EasyExcel.readSheet(0).head(EmpAchieveExcelData.class)
                    .registerReadListener(new AchieveActivityListener(achieveActivityMapper, orgMap, regionMap)).build();

            excelReader.read(readSheet0);

        } catch(IOException e) {
            throw new RuntimeException(e);
        }
    }


    public boolean isValid(AchieveActivityParams achieveActivityParams) {
        LocalDateTime startTime = LocalDateTime.parse(achieveActivityParams.getStartTime(), DateTimeFormatter.ofPattern(DateUtil.CN_LONG_FORMAT));
        LocalDateTime endTime =LocalDateTime.parse(achieveActivityParams.getEndTime(), DateTimeFormatter.ofPattern(DateUtil.CN_LONG_FORMAT));

        if(Objects.equals(achieveActivityParams.getActivityState(), "IN_ACTIVITY") && LocalDateTime.now().isAfter(startTime) && LocalDateTime.now().isBefore(endTime)) {
            return true;
        }
        return false;
    }

    @Override
    public void awardByListener(List<SmActivityReward> smActivityRewardList) {

    }

    @Override
    public void awardByJob(SystemActivityProgrammeVo systemActivityProgrammeVo, Map<String, BigDecimal> dataIdMap) {

    }

    @Override
    public void awardByJobV2(SystemActivityProgrammeVo systemActivityProgrammeVo, Map<String, BigDecimal> dataIdMap, Map<String, BigDecimal> dataIdAmountMap) {

    }

    @Override
    public void takeBackReward(Long saId, boolean softDelete) {

    }

    @Slf4j
    public static class AchieveActivityListener extends AnalysisEventListener<EmpAchieveExcelData> {
        private static final int BATCH_COUNT = 500;

        Map<String, String> orgCodeMap;

        Map<String, String> regionCodeMap;

        /**
         * 缓存的数据
         */
        private List<EmpAchieveExcelData> cachedDataList = Lists.newArrayListWithExpectedSize(BATCH_COUNT);

        public AchieveActivityListener(SmAchieveActivityMapper achieveActivityMapper, Map<String, String> orgCodeMap
                , Map<String, String> regionCodeMap) {
            this.orgCodeMap = orgCodeMap;
            this.regionCodeMap = regionCodeMap;
            this.achieveActivityMapper = achieveActivityMapper;
        }

        private SmAchieveActivityMapper achieveActivityMapper;

        @Override
        public void invoke(EmpAchieveExcelData data, AnalysisContext context) {
            cachedDataList.add(data);
            if (cachedDataList.size() >= BATCH_COUNT) {
                saveData();
                cachedDataList = Lists.newArrayListWithExpectedSize(BATCH_COUNT);
            }
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext context) {
            saveData();
        }

        /**
         * 加上存储数据库
         */
        private void saveData() {
            log.info("{}条数据，开始存储数据库！", cachedDataList.size());
            List<SmAchieveEmpCount> saveDataList = cachedDataList.stream().map(item -> {
                SmAchieveEmpCount empCount = EmpAchieveExcelData.convertToSmAchieveEmpCount(item);
                empCount.setOrganizationCode(orgCodeMap.get(item.getOrganizationName()));
                empCount.setRegionCode(regionCodeMap.get(item.getRegionName()));
                empCount.setCreateBy(HttpRequestUtil.getUserId());
                if (Objects.equals("-", empCount.getCount())) {
                    empCount.setCount("0");
                }
                return empCount;
            }).collect(Collectors.toList());
            achieveActivityMapper.insertList(saveDataList);
        }
    }

    @Data
    public static class EmpAchieveExcelData {
        @ExcelProperty(value = "区域", index = 0)
        String regionName;

        @ExcelProperty(value = "分支", index = 2)
        String organizationName;

        @ExcelProperty(value = "5月初在职员工", index = 3)
        String count;

        public static SmAchieveEmpCount convertToSmAchieveEmpCount(EmpAchieveExcelData item) {
            if (item == null) {
                return null;
            }
            SmAchieveEmpCount result = new SmAchieveEmpCount();
            result.setRegionName(item.getRegionName());
            result.setOrganizationName(item.getOrganizationName());
            result.setCount(item.getCount());
            return result;
        }
    }
}
