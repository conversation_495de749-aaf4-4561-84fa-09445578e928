package com.cfpamf.ms.insur.operation.phoenix.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @Date 2021/10/19 16:37
 * @Version 1.0
 */
@AllArgsConstructor
@Getter
public enum EnumRenewalIntention {

    /**
     * 意向 willing:愿意续保,consider:考虑, unwilling:不愿续保,loseContact:联系不上
     */
    WILLING("willing","愿意续保"),
    CONSIDER("consider","考虑"),
    UNWILLING("unwilling","不愿续保"),
    LOSE_CONTACT("lose_contact","联系不上"),
    NO_FOLLOW_UP("noFollowUp","未跟进"),
    TRANSFER("transfer","已转投我司其他产品"),
    RENEWED("renewed","已续保");
    String code;
    String desc;
    public static String dict(String code) {
        if(Objects.isNull(code)){
            return "";
        }
        return Stream.of(values())
                .filter(em -> em.getCode().equals(code))
                .findFirst().map(EnumRenewalIntention::getDesc)
                .orElse("");
    }
}
