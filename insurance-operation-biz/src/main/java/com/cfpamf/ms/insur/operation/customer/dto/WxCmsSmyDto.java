package com.cfpamf.ms.insur.operation.customer.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import java.math.BigDecimal;
import java.util.Objects;

/**
 * @description: 微信提成统计VO
 * @author: zhangnayi
 * @create: 2018-07-23 15:47
 **/
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class WxCmsSmyDto {

    /**
     * 业绩
     */
    @ApiModelProperty("总业绩")
    BigDecimal orderAmount;

    /**
     * 单量统计
     */
    @ApiModelProperty("单量")
    Integer orderQty;

    /**
     * 单量统计
     */
    @ApiModelProperty("已断保客户数")
    Integer interruptionQty;

    /**
     * 单量统计
     */
    @ApiModelProperty("已激活客户数")
    Integer renewQty;
}
