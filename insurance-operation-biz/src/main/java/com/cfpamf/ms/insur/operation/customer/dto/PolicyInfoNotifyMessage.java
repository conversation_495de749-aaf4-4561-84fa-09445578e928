package com.cfpamf.ms.insur.operation.customer.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @description
 * @date 2023/5/4 1:13 上午
 * @Version 1.0
 */
@Data
public class PolicyInfoNotifyMessage {
    /**
     * 订单Id
     */
    private String fhOrderId;

    /**
     * 产品Id
     */
    private Integer productId;

    /**
     * 计划Id
     */
    private Integer planId;

    /**
     * 单价
     */
    private String qty;

    /**
     * 订单总金额
     */
    private BigDecimal totalAmount;

    /**
     * 提交时间
     */
    private Date submitTime;

    /**
     * 保险开始时间
     */
    private Date startTime;

    /**
     * 保险结束时间
     */
    private Date endTime;

    /**
     * 保单号
     */
    private String policyNo;

    /**
     * 支付Id
     */
    private String payId;

    /**
     * 支付URL
     */
    private String payUrl;

    /**
     * 支付状态
     */
    private String payStatus;

    /**
     * 支付时间
     */
    private Date paymentTime;

    /**
     * 订单创建时间
     */
    private Date createTime;
    /**
     * 推荐人id
     */
    private String recommendId;
}
