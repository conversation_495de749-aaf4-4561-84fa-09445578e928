package com.cfpamf.ms.insur.operation.call.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 录音状态枚举
 *
 * <AUTHOR>
 * @date 2025-07-22
 */
@Getter
@AllArgsConstructor
public enum RecordStatusEnum {

    /**
     * 未获取
     */
    NOT_OBTAINED(0, "未获取"),

    /**
     * 已获取
     */
    OBTAINED(1, "已获取"),

    /**
     * 获取失败
     */
    FAILED(2, "获取失败");

    /**
     * 状态码
     */
    private final Integer code;

    /**
     * 状态描述
     */
    private final String desc;

    /**
     * 根据状态码获取枚举
     *
     * @param code 状态码
     * @return 枚举值
     */
    public static RecordStatusEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (RecordStatusEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }

    /**
     * 根据状态码获取描述
     *
     * @param code 状态码
     * @return 状态描述
     */
    public static String getDescByCode(Integer code) {
        RecordStatusEnum status = getByCode(code);
        return status != null ? status.getDesc() : "未知状态";
    }
}
