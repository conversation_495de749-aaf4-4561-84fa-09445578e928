package com.cfpamf.ms.insur.operation.planbook.validate;

import com.cfpamf.ms.insur.operation.base.exception.BusinessException;
import com.cfpamf.ms.insur.operation.planbook.form.OperationPlanBookDutyForm;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.Period;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2021/12/20
 * @Version 1.0
 */
@Service("planBookValidator" + "546")
public class FxMomBabyPlanBookValidator extends AbstractPlanBookBaseFormValidator {

    public static final String[] COVERED_YEAR_CONDITION_ONE = new String[]{"10Y", "25Y", "30Y"};
    public static final String[] COVERED_YEAR_CONDITION_TWO = new String[]{"<70Y", "<80Y"};

    @Override
    protected void checkCoveredYears() {
        Integer coveredYears = Optional.ofNullable(this.factorFieldMap.get("coveredYears"))
                .map(str -> str.replace("Y", ""))
                .map(Integer::valueOf)
                .orElseThrow(() -> new BusinessException("", "请选择保障期限"));
        if (Arrays.stream(COVERED_YEAR_CONDITION_ONE)
                .anyMatch(item -> item.equals(this.factorFieldMap.get("validPeriod")))) {
            if (coveredYears != 1 && coveredYears != 5 && coveredYears != 10) {
                throw new BusinessException("", "保障期小于等于30年：交费期间仅可选1/5/10年缴！");
            }
        }

    }

    @Override
    protected void checkInsuredAge() {
        //计算被保人的年龄 28d到60Y之间
        if (planBookMakeInfoForm.getPerson().getBirthday() == null) {
            throw new BusinessException("", "请选择被保人年龄");
        }

        LocalDate insuredBirth = planBookMakeInfoForm.getPerson().getBirthday();
        Period period = Period.between(insuredBirth, LocalDate.now());
        int year = period.getYears();
        if (year > 0 && year > 17) {
            throw new BusinessException("", "被保人年龄不能大于17岁");
        }
        if (year == 0 && period.getDays() < 38) {
            throw new BusinessException("", "被保人年龄不能小于30天");
        }

        Integer coveredYears = Optional.ofNullable(this.factorFieldMap.get("coveredYears"))
                .map(str -> str.replace("Y", ""))
                .map(Integer::valueOf)
                .orElseThrow(() -> new BusinessException("", "请选择保障期限"));

        //被保人年龄 + 缴费年期 < 60
        if (year + coveredYears > 60) {
            throw new BusinessException("", "被保险人年龄在缴费期满时不可超过60岁");
        }

    }

    @Override
    public void checkInsuredPremium() {
        if (CollectionUtils.isEmpty(planBookMakeInfoForm.getDutyFormList())) {
            throw new BusinessException("", "请选择责任");
        }

        BigDecimal premium = planBookMakeInfoForm.getDutyFormList().stream().map(OperationPlanBookDutyForm::getPrice)
                .reduce(BigDecimal.ZERO, (t1, t2) -> t1.add(t2));

        if (premium.compareTo(new BigDecimal("500")) < 0) {
            throw new BusinessException("", "单张保单最低保费500元");
        }
    }

    @Override
    public void checkInsuredAmount() {
        BigDecimal amount = planBookMakeInfoForm.getDutyFormList().get(0).getAmount();

        if (amount.remainder(new BigDecimal(1000)).compareTo(BigDecimal.ZERO) != 0) {
            throw new BusinessException("", "保额必须为1000的倍数");
        }

        if (planBookMakeInfoForm.getDutyFormList().get(0).getAmount().compareTo(new BigDecimal("100000")) < 0) {
            throw new BusinessException("", "最低保额为100000元");
        }

        LocalDate insuredBirth = planBookMakeInfoForm.getPerson().getBirthday();
        Period period = Period.between(insuredBirth, LocalDate.now());
        int year = period.getYears();

        if (year < 6) {
            if (amount.compareTo(new BigDecimal("600000")) > 0) {
                throw new BusinessException("", "0-5周岁周岁最高保额为60万元");
            }
        }

        if (year > 5 && year < 18) {
            if (amount.compareTo(new BigDecimal("400000")) > 0) {
                throw new BusinessException("", "6周岁-17周岁最高保额为80万元");
            }
        }

    }

    @Override
    public void checkApplicantAge() {
        LocalDate proposerBirth = planBookMakeInfoForm.getProposer().getBirthday();
        Period period = Period.between(proposerBirth, LocalDate.now());
        int year = period.getYears();
        if (year < 18) {
            throw new BusinessException("", "投保人年龄必须大于等于18周岁");
        }

        Integer coveredYears = Optional.ofNullable(this.factorFieldMap.get("coveredYears"))
                .map(str -> str.replace("Y", ""))
                .map(Integer::valueOf)
                .orElseThrow(() -> new BusinessException("", "请选择保障期限"));

        if (coveredYears + year > 70) {
            throw new BusinessException("", "投保人年龄在缴费期满时不能超过70岁");
        }

    }


    @Override
    public void checkGuarantee() {

    }

    @Override
    public void checkAdditionalRisks() {
        List<String> dutyList = planBookMakeInfoForm.getDutyFormList().stream()
                .map(OperationPlanBookDutyForm::getDutyCode).collect(
                        Collectors.toList());
        if ("1Y".equals(factorFieldMap.get("coveredYears")) && dutyList.contains("FXHM")) {
            throw new BusinessException("", "非一年缴才可投保附加投保人豁免保险费重大疾病保险（2021款）");
        }
    }

    @Override
    public AbstractPlanBookBaseFormValidator copySelf() {
        return new FxMomBabyPlanBookValidator();
    }
}
