package com.cfpamf.ms.insur.operation.promotion.enums;

import com.cfpamf.common.ms.exception.MSBizNormalException;
import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;

@Getter
public enum OmsMarketingAttachmentTypeEnum {
    TEXT(0,"文本"),
    IMAGE(1,"图片"),
    VIDEO(2,"视频")
    ;

    private Integer attachmentType;

    private String attachmentTypeName;
    OmsMarketingAttachmentTypeEnum(Integer attachmentType, String attachmentTypeName) {
        this.attachmentType = attachmentType;
        this.attachmentTypeName = attachmentTypeName;
    }

    public static OmsMarketingAttachmentTypeEnum decode(Integer attachmentType) {
        return Arrays.stream(OmsMarketingAttachmentTypeEnum.values())
                .filter(x -> Objects.equals(x.getAttachmentType(), attachmentType))
                .findFirst().orElseThrow(() -> new MSBizNormalException("", "附件类型不匹配"));
    }
}
