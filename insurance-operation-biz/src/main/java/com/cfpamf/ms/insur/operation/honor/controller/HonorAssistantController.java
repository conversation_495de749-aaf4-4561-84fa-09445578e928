package com.cfpamf.ms.insur.operation.honor.controller;

import com.alibaba.fastjson.JSONObject;
import com.cfpamf.common.ms.exception.MSBizNormalException;
import com.cfpamf.ms.insur.operation.base.annotaions.ResponseDecorated;
import com.cfpamf.ms.insur.operation.base.constant.BaseConstants;
import com.cfpamf.ms.insur.operation.fegin.bms.enums.ExcptEnum;
import com.cfpamf.ms.insur.operation.honor.dto.*;
import com.cfpamf.ms.insur.operation.honor.enums.EnumHonorLevel;
import com.cfpamf.ms.insur.operation.honor.po.HonorsRulesConfigurationPo;
import com.cfpamf.ms.insur.operation.honor.query.HonorIconResultQuery;
import com.cfpamf.ms.insur.operation.honor.service.HonorsRulesConfigurationService;
import com.cfpamf.ms.insur.operation.honor.service.HonorsSelectionResultsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

@Slf4j
@Api(value = "荣誉看板", tags = {"荣誉看板"})
@RequestMapping(value = {"assistant/honor"})
@ResponseDecorated
@RestController
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class HonorAssistantController {
    HonorsSelectionResultsService honorsSelectionResultsService;
    HonorsRulesConfigurationService honorRulesConfigurationService;
    @ApiOperation(value = "首页获取荣誉图标结果")
    @PostMapping("/rules/iconHomePage")
    public List<HonorIconResultDto> getIconHomePage(@RequestBody HonorIconResultQuery query) {

        return honorsSelectionResultsService.getResultsByLevelAndCode(query);
    }
    @ApiOperation(value = "获取已获得荣誉图标结果",notes = "已获得列表需要传参level，bchCode、areaCode、empCode中的一项和")
    @PostMapping("/rules/honorListPageIcon/Obtained")
    public List<HonorIconResultDto> honorListPageIconObtained(@RequestBody HonorIconResultQuery query) {
        if(StringUtils.isBlank(query.getLevel())){
            log.warn("参数错误：{}", JSONObject.toJSONString(query));
            throw new MSBizNormalException(ExcptEnum.PARAMS_ERROR.getCode(),"评选对象不能为空");
        }
        query.setState(Collections.singletonList(2));
        query.setIsObtained(1);
        return honorsSelectionResultsService.getResultsByLevelAndCode(query);
    }

    @ApiOperation(value = "获取评选中荣誉图标结果",notes = "需要传参level和bchCode、areaCode、empCode中的一种")
    @PostMapping("/rules/honorListPageIcon/inAction")
    public List<HonorIconResultDto> honorListPageIconInAction(@RequestBody HonorIconResultQuery query) {
        if(StringUtils.isBlank(query.getLevel())){
            log.warn("参数错误：{}", JSONObject.toJSONString(query));
            throw new MSBizNormalException(ExcptEnum.PARAMS_ERROR.getCode(),"评选对象不能为空");
        }
        query.setState(Collections.singletonList(1));
        return honorsSelectionResultsService.getResultsByLevelAndCode(query);
    }

    @ApiOperation(value = "获取历史荣誉图标结果",notes = "已获得列表需要传参level和bchCode、areaCode、empCode中的一种")
    @PostMapping("/rules/honorListPageIcon/history")
    public List<HonorIconResultDto> honorListPageIconHistory(@RequestBody HonorIconResultQuery query) {
        if(StringUtils.isBlank(query.getLevel())){
            log.warn("参数错误：{}", JSONObject.toJSONString(query));
            throw new MSBizNormalException(ExcptEnum.PARAMS_ERROR.getCode(),"评选对象不能为空");
        }
        query.setState(Arrays.asList(1,2));
        return honorsSelectionResultsService.getResultsByLevelAndCode(query);
    }
    /**
     * 获取评选中的荣誉列表
     * 【接口说明】：本接口用于查询当前评选中的荣誉列表，支持根据不同条件筛选查询
     * 【请求参数】：需要传入评选的ID，以及可选的level、bchCode、areaCode、empCode等参数进行筛选
     * 【返回数据】：返回一个HonorResultDto对象，包含评选中的荣誉列表和评选的详细配置信息
     * 【备注】：该接口主要用于支持前端展示评选中的荣誉列表，以及相关筛选和排序功能
     */
    @ApiOperation(value = "获取评选中的荣誉列表",notes = "已获得列表需要传参level和bchCode、areaCode、empCode中的一种")
    @PostMapping("/rules/searchHonorInAction")
    public HonorResultDto searchHonorInAction(@RequestBody HonorIconResultQuery query) {
        // 根据传入的评选ID获取评选的详细配置信息
        HonorRulesConfigurationDto dto =  honorRulesConfigurationService.getRulesDetailById(query.getId());
        // 创建一个荣誉计算规则对象，并将配置信息复制到该对象中
        HonorCalculateRule rule = new HonorCalculateRule();
        BeanUtils.copyProperties(dto,rule);
        // 如果评选状态为1（例如：表示评选中），设置预选数量到规则对象中
        if(dto.getState() == 1){
            rule.setSelectionNumber(dto.getPreSelectionNumber());
        }
        // 设置评选规则适用的范围代码，这里使用查询对象中的代码
        rule.setCodes(Collections.singletonList(getCodes(query)));
        rule.setNames(Collections.singletonList(getNames(query)));
        // 设置是否是全局视角查询
        rule.setIsGlobalView(query.getIsGlobalView());
        // 调用服务查询评选中的荣誉列表
        HonorResultDto result = honorsSelectionResultsService.searchHonorInAction(rule);
        // 将评选的详细配置信息设置到结果对象中
        result.setHonorRulesConfiguration(dto);
        // 如果荣誉列表为空，或者查询是全局视角，则直接返回结果，不进行差距计算
        if(CollectionUtils.isEmpty(result.getResults())||query.getIsGlobalView()){
            return result;
        }
        // 调用服务计算评选中的荣誉的差距，并更新到结果对象中
        honorsSelectionResultsService.calcGapForHonorInAction(result);
        // 返回最终的查询结果
        return result;
    }



    /**
     * 获取评选结束的荣誉列表
     * <p>
     * 该方法用于根据评选规则ID获取已评选结束的荣誉列表，支持不同级别、部门或地区的查询
     * 需要传入评选规则ID（query.getId()）以及指定的查询条件，如level和bchCode、areaCode、empCode等
     * </p>
     *
     * @param query 荣誉图标结果查询对象，包含评选规则ID和其他筛选条件
     * @return HonorResultDto 荣誉结果数据传输对象，包含荣誉列表和评选规则详情
     */
    @ApiOperation(value = "获取评选结束的荣誉列表",notes = "已获得列表需要传参level和bchCode、areaCode、empCode中的一种")
    @PostMapping("/rules/searchHonorResult")
    public HonorResultDto searchHonorResult(@RequestBody HonorIconResultQuery query) {
        // 根据查询条件中的规则ID获取规则详情
        HonorRulesConfigurationDto dto =  honorRulesConfigurationService.getRulesDetailById(query.getId());

        // 创建一个荣誉计算规则对象，并从规则配置中复制属性
        HonorCalculateRule rule = new HonorCalculateRule();
        BeanUtils.copyProperties(dto,rule);

        // 设置规则中的编码和名称列表，以及是否是全局视图
        rule.setCodes(Collections.singletonList(getCodes(query)));
        rule.setNames(Collections.singletonList(getNames(query)));
        rule.setIsGlobalView(query.getIsGlobalView());

        // 使用配置的规则获取荣誉评选结果
        HonorResultDto result = honorsSelectionResultsService.getResultsByConfigurationIdForAssistant(rule);

        // 将评选规则详情添加到结果中，并返回结果对象
        result.setHonorRulesConfiguration(dto);
        return result;
    }


    @ApiOperation(value = "获取分支员工评选荣誉列表",notes = "bchCode必填")
    @PostMapping("/rules/getEmpHonorsForBch")
    public List<HonerIconResultPerEmpDTO> getEmpHonorsForBch(@RequestBody HonorIconResultQuery query){
        if(StringUtils.isBlank(query.getBchCode())){
            log.warn("参数错误：{}", JSONObject.toJSONString(query));
            throw new MSBizNormalException(ExcptEnum.PARAMS_ERROR.getCode(),"分支编码不能为空");
        }
        return honorsSelectionResultsService.getEmpHonorsForBch(query);
    }


    private String getCodes(HonorIconResultQuery query) {
        if (EnumHonorLevel.AREA.getCode().equals(query.getLevel())) {
            return query.getAreaCode();
        }
        if (EnumHonorLevel.BCH.getCode().equals(query.getLevel())) {
            return query.getBchCode();
        }
        if (EnumHonorLevel.EMP.getCode().equals(query.getLevel())) {
            return query.getEmpCode();
        }
        return "";
    }

    private String getNames(HonorIconResultQuery query) {
        if (EnumHonorLevel.AREA.getCode().equals(query.getLevel())) {
            return query.getAreaName();
        }
        if (EnumHonorLevel.BCH.getCode().equals(query.getLevel())) {
            return query.getBchName();
        }
        if (EnumHonorLevel.EMP.getCode().equals(query.getLevel())) {
            return query.getEmpName();
        }
        return "";
    }
}
