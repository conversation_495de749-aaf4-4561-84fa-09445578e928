package com.cfpamf.ms.insur.operation.aicall.dto;

import com.cfpamf.ms.insur.operation.aicall.enums.AIRecordRoleEnums;
import com.cfpamf.ms.insur.operation.base.po.BasePO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;
import org.apache.commons.lang.StringUtils;
import springfox.documentation.spring.web.json.Json;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * 会话记录内容
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class SessionRecordDto extends BasePO {

    String role;

    @ApiModelProperty(name="会话角色，分为机器人侧（speech）和客户侧（voice）")
    String roleName;

    public String getRoleName() {
        if (!StringUtils.isEmpty(role)) {
            return AIRecordRoleEnums.dict(role);
        }
        return null;
    }

    @ApiModelProperty(name="会话时间戳，微秒")
    Long timestamp;

    @ApiModelProperty(name="会话文本")
    String contextText;

    @ApiModelProperty(name="意图")
    String intent;

    @ApiModelProperty(name = "片段开始时间，基于对话开始的相对时间 00:00.310")
    String start;

    @ApiModelProperty(name="片段结束时间，基于对话开始的相对时间 00:00.310")
    String stop;

    @ApiModelProperty(name="片段时长，毫秒")
    Long timeLen;
}
