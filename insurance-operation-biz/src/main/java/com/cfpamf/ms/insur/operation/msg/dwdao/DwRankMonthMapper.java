package com.cfpamf.ms.insur.operation.msg.dwdao;

import com.cfpamf.ms.insur.operation.msg.pojo.query.StataRankQuery;
import com.cfpamf.ms.insur.operation.msg.pojo.vo.StatRankVO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <AUTHOR> 2021/7/27 16:20
 */
@Mapper
public interface DwRankMonthMapper {

    /**
     * 区域排名
     * @param query
     * @return
     */
    List<StatRankVO> listRankArea(StataRankQuery query);

    /**
     * 分支排名
     * @param query
     * @return
     */
    List<StatRankVO> listRankBch(StataRankQuery query);

    /**
     * 员工排名
     * @param query
     * @return
     */
    List<StatRankVO> listRankEmp(StataRankQuery query);

}

