package com.cfpamf.ms.insur.operation.claim.service;

import com.alibaba.fastjson.JSONObject;
import com.cfpamf.ms.insur.operation.aicall.event.AiCallBackEvent;
import com.cfpamf.ms.insur.operation.base.event.BaseEventHandler;
import com.cfpamf.ms.insur.operation.claim.dao.ClaimTaskCallbackMapper;
import com.cfpamf.ms.insur.operation.claim.dao.PolicyNoticeTaskDao;
import com.cfpamf.ms.insur.operation.claim.dto.ClaimTaskCallback;
import com.cfpamf.ms.insur.operation.claim.dto.PolicyNoticeTask;
import com.google.common.eventbus.Subscribe;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@Slf4j
@AllArgsConstructor
public class ClaimAiCallBackHandler implements BaseEventHandler {
    @Autowired
    PolicyNoticeTaskDao policyNoticeTaskDao;

    @Autowired
    ClaimTaskCallbackMapper claimTaskCallbackMapper;

    @Subscribe
    public void handlerEvent(AiCallBackEvent event){
        if("CLAIM_NOTICE".equals(event.getTaskType())){
            log.info("AiCallBackEvent事件触发：{}", JSONObject.toJSONString(event));
            PolicyNoticeTask task = policyNoticeTaskDao.selectById(event.getOutSourceId());
            ClaimTaskCallback claimTaskCallback = new ClaimTaskCallback();
            BeanUtils.copyProperties(task, claimTaskCallback);
            claimTaskCallback.setGiftInsuranceWillingnes(convertIntent(event.getIntent()));
            String individualTag = event.getIndividualTag();
            parseClaimTaskCallback(individualTag, claimTaskCallback);
            claimTaskCallbackMapper.insert(claimTaskCallback);
        }
    }
    public void parseClaimTaskCallback(String input, ClaimTaskCallback claimTaskCallback) {
        if (StringUtils.isBlank(input)){
            return;
        }
        // 分割字符串
        String[] parts = input.split(",");
        for (String part : parts) {
            String[] keyValue = part.split(":");
            if (keyValue.length != 2) {
                continue; // 跳过格式不正确的部分
            }
            String key = keyValue[0].trim();
            String value = keyValue[1].trim();

            switch (key) {
                case "评分":
                    claimTaskCallback.setClaimExperienceSatisfactionScore(value);
                    break;
                case "满意度":
                    claimTaskCallback.setClaimExperienceSatisfaction(value);
                    break;
                case "收费":
                    claimTaskCallback.setUnreasonableFeeFlag(value);
                    break;
                default:
                    // 处理其他字段（如果需要）
                    break;
            }
        }
    }

    /**
     * A 高意向    愿意领取 肯定回答
     * B 较高意向  未明确是否愿意领取或 主动询问业务问题
     * C 未明确意向  兜底例如开场挂断，语音助手等
     * D 拒绝     明确拒绝领取赠险
     * E 投诉     投诉/辱骂
     * @param intentTag
     * @return
     */
    private String convertIntent(String intentTag) {
        switch (intentTag){
            case "A":
                return "高意向";
            case "B":
                return "较高意向";
            case "C":
                return "未明确意向";
            case "D":
                return "拒绝";
            case "E":
                return "投诉";
            default:
                return intentTag;
        }
    }
}
