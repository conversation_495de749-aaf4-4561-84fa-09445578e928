package com.cfpamf.ms.insur.operation.reward.redenvelope.po.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.OrderBy;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2022/1/4 18:20
 */
@Data
@ApiModel(value = "红包识图对象")
public class RedEnvelopeVo {
    /**
     * 用户id
     */
    @ApiModelProperty(value = "用户id")
    private String userId;

    /**
     * 发送状态1：未拆 2：已拆
     */
    @ApiModelProperty(value = "状态1：未拆 2：已拆")
    private String sendState;

    /**
     * 金额
     */
    @ApiModelProperty(value = "金额")
    private BigDecimal amount;

    /**
     * 数据标识 如订单号，保单号，用户id
     */
    @ApiModelProperty(value = "数据标识 如订单号，保单号，用户id")
    private String dataId;

    /**
     * 数据类型 如USER_ID,ORDER_ID，POLICY_NO
     */
    @ApiModelProperty(value = "数据类型 如USER_ID,ORDER_ID,POLICY_NO")
    private String dataType;

    /**
     * 主键id
     */
    @Id
    protected Long id;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;


    /**
     * 发送时间
     */
    @ApiModelProperty("发送时间")
    private LocalDateTime sendTime;

    @ApiModelProperty(value = "活动id")
    private Long saId;
}
