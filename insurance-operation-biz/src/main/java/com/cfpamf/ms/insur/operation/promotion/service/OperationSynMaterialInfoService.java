package com.cfpamf.ms.insur.operation.promotion.service;

import com.cfpamf.ms.insur.operation.promotion.entity.OperationSynMaterialInfoEntity;
import org.apache.ibatis.annotations.Param;

/**
 * 同步素材圈信息表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-04-29 19:33:41
 */
public interface OperationSynMaterialInfoService{

    OperationSynMaterialInfoEntity selectByMaterialCodeSystemCode(String materialCode , String sourceSystemCode, Integer materialCategory, Integer status);

    Long queryLinkIdByProductCode(String productCode);

    Long queryLinkIdByMaterialCategory(String materialLevelCode);

    Integer logicDelete(String materialCode , String sourceSystemCode);

    Long queryDefaultLinkId(String materialLevelCode);
}

