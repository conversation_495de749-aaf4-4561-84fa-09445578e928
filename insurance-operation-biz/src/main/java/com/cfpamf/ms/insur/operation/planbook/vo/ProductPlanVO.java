package com.cfpamf.ms.insur.operation.planbook.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/12/8 11:21
 * @Version 1.0
 */
@Data
public class ProductPlanVO {
    private Integer productId;

    /**
     * 内部产品计划Id
     */
    @ApiModelProperty("内部产品计划Id")
    private Integer planId;

    /**
     * 计划名称
     */
    @ApiModelProperty("计划名称")
    private String planName;

    /**
     * 计划描述
     */
    @ApiModelProperty("计划描述")
    private String description;

    /**
     * 微信下单时传的第三方产品Id
     */
    @ApiModelProperty("微信下单时传的第三方产品Id")
    private String fhProductId;

    /**
     * 计划见费出单(seeFee)/非见费出单(nonSeefee)
     */
    @ApiModelProperty("计划见费出单(seeFee)/非见费出单(nonSeefee)")
    private String planOrderOutType;

    /**
     * 计划编码
     */
    @ApiModelProperty(value = "计划编码 ")
    private String planCode;

    /**
     * 计划类型 DEFAULT_TYPE-缺省类型,QUOTA-定额计划,NON_QUOTA-非定额计划
     */
    @ApiModelProperty(value = "计划类型 DEFAULT_TYPE-缺省类型,QUOTA-定额计划,NON_QUOTA-非定额计划")
    private String planType;

    /**
     * 最低保费
     */
    @ApiModelProperty(value = "最低保费")
    private BigDecimal minPremium;

    /**
     * 包含险种
     */
    @ApiModelProperty(value = "包含险种")
    private List<SmPlanRiskVO> planRiskList;
}
