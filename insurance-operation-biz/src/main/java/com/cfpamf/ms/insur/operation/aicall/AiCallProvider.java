package com.cfpamf.ms.insur.operation.aicall;

import com.alibaba.fastjson.JSON;
import com.cfpamf.ms.insur.operation.aicall.dao.RobortConfigurationMapper;
import com.cfpamf.ms.insur.operation.aicall.entity.RobortConfiguration;
import com.cfpamf.ms.insur.operation.aicall.exceptions.CreateCallTaskException;
import com.cfpamf.ms.insur.operation.aicall.exceptions.ImportPhoneException;
import com.cfpamf.ms.insur.operation.aicall.exceptions.UpdateCallTaskStatusException;
import com.cfpamf.ms.insur.operation.aicall.service.CallServer;
import com.cfpamf.ms.insur.operation.aicall.vo.AiCallTask;
import com.cfpamf.ms.insur.operation.aicall.vo.AiCallTaskDetail;
import com.cfpamf.ms.insur.operation.aicall.vo.CreateCallTaskReturn;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;

/***
 * <AUTHOR>
 */
@Component
@Slf4j(topic = "AiCallProvider")
public class AiCallProvider  {
    @Autowired
    ApplicationContext applicationContext;

    @Autowired
    RobortConfigurationMapper robortConfigurationMapper;

    public CreateCallTaskReturn createAiCallTask(AiCallTask aiCallTask) throws CreateCallTaskException, UpdateCallTaskStatusException {
        CreateCallTaskReturn createCallTaskReturn = new CreateCallTaskReturn();
        RobortConfiguration robortConfiguration = new RobortConfiguration();
        robortConfiguration.setTaskType(aiCallTask.getTaskType());
        CallServer callServer = null;
        List<RobortConfiguration> robortConfigurationList=robortConfigurationMapper.select(robortConfiguration);
        if(!CollectionUtils.isEmpty(robortConfigurationList)){
            callServer = (CallServer) applicationContext.getBean(robortConfigurationList.get(0).getAiProvider());
        }
        if(callServer==null){
            createCallTaskReturn.setProcessStatus(CreateCallTaskReturn.FAIL);
            createCallTaskReturn.setFailReason("找不到AI服务商");
            return createCallTaskReturn;
        }

        List<AiCallTaskDetail> failList = callServer.createAiCallTask(aiCallTask,robortConfigurationList);
        createCallTaskReturn.setFailList(failList);
        createCallTaskReturn.setProcessStatus(CollectionUtils.isEmpty(failList)?CreateCallTaskReturn.SUCCESS:CreateCallTaskReturn.PARTAILLY_SUCCESS);

        if(!CollectionUtils.isEmpty(failList)&&failList.size()==aiCallTask.getAiCallTaskDetailList().size()){
            createCallTaskReturn.setProcessStatus(CreateCallTaskReturn.FAIL);
            createCallTaskReturn.setFailReason("通话记录全部导入失败");
        }
        return createCallTaskReturn;
    }
    public Map<String,String> getRecordFile(String contactUuid, String taskType){
        CreateCallTaskReturn createCallTaskReturn = new CreateCallTaskReturn();
        RobortConfiguration robortConfiguration = new RobortConfiguration();
        robortConfiguration.setTaskType(taskType);
        CallServer callServer = null;
        List<RobortConfiguration> robortConfigurationList=robortConfigurationMapper.select(robortConfiguration);
        if(CollectionUtils.isEmpty(robortConfigurationList)){
            log.warn("获取录音文件失败,未匹配到机器人，参数：contactUuid={},taskType={}",contactUuid,taskType);
            return null;
        }

        callServer = (CallServer) applicationContext.getBean(robortConfigurationList.get(0).getAiProvider());
        String result = callServer.getRecordFileUrl(contactUuid);
        if(StringUtils.isBlank(result)){
            log.warn("获取录音文件失败");
            return null;
        }
        Map<String,String> resultMap = JSON.parseObject(result,Map.class);
        return resultMap;
    }
}
