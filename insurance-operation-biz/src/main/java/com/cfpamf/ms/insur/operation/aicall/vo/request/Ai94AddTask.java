package com.cfpamf.ms.insur.operation.aicall.vo.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class Ai94AddTask {

    @ApiModelProperty(value = "本地任务Id", required = true)
    private Long taskId;
    @ApiModelProperty(value = "任务类型", required = true)
    private String taskType;

    @ApiModelProperty(value = "任务名称，不超过100个字符", required = true)
    private String name;

    @ApiModelProperty(value = "任务启动日期，格式为[年-月-日]，不传则立即外呼", required = false)
    private String startTime;

    @ApiModelProperty(value = "话术模板类型，1：单模版；2：智能话术策略模板，默认为1", required = false)
    private Integer templateType;

    @ApiModelProperty(value = "话术模板id", required = false)
    private Long templateId;

    @ApiModelProperty(value = "并发数，默认为1，不可超过企业设置最高并发数", required = false)
    private Integer maxConcurrency;

    @ApiModelProperty(value = "重呼配置，可选值：1，2，3，默认为1；1：不重呼；2：间隔重呼；3：定时重呼", required = false)
    private Integer recallType;

    @ApiModelProperty(value = "外呼时间段，格式为二维列表，如[[8.5,12][13.5,19]]，默认为[[8,20]]", required = false)
    private List<List<Float>> callTime;

    @ApiModelProperty(value = "重呼间隔，当重呼选项为2时，必填；单位为分钟，支持≥1的正整数", required = true)
    private Integer repeatInterval;

    @ApiModelProperty(value = "重呼次数，当重呼选项为2时，必填；支持≥1的正整数", required = true)
    private Integer repeatCount;

    @ApiModelProperty(value = "重呼时间，当重呼选项为3时，必填；格式为[\"8:00\",\"9:00\"]，多个时间点用逗号隔开", required = true)
    private List<String> repeatTimes;

    @ApiModelProperty(value = "录音地址，当企业开通业务类型是语音通知时，必填", required = true)
    private String recordPath;

    @ApiModelProperty(value = "播放间隔时长，当企业开通业务类型是语音通知时，必填；单位为秒，支持输入≥0的正整数", required = true)
    private Integer playSleepVal;

    @ApiModelProperty(value = "录音播放次数，当企业开通业务类型是语音通知时，必填；支持输入≥0的正整数", required = true)
    private Integer playTimes;

    @ApiModelProperty(value = "短信发送规则，约定挂机短信的发送规则", required = false)
    private List<Ai94AddTaskSmsPlan> sendSmsPlan;

    @ApiModelProperty(value = "重呼条件，可多选，不传时默认条件全部", required = false)
    private List<Integer> repeatReason;

    @ApiModelProperty(value = "挂断回调url地址", required = false)
    private String callbackUrl;

    @ApiModelProperty(value = "任务状态 修改任务状态，2：开启；4：停止；修改任务时使用，一般用来启动外呼任务", required = false)
    private Integer status;
}
