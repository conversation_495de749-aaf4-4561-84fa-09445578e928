package com.cfpamf.ms.insur.operation.phoenix.pojo.query;

import com.cfpamf.ms.insur.operation.base.form.DataAuthPageForm;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class PhoenixEmpTodoBackQuery extends DataAuthPageForm {

    String state;

    String customerName;

    String customerAdminName;
    @ApiModelProperty(value = "业务类型；断保：INTERRUPTION，续投：RENEW_SHORT，续期：RENEW_LONG ，异业转化：LOAN")
    String bizType;

    @ApiModelProperty(value = "断保任务开始时间")
    String startDate;
    @ApiModelProperty(value = "断保任务结束时间")
    String endDate;
    @ApiModelProperty(value = "计划开始时间")
    String startPlanDate;
    @ApiModelProperty(value = "计划结束时间")
    String endPlanDate;
    @ApiModelProperty(value = "完成任务开始时间")
    String startFinishDate;
    @ApiModelProperty(value = "完成任务结束时间")
    String endFinishDate;
    @ApiModelProperty("机器人意向")
    String intent;
    @ApiModelProperty("机器人意向")
    List<String> intents;
}
