package com.cfpamf.ms.insur.operation.pco.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * PCO等级调整实体
 */
@Data
public class PcoLevelChangeRecord {
    /**
     * pco级别
     */
    @ApiModelProperty("PCO级别 C B A S")
    private String pcoLevel;

    /**
     * 线上考试得分
     */
    @ApiModelProperty("线上考试得分")
    private Integer onlineScore;

    /**
     * 线下综合得分
     */
    @ApiModelProperty("线下综合得分")
    private Integer offlineScore;

    /**
     * 季度人均业绩
     */
    @ApiModelProperty("季度人均业绩")
    private BigDecimal performance;

    /**
     * 是否胜任
     */
    @ApiModelProperty("是否胜任 0-完全胜任 1-基本胜任 2-不胜任")
    private Integer beQualified;

    /**
     * 理赔审核及时率
     */
    @ApiModelProperty("理赔审核及时率")
    private String claimTimelinessRatio;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "调整时间")
    private LocalDateTime createTime;
}
