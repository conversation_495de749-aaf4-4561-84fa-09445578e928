package com.cfpamf.ms.insur.operation.base.vo;

import com.cfpamf.ms.insur.operation.base.util.Base64Util;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.nio.charset.StandardCharsets;
import java.util.Date;

/**
 * 微信用户vo
 *
 * <AUTHOR>
 */
@Data
@ApiModel
public class WxUserVO implements Serializable {

    /**
     * id
     */
    @ApiModelProperty(value = "id")
    private Integer id;

    /**
     * 分支路径
     */
    @ApiModelProperty(value = "分支路径")
    private String orgPath;

    /**
     * 分支
     */
    @ApiModelProperty(value = "分支")
    private String regionName;

    /**
     * 分支机构树区域名称
     */
    @ApiModelProperty(value = "分支机构树区域名称")
    private String branchRegionName;
    /**
     * 机构编码
     */
    @ApiModelProperty(value = "机构编码")
    private String orgCode;
    /**
     * 机构id
     */
    @ApiModelProperty(value = "机构id")
    private String hrOrgId;

    /**
     * 机构
     */
    @ApiModelProperty(value = "机构")
    private String organizationName;

    /**
     * 机构
     */
    @ApiModelProperty(value = "机构")
    private String organizationFullName;

    /**
     * 授权用户工号
     */
    @ApiModelProperty(value = "授权用户工号")
    private String userId;

    /**
     * 代理人Id
     */
    @ApiModelProperty(value = "代理人Id")
    private Integer agentId;

    /**
     * 代理团队Id
     */
    @ApiModelProperty(value = "代理团队Id")
    private Integer teamId;

    /**
     * 代理团队名称
     */
    @ApiModelProperty(value = "代理团队名称")
    private String teamName;

    /**
     * 代理人最上级员工工号
     */
    @ApiModelProperty(value = "代理人最上级员工工号")
    private String agentTopUserId;

    /**
     * 授权用户名
     */
    @ApiModelProperty(value = "授权用户名")
    private String userName;

    /**
     * 授权用户名
     */
    @ApiModelProperty(value = "证件号码")
    private String userIdCard;

    /**
     * 用户种类
     */
    @ApiModelProperty(value = "用户种类")
    private String userType;

    /**
     * 授权用户名手机号
     */
    @ApiModelProperty(value = "授权用户名手机号")
    private String userMobile;

    /**
     * 授权用户名邮箱
     */
    @ApiModelProperty(value = "授权用户名邮箱")
    private String userEmail;

    /**
     * 微信openid
     */
    @ApiModelProperty(value = "微信openid")
    private String wxOpenId;

    /**
     * 微信名称
     */
    @ApiModelProperty(value = "微信名称")
    private String wxNickName;

    /**
     * 微信头像url
     */
    @ApiModelProperty(value = "微信头像url")
    private String wxImgUrl;

    /**
     * 内部员工状态
     */
    @ApiModelProperty(value = "内部员工状态")
    private String status;

    /**
     * 员工岗位
     */
    @ApiModelProperty(value = "员工岗位")
    private String postCode;

    /**
     * 是否身份证验证
     */
    @ApiModelProperty(value = "是否身份证验证")
    private Boolean realVerify;

    /**
     * 切换组织时间
     */
    @ApiModelProperty(value = "切换组织时间")
    private Date switchTime;

    /**
     * 任职类型 serviceType 0 主职  1兼职
     */
    @ApiModelProperty(value = "任职类型 0 主职  1兼职")
    private Integer serviceType;

    /**
     * 任职唯一识别编码
     */
    @ApiModelProperty(value = "任职唯一识别编码")
    private String jobCode;
    /**
     * 任职唯一识别编码
     */
    @ApiModelProperty(value = "主职工号")
    private String mainJobNumber;

    @ApiModelProperty("业务推荐码")
    private String bizCode;

    /**
     * 覆盖get方法进行微信名称base64解码
     *
     * @return
     */
    public String getWxNickName() {
        if (wxNickName == null) {
            return null;
        }
        if (Base64Util.isEncrypt(wxNickName)) {
            return new String(Base64Util.decryptBase64(wxNickName), StandardCharsets.UTF_8);
        }
        return wxNickName;
    }
}
