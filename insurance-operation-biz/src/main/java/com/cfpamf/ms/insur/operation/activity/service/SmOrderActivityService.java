package com.cfpamf.ms.insur.operation.activity.service;

import com.cfpamf.ms.insur.operation.activity.entity.SmOrderActivity;
import com.cfpamf.ms.insur.operation.activity.listener.GrantCouponsOrderMessage;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/7/14 11:29
 */
@Service
public interface SmOrderActivityService {
    /**
     * 通过奖励数和订单生成订单活动奖励数
     *
     * @param activityRewardMap   key为活动id value为奖券数
     * @param couponsOrderMessage
     */
    public void saveOrderActivity(Map<Long, Integer> activityRewardMap, GrantCouponsOrderMessage couponsOrderMessage);

    /**
     * 流水号为  fhOrderId+"-"+saId
     *
     * @param fhOrderId
     * @param saId
     * @return
     */
    public String getTrxNo(String fhOrderId, Long saId);

    /**
     * 流水号为  fhOrderId+"-"+saId +"-"+ no
     *
     * @param orderId
     * @param saId
     * @param no
     * @return
     */
    String getTrxNo(String orderId, Long saId, int no);

    /**
     * 保存集合
     *
     * @param orderActivityList
     */
    public void insertList(List<SmOrderActivity> orderActivityList);

    /**
     * 获取未发送的券
     *
     * @param saId
     * @return
     */
    public List<SmOrderActivity> getNotSendActivityOrder(Long saId);

    /**
     * 获取活动奖励券
     *
     * @param saId
     * @return
     */
    public List<SmOrderActivity> getSendActivityOrder(Long saId);


}
