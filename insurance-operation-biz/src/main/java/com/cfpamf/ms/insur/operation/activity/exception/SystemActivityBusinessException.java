package com.cfpamf.ms.insur.operation.activity.exception;

import com.cfpamf.common.ms.exception.MSBizNormalException;
import com.cfpamf.ms.insur.operation.base.constant.ExceptionConstant;

/**
 * 系统活动业务异常
 *
 * <AUTHOR>
 * @date 2021/7/6 17:43
 */
public class SystemActivityBusinessException extends MSBizNormalException {
    public SystemActivityBusinessException(String code, String msg) {
        super(code, msg);
    }


    public static SystemActivityBusinessException buildValidError(String msg) {
        return new SystemActivityBusinessException(ExceptionConstant.INSURANCE_OPERATION_ACTIVITY_ERROR_CODE + "099", msg);
    }

    public static final SystemActivityBusinessException SYSTEM_ACTIVITY_TIME_ERROR = new SystemActivityBusinessException(ExceptionConstant.INSURANCE_OPERATION_ACTIVITY_ERROR_CODE + "001", "结束时间需在开始时间后");
    public static final SystemActivityBusinessException SYSTEM_ACTIVITY_PRODUCT_OR_CONFLICT_RULE_PARAMS_NULL = new SystemActivityBusinessException(ExceptionConstant.INSURANCE_OPERATION_ACTIVITY_ERROR_CODE + "002", "【活动产品及奖励规则】不能为空");
    public static final SystemActivityBusinessException SYSTEM_ACTIVITY_ELS_NUMBER_OR_PASSWORD_PARAMS_NULL = new SystemActivityBusinessException(ExceptionConstant.INSURANCE_OPERATION_ACTIVITY_ERROR_CODE + "003", "奖券活动类型，会务系统活动编码和赠送口令不能为空");
    public static final SystemActivityBusinessException SYSTEM_ACTIVITY_REL_ACTIVITY_TYPE_ERROR = new SystemActivityBusinessException(ExceptionConstant.INSURANCE_OPERATION_ACTIVITY_ERROR_CODE + "004", "关联失败，关联活动和当前活动类型不一致");
    public static final SystemActivityBusinessException SYSTEM_ACTIVITY_PRODUCT_MUST_ONLINE = new SystemActivityBusinessException(ExceptionConstant.INSURANCE_OPERATION_ACTIVITY_ERROR_CODE + "005", "活动产品必须已上线在售");
    public static final SystemActivityBusinessException SYSTEM_ACTIVITY_NOT_EXIST = new SystemActivityBusinessException(ExceptionConstant.INSURANCE_OPERATION_ACTIVITY_ERROR_CODE + "006", "活动方案不存在");
    public static final SystemActivityBusinessException SYSTEM_ACTIVITY_NOT_ALLOW_UPDATE_TYPE = new SystemActivityBusinessException(ExceptionConstant.INSURANCE_OPERATION_ACTIVITY_ERROR_CODE + "007", "已有关联的活动，不能修改活动类型");
    public static final SystemActivityBusinessException SYSTEM_ACTIVITY_REWORD_TYPE_ERROR = new SystemActivityBusinessException(ExceptionConstant.INSURANCE_OPERATION_ACTIVITY_ERROR_CODE + "008", "活动类型和奖励类型不匹配");
    public static final SystemActivityBusinessException SYSTEM_ACTIVITY_REF_NOT_EXIST = new SystemActivityBusinessException(ExceptionConstant.INSURANCE_OPERATION_ACTIVITY_ERROR_CODE + "009", "关联活动方案不存在");
    public static final SystemActivityBusinessException SYSTEM_ACTIVITY_TIME_ERROR_NOT_ALLOW_UPDATE = new SystemActivityBusinessException(ExceptionConstant.INSURANCE_OPERATION_ACTIVITY_ERROR_CODE + "010", "当前活动状态，不能修改活动");
    public static final SystemActivityBusinessException SYSTEM_ACTIVITY_TIME_ERROR_NOT_ALLOW_ACTIVITY_FLAG = new SystemActivityBusinessException(ExceptionConstant.INSURANCE_OPERATION_ACTIVITY_ERROR_CODE + "011", "已过活动开始时间，不能启用活动");
    public static final SystemActivityBusinessException SYSTEM_ACTIVITY_RULE_CODE_NOT_EXIST = new SystemActivityBusinessException(ExceptionConstant.INSURANCE_OPERATION_ACTIVITY_ERROR_CODE + "012", "规则编码不存在");
    public static final SystemActivityBusinessException SYSTEM_ACTIVITY_ELS_NUMBER_INVALID = new SystemActivityBusinessException(ExceptionConstant.INSURANCE_OPERATION_ACTIVITY_ERROR_CODE + "013", "els会务系统活动编码无效");
    public static final SystemActivityBusinessException SYSTEM_ACTIVITY_OPERATION_ERROR = new SystemActivityBusinessException(ExceptionConstant.INSURANCE_OPERATION_ACTIVITY_ERROR_CODE + "014", "当前活动操作非法");

    public static final SystemActivityBusinessException SYSTEM_ACTIVITY_CONFIG_ERROR = new SystemActivityBusinessException(ExceptionConstant.INSURANCE_OPERATION_ACTIVITY_ERROR_CODE + "015", "当前活动配置异常");
    public static final SystemActivityBusinessException SYSTEM_ACTIVITY_CONFIG_COPY_ERROR = new SystemActivityBusinessException(ExceptionConstant.INSURANCE_OPERATION_ACTIVITY_ERROR_CODE + "016", "当前活动暂不支持复制");

}
