package com.cfpamf.ms.insur.operation.pco.util;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * <AUTHOR>
 */
@Target({ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
public @interface ExcelField {

    /**
     * excel 表头
     */
    String value() default "";

    /**
     * 别名:兼容多个excel模板表头字段
     * @return
     */
    String[] alias() default "";

    CellReader excelReder() default CellReader.STRING;

    int defaultIndex() default -1;

    boolean hidden() default false;

    int order() default -1;

}
