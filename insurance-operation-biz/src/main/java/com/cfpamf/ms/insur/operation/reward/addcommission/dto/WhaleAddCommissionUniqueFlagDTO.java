package com.cfpamf.ms.insur.operation.reward.addcommission.dto;

import com.cfpamf.ms.insur.operation.base.util.StringTypeUtils;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @date 2022/4/19 16:14
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
@NoArgsConstructor
@AllArgsConstructor
public class WhaleAddCommissionUniqueFlagDTO {



    /**
     * 保单号
     */
    @ApiModelProperty(value = "保单号")
    private String policyNo;

    /**
     * 订单id
     */
    @ApiModelProperty(value = "批单号")
    private String endorsementNo;

    /**
     * 被保人证件号
     */
    @ApiModelProperty(value = "被保人编码")
    private String insuredCode;

    /**
     * 计划id
     */
    @ApiModelProperty(value = "商品id")
    private String sellProductCode;

    /**
     * 险种id，险种不存在时默认-1
     */
    @ApiModelProperty(value = "险种id，险种不存在时默认-1")
    private String riskCode;

    /**
     * 险种id，险种不存在时默认-1
     */
    @ApiModelProperty(value = "险种状态")
    private String productStatus;

    /**
     * 期数
     */
    @ApiModelProperty(value = "期数")
    private Integer termNum;


    public WhaleAddCommissionUniqueFlagDTO(String policyNo,String endorsementNo, String insuredCode, String sellProductCode, String riskCode, String termNum,String productStatus) {
        this.policyNo = StringUtils.isNotBlank(policyNo)?policyNo:"";
        this.endorsementNo = StringUtils.isNotBlank(endorsementNo)?endorsementNo:"";
        this.insuredCode = StringUtils.isNotBlank(insuredCode)?insuredCode:"";
        this.sellProductCode = StringUtils.isNotBlank(sellProductCode)?sellProductCode:"";
        this.riskCode = StringUtils.isNotBlank(riskCode)?riskCode:"";
        if (StringTypeUtils.isWholeNumber(termNum)) {
            this.termNum = Integer.valueOf(termNum);
        }
        if (StringUtils.isNotBlank(productStatus)) {
            this.productStatus = productStatus;
        }

    }
}
