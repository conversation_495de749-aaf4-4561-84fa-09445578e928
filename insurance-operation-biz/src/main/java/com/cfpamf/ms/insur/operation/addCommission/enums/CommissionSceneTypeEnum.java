package com.cfpamf.ms.insur.operation.addCommission.enums;

import com.cfpamf.ms.insur.operation.base.annotaions.Dictionary;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.experimental.FieldDefaults;

import java.util.Arrays;
import java.util.Objects;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 */
@Getter
@FieldDefaults(level = AccessLevel.PRIVATE)
@Dictionary(desc = "佣金刷数变更场景枚举")
public enum CommissionSceneTypeEnum {
    POLICY_NO("policyNo","保单号变更","policy_no"),
    ENDORSEMENT_NO("endorsementNo","批单号变更","policy_no"),
    COMMISSION_USER_ID("commissionUserId","推荐人变更","commission_user_id"),
    ACCOUNT_TIME("accountTime","记账时间","account_time"),
    PLAN_ID("planId","计划id","plan_id")
    ;
    String code;
    String desc;
    String fieldName;

    CommissionSceneTypeEnum(String code, String desc, String fieldName){
        this.code = code;
        this.desc = desc;
        this.fieldName = fieldName;
    }

    public boolean isMe(String code) {
        return Objects.equals(code, this.code);
    }

    public static String dict(String code) {
        return Stream.of(values()).filter(s -> s.code.equals(code))
                .findAny().map(CommissionSceneTypeEnum::getFieldName).orElse("");
    }

    /**
     * 根据编码获取理保全项目枚举
     *
     * @param code 材料编码
     * @return CommissionSceneTypeEnum
     */
    public static CommissionSceneTypeEnum decode(String code) {
        return Arrays.stream(CommissionSceneTypeEnum.values())
                .filter(x -> x.code.equals(code))
                .findFirst()
                .orElse(null);
    }
}
