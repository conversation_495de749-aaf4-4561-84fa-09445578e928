package com.cfpamf.ms.insur.operation.base.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cfpamf.common.ms.exception.MSBizNormalException;
import com.cfpamf.common.ms.result.Result;
import com.cfpamf.ms.insur.operation.auth.DataAuth;
import com.cfpamf.ms.insur.operation.auth.DataAuthFacade;
import com.cfpamf.ms.insur.operation.base.form.DataAuthPageForm;
import com.cfpamf.ms.insur.operation.base.response.CommonResult;
import com.cfpamf.ms.insur.operation.base.util.HttpRequestUtil;
import com.cfpamf.ms.insur.operation.fegin.bms.enums.ExcptEnum;
import com.cfpamf.ms.insur.operation.fegin.customer.facede.CustServiceFacade;
import com.cfpamf.ms.insur.operation.fegin.customer.request.CustBaseQueryByIdNoRequest;
import com.cfpamf.ms.insur.operation.fegin.customer.response.CustBaseInfoVo;
import com.google.gson.JsonObject;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Objects;


/**
 * <AUTHOR> 2022/9/8 11:34
 */
@Service
@AllArgsConstructor
@Slf4j
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class CustService {

    CustServiceFacade custServiceFacade;

    /**
     * 数据权限
     *
     * @param request 请求参数
     * @return CustBaseInfoVo
     */
    public CustBaseInfoVo baseInfoByIdNo(CustBaseQueryByIdNoRequest request) {
        Result<CustBaseInfoVo> custBaseInfoVoResult = custServiceFacade.baseInfoByIdNo(request);
        if (custBaseInfoVoResult.isSuccess()) {
            return custBaseInfoVoResult.getData();
        }
        log.warn("调用客户中心接口:" + JSON.toJSONString(custBaseInfoVoResult.getErrorContext()));
        return new CustBaseInfoVo();
    }
}
