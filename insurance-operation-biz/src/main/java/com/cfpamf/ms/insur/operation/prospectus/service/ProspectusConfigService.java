package com.cfpamf.ms.insur.operation.prospectus.service;

import com.cfpamf.ms.insur.operation.prospectus.form.ProspectusConfigForm;
import com.cfpamf.ms.insur.operation.prospectus.form.ProspectusConfigSearchForm;
import com.cfpamf.ms.insur.operation.prospectus.form.ProspectusProductSearchForm;
import com.cfpamf.ms.insur.operation.prospectus.vo.ProspectusConfigVo;
import com.cfpamf.ms.insur.operation.prospectus.vo.ProspectusProductVo;
import com.github.pagehelper.PageInfo;
import org.springframework.stereotype.Service;

/**
 * 计划书配置业务操作
 *
 * <AUTHOR>
 * @date 2021/5/19 14:51
 */
@Service
public interface ProspectusConfigService {

    /**
     * 保存计划书配置
     *
     * @param prospectusConfigForm
     */
    void save(ProspectusConfigForm prospectusConfigForm);

    /**
     * 编辑计划书配置
     *
     * @param id
     * @param prospectusConfigForm
     * @return
     */
    void update(Long id, ProspectusConfigForm prospectusConfigForm);

    /**
     * 查看计划书配置详情
     *
     * @param id
     * @return
     */
    ProspectusConfigVo getById(Long id);

    /**
     * 删除计划书配置
     *
     * @param id
     */
    void delete(Long id);

    /**
     * 搜索计划书配置详情
     *
     * @param prospectusConfigSearchForm
     * @return
     */
    PageInfo<ProspectusConfigVo> search(ProspectusConfigSearchForm prospectusConfigSearchForm);

    /**
     * 查看计划书配置详情
     *
     * @param productId
     * @return
     */
    ProspectusConfigVo getByProductId(Long productId);

    /**
     * 搜索计划书产品
     *
     * @param prospectusProductSearchForm
     * @return
     */
    public PageInfo<ProspectusProductVo> searchProspectusProduct(ProspectusProductSearchForm prospectusProductSearchForm);
}
