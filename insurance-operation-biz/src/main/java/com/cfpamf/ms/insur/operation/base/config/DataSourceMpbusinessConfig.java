package com.cfpamf.ms.insur.operation.base.config;

import com.alibaba.druid.pool.DruidDataSource;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.SqlSessionTemplate;
import org.mybatis.spring.annotation.MapperScan;
import org.mybatis.spring.boot.autoconfigure.MybatisProperties;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;

import javax.sql.DataSource;

/**
 *
 */
@Configuration
@MapperScan(basePackages = {
        "com.cfpamf.ms.insur.operation.xj.dao"
}, sqlSessionFactoryRef = "mpbusinessSqlSessionFactory")
@EnableConfigurationProperties(MybatisProperties.class)
public class DataSourceMpbusinessConfig {

    static final String MAPPER_LOCATION = "classpath:mapper/mpbusiness/*.xml";
    static final String PREFIX = "mpbusiness";
    final MybatisProperties properties;


    @Autowired(required = false)
    public DataSourceMpbusinessConfig(MybatisProperties properties) {
        this.properties = properties;
    }

    @ConfigurationProperties(prefix = "spring.datasource." + PREFIX)
    @Bean(name = PREFIX + "DataSource")
    public DataSource safespgDataSource() {
        return new DruidDataSource();
    }

    @Bean(name = PREFIX + "SqlSessionFactory")
    public SqlSessionFactory sqlSessionFactoryEngine(@Qualifier(PREFIX + "DataSource") DataSource dataSource) throws Exception {
        final SqlSessionFactoryBean sessionFactory = new SqlSessionFactoryBean();
        sessionFactory.setDataSource(dataSource);
        applyConfiguration(sessionFactory);
        Resource[] resources = new PathMatchingResourcePatternResolver().getResources(MAPPER_LOCATION);
        sessionFactory.setMapperLocations(resources);

        return sessionFactory.getObject();
    }

    @Bean(name = PREFIX + "DataSourceTransactionManager")
    public DataSourceTransactionManager dataSourceTransactionManagerEngine(@Qualifier(PREFIX + "DataSource") DataSource dataSource) {
        return new DataSourceTransactionManager(dataSource);
    }

    @Bean(name = PREFIX + "SqlSessionTemplate")
    public SqlSessionTemplate sqlSessionTemplateEngine(@Qualifier(PREFIX + "SqlSessionFactory") SqlSessionFactory sqlSessionFactory) throws Exception {
        return new SqlSessionTemplate(sqlSessionFactory);
    }

    private void applyConfiguration(SqlSessionFactoryBean factory) {
        org.apache.ibatis.session.Configuration configuration = new org.apache.ibatis.session.Configuration();
        configuration.setMapUnderscoreToCamelCase(true);
        factory.setConfiguration(configuration);
    }

    @Bean(name = PREFIX + "JdbcTemplate")
    public JdbcTemplate safeJdbcTemplate(@Qualifier(PREFIX + "DataSource") DataSource dataSource) {
        return new JdbcTemplate(dataSource);
    }
}
