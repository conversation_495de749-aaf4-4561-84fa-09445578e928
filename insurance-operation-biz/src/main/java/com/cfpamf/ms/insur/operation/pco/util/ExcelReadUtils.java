package com.cfpamf.ms.insur.operation.pco.util;

import com.cfpamf.common.ms.exception.MSBizNormalException;
import com.cfpamf.common.ms.exception.MSException;
import com.cfpamf.ms.insur.operation.fegin.bms.enums.ExcptEnum;
import com.monitorjbl.xlsx.StreamingReader;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.*;
import org.springframework.util.CollectionUtils;

import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import javax.validation.Validator;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;


/**
 * <AUTHOR>
 */
@Data
@Slf4j
@UtilityClass
public class ExcelReadUtils {

    private static final Validator VALIDATOR = Validation.buildDefaultValidatorFactory().getValidator();

    @Data
    @AllArgsConstructor
    static class ReadConfig {

        private String fieldName;

        private ExcelField field;

        private int order;

        private Method getter;

        private Method setter;

        Function<Cell, Object> reader;
    }

    private static final Map<String, List<ReadConfig>> CONFIG_MAP = new ConcurrentHashMap<>();

    private static List<ReadConfig> getConfigs(Class<?> clazz) {

        List<ReadConfig> result = CONFIG_MAP.get(clazz.getName());
        if (result == null) {
            AtomicInteger order = new AtomicInteger(1);
            result = Stream.of(clazz.getDeclaredFields())
                    .filter(field -> {
                        ExcelField annotation = field.getAnnotation(ExcelField.class);
                        return annotation != null && !annotation.hidden();
                    })
                    .map(field -> {
                        String pkName = field.getName();
                        try {
                            int defaultOrder = order.getAndIncrement();
                            ExcelField annotation = field.getAnnotation(ExcelField.class);
                            int orderVal = annotation.order() == -1 ? defaultOrder : annotation.order();
                            Method getter = clazz.getMethod("get" + StringUtils.capitalize(pkName));
                            Method setter = clazz.getMethod("set" + StringUtils.capitalize(pkName), field.getType());
                            //TODO other default
                            Function<Cell, Object> reader = annotation.excelReder() != CellReader.STRING ? annotation.excelReder() : findDefaultGet(field.getType());
                            return new ReadConfig(pkName, annotation, orderVal, getter, setter, reader);
                        } catch (NoSuchMethodException e) {
                            throw new UnsupportedOperationException("初始化模板对象失败", e);
                        }
                    })
                    .sorted(Comparator.comparing(ReadConfig::getOrder).thenComparing(ReadConfig::getFieldName))
                    .collect(Collectors.toList());
            CONFIG_MAP.put(clazz.getName(), result);
        }
        return result;
    }

    private static Function<Cell, Object> findDefaultGet(Class<?> type) {
        if (Integer.class.equals(type)) {
            return CellReader.INTEGER;
        }
        if (BigDecimal.class.equals(type)) {
            return CellReader.DECIMAL;
        }
        return CellReader.STRING;

    }

    public static <T> List<T> readWorkbook(InputStream is, Class<T> clazz) throws Exception {
        return readWorkbook(is, clazz, -1, false);
    }

    /**
     * 读取excel文件 分页读取
     *
     * @param clazz
     * @param startRow
     * @param <T>
     * @return
     * @throws Exception
     */
    public static <T> List<T> readWorkbookByUrl(String remoteUrl, Class<T> clazz,
                                                int startRow) throws Exception {
        return readWorkbookByUrl(remoteUrl, clazz, startRow, false);
    }

    /**
     * 读取excel文件 分页读取
     *
     * @param clazz
     * @param startRow
     * @param vaild
     * @param <T>
     * @return
     * @throws Exception
     */
    public static <T> List<T> readWorkbookByUrl(String remoteUrl, Class<T> clazz,
                                                int startRow, boolean vaild) throws Exception {
        if (StringUtils.isBlank(remoteUrl)) {
            return Collections.emptyList();
        }
        InputStream is = DownloadUtil.downloadByUrl(remoteUrl);
        return readWorkbookByStream(is, clazz, 0, startRow, vaild);
    }

    /**
     * 读取excel文件 分页读取
     *
     * @param is
     * @param clazz
     * @param sheetIndex
     * @param startRow
     * @param vaild
     * @param <T>
     * @return
     * @throws Exception
     */
    public static <T> List<T> readWorkbookByStream(InputStream is, Class<T> clazz,
                                                   int sheetIndex,
                                                   int startRow, boolean vaild) throws Exception {


        try (Workbook workbook = StreamingReader.builder()
                .rowCacheSize(100)
                .bufferSize(1024 * 8)
                .open(is)) {

            return getSheetData(clazz, sheetIndex, startRow, vaild, workbook);
        }
    }

    /**
     * 获取sheet页面数据
     *
     * @param clazz
     * @param sheetIndex
     * @param startRow
     * @param vaild
     * @param workbook
     * @param <T>
     */
    public static <T> List<T> getSheetData(Class<T> clazz,
                                           int sheetIndex,
                                           int startRow,
                                           boolean vaild,
                                           Workbook workbook) {
        List<T> res = new ArrayList<>();

        List<ReadConfig> configs = getConfigs(clazz);
        Sheet sheet = workbook.getSheetAt(sheetIndex);
        int firstRowNum = Math.max(startRow, 0);
        int rowIndex = 0;
        //如果有rowNum的配置
        Optional<ReadConfig> any = configs.stream().filter(config ->
                config.field.excelReder() == CellReader.ROW_NUM).findAny();
        Map<Integer, ReadConfig> indexConfigMap = new HashMap<>(16);
        for (Row row : sheet) {

            if (rowIndex == firstRowNum) {
                int cellIndex = 0;
                for (Cell cell : row) {
                    String cellValue = StringUtils.trim(cell.getStringCellValue());
                    Optional<ReadConfig> first = configs.stream()
                            .filter(config -> {
                                boolean match = config.getField().value().equals(cellValue);
                                if (match) {
                                    return match;
                                }
                                String[] alias = config.getField().alias();
                                if (alias == null) {
                                    return false;
                                }
                                return Arrays.stream(alias).anyMatch(
                                        a -> Objects.equals(a, cellValue)
                                );
                            })
                            .findFirst();
                    if (first.isPresent()) {
                        indexConfigMap.put(cellIndex, first.get());
                    } else if ((cellIndex + 1) < configs.size()) {
                        ReadConfig readConfig = configs.get(cellIndex + 1);
                        String fieldValue = readConfig.field.value();
                        if (StringUtils.isBlank(fieldValue) &&
                                !indexConfigMap.containsValue(readConfig)) {
                            indexConfigMap.put(cellIndex, readConfig);
                        }
                    }
                    cellIndex++;
                }
            }
            if (rowIndex > firstRowNum) {
                try {
                    AtomicBoolean allEmpty = new AtomicBoolean(true);
                    T t = clazz.newInstance();
                    // 如果有需要记录行好的属性
                    if (any.isPresent()) {
                        any.get().getSetter().invoke(t, row.getRowNum() + 1);
                    }
                    for (Cell cell : row) {

                        int columnIndex = cell.getColumnIndex();
                        ReadConfig readConfig = indexConfigMap.get(cell.getColumnIndex());
                        if (readConfig == null) {
                            continue;
                        }
                        Function<Cell, Object> cellReader = readConfig.getReader();

                        try {
                            Object apply = cellReader.apply(cell);
                            if (Objects.nonNull(apply)) {
                                allEmpty.set(false);
                                readConfig.getSetter().invoke(t, apply);
                            }

                        } catch (Exception e) {
                            throw new RuntimeException("为对象赋值失败【" + rowIndex + "," + columnIndex + "】", e);
                        }

                    }
                    //如果当前行所有数据都是空的 直接过滤掉
                    if (allEmpty.get()) {
                        continue;
                    }
                    res.add(t);

                    if (vaild) {
                        Set<ConstraintViolation<T>> validate = VALIDATOR.validate(t);
                        if (!CollectionUtils.isEmpty(validate)) {
                            String errorPoint = "第" + (rowIndex + 1) + "行";
                            String collect = validate.stream()
                                    .map(ConstraintViolation::getMessage)
                                    .collect(Collectors.joining(","));
                            throw new MSBizNormalException(ExcptEnum.PARAMS_ERROR.getCode(),
                                    "excel数据校验失败:" + errorPoint + "==>" + collect);

                        }
                    }
                } catch (MSException e) {
                    throw e;
                } catch (Exception e) {
                    throw new RuntimeException("初始化对象失败", e);
                }
            }
            rowIndex++;
        }
        return res;
    }

    /**
     * 读取excel文件 分页读取
     *
     * @param is
     * @param clazz
     * @param startRow
     * @param vaild
     * @param <T>
     * @return
     * @throws Exception
     */
    public static <T> List<T> readWorkbookByStream(InputStream is, Class<T> clazz,
                                                   int startRow, boolean vaild) throws Exception {
        return readWorkbookByStream(is, clazz, 0, startRow, vaild);
    }

    /**
     * 读取excel 并转换成对象
     *
     * @param is
     * @param clazz
     * @param startRow
     * @param vaild
     * @param <T>
     * @return
     * @throws IOException
     * @deprecated 这个方法会导致 无限fullGc最终内存溢出 程序假死（在excel有大量空行时）use {@link #readWorkbookByStream(InputStream, Class, int, boolean) instead}
     */
    @Deprecated
    public static <T> List<T> readWorkbook(InputStream is, Class<T> clazz, int startRow,
                                           boolean vaild) throws Exception {
        List<T> res = new ArrayList<>();

        try (Workbook workbook = WorkbookFactory.create(is)) {
            List<ReadConfig> configs = getConfigs(clazz);
            Sheet sheet = workbook.getSheetAt(0);
            int lastRowNum = sheet.getLastRowNum();
            int firstRowNum = startRow >= 0 ? startRow : sheet.getFirstRowNum();

            Row headRow = sheet.getRow(firstRowNum);
            if (headRow == null) {
                throw new MSBizNormalException(ExcptEnum.PARAMS_ERROR.getCode(), "模板文件格式错误");
            }
            int firstCellNum = headRow.getFirstCellNum();
            int lastCellNum = headRow.getLastCellNum();
            Map<Integer, ReadConfig> indexConfigMap = new HashMap<>(16);
            for (int i = firstCellNum; i < lastCellNum; i++) {

                String cellValue = headRow.getCell(i).getStringCellValue();
                Optional<ReadConfig> first = configs.stream().filter(config ->
                        config.getField().value().equals(StringUtils.trim(cellValue)))
                        .findFirst();
                if (first.isPresent()) {
                    indexConfigMap.put(i, first.get());
                } else if ((i + 1) < configs.size()) {
                    ReadConfig readConfig = configs.get(i + 1);
                    if (StringUtils.isBlank(readConfig.field.value()) &&
                            !indexConfigMap.containsValue(readConfig)) {
                        indexConfigMap.put(i, readConfig);
                    }
                }
            }
            //如果有rowNum的配置
            Optional<ReadConfig> any = configs.stream().filter(config ->
                    config.field.excelReder() == CellReader.ROW_NUM).findAny();
            for (int i = firstRowNum + 1; i <= lastRowNum; i++) {
                Row row = sheet.getRow(i);
                try {
                    AtomicBoolean allEmpty = new AtomicBoolean(true);
                    T t = clazz.newInstance();
                    // 如果有需要记录行好的属性
                    if (any.isPresent()) {
                        any.get().getSetter().invoke(t, row.getRowNum() + 1);
                    }
                    indexConfigMap.forEach((key, value) -> {
                        if (row == null) {
                            return;
                        }

                        Cell cell = row.getCell(key);
                        Function<Cell, Object> cellReader = value.getReader();
                        Object apply = cellReader.apply(cell);
                        try {
                            if (Objects.nonNull(apply)) {
                                allEmpty.set(false);
                                value.getSetter().invoke(t, apply);
                            }

                        } catch (Exception e) {
                            throw new RuntimeException("为对象赋值失败", e);
                        }
                    });
                    //如果当前行所有数据都是空的 直接过滤掉
                    if (allEmpty.get()) {
                        continue;
                    }
                    if (row != null) {
                        res.add(t);
                    }

                    if (vaild) {
                        Set<ConstraintViolation<T>> validate = VALIDATOR.validate(t);
                        if (!CollectionUtils.isEmpty(validate)) {
                            String errorPoint = "第" + (i + 1) + "行";
                            String collect = validate.stream()
                                    .map(ConstraintViolation::getMessage)
                                    .collect(Collectors.joining(","));
                            throw new MSBizNormalException(ExcptEnum.PARAMS_ERROR.getCode(),
                                    "excel数据校验失败:" + errorPoint
                                            + "==>" + collect);

                        }
                    }
                } catch (MSException e) {
                    throw e;
                } catch (Exception e) {
                    throw new RuntimeException("初始化对象失败", e);
                }
            }
        }

        return res;
    }

    public static <T> void write(OutputStream ps, List<T> list) throws IOException {
        if (CollectionUtils.isEmpty(list)) {
            log.info("过滤后需要输出的集合为空");
            return;
        }
        List<T> results = list.stream().filter(Objects::nonNull).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(results)) {
            log.info("过滤后需要输出的集合为空");
            return;

        }
        List<ReadConfig> configs = getConfigs(results.get(0).getClass());
        try (XSSFWorkbook workbook = new XSSFWorkbook()) {
            XSSFSheet sheet = workbook.createSheet();
            XSSFCellStyle cellStyle = workbook.createCellStyle();
            cellStyle.setAlignment(HorizontalAlignment.CENTER);
            cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            initStyle(cellStyle);
            XSSFFont font = workbook.createFont();
            font.setColor(Font.COLOR_NORMAL);
            font.setBold(true);
            cellStyle.setFont(font);
            XSSFRow row = sheet.createRow(0);
            for (int i = 0; i < configs.size(); i++) {
                XSSFCell cell = row.createCell(i, CellType.STRING);
                ReadConfig readConfig = configs.get(i);
                cell.setCellValue(readConfig.getField().value());
                sheet.setColumnWidth(i, 4000);
                cell.setCellStyle(cellStyle);
            }
            cellStyle = workbook.createCellStyle();
            initStyle(cellStyle);
            for (int i = 0; i < results.size(); i++) {
                XSSFRow vlRow = sheet.createRow(i + 1);
                T t = results.get(i);
                for (int j = 0; j < configs.size(); j++) {
                    XSSFCell cell = vlRow.createCell(j, CellType.STRING);
                    cell.setCellStyle(cellStyle);
                    ReadConfig readConfig = configs.get(j);
                    try {
                        Object invoke = readConfig.getGetter().invoke(t);
                        cell.setCellValue(invoke == null ? "" : String.valueOf(invoke));
                    } catch (Exception e) {
                        throw new RuntimeException("获取cell值失败");
                    }
                }
            }
            workbook.write(ps);
        }
    }

    private static void initStyle(XSSFCellStyle cellStyle) {
        cellStyle.setBorderRight(BorderStyle.THIN);
        cellStyle.setBorderLeft(BorderStyle.THIN);
        cellStyle.setBorderBottom(BorderStyle.THIN);
        cellStyle.setBorderTop(BorderStyle.THIN);
    }

}
