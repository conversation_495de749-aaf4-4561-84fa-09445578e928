package com.cfpamf.ms.insur.operation.qy.service;

import com.cfpamf.common.ms.exception.MSBizNormalException;
import com.cfpamf.ms.insur.operation.base.constant.ExceptionConstant;
import com.cfpamf.ms.insur.operation.qy.form.QyJsSdkForm;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.experimental.FieldDefaults;
import me.chanjar.weixin.common.bean.WxJsapiSignature;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.cp.api.WxCpService;
import me.chanjar.weixin.cp.bean.WxCpAgentJsapiSignature;
import me.chanjar.weixin.cp.bean.WxCpBaseResp;
import me.chanjar.weixin.cp.bean.WxCpOauth2UserInfo;
import me.chanjar.weixin.cp.bean.external.contact.WxCpExternalContactInfo;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR> 2022/8/4 10:49
 */
@Service
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@AllArgsConstructor
public class WxCpProxyService {

    WxCpService wxCpService;

    /**
     * 获取jsapi
     *
     * @param form
     * @return
     */
    public WxJsapiSignature jsapiSignature(QyJsSdkForm form) {
        return proxy(() -> wxCpService.createJsapiSignature(form.getUrl()));
    }

    /**
     * 生成应用签名
     *
     * @param form
     * @return
     */
    public WxCpAgentJsapiSignature agentJsapiSignature(QyJsSdkForm form) {
        return proxy(() -> wxCpService.createAgentJsapiSignature(form.getUrl()));
    }

    /**
     * 标记标签
     *
     * @param userid         员工id
     * @param externalUserid 外部联系人id
     * @param addTag         标签
     * @param removeTag      删除的标签
     * @return
     */
    public WxCpBaseResp markTag(String userid, String externalUserid, String[] addTag, String[] removeTag) {
        return proxy(() -> wxCpService.getExternalContactService().markTag(userid, externalUserid, addTag, removeTag));
    }

    /**
     * 根据code获取用户新
     *
     * @param code
     * @return
     */
    public WxCpOauth2UserInfo userInfo(String code) {
        return proxy(() -> wxCpService.getOauth2Service().getUserInfo(code));
    }


    /**
     * 获取外部联系人信息
     *
     * @param userid 外部联系人id
     * @return
     */
    public WxCpExternalContactInfo contactDetail(String userid) {
        return proxy(() -> wxCpService.getExternalContactService().getContactDetail(userid, null));
    }

    public void editLabel() {
//        wxCpService.getExternalContactService()
//                .markTag(
//                        "userid1",
//                        "tagid1",
//                        null,
//                        new String[]{"tagid2", "tagid3"}
//                );
    }

    private <T> T proxy(WxCpCall<T> wxCpCall) {
        try {
            return wxCpCall.call();
        } catch (WxErrorException e) {
            throw new MSBizNormalException(ExceptionConstant.INSURANCE_OPERATION_QY_ERROR_CODE, e.getError().getErrorMsg());
        }

    }


    interface WxCpCall<T> {

        /**
         * 调用微信api
         *
         * @return
         * @throws WxErrorException
         */
        T call() throws WxErrorException;
    }
}
