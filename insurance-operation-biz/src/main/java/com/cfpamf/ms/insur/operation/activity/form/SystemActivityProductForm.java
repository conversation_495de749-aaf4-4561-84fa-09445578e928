package com.cfpamf.ms.insur.operation.activity.form;

import com.alibaba.fastjson.JSON;
import com.cfpamf.ms.insur.operation.activity.enums.RewardType;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections4.CollectionUtils;

import javax.validation.Valid;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/7/6 16:15
 */
@ApiModel("活动产品以及规则奖励")
@Getter
@Setter
public class SystemActivityProductForm {
    /**
     * 产品id
     */
    @NotNull(message = "产品id不能为空")
    @ApiModelProperty(value = "产品id  -1为所有产品")
    private Long productId;
    /**
     * 产品id
     */
    @ApiModelProperty(value = "产品id  -1为所有产品")
    private String productName;

    /**
     * 计划id集合
     */
    @ApiModelProperty(value = "计划id集合")
    @NotEmpty(message = "计划id集合不能为空")
    private List<Long> planId;

    /**
     * 险种名称集合
     */
    @ApiModelProperty(value = "险种名称集合")
    private List<String> riskName;

    /**
     * 赠送的数量
     */
    @NotNull
    @ApiModelProperty(value = "赠送的数量")
    @Min(value = 1, message = "数量需大于0")
    private Integer quantity;

    /**
     *  '奖励类型Add_Commission-加佣 Coupon_delivery送券',
     */
    @ApiModelProperty(" 奖励类型ADD_COMMISSION加佣 COUPON_DELIVERY送券 RED_ENVELOPE红包")
    @NotNull(message = "奖励类型不能为空")
    private RewardType rewardType ;

    /**
     * 触发类型
     */
    @ApiModelProperty(value = "触发类型 任务触发-JOB  监听触发-LISTENER")
    private String triggerType;
    /**
     * 活动产品规则
     */
    @Valid
    @NotEmpty(message = "活动产品规则不能为空")
    @ApiModelProperty("活动产品规则")
    private List<SystemActivityProductRuleForm> systemActivityProductRuleList;

    @JsonIgnore
    public String getPlanIdJson() {
        if (CollectionUtils.isNotEmpty(planId)) {
            return JSON.toJSONString(planId);
        }
        return JSON.toJSONString(CollectionUtils.EMPTY_COLLECTION);
    }
    @JsonIgnore
    public String getRiskNameJson() {
        if (CollectionUtils.isNotEmpty(riskName)) {
            return JSON.toJSONString(riskName);
        }
        return JSON.toJSONString(CollectionUtils.EMPTY_COLLECTION);
    }
}
