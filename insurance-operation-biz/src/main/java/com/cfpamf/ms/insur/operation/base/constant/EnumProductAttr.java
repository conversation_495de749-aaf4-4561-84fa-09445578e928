package com.cfpamf.ms.insur.operation.base.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR> 2021/1/19 17:31
 */
@Getter
@AllArgsConstructor
public enum EnumProductAttr {

    /**
     * 是否赠险
     */
    IS_FREE("isFree"),

    UNINSURE_FILE("unInsureFile"),

    /**
     * 加减费因素精度
     */
    PREMIUM_PEOPLE_FACTOR_ACC("PREMIUM_PEOPLE_FACTOR_ACC"),
    /**
     * 责任因素精度
     */
    PREMIUM_DUTY_FACTOR_ACC("PREMIUM_DUTY_FACTOR_ACC"),
    /**
     * 短期费率计算精度
     */
    PREMIUM_SHORT_TERM_ACC("PREMIUM_SHORT_TERM_ACC"),
    /**
     * 个险
     */
    PERSON("person"),
    /**
     * 团险
     */
    GROUP("group"),
    /**
     * 雇主责任险
     */
    EMPLOYER("employer"),

    /**
     * 企业风险系数
     */
    PREMIUM_ENTERPRISE_RISK_ACC("PREMIUM_ENTERPRISE_RISK_ACC");

    String code;
}
