package com.cfpamf.ms.insur.operation.dingtalk.service;

import com.cfpamf.ms.insur.operation.base.exception.BusinessException;
import com.cfpamf.ms.insur.operation.dingtalk.api.DingTalkApi;
import com.cfpamf.ms.insur.operation.dingtalk.api.model.DingTalkResponse;
import com.cfpamf.ms.insur.operation.dingtalk.api.model.UserGetByMobileRequest;
import com.cfpamf.ms.insur.operation.fegin.bms.enums.OperationErrorEnum;
import com.cfpamf.ms.insur.operation.msg.service.DingTalkService;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.function.Supplier;

/**
 * <AUTHOR> 2022/9/29 16:28
 */
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@Service
@Slf4j
@AllArgsConstructor
public class DingTalkSdkMissService {

    DingTalkApi api;

    DingTalkService dingTalkService;

    /**
     * 调用本接口，可以通过手机号查询在职员工的userId。如果员工离职，无法通过手机号获取用户的userId。
     *
     * @param mobile
     * @return
     */
    public String getUserId(String mobile) {
        UserGetByMobileRequest request = new UserGetByMobileRequest();
        request.setMobile(mobile);
        return call(() -> api.getUser(dingTalkService.getToken(), request)).getUserid();

    }

    public String getUserIdNotError(String mobile) {

        try {
            UserGetByMobileRequest request = new UserGetByMobileRequest();
            request.setMobile(mobile);
            return call(() -> api.getUser(dingTalkService.getToken(), request)).getUserid();
        } catch (BusinessException e) {
            log.error("获取订单数据失败", e);
            return null;
        }


    }

    private <T> T call(Supplier<DingTalkResponse<T>> supplier) {
        DingTalkResponse<T> response = supplier.get();

        if (response.isSuccess()) {
            return response.getResult();
        }
        throw new BusinessException(OperationErrorEnum.DING_TALK_API_ERROR.getCode(), response.getMessage());

    }
}
