package com.cfpamf.ms.insur.operation.aicall.event.handler;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cfpamf.ms.insur.operation.aicall.convertor.AiFollowRecordConverter;
import com.cfpamf.ms.insur.operation.aicall.dao.AiFollowRecordMapper;
import com.cfpamf.ms.insur.operation.aicall.entity.AiFollowRecordPo;
import com.cfpamf.ms.insur.operation.aicall.enums.AIIntentEnums;
import com.cfpamf.ms.insur.operation.aicall.event.AiCallBackEvent;
import com.cfpamf.ms.insur.operation.base.event.BaseEventHandler;
import com.cfpamf.ms.insur.operation.order.dao.SmOrderInsuredMapper;
import com.cfpamf.ms.insur.operation.order.dto.SmOrderInsuredDto;
import com.cfpamf.ms.insur.operation.phoenix.config.WechatConfigProperties;
import com.cfpamf.ms.insur.operation.phoenix.dao.PhoenixEmpTodoMapper;
import com.cfpamf.ms.insur.operation.phoenix.enums.EnumTodoBizType;
import com.cfpamf.ms.insur.operation.phoenix.pojo.domain.PhoenixEmpTodoStat;
import com.cfpamf.ms.insur.operation.phoenix.service.WxApiService;
import com.google.common.eventbus.Subscribe;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.mp.bean.template.WxMpTemplateData;
import me.chanjar.weixin.mp.bean.template.WxMpTemplateMessage;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description Ai外呼回调添加跟进记录事件
 * @date 2023/11/02 17:55 上午
 * @Version 1.0
 */
@Slf4j
@Component
@AllArgsConstructor
public class AiFollowRecordHandler implements BaseEventHandler {

    PhoenixEmpTodoMapper phoenixEmpTodoMapper;

    AiFollowRecordMapper aiFollowRecordMapper;

    WechatConfigProperties configProperties;

    WxApiService wxApiService;

    SmOrderInsuredMapper smOrderInsuredMapper;

    /**
     * 处理保单状态变化事件
     *
     * @param event 事件
     */
    @Subscribe
    public void handlerEvent(AiCallBackEvent event) {
        if (!(EnumTodoBizType.INTERRUPTION.name().equals(event.getTaskType())||
                EnumTodoBizType.RENEW_SHORT.name().equals(event.getTaskType()))){
            return;
        }
        event.setIntent(convertIntent(event.getIntent()));
        log.info("外呼回调生成跟进记录事件触发：{}", JSONObject.toJSONString(event));
        //1.获取待办信息（证件号，客户主键等信息）
        PhoenixEmpTodoStat phoenixEmpTodo = phoenixEmpTodoMapper.selectTodoById(event.getOutSourceId());
        if (Objects.isNull(phoenixEmpTodo)) {
            log.warn("外呼回调生成跟进记录事件处理失败：未找到对应待办事项，回调参数：{}", JSONObject.toJSONString(event));
            return;
        }

        //2.初始化跟进记录信息
        AiFollowRecordPo po = AiFollowRecordConverter.INS.initFollowRecordPo(event,phoenixEmpTodo);
        po.setId(null);
        String customerId = "";
        String customerName = "";
        if (!StringUtils.isBlank(phoenixEmpTodo.getTodoProperty())) {
            JSONObject jsonObject = JSON.parseObject(phoenixEmpTodo.getTodoProperty());
            customerId = jsonObject.containsKey("customerId")?jsonObject.get("customerId").toString():"";
            customerName = jsonObject.containsKey("customerName")?jsonObject.get("customerName").toString():"";
        }
        po.setTargetId(EnumTodoBizType.INTERRUPTION.name().equals(event.getTaskType())?customerId:phoenixEmpTodo.getTargetId());
        log.info("外呼回调生成跟进记录事件，跟进记录信息{}", JSONObject.toJSONString(po));

        //3.更新历史跟进记录newest状态
        aiFollowRecordMapper.updateNewest(po.getTargetId());

        //4.插入跟进记录
        aiFollowRecordMapper.insert(po);

        //5.高意向公众号消息通知
        if (AIIntentEnums.TAG_H.getCode().equals(event.getIntent()) || AIIntentEnums.TAG_M.getCode().equals(event.getIntent())) {
            WxMpTemplateMessage templateMessage = new WxMpTemplateMessage();
            if (EnumTodoBizType.INTERRUPTION.name().equals(event.getTaskType())) {
                buildWechatInterruptionData(templateMessage, customerName,AIIntentEnums.dict(event.getIntent()));
                String tag = AIIntentEnums.TAG_H.getCode().equals(event.getIntent())?"aiCallBreak":"aiInterruptionTagM";
                templateMessage.setUrl(getCustomerDetailUrl(customerId,tag));
                log.info("AI高意向断保通知链接{}",templateMessage.getUrl());
            } else {
                //获取被保人主键
                List<SmOrderInsuredDto> insureds = smOrderInsuredMapper.selectByPolicyNo(phoenixEmpTodo.getTargetId());
                String productAttrCode = null;
                String fhOrderId = "";

                if (CollectionUtils.isNotEmpty(insureds)) {
                    productAttrCode = insureds.get(0).getProductAttrCode();
                    fhOrderId = insureds.get(0).getFhOrderId();
                }
                buildWechatRenewData(templateMessage, customerName,AIIntentEnums.dict(event.getIntent()));
                String tag = AIIntentEnums.TAG_H.getCode().equals(event.getIntent())?"aiCallRenew":"aiRenewShortTagM";
                templateMessage.setUrl(getRenewDetailUrl(phoenixEmpTodo.getTargetId(),productAttrCode,fhOrderId,tag));
                log.info("AI高意向续保通知链接{}",templateMessage.getUrl());
            }
            templateMessage.setTemplateId(configProperties.getTempMessageAiFollow());
            templateMessage.setToUser(phoenixEmpTodo.getWxOpenId());
            wxApiService.sendTemplateMsg(templateMessage);
        }

        //6.更新待办排序
        updatePhoenixFollowSort(phoenixEmpTodo,event);
    }

    private String convertIntent(String intentTag) {
        switch (intentTag){
            case "A":
                return "TAG_H";
            case "B":
                return "TAG_M";
            case "C":
                return "TAG_L";
            case "D":
                return "TAG_N";
            case "E":
                return "TAG_E";
            case "F":
                return "TAG_F";
            case "G":
                return "TAG_F";
            case "H":
                return "TAG_F";
            case "I":
                return "TAG_OTHER";
            default:
                return "";
        }
    }

    /**
     * 根据意向更新待办排序
     * @param phoenixEmpTodo 待办TODO
     * @param event 事件
     */
    private void updatePhoenixFollowSort(PhoenixEmpTodoStat phoenixEmpTodo, AiCallBackEvent event) {
        int followSortNo;
        if (AIIntentEnums.TAG_H.getCode().equals(event.getIntent())) {
            followSortNo = -40;
        } else if (AIIntentEnums.TAG_M.getCode().equals(event.getIntent())) {
            followSortNo = -30;
        } else if (AIIntentEnums.TAG_L.getCode().equals(event.getIntent())) {
            followSortNo = 10;
        } else {
            followSortNo = 20;
        }
        phoenixEmpTodoMapper.updateFollowSortNo(followSortNo,phoenixEmpTodo.getId());
    }

    /**
     * 断保高意向微信通知
     * @param wtm 消息体
     * @param customerName 客户姓名
     * @return WxMpTemplateMessage
     */
    private WxMpTemplateMessage buildWechatInterruptionData(WxMpTemplateMessage wtm,String customerName,String tagName) {
        wtm.addData(new WxMpTemplateData("keyword1", customerName));
        wtm.addData(new WxMpTemplateData("keyword2", "断保客户"));
        wtm.addData(new WxMpTemplateData("keyword3", "机器人回访"+tagName+"，尽快跟进"));
        return wtm;
    }
    /**
     * 续投高意向微信通知
     * @param wtm 消息体
     * @return WxMpTemplateMessage
     */
    private WxMpTemplateMessage buildWechatRenewData(WxMpTemplateMessage wtm,String customerName,String tagName) {
        wtm.addData(new WxMpTemplateData("keyword1", customerName));
        wtm.addData(new WxMpTemplateData("keyword2", "续投客户"));
        wtm.addData(new WxMpTemplateData("keyword3", "机器人回访"+tagName+"，尽快跟进"));
        return wtm;
    }

    /**
     * 获取微信断保客户详情页地址
     *
     * @return String
     */
    public String getCustomerDetailUrl(String customerId,String tag) {
        try {
            String url = URLEncoder.encode(String.format(configProperties.getCustomerDetailUrl(), customerId,tag));
            return String.format(configProperties.getFrontCustomerDetailUrl(), url,"");
        } catch (Exception e) {
            log.error("", e);
        }
        return null;
    }
    /**
     * 获取微信断保客户详情页地址
     *
     * @return String
     */
    public String getRenewDetailUrl(String policyNo,String productAttrCode,String fhOrderId,String tag) {
        try {
            String url = URLEncoder.encode(String.format(configProperties.getRenewDetailUrl(), productAttrCode,policyNo,fhOrderId,tag));
            return String.format(configProperties.getFrontRenewDetailUrl(), url, "");
        } catch (Exception e) {
            log.error("", e);
        }
        return null;
    }
}
