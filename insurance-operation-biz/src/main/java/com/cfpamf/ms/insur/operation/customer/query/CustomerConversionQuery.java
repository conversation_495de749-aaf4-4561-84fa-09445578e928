package com.cfpamf.ms.insur.operation.customer.query;

import com.cfpamf.ms.insur.operation.phoenix.enums.EnumTodoBizType;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.ToString;
import lombok.experimental.FieldDefaults;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 微信产品Query
 *
 * <AUTHOR>
 **/
@Data
@ToString(callSuper = true)
@FieldDefaults(level = AccessLevel.PRIVATE)
public class CustomerConversionQuery {

    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间")
    LocalDateTime startDate;
    /**
     * 投保人身份证
     */
    @ApiModelProperty(value = "结束时间")
    LocalDateTime endDate;

    /**
     * 员工工号列表
     */
    @ApiModelProperty(value = "员工工号列表")
    List<String> empCodes;
}
