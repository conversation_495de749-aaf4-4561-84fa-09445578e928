package com.cfpamf.ms.insur.operation.honor.service;

import org.springframework.util.StringUtils;
import com.cfpamf.common.ms.exception.MSBizNormalException;
import com.cfpamf.ms.insur.operation.activity.dao.AuthUserMapper;
import com.cfpamf.ms.insur.operation.activity.entity.AuthUser;
import com.cfpamf.ms.insur.operation.honor.dto.HonorListExcelDTO;
import com.cfpamf.ms.insur.operation.honor.dto.HonorListImportDTO;
import com.cfpamf.ms.insur.operation.honor.validation.HonorValidationResult;
import com.cfpamf.ms.insur.operation.honor.validation.level.HonorLevelValidation;
import com.cfpamf.ms.insur.operation.pco.util.ExcelField;
import com.cfpamf.ms.insur.operation.pco.validation.ValidationResult;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.validation.ConstraintViolation;
import javax.validation.Validator;
import java.lang.reflect.Field;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class HonorValidationService {
    Validator validator;

    AuthUserMapper authUserMapper;

    List<String> honorNormNameList = Arrays.asList("标准保费","人均标准保费","规模保费","人均规模保费","留存率","交叉销售比");

    Map<String, HonorLevelValidation> validationMap = new ConcurrentHashMap<>();

    ApplicationContext context;

    /**
     * 调整记录校验规则
     * @param excelDtoS excel数据
     * @return 校验结果集
     */
    public List<ValidationResult<HonorListExcelDTO>> valid(List<HonorListExcelDTO> excelDtoS, HonorListImportDTO dto) {
        //基本校验  true --> success
        //excel数据基础校验
        List<ValidationResult<HonorListExcelDTO>> allRes = excelDtoS.stream()
                .map(this::validateBaseData).collect(Collectors.toList());

        Map<Boolean, List<ValidationResult<HonorListExcelDTO>>> baseResMap = allRes.stream().collect(Collectors.partitioningBy(ValidationResult::isSuccess));

        //成功记录
        List<ValidationResult<HonorListExcelDTO>> success = baseResMap.getOrDefault(Boolean.TRUE, Collections.emptyList());

        Map<String, AuthUser> userList = authUserMapper.getDistinctUser().stream().collect(Collectors.toMap(AuthUser::getUserId, user -> user));
        //校验数据（重复数据、员工姓名与工号不一致等）
        validateData(success,dto,userList);

        //校验pco数据是否重复
        return allRes;
    }

    /**
     * 校验pco信息（pco工号是否存在，姓名工号是否匹配等）
     * @param success
     */
    private void validateData(List<ValidationResult<HonorListExcelDTO>> success,HonorListImportDTO importDto,Map<String,AuthUser> userMap){
        if (CollectionUtils.isEmpty(success)) {
            return;
        }
        List<String> userIds = new ArrayList<>();
        HonorLevelValidation levelValidation = findLevelValidation(importDto.getLevel());
        success.forEach(
                validation -> {
                    HonorListExcelDTO dto = validation.getSource();
                    //1、校验是否存在属于非配置指标却导入了结果集的数据
                    String errorMsg = getFieldByExcelFieldName(dto,Arrays.asList(importDto.getHonorNormName().split(",")));
                    if(!StringUtils.isEmpty(errorMsg)) {
                        validation.addMessage(errorMsg);
                    }

                    //2、校验重复数据
                    String key = dto.getEmpCode()+"-"+dto.getEmpName()+"-"+dto.getAreaName()+"-"+dto.getAreaCode()+"-"+dto.getBchName()+"-"+dto.getBchCode();
                    if (userIds.contains(key)){
                        validation.addMessage("排名数据重复");
                    }

                    //3、根据不同的评选对象走不同的校验
                    dto.setUserMap(userMap);
                    HonorValidationResult result = levelValidation.validate(dto);
                    if (!result.isSuccess()) {
                        validation.addMessage(result.getMessage());
                    }
                    userIds.add(key);
                }
        );
    }

    /**
     * 基础校验
     * @param adjust
     * @return
     */
    private ValidationResult<HonorListExcelDTO> validateBaseData(HonorListExcelDTO adjust) {
        //第一次校验
        Set<ConstraintViolation<HonorListExcelDTO>> validate = validator.validate(adjust);
        //基础校验 非空 简单正则等
        if (!CollectionUtils.isEmpty(validate)) {
            String collect = validate.stream()
                    .map(ConstraintViolation::getMessage)
                    .collect(Collectors.joining(","));
            //校验出问题
            return new ValidationResult<>(collect, adjust);
        }
        return  ValidationResult.success(adjust);
    }


    public String getFieldByExcelFieldName(HonorListExcelDTO dto, List<String> honorNormName) {
        try {
            for (Field field : HonorListExcelDTO.class.getDeclaredFields()) {
                ExcelField excelField = field.getAnnotation(ExcelField.class);
                //不包含在配置的指标集内，又属于指标字段的，需判断是否有导入值，有则报错
                if (excelField != null
                        && honorNormName.stream().noneMatch(fieldName -> fieldName.equals(excelField.value()))
                        && honorNormNameList.stream().anyMatch(fieldName -> fieldName.equals(excelField.value()))) {
                    // 确保私有字段可访问
                    field.setAccessible(true);
                    if (!Objects.isNull(field.get(dto))) {
                        return excelField.value()+"不在配置的评选指标集内，不允许导入";
                    }
                }
            }
        } catch (IllegalAccessException e) {
            // 处理反射访问权限异常
            throw new MSBizNormalException("400", "指标集校验失败："+e.getMessage());
        }
        return null;
    }

    /**
     * 找到对应的校验规则
     *
     * @param level
     * @return
     */
    public HonorLevelValidation findLevelValidation(String level) {
        if (validationMap.isEmpty()) {
            synchronized (this) {
                validationMap.putAll(context.getBeansOfType(HonorLevelValidation.class));
            }
        }
        HonorLevelValidation validation = validationMap.get(level + "Validation");
        if (Objects.isNull(validation)) {
            throw new UnsupportedOperationException("评选对象暂不支持暂不支持:" + level);
        }
        return validation;
    }
}
