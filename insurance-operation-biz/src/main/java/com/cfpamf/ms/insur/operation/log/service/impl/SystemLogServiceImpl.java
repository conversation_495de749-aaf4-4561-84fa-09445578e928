package com.cfpamf.ms.insur.operation.log.service.impl;


import com.cfpamf.ms.insur.operation.log.dao.SystemLogMapper;
import com.cfpamf.ms.insur.operation.log.dto.SystemLogDTO;
import com.cfpamf.ms.insur.operation.log.form.SystemLogForm;
import com.cfpamf.ms.insur.operation.log.service.SystemLogService;
import com.cfpamf.ms.insur.operation.log.vo.SystemLogVO;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * 系统日志接口
 *
 * <AUTHOR>
 **/
@Slf4j
@Service
public class SystemLogServiceImpl implements SystemLogService {

    /**
     * 接口调用成功 result
     */
    public static final int API_RESULT_SUCCESS = 0;

    /**
     * 接口调用失败 result
     */
    public static final int API_RESULT_FAIL = 1;

    /**
     * 系统日志mapper
     */
    @Autowired
    private SystemLogMapper mapper;

    /**
     * 查询系统日志
     *
     * @param form
     */
    @Override
    public PageInfo<SystemLogVO> getSystemLogs(SystemLogForm form) {
        PageInfo<SystemLogVO> systemLogVOPage = PageHelper.startPage(form.getPageNo(), form.getPageSize()).doSelectPageInfo(()->mapper.listSystemLog(form));
        return systemLogVOPage;
    }

    /**
     * 保存日志
     *
     * @param dto
     */
    @Override
    @Async("log-Executor")
    public void saveLog(SystemLogDTO dto) {
        int maxLength = 4900;
        String parameters = dto.getParameters();
        if (parameters != null && parameters.length() > maxLength) {
            dto.setParameters(parameters.substring(0, maxLength));
        }
        mapper.insertSystemLog(dto);
    }

    /**
     * 清理某个时间前日志
     */
    @Override
    public void deleteLogBeforeDate(Date date) {
        mapper.deleteLogBeforeDate(date);
    }
}
