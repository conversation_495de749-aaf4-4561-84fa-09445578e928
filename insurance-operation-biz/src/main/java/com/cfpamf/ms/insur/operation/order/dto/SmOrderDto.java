
package com.cfpamf.ms.insur.operation.order.dto;

import com.cfpamf.ms.insur.operation.base.entity.BaseNoUserEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import javax.persistence.Column;
import javax.persistence.Table;
import java.time.LocalDateTime;

/**
 * Created by zhengjing  on 2022-06-28 11:03:19
 * <AUTHOR>
 */
@ApiModel("订单")
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class SmOrderDto extends BaseNoUserEntity {

	@ApiModelProperty(value = "泛华保险订单号")
	String fhOrderId;

	@ApiModelProperty(value = "产品id")
	Integer productId;

	@ApiModelProperty(value = "推荐员工号")
	String recommendId;

	@ApiModelProperty(value = "微信openId")
	String wxopenId;

	@ApiModelProperty(value = "计划Id")
	Integer planId;

	@ApiModelProperty(value = "每个产品的保费[每个被保人的平均保费]")
	java.math.BigDecimal unitPrice;

	@ApiModelProperty(value = "购买产品数量")
	Integer qty;

	@ApiModelProperty(value = "订单总保费")
	java.math.BigDecimal totalAmount;

	@ApiModelProperty(value = "保险开始时间")
	java.time.LocalDateTime startTime;

	@ApiModelProperty(value = "保险结束时间")
	java.time.LocalDateTime endTime;

	@ApiModelProperty(value = "保险期限")
	String validPeriod;

	@ApiModelProperty(value = "")
	String submitTime;

	@ApiModelProperty(value = "提示信息代码")
	String noticeCode;

	@ApiModelProperty(value = "提示信息")
	String noticeMsg;

	@ApiModelProperty(value = "订单状态：10=暂存")
	String orderState;

	@ApiModelProperty(value = "支付状态")
	String payStatus;
	@ApiModelProperty(value = "保单状态")
	String appStatus;

	@ApiModelProperty(value = "提成Id")
	Integer commissionId;

	@ApiModelProperty(value = "提出客户信息Flag")
	Integer extractFlag;

	@ApiModelProperty(value = "")
	String updateBy;

	@ApiModelProperty(value = "")
	java.time.LocalDateTime paymentTime;

	@ApiModelProperty(value = "")
	String customerAdminId;

	@ApiModelProperty(value = "")
	String createBy;

	@ApiModelProperty(value = "")
	String underwritingAge;

	@ApiModelProperty(value = "出单渠道")
	String channel;

	@ApiModelProperty(value = "[团单-投保单号]/[个险-???]")
	String appNo;

	@ApiModelProperty(value = "[团单-保单号]")
	String policyNo;

	@ApiModelProperty(value = "")
	String payId;

	@ApiModelProperty(value = "")
	String payUrl;

	@ApiModelProperty(value = "")
	String renewOrderId;

	@ApiModelProperty(value = "")
	String recommendMasterName;

	@ApiModelProperty(value = "")
	String recommendAdminName;

	@ApiModelProperty(value = "")
	java.time.LocalDateTime recommendEntryDate;

	@ApiModelProperty(value = "")
	String recommendPostName;

	@ApiModelProperty(value = "")
	Integer agentId;

	@ApiModelProperty(value = "子渠道 乡助微服务（xiangzhu）/CAPP（capp）")
	String subChannel;

	@ApiModelProperty(value = "计划价格因素选线JSON")
	String planFactorPriceOptionJson;

	@ApiModelProperty(value = "推荐人任职编码")
	String recommendJobCode;

	@ApiModelProperty(value = "推荐人主职工号")
	String recommendMainJobNumber;

	@ApiModelProperty(value = "当前订单负责人任职编码")
	String customerAdminJobCode;

	@ApiModelProperty(value = "当前订单负责人主职工号")
	String customerAdminMainJobNumber;

	@ApiModelProperty(value = "推荐人业务上级任职编码")
	String recommendMasterJobCode;

	@ApiModelProperty(value = "推荐人行政上级任职编码")
	String recommendAdminJobCode;
	/**
	 *  字段名称 推荐人机构编码
	 */
	@ApiModelProperty(value = "推荐人机构编码")
	String recommendOrgCode;
	/**
	 *  字段名称 当前订单负责人机构编码
	 */
	@ApiModelProperty(value = "当前订单负责人机构编码")
	String customerAdminOrgCode;
	/**
	 *  字段名称 产品类型(开发用) house car normal
	 */
	@ApiModelProperty(value = "产品类型(开发用) house car normal")
	String productType;

	@ApiModelProperty(value = "0=普通单；1=分销单")
	Integer orderType;

	@ApiModelProperty(value = "批改单号:原单的值为:000")
	String endorsementNo;

	@ApiModelProperty(value = "支付方式：online/offline/wxpay/alipay......")
	String payType;

	@ApiModelProperty(value = "推荐渠道：小鲸向海（xjxh），中和农信(zhnx)")
	private String recommendChannel;

	@ApiModelProperty(value = "产品属性：团险group/employer，个险person")
	private String productAttrCode;

	@ApiModelProperty(value = "记账时间")
	private LocalDateTime accountTime;

}

