package com.cfpamf.ms.insur.operation.reward.addcommission.entity;

import com.cfpamf.ms.insur.operation.base.entity.BaseNoUserEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Objects;

/**
 * sm_add_commission_detail
 *
 * <AUTHOR>
@ApiModel(value = "com.cfpamf.ms.insur.operation.reward.addcommission.dto.SmAddCommissionDetail加佣表")
@Data
@Table(name = "sm_add_commission_detail")
public class SmAddCommissionDetail extends BaseNoUserEntity {


    /**
     * 订单id
     */
    @ApiModelProperty(value = "订单id")
    private String orderId;

    /**
     * 保单号
     */
    @ApiModelProperty(value = "保单号")
    private String policyNo;

    /**
     * 被保人证件号
     */
    @ApiModelProperty(value = "被保人证件号")
    private String insuredIdNumber;

    /**
     * 计划id
     */
    @ApiModelProperty(value = "计划id")
    private Integer planId;

    /**
     * 险种id，险种不存在时默认-1
     */
    @ApiModelProperty(value = "险种id，险种不存在时默认-1")
    private Integer riskId;

    /**
     * 期数
     */
    @ApiModelProperty(value = "期数")
    private Integer termNum;

    @ApiModelProperty("状态 1-加佣 4-退")
    private String status;

    /**
     * 加佣比例
     */
    @ApiModelProperty(value = "加佣比例")
    private BigDecimal proportion;
    @ApiModelProperty(value = "保费")
    private BigDecimal amount;
    @ApiModelProperty(value = "加佣金额")
    private BigDecimal addCommissionAmount;

    @ApiModelProperty(value = "唯一标识，唯一索引字段值拼接而成 拼接字符|")
    private String uuid;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private LocalDateTime createTime;
    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private LocalDateTime updateTime;


    public Integer getRiskId() {
        return Objects.nonNull(riskId) ? riskId : -1;
    }

}
