package com.cfpamf.ms.insur.operation.prospectus.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;

/**
 * 计划书产品视图对象
 *
 * <AUTHOR>
 * @date 2021/5/29 10:32
 */
@Getter
@Setter
public class ProspectusProductVo {
    /**
     * id
     */
    @ApiModelProperty("id")
    private long id;
    /**
     * 产品名
     */
    @ApiModelProperty("产品名")
    private String productName;
    /**
     * 产品标签
     */
    @JsonIgnore
    private String productTags;
    /**
     * 产品标签数组
     */
    @ApiModelProperty("产品标签数组")
    private String[] productTagArray;
    /**
     * 产品列表图片url
     */
    @ApiModelProperty("产品列表图片url")
    private String thumbnailImageUrl;
    /**
     * 产品摘要
     */
    @ApiModelProperty("产品摘要")
    private String productFeature;

    @ApiModelProperty("产品扩展属性 具体 同后台")
    private Map<String, String> attrs;

    @ApiModelProperty(value = "对接类型 1-api 2-H5", example = "2")
    private Integer apiType;

    @ApiModelProperty("h5地址 不能为空")
    private String h5Url;

    /**
     * 保险条款
     */
    @ApiModelProperty(value = "保险条款")
    private List<ProductClauseVo> clauses;

    /**
     * 渠道
     */
    @ApiModelProperty(value = "渠道")
    private String channel;

    public void setProductTags(String productTags) {
        this.productTags = productTags;
        if (StringUtils.isNotBlank(productTags)) {
            this.productTagArray = productTags.split(",");
        }
    }
}
