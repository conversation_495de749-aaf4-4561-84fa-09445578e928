package com.cfpamf.ms.insur.operation.pk.pojo.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import java.math.BigDecimal;

/**
 * 实收数据
 *
 * <AUTHOR>
 * @since 2023/8/9 11:49
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class InsIndicators {

    @ApiModelProperty(value = "1.区域 2.分支 3.客户经理 4.督导， 必填")
    String type;

    @ApiModelProperty(value = "客户经理、督导为工号，分支以及区域为分支以及区域编码 必填")
    String code;

    String name;

    @ApiModelProperty("指标类型")
    String indicatorType;

    @ApiModelProperty("指标值")
    BigDecimal value;
}
