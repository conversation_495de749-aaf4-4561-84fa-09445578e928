package com.cfpamf.ms.insur.operation.prospectus.api;

import com.cfpamf.ms.insur.operation.prospectus.vo.ProspectusTemplateConfigVo;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/5/19 14:54
 */
@RequestMapping("/back/operation/prospectus/template/config")
public interface ProspectusTemplateApi {

    /**
     * 修改计划书模板配置
     *
     * @param coverFileList
     */
    @ApiOperation(value = "修改计划书模板配置", tags = {"计划书模板配置",})
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "success")})
    @PutMapping("")
    void updateTemplate(@ApiParam(value = "封面路径文件集合", required = true) @NotEmpty @RequestBody List<String> coverFileList);

    /**
     * 查看计划书配置详情
     *
     * @return
     */
    @GetMapping("")
    @ApiOperation(value = "获取计划书模板配置", notes = "", response = String.class, responseContainer = "List", tags = {"计划书模板配置",})
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "封面路径文件集合", response = String.class, responseContainer = "List")})
    ProspectusTemplateConfigVo getById();


}
