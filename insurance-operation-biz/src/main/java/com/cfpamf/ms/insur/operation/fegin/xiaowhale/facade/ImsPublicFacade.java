package com.cfpamf.ms.insur.operation.fegin.xiaowhale.facade;


import com.cfpamf.ms.insur.operation.fegin.image.facade.InsuranceImageConfiguration;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(name = "ImsPublicFacade", url = "${ims-public.url}", configuration = InsuranceImageConfiguration.class)
public interface ImsPublicFacade {
    @PostMapping("/message/sendCallBackSmsMessage")
    String sendCallBackSmsMessage(@RequestParam String taskId);
}
