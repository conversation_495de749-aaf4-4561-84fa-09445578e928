package com.cfpamf.ms.insur.operation.promotion.service.impl;


import com.cfpamf.ms.insur.operation.promotion.dao.OperationSynMaterialInfoMapper;
import com.cfpamf.ms.insur.operation.promotion.entity.OperationSynMaterialInfoEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.cfpamf.ms.insur.operation.promotion.service.OperationSynMaterialInfoService;

@Service
public class OperationSynMaterialInfoServiceImpl implements OperationSynMaterialInfoService {

    @Autowired
    private OperationSynMaterialInfoMapper operationSynMaterialInfoMapper;

    @Override
    public OperationSynMaterialInfoEntity selectByMaterialCodeSystemCode(String materialCode, String sourceSystemCode, Integer materialCategory, Integer status) {
        return operationSynMaterialInfoMapper.queryByMaterialCodeSystemCode(materialCode, sourceSystemCode, materialCategory, status);
    }

    @Override
    public Long queryLinkIdByProductCode(String productCode) {
        return operationSynMaterialInfoMapper.queryLinkIdByProductCode(productCode);
    }

    @Override
    public Long queryLinkIdByMaterialCategory(String materialLevelCode) {
        return operationSynMaterialInfoMapper.queryLinkIdByMaterialCategory(materialLevelCode);
    }

    @Override
    public Integer logicDelete(String materialCode, String sourceSystemCode) {
        return operationSynMaterialInfoMapper.logicDelete(materialCode,sourceSystemCode);
    }

    @Override
    public Long queryDefaultLinkId(String materialLevelCode) {
        return operationSynMaterialInfoMapper.queryDefaultLinkId(materialLevelCode);
    }
}
