package com.cfpamf.ms.insur.operation.msg.pojo.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import java.util.Map;

/**
 * <AUTHOR> 2021/7/16 09:47
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class MsfDebugFtlDTO {

    @ApiModelProperty("Freemarker 模板内容")
    String ftlContent;

    @ApiModelProperty("调试参数")
    Map<String, Object> params;
}
