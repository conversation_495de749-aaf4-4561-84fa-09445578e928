package com.cfpamf.ms.insur.operation.reward.service.impl;

import com.alibaba.fastjson.JSON;
import com.cfpamf.common.ms.exception.MSBizNormalException;
import com.cfpamf.common.ms.exception.MSException;
import com.cfpamf.ms.insur.operation.activity.dao.SmOrderAddCommissionMapper;
import com.cfpamf.ms.insur.operation.activity.entity.SmActivityReward;
import com.cfpamf.ms.insur.operation.activity.entity.SmOrderAddCommission;
import com.cfpamf.ms.insur.operation.activity.entity.SmOrderRiskDuty;
import com.cfpamf.ms.insur.operation.activity.enums.EnumCommissionType;
import com.cfpamf.ms.insur.operation.activity.enums.RewardType;
import com.cfpamf.ms.insur.operation.activity.service.SystemActivityProgrammeService;
import com.cfpamf.ms.insur.operation.activity.vo.AddCommissionProportionVo;
import com.cfpamf.ms.insur.operation.activity.vo.OrderAddCommissionProportionVo;
import com.cfpamf.ms.insur.operation.activity.vo.SystemActivityProgrammeVo;
import com.cfpamf.ms.insur.operation.addCommission.dao.WhaleAddCommissionDetailItemMapper;
import com.cfpamf.ms.insur.operation.addCommission.dao.WhaleAddCommissionDetailMapper;
import com.cfpamf.ms.insur.operation.base.event.EventBusEngine;
import com.cfpamf.ms.insur.operation.event.AddCommissionCalculateBatchEvent;
import com.cfpamf.ms.insur.operation.msg.dao.OrderMapper;
import com.cfpamf.ms.insur.operation.reward.addcommission.dao.SmAddCommissionDetailItemMapper;
import com.cfpamf.ms.insur.operation.reward.addcommission.dao.SmAddCommissionDetailMapper;
import com.cfpamf.ms.insur.operation.reward.addcommission.dto.AddCommissionUniqueFlagDTO;
import com.cfpamf.ms.insur.operation.reward.addcommission.entity.SmAddCommissionDetail;
import com.cfpamf.ms.insur.operation.reward.addcommission.entity.SmAddCommissionDetailItem;
import com.cfpamf.ms.insur.operation.reward.service.RewardService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/1/25 16:24
 */
@Slf4j
@Service(value = "ADD_COMMISSION")
public class AddCommissionRewardServiceImpl implements RewardService {

    static final int DATA_ID_SPLIT_LENGTH = 6;
    @Autowired
    private SmOrderAddCommissionMapper smOrderAddCommissionMapper;

    @Autowired
    private SmAddCommissionDetailItemMapper smAddCommissionDetailItemMapper;
    @Autowired
    private WhaleAddCommissionDetailItemMapper whaleAddCommissionDetailItemMapper;
    @Autowired
    OrderMapper orderMapper;
    @Autowired
    SmAddCommissionDetailMapper smAddCommissionDetailMapper;
    @Autowired
    WhaleAddCommissionDetailMapper whaleAddCommissionDetailMapper;
    @Autowired
    @Lazy
    SystemActivityProgrammeService systemActivityProgrammeService;
    @Autowired
    AddCommissionRewardConstWhaleServiceImpl rewardService;
    @Lazy
    @Autowired
    EventBusEngine busEngine;


    @Override
    public void awardByListener(List<SmActivityReward> smActivityRewardList) {
        //过滤为加佣的奖励
        List<SmActivityReward> rewardList = smActivityRewardList.stream()
                .filter(smActivityReward -> Objects.equals(smActivityReward.getRewardType(), RewardType.ADD_COMMISSION))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(rewardList)) {
            return;
        }
        //通过活动id 奖励分组
        Map<Long, List<SmActivityReward>> activityRewardMapBySaId = rewardList.stream()
                .collect(Collectors.groupingBy(SmActivityReward::getSaId));
        //发送奖励
        for (Long saId : activityRewardMapBySaId.keySet()) {
            Map<String, BigDecimal> dataIdMap = Maps.newHashMap();
            activityRewardMapBySaId.get(saId)
                    .forEach(smActivityReward -> {
                        dataIdMap.put(smActivityReward.getDataId(), smActivityReward.getProportion());

                    });
            rewardAddCommissionV2(dataIdMap, saId);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void awardByJob(SystemActivityProgrammeVo systemActivityProgrammeVo, Map<String, BigDecimal> dataIdMap) {
        Long saId = systemActivityProgrammeVo.getId();
        rewardAddCommissionV2(dataIdMap, saId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void awardByJobV2(SystemActivityProgrammeVo systemActivityProgrammeVo, Map<String, BigDecimal> dataIdMap, Map<String, BigDecimal> dataIdAmountMap) {
        rewardService.awardByJob(systemActivityProgrammeVo, dataIdMap, dataIdAmountMap);
    }

    /**
     * 奖励加佣
     *
     * @param dataIdMap
     * @param saId
     */
    private void rewardAddCommission(Map<String, BigDecimal> dataIdMap, Long saId) {
        List<SmOrderAddCommission> addOrderAddCommissionList = Lists.newArrayList();
        List<SmOrderAddCommission> updateOrderAddCommissionList = Lists.newArrayList();
        //计算加佣奖励
        calculationCommissionReward(dataIdMap, saId, addOrderAddCommissionList, updateOrderAddCommissionList);
        //发送奖励
        grantReward(addOrderAddCommissionList, updateOrderAddCommissionList);
    }


    /**
     * 奖励加佣
     *
     * @param dataIdMap
     * @param saId
     */
    private void rewardAddCommissionV2(Map<String, BigDecimal> dataIdMap, Long saId) {
        List<SmAddCommissionDetailItem> addAddCommissionList = Lists.newArrayList();
        List<SmAddCommissionDetailItem> updateAddCommissionList = Lists.newArrayList();
        //计算加佣奖励
        calculationCommissionRewardV2(dataIdMap, saId, addAddCommissionList, updateAddCommissionList);
        //发送奖励
        grantRewardV2(addAddCommissionList, updateAddCommissionList,saId);
    }

    /**
     * 发放加佣奖励
     *
     * @param addAddCommissionList
     * @param updateAddCommissionList
     */
    private void grantRewardV2(List<SmAddCommissionDetailItem> addAddCommissionList, List<SmAddCommissionDetailItem> updateAddCommissionList,Long saId) {
        //变更奖励
        List<String> uuidList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(addAddCommissionList)) {
            smAddCommissionDetailItemMapper.insertList(addAddCommissionList);
            List<String> idList = addAddCommissionList.stream()
                    .map(SmAddCommissionDetailItem::getUuid)
                    .distinct().collect(Collectors.toList());
            uuidList.addAll(idList);
            List<String> orderIdList = addAddCommissionList.stream()
                    .map(SmAddCommissionDetailItem::getOrderId)
                    .distinct().collect(Collectors.toList());

        }
        if (CollectionUtils.isNotEmpty(updateAddCommissionList)) {
            smAddCommissionDetailItemMapper.batchUpdateProportion(updateAddCommissionList);
            List<String> idList = updateAddCommissionList.stream()
                    .map(SmAddCommissionDetailItem::getUuid)
                    .distinct().collect(Collectors.toList());
            uuidList.addAll(idList);
            List<String> orderIdList = updateAddCommissionList.stream()
                    .map(SmAddCommissionDetailItem::getOrderId)
                    .distinct().collect(Collectors.toList());

        }
        //变更订单的加佣比例提成
        if (CollectionUtils.isNotEmpty(uuidList)) {
            updateAddCommissionProportionV3(uuidList,saId);
        }

    }


    /**
     * 计算加佣奖励
     * 对比已发送差值记录进行计算
     *
     * @param dataIdMap
     * @param saId
     * @param addOrderAddCommissionList
     * @param updateOrderAddCommissionList
     */
    private void calculationCommissionReward(Map<String, BigDecimal> dataIdMap, Long saId, List<SmOrderAddCommission> addOrderAddCommissionList, List<SmOrderAddCommission> updateOrderAddCommissionList) {
        //获取已经提成的奖励
        Map<String, SmOrderAddCommission> fhOrderIdMap = smOrderAddCommissionMapper.getByDataIdAndType(String.valueOf(saId), EnumCommissionType.ACTIVITY.name())
                .stream()
                .collect(Collectors.toMap(SmOrderAddCommission::getFhOrderId, Function.identity()));
        //计算奖励
        for (String fhOrderId : dataIdMap.keySet()) {
            BigDecimal totalCommissionProportion = dataIdMap.get(fhOrderId);
            SmOrderAddCommission smOrderAddCommission = fhOrderIdMap.get(fhOrderId);
            if (Objects.isNull(smOrderAddCommission)) {
                //当前订单没有加佣奖励
                SmOrderAddCommission addOrderAddCommission = new SmOrderAddCommission();
                addOrderAddCommission.setFhOrderId(fhOrderId);
                addOrderAddCommission.setCommissionType(EnumCommissionType.ACTIVITY);
                addOrderAddCommission.setCommissionProportion(totalCommissionProportion);
                addOrderAddCommission.setDataId(String.valueOf(saId));
                addOrderAddCommission.setEnabledFlag(0);
                addOrderAddCommissionList.add(addOrderAddCommission);
            } else {
                /**
                 * 20220627-已跟刘奇隆沟通确认:
                 * 加佣场景下新数据直接覆盖历史加佣比例!!!
                 */
//                if (totalCommissionProportion.compareTo(smOrderAddCommission.getCommissionProportion()) > 0) {
                smOrderAddCommission.setCommissionProportion(totalCommissionProportion);
                updateOrderAddCommissionList.add(smOrderAddCommission);
//                }
            }
        }
    }

    /**
     * 计算加佣奖励
     * 对比已发送差值记录进行计算
     *
     * @param dataIdMap
     * @param saId
     * @param addAddCommissionList
     * @param updateAddCommissionList
     */
    private void calculationCommissionRewardV2(Map<String, BigDecimal> dataIdMap, Long saId, List<SmAddCommissionDetailItem> addAddCommissionList, List<SmAddCommissionDetailItem> updateAddCommissionList) {
        //获取已经提成的奖励
        Map<String, SmAddCommissionDetailItem> dataIdCommissionMap = smAddCommissionDetailItemMapper.getByDataIdAndType(String.valueOf(saId), EnumCommissionType.ACTIVITY.name())
                .stream()
                .collect(Collectors.toMap(SmAddCommissionDetailItem::getUuid, Function.identity()));
        //计算奖励
        for (String dataId : dataIdMap.keySet()) {
            BigDecimal totalCommissionProportion = dataIdMap.get(dataId);
            SmAddCommissionDetailItem smAddCommissionDetailItem = dataIdCommissionMap.get(dataId);
            if (Objects.isNull(smAddCommissionDetailItem)) {
                //当前订单没有加佣奖励
                SmAddCommissionDetailItem addCommissionDetailItem = new SmAddCommissionDetailItem();
                addCommissionDetailItem.setCommissionType(EnumCommissionType.ACTIVITY.name());
                addCommissionDetailItem.setProportion(totalCommissionProportion);
                addCommissionDetailItem.setDataId(String.valueOf(saId));
                addCommissionDetailItem.setEnabledFlag(0);
                addCommissionDetailItem.setUuid(dataId);
                AddCommissionUniqueFlagDTO addCommissionUniqueFlagDTO = parseAddCommissionDataId(dataId);
                BeanUtils.copyProperties(addCommissionUniqueFlagDTO, addCommissionDetailItem);
                addAddCommissionList.add(addCommissionDetailItem);
            } else {
                /**
                 *
                 * 2022-06-27
                 */
                smAddCommissionDetailItem.setProportion(totalCommissionProportion);
                updateAddCommissionList.add(smAddCommissionDetailItem);
            }
        }
    }

    /**
     * 发放加佣奖励
     *
     * @param addOrderAddCommissionList
     * @param updateOrderAddCommissionList
     */
    private void grantReward(List<SmOrderAddCommission> addOrderAddCommissionList, List<SmOrderAddCommission> updateOrderAddCommissionList) {
        //变更奖励
        List<String> fhOrderIdList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(addOrderAddCommissionList)) {
            smOrderAddCommissionMapper.insertList(addOrderAddCommissionList);
            List<String> idList = addOrderAddCommissionList.stream()
                    .map(SmOrderAddCommission::getFhOrderId)
                    .distinct().collect(Collectors.toList());
            fhOrderIdList.addAll(idList);
        }
        if (CollectionUtils.isNotEmpty(updateOrderAddCommissionList)) {
            smOrderAddCommissionMapper.batchUpdate(updateOrderAddCommissionList);
            List<String> idList = updateOrderAddCommissionList.stream()
                    .map(SmOrderAddCommission::getFhOrderId)
                    .distinct().collect(Collectors.toList());
            fhOrderIdList.addAll(idList);
        }
        //变更订单的加佣比例提成
        if (CollectionUtils.isNotEmpty(fhOrderIdList)) {
            updateOrderAddCommissionProportion(fhOrderIdList);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void takeBackReward(Long saId, boolean softDelete) {
        List<String> fhOrderIdList = smOrderAddCommissionMapper.getByDataIdAndType(String.valueOf(saId), EnumCommissionType.ACTIVITY.name())
                .stream()
                .map(SmOrderAddCommission::getFhOrderId)
                .collect(Collectors.toList());

        List<String> uuidList = smAddCommissionDetailItemMapper.getByDataIdAndType(String.valueOf(saId), EnumCommissionType.ACTIVITY.name())
                .stream()
                .map(SmAddCommissionDetailItem::getUuid)
                .collect(Collectors.toList());
        if (softDelete) {
            smOrderAddCommissionMapper.softDeleteByDataIdAndType(String.valueOf(saId), EnumCommissionType.ACTIVITY.name());
            smAddCommissionDetailItemMapper.softDeleteByDataIdAndType(String.valueOf(saId), EnumCommissionType.ACTIVITY.name());
        } else {
            smOrderAddCommissionMapper.deleteByDataIdAndType(String.valueOf(saId), EnumCommissionType.ACTIVITY.name());
            smAddCommissionDetailItemMapper.deleteByDataIdAndType(String.valueOf(saId), EnumCommissionType.ACTIVITY.name());

        }

        //变更订单的加佣比例提成
        if (CollectionUtils.isNotEmpty(fhOrderIdList)) {
            updateOrderAddCommissionProportion(fhOrderIdList);
        }

        //变更订单的加佣比例提成
        if (CollectionUtils.isNotEmpty(uuidList)) {
            updateAddCommissionProportionV2(uuidList);
        }

    }

    /**
     * 更新加佣奖励
     *
     * @param fhOrderListId
     */
    public void updateOrderAddCommissionProportion(List<String> fhOrderListId) {
        List<OrderAddCommissionProportionVo> orderAddCommissionProportionVoList = smOrderAddCommissionMapper.getCommissionMapByFhOrderIdList(fhOrderListId);
        if (CollectionUtils.isEmpty(orderAddCommissionProportionVoList)) {
            return;
        }
        smOrderAddCommissionMapper.updateOrderAddCommissionProportion(orderAddCommissionProportionVoList);
    }

    /**
     * 更新加佣奖励
     *
     * @param uuidList
     */
    private void updateAddCommissionProportionV2(List<String> uuidList) {
        List<AddCommissionProportionVo> addCommissionProportionVoList = smAddCommissionDetailItemMapper.getCommissionMapByFhOrderIdList(uuidList);

        if (CollectionUtils.isEmpty(addCommissionProportionVoList)) {
            return;
        }

        Collection<SmAddCommissionDetail> smAddCommissionDetailCollection = addCommissionProportionVoList.stream()
                .collect(Collectors.toMap(AddCommissionProportionVo::getDataId,
                        addCommissionProportionVo -> convert(addCommissionProportionVo),
                        (v1, v2) -> {
                            BigDecimal proportion = v1.getProportion();
                            BigDecimal proportion2 = v2.getProportion();
                            v1.setProportion(proportion.add(proportion2));
                            return v1;
                        }))
                .values();
        if (CollectionUtils.isNotEmpty(smAddCommissionDetailCollection)) {
            smAddCommissionDetailCollection.forEach(d -> d.setStatus("1"));
            smAddCommissionDetailMapper.batchUpdate(smAddCommissionDetailCollection);
            busEngine.publish(new AddCommissionCalculateBatchEvent(smAddCommissionDetailCollection));
        }

    }
    /**
     * 更新加佣奖励
     *
     * @param uuidList
     */
    private void updateAddCommissionProportionV3(List<String> uuidList,Long saId) {
        List<AddCommissionProportionVo> addCommissionProportionVoList = smAddCommissionDetailItemMapper.getByUuIdListAndSaId(uuidList,saId).stream().map(
                item->{
                    AddCommissionProportionVo vo=new AddCommissionProportionVo();
                    vo.setDataId(item.getUuid());
                    vo.setAddCommissionProportion(item.getProportion());
                    return vo;
                }
        ).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(addCommissionProportionVoList)) {
            return;
        }

        Collection<SmAddCommissionDetail> smAddCommissionDetailCollection = addCommissionProportionVoList.stream()
                .collect(Collectors.toMap(AddCommissionProportionVo::getDataId,
                        addCommissionProportionVo -> convert(addCommissionProportionVo),
                        (v1, v2) -> {
                            BigDecimal proportion = v1.getProportion();
                            BigDecimal proportion2 = v2.getProportion();
                            v1.setProportion(proportion.add(proportion2));
                            return v1;
                        }))
                .values();
        if (CollectionUtils.isNotEmpty(smAddCommissionDetailCollection)) {
            smAddCommissionDetailCollection.forEach(d -> d.setStatus("1"));
            smAddCommissionDetailMapper.batchUpdate(smAddCommissionDetailCollection);
            busEngine.publish(new AddCommissionCalculateBatchEvent(smAddCommissionDetailCollection));
        }

    }

    private SmAddCommissionDetail convert(AddCommissionProportionVo addCommissionProportionVo) {
        SmAddCommissionDetail smAddCommissionDetail = new SmAddCommissionDetail();
        AddCommissionUniqueFlagDTO addCommissionUniqueFlagDTO = parseAddCommissionDataId(addCommissionProportionVo.getDataId());
        BeanUtils.copyProperties(addCommissionUniqueFlagDTO, smAddCommissionDetail);
        smAddCommissionDetail.setProportion(addCommissionProportionVo.getAddCommissionProportion());
        smAddCommissionDetail.setUuid(addCommissionProportionVo.getDataId());
        smAddCommissionDetail.setCreateTime(LocalDateTime.now());
        smAddCommissionDetail.setUpdateTime(LocalDateTime.now());
        return smAddCommissionDetail;
    }

    public SmAddCommissionDetail convert(SmAddCommissionDetailItem smAddCommissionDetailItem) {
        SmAddCommissionDetail smAddCommissionDetail = new SmAddCommissionDetail();
        smAddCommissionDetail.setOrderId(smAddCommissionDetailItem.getOrderId());
        smAddCommissionDetail.setPolicyNo(smAddCommissionDetailItem.getPolicyNo());
        smAddCommissionDetail.setInsuredIdNumber(smAddCommissionDetailItem.getInsuredIdNumber());
        smAddCommissionDetail.setPlanId(smAddCommissionDetailItem.getPlanId());
        smAddCommissionDetail.setRiskId(smAddCommissionDetailItem.getRiskId());
        smAddCommissionDetail.setTermNum(smAddCommissionDetailItem.getTermNum());
        smAddCommissionDetail.setProportion(smAddCommissionDetailItem.getProportion());
        smAddCommissionDetail.setCreateTime(LocalDateTime.now());
        smAddCommissionDetail.setUpdateTime(LocalDateTime.now());
        return smAddCommissionDetail;
    }

    /**
     * 获取加佣奖励 dataId 解析
     * data = "order_id|policy_no|insured_id_number|plan_id|risk_id|term_num|policystatus";
     */
    public static AddCommissionUniqueFlagDTO parseAddCommissionDataId(String dataId) {
        if (StringUtils.isBlank(dataId)) {
            return new AddCommissionUniqueFlagDTO();
        }
        String[] split = dataId.split("\\|");
        if (split.length < DATA_ID_SPLIT_LENGTH) {
            throw new MSBizNormalException("", "加佣奖励dataId格式错误");
        }
        return new AddCommissionUniqueFlagDTO(split[0], split[1], split[2], split[3], split[4], split[5]);
    }

    /**
     * 计算加佣
     *
     * @param smAddCommissionDetail
     */
    public void calculateAddCommission(SmAddCommissionDetail smAddCommissionDetail) {
        Integer termNum = smAddCommissionDetail.getTermNum();
        Integer riskId = smAddCommissionDetail.getRiskId();
        log.warn("开始计算加佣金额：{}", JSON.toJSONString(smAddCommissionDetail));
        BigDecimal finalAmount;
        if (Objects.equals(riskId, -1)) {
            //计划维度
            finalAmount = orderMapper.getPolicyPersonAmountByCommissionDetail(smAddCommissionDetail.getOrderId(), smAddCommissionDetail.getPolicyNo(),
                    smAddCommissionDetail.getInsuredIdNumber(), smAddCommissionDetail.getPlanId(),smAddCommissionDetail.getTermNum());
        } else {
            //险种维度 暂无团险逻辑
            finalAmount = termNum > 1?getTermRiskFinalAmount(smAddCommissionDetail):getRiskFinalAmount(smAddCommissionDetail);
        }

        //是否团险批减单，获取保单金额
        if (Objects.isNull(finalAmount)){
            finalAmount = orderMapper.getPolicyPersonAmountByEndorNo(smAddCommissionDetail.getOrderId(), smAddCommissionDetail.getPolicyNo(), smAddCommissionDetail.getInsuredIdNumber());
        }

        if (Objects.isNull(finalAmount)) {
            log.warn("当前加佣数据的保费获取失败：{}", JSON.toJSONString(smAddCommissionDetail));
            return;
        }
        smAddCommissionDetail.setAmount(finalAmount);
        smAddCommissionDetail.setAddCommissionAmount(finalAmount.multiply(smAddCommissionDetail.getProportion()).divide(BigDecimal.valueOf(100), 2, BigDecimal.ROUND_HALF_UP));
        smAddCommissionDetailMapper.updateAmount(smAddCommissionDetail);

        //更新汇总金额
        smAddCommissionDetailMapper.updateTotalAmount(smAddCommissionDetail);
    }

    /**
     * 获取险种最终金额 已考虑了退保情况
     *
     * @param smAddCommissionDetail
     * @return
     */
    private BigDecimal getRiskFinalAmount(SmAddCommissionDetail smAddCommissionDetail) {
        List<SmOrderRiskDuty> orderRisk = orderMapper.getOrderRisk(smAddCommissionDetail.getOrderId(), smAddCommissionDetail.getPolicyNo(), smAddCommissionDetail.getInsuredIdNumber(), smAddCommissionDetail.getRiskId());
        if (CollectionUtils.isEmpty(orderRisk)) {
            throw new MSException("", "无法获取当前加佣数据的险种保费" + JSON.toJSONString(smAddCommissionDetail));
        }
        BigDecimal amount = BigDecimal.ZERO;
        BigDecimal refundAmount = BigDecimal.ZERO;
        for (SmOrderRiskDuty smOrderRiskDuty : orderRisk) {
            BigDecimal riskAmount = smOrderRiskDuty.getPremium();
            BigDecimal riskRefundAmount = smOrderRiskDuty.getRefundAmount();
            if (Objects.nonNull(riskRefundAmount)) {
                refundAmount = refundAmount.add(riskRefundAmount);
            }
            if (Objects.nonNull(riskAmount)) {
                amount = amount.add(riskAmount);
            }
        }
        BigDecimal finalAmount = amount.subtract(refundAmount);
        return finalAmount;
    }

    /**
     * 获取续期险种最终金额 已考虑了退保情况
     *
     * @param smAddCommissionDetail
     * @return
     */
    private BigDecimal getTermRiskFinalAmount(SmAddCommissionDetail smAddCommissionDetail) {
        List<SmOrderRiskDuty> orderRisk = orderMapper.getOrderTermRisk(smAddCommissionDetail.getOrderId(), smAddCommissionDetail.getInsuredIdNumber(), smAddCommissionDetail.getRiskId(),smAddCommissionDetail.getTermNum());
        if (CollectionUtils.isEmpty(orderRisk)) {
            throw new MSException("", "无法获取当前加佣数据的险种保费" + JSON.toJSONString(smAddCommissionDetail));
        }
        BigDecimal amount = BigDecimal.ZERO;
        BigDecimal refundAmount = BigDecimal.ZERO;
        for (SmOrderRiskDuty smOrderRiskDuty : orderRisk) {
            BigDecimal riskAmount = smOrderRiskDuty.getPremium();
            BigDecimal riskRefundAmount = smOrderRiskDuty.getRefundAmount();
            if (Objects.nonNull(riskRefundAmount)) {
                refundAmount = refundAmount.add(riskRefundAmount);
            }
            if (Objects.nonNull(riskAmount)) {
                amount = amount.add(riskAmount);
            }
        }
        BigDecimal finalAmount = amount.subtract(refundAmount);
        return finalAmount;
    }

}
