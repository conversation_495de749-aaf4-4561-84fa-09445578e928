package com.cfpamf.ms.insur.operation.phoenix.controller;

import com.cfpamf.ms.insur.operation.base.annotaions.ResponseDecorated;
import com.cfpamf.ms.insur.operation.phoenix.pojo.po.PhoenixEventDetail;
import com.cfpamf.ms.insur.operation.phoenix.service.PhoenixEventService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR> 2022/12/12 14:48
 */
@RestController
@ResponseDecorated
@RequestMapping("pub/phoenix/event")
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@AllArgsConstructor
@Api(value = "scrm埋点",tags = "scrm埋点")
public class PhoenixEventController {

    PhoenixEventService service;

    @ApiOperation("新增埋点")
    @PostMapping("")
    public Boolean event(@RequestBody PhoenixEventDetail eventDetail) {
        service.event(eventDetail);
        return Boolean.TRUE;
    }
}
