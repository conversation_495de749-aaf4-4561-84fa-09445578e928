package com.cfpamf.ms.insur.operation.planbook.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/12/10 17:22
 * @Version 1.0
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class SmProductRiskVO {

    private Integer id;

    /**
     * 是否必填1-必填，0-不必填
     */
    @ApiModelProperty(value = "是否必填1-必填，0-不必填")
    private Integer required;

    /**
     * 是否为主保障1-是，0-否
     */
    @ApiModelProperty(value = "是否为主保障1-是，0-否")
    private Integer mainGuarantee;

    /**
     * 字段名称 版本号
     */
    @ApiModelProperty(value = "版本号", required = true)
    Integer version;
    /**
     * 字段名称 险种名称
     */
    @ApiModelProperty(value = "险种名称", required = true)
    String riskName;
    /**
     * 字段名称 险种编码
     */
    @ApiModelProperty(value = "险种编码", required = true)
    String riskCode;
    /**
     * 字段名称 险种标识 uuid
     */
    @ApiModelProperty(value = "险种标识 uuid ", required = true)
    String riskKey;
    /**
     * 字段名称 险种类型 1-主险 2-附加险
     */
    @ApiModelProperty(value = "险种类型 1-主险 2-附加险", required = true, example = "1")
    Integer riskType;
    /**
     * 字段名称 保险公司id
     */
    @ApiModelProperty(value = "保险公司id", required = true, example = "1")
    Integer companyId;
    /**
     * 字段名称 是否豁免条款 0-否 1-是
     */
    @ApiModelProperty(value = "是否豁免条款 0-否 1-是", required = true, example = "0")
    Integer exempt;
    /**
     * 字段名称 豁免主体 1-投保人 2-被保人 3 投保人+被保人
     */
    @ApiModelProperty(value = "豁免主体 1-投保人 2-被保人 3 投保人+被保人", required = true)
    Integer exemptBody;
    /**
     * 字段名称 险种分类 大分类/ 小分类 方式隔离 cx/xjc
     */
    @ApiModelProperty(value = "险种分类 大分类/小分类 方式隔离 cx/xjc", example = "cx/xjc", required = true)
    String riskClassify;
    /**
     * 字段名称 支付方式 1-趸交 2-年缴 3-月缴[1,2,3]
     */
    @ApiModelProperty(value = "支付方式 1-趸交 2-年缴 3-月缴 [1,2,3]", example = "\"[1,2,3]\"", required = true)
    String payWay;
    /**
     * 字段名称 缴费年期 [\"20Y\",\"30Y\"]
     */
    @ApiModelProperty(value = "缴费年期 [\"20Y\",\"30Y\"]", required = true)
    String coveredYears;
    /**
     * 字段名称 保障期限 [\"20Y\",\"<70Y\",\"ALL\"]
     */
    @ApiModelProperty(value = "保障期限 20Y 20年 <70Y 至70周岁 ALL 终身  eg. [\"20Y\",\"<70Y\",\"ALL\"]", required = true)
    String validPeriod;
    /**
     * 字段名称 犹豫期
     */
    @ApiModelProperty(value = "犹豫期")
    Integer hesitationPeriod;
    /**
     * 字段名称 等待期
     */
    @ApiModelProperty(value = "等待期")
    Integer waitingPeriod;
    /**
     * 字段名称 宽限期
     */
    @ApiModelProperty(value = "宽限期")
    Integer gracePeriod;
    /**
     * 字段名称 受益人 0代表法定受益人 大于0代表指定x个 [0,3]
     */
    @ApiModelProperty(value = "受益人 0代表法定受益人 大于0代表指定x个 [0,3]")
    String beneficiary;

    @ApiModelProperty("额外参数 {\"isRevival\":true,\"revival\":\"2Y\"}")
    String riskParams;

    @ApiModelProperty("险种保费范围")
    List<SysRiskAmountVO> sysRiskAmountVOList;
}
