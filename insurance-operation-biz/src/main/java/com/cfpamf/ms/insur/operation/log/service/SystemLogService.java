package com.cfpamf.ms.insur.operation.log.service;

import com.cfpamf.ms.insur.operation.log.dto.SystemLogDTO;
import com.cfpamf.ms.insur.operation.log.form.SystemLogForm;
import com.cfpamf.ms.insur.operation.log.vo.SystemLogVO;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.springframework.scheduling.annotation.Async;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2021/7/12 11:22
 */
public interface SystemLogService {

    /**
     * 查询系统日志
     *
     * @param form
     * @return
     */
    public PageInfo<SystemLogVO> getSystemLogs(SystemLogForm form);

    /**
     * 保存日志
     *
     * @param dto
     */
    @Async("log-Executor")
    public void saveLog(SystemLogDTO dto);

    /**
     * 清理某个时间前日志
     * @param date
     */
    public void deleteLogBeforeDate(Date date);
}
