package com.cfpamf.ms.insur.operation.promotion.entity;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.OrderBy;
import javax.persistence.Table;
import java.util.Date;

/**
 * 素材变更通知表
 *
 * <AUTHOR>
 * @date 2024-04-29 19:33:41
 */
@Data
@Table(name = "material_change_notify")
public class MaterialChangeNotifyEntity {

	@Id
	@OrderBy("desc")
	protected Long id;

	/**
	 * 素材编码
	 */
	@Column(name = "content")
	private String content;

	/**
	 * 逻辑删除 0:存在;1:删除
	 */
	@Column(name = "enabled_flag")
	private Integer enabledFlag;

	/**
	 * 创建时间
	 */
	@Column(name = "create_time")
	private Date createTime;

	/**
	 * 修改时间
	 */
	@Column(name = "update_time")
	private Date updateTime;
}
