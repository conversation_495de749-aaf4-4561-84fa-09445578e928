
package com.cfpamf.ms.insur.operation.order.dto;

import com.cfpamf.ms.insur.operation.base.entity.BaseNoUserEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import javax.persistence.Column;
import javax.persistence.Table;

/**
 * Created by zhengjing  on 2022-06-28 13:50:00
 *
 * <AUTHOR>
 */
@ApiModel("折算保费+佣金")
@Table(name = "sm_commission_detail")
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class SmCommissionDetail extends BaseNoUserEntity {

    /**
     * 字段名称 订单id
     */
    @Column(name = "order_id")
    @ApiModelProperty(value = "订单id")
    String orderId;
    /**
     * 字段名称 保单号
     */
    @Column(name = "policy_no")

    @ApiModelProperty(value = "保单号")
    String policyNo;
    /**
     * 字段名称 被保人证件号
     */
    @Column(name = "insured_id_number")

    @ApiModelProperty(value = "被保人证件号")
    String insuredIdNumber;
    /**
     * 字段名称 计划id
     */
    @Column(name = "plan_id")
    @ApiModelProperty(value = "计划id", required = true)
    Integer planId;
    /**
     * 字段名称 险种id
     */
    @Column(name = "risk_id")

    @ApiModelProperty(value = "险种id")
    Integer riskId;
    /**
     * 字段名称 记账时间
     */
    @Column(name = "account_time")
    @ApiModelProperty(value = "记账时间", required = true)
    java.time.LocalDateTime accountTime;
    /**
     * 字段名称 保单状态，同sm_order_insured中的appStatus
     */
    @Column(name = "policy_status")

    @ApiModelProperty(value = "保单状态，同sm_order_insured中的appStatus")
    String policyStatus;
    /**
     * 字段名称 险种保费
     */
    @Column(name = "amount")
    @ApiModelProperty(value = "险种保费", required = true)
    java.math.BigDecimal amount;
    /**
     * 字段名称 支付佣金id
     */
    @Column(name = "payment_commission_id")

    @ApiModelProperty(value = "支付佣金id")
    Integer paymentCommissionId;
    /**
     * 字段名称 支付佣金比列
     */
    @Column(name = "payment_rate")

    @ApiModelProperty(value = "支付佣金比列")
    java.math.BigDecimal paymentRate;
    /**
     * 字段名称 支付佣金
     */
    @Column(name = "payment_amount")

    @ApiModelProperty(value = "支付佣金")
    java.math.BigDecimal paymentAmount;
    /**
     * 字段名称 结算佣金id
     */
    @Column(name = "settlement_commission_id")

    @ApiModelProperty(value = "结算佣金id")
    Integer settlementCommissionId;
    /**
     * 字段名称 结算佣金比例
     */
    @Column(name = "settlement_rate")

    @ApiModelProperty(value = "结算佣金比例")
    java.math.BigDecimal settlementRate;
    /**
     * 字段名称 结算佣金
     */
    @Column(name = "settlement_amount")

    @ApiModelProperty(value = "结算佣金")
    java.math.BigDecimal settlementAmount;
    /**
     * 字段名称 折算佣金id
     */
    @Column(name = "conversion_commission_id")

    @ApiModelProperty(value = "折算佣金id")
    Integer conversionCommissionId;
    /**
     * 字段名称 折算佣金比例
     */
    @Column(name = "conversion_rate")

    @ApiModelProperty(value = "折算佣金比例")
    java.math.BigDecimal conversionRate;
    /**
     * 字段名称 折算佣金
     */
    @Column(name = "conversion_amount")

    @ApiModelProperty(value = "折算佣金")
    java.math.BigDecimal conversionAmount;
    /**
     * 字段名称
     */
    @Column(name = "create_by")

    @ApiModelProperty(value = "")
    String createBy;
    /**
     * 字段名称
     */
    @Column(name = "update_by")

    @ApiModelProperty(value = "")
    String updateBy;
    /**
     * 字段名称 缴费期数
     */
    @Column(name = "term_num")

    @ApiModelProperty(value = "缴费期数")
    Integer termNum;
    /**
     * 字段名称 支付险种json串
     */
    @Column(name = "payment_risk_json")

    @ApiModelProperty(value = "支付险种json串")
    String paymentRiskJson;
    /**
     * 字段名称 结算险种json串
     */
    @Column(name = "settlement_risk_json")

    @ApiModelProperty(value = "结算险种json串")
    String settlementRiskJson;
    /**
     * 字段名称 折算险种json串
     */
    @Column(name = "conversion_risk_json")

    @ApiModelProperty(value = "折算险种json串")
    String conversionRiskJson;
    /**
     * 字段名称 佣金受益人
     */
    @Column(name = "commission_user_id")

    @ApiModelProperty(value = "佣金受益人")
    String commissionUserId;
    /**
     * 字段名称 业务类型 common 普通佣金，renewal 续期佣金，increase 中途增保,decrease 中途减保
     */
    @Column(name = "business_type")

    @ApiModelProperty(value = "业务类型 common 普通佣金，renewal 续期佣金，increase 中途增保,decrease 中途减保")
    String businessType;
    /**
     * 字段名称 业务id
     */
    @Column(name = "business_id")

    @ApiModelProperty(value = "业务id")
    String businessId;
}

