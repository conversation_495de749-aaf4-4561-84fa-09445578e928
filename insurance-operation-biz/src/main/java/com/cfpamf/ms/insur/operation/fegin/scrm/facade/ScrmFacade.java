package com.cfpamf.ms.insur.operation.fegin.scrm.facade;

import com.cfpamf.common.ms.result.Result;
import com.cfpamf.ms.insur.operation.aitraining.model.ScrmInsuranceRecordVO;
import com.cfpamf.ms.insur.operation.aitraining.model.ScrmUserReq;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 *
 */
@FeignClient(name = "scrm-service", url = "${scrm-service.url}", contextId = "ScrmFacade")
public interface ScrmFacade {

    /**
     * 是否存在新媒体标签
     * @param mobile
     * @return
     */
    @GetMapping("/scrm/feign/user/fromSocialMedia")
    Result<Boolean> fromSocialMedia(@RequestParam("mobile")String mobile);
}
