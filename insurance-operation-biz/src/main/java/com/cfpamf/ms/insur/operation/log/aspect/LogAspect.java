package com.cfpamf.ms.insur.operation.log.aspect;

import com.alibaba.fastjson.JSON;
import com.cfpamf.ms.insur.operation.base.config.ThreadUserHolder;
import com.cfpamf.ms.insur.operation.base.util.HttpRequestUtil;
import com.cfpamf.ms.insur.operation.base.util.SpringFactoryUtil;
import com.cfpamf.ms.insur.operation.log.annotaions.SystemLog;
import com.cfpamf.ms.insur.operation.log.dto.SystemLogDTO;
import com.cfpamf.ms.insur.operation.log.service.SystemLogService;
import com.cfpamf.ms.insur.operation.log.service.impl.SystemLogServiceImpl;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.Signature;
import org.aspectj.lang.annotation.AfterThrowing;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.lang.reflect.Method;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 定义日志切入类
 *
 * <AUTHOR>
 **/
@Aspect
@Component
@Order(1)
public class LogAspect {

    /**
     * 开始时间ThreadLocal
     */
    private static ThreadLocal<Long> startTimeThreadLocal = new ThreadLocal<>();

    /**
     * 定义controller切入点拦截规则，拦截SystemLog注解的方法
     */
    @Pointcut("@annotation(com.cfpamf.ms.insur.operation.log.annotaions.SystemLog)")
    public void controllerAspect() {
    }

    /***
     * 拦截控制层的操作日志
     *
     * @param joinPoint
     * @return
     * @throws Throwable
     */
    @Around("controllerAspect()")
    public Object recordLog(ProceedingJoinPoint joinPoint) throws Throwable {
        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
        String userId = HttpRequestUtil.getUserId();
        SystemLog systemLog = getServiceMethodAnnoation(joinPoint);
        String methodName = joinPoint.getSignature().toShortString();
        startTimeThreadLocal.set(System.currentTimeMillis());
        Object result = joinPoint.proceed();
        SpringFactoryUtil.getBean(SystemLogService.class).saveLog(SystemLogDTO
                .builder()
                .userId(userId)
                .userIp(HttpRequestUtil.getIpAddr(request))
                .actionType(systemLog.actionType().getName())
                .moduleName(systemLog.module())
                .description(systemLog.descrption())
                .method(methodName)
                .url(request.getRequestURI())
                .parameters(JSON.toJSONString(Stream.of(joinPoint.getArgs())
                        .filter(ags -> !(ags instanceof HttpServletResponse)
                                    && !(ags instanceof HttpServletRequest))
                        .collect(Collectors.toList())))
                .result(SystemLogServiceImpl.API_RESULT_SUCCESS)
                .costTime(System.currentTimeMillis() - startTimeThreadLocal.get())
                .build());
        startTimeThreadLocal.remove();
        clearThreadContextUser();
        return result;
    }

    /**
     * 控制层的操作异常处理
     *
     * @param joinPoint
     * @param e
     * @return
     */
    @AfterThrowing(pointcut = "controllerAspect()", throwing = "e")
    public void doAfterThrowing(JoinPoint joinPoint, Throwable e) throws Exception {
        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
        String userId = HttpRequestUtil.getUserId();
        SystemLog systemLog = getServiceMethodAnnoation(joinPoint);
        String methodName = joinPoint.getSignature().toShortString();
        SpringFactoryUtil.getBean(SystemLogService.class).saveLog(SystemLogDTO
                .builder()
                .userId(userId)
                .userIp(HttpRequestUtil.getIpAddr(request))
                .actionType(systemLog.actionType().getName())
                .moduleName(systemLog.module())
                .description(systemLog.descrption())
                .method(methodName)
                .parameters(JSON.toJSONString(Stream.of(joinPoint.getArgs()).filter(ags -> !(ags instanceof HttpServletResponse)
                        && !(ags instanceof HttpServletRequest)).collect(Collectors.toList())))
                .url(request.getRequestURL().toString())
                .result(SystemLogServiceImpl.API_RESULT_FAIL)
                .message((e != null && e.getMessage() != null && e.getMessage().length() > 300) ? e.getMessage().substring(0, 300) : (e != null ? e.getMessage() : null))
                .costTime(System.currentTimeMillis() - startTimeThreadLocal.get())
                .build());
        startTimeThreadLocal.remove();
        clearThreadContextUser();
    }

    /***
     * 获取service的操作注解信息
     * @param joinpoint
     * @return
     * @throws Exception
     */
    public SystemLog getServiceMethodAnnoation(JoinPoint joinpoint) throws Exception {

        Signature signature = joinpoint.getSignature();
        if (signature instanceof MethodSignature) {
            Method method = ((MethodSignature) signature).getMethod();
            return method.getAnnotation(SystemLog.class);
        }
        // 获取连接点目标类名
        String className = joinpoint.getTarget().getClass().getName();
        // 获取连接点签名的方法名
        String methodName = joinpoint.getSignature().getName();
        // 获取连接点参数
        Object[] args = joinpoint.getArgs();
        // 根据连接点类的名字获取指定类
        Class targetClass = Class.forName(className);
        // 拿到类里面的方法
        Method[] methods = targetClass.getMethods();
        SystemLog systemLog = null;
        // 遍历方法名，找到被调用的方法名
        for (Method method : methods) {
            if (method.getName().equals(methodName)) {
                Class[] clazz = method.getParameterTypes();
                if (clazz.length == args.length) {
                    // 获取注解的说明
                    systemLog = method.getAnnotation(SystemLog.class);
                    break;
                }
            }
        }
        return systemLog;
    }

    /**
     * 清除threadlocal 缓存
     */
    private void clearThreadContextUser() {
        ThreadUserHolder.USER_MODULES_TL.remove();
        ThreadUserHolder.USER_DETAIL_TL.remove();
    }
}
