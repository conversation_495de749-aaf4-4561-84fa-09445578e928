
package com.cfpamf.ms.insur.operation.order.dto;

import com.cfpamf.ms.insur.operation.base.entity.BaseNoUserEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import javax.persistence.Column;
import javax.persistence.Table;

/**
 * Created by zhengjing  on 2022-06-28 11:03:19
 * <AUTHOR>
 */
@ApiModel("订单")
@Table(name = "sm_order")
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class SmOrder  extends BaseNoUserEntity {

	/**
	 *  字段名称 泛华保险订单号
	 */
    @Column(name = "fhOrderId")

	@ApiModelProperty(value = "泛华保险订单号")
	String fhorderid;
	/**
	 *  字段名称 产品id
	 */
    @Column(name = "productId")

	@ApiModelProperty(value = "产品id")
	Integer productid;
	/**
	 *  字段名称 推荐员工号
	 */
    @Column(name = "recommendId")

	@ApiModelProperty(value = "推荐员工号")
	String recommendid;
	/**
	 *  字段名称 微信openId
	 */
    @Column(name = "wxOpenId")

	@ApiModelProperty(value = "微信openId")
	String wxopenid;
	/**
	 *  字段名称 计划Id
	 */
    @Column(name = "planId")

	@ApiModelProperty(value = "计划Id")
	Integer planid;
	/**
	 *  字段名称 每个产品的保费[每个被保人的平均保费]
	 */
    @Column(name = "unitPrice")

	@ApiModelProperty(value = "每个产品的保费[每个被保人的平均保费]")
	java.math.BigDecimal unitprice;
	/**
	 *  字段名称 购买产品数量
	 */
    @Column(name = "qty")

	@ApiModelProperty(value = "购买产品数量")
	Integer qty;
	/**
	 *  字段名称 订单总保费
	 */
    @Column(name = "totalAmount")

	@ApiModelProperty(value = "订单总保费")
	java.math.BigDecimal totalamount;
	/**
	 *  字段名称 保险开始时间
	 */
    @Column(name = "startTime")

	@ApiModelProperty(value = "保险开始时间")
	java.time.LocalDateTime starttime;
	/**
	 *  字段名称 保险结束时间
	 */
    @Column(name = "endTime")

	@ApiModelProperty(value = "保险结束时间")
	java.time.LocalDateTime endtime;
	/**
	 *  字段名称 保险期限
	 */
    @Column(name = "validPeriod")

	@ApiModelProperty(value = "保险期限")
	String validperiod;
	/**
	 *  字段名称
	 */
    @Column(name = "submitTime")

	@ApiModelProperty(value = "")
	String submittime;
	/**
	 *  字段名称 提示信息代码
	 */
    @Column(name = "noticeCode")

	@ApiModelProperty(value = "提示信息代码")
	String noticecode;
	/**
	 *  字段名称 提示信息
	 */
    @Column(name = "noticeMsg")

	@ApiModelProperty(value = "提示信息")
	String noticemsg;
	/**
	 *  字段名称 订单状态：10=暂存
	 */
    @Column(name = "orderState")

	@ApiModelProperty(value = "订单状态：10=暂存")
	String orderstate;
	/**
	 *  字段名称 支付状态
	 */
    @Column(name = "payStatus")

	@ApiModelProperty(value = "支付状态")
	String paystatus;
	/**
	 *  字段名称 提成Id
	 */
    @Column(name = "commissionId")

	@ApiModelProperty(value = "提成Id")
	Integer commissionid;
	/**
	 *  字段名称 提出客户信息Flag
	 */
    @Column(name = "extractFlag")

	@ApiModelProperty(value = "提出客户信息Flag")
	Integer extractflag;
	/**
	 *  字段名称
	 */
    @Column(name = "update_by")

	@ApiModelProperty(value = "")
	String updateBy;
	/**
	 *  字段名称
	 */
    @Column(name = "paymentTime")

	@ApiModelProperty(value = "")
	java.time.LocalDateTime paymenttime;
	/**
	 *  字段名称
	 */
    @Column(name = "customerAdminId")

	@ApiModelProperty(value = "")
	String customeradminid;
	/**
	 *  字段名称
	 */
    @Column(name = "create_by")

	@ApiModelProperty(value = "")
	String createBy;
	/**
	 *  字段名称
	 */
    @Column(name = "underWritingAge")

	@ApiModelProperty(value = "")
	String underwritingage;
	/**
	 *  字段名称 出单渠道
	 */
    @Column(name = "channel")

	@ApiModelProperty(value = "出单渠道")
	String channel;
	/**
	 *  字段名称 [团单-投保单号]/[个险-???]
	 */
    @Column(name = "appNo")

	@ApiModelProperty(value = "[团单-投保单号]/[个险-???]")
	String appno;
	/**
	 *  字段名称 [团单-保单号]
	 */
    @Column(name = "policy_no")

	@ApiModelProperty(value = "[团单-保单号]")
	String policyNo;
	/**
	 *  字段名称
	 */
    @Column(name = "payId")

	@ApiModelProperty(value = "")
	String payid;
	/**
	 *  字段名称
	 */
    @Column(name = "payUrl")

	@ApiModelProperty(value = "")
	String payurl;
	/**
	 *  字段名称
	 */
    @Column(name = "renewOrderId")

	@ApiModelProperty(value = "")
	String reneworderid;
	/**
	 *  字段名称
	 */
    @Column(name = "recommendMasterName")

	@ApiModelProperty(value = "")
	String recommendmastername;
	/**
	 *  字段名称
	 */
    @Column(name = "recommendAdminName")

	@ApiModelProperty(value = "")
	String recommendadminname;
	/**
	 *  字段名称
	 */
    @Column(name = "recommendEntryDate")

	@ApiModelProperty(value = "")
	java.time.LocalDateTime recommendentrydate;
	/**
	 *  字段名称
	 */
    @Column(name = "recommendPostName")

	@ApiModelProperty(value = "")
	String recommendpostname;
	/**
	 *  字段名称
	 */
    @Column(name = "agentId")

	@ApiModelProperty(value = "")
	Integer agentid;
	/**
	 *  字段名称 子渠道 乡助微服务（xiangzhu）/CAPP（capp）
	 */
    @Column(name = "subChannel")

	@ApiModelProperty(value = "子渠道 乡助微服务（xiangzhu）/CAPP（capp）")
	String subchannel;
	/**
	 *  字段名称 计划价格因素选线JSON
	 */
    @Column(name = "planFactorPriceOptionJson")

	@ApiModelProperty(value = "计划价格因素选线JSON")
	String planfactorpriceoptionjson;
	/**
	 *  字段名称 推荐人任职编码
	 */
    @Column(name = "recommendJobCode")

	@ApiModelProperty(value = "推荐人任职编码")
	String recommendjobcode;
	/**
	 *  字段名称 推荐人主职工号
	 */
    @Column(name = "recommendMainJobNumber")

	@ApiModelProperty(value = "推荐人主职工号")
	String recommendmainjobnumber;
	/**
	 *  字段名称 当前订单负责人任职编码
	 */
    @Column(name = "customerAdminJobCode")

	@ApiModelProperty(value = "当前订单负责人任职编码")
	String customeradminjobcode;
	/**
	 *  字段名称 当前订单负责人主职工号
	 */
    @Column(name = "customerAdminMainJobNumber")

	@ApiModelProperty(value = "当前订单负责人主职工号")
	String customeradminmainjobnumber;
	/**
	 *  字段名称 推荐人业务上级任职编码
	 */
    @Column(name = "recommendMasterJobCode")

	@ApiModelProperty(value = "推荐人业务上级任职编码")
	String recommendmasterjobcode;
	/**
	 *  字段名称 推荐人行政上级任职编码
	 */
    @Column(name = "recommendAdminJobCode")

	@ApiModelProperty(value = "推荐人行政上级任职编码")
	String recommendadminjobcode;
	/**
	 *  字段名称 推荐人机构编码
	 */
    @Column(name = "recommendOrgCode")

	@ApiModelProperty(value = "推荐人机构编码")
	String recommendorgcode;
	/**
	 *  字段名称 当前订单负责人机构编码
	 */
    @Column(name = "customerAdminOrgCode")

	@ApiModelProperty(value = "当前订单负责人机构编码")
	String customeradminorgcode;
	/**
	 *  字段名称 出单类型
	 */
    @Column(name = "orderOutType")

	@ApiModelProperty(value = "出单类型")
	String orderouttype;
	/**
	 *  字段名称 产品类型(开发用) house car normal
	 */
    @Column(name = "productType")

	@ApiModelProperty(value = "产品类型(开发用) house car normal")
	String producttype;
	/**
	 *  字段名称 0=普通单；1=分销单
	 */
    @Column(name = "orderType")

	@ApiModelProperty(value = "0=普通单；1=分销单")
	Integer ordertype;
	/**
	 *  字段名称 批改单号:原单的值为:000
	 */
    @Column(name = "endorsement_no")

	@ApiModelProperty(value = "批改单号:原单的值为:000")
	String endorsementNo;
	/**
	 *  字段名称 支付方式：online/offline/wxpay/alipay......
	 */
    @Column(name = "pay_type")

	@ApiModelProperty(value = "支付方式：online/offline/wxpay/alipay......")
	String payType;
	/**
	 *  字段名称 推荐渠道：小鲸向海（xjxh），中和农信(zhnx)
	 */
    @Column(name = "recommend_channel")

	@ApiModelProperty(value = "推荐渠道：小鲸向海（xjxh），中和农信(zhnx)")
	String recommendChannel;
}

