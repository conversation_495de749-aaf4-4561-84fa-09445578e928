package com.cfpamf.ms.insur.operation.phoenix.api;

import com.cfpamf.ms.insur.operation.fegin.wx.request.*;
import com.cfpamf.ms.insur.operation.fegin.wx.response.ProductList;
import com.cfpamf.ms.insur.operation.fegin.wx.response.SellProductListOut;
import com.cfpamf.ms.insur.operation.phoenix.api.model.ReportApplyReq;
import com.cfpamf.ms.insur.operation.phoenix.api.model.UrlReq;
import com.cfpamf.ms.insur.operation.phoenix.api.model.WhaleResp;
import com.cfpamf.ms.insur.operation.whale.model.OnlineNewPolicyVo;
import com.cfpamf.ms.insur.operation.whale.model.PreservationDetail;
import com.cfpamf.ms.insur.operation.whale.model.WhaleContract;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR> 2020/4/10 15:36
 */
@FeignClient(url = "${whale.domain}", value = "whaleOrderClient")
public interface WhaleOrderClient {

    @GetMapping("/channel/api/miniapp/url_link")
    WhaleResp<String> urlLink(@RequestParam("aid") String aid,
                              @RequestParam("version") String version,
                              @RequestBody UrlReq req);

    /**
     * 断保话术报告申请
     *
     * @param aid
     * @param version
     * @param breakReport 客户经理信息
     * @return
     */
    @GetMapping("/channel/api/break/report/apply")
    WhaleResp<String> breakReport(@RequestParam("aid") String aid,
                                  @RequestParam("version") String version,
                                  @RequestBody ReportApplyReq breakReport);

    /**
     * 获取微信二维码链接
     *
     * @param appId   微信小程序AppId
     * @param wxaCodeCreateInput
     * @return com.mpolicy.common.result.Result<java.lang.String> 微信二维码跳转链接
     * <AUTHOR>
     * @since 2023/4/17
     */
    @ApiOperation("获取微信二维码链接")
    @RequestMapping(value = "/channel/api/wx/customer/{appId}/wxaCodeCreateByPage", method = RequestMethod.POST)
    WhaleResp<String> wxaCodeCreateByPage(@PathVariable @ApiParam(name = "appId", value = "appId") String appId,
                                          @RequestParam("aid") @ApiParam(name = "aid", value = "渠道编号") String aid,
                                          @RequestParam("version") @ApiParam(name = "version", value = "版本号") String version,
                                          @RequestBody WxaCodeCreateInput wxaCodeCreateInput);

    @PostMapping(value = "/channel/api/customer/onlinePromotion/queryCustVisitInfoList")
    WhaleResp<CustVisitInfoOutput> queryCustVisitInfoList(
                                                          @RequestParam("aid") @ApiParam(name = "aid", value = "渠道编号") String aid,
                                                          @RequestParam("version") @ApiParam(name = "version", value = "版本号") String version,
            @RequestBody CustVisitListQueryInput custVisitListQueryInput);

    @PostMapping(value = "/channel/api/customer/onlinePromotion/queryCustVisitGroupList")
    WhaleResp<CustVisitGroupOutput> queryCustVisitGroupList(
                                                            @RequestParam("aid") @ApiParam(name = "aid", value = "渠道编号") String aid,
                                                            @RequestParam("version") @ApiParam(name = "version", value = "版本号") String version,
                                                            @RequestBody CustomerGroupVisitGroupQueryInput customerGroupVisitGroupQueryInput);

    @PostMapping(value = "/channel/api/customer/onlinePromotion/followHandle")
    WhaleResp followHandle(
                           @RequestParam("aid") @ApiParam(name = "aid", value = "渠道编号") String aid,
                           @RequestParam("version") @ApiParam(name = "version", value = "版本号") String version,
                           @RequestBody VisitFollowHandleInput visitFollowHandleInput);

    @PostMapping("/channel/api/product/queryProductList")
    WhaleResp<List<ProductList>> queryProductList(@RequestParam("aid") String aid,
                                                  @RequestParam("version") @ApiParam(name = "version", value = "版本号") String version,
                                                  @RequestParam("portfolioCode") @ApiParam(name = "portfolioCode", value = "组合编码") String portfolioCode,
                                                  @RequestParam("productName") @ApiParam(name = "productName", value = "险种名称") String productName);

    /**
     * 获取商品下拉列表
     */
    @PostMapping("/channel/api/sellProduct/queryProductList")
    WhaleResp<List<SellProductListOut>> querySellProductList(@RequestParam("aid") String aid,
                                                             @RequestParam("version") @ApiParam(name = "version", value = "版本号") String version,
                                                             @RequestParam("sellProductName") @ApiParam(name = "sellProductName", value = "商品名称") String sellProductName,
                                                             @RequestParam("sellProductCode") @ApiParam(name = "sellProductCode", value = "商品编码") String sellProductCode);

    @GetMapping("/channel/api/policy/preservation/detail")
    WhaleResp<PreservationDetail> getPreservationDetail(@RequestParam("aid") String aid,
                                                        @RequestParam("version") String version,
                                                        @RequestParam("preservationCode") String preservationCode,
                                                        @RequestParam("channelCode") String channelCode);

    @GetMapping("/channel/api/policy/info")
    WhaleResp<WhaleContract> getPolicy(@RequestParam("aid") String aid,
                                       @RequestParam("version") String version,
                                       @RequestParam("contractCode") String con,
                                       @RequestParam("businessScenario") String businessScenario);

    @GetMapping("/channel/api/policy/list15MinuterOnlineNewPolicy")
    WhaleResp<List<OnlineNewPolicyVo>> list15MinuterOnlineNewPolicy(@RequestParam("aid") String aid,
                                                     @RequestParam("version") String version,
                                                     @RequestParam("minute") Integer minute);
}
