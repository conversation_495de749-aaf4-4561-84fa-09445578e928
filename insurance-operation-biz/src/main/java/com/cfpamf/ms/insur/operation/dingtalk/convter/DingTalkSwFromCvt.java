package com.cfpamf.ms.insur.operation.dingtalk.convter;

import com.aliyun.dingtalkswform_1_0.models.ListFormInstancesResponseBody;
import com.cfpamf.ms.insur.operation.dingtalk.entity.DingTalkSwFormInstance;
import com.cfpamf.ms.insur.operation.dingtalk.entity.DingTalkSwFormInstanceDetail;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR> 2022/8/31 11:04
 */
@UtilityClass
@Slf4j
public class DingTalkSwFromCvt {

    /**
     * 订单表单实例
     *
     * @param formCode
     * @param body
     * @return
     */
    public static List<DingTalkSwFormInstance> cvtFormInstance(String formCode, ListFormInstancesResponseBody body) {
        body.getResult().getList();
        List<DingTalkSwFormInstance> instances = body.getResult().getList().stream()
                .map(s -> cvtFormInstance(formCode, s)).collect(Collectors.toList());
        return instances;
    }

    /**
     * 转换一个对象
     *
     * @param formCode
     * @param s
     * @return
     */
    public static DingTalkSwFormInstance cvtFormInstance(String formCode, ListFormInstancesResponseBody.ListFormInstancesResponseBodyResultList s) {
        DingTalkSwFormInstance instance = new DingTalkSwFormInstance();
        instance.setFormCode(formCode);
        instance.setSubmitUserId(s.getSubmitterUserId());
        instance.setSubmitUserName(s.getSubmitterUserName());
        instance.setSubmitTime(LocalDateTime.parse(s.getCreateTime(), DateTimeFormatter.ISO_DATE_TIME));
        instance.setFormInstanceId(s.getFormInstanceId());
        //从表单里面找日期作为日期
        LocalDate formDate = s.getForms()
                .stream().filter(fd -> Objects.equals(fd.getLabel(), "日期"))
                .findFirst().map(ListFormInstancesResponseBody.ListFormInstancesResponseBodyResultListForms::getValue)
                .map(LocalDate::parse).orElseGet(() -> {
                    log.warn("表单日期为空{}", s.getFormInstanceId());
                    return LocalDate.MIN;
                });
        instance.setFormDate(formDate);
        return instance;
    }

    /**
     * 实例转换（表单问题）
     *
     * @param body
     * @return
     */
    public static List<DingTalkSwFormInstanceDetail> cvtInstanceDetail(ListFormInstancesResponseBody body) {
        return body.getResult().getList().stream()
                .map(s -> {
                    return s.getForms().stream().map(f -> {
                        DingTalkSwFormInstanceDetail detail = new DingTalkSwFormInstanceDetail();
                        detail.setFormKey(f.getKey());
                        detail.setFormLabel(f.getLabel());
                        detail.setFormInstanceId(s.getFormInstanceId());
                        detail.setFormValue(Objects.isNull(f.getValue()) ? "" : f.getValue());
                        return detail;
                    }).collect(Collectors.toList());
                }).flatMap(Collection::stream).collect(Collectors.toList());
    }

}
