package com.cfpamf.ms.insur.operation.phoenix.dao;

import com.cfpamf.ms.insur.operation.base.dao.CommonMapper;
import com.cfpamf.ms.insur.operation.phoenix.pojo.dto.TransferPolicyDto;
import com.cfpamf.ms.insur.operation.phoenix.pojo.po.PhoenixEmpTodo;
import com.cfpamf.ms.insur.operation.phoenix.pojo.po.PolicyRenewalBaseInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface PolicyRenewalBaseInfoMapper extends CommonMapper<PolicyRenewalBaseInfo> {

    List<PolicyRenewalBaseInfo> listPolicyRenewalBaseInfoByOldPolicyNos(@Param("oldPolicyList") List<String> oldPolicyList);

    List<String> listExistTransferPolicyNo(@Param("transferPolicyList")List<String> transferPolicyList) ;

    List<TransferPolicyDto> autoQueryPersonTransferPolicy(@Param("newPolicyNo")String newPolicyNo,@Param("riskCategory2")String riskCategory2);

    List<TransferPolicyDto> autoQueryGroupTransferPolicy(@Param("newPolicyNo")String newPolicyNo,@Param("riskCategory2")String riskCategory2);

    int updateRenewedStatusByPolicyNo(PolicyRenewalBaseInfo baseInfo);
}
