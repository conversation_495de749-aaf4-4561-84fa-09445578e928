package com.cfpamf.ms.insur.operation.phoenix.service;

import com.alibaba.fastjson.JSON;
import com.cfpamf.common.ms.exception.MSBizNormalException;
import com.cfpamf.ms.bms.facade.constants.BizExceptEnum;
import com.cfpamf.ms.insur.operation.phoenix.api.WhaleApiProperties;
import com.cfpamf.ms.insur.operation.phoenix.api.WhaleCustClient;
import com.cfpamf.ms.insur.operation.phoenix.api.WhaleOrderClient;
import com.cfpamf.ms.insur.operation.phoenix.api.model.*;
import com.cfpamf.ms.insur.operation.pk.enums.EnumIndicatorType;
import com.cfpamf.ms.insur.operation.pk.pojo.dto.InsIndicators;
import com.cfpamf.ms.insur.operation.pk.pojo.req.IndicatorsRequest;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR> 2022/12/23 10:09
 */
@Slf4j
@AllArgsConstructor
@Service
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class WhaleApiService {

    WhaleOrderClient orderClient;

    WhaleCustClient custClient;

    WhaleApiProperties properties;

    public String urlLink(UrlReq req) {
        log.info("orderClient= {}; properties= {}",orderClient,properties);
        log.info("properties.getAid()= {}; req= {}",properties.getAid(),JSON.toJSONString(req));
        WhaleResp<String> resp = orderClient.urlLink(properties.getAid(),
                "v1.0"
                , req);
        log.info("resp= {}",JSON.toJSONString(resp));
        if (resp.isSuccess()) {
            return resp.getData();
        }
        log.error("生成小程序url失败{}", JSON.toJSONString(resp));
        throw new MSBizNormalException(BizExceptEnum.FacadeCallFail.getCode(), "生成小程序url失败");
    }

    public String breakReport(ReportApplyReq req) {
        WhaleResp<String> resp = orderClient.breakReport(properties.getAid(),
                "v1.0",
                req);
        if (resp.isSuccess()) {
            return resp.getData();
        }
        log.error("断保话术报告申请{}", JSON.toJSONString(resp));
        throw new MSBizNormalException(BizExceptEnum.FacadeCallFail.getCode(), "断保话术报告申请失败");
    }

    public List<InsIndicators> userCount(@Valid IndicatorsRequest request, boolean avg) {

        PkUserQueryRequest whaleReq = new PkUserQueryRequest();
        whaleReq.setType(request.getType());
        whaleReq.setValue(request.getCodeList());
        whaleReq.setBeginTime(request.getStartDate());
        whaleReq.setEndTime(request.getEndDate());
        WhaleResp<List<PkUserQueryResp>> listWhaleResp = custClient.userCount(properties.getAid(), "v1.0", whaleReq);

        if (listWhaleResp.isSuccess()) {
            List<PkUserQueryResp> data = Objects.isNull(listWhaleResp.getData()) ? Collections.emptyList() : listWhaleResp.getData();

            return request.getCodeList()
                    .stream()
                    .map(s -> {
                        InsIndicators insIndicators = new InsIndicators();
                        insIndicators.setIndicatorType(avg ? EnumIndicatorType.REG_CUST_AVG.getCode() : EnumIndicatorType.REG_CUST.getCode());
                        Optional<PkUserQueryResp> any = data.stream().filter(whaleData -> Objects.equals(whaleData.getValue(), s)).findAny();
                        insIndicators.setValue(new BigDecimal(any.map(PkUserQueryResp::getCount).orElse(0)));
                        insIndicators.setName(s);
                        insIndicators.setCode(s);
                        return insIndicators;
                    }).collect(Collectors.toList());
        }
        log.error("实时注册客户查询{}", JSON.toJSONString(listWhaleResp));
        throw new MSBizNormalException(BizExceptEnum.FacadeCallFail.getCode(), "实时注册客户查询失败");
    }
}
