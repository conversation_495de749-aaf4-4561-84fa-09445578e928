package com.cfpamf.ms.insur.operation.call.feign.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 第三方呼叫服务响应DTO
 *
 * <AUTHOR>
 * @date 2025-07-22
 */
@Data
public class CallServiceResponseDto {

    @ApiModelProperty("结果")
    @JsonProperty("Result")
    private CallResult result;

    @ApiModelProperty("数据")
    private Object data;

    @ApiModelProperty("消息")
    private String message;

    @ApiModelProperty("是否成功")
    private Boolean success;

    @Data
    public static class CallResult {
        @ApiModelProperty("呼叫SID")
        private String callSid;
    }
}
