package com.cfpamf.ms.insur.operation.call.feign.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

/**
 * 第三方呼叫服务响应DTO
 *
 * <AUTHOR>
 * @date 2025-07-22
 */
@Data
@ApiModel("第三方呼叫服务响应")
@FieldDefaults(level = AccessLevel.PRIVATE)
public class CallServiceResponseDto {

    @ApiModelProperty(value = "结果", notes = "呼叫结果信息")
    @JsonProperty("Result")
    CallResult result;

    @ApiModelProperty(value = "数据", notes = "额外数据")
    Object data;

    @ApiModelProperty(value = "消息", example = "呼叫发起成功")
    String message;

    @ApiModelProperty(value = "是否成功", example = "true")
    Boolean success;

    @Data
    @ApiModel("呼叫结果")
    @FieldDefaults(level = AccessLevel.PRIVATE)
    public static class CallResult {
        @ApiModelProperty(value = "呼叫SID", example = "call_sid_123456")
        String callSid;
    }
}
