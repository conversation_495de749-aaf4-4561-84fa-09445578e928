package com.cfpamf.ms.insur.operation.addCommission.service.preservation;

import com.alibaba.fastjson.JSONObject;
import com.cfpamf.ms.insur.operation.addCommission.convertor.AddCommissionConvert;
import com.cfpamf.ms.insur.operation.addCommission.dao.*;
import com.cfpamf.ms.insur.operation.addCommission.dto.*;
import com.cfpamf.ms.insur.operation.addCommission.po.WhaleAddCommissionDetail;
import com.cfpamf.ms.insur.operation.addCommission.po.WhaleAddCommissionDetailItem;
import com.cfpamf.ms.insur.operation.addCommission.po.WhaleAddCommissionSettlementPush;
import com.cfpamf.ms.insur.operation.base.util.DateUtils;
import com.cfpamf.ms.insur.operation.whale.model.PreservationDetail;
import com.cfpamf.ms.insur.operation.whale.model.WhaleContract;
import lombok.AccessLevel;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 保单删除处理服务类
 * 该类用于处理保单删除相关的加佣数据备份和更新操作
 * <AUTHOR>
 */
@Component("deletePreservation")
@FieldDefaults(level = AccessLevel.PRIVATE)
@Slf4j
public class PolicyDeleteHandleService extends AbstractPreservationHandleService {

    @Autowired
    WhaleAddCommissionDetailItemMapper whaleAddCommissionDetailItemMapper;
    @Autowired
    WhaleAddCommissionDetailMapper whaleAddCommissionDetailMapper;
    @Autowired
    WhaleAddCommissionDetailItemBackUpsMapper whaleAddCommissionDetailItemBackUpsMapper;
    @Autowired
    WhaleAddCommissionDetailBackUpsMapper whaleAddCommissionDetailBackUpsMapper;
    @Autowired
    WhaleAddCommissionSettlementPushMapper whaleAddCommissionSettlementPushMapper;

    /**
     * 初始化推送数据
     * 根据保单号和操作DTO准备加佣结算推送数据
     *
     * @param operationDto  操作DTO，包含操作相关信息
     * @param pushDtos      推送DTO列表，用于收集推送业财的数据
     * @return              准备好的加佣结算推送数据列表
     */
    @Override
    public PreservationSettlementDto doAddCommissionCorrect(PreservationOperationDto operationDto, List<AddCommissionSettlementPushDto> pushDtos, PreservationDto preservationDto) {
        log.info("保单删除加佣记录处理,operationDto:{},pushDto:{}", JSONObject.toJSONString(operationDto), JSONObject.toJSONString(pushDtos));

        // 将推送DTO转换为加佣明细项，并设置结算状态为作废
        List<WhaleAddCommissionDetailItem> whaleAddCommissionDetailItems = pushDtos.stream().map(dto -> {
            WhaleAddCommissionDetailItem whaleAddCommissionDetailItem = AddCommissionConvert.INS.cvtItem(dto);
            whaleAddCommissionDetailItem.setSettlementState(3); // 3代表作废状态
            return whaleAddCommissionDetailItem;
        }).collect(Collectors.toList());

        log.info("保单删除加佣明细：{}", JSONObject.toJSONString(whaleAddCommissionDetailItems));

        String requestId = preservationDto.getRequestId();

        // 使用AOP上下文获取当前代理对象
        PolicyDeleteHandleService me = (PolicyDeleteHandleService) AopContext.currentProxy();

        // 根据明细项转换为汇总PO
        List<WhaleAddCommissionDetail> detailList = convertDetailPo(whaleAddCommissionDetailItems);

        List<AddCommissionSettlementPushDto> result = pushDtos.stream()
                .filter(dto->!Objects.isNull(dto.getSettlementState()) && dto.getSettlementState()!=0)
                .collect(Collectors.toList());


        // 执行事务操作
        me.transactional(() -> {
            // 备份明细表数据
            whaleAddCommissionDetailItemBackUpsMapper.insertBackUps(whaleAddCommissionDetailItems, "保单删除");
            // 备份汇总表数据
            whaleAddCommissionDetailBackUpsMapper.insertBackUps(whaleAddCommissionDetailItems, "保单删除");
            // 更新加佣记录为作废
            whaleAddCommissionDetailItemMapper.updateSettlementState(whaleAddCommissionDetailItems);
            // 更新对应UUID的汇总比例
            whaleAddCommissionDetailMapper.updateProportion(detailList);
            //插入业财结算推送数据明细
            if (!CollectionUtils.isEmpty(result)) {
                result.forEach(dto -> {
                    dto.setEventTypeCode(operationDto.getSettlementEventType().getEventCode());
                    dto.setAccountTime(DateUtils.toDateString(new Date()));
                    dto.setApplyTime(DateUtils.toDateString(new Date()));
                    dto.setPreservationEffectTime(DateUtils.toDateString(new Date()));
                    dto.setSettlementState(3);
                });

                List<WhaleAddCommissionSettlementPush> settlementPushes = result.stream().map(dto -> {
                    WhaleAddCommissionSettlementPush settlementPush = new WhaleAddCommissionSettlementPush();
                    BeanUtils.copyProperties(dto, settlementPush);
                    settlementPush.setRequestId(requestId);
                    return settlementPush;
                }).collect(Collectors.toList());
                whaleAddCommissionSettlementPushMapper.insertList(settlementPushes);
            }
        });

        // 返回处理后的推送数据
        PreservationSettlementDto settlementDto = new PreservationSettlementDto();
        settlementDto.setRequestId(requestId);
        settlementDto.setSettlementPushDtos(result);
        log.info("保单删除操作处理完成，结算参数：" + JSONObject.toJSONString(settlementDto));
        return settlementDto;
    }

    /**
     * 构建操作DTO
     * 根据保全明细中的重做DTO设置操作DTO的相关信息
     *
     * @param operationDto      操作DTO，用于保存操作相关信息
     * @param preservationDto 保全明细，包含重做DTO等信息
     */
    @Override
    public void getOperationDto(PreservationOperationDto operationDto, PreservationDto preservationDto) {
        CommissionRedoDTO commissionRedoDTO = preservationDto.getCommissionRedoDTO();
        operationDto.setSettlementEventType(cvtSettlementEventType(commissionRedoDTO));
        PreservationOperationDetail before = new PreservationOperationDetail(commissionRedoDTO.getPolicyNo(),
                commissionRedoDTO.getEndorsementNo(), commissionRedoDTO.getTermNum());
        operationDto.setBefore(before);
    }

    /**
     * 执行事务操作
     * 用于需要在一个单独事务中执行的操作
     *
     * @param runnable  需要在事务中执行的代码块
     */
    @Transactional(rollbackFor = Exception.class)
    public void transactional(Runnable runnable) {
        runnable.run();
    }
}
