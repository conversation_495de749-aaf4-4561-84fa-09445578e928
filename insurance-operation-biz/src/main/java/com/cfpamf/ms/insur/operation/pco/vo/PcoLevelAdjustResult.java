package com.cfpamf.ms.insur.operation.pco.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class PcoLevelAdjustResult {
    /**
     * 导入总条数
     */
    @ApiModelProperty("导入总条数")
    private Integer totalNumber;

    /**
     * 成功条数
     */
    @ApiModelProperty("成功条数")
    private Integer successNumber;

    /**
     * 失败条数
     */
    @ApiModelProperty("失败条数")
    private Integer errorNumber;

    /**
     * 失败记录下载地址
     */
    @ApiModelProperty("失败记录下载地址")
    private String errUrl;
}
