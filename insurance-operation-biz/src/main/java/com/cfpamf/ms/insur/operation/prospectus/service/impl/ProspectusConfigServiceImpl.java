package com.cfpamf.ms.insur.operation.prospectus.service.impl;

import com.cfpamf.ms.insur.operation.base.helper.WxCheckAuthorityHelper;
import com.cfpamf.ms.insur.operation.base.util.HttpRequestUtil;
import com.cfpamf.ms.insur.operation.base.vo.WxSessionVO;
import com.cfpamf.ms.insur.operation.prospectus.dao.ProductMapper;
import com.cfpamf.ms.insur.operation.prospectus.dao.ProspectusConfigMapper;
import com.cfpamf.ms.insur.operation.prospectus.dao.ProspectusMapper;
import com.cfpamf.ms.insur.operation.prospectus.entity.ProspectusConfig;
import com.cfpamf.ms.insur.operation.prospectus.entity.SmProduct;
import com.cfpamf.ms.insur.operation.prospectus.exception.ProspectusConfigBusinessException;
import com.cfpamf.ms.insur.operation.prospectus.form.ProspectusConfigForm;
import com.cfpamf.ms.insur.operation.prospectus.form.ProspectusConfigSearchForm;
import com.cfpamf.ms.insur.operation.prospectus.form.ProspectusProductSearchForm;
import com.cfpamf.ms.insur.operation.prospectus.service.ProspectusConfigService;
import com.cfpamf.ms.insur.operation.prospectus.vo.ProspectusConfigVo;
import com.cfpamf.ms.insur.operation.prospectus.vo.ProspectusProductVo;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/5/19 15:03
 */
@Service
public class ProspectusConfigServiceImpl implements ProspectusConfigService {

    private ProspectusConfigMapper prospectusConfigMapper;
    private ProspectusMapper prospectusMapper;
    private ProductMapper productMapper;
    private WxCheckAuthorityHelper wxCheckAuthorityHelper;

    public ProspectusConfigServiceImpl(ProspectusConfigMapper prospectusConfigMapper, ProspectusMapper prospectusMapper, ProductMapper productMapper, WxCheckAuthorityHelper wxCheckAuthorityHelper) {
        this.prospectusConfigMapper = prospectusConfigMapper;
        this.prospectusMapper = prospectusMapper;
        this.productMapper = productMapper;
        this.wxCheckAuthorityHelper = wxCheckAuthorityHelper;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void save(ProspectusConfigForm prospectusConfigForm) {
        //校验计划书配置表单
        checkProspectusConfigForm(prospectusConfigForm, null);
        //生成ProspectusConfig实体
        ProspectusConfig prospectusConfig = new ProspectusConfig();
        BeanUtils.copyProperties(prospectusConfigForm, prospectusConfig);
        prospectusConfig.setUpdateBy(HttpRequestUtil.getUserId());
        prospectusConfig.setCreateBy(HttpRequestUtil.getUserId());
        //如果未对产品是否启用配置 默认启用
        if (Objects.isNull(prospectusConfig.getEnabled())) {
            prospectusConfig.setEnabled(1);
        }
        //保存实体
        prospectusConfigMapper.insert(prospectusConfig);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(Long id, ProspectusConfigForm prospectusConfigForm) {
        //判断当前更新的计划书配置是否存在
        ProspectusConfig prospectusConfig = prospectusConfigMapper.getById(id);
        if (Objects.isNull(prospectusConfig)) {
            throw ProspectusConfigBusinessException.PROSPECTUS_CONFIG_NOT_EXIST;
        }
        //校验计划书表单
        checkProspectusConfigForm(prospectusConfigForm, id);
        //修改ProspectusConfig实体
        BeanUtils.copyProperties(prospectusConfigForm, prospectusConfig);
        prospectusConfig.setUpdateBy(HttpRequestUtil.getUserId());
        //修改实体
        prospectusConfigMapper.updateByPrimaryKey(prospectusConfig);
    }


    @Override
    public ProspectusConfigVo getById(Long id) {
        //查找计划书配置实体
        ProspectusConfig prospectusConfig = prospectusConfigMapper.getById(id);
        //获取视图对象
        return getProspectusConfigVo(prospectusConfig);
    }

    @Override
    public ProspectusConfigVo getByProductId(Long productId) {
        //查找计划书配置实体
        ProspectusConfig prospectusConfig = prospectusConfigMapper.getByProductId(productId);
        //获取视图对象
        return getProspectusConfigVo(prospectusConfig);
    }

    /**
     * 通过计划书配置实体转换为视图对象
     *
     * @param prospectusConfig
     * @return
     */
    public ProspectusConfigVo getProspectusConfigVo(ProspectusConfig prospectusConfig) {
        if (Objects.isNull(prospectusConfig)) {
            return null;
        }
        //转换实体成为视图vo
        ProspectusConfigVo prospectusConfigVo = new ProspectusConfigVo();
        BeanUtils.copyProperties(prospectusConfig, prospectusConfigVo);
        //获取计划书配置的产品信息
        SmProduct smProduct = productMapper.getById(prospectusConfig.getProductId());
        if (Objects.isNull(smProduct)) {
            return prospectusConfigVo;
        }
        //设置计划书配置的产品名
        prospectusConfigVo.setProductName(smProduct.getProductName());
        return prospectusConfigVo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(Long id) {
        //查找计划书配置实体
        ProspectusConfig prospectusConfig = prospectusConfigMapper.getById(id);
        if (Objects.isNull(prospectusConfig)) {
            return;
        }
        //判断是否被使用 如果被使用 不让删除
        int count = prospectusMapper.getCountByProductId(prospectusConfig.getProductId());
        if (count > 0) {
            throw ProspectusConfigBusinessException.PROSPECTUS_CONFIG_USED_NOT_CAN_DELETE;
        }
        //设置为软删除状态
        prospectusConfig.setEnabledFlag(1);
        //修改实体
        prospectusConfigMapper.updateByPrimaryKey(prospectusConfig);
    }

    @Override
    public PageInfo<ProspectusConfigVo> search(ProspectusConfigSearchForm prospectusConfigSearchForm) {
        //查找计划书配置分页数据
        PageInfo<ProspectusConfigVo> prospectusConfigPageInfo = PageHelper.startPage(prospectusConfigSearchForm.getPageNo(), prospectusConfigSearchForm.getPageSize())
                .doSelectPageInfo(() -> prospectusConfigMapper.search(prospectusConfigSearchForm));
        List<ProspectusConfigVo> prospectusConfigList = prospectusConfigPageInfo.getList();
        if (CollectionUtils.isEmpty(prospectusConfigList)) {
            return prospectusConfigPageInfo;
        }
        //初始化计划书配置产品信息
        List<Long> productIdList = prospectusConfigList.stream()
                .map(ProspectusConfigVo::getProductId)
                .collect(Collectors.toList());
        //获取<产品id ,产品name>的map
        Map<Long, String> productNameMap = productMapper.getByIdList(productIdList).stream()
                .collect(Collectors.toMap(SmProduct::getId, SmProduct::getProductName, (e1, e2) -> e2));
        for (ProspectusConfigVo prospectusConfigVo : prospectusConfigList) {
            prospectusConfigVo.setProductName(productNameMap.get(prospectusConfigVo.getProductId()));
        }
        return prospectusConfigPageInfo;
    }

    @Override
    public PageInfo<ProspectusProductVo> searchProspectusProduct(ProspectusProductSearchForm prospectusProductSearchForm) {
        WxSessionVO wxSessionVO = wxCheckAuthorityHelper.checkAuthority(prospectusProductSearchForm.getOpenId(), prospectusProductSearchForm.getAuthorization());

        List<Long> productIdList = productMapper.listProductSalesOrgByOrgPath(wxSessionVO.getRegionName());
        if (CollectionUtils.isEmpty(productIdList)) {
            return new PageInfo<>(Collections.emptyList());
        }
        return PageHelper.startPage(prospectusProductSearchForm.getPageNo(), prospectusProductSearchForm.getPageSize())
                .doSelectPageInfo(() -> prospectusConfigMapper.searchProspectusProduct(prospectusProductSearchForm.getProductName(), productIdList));

    }

    /**
     * 1.产品id存在
     * 2.名称不重复
     * 3.产品id不存在已有的配置
     *
     * @param prospectusConfigForm
     */
    private void checkProspectusConfigForm(ProspectusConfigForm prospectusConfigForm, Long id) {
        Long productId = prospectusConfigForm.getProductId();
        //查看产品是否存在
        SmProduct smProduct = productMapper.getById(productId);
        if (Objects.isNull(smProduct)) {
            throw ProspectusConfigBusinessException.PROSPECTUS_CONFIG_PRODUCT_NOT_EXIST;
        }
        //判断配置的产品id是否已存在配置
        ProspectusConfig prospectusConfig = prospectusConfigMapper.getByProductId(productId);
        if (Objects.nonNull(prospectusConfig) && !Objects.equals(prospectusConfig.getId(), id)) {
            throw ProspectusConfigBusinessException.PROSPECTUS_CONFIG_PRODUCT_EXIST_CONFIG;
        }

        //判断配置的名称是否已存在
        ProspectusConfig prospectusConfigByName = prospectusConfigMapper.getByName(prospectusConfigForm.getName());
        if (Objects.nonNull(prospectusConfigByName) && !Objects.equals(prospectusConfigByName.getId(), id)) {
            throw ProspectusConfigBusinessException.PROSPECTUS_CONFIG_NAME_EXIST;
        }
    }
}
