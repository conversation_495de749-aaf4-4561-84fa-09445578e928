package com.cfpamf.ms.insur.operation.phoenix.service;

import com.alibaba.fastjson.JSON;
import com.cfpamf.ms.insur.operation.order.dao.SmOrderMapper;
import com.cfpamf.ms.insur.operation.order.dto.SmBaseOrderVO;
import com.cfpamf.ms.insur.operation.phoenix.dao.PolicyRenewalBaseInfoMapper;
import com.cfpamf.ms.insur.operation.phoenix.dao.SmOrderRenewalFollowMapper;
import com.cfpamf.ms.insur.operation.phoenix.enums.EnumRenewalIntention;
import com.cfpamf.ms.insur.operation.phoenix.enums.EnumTodoBizType;
import com.cfpamf.ms.insur.operation.phoenix.pojo.dto.SmOrderRenewalFollowDto;
import com.cfpamf.ms.insur.operation.phoenix.pojo.dto.TransferPolicyDto;
import com.cfpamf.ms.insur.operation.phoenix.pojo.po.PolicyRenewalBaseInfo;
import com.cfpamf.ms.insur.operation.phoenix.pojo.po.SmOrderRenewalFollowPo;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
@AllArgsConstructor
public class SmOrderRenewalFollowService  {

    SmOrderRenewalFollowMapper smOrderRenewalFollowMapper;

    PhoenixEmpTodoService phoenixEmpTodoService;

    SmOrderMapper smOrderMapper;

    PolicyRenewalBaseInfoMapper policyRenewalBaseInfoMapper;



    public void saveRenewalFollowRecord(SmOrderRenewalFollowDto dto) {
        //更新以往的跟进记录最新状态为0
        smOrderRenewalFollowMapper.updateNewest(dto.getPolicyNo());

        SmOrderRenewalFollowPo po = new SmOrderRenewalFollowPo();
        BeanUtils.copyProperties(dto,po);
        po.setId(null);
        po.setNewest(1);
        po.setEnabledFlag(0);;
        smOrderRenewalFollowMapper.insert(po);
        //初始化跟进排序字段
        Integer followSortNo = initFollowSortNo(dto);

        if(EnumRenewalIntention.TRANSFER.getCode().equals(dto.getIntention())){
            SmBaseOrderVO order = smOrderMapper.getBaseOrderInfoByOrderId(dto.getTransferOrderId());
            TransferPolicyDto vo = new TransferPolicyDto();
            vo.setFhOrderId(dto.getTransferOrderId());
            vo.setPolicyNo(dto.getTransferPolicyNo());
            vo.setPaymentTime(order.getPaymentTime());
            vo.setTotalAmount(order.getTotalAmount());
            this.updateTransferBaseInfo(vo,dto.getPolicyNo());
        }

        //不愿续保移除待办
        phoenixEmpTodoService.finishTodo(EnumTodoBizType.RENEW_SHORT,dto.getPolicyNo(),followSortNo,dto.getIntention());
    }

    public void updateTransferBaseInfo(TransferPolicyDto vo,String oldPolicyNo){
        log.info("开始组装续保基础信息，入参:TransferPolicyVo= {},oldPolicyNo={}", JSON.toJSONString(vo),oldPolicyNo);
        PolicyRenewalBaseInfo baseInfo = new PolicyRenewalBaseInfo();
        baseInfo.setOldPolicyNo(oldPolicyNo);
        baseInfo.setRenewalType("transfer");
        baseInfo.setInsStatus("renewing");
        baseInfo.setNewOrderTime(vo.getPaymentTime());
        baseInfo.setNewPolicyNo(vo.getPolicyNo());
        baseInfo.setNewPremium(vo.getTotalAmount());

        policyRenewalBaseInfoMapper.updateRenewedStatusByPolicyNo(baseInfo);


    }



    /**
     * 初始化跟进排序字段
     * @param dto 跟进dto
     * @return int
     */
    private Integer initFollowSortNo(SmOrderRenewalFollowDto dto) {
        int followSortNo;
        if (EnumRenewalIntention.WILLING.getCode().equals(dto.getIntention())) {
            followSortNo = 30;
        } else if (EnumRenewalIntention.CONSIDER.getCode().equals(dto.getIntention())) {
            followSortNo = 40;
        } else if (EnumRenewalIntention.LOSE_CONTACT.getCode().equals(dto.getIntention())) {
            followSortNo = 50;
        } else {
            followSortNo = 999;
        }
        return followSortNo;
    }


}
