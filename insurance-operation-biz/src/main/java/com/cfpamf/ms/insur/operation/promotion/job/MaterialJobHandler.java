package com.cfpamf.ms.insur.operation.promotion.job;

import cn.hutool.core.date.DateUtil;
import com.cfpamf.ms.insur.operation.activity.dao.AuthUserMapper;
import com.cfpamf.ms.insur.operation.activity.entity.AuthUser;
import com.cfpamf.ms.insur.operation.aicall.enums.AIIntentEnums;
import com.cfpamf.ms.insur.operation.aitraining.service.ScrmService;
import com.cfpamf.ms.insur.operation.phoenix.config.WechatConfigProperties;
import com.cfpamf.ms.insur.operation.phoenix.service.WxApiService;
import com.cfpamf.ms.insur.operation.promotion.dao.MaterialChangeNotifyDetailMapper;
import com.cfpamf.ms.insur.operation.promotion.dao.MaterialChangeUserNotifyDetailMapper;
import com.cfpamf.ms.insur.operation.promotion.entity.MaterialChangeNotifyDetailEntity;
import com.cfpamf.ms.insur.operation.promotion.entity.MaterialChangeUserNotifyDetailEntity;
import com.cfpamf.ms.insur.operation.promotion.service.MaterialNotifyService;
import com.cfpamf.ms.insur.operation.promotion.service.MaterialUserNotifyService;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.mp.bean.template.WxMpTemplateData;
import me.chanjar.weixin.mp.bean.template.WxMpTemplateMessage;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.net.URLEncoder;
import java.util.List;

import static cn.hutool.core.date.DatePattern.CHINESE_DATE_PATTERN;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class MaterialJobHandler {


    @Autowired
    private WechatConfigProperties configProperties;
    @Autowired
    private MaterialChangeNotifyDetailMapper materialChangeNotifyDetailMapper;
    @Autowired
    private AuthUserMapper authUserMapper;
    @Autowired
    private MaterialUserNotifyService materialUserNotifyService;
    @Autowired
    private MaterialChangeUserNotifyDetailMapper materialChangeUserNotifyDetailMapper;
    @Autowired
    private WxApiService wxApiService;
    /**
     * 推送每日
     */
    @XxlJob("generate-user-material-notify")
    public void generateUserMaterialNotify() {
        //查询所有代生成用户通知
        MaterialChangeNotifyDetailEntity query = new MaterialChangeNotifyDetailEntity();
        query.setGenerateStatus(0);
        query.setBizType(4);
        query.setLaunchStatus(2);
        query.setEnabledFlag(0);
        List<MaterialChangeNotifyDetailEntity> waitGenerateList =  materialChangeNotifyDetailMapper.select(query);
        if(CollectionUtils.isEmpty(waitGenerateList)){
            log.info("没有需要生成的素材变更通知");
            return ;
        }
        //获取所有分支员工
        List<AuthUser> users =  authUserMapper.getNotHeadUsers();
        waitGenerateList.stream().forEach(w->{
            materialUserNotifyService.generateUserNotify(users,w);
        });
    }

    @XxlJob("send-user-material-notify")
    public void sendUserMaterialNotify() {
        int times = 0;
        while (true) {
            List<MaterialChangeUserNotifyDetailEntity> list = materialChangeUserNotifyDetailMapper.listUnSendByLimitSize(1000);
            if(CollectionUtils.isEmpty(list)||times>5){
                break;
            }
            list.stream().forEach(l->{
                WxMpTemplateMessage templateMessage = new WxMpTemplateMessage();
                buildWechatMaterialNoticeData(templateMessage, DateUtil.format(l.getChangeTime(),CHINESE_DATE_PATTERN));
                templateMessage.setUrl(getWxMaterialDetailUrl(l.getMarketingId()));
                templateMessage.setTemplateId(configProperties.getMaterialChangeMessage());
                templateMessage.setToUser(l.getWxOpenId());
                boolean ret = wxApiService.sendTemplateMsg(templateMessage);
                if(ret){
                    MaterialChangeUserNotifyDetailEntity update = new MaterialChangeUserNotifyDetailEntity();
                    update.setId(l.getId());
                    update.setSendStatus(1);
                    materialChangeUserNotifyDetailMapper.updateByPrimaryKeySelective(update);
                }
            });

            times++;
        }
    }

    /**
     * 断保高意向微信通知
     * @param wtm 消息体
     * @param changeTime 触发通知时间（即素材更新时间）
     * @return WxMpTemplateMessage
     */
    private WxMpTemplateMessage buildWechatMaterialNoticeData(WxMpTemplateMessage wtm, String changeTime) {
        wtm.addData(new WxMpTemplateData("first", ""));
        wtm.addData(new WxMpTemplateData("keyword1", changeTime));
        wtm.addData(new WxMpTemplateData("keyword2", "推广素材更新"));
        wtm.addData(new WxMpTemplateData("keyword3", "待查看发朋友圈推广"));
        wtm.addData(new WxMpTemplateData("remark", ""));
        return wtm;
    }

    /**
     * 获取微信素材详情页地址
     *
     * @return String
     */
    public String getWxMaterialDetailUrl(Long materialId) {
        try {

            String insuranceJumpUrl = URLEncoder.encode(String.format(configProperties.getMaterialDetailUrl(), materialId));
            log.info("insuranceJumpUrl = {}", insuranceJumpUrl);

            String wxRedirectUrl = String.format(configProperties.getBackJumpUrl(), insuranceJumpUrl, "");
            log.info("wxRedirectUrl = {}", wxRedirectUrl);
            return  wxRedirectUrl;
            //return String.format(configProperties.getFrontCustomerDetailUrl(), url,"");
        } catch (Exception e) {
            log.error("", e);
        }
        return null;
    }

    public void main(){

    }
}
