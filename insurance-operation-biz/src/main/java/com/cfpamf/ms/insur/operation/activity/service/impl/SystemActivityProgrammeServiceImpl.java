package com.cfpamf.ms.insur.operation.activity.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.cfpamf.common.ms.exception.MSBizNormalException;
import com.cfpamf.common.ms.result.Result;
import com.cfpamf.ms.insur.operation.activity.convter.SystemActivityCvt;
import com.cfpamf.ms.insur.operation.activity.dao.*;
import com.cfpamf.ms.insur.operation.activity.entity.*;
import com.cfpamf.ms.insur.operation.activity.enums.*;
import com.cfpamf.ms.insur.operation.activity.exception.SystemActivityBusinessException;
import com.cfpamf.ms.insur.operation.activity.form.*;
import com.cfpamf.ms.insur.operation.activity.service.SmActivityRewardService;
import com.cfpamf.ms.insur.operation.activity.service.SmOrderActivityService;
import com.cfpamf.ms.insur.operation.activity.service.SystemActivityProgrammeService;
import com.cfpamf.ms.insur.operation.activity.vo.*;
import com.cfpamf.ms.insur.operation.base.constant.BaseConstants;
import com.cfpamf.ms.insur.operation.base.constant.BusinessConstants;
import com.cfpamf.ms.insur.operation.base.constant.CacheKeyConstants;
import com.cfpamf.ms.insur.operation.base.constant.GroovyCodeConstants;
import com.cfpamf.ms.insur.operation.base.exception.BusinessException;
import com.cfpamf.ms.insur.operation.base.form.DataAuthPageForm;
import com.cfpamf.ms.insur.operation.base.exception.BusinessException;
import com.cfpamf.ms.insur.operation.base.helper.BmsHelper;
import com.cfpamf.ms.insur.operation.base.helper.WxCheckAuthorityHelper;
import com.cfpamf.ms.insur.operation.base.service.DataAuthService;
import com.cfpamf.ms.insur.operation.base.util.HttpRequestUtil;
import com.cfpamf.ms.insur.operation.base.util.LambdaUtils;
import com.cfpamf.ms.insur.operation.base.vo.WxSessionVO;
import com.cfpamf.ms.insur.operation.fegin.bms.enums.ExcptEnum;
import com.cfpamf.ms.insur.operation.fegin.bms.facede.BmsFacade;
import com.cfpamf.ms.insur.operation.fegin.bms.response.vo.UserDetailVO;
import com.cfpamf.ms.insur.operation.fegin.els.helper.EventInfoHelper;
import com.cfpamf.ms.insur.operation.fegin.els.response.vo.EventInfoDetailVO;
import com.cfpamf.ms.insur.operation.prospectus.dao.PlanMapper;
import com.cfpamf.ms.insur.operation.prospectus.dao.ProductMapper;
import com.cfpamf.ms.insur.operation.prospectus.entity.SmProduct;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/7/6 17:11
 */
@Service
@Slf4j
public class SystemActivityProgrammeServiceImpl implements SystemActivityProgrammeService {

    private SystemActivityProgrammeMapper systemActivityProgrammeMapper;
    private SystemActivityProductMapper systemActivityProductMapper;

    @Autowired
    private SystemActivityProductWhaleMapper systemActivityProductWhaleMapper;
    private SystemActivityProductRuleMapper systemActivityProductRuleMapper;

    @Autowired
    private SystemActivityProductRuleWhaleMapper systemActivityProductRuleWhaleMapper;
    private SystemActivityRefMapper systemActivityRefMapper;
    private ProductMapper productMapper;
    private PlanMapper planMapper;
    private WxCheckAuthorityHelper wxCheckAuthorityHelper;
    private AuthUserMapper authUserMapper;
    private BmsHelper bmsHelper;

    private SystemGroovyRuleMapper ruleMapper;
    private EventInfoHelper eventInfoHelper;
    private OperationActivityOrderMessageMapper operationActivityOrderMessageMapper;

    private final SystemActivityConstRuleMapper constRuleMapper;
    @Autowired
    private SystemActivityConstRuleWhaleMapper constRuleWhaleMapper;

    @Autowired
    private SmOrderActivityService smOrderActivityService;
    @Autowired
    private SmOrderActivityMapper smOrderActivityMapper;
    @Autowired
    private SmActivityOperationRecordMapper operationRecordMapper;
    @Autowired
    private SmActivitySuspendCensusMapper suspendCensusMapper;
    @Autowired
    SmActivityRewardService smActivityRewardService;
    @Autowired
    private BmsFacade bmsFacade;
    @Autowired
    DataAuthService dataAuthService;

    public SystemActivityProgrammeServiceImpl(SystemActivityProgrammeMapper systemActivityProgrammeMapper, SystemActivityProductMapper systemActivityProductMapper, SystemActivityProductRuleMapper systemActivityProductRuleMapper, SystemActivityRefMapper systemActivityRefMapper, ProductMapper productMapper, PlanMapper planMapper, WxCheckAuthorityHelper wxCheckAuthorityHelper, AuthUserMapper authUserMapper, BmsHelper bmsHelper, SystemGroovyRuleMapper ruleMapper, EventInfoHelper eventInfoHelper, OperationActivityOrderMessageMapper operationActivityOrderMessageMapper, SystemActivityConstRuleMapper constRuleMapper) {
        this.systemActivityProgrammeMapper = systemActivityProgrammeMapper;
        this.systemActivityProductMapper = systemActivityProductMapper;
        this.systemActivityProductRuleMapper = systemActivityProductRuleMapper;
        this.systemActivityRefMapper = systemActivityRefMapper;
        this.productMapper = productMapper;
        this.planMapper = planMapper;
        this.wxCheckAuthorityHelper = wxCheckAuthorityHelper;
        this.authUserMapper = authUserMapper;
        this.bmsHelper = bmsHelper;
        this.ruleMapper = ruleMapper;
        this.eventInfoHelper = eventInfoHelper;
        this.operationActivityOrderMessageMapper = operationActivityOrderMessageMapper;
        this.constRuleMapper = constRuleMapper;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long create(SystemActivityProgrammeForm systemActivityProgrammeForm) {
        checkSystemActivityProgrammeForm(systemActivityProgrammeForm);
        return saveSystemActivityProgramme(systemActivityProgrammeForm);
    }

    @Override
    public PageInfo<SystemActivityProgrammeVo> search(SystemActivitySearchForm searchForm) {
        if (searchForm.isAll()) {
            List<SystemActivityProgrammeVo> systemActivityProgrammeVoList = systemActivityProgrammeMapper.search(searchForm);
            //设置活动产品以及规则
            setActivityProductAndRef(systemActivityProgrammeVoList);
            //计算活动状态
            systemActivityProgrammeVoList.forEach(SystemActivityProgrammeVo::calculationActiveState);
            return new PageInfo<>(systemActivityProgrammeVoList);
        } else {
            //查询分页数据
            PageInfo<SystemActivityProgrammeVo> pageInfo = PageHelper.startPage(searchForm.getPageNo(), searchForm.getPageSize())
                    .doSelectPageInfo(() -> systemActivityProgrammeMapper.search(searchForm));

            List<SystemActivityProgrammeVo> activityProgrammeList = pageInfo.getList();
            //设置活动产品以及规则
            setActivityProductAndRef(activityProgrammeList);
            //计算活动状态
            activityProgrammeList.forEach(SystemActivityProgrammeVo::calculationActiveState);
            return pageInfo;
        }

    }

    @Override
    public List<SystemActivityProgrammeVo> getSystemActivityProgrammeByState(EnumActivityState activityState) {
        SystemActivitySearchForm searchForm = new SystemActivitySearchForm();
        searchForm.setActiveState(activityState.name());
        searchForm.setRelativeTime(LocalDateTime.now().minusDays(1));
        List<SystemActivityProgrammeVo> systemActivityProgrammeList = systemActivityProgrammeMapper.searchByLazy(searchForm);
        if (CollectionUtils.isNotEmpty(systemActivityProgrammeList)) {
            //设置活动产品以及规则
            setActivityProductAndRef(systemActivityProgrammeList);
        }
        return systemActivityProgrammeList;
    }

    @Override
    public List<SystemActivityProgrammeVo> getActivityByStateAndRelativeTime(SystemActivitySearchForm searchForm) {
        List<SystemActivityProgrammeVo> systemActivityProgrammeList = systemActivityProgrammeMapper.searchByLazy(searchForm);
        if (CollectionUtils.isNotEmpty(systemActivityProgrammeList)) {
            //设置活动产品以及规则
            setActivityProductAndRef(systemActivityProgrammeList);
        }
        return systemActivityProgrammeList;
    }

    @Override
    public SystemActivityProgrammeVo getSystemActivityProgrammeById(Long id) {
        SystemActivityProgrammeVo systemActivityProgramme = systemActivityProgrammeMapper.getById(id);
        if (systemActivityProgramme != null) {
            //设置活动产品以及规则
            setActivityProduct(systemActivityProgramme);
        }
        return systemActivityProgramme;
    }

    @Override
    public List<SystemActivityConstProductVO> seachProductActivity(SystemActivityConstProductForm constProductForm) {
        return constProductForm.getSceneForms()
                .stream()
                .map(pa -> {
                    SystemActivityConstProductSceneQueryForm queryForm = new SystemActivityConstProductSceneQueryForm();
                    queryForm.setStartTime(constProductForm.getStartTime());
                    queryForm.setEndTime(constProductForm.getEndTime());
                    queryForm.setProductIds(pa.getProductIds());
                    queryForm.setRuleType(pa.getRuleType());

                    SystemActivityConstProductVO res = new SystemActivityConstProductVO();
                    if ("nb".equals(constProductForm.getActivityPlatform())) {
                        res.setNum(systemActivityProductMapper.searchProductActivity(queryForm));
                    } else {
                        res.setNum(systemActivityProductWhaleMapper.searchProductActivity(queryForm));
                    }
                    res.setRuleType(pa.getRuleType());
                    return res;
                }).collect(Collectors.toList());

    }


    @Override
    public SystemActivityProgrammeVo detail(Long id) {
        //获取活动方案详情 不包括推荐产品信息
        SystemActivityProgrammeVo systemActivityProgrammeVo = getSystemActivityProgrammeVo(id);
        if (systemActivityProgrammeVo == null) {
            return null;
        }

        //推荐产品
        List<Long> productIdList = systemActivityProgrammeVo.getProductIdList();
        if (CollectionUtils.isNotEmpty(productIdList)) {
            List<RecommendedProductVo> recommendedProductList = productMapper.getRecommendedProductByIdList(productIdList);
            systemActivityProgrammeVo.setRecommendedProductList(recommendedProductList);
        }
        //计算活动状态
        systemActivityProgrammeVo.calculationActiveState();
        return systemActivityProgrammeVo;
    }

    /**
     * 获取活动方案详情 不包括推荐产品信息
     *
     * @param id
     * @return
     */
    private SystemActivityProgrammeVo getSystemActivityProgrammeVo(Long id) {
        SystemActivityProgrammeVo systemActivityProgrammeVo = systemActivityProgrammeMapper.getById(id);
        if (Objects.isNull(systemActivityProgrammeVo)) {
            return null;
        }
        //活动编号
        systemActivityProgrammeVo.setActivityCode(String.format("%05d", id));
        //设置活动产品以及规则
        setActivityProductAndRef(Lists.newArrayList(systemActivityProgrammeVo));
        //设置关联活动
        List<SystemActivityRefVo> activityRefList = systemActivityRefMapper.getBySystemActivityId(id);
        systemActivityProgrammeVo.setSystemActivityRefList(activityRefList);

        return systemActivityProgrammeVo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, transactionManager = "dataSourceTransactionManagerSafes")
    @CacheEvict(cacheNames = {CacheKeyConstants.ACTIVITY_PRODUCT, CacheKeyConstants.ACTIVITY_PRODUCT_RULE}, allEntries = true)
    public void update(Long id, SystemActivityProgrammeForm systemActivityProgrammeForm) {
        SystemActivityProgramme systemActivityProgramme = systemActivityProgrammeMapper.selectByPrimaryKey(id);
        //校验活动方案是否存在
        if (Objects.isNull(systemActivityProgramme)) {
            throw SystemActivityBusinessException.SYSTEM_ACTIVITY_NOT_EXIST;
        }
        Integer activeFlag = systemActivityProgramme.getActiveFlag();
        EnumActivityState activityState = EnumActivityState.getActivityStateByCodeAndTime(systemActivityProgramme.getStartTime(), systemActivityProgramme.getEndTime(), activeFlag);
        //活动作废 和结束不允许修改
        if (Objects.equals(activityState, EnumActivityState.CANCEL) || Objects.equals(activityState, EnumActivityState.END)) {
            throw SystemActivityBusinessException.SYSTEM_ACTIVITY_TIME_ERROR_NOT_ALLOW_UPDATE;
        }
        //活动中、已暂停 只让修改活动标题活动图片和活动介绍和推荐产品
        if (Objects.equals(activityState, EnumActivityState.IN_ACTIVITY) || Objects.equals(activityState, EnumActivityState.SUSPEND)) {
            systemActivityProgramme.setTitle(systemActivityProgrammeForm.getTitle());
            systemActivityProgramme.setImageUrl(systemActivityProgrammeForm.getImageUrl());
            systemActivityProgramme.setContent(systemActivityProgrammeForm.getContent());
            systemActivityProgramme.setUpdateTime(LocalDateTime.now());
            systemActivityProgramme.setConfigType(systemActivityProgrammeForm.getConfigType());
            systemActivityProgramme.setProducts(systemActivityProgrammeForm.getProductsJson());
            systemActivityProgrammeMapper.updateByPrimaryKeySelective(systemActivityProgramme);
            return;
        }
        //未发布、未开始	编辑所有内容
        //编辑时若有活动已有关联的活动，则不能修改活动类型
        ActivityType activityType = systemActivityProgramme.getType();
        List<SystemActivityRefVo> activityRefList = systemActivityRefMapper.getBySystemActivityId(id);
        if (CollectionUtils.isNotEmpty(activityRefList) && !activityType.equals(systemActivityProgrammeForm.getType())) {
            throw SystemActivityBusinessException.SYSTEM_ACTIVITY_NOT_ALLOW_UPDATE_TYPE;
        }
        //校验活动方案
        checkSystemActivityProgrammeForm(systemActivityProgrammeForm);

        String userId = HttpRequestUtil.getUserId();
        //更新活动方案
        updateSystemActivityProgramme(systemActivityProgrammeForm, userId, systemActivityProgramme);
        //删除关联关系
        systemActivityRefMapper.softDeleteBySystemActivityId(id);

        //脚本规则修改
        List<SystemActivityProduct> activityProductList = systemActivityProductMapper.getBySystemActivityId(id);
        List<SystemActivityProduct> activityProductWhaleList = systemActivityProductWhaleMapper.getBySystemActivityId(id);
        if (CollectionUtils.isNotEmpty(activityProductList)) {
            List<Long> activityProductIdList = activityProductList.stream()
                    .map(SystemActivityProduct::getId)
                    .collect(Collectors.toList());
            systemActivityProductRuleMapper.softDeleteBySystemActivityProductIdList(activityProductIdList);
            systemActivityProductMapper.softDeleteBySystemActivityId(id);
        }

        if (CollectionUtils.isNotEmpty(activityProductWhaleList)) {
            List<Long> activityProductIdList = activityProductWhaleList.stream()
                    .map(SystemActivityProduct::getId)
                    .collect(Collectors.toList());
            systemActivityProductRuleWhaleMapper.softDeleteBySystemActivityProductIdList(activityProductIdList);
            systemActivityProductWhaleMapper.softDeleteBySystemActivityId(id);
        }
        //新增关联关系
        saveSystemActivityProductAndRel(systemActivityProgrammeForm, userId, id);
    }

    @Override
    public void updateActivityFlag(Long id, Integer activityFlag) {
        SystemActivityProgramme systemActivityProgramme = systemActivityProgrammeMapper.selectByPrimaryKey(id);
        //校验活动方案是否存在
        if (Objects.isNull(systemActivityProgramme)) {
            throw SystemActivityBusinessException.SYSTEM_ACTIVITY_NOT_EXIST;
        }
        systemActivityProgrammeMapper.updateActivityFlag(id, activityFlag);

    }

    @Override
    public void compensateActivity(Long id, Integer activityFlag) {
        SystemActivityProgramme systemActivityProgramme = systemActivityProgrammeMapper.selectByPrimaryKey(id);
        //校验活动方案是否存在
        if (Objects.isNull(systemActivityProgramme)) {
            throw SystemActivityBusinessException.SYSTEM_ACTIVITY_NOT_EXIST;
        }
        if (activityFlag == 1 && systemActivityProgramme.getOpenCount() == 1) {
            LocalDateTime startTime = systemActivityProgramme.getStartTime();
            LocalDateTime nowTime = LocalDateTime.now();
            //且活动已经开始则对之前的数据进行补偿
            if (startTime.isBefore(nowTime) && Objects.equals(systemActivityProgramme.getType(), ActivityType.TICKET)) {
                compensateHistoryOrder(startTime, nowTime, systemActivityProgramme);
            }
        }
    }

    /**
     * 补偿历史订单
     *
     * @param startTime
     * @param endTime
     * @param systemActivityProgramme
     */
    public void compensateHistoryOrder(LocalDateTime startTime, LocalDateTime endTime, SystemActivityProgramme systemActivityProgramme) {
        Long activityProgrammeId = systemActivityProgramme.getId();
        List<Long> activityProductIdList = systemActivityProductMapper.getBySystemActivityId(activityProgrammeId)
                .stream()
                .map(SystemActivityProduct::getProductId)
                .collect(Collectors.toList());
        List<OperationActivityOrderMessage> orderMessageList;
        if (activityProductIdList.contains(Long.valueOf(BusinessConstants.ALL_PRODUCT_ID))) {
            orderMessageList = operationActivityOrderMessageMapper.getByPayTimeAndActivityId(startTime, endTime, null);
        } else {
            orderMessageList = operationActivityOrderMessageMapper.getByPayTimeAndActivityId(startTime, endTime, activityProductIdList);
        }
        orderMessageListFor:
        for (int i = 0; i < orderMessageList.size(); i++) {
            OperationActivityOrderMessage operationActivityOrderMessage = orderMessageList.get(i);
            try {
                List<SystemActivityProductDetailVo> systemActivityProduct = getSystemActivityProduct(activityProgrammeId);
                Map<Long, List<SystemActivityProductRuleVo>> systemActivityProductRuleVo = getSystemActivityProductRuleVo(activityProgrammeId);
                List<SmActivityReward> smActivityRewards = smActivityRewardService.calculateRewards(operationActivityOrderMessage, systemActivityProduct, systemActivityProductRuleVo);
                smActivityRewardService.sendReward(smActivityRewards);
            } catch (Exception e) {
                log.error("补偿失败operationActivityOrderMessageId:{}", operationActivityOrderMessage.getId(), e);
            }
            if (i % 100 == 0) {
                try {
                    Thread.sleep(2000);
                } catch (InterruptedException e) {
                    log.error("线程休眠失败", e);
                }
            }
        }
    }

    /**
     * 保存订单和活动的关系记录
     *
     * @param activityProgrammeId
     * @param operationActivityOrderMessage
     * @param couponsQuantity
     */
    private void saveSmOrderActivity(Long activityProgrammeId, OperationActivityOrderMessage operationActivityOrderMessage, Integer couponsQuantity) {
        String recommendId = operationActivityOrderMessage.getRecommendId();
        String fhOrderId = operationActivityOrderMessage.getFhOrderId();
        SmOrderActivity smOrderActivity = new SmOrderActivity();
        smOrderActivity.setFhOrderId(fhOrderId);
        smOrderActivity.setSaId(activityProgrammeId);
        smOrderActivity.setUserId(recommendId);
        smOrderActivity.setRemark(BusinessConstants.REMARK + fhOrderId);
        smOrderActivity.setTrxNo(smOrderActivityService.getTrxNo(fhOrderId, activityProgrammeId));
        smOrderActivity.setQuantity(couponsQuantity);
        smOrderActivity.setState(BusinessConstants.ORDER_ACTIVITY_STATUS_WAIT);
        smOrderActivity.setCreateBy(recommendId);
        smOrderActivity.setUpdateBy(recommendId);
        smOrderActivityMapper.insert(smOrderActivity);
    }

    @Cacheable(cacheNames = CacheKeyConstants.ACTIVITY_PRODUCT_RULE, key = "'activityProgrammeId'+#activityProgrammeId")
    public Map<Long, List<SystemActivityProductRuleVo>> getSystemActivityProductRuleVo(Long activityProgrammeId) {
        List<SystemActivityProduct> systemActivityProductList = systemActivityProductMapper.getBySystemActivityId(activityProgrammeId);
        //获取活动产品id集合
        List<Long> activityProductIdList = systemActivityProductList.stream()
                .map(SystemActivityProduct::getId)
                .collect(Collectors.toList());
        //获取活动产品规则映射
        Map<Long, List<SystemActivityProductRuleVo>> activityProductRuleMap = systemActivityProductRuleMapper.getBySystemActivityProductIdList(activityProductIdList)
                .stream()
                .collect(Collectors.groupingBy(SystemActivityProductRuleVo::getSystemActivityProductId));
        return activityProductRuleMap;
    }

    @Cacheable(cacheNames = CacheKeyConstants.ACTIVITY_PRODUCT, key = "'activityProgrammeId'+#activityProgrammeId")
    public List<SystemActivityProductDetailVo> getSystemActivityProduct(Long activityProgrammeId) {
        List<SystemActivityProductDetailVo> systemActivityProductList = systemActivityProductMapper.getById(activityProgrammeId.intValue());
        return systemActivityProductList;

    }

    @Override
    public PageInfo<WxActivitySimpleVo> getWxActivityListWithoutOpenId(WxActivitySearchForm wxActivitySearchForm) {
        //WxSessionVO session = wxCheckAuthorityHelper.checkAuthority(wxActivitySearchForm.getOpenId(), wxActivitySearchForm.getAuthorization());
        UserDetailVO vo = bmsHelper.getUserDetailByToken(wxActivitySearchForm.getAuthorization());
        if (bmsHelper.checkOrgIsAreaOffice(String.valueOf(vo.getHrOrgId()))) {
            //如果是区域办公室，则获取分支机构树对应的区域code
            wxActivitySearchForm.setRegionCode(bmsHelper.getBranchOrgCode(vo.getOrgCode()));
        } else {
            AuthUser authUser = authUserMapper.getByUserId(vo.getJobNumber());
            wxActivitySearchForm.setRegionCode(authUser.getRegionCode());
        }
        return PageHelper.startPage(wxActivitySearchForm.getPageNo(), wxActivitySearchForm.getPageSize())
                .doSelectPageInfo(() -> systemActivityProgrammeMapper.getWxActivityList(wxActivitySearchForm));
    }

    @Override
    public PageInfo<WxActivitySimpleVo> getWxActivityList(WxActivitySearchForm wxActivitySearchForm) {
        WxSessionVO session = wxCheckAuthorityHelper.checkAuthority(wxActivitySearchForm.getOpenId(), wxActivitySearchForm.getAuthorization());
        if (bmsHelper.checkOrgIsAreaOffice(session.getHrOrgId())) {
            //如果是区域办公室，则获取分支机构树对应的区域code
            wxActivitySearchForm.setRegionCode(bmsHelper.getBranchOrgCode(session.getOrgCode()));
        } else {
            AuthUser authUser = authUserMapper.getByUserId(session.getUserId());
            wxActivitySearchForm.setRegionCode(authUser.getRegionCode());
        }
        return PageHelper.startPage(wxActivitySearchForm.getPageNo(), wxActivitySearchForm.getPageSize())
                .doSelectPageInfo(() -> systemActivityProgrammeMapper.getWxActivityList(wxActivitySearchForm));
    }

    @Override
    public SystemActivityProgrammeVo getWxActivityDetail(Long id, String openId, String authorization) {
        SystemActivityProgrammeVo systemActivityProgrammeVo = getSystemActivityProgrammeVo(id);
        if (systemActivityProgrammeVo == null) {
            return null;
        }
        //推荐产品
        List<Long> productIdList = systemActivityProgrammeVo.getProductIdList();
        WxSessionVO wxSessionVO = wxCheckAuthorityHelper.checkAuthority(openId, authorization);
        //查看当前用户销售区域的产品集合
        List<Long> salesOrgProductIdList = productMapper.listProductSalesOrgByOrgPath(wxSessionVO.getRegionName());
        //过滤掉未在此区域销售的产品
        List<Long> filterProductIdList = productIdList.stream()
                .filter(salesOrgProductIdList::contains)
                .collect(Collectors.toList());

        if (!productIdList.isEmpty()) {
            List<RecommendedProductVo> recommendedProductVoList = productMapper.listWxProductsWithGroup(filterProductIdList);
            systemActivityProgrammeVo.setRecommendedProductList(recommendedProductVoList);
        }
        return systemActivityProgrammeVo;
    }

    @Override
    public SystemActivityProgrammeVo getWxHelperActivityDetail(Long id,String regionName, String authorization) {

        DataAuthPageForm dataAuth = new DataAuthPageForm();
        dataAuthService.dataAuth(dataAuth);
        log.info("用户信息：{}", JSONObject.toJSONString(dataAuth));

        SystemActivityProgrammeVo systemActivityProgrammeVo = getSystemActivityProgrammeVo(id);
        if (systemActivityProgrammeVo == null) {
            return null;
        }
        //推荐产品
        List<Long> productIdList = systemActivityProgrammeVo.getProductIdList();
        //查看当前用户销售区域的产品集合
        List<Long> salesOrgProductIdList = productMapper.listProductSalesOrgByOrgPath(dataAuth.getRegionName());
        //过滤掉未在此区域销售的产品
        List<Long> filterProductIdList = productIdList.stream()
                .filter(salesOrgProductIdList::contains)
                .collect(Collectors.toList());

        if (!productIdList.isEmpty()) {
            List<RecommendedProductVo> recommendedProductVoList = productMapper.listWxProductsWithGroup(filterProductIdList);
            systemActivityProgrammeVo.setRecommendedProductList(recommendedProductVoList);
        }
        return systemActivityProgrammeVo;
    }

    /**
     * 设置活动产品以及规则
     * 初始化单个活动信息
     *
     * @param activityProgrammeVo
     */
    private void setActivityProduct(SystemActivityProgrammeVo activityProgrammeVo) {

        List<Long> systemActivityIdList = Arrays.asList(activityProgrammeVo.getId());

        //设置活动产品以及规则
        List<SystemActivityProductVo> activityProductList = systemActivityProductMapper.getBySystemActivityIdList(systemActivityIdList);
        if (CollectionUtils.isNotEmpty(activityProductList)) {

            setProductPlan(activityProductList);

            //获取活动产品id集合
            List<Long> activityProductIdList = activityProductList.stream()
                    .map(SystemActivityProductVo::getId)
                    .collect(Collectors.toList());
            //活动产品id和活动产品规则的映射关系
            Map<Long, List<SystemActivityProductRuleVo>> systemActivityProductRuleMap = systemActivityProductRuleMapper.getBySystemActivityProductIdList(activityProductIdList)
                    .stream()
                    .collect(Collectors.groupingBy(SystemActivityProductRuleVo::getSystemActivityProductId));
            //设置SystemActivityProductVo对象的规则
            for (SystemActivityProductVo systemActivityProductVo : activityProductList) {
                Long activityProductId = systemActivityProductVo.getId();
                systemActivityProductVo.setSystemActivityProductRuleList(systemActivityProductRuleMap.get(activityProductId));
            }

        }
        //获取活动id和活动产品映射关系
        Map<Long, List<SystemActivityProductVo>> systemActivityProductMap = activityProductList
                .stream()
                .collect(Collectors.groupingBy(SystemActivityProductVo::getSystemActivityProgrammeId));
        //设置活动的活动产品对象
        Long activityProgrammeVoId = activityProgrammeVo.getId();
        activityProgrammeVo.setSystemActivityProductList(systemActivityProductMap.get(activityProgrammeVoId));
        //初始化区域信息
        activityProgrammeVo.initRegionListByRegions();
    }

    /**
     * 设置活动产品以及规则
     *
     * @param activityProgrammeList
     */
    private void setActivityProductAndRef(List<SystemActivityProgrammeVo> activityProgrammeList) {
        if (CollectionUtils.isEmpty(activityProgrammeList)) {
            return;
        }

        List<Long> systemActivityIdList = activityProgrammeList.stream()
                .map(SystemActivityProgrammeVo::getId)
                .collect(Collectors.toList());

        //设置活动产品以及规则
        List<SystemActivityProductVo> activityProductList = systemActivityProductMapper.getBySystemActivityIdList(systemActivityIdList);
        if (CollectionUtils.isNotEmpty(activityProductList)) {

            setProductPlan(activityProductList);

            //获取活动产品id集合
            List<Long> activityProductIdList = activityProductList.stream()
                    .map(SystemActivityProductVo::getId)
                    .collect(Collectors.toList());
            //活动产品id和活动产品规则的映射关系
            Map<Long, List<SystemActivityProductRuleVo>> systemActivityProductRuleMap = systemActivityProductRuleMapper.getBySystemActivityProductIdList(activityProductIdList)
                    .stream()
                    .collect(Collectors.groupingBy(SystemActivityProductRuleVo::getSystemActivityProductId));
            //设置SystemActivityProductVo对象的规则
            for (SystemActivityProductVo systemActivityProductVo : activityProductList) {
                Long activityProductId = systemActivityProductVo.getId();
                systemActivityProductVo.setSystemActivityProductRuleList(systemActivityProductRuleMap.get(activityProductId));
            }

        }

        //设置小鲸活动产品及规则
        List<SystemActivityProductVo> activityProductWhaleList = systemActivityProductWhaleMapper.getBySystemActivityIdList(systemActivityIdList);
        initWhaleRiskInfo(activityProductWhaleList);

        //获取活动id和活动产品映射关系
        Map<Long, List<SystemActivityProductVo>> systemActivityProductMap = activityProductList
                .stream()
                .collect(Collectors.groupingBy(SystemActivityProductVo::getSystemActivityProgrammeId));
        Map<Long, List<SystemActivityProductVo>> systemActivityProductWhaleMap = activityProductWhaleList
                .stream()
                .collect(Collectors.groupingBy(SystemActivityProductVo::getSystemActivityProgrammeId));

        //设置活动的活动产品对象
        for (SystemActivityProgrammeVo systemActivityProgrammeVo : activityProgrammeList) {
            Long activityProgrammeVoId = systemActivityProgrammeVo.getId();
            systemActivityProgrammeVo.setSystemActivityProductList(systemActivityProductMap.get(activityProgrammeVoId));
            if (systemActivityProductWhaleMap.containsKey(activityProgrammeVoId)) {
                systemActivityProgrammeVo.setSystemActivityProductWhaleList(systemActivityProductWhaleMap.get(activityProgrammeVoId));
                systemActivityProgrammeVo.setSystemActivityProductList(systemActivityProductWhaleMap.get(activityProgrammeVoId));
            }
            //初始化区域信息
            systemActivityProgrammeVo.initRegionListByRegions();
        }

        //手动规则配置
        List<SystemActivityConstRule> rules = constRuleMapper.selectBySaIds(systemActivityIdList);
        Map<Long, List<SystemActivityConstRule>> saMap = LambdaUtils.groupBy(rules, SystemActivityConstRule::getSaId);

        List<SystemActivityConstRuleWhale> whaleRules = constRuleWhaleMapper.selectBySaIds(systemActivityIdList);
        Map<Long, List<SystemActivityConstRuleWhale>> whaleSaMap = LambdaUtils.groupBy(whaleRules, SystemActivityConstRuleWhale::getSaId);
        for (SystemActivityProgrammeVo programmeVo : activityProgrammeList) {
            List<SystemActivityConstRule> saRules = saMap.get(programmeVo.getId());
            if (CollectionUtils.isNotEmpty(saRules)) {
                List<String> ruleTypes = saRules.stream().map(SystemActivityConstRule::getRuleType)
                        .distinct()
                        .map(EnumActivityConstRuleType::name)
                        .collect(Collectors.toList());
                programmeVo.setConstRule(new SystemActivityConstRuleWrapperForm(ruleTypes,
                        SystemActivityCvt.INS.po2Form(saRules)));
            }

            //小鲸规则
            List<SystemActivityConstRuleWhale> whaleSaRules = whaleSaMap.get(programmeVo.getId());
            if (CollectionUtils.isNotEmpty(whaleSaRules)) {
                List<String> ruleTypes = whaleSaRules.stream().map(SystemActivityConstRuleWhale::getRuleType)
                        .distinct()
                        .map(EnumActivityConstRuleType::name)
                        .collect(Collectors.toList());
                programmeVo.setConstRuleWhale(new SystemActivityConstRuleWrapperForm(ruleTypes,
                        SystemActivityCvt.INS.po2WhaleForm(whaleSaRules)));
                if (Objects.isNull(programmeVo.getConstRule())) {
                    programmeVo.setConstRule(programmeVo.getConstRuleWhale());
                }
            }
        }
    }

    /**
     * 初始化小鲸活动产品及规则
     * @param activityProductWhaleList 小鲸活动产品
     */
    private void initWhaleRiskInfo(List<SystemActivityProductVo> activityProductWhaleList) {
        if (CollectionUtils.isNotEmpty(activityProductWhaleList)) {
            for (SystemActivityProductVo activityProductVo : activityProductWhaleList) {
                List<String> riskNameList = activityProductVo.getRiskNameList();
                List<Long> riskIdList = activityProductVo.getPlanIdList();
                List<PlanSimpleVo> smPlanList = new ArrayList<>();
                for (int i=0;i<riskIdList.size();i++) {
                    PlanSimpleVo vo = new PlanSimpleVo();
                    vo.setId(riskIdList.get(i));
                    if (CollectionUtils.isNotEmpty(riskNameList)) {
                        vo.setPlanName(riskNameList.get(i));
                    }
                    smPlanList.add(vo);
                }
                activityProductVo.setPlanList(smPlanList);
            }

            //获取活动产品id集合
            List<Long> activityProductIdList = activityProductWhaleList.stream()
                    .map(SystemActivityProductVo::getId)
                    .collect(Collectors.toList());
            //活动产品id和活动产品规则的映射关系
            Map<Long, List<SystemActivityProductRuleVo>> systemActivityProductRuleMap = systemActivityProductRuleWhaleMapper.getBySystemActivityProductIdList(activityProductIdList)
                    .stream()
                    .collect(Collectors.groupingBy(SystemActivityProductRuleVo::getSystemActivityProductId));
            //设置SystemActivityProductVo对象的规则
            for (SystemActivityProductVo systemActivityProductVo : activityProductWhaleList) {
                Long activityProductId = systemActivityProductVo.getId();
                systemActivityProductVo.setSystemActivityProductRuleList(systemActivityProductRuleMap.get(activityProductId));
            }

        }
    }

    /**
     * 设置产品的计划
     *
     * @param activityProductList
     */
    private void setProductPlan(List<SystemActivityProductVo> activityProductList) {
        //获取所有计划id的集合
        List<Long> planIdList = Lists.newArrayList();
        for (SystemActivityProductVo activityProductVo : activityProductList) {
            planIdList.addAll(activityProductVo.getPlanIdList());
        }
        //获取计划的映射关系
        Map<Long, PlanSimpleVo> planMap = planMapper.getByIdList(planIdList)
                .stream()
                .collect(Collectors.toMap(PlanSimpleVo::getId, e -> e));

        for (SystemActivityProductVo activityProductVo : activityProductList) {
            List<PlanSimpleVo> smPlanList = activityProductVo.getPlanIdList()
                    .stream()
                    .map(planId -> planMap.get(planId))
                    .collect(Collectors.toList());
            activityProductVo.setPlanList(smPlanList);
        }
    }

    /**
     * 校验表单
     * <p>
     * 1.结束时间需在开始时间后
     * 2.普通活动：【活动产品及奖励规则】隐藏不展示；
     * 加佣奖励：【活动产品及奖励规则】必填，V2.0
     * 悦活动抽奖：【活动产品及奖励规则】必填，会务系统活动编码必填；
     * 3.关联活动（加佣活动）和当前活动类型（悦活动抽奖）不一致，前端提示且关联失败；关联失败，关联活动和当前活动类型不一致
     * 4.系统在售产品
     * 5.活动类型和奖励类型必须匹配
     * </p>
     *
     * @param systemActivityProgrammeForm
     */
    private void checkSystemActivityProgrammeForm(SystemActivityProgrammeForm systemActivityProgrammeForm) {
        //结束时间需在开始时间后
        LocalDateTime endTime = systemActivityProgrammeForm.getEndTime();
        LocalDateTime startTime = systemActivityProgrammeForm.getStartTime();
        if (startTime.isAfter(endTime)) {
            throw SystemActivityBusinessException.SYSTEM_ACTIVITY_TIME_ERROR;
        }

        //校验不同活动类型的参数
        ActivityType type = systemActivityProgrammeForm.getType();
        List<SystemActivityProductForm> systemActivityProductList = systemActivityProgrammeForm.getSystemActivityProductList();
        ConflictRule conflictRule = systemActivityProgrammeForm.getConflictRule();
        switch (type) {
            case NORMAL:
                //【活动产品及奖励规则】隐藏不展示；
                systemActivityProgrammeForm.setSystemActivityRefList(null);
                systemActivityProgrammeForm.setSystemActivityProductList(null);
                systemActivityProgrammeForm.setElsGrantPassword(null);
                systemActivityProgrammeForm.setElsActivityNumber(null);
                systemActivityProgrammeForm.setConflictRule(null);
                break;
            case RED_ENVELOPE:
                // 加佣奖励：【活动产品及奖励规则】必填，V2.0
                checkActivityProductAndConflictRule(systemActivityProductList, conflictRule);
                //活动产品必须在线(2021年12月14日起不再校验) 活动类型和奖励类型必须匹配
                checkActivityType(systemActivityProductList, type);
            case ADD_COMMISSION:
                if (systemActivityProgrammeForm.isConst()) {
                    //校验配置不能为空
                    if (Objects.isNull(systemActivityProgrammeForm.getConstRule()) || CollectionUtils.isEmpty(systemActivityProgrammeForm.getConstRule().getConstRules())) {
                        throw SystemActivityBusinessException.buildValidError("手动配置规则不能为空");
                    }
                } else {
                    //脚本配置 走以前的逻辑
                    // 加佣奖励：【活动产品及奖励规则】必填，V2.0
                    checkActivityProductAndConflictRule(systemActivityProductList, conflictRule);
                    //活动产品必须在线(2021年12月14日起不再校验) 活动类型和奖励类型必须匹配
                    checkActivityType(systemActivityProductList, type);
                    //校验规则code码是否存在
                    checkRuleCode(systemActivityProductList);
                }

                break;
            case TICKET:
                //悦活动抽奖：【活动产品及奖励规则】必填
                checkActivityProductAndConflictRule(systemActivityProductList, conflictRule);
                //活动产品必须在线(2021年12月14日起不再校验) 活动类型和奖励类型必须匹配
                checkActivityType(systemActivityProductList, type);
                //校验规则code码是否存在
                checkRuleCode(systemActivityProductList);

                //活动抽奖：会务系统活动编码必填；

                Integer elsActivityNumber = systemActivityProgrammeForm.getElsActivityNumber();
                String elsGrantPassword = systemActivityProgrammeForm.getElsGrantPassword();
                if (Objects.isNull(elsActivityNumber) || StringUtils.isBlank(elsGrantPassword)) {
                    throw SystemActivityBusinessException.SYSTEM_ACTIVITY_ELS_NUMBER_OR_PASSWORD_PARAMS_NULL;
                }
                //校验活动编码有效
                EventInfoDetailVO eventInfoDetailVO = eventInfoHelper.queryById(systemActivityProgrammeForm.getElsActivityNumber());
                if (Objects.isNull(eventInfoDetailVO) || eventInfoDetailVO.getIsDrawComplete()) {
                    throw SystemActivityBusinessException.SYSTEM_ACTIVITY_ELS_NUMBER_INVALID;
                }
                break;

            default:
                break;
        }

        //关联活动（加佣活动）和当前活动类型（悦活动抽奖）不一致，前端提示且关联失败；关联失败，关联活动和当前活动类型不一致
        List<SystemActivityRefForm> systemActivityRefList = systemActivityProgrammeForm.getSystemActivityRefList();
        if (CollectionUtils.isNotEmpty(systemActivityRefList)) {
            List<Long> refSaIdList = systemActivityRefList.stream()
                    .map(SystemActivityRefForm::getSaId)
                    .distinct()
                    .collect(Collectors.toList());
            List<SystemActivityProgramme> systemActivityProgrammeList = systemActivityProgrammeMapper.getByIdList(refSaIdList);
            //判断活动是否存在
            if (refSaIdList.size() != systemActivityProgrammeList.size()) {
                throw SystemActivityBusinessException.SYSTEM_ACTIVITY_REF_NOT_EXIST;
            }
            for (SystemActivityProgramme systemActivityProgramme : systemActivityProgrammeList) {
                if (!systemActivityProgrammeForm.getType().equals(systemActivityProgramme.getType())) {
                    throw SystemActivityBusinessException.SYSTEM_ACTIVITY_REL_ACTIVITY_TYPE_ERROR;
                }
            }
        }

        if (systemActivityProgrammeForm.isConst()) {
            List<SystemActivityConstRuleForm> constRules = systemActivityProgrammeForm.getConstRule().getConstRules();
            constRules.forEach(rule -> {
                if (StringUtils.isBlank(rule.getParams().getCompareSymbol())) {
                    throw SystemActivityBusinessException.buildValidError("运算符不能为空");
                }
                EnumMathOperator.valueOfExpression(rule.getParams().getCompareSymbol());
            });
        }
    }

    /**
     * 校验活动规则码是否存在
     *
     * @param systemActivityProductList
     */
    private void checkRuleCode(List<SystemActivityProductForm> systemActivityProductList) {
        List<String> codeList = Lists.newArrayList();
        for (SystemActivityProductForm systemActivityProductForm : systemActivityProductList) {
            List<String> ruleCodeList = systemActivityProductForm.getSystemActivityProductRuleList()
                    .stream()
                    .map(SystemActivityProductRuleForm::getRuleCode)
                    .distinct()
                    .collect(Collectors.toList());
            codeList.addAll(ruleCodeList);
        }
        List<String> dbRuleCodeList = ruleMapper.selectByType(GroovyCodeConstants.OPERATION_ACTIVITY_GROOVY_TYPE)
                .stream()
                .map(SystemGroovyRule::getCode)
                .collect(Collectors.toList());
        if (!dbRuleCodeList.containsAll(codeList)) {
            throw SystemActivityBusinessException.SYSTEM_ACTIVITY_RULE_CODE_NOT_EXIST;
        }
    }

    /**
     * 活动产品必须在线
     * 活动类型和奖励类型必须匹配
     *
     * @param systemActivityProductList
     * @param type
     */
    private void checkActivityProductOnlineAndType(List<SystemActivityProductForm> systemActivityProductList, ActivityType type) {
        checkActivityType(systemActivityProductList, type);

        checkActivityProductOnline(systemActivityProductList);
    }

    /**
     * 活动类型和奖励类型必须匹配
     *
     * @param systemActivityProductList
     * @param type
     */
    private void checkActivityType(List<SystemActivityProductForm> systemActivityProductList, ActivityType type) {
        for (SystemActivityProductForm systemActivityProductForm : systemActivityProductList) {
            if (!systemActivityProductForm.getRewardType().containActivityType(type)) {
                throw SystemActivityBusinessException.SYSTEM_ACTIVITY_REWORD_TYPE_ERROR;
            }
        }
    }

    /**
     * 校验产品必须在线
     *
     * @param systemActivityProductList
     */
    private void checkActivityProductOnline(List<SystemActivityProductForm> systemActivityProductList) {
        //活动产品必须在线
        List<Long> productIdList = systemActivityProductList.stream()
                .map(SystemActivityProductForm::getProductId)
                .filter(id -> id != BusinessConstants.ALL_PRODUCT_ID)
                .distinct()
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(productIdList)) {
            return;
        }
        List<SmProduct> productList = productMapper.getByIdList(productIdList);
        boolean existOfflineProduct = productList.stream().anyMatch(smProduct -> smProduct.getState() != BusinessConstants.PRODUCT_STATUS_ONLINE);
        if (existOfflineProduct) {
            throw SystemActivityBusinessException.SYSTEM_ACTIVITY_PRODUCT_MUST_ONLINE;
        }
    }

    /**
     * 校验活动产品和优惠类型是否为空
     *
     * @param systemActivityProductList
     * @param conflictRule
     */
    private void checkActivityProductAndConflictRule(List<SystemActivityProductForm> systemActivityProductList, ConflictRule conflictRule) {

        if (Objects.isNull(systemActivityProductList) || Objects.isNull(conflictRule)) {
            throw SystemActivityBusinessException.SYSTEM_ACTIVITY_PRODUCT_OR_CONFLICT_RULE_PARAMS_NULL;
        }
    }

    /**
     * 保存活动方案
     *
     * @param systemActivityProgrammeForm
     */
    public Long saveSystemActivityProgramme(SystemActivityProgrammeForm systemActivityProgrammeForm) {
        String currentUserId = HttpRequestUtil.getUserId();
        //获取活动方案实体
        SystemActivityProgramme systemActivityProgramme = getSystemActivityProgramme(systemActivityProgrammeForm, currentUserId);
        //保存活动方案
        systemActivityProgrammeMapper.insertUseGeneratedKeys(systemActivityProgramme);
        Long systemActivityProgrammeId = systemActivityProgramme.getId();
        //保存活动产品和活动关系
        saveSystemActivityProductAndRel(systemActivityProgrammeForm, currentUserId, systemActivityProgrammeId);
        return systemActivityProgrammeId;
    }
    /**
     * 复制活动方案
     *
     * @param
     */
    @Transactional(rollbackFor = Exception.class)
    public void copy(Long id,String activityPlatform) {
        String currentUserId = HttpRequestUtil.getUserId();
        SystemActivityProgramme systemActivityProgramme=new SystemActivityProgramme();
        systemActivityProgramme.setId(id);
        systemActivityProgramme.setCreateBy(currentUserId);
        systemActivityProgrammeMapper.insertCopyProgramme(systemActivityProgramme);
        Long Id = systemActivityProgramme.getId();

        systemActivityRefMapper.insertCopyRef(id,currentUserId,Id);

        if ("nb".equals(activityPlatform)) {
            constRuleMapper.insertCopyRule(id,Id);
            List<SystemActivityProduct> productList = systemActivityProductMapper.selectCopyProduct(id);
            if(!productList.isEmpty()){
                for (SystemActivityProduct systemActivityProduct:productList){
                    Long OldSystemActivityProductId=systemActivityProduct.getId();
                    systemActivityProduct.setId(null);
                    systemActivityProduct.setSaId(Id);
                    systemActivityProductMapper.insert(systemActivityProduct);
                    Long systemActivityProductId=systemActivityProductMapper.getLastId();
                    systemActivityProductRuleMapper.insertCopyProductRule(OldSystemActivityProductId,currentUserId,systemActivityProductId);
                }
            }
        } else {
            constRuleWhaleMapper.insertCopyRule(id,Id);

            List<SystemActivityProductWhale> productList = systemActivityProductWhaleMapper.selectCopyProduct(id);
            if(!productList.isEmpty()){
                for (SystemActivityProductWhale systemActivityProduct:productList){
                    Long OldSystemActivityProductId=systemActivityProduct.getId();
                    systemActivityProduct.setId(null);
                    systemActivityProduct.setSaId(Id);
                    systemActivityProductWhaleMapper.insert(systemActivityProduct);
                    Long systemActivityProductId=systemActivityProductWhaleMapper.getLastId();
                    systemActivityProductRuleWhaleMapper.insertCopyProductRule(OldSystemActivityProductId,currentUserId,systemActivityProductId);
                }
            }
        }
    }

    /**
     * 保存活动产品和活动关系数据
     *
     * @param systemActivityProgrammeForm
     * @param currentUserId
     * @param systemActivityProgrammeId
     */
    private void saveSystemActivityProductAndRel(SystemActivityProgrammeForm systemActivityProgrammeForm, String currentUserId, Long systemActivityProgrammeId) {
        List<SystemActivityRefForm> systemActivityRefList = systemActivityProgrammeForm.getSystemActivityRefList();
        if (CollectionUtils.isNotEmpty(systemActivityRefList)) {
            //如果关联活动不为空则保存活动关系
            List<SystemActivityRef> activityRefList = systemActivityRefList.stream()
                    .map(systemActivityRefForm -> {
                        SystemActivityRef systemActivityRef = new SystemActivityRef();
                        systemActivityRef.setSaId1(systemActivityProgrammeId);
                        systemActivityRef.setSaId2(systemActivityRefForm.getSaId());
                        systemActivityRef.setConflictRule(systemActivityRefForm.getConflictRule());
                        systemActivityRef.setCreateBy(currentUserId);
                        return systemActivityRef;
                    })
                    .collect(Collectors.toList());
            systemActivityRefMapper.insertList(activityRefList);
        }
        //活动平台是农保则走之前的保存逻辑
        if ("nb".equals(systemActivityProgrammeForm.getActivityPlatform())) {
            //保存活动产品以及规则
            if (systemActivityProgrammeForm.isConst()) {
                saveSystemActivityConstRule(systemActivityProgrammeForm, systemActivityProgrammeId);
            } else {
                saveSystemActivityProduct(systemActivityProgrammeForm, currentUserId, systemActivityProgrammeId);
            }
        }
        //活动平台为小鲸则走新的商品、险种保存逻辑
        else {
            //保存活动产品以及规则
            if (systemActivityProgrammeForm.isConst()) {
                saveSystemActivityConstRuleWhale(systemActivityProgrammeForm, systemActivityProgrammeId);
            } else {
                saveSystemActivityProductWhale(systemActivityProgrammeForm, currentUserId, systemActivityProgrammeId);
            }
        }

    }

    /**
     * 保存手动配置的规则
     *
     * @param systemActivityProgrammeForm
     * @param systemActivityProgrammeId
     */
    private void saveSystemActivityConstRule(SystemActivityProgrammeForm systemActivityProgrammeForm, Long systemActivityProgrammeId) {
        List<SystemActivityConstRuleForm> constRules = systemActivityProgrammeForm.getConstRule().getConstRules();

        SystemActivityConstRule delete = new SystemActivityConstRule();
        delete.setSaId(systemActivityProgrammeId);
        constRuleMapper.delete(delete);

        List<SystemActivityConstRule> rules = SystemActivityCvt.INS.from2Po(constRules);
        rules.forEach(r -> r.setSaId(systemActivityProgrammeId));
        constRuleMapper.insertList(rules);
    }

    /**
     * 保存手动配置的规则
     *
     * @param systemActivityProgrammeForm
     * @param systemActivityProgrammeId
     */
    private void saveSystemActivityConstRuleWhale(SystemActivityProgrammeForm systemActivityProgrammeForm, Long systemActivityProgrammeId) {
        List<SystemActivityConstRuleForm> constRules = systemActivityProgrammeForm.getConstRule().getConstRules();

        SystemActivityConstRuleWhale delete = new SystemActivityConstRuleWhale();
        delete.setSaId(systemActivityProgrammeId);
        constRuleWhaleMapper.delete(delete);

        List<SystemActivityConstRuleWhale> rules = SystemActivityCvt.INS.from2WhalePo(constRules);
        rules.forEach(r -> r.setSaId(systemActivityProgrammeId));
        log.info("rules：{}",JSONObject.toJSONString(rules));
        constRuleWhaleMapper.insertList(rules);
    }

    /**
     * 保存活动产品以及规则
     *
     * @param systemActivityProgrammeForm
     * @param currentUserId
     * @param systemActivityProgrammeId
     */
    private void saveSystemActivityProduct(SystemActivityProgrammeForm systemActivityProgrammeForm, String currentUserId, Long systemActivityProgrammeId) {
        List<SystemActivityProductForm> systemActivityProductList = systemActivityProgrammeForm.getSystemActivityProductList();
        if (CollectionUtils.isNotEmpty(systemActivityProductList)) {
            //如果活动产品不为空的话则保存活动产品
            List<SystemActivityProductRule> activityProductRuleList = Lists.newArrayList();
            for (SystemActivityProductForm systemActivityProductForm : systemActivityProductList) {
                //保存活动产品
                SystemActivityProduct systemActivityProduct = new SystemActivityProduct();
                systemActivityProduct.setSaId(systemActivityProgrammeId);
                systemActivityProduct.setProductId(systemActivityProductForm.getProductId());
                systemActivityProduct.setPlanId(systemActivityProductForm.getPlanIdJson());
                systemActivityProduct.setQuantity(systemActivityProductForm.getQuantity());
                systemActivityProduct.setRewardType(systemActivityProductForm.getRewardType());
                systemActivityProduct.setCreateBy(currentUserId);
                systemActivityProduct.setTriggerType(systemActivityProductForm.getTriggerType());
                systemActivityProductMapper.insertUseGeneratedKeys(systemActivityProduct);

                List<SystemActivityProductRuleForm> systemActivityProductRuleList = systemActivityProductForm.getSystemActivityProductRuleList();
                for (SystemActivityProductRuleForm systemActivityProductRuleForm : systemActivityProductRuleList) {
                    SystemActivityProductRule systemActivityProductRule = new SystemActivityProductRule();
                    BeanUtils.copyProperties(systemActivityProductRuleForm, systemActivityProductRule);
                    systemActivityProductRule.setSystemActivityProductId(systemActivityProduct.getId());
                    systemActivityProductRule.setCreateBy(currentUserId);
                    activityProductRuleList.add(systemActivityProductRule);
                }
            }
            //批量保存活动产品规则
            systemActivityProductRuleMapper.insertList(activityProductRuleList);
        }
    }

    /**
     * 保存活动产品以及规则
     *
     * @param systemActivityProgrammeForm
     * @param currentUserId
     * @param systemActivityProgrammeId
     */
    private void saveSystemActivityProductWhale(SystemActivityProgrammeForm systemActivityProgrammeForm, String currentUserId, Long systemActivityProgrammeId) {
        List<SystemActivityProductForm> systemActivityProductList = systemActivityProgrammeForm.getSystemActivityProductList();
        if (CollectionUtils.isNotEmpty(systemActivityProductList)) {
            //如果活动产品不为空的话则保存活动产品
            List<SystemActivityProductRuleWhale> activityProductRuleList = Lists.newArrayList();
            for (SystemActivityProductForm systemActivityProductForm : systemActivityProductList) {
                //保存活动产品
                SystemActivityProductWhale systemActivityProduct = new SystemActivityProductWhale();
                systemActivityProduct.setSaId(systemActivityProgrammeId);
                systemActivityProduct.setSellProductId(systemActivityProductForm.getProductId());
                systemActivityProduct.setSellProductName(systemActivityProductForm.getProductName());
                systemActivityProduct.setRiskId(systemActivityProductForm.getPlanIdJson());
                systemActivityProduct.setRiskName(systemActivityProductForm.getRiskNameJson());
                systemActivityProduct.setQuantity(systemActivityProductForm.getQuantity());
                systemActivityProduct.setRewardType(systemActivityProductForm.getRewardType());
                systemActivityProduct.setCreateBy(currentUserId);
                systemActivityProduct.setTriggerType(systemActivityProductForm.getTriggerType());
                systemActivityProductWhaleMapper.insertUseGeneratedKeys(systemActivityProduct);

                List<SystemActivityProductRuleForm> systemActivityProductRuleList = systemActivityProductForm.getSystemActivityProductRuleList();
                for (SystemActivityProductRuleForm systemActivityProductRuleForm : systemActivityProductRuleList) {
                    SystemActivityProductRuleWhale systemActivityProductRule = new SystemActivityProductRuleWhale();
                    BeanUtils.copyProperties(systemActivityProductRuleForm, systemActivityProductRule);
                    systemActivityProductRule.setSystemActivityProductId(systemActivityProduct.getId());
                    systemActivityProductRule.setCreateBy(currentUserId);
                    activityProductRuleList.add(systemActivityProductRule);
                }
            }
            //批量保存活动产品规则
            systemActivityProductRuleWhaleMapper.insertList(activityProductRuleList);
        }
    }

    /**
     * 获取活动方案实体
     *
     * @param systemActivityProgrammeForm
     * @param currentUserId
     */
    private SystemActivityProgramme getSystemActivityProgramme(SystemActivityProgrammeForm systemActivityProgrammeForm, String currentUserId) {
        SystemActivityProgramme systemActivityProgramme = new SystemActivityProgramme();
        BeanUtils.copyProperties(systemActivityProgrammeForm, systemActivityProgramme);
        systemActivityProgramme.setRegions(systemActivityProgrammeForm.getRegionJson());
        systemActivityProgramme.setProducts(systemActivityProgrammeForm.getProductsJson());
        systemActivityProgramme.setActiveFlag(0);
        systemActivityProgramme.setCreateBy(currentUserId);
        systemActivityProgramme.setOpenCount(0);
        return systemActivityProgramme;
    }

    /**
     * 获取活动方案实体
     *
     * @param systemActivityProgrammeForm
     * @param currentUserId
     */
    private void updateSystemActivityProgramme(SystemActivityProgrammeForm systemActivityProgrammeForm, String currentUserId, SystemActivityProgramme systemActivityProgramme) {
        BeanUtils.copyProperties(systemActivityProgrammeForm, systemActivityProgramme);
        systemActivityProgramme.setRegions(systemActivityProgrammeForm.getRegionJson());
        systemActivityProgramme.setProducts(systemActivityProgrammeForm.getProductsJson());
        systemActivityProgramme.setUpdateBy(currentUserId);
        systemActivityProgramme.setUpdateTime(LocalDateTime.now());
        systemActivityProgrammeMapper.updateByPrimaryKeySelective(systemActivityProgramme);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateActivityState(Long id, EnumActivityOperation activityOperation) {
        SystemActivityProgramme systemActivityProgramme = systemActivityProgrammeMapper.selectByPrimaryKey(id);
        //校验活动方案是否存在
        if (Objects.isNull(systemActivityProgramme)) {
            throw SystemActivityBusinessException.SYSTEM_ACTIVITY_NOT_EXIST;
        }
        //获取当前活动状态
        EnumActivityState activityState = EnumActivityState.getActivityStateByCodeAndTime(systemActivityProgramme.getStartTime(), systemActivityProgramme.getEndTime(), systemActivityProgramme.getActiveFlag());
        //判断当前活动状态是否改支持活动操作
        boolean supportOperation = activityOperation.checkActivityOperation(activityState);
        if (!supportOperation) {
            throw SystemActivityBusinessException.SYSTEM_ACTIVITY_OPERATION_ERROR;
        }
        //插入活动状态操作记录
        SmActivityOperationRecord smActivityOperationRecord = new SmActivityOperationRecord();
        smActivityOperationRecord.setSaId(id);
        smActivityOperationRecord.setOperationType(activityOperation.name());
        smActivityOperationRecord.setOperationTime(LocalDateTime.now());
        smActivityOperationRecord.setOperator(HttpRequestUtil.getUserId());
        operationRecordMapper.insertUseGeneratedKeys(smActivityOperationRecord);
        if (Objects.equals(activityOperation, EnumActivityOperation.DELETE)) {
            systemActivityProgramme.setEnabledFlag(1);
            systemActivityProgrammeMapper.updateByPrimaryKeySelective(systemActivityProgramme);
        }
        //更新活动状态
        systemActivityProgrammeMapper.updateActivityFlag(id, activityOperation.getNextActiveFlag());
        //如果当前活动操作是继续 则插入暂停时间统计数据
        if (Objects.equals(activityOperation, EnumActivityOperation.CONTINUE)) {
            //获取上次暂停的操作时间
            SmActivityOperationRecord lastSuspendOperationRecord = operationRecordMapper.getByLastOperationRecord(id, EnumActivityOperation.SUSPEND.name());
            SmActivitySuspendCensus smActivitySuspendCensus = new SmActivitySuspendCensus();
            smActivitySuspendCensus.setSaId(id);
            smActivitySuspendCensus.setSuspendOperationRecordId(lastSuspendOperationRecord.getId());
            smActivitySuspendCensus.setContinueOperationRecordId(smActivityOperationRecord.getId());
            smActivitySuspendCensus.setStartTime(lastSuspendOperationRecord.getOperationTime());
            smActivitySuspendCensus.setEndTime(LocalDateTime.now());
            suspendCensusMapper.insert(smActivitySuspendCensus);

        }
    }

}
