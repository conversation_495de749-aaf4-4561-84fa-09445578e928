package com.cfpamf.ms.insur.operation.phoenix.pojo.po;

import com.cfpamf.ms.insur.operation.base.entity.BaseNoUserEntity;
import com.cfpamf.ms.insur.operation.phoenix.enums.EnumTodoBizType;
import com.cfpamf.ms.insur.operation.phoenix.enums.EnumTodoState;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import javax.persistence.Column;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import java.time.LocalDateTime;

/**
 * <AUTHOR> 2022/12/12 14:46
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class PhoenixEmpTodo extends BaseNoUserEntity {

    String jobNumber;

    String targetId;

    String todoMsg;

    LocalDateTime finishTime;

    LocalDateTime planTime;

    @Column(name = "biz_type")
    @Enumerated(EnumType.STRING)
    EnumTodoBizType bizType;

    /**
     *
     */
    @Column(name = "state")
    @Enumerated(EnumType.STRING)
    EnumTodoState state;

    String todoProperty;

    String remark;

    Integer followSortNo;
}
