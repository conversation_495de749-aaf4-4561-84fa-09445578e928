package com.cfpamf.ms.insur.operation.pco.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class PcoLevelList {

    /**
     * 钉钉用户信息
     */
    @ApiModelProperty("钉钉用户信息")
    private DingUserInfo dingUserInfo;

    /**
     * PCO分级管理级别人数统计
     */
    @ApiModelProperty("PCO分级管理级别人数统计")
    private PcoLevelStats pcoLevelStats;

    /**
     * 待办信息
     */
    @ApiModelProperty("待办信息")
    private List<PcoScheduleInfo> pcoScheduleInfos;
}
