package com.cfpamf.ms.insur.operation.honor.service;


import com.cfpamf.ms.insur.operation.dingtalk.entity.wecat.WechatUserInfoPO;
import com.cfpamf.ms.insur.operation.dingtalk.service.WecatService;
import com.cfpamf.ms.insur.operation.honor.calculate.HonorCalculate;
import com.cfpamf.ms.insur.operation.honor.dto.*;
import com.cfpamf.ms.insur.operation.honor.enums.EnumHonorLevel;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 */
@Service
@Slf4j
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class CalculateHonorResultService {

    Map<String, HonorCalculate> calculateMap = new ConcurrentHashMap<>();

    @Autowired
    WecatService wecatService;

    ApplicationContext context;

    public List<HonorsCalculateResults> calculate(HonorCalculateRule rule) {
        return findLevelValidation(rule.getHonorType()).calculate(rule);
    }

    /**
     * 人工评选结果计算
     */
    public List<HonorsCalculateResults> calculateByPeople(HonorCalculateRule rule) {
        return findLevelValidation(rule.getHonorType()).calculateByPeople(rule);
    }

    /**
     * 找到对应的榜单计算规则
     *
     * @param honorType 荣誉榜单类型
     * @return HonorCalculate
     */
    public HonorCalculate findLevelValidation(String honorType) {
        if (calculateMap.isEmpty()) {
            synchronized (this) {
                calculateMap.putAll(context.getBeansOfType(HonorCalculate.class));
            }
        }
        HonorCalculate validation = calculateMap.get(honorType + "Calculate");
        if (Objects.isNull(validation)) {
            throw new UnsupportedOperationException("评选类型暂不支持:" + honorType);
        }
        return validation;
    }

    public List<HonorsCalculateResults> searchHonorInAction(HonorCalculateRule rule) {
        List<HonorsCalculateResults> results = findLevelValidation(rule.getHonorType()).searchHonorInAction(rule);
        //如果是员工荣誉，则获取钉钉头像
        fillAvatars(rule, results);
        return results;
    }

    public void fillAvatars(HonorCalculateRule rule, List<HonorsCalculateResults> results) {
        try {
            if(EnumHonorLevel.EMP.getCode().equals(rule.getLevel())){
                List<String> jobNumbers = results.stream().map(HonorsCalculateResults::getEmpCode).collect(Collectors.toList());
                //获取员工的钉钉头像信息
                List<WechatUserInfoPO> wechatUserInfoPOS = this.wecatService.getDingTalkInfo(jobNumbers);
                //将对应工号的头像地址放入results对应的员工荣誉结果中
                results.forEach(result -> {
                    wechatUserInfoPOS.forEach(wechatUserInfoPO -> {
                        if (result.getEmpCode().equals(wechatUserInfoPO.getWechatUserId())){
                            result.setAvatar(wechatUserInfoPO.getAvatar());
                        }
                    });
                });
            }
        }catch (Exception e){
            log.warn("获取钉钉头像信息失败",e);
        }
    }

    public HonorsCalculateResults getSingleHonorRank(HonorCalculateRule rule) {
        List<HonorsCalculateResults> results = findLevelValidation(rule.getHonorType()).searchObjectHonorRank(rule);
        fillAvatars(rule, results);
        if (CollectionUtils.isNotEmpty(results)){
            HonorsCalculateResults result = results.get(0);
            if (!result.getAreaName().endsWith("区域")){
                result.setRankNum(null);
            }
            return result;
        }
        return null;
    }
}
