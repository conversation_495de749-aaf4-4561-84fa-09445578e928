package com.cfpamf.ms.insur.operation.activity.entity;

import com.cfpamf.ms.insur.operation.activity.enums.ConflictRule;
import com.cfpamf.ms.insur.operation.base.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.Table;

/**
 * system_activity_ref
 * <AUTHOR>
@Table(name="system_activity_ref")
@ApiModel(value="活动关联表")
@Data
public class SystemActivityRef extends BaseEntity {

    /**
     * 活动id1
     */
    @ApiModelProperty(value="活动id1")
    private Long saId1;

    /**
     * 活动id2
     */
    @ApiModelProperty(value="活动id2")
    private Long saId2;

    /**
     * 关联类型 overlay-叠加 optimal-最优
     */
    @ApiModelProperty(value="关联类型 overlay-叠加 optimal-最优")
    @Enumerated(EnumType.STRING)
    @Column(name = "conflict_rule")
    private ConflictRule conflictRule;

}