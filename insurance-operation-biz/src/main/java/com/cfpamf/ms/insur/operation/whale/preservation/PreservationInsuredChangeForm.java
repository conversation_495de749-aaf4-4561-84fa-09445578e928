package com.cfpamf.ms.insur.operation.whale.preservation;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
@ApiModel("被保人信息变更")
public class PreservationInsuredChangeForm {

    @NotBlank(message = "被保人编号不能为空")
    @ApiModelProperty("被保人编号")
    private String insuredCode;

    @ApiModelProperty("变更前信息")
    private PreservationInsuredBaseInfo before;

    @NotNull(message = "被保人变更信息不能为空")
    @ApiModelProperty("变更后信息")
    private PreservationInsuredBaseInfo after;

}
