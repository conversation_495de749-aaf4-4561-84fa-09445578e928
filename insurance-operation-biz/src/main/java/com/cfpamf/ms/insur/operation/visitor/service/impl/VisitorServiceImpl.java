package com.cfpamf.ms.insur.operation.visitor.service.impl;

import com.cfpamf.ms.insur.operation.visitor.exception.VisitorBusinessException;
import com.cfpamf.ms.insur.operation.visitor.form.ProductVisitRecordSearchForm;
import com.cfpamf.ms.insur.operation.visitor.form.TimeSearchForm;
import com.cfpamf.ms.insur.operation.visitor.service.VisitorService;
import com.cfpamf.ms.insur.operation.visitor.vo.*;
import com.github.pagehelper.PageInfo;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @date 2021/5/17 20:14
 */
@Service
public class VisitorServiceImpl implements VisitorService {
    /**
     * yyyyMMdd时间字符判断的正则表达式
     */
    private final static String REGEX = "((\\d{3}[1-9]|\\d{2}[1-9]\\d|\\d[1-9]\\d{2}|[1-9]\\d{3})(((0[13578]|1[02])(0[1-9]|[12]\\d|3[01]))|((0[469]|11)(0[1-9]|[12]\\d|30))|(02(0[1-9]|[1]\\d|2[0-8]))))|(((\\d{2})(0[48]|[2468][048]|[13579][26])|((0[48]|[2468][048]|[3579][26])00))0229)";
    /**
     * yyyyMMdd时间字符判断的Pattern
     */
    private final static Pattern PATTERN = Pattern.compile(REGEX);

//    @Autowired
//    VisitRecordMapper visitRecordMapper;

    @Override
    public VisitorSimpleStatisticsVo getVisitorSimpleStatistics(String deadlineString, String jobNumber) {
        Matcher matcher = PATTERN.matcher(deadlineString);
        if (!matcher.matches()) {
            throw VisitorBusinessException.VISITOR_TIME_PARAM_FORMAT_ERROR;
        }
        return null;
    }

    @Override
    public PageInfo<VisitorRecordVo> searchVisitorRecord(TimeSearchForm timeSearchForm) {
        return null;
    }

    @Override
    public VisitorDetailVo getVisitorDetail(String wxOpenId) {
        return null;
    }

    @Override
    public List<ProductVisitVo> findProductVisitRecord() {
        return null;
    }

    @Override
    public PageInfo<VisitorProductRecordVo> searchVisitorProductRecord(ProductVisitRecordSearchForm productVisitRecordSearchForm) {
        return null;
    }
}
