package com.cfpamf.ms.insur.operation.prospectus.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.Setter;
/**
 * Response class to be returned by Api
 *
 * <AUTHOR>
 */

/**
 * 个人计划书统计视图对象
 *
 * <AUTHOR>
 */
@ApiModel(description = "个人计划书统计视图对象")
@Getter
@Setter
public class ProspectusPersonStatisticsVo {
    @JsonProperty("thirtyDaysCount")
    private Integer thirtyDaysCount;

    @JsonProperty("totalCount")
    private Integer totalCount;

    public ProspectusPersonStatisticsVo() {
    }

    public ProspectusPersonStatisticsVo(Integer thirtyDaysCount, Integer totalCount) {
        this.thirtyDaysCount = thirtyDaysCount;
        this.totalCount = totalCount;
    }
}

