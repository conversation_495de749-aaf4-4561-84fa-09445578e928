package com.cfpamf.ms.insur.operation.dingtalk.event;

import com.cfpamf.ms.insur.operation.base.event.BaseEvent;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/6/20 10:04
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
@AllArgsConstructor
@NoArgsConstructor
public class DingTalkUserIdErrorEvent implements BaseEvent {

    List<String> userIds;
}
