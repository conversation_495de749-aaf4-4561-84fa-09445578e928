package com.cfpamf.ms.insur.operation.assistant.service.impl;

import com.cfpamf.ms.insur.operation.assistant.enums.AssistantTargetEnum;
import com.cfpamf.ms.insur.operation.assistant.service.CalcContext;
import com.cfpamf.ms.insur.operation.assistant.service.IncrementCalcStrategy;
import com.cfpamf.ms.insur.operation.assistant.vo.EstimateVo;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;

@Order(4)
@Component
public class UnloanNcNormInsuranceAmtIncrCalcStrategy implements IncrementCalcStrategy {
    @Override
    public EstimateVo calc(CalcContext context) {
        EstimateVo vo = new EstimateVo();
        BigDecimal value = BigDecimal.ZERO;
        //非主营拓新目标值=标保目标值-主营标保预计可达成值-非主营留存标保预计可达成值-主营留存标保预计可达成值
        if(context.getSmNormInsuranceAmtTarget()!=null){
            value = context.getSmNormInsuranceAmtTarget()
                    .subtract(context.getSmLoanNormInsuranceAmtTarget())
                    .subtract(context.getSmUnloanRpNormInsuranceAmtTarget())
                    .subtract(context.getSmLoanRpNormInsuranceAmtTarget())
                    .subtract(context.getSmUnloanNcNormInsuranceAmt())
                    .subtract(context.getSmUnloanRpNormInsuranceAmt())
                    .subtract(context.getSmLoanRpNormInsuranceAmt());
        }
        if (value.compareTo(BigDecimal.ZERO)<=0){
            value = BigDecimal.ZERO;
        }
        vo.setEstimateType(AssistantTargetEnum.UNLOAN_NC_CLASSIC_INSURANCE_AMOUNT);
        vo.setValue(value);
        context.setSmUnloanNcNormInsuranceAmtTarget(value);
        return vo;
    }
}
