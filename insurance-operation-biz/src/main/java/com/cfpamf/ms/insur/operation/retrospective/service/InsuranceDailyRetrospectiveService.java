package com.cfpamf.ms.insur.operation.retrospective.service;

import com.cfpamf.ms.insur.operation.customer.dto.CustomerInterruptionFollowDto;
import com.cfpamf.ms.insur.operation.retrospective.dto.CustomerConvertPremiumDto;
import com.cfpamf.ms.insur.operation.retrospective.query.QueryCustomerConvertPremium;
import com.cfpamf.ms.insur.operation.retrospective.query.QueryFollowListIncludeAIRetrospectiveQuery;

import java.util.List;

/**
 * @Description 日复盘接口
 * @Date 19:06 2024/3/19
 * @Param
 * <AUTHOR>
 * @return
 **/
public interface InsuranceDailyRetrospectiveService {


    List<CustomerConvertPremiumDto> getCustomerConvertPremium(QueryCustomerConvertPremium query);
    List<CustomerInterruptionFollowDto> queryFollowListIncludeRetrospective(QueryFollowListIncludeAIRetrospectiveQuery query);
}
