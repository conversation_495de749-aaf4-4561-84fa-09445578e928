package com.cfpamf.ms.insur.operation.phoenix.controller;

import com.cfpamf.common.ms.result.Result;
import com.cfpamf.ms.insur.operation.base.annotaions.ResponseDecorated;
import com.cfpamf.ms.insur.operation.job.PhoenixJobHandler;
import com.cfpamf.ms.insur.operation.phoenix.pojo.dto.ManualMatchRenewalPolicyTaskDto;
import com.cfpamf.ms.insur.operation.phoenix.pojo.query.PhoenixEmpTodoBackQuery;
import com.cfpamf.ms.insur.operation.phoenix.pojo.query.PhoenixEmpTodoQuery;
import com.cfpamf.ms.insur.operation.phoenix.pojo.vo.PhoenixEmpTodoBackVo;
import com.cfpamf.ms.insur.operation.phoenix.pojo.vo.PhoenixEmpTodoCountVo;
import com.cfpamf.ms.insur.operation.phoenix.pojo.vo.PhoenixEmpTodoTabCountVo;
import com.cfpamf.ms.insur.operation.phoenix.pojo.vo.PhoenixEmpTodoVO;
import com.cfpamf.ms.insur.operation.phoenix.service.PhoenixEmpTodoService;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR> 2022/12/12 14:48
 */
@RestController
@ResponseDecorated
@RequestMapping("/phoenix/emp/todo")
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@AllArgsConstructor
@Api(value = "待办", tags = "待办")
public class PhoenixEmpTodoController {

    PhoenixEmpTodoService empTodoService;

    PhoenixJobHandler phoenixJobHandler;

    @ApiOperation(value = "待办统计")
    @GetMapping("/count")
    public PhoenixEmpTodoCountVo countTodo(PhoenixEmpTodoQuery query) {
        return empTodoService.countTodo(query);
    }

    @ApiOperation(value = "待办分类统计")
    @GetMapping("/tab/count")
    public PhoenixEmpTodoTabCountVo countTabTodo(PhoenixEmpTodoQuery query) {
        return empTodoService.countTabTodo(query);
    }

    @Deprecated
    @ApiOperation(value = "待办清单(不在维护,请升级成v1接口)",tags = "CORE")
    @GetMapping("")
    public PageInfo<PhoenixEmpTodoVO> list(PhoenixEmpTodoQuery query) {
        return empTodoService.list(query);
    }

    @ApiOperation(value = "待办清单V1",tags = "CORE")
    @GetMapping("/v1")
    public PageInfo<PhoenixEmpTodoVO> listV1(PhoenixEmpTodoQuery query) {
        return empTodoService.listV1(query);
    }

    @ApiOperation(value = "根据业务类型列表查询个人待办数量",tags = "CORE")
    @PostMapping("/bizs/count")
    public Integer queryPersonTodoCount(@RequestBody PhoenixEmpTodoQuery query) {
        return empTodoService.queryPersonTodoCount(query.getUserId(),query.getBizTypes());
    }

    @ApiOperation(value = "后台待办列表",tags = "CORE")
    @PostMapping("/back/list")
    public PageInfo<PhoenixEmpTodoBackVo> backList(@RequestBody PhoenixEmpTodoBackQuery query) {
        return empTodoService.listBack(query);
    }

    @ApiOperation(value = "后台待办列表--总条数",tags = "CORE")
    @PostMapping("/back/list/cnt")
    public Integer backListCnt(@RequestBody PhoenixEmpTodoBackQuery query) {
        return empTodoService.listBackCnt(query);
    }

    @ApiOperation(value = "后台待办列表",tags = "CORE")
    @GetMapping("/back/list/download")
    public void downloadBackList(PhoenixEmpTodoBackQuery query, HttpServletResponse response) {
        empTodoService.downloadBackList(query,response);
    }

    @ApiOperation(value = "测试",tags = "CORE")
    @GetMapping("/back/list/test")
    public void test() {
        phoenixJobHandler.handlerRenewTdoInit();
    }

    @ApiOperation(value = "手工测试",tags = "CORE")
    @PostMapping("/back/list/doManualMatchRenewalPolicyHandler")
    public void doManualMatchRenewalPolicyHandler(@RequestBody ManualMatchRenewalPolicyTaskDto task) {

        phoenixJobHandler.doManualMatchRenewalPolicyHandler(task.getPolicyList());
    }
}
