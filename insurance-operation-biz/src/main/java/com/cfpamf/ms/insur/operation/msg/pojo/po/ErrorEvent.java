package com.cfpamf.ms.insur.operation.msg.pojo.po;

import com.cfpamf.ms.insur.operation.base.entity.BaseNoUserEntity;
import lombok.AccessLevel;
import lombok.experimental.FieldDefaults;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2023/5/26 17:32
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class ErrorEvent extends BaseNoUserEntity {

    String bizType;

    String targetId;

    String msg;

    String extend;
}
