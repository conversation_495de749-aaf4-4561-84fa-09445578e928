package com.cfpamf.ms.insur.operation.prospectus.form;

import com.cfpamf.ms.insur.operation.prospectus.enums.ProspectusType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 计划书表单
 *
 * <AUTHOR>
 * @date 2021/5/18 18:24
 */
@Getter
@Setter
@ApiModel(value = "计划书表单")
public class ProspectusConfigForm {

    @ApiModelProperty("名称")
    @NotBlank(message = "计划书名称不能为空")
    @Length(max = 20, message = "计划书名称不能超过20个字符")
    private String name;

    @ApiModelProperty("类型")
    @NotNull(message = "计划书类型不能为空")
    private ProspectusType type;

    @ApiModelProperty("产品id")
    @NotNull(message = "计划书产品不能为空")
    private Long productId;

    @NotBlank(message = "计划书推荐理由不能为空")
    @ApiModelProperty("推荐理由")
    @Length(max = 200, message = "计划书推荐理由不能超过200位")
    private String recommendationReason;

    @ApiModelProperty("视频讲解文件路径")
    private String explainVideoFilePath;

    @ApiModelProperty("计划书视频封面")
    private String explainVideoImg;


    @ApiModelProperty("是否启用 1启用 0禁用")
    private Integer enabled;
}
