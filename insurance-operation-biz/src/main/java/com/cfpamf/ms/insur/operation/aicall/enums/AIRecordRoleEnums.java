package com.cfpamf.ms.insur.operation.aicall.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * AI会话角色
 * @Date 2023/04/19 15:37
 * @Version 1.0
 */
@AllArgsConstructor
@Getter
public enum AIRecordRoleEnums {

    /**
     * AI会话角色 speech:机器人,voice:客户
     */
    SPEECH("speech","机器人"),
    VOICE("voice","客户");

    String code;
    String desc;
    public static String dict(String code) {
        if(Objects.isNull(code)){
            return "";
        }
        return Stream.of(values())
                .filter(em -> em.getCode().equals(code))
                .findFirst().map(AIRecordRoleEnums::getDesc)
                .orElse("");
    }
}
