package com.cfpamf.ms.insur.operation.customer.dao;

import com.cfpamf.ms.insur.operation.base.dao.CommonMapper;
import com.cfpamf.ms.insur.operation.customer.dto.OrderCustomerDto;
import com.cfpamf.ms.insur.operation.customer.dto.WxCmsSmyDto;
import com.cfpamf.ms.insur.operation.customer.po.CustomerInterruptionPolicyPo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Example;

import java.math.BigDecimal;
import java.util.List;


/**
 * <AUTHOR>
 */
@Mapper
public interface CustomerInterruptionPolicyMapper extends CommonMapper<CustomerInterruptionPolicyPo> {

    /**
     * 获取当前用户的激活保单数
     * @param userId
     * @return
     */
    WxCmsSmyDto getQtyByRenewEmp(@Param("userId") String userId);

    /**
     * 新增激活保单信息
     * @param list
     */
    void insertListDuplicateUpdate(@Param("list")List<CustomerInterruptionPolicyPo> list);

    /**
     * 根据订单号、投保人证件号获取激活保单明细
     * @param interruptionDto
     */
    default CustomerInterruptionPolicyPo getByOrderIdAndAppIdNumber(OrderCustomerDto interruptionDto){
        Example e = new Example(CustomerInterruptionPolicyPo.class);
        e.createCriteria().andEqualTo("fhOrderId",interruptionDto.getFhOrderId()).andEqualTo("idNumber",interruptionDto.getIdNumber())
                .andEqualTo("policyNo",interruptionDto.getThPolicyNo()).andEqualTo("policyState",1);
        List<CustomerInterruptionPolicyPo> list = this.selectByExample(e);
        return CollectionUtils.isEmpty(list)?null:list.get(0);
    }
    /**
     * 根据订单号、投保人证件号获取激活保单明细
     * @param policyNo
     */
    default BigDecimal getAmountByPolicyNo(String policyNo){
        Example e = new Example(CustomerInterruptionPolicyPo.class);
        e.createCriteria().andEqualTo("policyNo",policyNo).andEqualTo("policyState",4);
        List<CustomerInterruptionPolicyPo> list = this.selectByExample(e);
        return CollectionUtils.isEmpty(list)?BigDecimal.ZERO:list.get(0).getAmount();
    }
}
