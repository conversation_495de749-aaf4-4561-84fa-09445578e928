package com.cfpamf.ms.insur.operation.addCommission.query;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Objects;

/**
 * 小额保险订单查询query
 *
 * <AUTHOR>
 */
@Data
@ApiModel
public class WhaleAddCommissionPushQuery {

    /**
     * 批次号
     */
    @ApiModelProperty(value = "结算批次号")
    private List<String> batchNoList;

    /**
     * 加佣记录id
     */
    @ApiModelProperty(value = "加佣记录id")
    private Integer id;

    /**
     * 活动id
     */
    @ApiModelProperty(value = "活动id")
    private Integer saId;

    @ApiModelProperty(value = "保单号")
    private String policyNo;

    @ApiModelProperty(value = "批单号")
    private String endorsementNo;

    @ApiModelProperty(value = "被保人编码")
    private String insuredCode;

    @ApiModelProperty(value = "期数")
    private Integer termNum;

    /**
     * 偏移量
     */
    @ApiModelProperty(value = "偏移量")
    private Integer offset;

    /**
     * 页码
     */
    @ApiModelProperty(value = "页码")
    private Integer pageSize;

    /**
     * 每页记录数
     */
    @ApiModelProperty(value = "每页记录数")
    private Integer size;

    public Integer getOffset() {
        if (!Objects.isNull(pageSize)) {
            return size*(pageSize-1);
        }
        return 0;
    }
}
