package com.cfpamf.ms.insur.operation.planbook.dao;

import com.cfpamf.ms.insur.operation.base.dao.CommonMapper;
import com.cfpamf.ms.insur.operation.planbook.entity.OperationPlanBookProposer;
import org.apache.ibatis.annotations.Mapper;
import tk.mybatis.mapper.entity.Example;

/**
 * <AUTHOR>
 */
@Mapper
public interface OperationPlanBookProposerMapper extends CommonMapper<OperationPlanBookProposer> {

    /**
     * 根据计划书id删除数据
     *
     * @param planBookId
     * @return
     */
    default Integer deleteByPlanBookId(Integer planBookId) {
        Example example = new Example(OperationPlanBookProposer.class);
        example.createCriteria().andEqualTo("planBookId", planBookId);
        return deleteByExample(example);
    }

    /**
     * 查询计划书投保人
     * @param planBookId
     * @return
     */
    default OperationPlanBookProposer queryPlanBookProposerByPlanBookId(Integer planBookId) {
        Example example = new Example(OperationPlanBookProposer.class);
        example.createCriteria().andEqualTo("planBookId", planBookId);
        return selectOneByExample(example);
    }

}