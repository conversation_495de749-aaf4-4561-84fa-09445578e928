package com.cfpamf.ms.insur.operation.job;

import com.cfpamf.ms.insur.operation.activity.service.SmCommissionDetailPerformanceService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 实收业绩job 2022年版本
 * <p>
 * 参数 start~end
 *
 * <AUTHOR>
 * @date 2022/8/27 13:52
 */
@Slf4j
@Component
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class CommissionPerformanceJobHandler {

    static String TIME_SPLIT = "~";
    @Autowired
    SmCommissionDetailPerformanceService service;

    /**
     * 实收业绩计算
     */
    @XxlJob("commission-performance")
    public void performance() {

        //默认的车险时间 车险的计算时间是固定的 因为是回算一整年
        LocalDateTime start = LocalDate.parse("2023-01-01").atStartOfDay();
        LocalDateTime end = LocalDate.parse("2023-12-31").atTime(23, 59, 59);
        service.performanceAuto(start, end);
        XxlJobHelper.log("车险实收处理成功");
        String jobParam = XxlJobHelper.getJobParam();

        //默认的非车险数据  插入最近三十一天的数据（部分数据可以调整31天内的数据）
        start = LocalDate.now().minusDays(31).atStartOfDay();
        end = LocalDate.now().atStartOfDay();
        if (StringUtils.isNotBlank(jobParam) && StringUtils.contains(jobParam, TIME_SPLIT)) {
            String[] split = jobParam.split(TIME_SPLIT);
            start = LocalDate.parse(split[0]).atStartOfDay();
            end = LocalDate.parse(split[1]).atStartOfDay();
        }
        service.performanceNotCx(start, end);
        XxlJobHelper.log("非车险实收处理成功");
    }

}
