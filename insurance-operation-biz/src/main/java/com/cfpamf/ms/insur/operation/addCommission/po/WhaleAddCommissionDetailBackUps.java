package com.cfpamf.ms.insur.operation.addCommission.po;

import com.cfpamf.ms.insur.operation.base.entity.BaseNoUserEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * whale_add_commission_detail
 *
 * <AUTHOR>
@Data
@Table(name = "whale_add_commission_detail_backups")
public class WhaleAddCommissionDetailBackUps extends BaseNoUserEntity {


    /**
     * 订单id
     */
    @ApiModelProperty(value = "批单号")
    private String endorsementNo;

    /**
     * 保单号
     */
    @ApiModelProperty(value = "保单号")
    private String policyNo;

    /**
     * 商品编码
     */
    @ApiModelProperty(value="商品编码")
    private String sellProductCode;

    /**
     * 险种编码
     */
    @ApiModelProperty(value="险种编码")
    private String riskCode;

    /**
     * 期数
     */
    @ApiModelProperty(value="期数")
    private Integer termNum;
    /**
     * 期数
     */
    @ApiModelProperty(value="险种状态")
    private String productStatus;

    /**
     * 加佣比例
     */
    @ApiModelProperty(value = "加佣比例")
    private BigDecimal proportion;
    @ApiModelProperty(value = "保费")
    private BigDecimal amount;
    @ApiModelProperty(value = "加佣金额")
    private BigDecimal addCommissionAmount;
    @ApiModelProperty(value = "被保人编码")
    private String insuredCode;

    @ApiModelProperty(value = "唯一标识，唯一索引字段值拼接而成 拼接字符|")
    private String uuid;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private LocalDateTime createTime;
    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private LocalDateTime updateTime;
    /**
     * 操作备注（是什么操作前的快照数据）
     */
    @ApiModelProperty(value = "操作备注（是什么操作前的快照数据）")
    private String operationRemark;
}
