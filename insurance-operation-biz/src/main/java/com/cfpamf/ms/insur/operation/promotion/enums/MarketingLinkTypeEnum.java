package com.cfpamf.ms.insur.operation.promotion.enums;

import com.cfpamf.common.ms.exception.MSBizNormalException;
import com.cfpamf.ms.insur.operation.base.exception.BusinessException;
import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;

@Getter
public enum MarketingLinkTypeEnum {
    URL(3,"url"),
    MINI_PROGRAM(6,"小程序"),
    WECHAT_ENTERPRISE(1,"企业微信")
    ;
    private Integer linkType;
    private String linkTypeName;
    MarketingLinkTypeEnum(Integer linkType, String linkTypeName)
    {
        this.linkType = linkType;
        this.linkTypeName = linkTypeName;
    }
    public static MarketingLinkTypeEnum decode(Integer linkType) {
        return Arrays.stream(MarketingLinkTypeEnum.values())
                .filter(x -> Objects.equals(x.getLinkType(), linkType))
                .findFirst().orElseThrow(() -> new MSBizNormalException("", "跳转类型不匹配"));
    }
}
