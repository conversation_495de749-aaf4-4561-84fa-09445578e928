package com.cfpamf.ms.insur.operation.call.service;

import com.cfpamf.common.ms.exception.MSBizNormalException;
import com.cfpamf.ms.insur.operation.base.helper.BmsHelper;
import com.cfpamf.ms.insur.operation.call.dao.CallRecordMapper;
import com.cfpamf.ms.insur.operation.call.dto.CallRequestDto;
import com.cfpamf.ms.insur.operation.call.dto.CallResponseDto;
import com.cfpamf.ms.insur.operation.call.po.CallRecordPo;
import com.cfpamf.ms.insur.operation.call.feign.CallServiceFacade;
import com.cfpamf.ms.insur.operation.call.feign.dto.CallServiceRequestDto;
import com.cfpamf.ms.insur.operation.call.feign.dto.CallServiceResponseDto;
import com.cfpamf.ms.insur.operation.fegin.bms.response.vo.UserDetailVO;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;

/**
 * 呼叫服务
 *
 * <AUTHOR>
 * @date 2025-07-22
 */

public interface CallService {


    /**
     * 一键呼叫
     *
     * @param request 呼叫请求
     * @return 呼叫响应
     */
    CallResponseDto oneClickCall(CallRequestDto request);

    /**
     * 根据保单号获取投保人手机号
     *
     * @param policyNo 保单号
     * @return 手机号
     */
    String getMobileByPolicyNo(String policyNo);
}
