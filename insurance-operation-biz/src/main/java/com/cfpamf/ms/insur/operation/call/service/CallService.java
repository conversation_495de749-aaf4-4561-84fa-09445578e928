package com.cfpamf.ms.insur.operation.call.service;

import com.cfpamf.common.ms.exception.MSBizNormalException;
import com.cfpamf.ms.insur.operation.base.helper.BmsHelper;
import com.cfpamf.ms.insur.operation.call.dao.CallRecordMapper;
import com.cfpamf.ms.insur.operation.call.dto.CallRequestDto;
import com.cfpamf.ms.insur.operation.call.dto.CallResponseDto;
import com.cfpamf.ms.insur.operation.call.entity.CallRecordPo;
import com.cfpamf.ms.insur.operation.call.feign.CallServiceFacade;
import com.cfpamf.ms.insur.operation.call.feign.dto.CallServiceRequestDto;
import com.cfpamf.ms.insur.operation.call.feign.dto.CallServiceResponseDto;
import com.cfpamf.ms.insur.operation.fegin.bms.response.vo.UserDetailVO;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;

/**
 * 呼叫服务
 *
 * <AUTHOR>
 * @date 2025-07-22
 */
@Service
@Slf4j
@AllArgsConstructor
public class CallService {

    private final CallRecordMapper callRecordMapper;
    private final CallServiceFacade callServiceFacade;
    private final BmsHelper bmsHelper;

    /**
     * 一键呼叫
     *
     * @param request 呼叫请求
     * @return 呼叫响应
     */
    @Transactional(rollbackFor = Exception.class)
    public CallResponseDto oneClickCall(CallRequestDto request) {
        log.info("开始一键呼叫，保单号：{}，客户手机号：{}", request.getPolicyNo(), request.getCustomerPhone());

        // 获取当前登录用户信息
        UserDetailVO userDetail = bmsHelper.getContextUserDetail();
        if (userDetail == null) {
            throw new MSBizNormalException("获取用户信息失败");
        }

        // 创建呼叫记录
        CallRecordPo callRecord = createCallRecord(request, userDetail);
        callRecordMapper.insert(callRecord);

        try {
            // 构建第三方呼叫请求
            CallServiceRequestDto callServiceRequest = buildCallServiceRequest(request, userDetail, callRecord);

            // 调用第三方呼叫接口
            CallServiceResponseDto callServiceResponse = callServiceFacade.call(callServiceRequest);

            // 更新呼叫记录
            updateCallRecordAfterCall(callRecord, callServiceResponse);

            // 构建响应
            CallResponseDto response = new CallResponseDto();
            response.setCallRecordId(callRecord.getId());
            response.setSuccess(callServiceResponse.getSuccess());
            response.setMessage(callServiceResponse.getMessage());

            if (callServiceResponse.getSuccess() && callServiceResponse.getResult() != null) {
                response.setCallSid(callServiceResponse.getResult().getCallSid());
                response.setCallStatus(1); // 呼叫中
            } else {
                response.setCallStatus(3); // 呼叫失败
            }

            log.info("一键呼叫完成，呼叫记录ID：{}，呼叫SID：{}", callRecord.getId(), response.getCallSid());
            return response;

        } catch (Exception e) {
            log.error("呼叫失败，保单号：{}，错误信息：{}", request.getPolicyNo(), e.getMessage(), e);
            
            // 更新呼叫记录为失败状态
            callRecord.setCallStatus(3);
            callRecord.setFailReason(e.getMessage());
            callRecord.setUpdateTime(LocalDateTime.now());
            callRecordMapper.updateByPrimaryKey(callRecord);

            CallResponseDto response = new CallResponseDto();
            response.setCallRecordId(callRecord.getId());
            response.setSuccess(false);
            response.setMessage("呼叫失败：" + e.getMessage());
            response.setCallStatus(3);
            return response;
        }
    }

    /**
     * 创建呼叫记录
     */
    private CallRecordPo createCallRecord(CallRequestDto request, UserDetailVO userDetail) {
        CallRecordPo callRecord = new CallRecordPo();
        callRecord.setPolicyNo(request.getPolicyNo());
        callRecord.setCustomerPhone(request.getCustomerPhone());
        callRecord.setClientId(request.getClientId());
        callRecord.setClientName(request.getClientName());
        callRecord.setEmployeeId(userDetail.getJobNumber());
        callRecord.setEmployeeName(userDetail.getUserName());
        callRecord.setEmployeePhone(userDetail.getMobile());
        callRecord.setCallStatus(0); // 初始化状态
        callRecord.setRecordStatus(0); // 未获取录音
        callRecord.setFollowMatchStatus(0); // 未匹配跟进记录
        callRecord.setRemark(request.getRemark());
        callRecord.setEnabledFlag(0);
        callRecord.setCreateTime(LocalDateTime.now());
        callRecord.setUpdateTime(LocalDateTime.now());
        return callRecord;
    }

    /**
     * 构建第三方呼叫请求
     */
    private CallServiceRequestDto buildCallServiceRequest(CallRequestDto request, UserDetailVO userDetail, CallRecordPo callRecord) {
        CallServiceRequestDto callServiceRequest = new CallServiceRequestDto();
        callServiceRequest.setFrom(userDetail.getMobile()); // 主叫号码（客户经理手机号）
        callServiceRequest.setTo(request.getCustomerPhone()); // 被叫号码（客户手机号）
        callServiceRequest.setEmployeeId(userDetail.getJobNumber());
        callServiceRequest.setClientId(request.getClientId());
        callServiceRequest.setClientName(request.getClientName());
        callServiceRequest.setKey(request.getPolicyNo()); // 使用保单号作为关键值
        callServiceRequest.setModule("INSURANCE_OPERATION"); // 模块标识
        callServiceRequest.setRemark(request.getRemark());
        return callServiceRequest;
    }

    /**
     * 呼叫后更新记录
     */
    private void updateCallRecordAfterCall(CallRecordPo callRecord, CallServiceResponseDto response) {
        if (response.getSuccess() && response.getResult() != null) {
            callRecord.setCallSid(response.getResult().getCallSid());
            callRecord.setCallStatus(1); // 呼叫中
            callRecord.setBeginCallTime(LocalDateTime.now());
        } else {
            callRecord.setCallStatus(3); // 呼叫失败
            callRecord.setFailReason(response.getMessage());
        }
        callRecord.setUpdateTime(LocalDateTime.now());
        callRecordMapper.updateByPrimaryKey(callRecord);
    }
}
