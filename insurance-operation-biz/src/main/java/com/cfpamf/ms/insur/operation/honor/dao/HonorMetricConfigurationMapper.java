package com.cfpamf.ms.insur.operation.honor.dao;

import com.cfpamf.ms.insur.operation.base.dao.CommonMapper;
import com.cfpamf.ms.insur.operation.honor.dto.HonorMetricConfigurationList;
import com.cfpamf.ms.insur.operation.honor.po.HonorsMetricConfigurationPo;
import com.cfpamf.ms.insur.operation.honor.query.HonorMetricConfigurationQuery;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;


/**
 * <AUTHOR>
 */
@Mapper
public interface HonorMetricConfigurationMapper extends CommonMapper<HonorsMetricConfigurationPo> {

    /**
     * 获取荣誉规则指标配置
     * @param query 查询条件
     * @return HonorMetricConfigurationList
     */
    List<HonorMetricConfigurationList> list(HonorMetricConfigurationQuery query);
}
