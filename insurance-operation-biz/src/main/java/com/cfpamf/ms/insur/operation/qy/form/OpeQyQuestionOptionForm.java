
package com.cfpamf.ms.insur.operation.qy.form;

import com.cfpamf.ms.insur.operation.base.entity.BaseNoUserEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import javax.persistence.Table;
import javax.validation.constraints.NotNull;

/**
 * Created by zhengjing  on 2022-08-05 15:21:46
 *
 * <AUTHOR>
 */
@ApiModel("企业微信问题目选项表单")
@Table(name = "ope_qy_question_option")
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class OpeQyQuestionOptionForm extends BaseNoUserEntity {

    /**
     * 字段名称 问题id
     */
    @NotNull(message = "问题id不能为空")
    @ApiModelProperty(value = "问题id", required = true)
    Long questionId;
    /**
     * 字段名称 选项值
     */
    @NotNull(message = "选项值不能为空")
    @ApiModelProperty(value = "选项值", required = true)
    String optionValue;
    /**
     * 字段名称 参数
     */
    @NotNull(message = "参数不能为空")
    @ApiModelProperty(value = "参数", required = true)
    OpeQyQuestionOptionParamForm params;

    public static void main(String[] args) {

    }
}

