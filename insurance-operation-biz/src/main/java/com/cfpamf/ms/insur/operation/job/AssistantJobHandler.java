package com.cfpamf.ms.insur.operation.job;

import com.cfpamf.ms.insur.operation.assistant.dao.safespg.AdsInsuranceBchMarketingProgressDfpMapper;
import com.cfpamf.ms.insur.operation.assistant.entity.safespg.AdsInsuranceBchMarketingProgressDfp;
import com.cfpamf.ms.insur.operation.assistant.service.AssistantTargetEstimateService;
import com.cfpamf.ms.insur.operation.assistant.service.CalcContext;
import com.cfpamf.ms.insur.operation.base.constant.BaseConstants;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.util.List;

@Slf4j
@Component
public class AssistantJob<PERSON>andler {
    @Autowired
    AssistantTargetEstimateService assistantTargetEstimateService;
    @Autowired
    AdsInsuranceBchMarketingProgressDfpMapper adsInsuranceBchMarketingProgressDfpMapper;

    @XxlJob("insurance-operation-assistant-target-calc-job")
    public void execute() throws InterruptedException {
        log.info("insurance-operation-assistant-target-calc-job 开始执行！");
        LocalDate yesterday = LocalDate.now().minusDays(1);
        String yesterdayPt = yesterday.format(BaseConstants.FMT_DATE);
        List<AdsInsuranceBchMarketingProgressDfp> bchList = null;
        Integer tryCount = 1;
        do {
            bchList = adsInsuranceBchMarketingProgressDfpMapper.selectAllBch(yesterdayPt);
            if (CollectionUtils.isEmpty(bchList)){
                Thread.sleep(1000L *tryCount * tryCount);
            }else{
                break;
            }
            tryCount++;
        } while (tryCount < 10);

        if (CollectionUtils.isEmpty(bchList)){
            log.warn(yesterdayPt + " 指标不存在，请检查！");
            return;
        }
        bchList.stream().forEach(bch -> {
            CalcContext context = new CalcContext();
            context.setBchCode(bch.getBchCode());
            assistantTargetEstimateService.calcTargetEstimate(context);
        });
        log.info("insurance-operation-assistant-target-calc-job 执行完成！");
    }
}
