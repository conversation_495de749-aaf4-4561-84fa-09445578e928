package com.cfpamf.ms.insur.operation.prospectus.service;

import com.cfpamf.ms.insur.operation.prospectus.form.ProspectusForm;
import com.cfpamf.ms.insur.operation.prospectus.form.ProspectusSearchForm;
import com.cfpamf.ms.insur.operation.prospectus.vo.ProspectusPersonStatisticsVo;
import com.cfpamf.ms.insur.operation.prospectus.vo.ProspectusVo;
import com.github.pagehelper.PageInfo;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2021/5/25 17:21
 */
@Service
public interface ProspectusService {
    /**
     * 制作计划书
     *
     * @param prospectusForm
     * @return
     */

    Long createProspectus(ProspectusForm prospectusForm);

    /**
     * 预览计划书
     *
     * @param prospectusId
     * @return
     */
    ProspectusVo getProspectusDetail(Long prospectusId);

    /**
     * 搜索计划书
     *
     * @param prospectusSearchForm
     * @return
     */
    PageInfo<ProspectusVo> search(ProspectusSearchForm prospectusSearchForm);

    /**
     * 个人计划书统计
     *
     * @return
     */
    ProspectusPersonStatisticsVo statisticsPerson();

    /**
     * 修改计划书
     *
     * @param prospectusId
     * @param prospectusForm
     */
    void updateProspectus(Long prospectusId, ProspectusForm prospectusForm);

    /**
     * 删除计划书
     *
     * @param prospectusId
     */
    void delete(Long prospectusId);


}
