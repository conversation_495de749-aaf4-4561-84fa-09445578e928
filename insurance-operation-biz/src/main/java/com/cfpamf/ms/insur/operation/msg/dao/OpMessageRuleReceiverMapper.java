package com.cfpamf.ms.insur.operation.msg.dao;

import com.cfpamf.ms.insur.operation.base.dao.CommonMapper;
import com.cfpamf.ms.insur.operation.msg.pojo.po.OpMessageRuleReceiver;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.ibatis.annotations.Mapper;
import tk.mybatis.mapper.common.IdsMapper;
import tk.mybatis.mapper.entity.Example;

import java.util.Collections;
import java.util.List;

/**
 * 消息接受者配置
 *
 * <AUTHOR> 2021/7/7 13:45
 */
@Mapper
public interface OpMessageRuleReceiverMapper extends CommonMapper<OpMessageRuleReceiver>, IdsMapper<OpMessageRuleReceiver> {

    /**
     * 根据规则信息查询配置
     *
     * @param ruleId
     * @return
     */
    default List<OpMessageRuleReceiver> selectByRuleId(Long ruleId) {

        final OpMessageRuleReceiver query = new OpMessageRuleReceiver();
        query.setMessageRuleId(ruleId);
        return select(query);
    }

    /**
     * 查询列表
     * @param ruleIds 规则id列表
     * @return
     */
    default List<OpMessageRuleReceiver> selectByRuleIds(List<Long> ruleIds) {

        if (CollectionUtils.isEmpty(ruleIds)) {
            return Collections.emptyList();
        }
        Example example = new Example(OpMessageRuleReceiver.class);
        example.createCriteria().andIn("messageRuleId", ruleIds);
        return selectByExample(example);
    }

    /**
     * 修改审核状态
     * @param id
     * @param auditState
     * @return
     */
    default int updateAuditState(Long id,Integer auditState) {
        Example example = new Example(OpMessageRuleReceiver.class);
        example.createCriteria().andEqualTo("messageRuleId", id);
        OpMessageRuleReceiver receiver = new OpMessageRuleReceiver();
        receiver.setAuditState(auditState);
        return updateByExampleSelective(receiver,example);

    }
}
