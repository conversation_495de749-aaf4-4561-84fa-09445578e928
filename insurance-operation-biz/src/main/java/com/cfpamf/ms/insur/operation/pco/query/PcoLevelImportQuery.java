package com.cfpamf.ms.insur.operation.pco.query;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;

/**
 * <AUTHOR>
 */
@Data
public class PcoLevelImportQuery {
    /**
     * 页码
     */
    @ApiModelProperty(value = "页码 从1开始")
    private int page;

    /**
     * 每页条数
     */
    @ApiModelProperty(value = "每页条数")
    private int size = 20;

    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间")
    private LocalDate startDate;

    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间")
    private LocalDate endDate;
}
