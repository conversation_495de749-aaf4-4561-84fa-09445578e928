package com.cfpamf.ms.insur.operation.customer.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @Date 2023/04/19 15:37
 * @Version 1.0
 */
@AllArgsConstructor
@Getter
public enum EnumNonRenewalReason {

    /**
     * 不续原因 1:客户未续贷,2:已在其他处投保,3:产品没优势,4:客户认为保险没有用,5:保费涨价了,6:其他
     */
    LOAN("1","客户未续贷"),
    INSURE("2","已在其他处投保"),
    NO_ADVANTAGE("3","产品没优势"),
    useless("4","客户认为保险没有用"),
    RISE("5","保费涨价了"),
    OTHER("6","其他"),
    LOAN_FIRST_7("7","借新还旧客户"),
    LOAN_FIRST_8("8","未能成功说服客户购买");

    String code;
    String desc;
    public static String dict(String code) {
        if(Objects.isNull(code)){
            return "";
        }
        return Stream.of(values())
                .filter(em -> em.getCode().equals(code))
                .findFirst().map(EnumNonRenewalReason::getDesc)
                .orElse("");
    }
}
