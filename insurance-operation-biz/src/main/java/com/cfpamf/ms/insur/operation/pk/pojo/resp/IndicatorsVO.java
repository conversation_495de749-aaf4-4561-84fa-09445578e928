package com.cfpamf.ms.insur.operation.pk.pojo.resp;

import com.cfpamf.ms.insur.operation.pk.enums.EnumIndicatorType;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class IndicatorsVO {

    public IndicatorsVO(EnumIndicatorType type, BigDecimal value, boolean isNear3) {
        this(type.getCode(), isNear3 ? type.getNear3Name() : type.getName(), type.getUnit(), value);
    }

    @ApiModelProperty(value = "指标类型")
    private String type;

    private String typeName;

    @ApiModelProperty("指标单位")
    private String unit;

    @ApiModelProperty(value = "指标值")
    private BigDecimal value;

    public BigDecimal getValue() {
        if (Objects.isNull(value)) {
            return BigDecimal.ZERO;
        }
        return value.setScale(1, RoundingMode.HALF_UP);
    }
}