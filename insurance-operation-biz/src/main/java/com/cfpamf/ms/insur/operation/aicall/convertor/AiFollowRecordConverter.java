package com.cfpamf.ms.insur.operation.aicall.convertor;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.cfpamf.ms.insur.operation.aicall.entity.AiFollowRecordPo;
import com.cfpamf.ms.insur.operation.aicall.event.AiCallBackEvent;
import com.cfpamf.ms.insur.operation.phoenix.pojo.domain.PhoenixEmpTodoStat;
import com.cfpamf.ms.insur.operation.phoenix.pojo.po.PhoenixEmpTodo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;


/**
 * <AUTHOR>
 */
@Mapper(imports = {JSONObject.class, JSONArray.class, JSON.class})
public interface AiFollowRecordConverter {
    AiFollowRecordConverter INS = Mappers.getMapper(AiFollowRecordConverter.class);


    /**
     * DTO转PO
     *
     * @param event
     * @param todo
     * @return
     */
    @Mappings({
            @Mapping(target = "idNumber", source = "todo.targetId"),
            @Mapping(target = "taskType", source = "event.taskType"),
            @Mapping(target = "outSourceId", source = "event.outSourceId"),
            @Mapping(target = "phone", source = "event.mobile"),
            @Mapping(target = "intent", source = "event.intent"),
            @Mapping(target = "voiceAddress", source = "event.contactUUID"),
            @Mapping(target = "startTime", source = "event.startTime"),
            @Mapping(target = "endType", source = "event.endType"),
            @Mapping(target = "durationTimeLen", source = "event.durationTimeLen"),
            @Mapping(target = "ringingTimeLen", source = "event.ringingTimeLen"),
            @Mapping(target = "talkingTimeLen", source = "event.talkingTimeLen"),
            @Mapping(target = "recordJson", expression = "java(com.alibaba.fastjson.JSONObject.toJSONString(event.getRecords()))"),
            @Mapping(target = "endTypeReason", source = "event.endTypeReason"),
            @Mapping(target = "sessionId", source = "event.taskType"),
    })
    AiFollowRecordPo initFollowRecordPo(AiCallBackEvent event, PhoenixEmpTodoStat todo);
}
