package com.cfpamf.ms.insur.operation.prospectus.controller;

import com.cfpamf.ms.insur.operation.base.annotaions.ResponseDecorated;
import com.cfpamf.ms.insur.operation.prospectus.api.ProspectusTemplateApi;
import com.cfpamf.ms.insur.operation.prospectus.service.ProspectusTemplateService;
import com.cfpamf.ms.insur.operation.prospectus.vo.ProspectusTemplateConfigVo;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/5/19 14:54
 */
@RestController
@ResponseDecorated
public class ProspectusTemplateController implements ProspectusTemplateApi {

    private ProspectusTemplateService prospectusTemplateService;

    public ProspectusTemplateController(ProspectusTemplateService prospectusTemplateService) {
        this.prospectusTemplateService = prospectusTemplateService;
    }

    @Override
    public void updateTemplate(List<String> coverFileList) {
        prospectusTemplateService.updateTemplate(coverFileList);
    }

    @Override
    public ProspectusTemplateConfigVo getById() {
        return prospectusTemplateService.getById();
    }
}
