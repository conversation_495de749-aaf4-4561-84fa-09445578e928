package com.cfpamf.ms.insur.operation.base.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.task.AsyncTaskExecutor;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.RejectedExecutionHandler;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * 线程池配置
 *
 * <AUTHOR>
 * @date 2021/8/24 16:47
 */
@Configuration
public class ExecutorConfig {
    public static final String ASYNC_EXECUTOR_BEAN_BUS_TIME = "event-bus-time-executor";

    /**
     * 异步日志保存线程池
     *
     * @return
     */
    @Bean("log-Executor")
    public AsyncTaskExecutor logExecutor() {
        return buildAsyncTaskExecutor(2, 4, 20, "log-Executor", new ThreadPoolExecutor.DiscardPolicy());
    }

    /**
     * 异步补偿活动线程池
     *
     * @return
     */
    @Bean("activity-compensate-history-order")
    public AsyncTaskExecutor compensateHistoryOrderExecutor() {
        return buildAsyncTaskExecutor(2, 4, 20, "activity-compensate-history-order", new ThreadPoolExecutor.DiscardPolicy());
    }


    /**
     * 保存订单 更新为微信登录信息线程池
     *
     * @return
     */
    @Bean("event-bus-Executor")
    public AsyncTaskExecutor eventBusExecutor() {
        return buildAsyncTaskExecutor(1, 20, 40, "event-bus-Executor");
    }
    //多图推送异步线程池
    @Bean("msg-push-Executor")
    public AsyncTaskExecutor msgPushExecutor() {//需要支持至少20个群同时推送数据。
        return buildAsyncTaskExecutor(32, 64, 64, "msg-push-Executor");
    }


    //coze调用线程池
    @Bean("coze-workflow-Executor")
    public AsyncTaskExecutor cozeWorkflowExecutor() {
        return buildAsyncTaskExecutor(28, 56, 64, "coze-workflow-Executor");
    }

    /**
     * 构建异步线程池
     *
     * @param poolSize
     * @param maxSize
     * @param capacity
     * @param threadName
     * @return
     */
    private AsyncTaskExecutor buildAsyncTaskExecutor(int poolSize, int maxSize, int capacity, String threadName) {
        return buildAsyncTaskExecutor(poolSize, maxSize, capacity, threadName, new ThreadPoolExecutor.CallerRunsPolicy());
    }

    /**
     * 构建异步线程池
     *
     * @param poolSize
     * @param maxSize
     * @param capacity
     * @param threadName
     * @return
     */
    private AsyncTaskExecutor buildAsyncTaskExecutor(int poolSize, int maxSize, int capacity, String threadName, RejectedExecutionHandler
            rejectedExecutionHandler) {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(poolSize);
        executor.setMaxPoolSize(maxSize);
        executor.setQueueCapacity(capacity);
        executor.setKeepAliveSeconds(60);
        executor.setThreadNamePrefix(threadName);
        executor.setRejectedExecutionHandler(rejectedExecutionHandler);
        return executor;
    }
}
