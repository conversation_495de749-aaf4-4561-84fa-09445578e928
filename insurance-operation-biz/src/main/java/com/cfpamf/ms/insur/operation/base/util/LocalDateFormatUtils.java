package com.cfpamf.ms.insur.operation.base.util;

import com.cfpamf.common.ms.exception.MSException;
import com.cfpamf.common.ms.util.DateUtil;
import lombok.experimental.UtilityClass;

import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Calendar;
import java.util.Date;

/**
 * <AUTHOR> 2022/7/5 14:19
 */
@UtilityClass
public class LocalDateFormatUtils {
    public static DateTimeFormatter DEFAULT_FMT = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    public static String fmtDefault(LocalDateTime dateTime) {
        return DEFAULT_FMT.format(dateTime);
    }

    /**
     * 相隔天数（相反就是负数）
     *
     * @param smdate 开始时间
     * @param bdate  结束时间
     * @return
     * @throws MSException
     */
    public static long daysBetween(Date smdate, Date bdate) {
        SimpleDateFormat sdf = new SimpleDateFormat(DateUtil.CN_YEAR_MONTH_DAY_FORMAT);
        try {
            smdate = sdf.parse(sdf.format(smdate));
            bdate = sdf.parse(sdf.format(bdate));
        } catch (Exception e) {
            throw new MSException("0000", "时间转换错误" + smdate + "#" + bdate, e);
        }

        Calendar cal = Calendar.getInstance();
        cal.setTime(smdate);
        long time1 = cal.getTimeInMillis();
        cal.setTime(bdate);
        long time2 = cal.getTimeInMillis();
        return (time2 - time1) / (1000 * 3600 * 24);
    }

}

