package com.cfpamf.ms.insur.operation.base.annotaions;

import com.github.pagehelper.PageInfo;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.lang.reflect.Modifier;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Stream;

/**
 * 手机号  身份证脱敏
 *
 * <AUTHOR>
 **/
public class DataMaskUtil {
    private static Logger log = LoggerFactory.getLogger(DataMaskUtil.class);

    private static Set<String> basicType = new HashSet<>();

    private static String CFPAMF_CLASS = "com.cfpamf.ms.insur.*";

    static {
        basicType.add("java.lang.String");
        basicType.add("java.math.BigDecimal");

        basicType.add("java.lang.Integer");
        basicType.add("java.lang.Long");
        basicType.add("java.lang.Float");
        basicType.add("java.lang.Double");
        basicType.add("java.lang.Character");
        basicType.add("java.lang.Boolean");
        basicType.add("int");
        basicType.add("long");
        basicType.add("float");
        basicType.add("double");
        basicType.add("char");
        basicType.add("boolean");
    }

    /**
     * 隐藏手机号正则
     */
    private final static String MASK_MOBILE_REGEX = "(\\d{3})\\d{4}(\\d{4})";

    /**
     * 隐藏邮箱正则
     */
    private final static String MASK_EMAIL_REGEX = "(\\w)(\\w+)(@\\w+)";

    /**
     * 隐藏身份证正则
     */
    private final static String MASK_ID_CARD_REGEX = "(\\d)(\\w+)(\\w{2})";

    /**
     * 隐藏车牌号正则
     */
    private final static String MASK_PLATE_NUM_REGEX = "(\\w{2})(\\w+)(\\w{2})";
    /**
     * 隐藏发动机号正则
     */
    private final static String MASK_ENGINE_NUM_REGEX = "(\\w{2})(\\w+)(\\w{2})";
    /**
     * 隐藏车架号正则
     */
    private final static String MASK_VIN_REGEX = "(\\w{2})(\\w+)(\\w{2})";

    /**
     * 隐藏手机号
     *
     * @param mobile
     * @return
     */
    public static String maskMobile(String mobile) {
        if (StringUtils.isEmpty(mobile) || (mobile.length() != 11)) {
            return mobile;
        }
        return safeReplaceAll(mobile, MASK_MOBILE_REGEX, "$1****$2");
    }

    /**
     * 隐藏邮箱信息
     *
     * @param email
     * @return
     */
    public static String maskEmail(String email) {
        if (StringUtils.isEmpty(email)) {
            return email;
        }
        return safeReplaceAll(email, MASK_EMAIL_REGEX, "$1***$3");
    }

    /**
     * 隐藏身份证号码
     *
     * @param id
     * @return
     */
    public static String maskIdCardNo(String id) {
        if (StringUtils.isEmpty(id) || (id.length() < 8)) {
            return id;
        }

        return id.replaceAll(MASK_ID_CARD_REGEX, "$1*********$3");

    }

    /**
     * 隐藏车牌号
     *
     * @param plateNum
     * @return
     */
    public static String maskPlateNum(String plateNum) {
        if (StringUtils.isEmpty(plateNum) || (plateNum.length() < 5)) {
            return plateNum;
        }
        return safeReplaceAll(plateNum, MASK_PLATE_NUM_REGEX, "$1***$3");
    }

    /**
     * 隐藏发动机号
     *
     * @param engineNum
     * @return
     */
    public static String maskEngineNum(String engineNum) {
        if (StringUtils.isEmpty(engineNum)) {
            return engineNum;
        }
        return safeReplaceAll(engineNum, MASK_ENGINE_NUM_REGEX, "$1****$3");
    }

    /**
     * 隐藏车架号
     *
     * @param vin
     * @return
     */
    public static String maskVin(String vin) {
        if (StringUtils.isEmpty(vin)) {
            return vin;
        }
        return safeReplaceAll(vin, MASK_VIN_REGEX, "$1*************$3");
    }

    public static String maskAddress(String address) {
        if (StringUtils.isEmpty(address)) {
            return address;
        }
        return "******";
    }

    public static String maskBank(String bankNo) {
        if (StringUtils.isEmpty(bankNo) || bankNo.length() < 5) {
            return bankNo;
        }
        String reg = "\\d+(\\d{4})";
        return bankNo.replaceAll(reg, "********$1");
    }

    public static String maskHouse(String houseNo) {
        if (StringUtils.isEmpty(houseNo) || houseNo.length() < 3) {
            return houseNo;
        }
        String reg = "(\\w{1})\\w+(\\w{2})";
        return houseNo.replaceAll(reg, "$1******$2");
    }

    public static String maskArea(String area) {
        if (StringUtils.isEmpty(area)) {
            return area;
        }
        String areg = "^(\\S+省)(\\S+市)?(\\S+区|\\S+县)?\\S+$";
        if (area.matches(areg)) {
            return area.replaceAll(areg, "$1$2$3******");
        }
        return "******";
    }


    /**
     * 隐藏姓名姓氏
     *
     * @param name
     * @return
     */
    public static String maskFullName(String name) {
        if (StringUtils.isEmpty(name)) {
            return name;
        }
        return "*" + name.substring(1);
    }

    public static <T> List<T> maskList(List<T> datas) {
        datas.forEach(a -> DataMaskUtil.maskObject(a, null));
        return datas;
    }

    /**
     * 对实体列表脱敏
     *
     * @param datas
     * @param <T>
     * @return
     */
    @SuppressWarnings("uncheck")
    public static <T> List<T> maskList(List<T> datas, String scene) {
        datas.forEach(a -> DataMaskUtil.maskObject(a, scene));
        return datas;
    }

    public static <T> Collection<T> maskCollection(Collection datas, String scene) {
        datas.forEach(a -> DataMaskUtil.maskObject(a, scene));
        return datas;
    }

    /**
     * 对数据属性脱敏
     *
     * @param data
     * @param <T>
     * @return
     */
    public static <T> T mask(T data, String scene) {
        if (Objects.nonNull(data)) {
            if (data instanceof Collection) {
                DataMaskUtil.maskCollection((Collection) data, scene);
            } else if (data instanceof PageInfo) {
                DataMaskUtil.maskCollection(((PageInfo) data).getList(), scene);
            } else {
                DataMaskUtil.maskObject(data, scene);
            }
        }
        return data;
    }

    public static <T> T maskObject(T data) {
        return maskObject(data, null);
    }

    /**
     * 对单实体脱敏
     *
     * @param data
     * @param <T>
     * @return
     */
    public static <T> T maskObject(T data, String scene) {
        Class clazz = data.getClass();
        List<Class> clazzs = new ArrayList<>();
        clazzs.add(clazz);
        /**
         * !!!存在一些bug!!!
         * 1.譬如将一个对象包装成<PageInfo>对象，可能会解析不出来
         *   如果需要包装，尽量自己定义一个轻量级的包装类
         *   或者自行处理此类特殊情况
         */
        while (clazz != Object.class) {
            clazz = clazz.getSuperclass();
            if (cfpamfType(clazz)) {
                clazzs.add(clazz);
            }
        }
        try {
            for (Class entry : clazzs) {
                Field[] fs = entry.getDeclaredFields();
                for (Field f : fs) {
                    if (ignoreField(f)) {
                        continue;
                    }
                    f.setAccessible(true);
                    Object o = f.get(data);
                    if (o == null) {
                        continue;
                    }
                    Class<?> filedType = f.getType();
                    /**
                     * 针对[String]脱敏
                     */
                    if (!isBasicType(filedType)) {
                        mask(o, scene);
                    } else {
                        Mask mask = f.getAnnotation(Mask.class);
                        if (mask != null) {
                            String ignore = mask.ignore();
                            if (StringUtils.isNotBlank(ignore) && ignore.equals(scene)) {
                                continue;
                            } else {
                                String r = mask(mask, o);
                                if (r != null) {
                                    f.set(data, r);
                                }
                            }
                        }
                    }
                }
            }
        } catch (IllegalAccessException e) {
            log.warn("数据脱敏失败", e);
        }
        return data;
    }

    private static String mask(Mask mask, Object o) {
        String data = String.valueOf(o);
        if (mask.dataType() == Mask.DataType.MOBILE) {
            return DataMaskUtil.maskMobile(data);
        } else if (mask.dataType() == Mask.DataType.EMAIL) {
            return DataMaskUtil.maskEmail(data);
        } else if (mask.dataType() == Mask.DataType.ID_CARD) {
            return DataMaskUtil.maskIdCardNo(data);
        } else if (mask.dataType() == Mask.DataType.PLATE_NUM) {
            return DataMaskUtil.maskPlateNum(data);
        } else if (mask.dataType() == Mask.DataType.ENGINE_NUM) {
            return DataMaskUtil.maskEngineNum(data);
        } else if (mask.dataType() == Mask.DataType.VIN) {
            return DataMaskUtil.maskVin(data);
        } else if (mask.dataType() == Mask.DataType.ADDRESS) {
            return DataMaskUtil.maskAddress(data);
        } else if (mask.dataType() == Mask.DataType.BANK_NO) {
            return DataMaskUtil.maskBank(data);
        } else if (mask.dataType() == Mask.DataType.HOUSE_NO) {
            return DataMaskUtil.maskHouse(data);
        } else if (mask.dataType() == Mask.DataType.AREA) {
            return DataMaskUtil.maskArea(data);
        }
        return data;
    }

    public static boolean isBasicType(Class<?> filedType) {
        String name = filedType.getName();
        return basicType.contains(name);
    }

    public static boolean ignoreField(Field field) {
        if (Modifier.isStatic(field.getModifiers())) {
            return true;
        }
        if (Modifier.isFinal(field.getModifiers())) {
            return true;
        }
        return false;
    }

    /**
     * 判断是否自定义的类，其他类不需要解析
     *
     * @param clazz
     * @return
     */
    public static boolean cfpamfType(Class clazz) {
        String name = clazz.getName();
        return name.matches(CFPAMF_CLASS);
    }

    /**
     * 对实体列表脱敏
     *
     * @param datas
     * @param <T>
     * @return
     */
    @SuppressWarnings("uncheck")
    public static <T> List<T> maskList2(List<T> datas) {
        datas.forEach(DataMaskUtil::maskObject2);
        return datas;
    }

    /**
     * 对单实体脱敏
     *
     * @param data
     * @param <T>
     * @return
     */
    @SuppressWarnings("unchecked")
    public static <T> T maskObject2(T data) {
        Class clazz0 = data.getClass();
        List<Class> clazzs = new ArrayList<>();
        clazzs.add(clazz0);
        while (clazz0.getSuperclass() != Object.class) {
            clazz0 = clazz0.getSuperclass();
            clazzs.add(clazz0);
        }
        clazzs.forEach(clazz -> Stream.of(clazz.getDeclaredFields()).forEach(f -> {

            if (f.getType() != String.class && f.getType() != Integer.class && f.getType() != Boolean.class && f.getType() != BigDecimal.class
                    && f.getType() != Date.class) {
                String fName = f.getName();
                System.out.println(fName);
                try {
                    String methodName = fName.substring(0, 1).toUpperCase() + fName.substring(1);
                    Method getMethod = clazz.getMethod("get" + methodName, null);
                    Method setMethod = clazz.getMethod("set" + methodName, f.getType());
                    if (f.getType() == List.class) {
                        if (getMethod.invoke(data, null) != null) {
                            setMethod.invoke(data, maskList2((List) getMethod.invoke(data, null)));
                        }
                    } else {
                        if (getMethod.invoke(data, null) != null) {
                            setMethod.invoke(data, maskObject2(getMethod.invoke(data, null)));
                        }
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            } else {
                Mask mask = f.getAnnotation(Mask.class);
                if (mask != null) {
                    String fName = f.getName();
                    try {
                        String methodName = fName.substring(0, 1).toUpperCase() + fName.substring(1);
                        Method getMethod = clazz.getMethod("get" + methodName, null);
                        Method setMethod = clazz.getMethod("set" + methodName, String.class);
                        StringFunction sf = null;
                        if (mask.dataType() == Mask.DataType.MOBILE) {
                            sf = DataMaskUtil::maskMobile;
                        } else if (mask.dataType() == Mask.DataType.EMAIL) {
                            sf = DataMaskUtil::maskEmail;
                        } else if (mask.dataType() == Mask.DataType.ID_CARD) {
                            sf = DataMaskUtil::maskIdCardNo;
                        } else if (mask.dataType() == Mask.DataType.PLATE_NUM) {
                            sf = DataMaskUtil::maskPlateNum;
                        } else if (mask.dataType() == Mask.DataType.ENGINE_NUM) {
                            sf = DataMaskUtil::maskEngineNum;
                        } else if (mask.dataType() == Mask.DataType.VIN) {
                            sf = DataMaskUtil::maskVin;
                        } else if (mask.dataType() == Mask.DataType.BANK_NO) {
                            sf = DataMaskUtil::maskVin;
                        } else if (mask.dataType() == Mask.DataType.AREA) {
                            sf = DataMaskUtil::maskArea;
                        } else if (mask.dataType() == Mask.DataType.HOUSE_NO) {
                            sf = DataMaskUtil::maskHouse;
                        } else if (mask.dataType() == Mask.DataType.ADDRESS) {
                            sf = DataMaskUtil::maskAddress;
                        }
                        if (sf != null) {
                            setMethod.invoke(data, sf.apply(getMethod.invoke(data, null).toString()));
                        }
                    } catch (Exception e) {
                        log.warn("数据脱敏失败", e);
                    }
                }
            }
        }));
        return data;
    }

    /**
     * 数据被传递至应用程序并作为正则表达式使用。可能导致线程过度使用 CPU 资源，从而导致拒绝服务攻击。
     * 使用线程池 + Future, 限定执行时间, 并捕获异常.
     *
     * @param input
     * @param regex
     * @param replacement
     * @return
     */
    public static String safeReplaceAll(String input, String regex, String replacement) {
        return input.replaceAll(regex, replacement);
//        AsyncTaskExecutor executor = SpringFactoryUtil.getBean("regex-Executor", AsyncTaskExecutor.class);
//        Future<String> result = executor.submit(() -> input.replaceAll(regex, replacement));
//        try {
//            return result.get(1, TimeUnit.SECONDS);
//        } catch (Exception e) {
//            return null;
//        }
    }
}
