package com.cfpamf.ms.insur.operation.msg.enums;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.experimental.FieldDefaults;

@Getter
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public enum ImageNatureEnum {

    /**
     * 图片  模板内容必须是html格式
     */
    DYNAMICS("dynamics", "动态图"),
    STATIC("static", "静态图")
    ;

    String code;

    String desc;

    public boolean isMe(String code) {
        return this.code.equals(code);
    }
}
