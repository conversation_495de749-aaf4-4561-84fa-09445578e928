package com.cfpamf.ms.insur.operation.customer.service;

import com.alibaba.fastjson.JSON;
import com.cfpamf.ms.insur.operation.base.constant.BaseConstants;
import com.cfpamf.ms.insur.operation.base.constant.EnumProductAttr;
import com.cfpamf.ms.insur.operation.customer.convertor.CustomerInterruptionConverter;
import com.cfpamf.ms.insur.operation.customer.convertor.CustomerInterruptionPolicyConverter;
import com.cfpamf.ms.insur.operation.customer.dao.*;
import com.cfpamf.ms.insur.operation.customer.dto.LoanOrderCustomerDto;
import com.cfpamf.ms.insur.operation.customer.dto.OrderCustomerDto;
import com.cfpamf.ms.insur.operation.customer.dto.PolicyInfoNotifyMessage;
import com.cfpamf.ms.insur.operation.customer.po.CustomerInterruptionPo;
import com.cfpamf.ms.insur.operation.customer.po.CustomerInterruptionPolicyPo;
import com.cfpamf.ms.insur.operation.customer.po.CustomerLoanPo;
import com.cfpamf.ms.insur.operation.customer.po.CustomerLoanPolicyPo;
import com.cfpamf.ms.insur.operation.msg.dao.OrderMapper;
import com.cfpamf.ms.insur.operation.order.dto.SmOrderDto;
import com.cfpamf.ms.insur.operation.phoenix.enums.EnumTodoBizType;
import com.cfpamf.ms.insur.operation.phoenix.service.PhoenixEmpTodoService;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class CustomerRenewService {

    OrderMapper orderMapper;

    CustomerInterruptionMapper customerInterruptionMapper;

    CustomerInterruptionPolicyMapper customerInterruptionPolicyMapper;

    PhoenixEmpTodoService phoenixEmpTodoService;

    CustomerInterruptionFollowMapper customerInterruptionFollowMapper;

    CustomerLoanFollowMapper customerLoanFollowMapper;

    CustomerLoanMapper customerLoanMapper;

    CustomerLoanPolicyMapper customerLoanPolicyMapper;

    /**
     * 记录客户投保订单明细
     *
     * @param message
     */
    public void handleMessage(PolicyInfoNotifyMessage message) {
        try {
            SmOrderDto orderDto = orderMapper.getOrderInfoByOrderId(message.getFhOrderId());
            log.info("激活客户-订单信息：{}", JSON.toJSONString(orderDto));
            if (Objects.isNull(orderDto)) {
                return;
            }

            List<CustomerInterruptionPolicyPo> policyPos = new ArrayList<>();

            List<CustomerInterruptionPo> renewCustomers = new ArrayList<>();

            //投保人信息
            LoanOrderCustomerDto applicantCustomers = orderMapper.getApplicantLoanInfoByOrderId(orderDto.getFhOrderId());

            //被保人信息
            LoanOrderCustomerDto insuredCustomers = orderMapper.getLoanInfoByOrderId(orderDto.getFhOrderId());

            if (insuredCustomers != null) {
                //被保人转化逻辑处理
                insuredConvert(insuredCustomers, orderDto);
            }
            if(applicantCustomers != null && !Objects.equals(insuredCustomers.getIdNumber(), applicantCustomers.getIdNumber())) {
                //投保人转化逻辑处理
                insuredConvert(applicantCustomers, orderDto);
            }
            if (EnumProductAttr.PERSON.getCode().equals(orderDto.getProductAttrCode())) {
                //被保人信息
                List<OrderCustomerDto> insuredList = orderMapper.getInsurInfoByOrderId(orderDto.getFhOrderId());
                //个险客户激活逻辑处理
                insuredRenew(insuredList, orderDto, policyPos, renewCustomers, EnumProductAttr.PERSON.getCode());

            } else if (EnumProductAttr.GROUP.getCode().equals(orderDto.getProductAttrCode())
                    || EnumProductAttr.EMPLOYER.getCode().equals(orderDto.getProductAttrCode())) {
                //团险客户激活逻辑处理
                groupRenew(orderDto, policyPos, renewCustomers);
            }


            //修改客户激活状态
            if (CollectionUtils.isNotEmpty(renewCustomers)) {
                customerInterruptionMapper.updateByIds(renewCustomers);

                //新增一条已完成的跟进记录
                customerInterruptionFollowMapper.insertFollow(renewCustomers);

                phoenixEmpTodoService.finishTodo(EnumTodoBizType.INTERRUPTION, renewCustomers.stream().map(CustomerInterruptionPo::getIdNumber).collect(Collectors.toList()), "激活");
            }

            //记录激活保单
            if (CollectionUtils.isNotEmpty(policyPos)) {
                customerInterruptionPolicyMapper.insertListDuplicateUpdate(policyPos);
            }
        } catch (Exception e) {
            log.warn("统计激活保单明细失败，原因：{}，参数：{}", e, JSON.toJSONString(message));
        }
    }

    private void applicantConvert(LoanOrderCustomerDto orderCustomers, SmOrderDto orderDto) {


    }

    private void insuredConvert(LoanOrderCustomerDto orderCustomers, SmOrderDto orderDto) {
        //如果状态为未转化，更改状态+添加跟进记录+记录保单信息
        if (BaseConstants.CUSTOMER_UN_CONVERSION.equals(orderCustomers.getConversionState())) {
            customerLoanMapper.updateByIbNumber(orderCustomers.getIdNumber(), orderDto.getAccountTime()
                    , orderDto.getCustomerAdminId(),orderDto.getFhOrderId());
            CustomerLoanPolicyPo customerLoanPolicyPo = new CustomerLoanPolicyPo();
            buildLoanPolicy(customerLoanPolicyPo, orderCustomers, orderDto);
            customerLoanPolicyMapper.insert(customerLoanPolicyPo);
            customerLoanFollowMapper.insertFollow(orderCustomers.getCustomerId(), orderCustomers.getIdNumber(), orderDto.getAccountTime());
            phoenixEmpTodoService.finishTodo(EnumTodoBizType.LOAN, orderCustomers.getIdNumber(), "转化");
        }
        //如果状态为已转化且退保，更新保单信息?
    }

    private void buildLoanPolicy(CustomerLoanPolicyPo customerLoanPolicyPo, LoanOrderCustomerDto orderCustomers, SmOrderDto orderDto) {
        customerLoanPolicyPo.setIdNumber(orderCustomers.getIdNumber());
        customerLoanPolicyPo.setCustomerName(orderCustomers.getPersonName());
        customerLoanPolicyPo.setPolicyNo(orderCustomers.getPolicyNo());
        customerLoanPolicyPo.setFhOrderId(orderCustomers.getFhOrderId());
        customerLoanPolicyPo.setPolicyState(orderCustomers.getAppStatus());
        customerLoanPolicyPo.setCancelFlag(1);
        customerLoanPolicyPo.setAmount(orderDto.getTotalAmount());
        customerLoanPolicyPo.setTotalAmount(orderDto.getTotalAmount());
        if (EnumProductAttr.PERSON.getCode().equals(orderDto.getProductAttrCode())) {
            customerLoanPolicyPo.setInsur(0);
        } else {
            customerLoanPolicyPo.setInsur(1);
        }
        customerLoanPolicyPo.setCustomerAdmin(orderCustomers.getCustomerAdmin());
        customerLoanPolicyPo.setConvertEmp(orderDto.getCustomerAdminId());
        customerLoanPolicyPo.setConvertTime(orderDto.getAccountTime());
    }

    /**
     * 团险客户激活逻辑处理
     *
     * @param orderDto
     */
    private void groupRenew(SmOrderDto orderDto, List<CustomerInterruptionPolicyPo> policyPos, List<CustomerInterruptionPo> renewCustomers) {

        //投保人激活逻辑处理
        applicantRenew(orderDto, policyPos, renewCustomers);

        //被保人信息
        List<OrderCustomerDto> insuredList = orderMapper.getItemInfoByOrderId(orderDto.getFhOrderId());
        //被保人激活逻辑处理
        insuredRenew(insuredList, orderDto, policyPos, renewCustomers, EnumProductAttr.GROUP.getCode());
    }

    /**
     * 团险投保人激活逻辑处理
     * 当前订单为激活投保人订单时（返回True），团险被保人激活客户也需累计激活保单
     *
     * @param orderDto
     */
    private void applicantRenew(SmOrderDto orderDto, List<CustomerInterruptionPolicyPo> policyPos, List<CustomerInterruptionPo> renewCustomers) {
        //投保人信息
        OrderCustomerDto interruptionDto = orderMapper.getApplicantInfoByOrderId(orderDto.getFhOrderId());

        if (!Objects.isNull(interruptionDto)) {
            //断保状态客户更新为已激活
            CustomerInterruptionPo po = CustomerInterruptionConverter.INS.insuredDtoToPo(interruptionDto);
            if (BaseConstants.CUSTOMER_UN_RENEW.equals(interruptionDto.getRenewState())) {
                po.setRenewTime(orderDto.getAccountTime());
                po.setRenewEmp(orderDto.getCustomerAdminId());
                po.setRenewState(BaseConstants.CUSTOMER_RENEW);
                renewCustomers.add(po);
            }

            interruptionDto.setFhOrderId(orderDto.getFhOrderId().contains("_") ? orderDto.getFhOrderId().substring(0, orderDto.getFhOrderId().indexOf("_")) : orderDto.getFhOrderId());
            //初始化客户保单信息
            CustomerInterruptionPolicyPo policyPo = customerInterruptionPolicyMapper
                    .getByOrderIdAndAppIdNumber(interruptionDto);
            log.info("原始激活保单：{}", JSON.toJSONString(policyPo));

            //更新原始激活保单的总保费
            if (!Objects.isNull(policyPo)) {
                //订单总金额计算（退保扣减，承保累加）
                if (BaseConstants.POLICY_STATUS_CANCEL_SUCCESS.equals(interruptionDto.getAppStatus())) {
                    //考虑多次批改退保金额的情况，扣除退保金额时，先加上原始退保金额再扣除
                    BigDecimal amount = customerInterruptionPolicyMapper.getAmountByPolicyNo(interruptionDto.getPolicyNo());
                    BigDecimal totalAmount = policyPo.getTotalAmount().add(amount).subtract(orderDto.getTotalAmount());
                    policyPo.setTotalAmount(totalAmount);
                    policyPo.setCancelTime(totalAmount.compareTo(BigDecimal.ZERO) == 0 ? orderDto.getAccountTime() : null);
                    policyPo.setCancelFlag(totalAmount.compareTo(BigDecimal.ZERO) == 0 ? 0 : null);
                } else if (interruptionDto.getType() == 1) {
                    policyPo.setTotalAmount(policyPo.getTotalAmount().add(orderDto.getTotalAmount()));
                }
                policyPos.add(policyPo);
            }

            CustomerInterruptionPolicyPo newPolicyPo = CustomerInterruptionPolicyConverter.INS.dtoToPo(interruptionDto, orderDto, po);
            log.info("激活保单信息：{}", JSON.toJSONString(newPolicyPo));
            newPolicyPo.setAmount(orderDto.getTotalAmount());
            newPolicyPo.setInsur(1);
            //初次统计且保单状态不为退保的，写入订单总保费
            newPolicyPo.setTotalAmount(!Objects.isNull(policyPo) ? policyPo.getTotalAmount() : orderDto.getTotalAmount());
            if (interruptionDto.getType() == 1 || BaseConstants.POLICY_STATUS_CANCEL_SUCCESS.equals(interruptionDto.getAppStatus())) {
                newPolicyPo.setTotalAmount(BigDecimal.ZERO);
            }
            policyPos.add(newPolicyPo);
        }
    }

    /**
     * 被保客户激活逻辑处理
     */
    private void insuredRenew(List<OrderCustomerDto> insuredList, SmOrderDto orderDto, List<CustomerInterruptionPolicyPo> policyPos, List<CustomerInterruptionPo> renewCustomers, String orderType) {
        if (CollectionUtils.isNotEmpty(insuredList)) {
            for (OrderCustomerDto interruptionDto : insuredList) {
                //断保状态客户更新为已激活
                CustomerInterruptionPo po = CustomerInterruptionConverter.INS.insuredDtoToPo(interruptionDto);
                if (BaseConstants.CUSTOMER_UN_RENEW.equals(interruptionDto.getRenewState())) {
                    po.setRenewTime(orderDto.getAccountTime());
                    po.setRenewEmp(orderDto.getCustomerAdminId());
                    po.setRenewState(BaseConstants.CUSTOMER_RENEW);
                    renewCustomers.add(po);
                }

                //团险被保人不计激活保单
                if (!EnumProductAttr.GROUP.getCode().equals(orderType)) {
                    //初始化客户保单信息
                    CustomerInterruptionPolicyPo oldPolicyPo = customerInterruptionPolicyMapper
                            .getByOrderIdAndAppIdNumber(interruptionDto);
                    log.info("原始激活保单：{}", JSON.toJSONString(oldPolicyPo));
                    //初始化客户保单信息
                    CustomerInterruptionPolicyPo policyPo = CustomerInterruptionPolicyConverter.INS.dtoToPo(interruptionDto, orderDto, po);
                    log.info("激活保单信息：{}", JSON.toJSONString(policyPo));
                    policyPo.setAmount(orderDto.getTotalAmount());
                    policyPo.setInsur(0);
                    //更新原始激活保单的总保费
                    if (!Objects.isNull(oldPolicyPo) && BaseConstants.POLICY_STATUS_CANCEL_SUCCESS.equals(interruptionDto.getAppStatus())) {
                        //订单总金额计算（退保扣减，承保累加）
                        oldPolicyPo.setTotalAmount(oldPolicyPo.getAmount().subtract(orderDto.getTotalAmount()));
                        oldPolicyPo.setCancelFlag(0);
                        oldPolicyPo.setCancelTime(orderDto.getAccountTime());
                        policyPos.add(oldPolicyPo);
                    }

                    policyPo.setTotalAmount(orderDto.getTotalAmount());
                    if (BaseConstants.POLICY_STATUS_CANCEL_SUCCESS.equals(interruptionDto.getAppStatus())) {
                        //保单状态为退保的，总保费为0
                        policyPo.setTotalAmount(BigDecimal.ZERO);
                    }

                    policyPos.add(policyPo);
                }
            }
        }
    }

    public Integer getLoanCustomerRemark(String customerId) {
        return customerLoanFollowMapper.getLoanCustomerRemark(customerId);
    }
}
