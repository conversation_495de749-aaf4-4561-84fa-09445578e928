package com.cfpamf.ms.insur.operation.retrospective.query;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import javax.validation.constraints.NotBlank;

/**
 * @Description 客户跟进记录查询对象
 * @Date 19:04 2024/3/19
 * @Param 
 * <AUTHOR>
 * @return 
 **/
@Data
@ApiModel("客户跟进记录查询对象")
@FieldDefaults(level = AccessLevel.PRIVATE)
public class QueryFollowListIncludeAIRetrospectiveQuery {

    @ApiModelProperty("员工工号")
    @NotBlank(message = "员工工号不能为空")
    private String empCode;

    @ApiModelProperty("查询日期")
    @NotBlank(message = "查询日期不能为空")
    private String date;
}
