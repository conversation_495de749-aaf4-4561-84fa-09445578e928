package com.cfpamf.ms.insur.operation.reward.service.impl;

import com.cfpamf.ms.insur.operation.activity.entity.SmActivityReward;
import com.cfpamf.ms.insur.operation.activity.vo.SystemActivityProgrammeVo;
import com.cfpamf.ms.insur.operation.base.constant.CharConstants;
import com.cfpamf.ms.insur.operation.base.constant.RedEnvelopeConstants;
import com.cfpamf.ms.insur.operation.reward.redenvelope.dao.SystemRedEnvelopeMapper;
import com.cfpamf.ms.insur.operation.reward.redenvelope.po.entity.SystemRedEnvelope;
import com.cfpamf.ms.insur.operation.reward.service.RewardService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.NotImplementedException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/1/6 15:46
 */
@Service(value = "RED_ENVELOPE")
public class RedEnvelopeRewardServiceImpl implements RewardService {

    @Autowired
    SystemRedEnvelopeMapper systemRedEnvelopeMapper;

    /**
     * 默认的数据类型为保单号
     *
     * @param smActivityRewardList
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void awardByListener(List<SmActivityReward> smActivityRewardList) {
        if (CollectionUtils.isEmpty(smActivityRewardList)) {
            return;
        }
        List<SystemRedEnvelope> systemRedEnvelopeList = smActivityRewardList.stream()
                .map(
                        smActivityReward -> {
                            SystemRedEnvelope systemRedEnvelope = new SystemRedEnvelope();
                            systemRedEnvelope.setUserId(smActivityReward.getDataId());
                            systemRedEnvelope.setSendState(RedEnvelopeConstants.NOT_OPEN_STATE);
                            //TODO 此块链接设计逻辑勉强 不能很好的兼容其他红包奖励维度
                            String uuid = smActivityReward.getUuid();
                            if (StringUtils.isNotBlank(uuid)) {
                                String[] split = uuid.split(CharConstants.LINE);
                                systemRedEnvelope.setDataId(split[1]);
                                systemRedEnvelope.setDataType(split[0]);
                            }
                            systemRedEnvelope.setEnabledFlag(0);
                            systemRedEnvelope.setSaId(smActivityReward.getSaId());
                            systemRedEnvelope.setCreateTime(LocalDateTime.now());
                            systemRedEnvelope.setUpdateTime(LocalDateTime.now());
                            return systemRedEnvelope;
                        }
                ).collect(Collectors.toList());

        //通过活动id分组
        Map<Long, List<SystemRedEnvelope>> systemRedEnvelopeMap = systemRedEnvelopeList.stream()
                .collect(Collectors.groupingBy(SystemRedEnvelope::getSaId));
        for (Long saId : systemRedEnvelopeMap.keySet()) {
            List<SystemRedEnvelope> redEnvelopeList = systemRedEnvelopeMap.get(saId);
            List<String> dataIdList = redEnvelopeList.stream()
                    .map(SystemRedEnvelope::getDataId)
                    .distinct()
                    .collect(Collectors.toList());
            List<SystemRedEnvelope> sendSystemRedEnvelopeList = systemRedEnvelopeMapper.getByDateTypeAndDataIdList(dataIdList, saId);
            if (CollectionUtils.isNotEmpty(sendSystemRedEnvelopeList)) {
                List<String> sendDataIdList = sendSystemRedEnvelopeList.stream()
                        .map(SystemRedEnvelope::getDataId)
                        .collect(Collectors.toList());
                //过滤掉已发送的红包集合
                redEnvelopeList = redEnvelopeList
                        .stream()
                        .filter(systemRedEnvelope -> !sendDataIdList.contains(systemRedEnvelope.getDataId()))
                        .collect(Collectors.toList());
            }
            if (CollectionUtils.isNotEmpty(redEnvelopeList)) {
                systemRedEnvelopeMapper.insertList(redEnvelopeList);
            }

        }
    }

    @Override
    public void awardByJob(SystemActivityProgrammeVo systemActivityProgrammeVo, Map<String, BigDecimal> dataIdMap) {
        throw new NotImplementedException("", "红包奖励方式暂不该任务触发方式发放奖励");
    }

    @Override
    public void awardByJobV2(SystemActivityProgrammeVo systemActivityProgrammeVo, Map<String, BigDecimal> dataIdMap, Map<String, BigDecimal> dataIdAmountMap) {

    }

    @Override
    public void takeBackReward(Long saId, boolean b) {
        throw new NotImplementedException("", "红包奖励方式暂不支持收回奖励");
    }
}
