package com.cfpamf.ms.insur.operation.honor.dto;

import com.cfpamf.ms.insur.operation.base.po.BasePO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import javax.persistence.Table;
import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class HonorCalculateRule {

    Integer id;

    @ApiModelProperty(name="年度")
    String year;

    @ApiModelProperty("荣誉称号")
    String honorName;

    @ApiModelProperty(name="评选对象 area:区域，bch：分支，emp：个人")
    String level;

    @ApiModelProperty(name="评选开始时间")
    LocalDate startTime;

    @ApiModelProperty(name="评选结束时间")
    LocalDate endTime;

    @ApiModelProperty(name="评选周期 year:年度，quarter：季度，month：月")
    String period;

    @ApiModelProperty(name="脚本规则code")
    String ruleCode;

    @ApiModelProperty(name="评选指标名称（按勾选顺序传递）")
    String honorNormName;

    @ApiModelProperty(name="评选指标编码（按勾选顺序传递）")
    String honorNormCode;

    @ApiModelProperty(name="评选数量")
    Integer selectionNumber;

    @ApiModelProperty(name="评选方式 people:人工，system：系统")
    String selectionType;

    @ApiModelProperty(name="状态 0:未开始，1：评选中，2：已结束")
    Integer state;

    String honorType;

    /**
     * 区域名称/机构名称/员工工号
     */
    List<String> names;

    /**
     * 区域编码/机构编码/员工工号
     */
    @ApiModelProperty(name="区域编码/机构编码/员工工号")
    List<String> codes;

    @ApiModelProperty("全局视角")
    Boolean isGlobalView;

}
