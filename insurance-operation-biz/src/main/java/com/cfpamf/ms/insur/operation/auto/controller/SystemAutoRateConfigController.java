package com.cfpamf.ms.insur.operation.auto.controller;

import com.cfpamf.ms.insur.operation.auto.dto.SystemAutoRateConfigDetailDto;
import com.cfpamf.ms.insur.operation.auto.dto.SystemAutoRateConfigDto;
import com.cfpamf.ms.insur.operation.auto.dto.SystemAutoRateConfigListDto;
import com.cfpamf.ms.insur.operation.auto.query.SystemAutoRateConfigQuery;
import com.cfpamf.ms.insur.operation.auto.service.SystemAutoRateConfigHistoryService;
import com.cfpamf.ms.insur.operation.auto.service.SystemAutoRateConfigService;
import com.cfpamf.ms.insur.operation.base.annotaions.MaskMethod;
import com.cfpamf.ms.insur.operation.base.annotaions.ResponseDecorated;
import com.cfpamf.ms.insur.operation.base.constant.BaseConstants;
import com.cfpamf.ms.insur.operation.log.annotaions.SystemLog;
import com.cfpamf.ms.insur.operation.pco.vo.PcoLevelList;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.*;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * 断保客户管理
 *
 * <AUTHOR>
 * @date 2023/4/19 16:04
 */
@Slf4j
@Api(value = "车险推广费配置管理", tags = {"车险推广费配置管理"})
@RequestMapping(value = {BaseConstants.ADMIN_VERSION + "/auto/rate/config"})
@ResponseDecorated
@RestController
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class SystemAutoRateConfigController {
    SystemAutoRateConfigService systemAutoRateConfigService;

    SystemAutoRateConfigHistoryService systemAutoRateConfigHistoryService;

    /**
     * 查询车险费率配置分页列表
     *
     * @param query 查询参数
     * @return SystemAutoRateConfigDto
     */
    @ApiOperation(value = "车险费率配置分页列表")
    @PostMapping("/back/list")
    @MaskMethod
    public PageInfo<SystemAutoRateConfigListDto> getConfigListByPage(@RequestBody SystemAutoRateConfigQuery query) {
        return systemAutoRateConfigService.getConfigListByPage(query);
    }

    /**
     * 车险费率配置保存
     *
     * @param dto 详情
     */
    @ApiOperation(value = "车险费率配置保存")
    @PostMapping("/saveOrUpdate")
    @MaskMethod
    public void save(@RequestBody SystemAutoRateConfigDto dto) {
        systemAutoRateConfigService.saveOrUpdate(dto);
    }

    /**
     * 获取车险费率配置历史版本
     *
     * @param productId 产品主键
     * @return SystemAutoRateConfigListDto
     */
    @GetMapping("/history/{productId:\\d+}")
    @ApiOperation(value = "获取车险费率配置历史版本")
    List<SystemAutoRateConfigListDto> getRenewalConfigVersionByProductId(@PathVariable("productId") Integer productId) {
        return systemAutoRateConfigHistoryService.getConfigVersionByProductId(productId);
    }

    /**
     * 获取车险费率配置历史版本配置详情
     *
     * @param productId 产品主键
     * @param version   版本号
     * @return
     */
    @GetMapping("/history/detail")
    @ApiOperation(value = "获取车险费率历史版本配置详情")
    SystemAutoRateConfigDto getHistoryDetail(@RequestParam(required = true) Integer productId,@RequestParam(required = true)Integer version) {
        return systemAutoRateConfigHistoryService.getHistoryDetail(productId,version);
    }

    /**
     * 获取车险费率配置历史版本配置详情
     *
     * @param productId 产品主键
     * @return
     */
    @GetMapping("/detail")
    @ApiOperation(value = "获取车险费率配置详情")
    SystemAutoRateConfigDto getDetail(@RequestParam(required = true) Integer productId) {
        return systemAutoRateConfigService.getDetail(productId);
    }

    @ApiOperation(value = "用户推广费政策查看页-公众号")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "openId", value = "微信openId", required = false, dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "authorization", value = "authorization", required = true, dataType = "string", paramType = "query")
    })
    @GetMapping("/getConfigListByUser")
    public List<SystemAutoRateConfigDetailDto> getConfigListByUser(@RequestParam(required = false) String openId, @RequestParam String authorization) {
        return systemAutoRateConfigService.getConfigListByUser(openId, authorization);
    }

    @ApiOperation(value = "区域推广政策详情页（获取产品+区域配置文件）-公众号")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "productId", value = "产品主键", required = true, dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "regionName", value = "区域名称", required = true, dataType = "string", paramType = "query")
    })
    @GetMapping("/getFiles")
    public SystemAutoRateConfigDetailDto getFiles(@RequestParam Integer productId, @RequestParam String regionName) {
        return systemAutoRateConfigService.getFiles(productId, regionName);
    }
}
