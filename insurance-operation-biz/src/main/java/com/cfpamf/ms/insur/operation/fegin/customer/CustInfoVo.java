package com.cfpamf.ms.insur.operation.fegin.customer;

import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR> 2022/12/5 13:44
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class CustInfoVo {
    @ApiModelProperty(value = "客户id")
    private Long id;

    /**
     * 客户编号
     */
    @ApiModelProperty(value = "客户编号")
    private String custNo;

    /**
     * 客户姓名
     */
    @ApiModelProperty(value = "客户姓名")
    private String custName;

    /**
     * 客户年龄
     */
    @ApiModelProperty(value = "客户年龄")
    private Integer age;

    /**
     * 证件类型 01-身份证
     */
    @ApiModelProperty(value = "证件类型 01-身份证")
    private String idType;

    /**
     * 证件号码
     */
    @ApiModelProperty(value = "证件号码")
    private String idNo;

    /**
     * 手机号
     */
    @ApiModelProperty(value = "手机号")
    private String mobile;

    /**
     * 密码
     */
    @ApiModelProperty(value = "密码")
    private String password;

    /**
     * 是否实名认证 01-已实名认证  02-未实名认证
     */
    @ApiModelProperty(value = "是否实名认证 01-已实名认证  02-未实名认证")
    private String isReal;

    /**
     * 状态 01-有效  02-无效
     */
    @ApiModelProperty(value = "状态 01-有效  02-无效")
    private String sts;

    /**
     * 客户余额
     */
    @ApiModelProperty(value = "客户余额")
    private BigDecimal balance;

    /**
     * 是否有贷款业务可用银行卡
     */
    @ApiModelProperty(value = "是否有贷款业务可用银行卡")
    private Boolean hasBankCard4Loan;

    /**
     * 评估等级
     */
    @ApiModelProperty(value = "评估等级")
    private String assessLevel;


    /**
     * 是否关闭消息推送 默认false
     */
    @ApiModelProperty(value = "是否关闭消息推送 默认false")
    private Boolean isClosed = false;

    /**
     * 账户余额
     */
    @ApiModelProperty(value = "账户余额")
    private String accountBalance;

    /**
     * 决策编号
     */
    @ApiModelProperty(value = "决策编号")
    private String decisionNo;

    /**
     * 乡信余额
     */
    @ApiModelProperty(value = "乡信余额")
    private BigDecimal investBalance;

    /**
     * 乡信余额
     */
    @ApiModelProperty(value = "总余额")
    private BigDecimal totalBalance;

    /**
     * 虚拟账号
     */
    @ApiModelProperty(value = "虚拟账号")
    private String virtualAcctNo;

    @ApiModelProperty(value = "是否完成人脸识别")
    private Boolean finishFaceDetect = Boolean.FALSE;

    @ApiModelProperty(value = "是否完成授信评估")
    private Boolean finishCreditApply = Boolean.FALSE;

    @ApiModelProperty(value = "是否完成capp注册 TRUE-注册 FALSE-未注册  暂未实现")
    private Boolean finishRegister = Boolean.FALSE;

    @ApiModelProperty(value = "是否完成capp实名认证 TRUE-完成 FALSE-未完成")
    private Boolean finishCerfify = Boolean.FALSE;

    @ApiModelProperty(value = "注册状态 1-未注册 2-已注册 3-已注销")
    private String registerStatus = "1";

    @ApiModelProperty(value = "创建时间")
    private String createTime;

    @ApiModelProperty(value = "更新时间")
    private String updateTime;



    @ApiModelProperty(value = "微信openid")
    private String weChatOpenid;

    @ApiModelProperty(value = "昵称")
    private String nickName;

    @ApiModelProperty(value = "头像链接")
    private String avatarUrl;

    @ApiModelProperty(value = "微信关注状态 1:有效(已关注) 0:无效(取消关注)")
    private String weChatSts;

    @ApiModelProperty(value = "用户编号")
    private String userNo;

    @ApiModelProperty(value = "最近一次登陆时间")
    private String loginTime;

    @ApiModelProperty(value = "注册时间")
    private String registerTime;

    @ApiModelProperty(value = "极速贷免责 0-非免责 1-免责")
    private String exemptionFlag = "0";

    @ApiModelProperty(value = "用呗免责 0-非免责 1-免责")
    private String ybExemptionFlag = "0";

    @ApiModelProperty(value = "客户所有用户编号")
    private List<String> allUserNos;

    @ApiModelProperty(value = "是否可自动代扣")
    private Boolean unionpayWithhold;

}
