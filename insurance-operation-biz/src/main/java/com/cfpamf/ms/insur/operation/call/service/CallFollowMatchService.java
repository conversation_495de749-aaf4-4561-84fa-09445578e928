package com.cfpamf.ms.insur.operation.call.service;

import com.cfpamf.ms.insur.operation.call.dao.CallRecordMapper;
import com.cfpamf.ms.insur.operation.call.po.CallRecordPo;
import com.cfpamf.ms.insur.operation.phoenix.dao.SmOrderRenewalFollowMapper;
import com.cfpamf.ms.insur.operation.phoenix.enums.EnumRenewalIntention;
import com.cfpamf.ms.insur.operation.phoenix.pojo.dto.SmOrderRenewalFollowDto;
import com.cfpamf.ms.insur.operation.phoenix.pojo.po.SmOrderRenewalFollowPo;
import com.cfpamf.ms.insur.operation.phoenix.service.SmOrderRenewalFollowService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 呼叫跟进记录匹配服务
 *
 * <AUTHOR>
 * @date 2025-07-22
 */
public interface CallFollowMatchService {

    /**
     * 匹配呼叫记录与跟进记录
     * 查找已获取录音但未匹配跟进记录的呼叫记录，为其创建或匹配跟进记录
     */
    void matchCallWithFollowRecords() ;

    /**
     * 匹配单条呼叫记录
     */
    void matchSingleCallRecord(CallRecordPo callRecord) ;


}
