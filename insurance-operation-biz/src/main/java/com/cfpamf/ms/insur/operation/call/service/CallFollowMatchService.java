package com.cfpamf.ms.insur.operation.call.service;

import com.cfpamf.ms.insur.operation.call.dao.CallRecordMapper;
import com.cfpamf.ms.insur.operation.call.entity.CallRecordPo;
import com.cfpamf.ms.insur.operation.phoenix.dao.SmOrderRenewalFollowMapper;
import com.cfpamf.ms.insur.operation.phoenix.enums.EnumRenewalIntention;
import com.cfpamf.ms.insur.operation.phoenix.pojo.dto.SmOrderRenewalFollowDto;
import com.cfpamf.ms.insur.operation.phoenix.pojo.po.SmOrderRenewalFollowPo;
import com.cfpamf.ms.insur.operation.phoenix.service.SmOrderRenewalFollowService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 呼叫跟进记录匹配服务
 *
 * <AUTHOR>
 * @date 2025-07-22
 */
@Service
@Slf4j
@AllArgsConstructor
public class CallFollowMatchService {

    private final CallRecordMapper callRecordMapper;
    private final SmOrderRenewalFollowMapper smOrderRenewalFollowMapper;
    private final SmOrderRenewalFollowService smOrderRenewalFollowService;

    /**
     * 匹配呼叫记录与跟进记录
     * 查找已获取录音但未匹配跟进记录的呼叫记录，为其创建或匹配跟进记录
     */
    public void matchCallWithFollowRecords() {
        log.info("开始匹配呼叫记录与跟进记录");
        
        // 查询已获取录音但未匹配跟进记录的记录，限制每次处理50条
        List<CallRecordPo> unMatchedCalls = callRecordMapper.selectUnMatchedFollowCalls(50);
        
        if (CollectionUtils.isEmpty(unMatchedCalls)) {
            log.info("没有需要匹配的呼叫记录");
            return;
        }

        log.info("找到{}条需要匹配的呼叫记录", unMatchedCalls.size());

        for (CallRecordPo callRecord : unMatchedCalls) {
            try {
                matchSingleCallRecord(callRecord);
            } catch (Exception e) {
                log.error("匹配呼叫记录失败，记录ID：{}，错误信息：{}", callRecord.getId(), e.getMessage(), e);
            }
        }

        log.info("呼叫记录匹配完成");
    }

    /**
     * 匹配单条呼叫记录
     */
    @Transactional(rollbackFor = Exception.class)
    public void matchSingleCallRecord(CallRecordPo callRecord) {
        log.info("开始匹配呼叫记录，ID：{}，保单号：{}", callRecord.getId(), callRecord.getPolicyNo());

        try {
            // 查找呼叫时间之后的跟进记录
            List<SmOrderRenewalFollowPo> followRecords = findFollowRecordsAfterCall(callRecord);

            SmOrderRenewalFollowPo matchedFollow = null;

            if (!CollectionUtils.isEmpty(followRecords)) {
                // 找到最近的一条跟进记录
                matchedFollow = followRecords.get(0);
                log.info("找到匹配的跟进记录，呼叫记录ID：{}，跟进记录ID：{}", callRecord.getId(), matchedFollow.getId());
            } else {
                // 没有找到跟进记录，创建新的跟进记录
                matchedFollow = createNewFollowRecord(callRecord);
                log.info("创建新的跟进记录，呼叫记录ID：{}，跟进记录ID：{}", callRecord.getId(), matchedFollow.getId());
            }

            // 更新呼叫记录的匹配信息
            if (matchedFollow != null) {
                callRecordMapper.updateFollowMatchInfo(callRecord.getId(), 
                                                     Long.valueOf(matchedFollow.getId()), 1);
                log.info("呼叫记录匹配完成，呼叫记录ID：{}，跟进记录ID：{}", callRecord.getId(), matchedFollow.getId());
            }

        } catch (Exception e) {
            log.error("匹配呼叫记录异常，记录ID：{}，错误：{}", callRecord.getId(), e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 查找呼叫时间之后的跟进记录
     */
    private List<SmOrderRenewalFollowPo> findFollowRecordsAfterCall(CallRecordPo callRecord) {
        // 查询该保单在呼叫时间之后的跟进记录
        try {
            // 使用tk.mybatis的Example查询
            tk.mybatis.mapper.entity.Example example = new tk.mybatis.mapper.entity.Example(SmOrderRenewalFollowPo.class);
            tk.mybatis.mapper.entity.Example.Criteria criteria = example.createCriteria();

            criteria.andEqualTo("policyNo", callRecord.getPolicyNo());
            criteria.andEqualTo("enabledFlag", 0);

            // 如果有呼叫开始时间，查询呼叫时间之后的跟进记录
            if (callRecord.getBeginCallTime() != null) {
                criteria.andGreaterThanOrEqualTo("followTime", callRecord.getBeginCallTime());
            }

            // 按跟进时间升序排列，取最近的记录
            example.setOrderByClause("follow_time ASC");

            return smOrderRenewalFollowMapper.selectByExample(example);
        } catch (Exception e) {
            log.error("查询跟进记录失败，保单号：{}，错误：{}", callRecord.getPolicyNo(), e.getMessage(), e);
            return null;
        }
    }

    /**
     * 创建新的跟进记录
     */
    private SmOrderRenewalFollowPo createNewFollowRecord(CallRecordPo callRecord) {
        SmOrderRenewalFollowDto followDto = new SmOrderRenewalFollowDto();
        followDto.setPolicyNo(callRecord.getPolicyNo());
        followDto.setServiceMode("电话跟进"); // 服务方式设为电话跟进
        followDto.setIntention(EnumRenewalIntention.CONSIDER.getCode()); // 意向设为考虑
        followDto.setRemark("系统自动创建：基于一键呼叫记录生成的跟进记录");
        followDto.setFollowPeople(callRecord.getEmployeeId());
        followDto.setFollowTime(callRecord.getBeginCallTime() != null ? 
                               callRecord.getBeginCallTime() : LocalDateTime.now());

        // 调用跟进服务保存记录
        smOrderRenewalFollowService.saveRenewalFollowRecord(followDto);

        // 查询刚创建的记录
        SmOrderRenewalFollowPo createdRecord = new SmOrderRenewalFollowPo();
        BeanUtils.copyProperties(followDto, createdRecord);
        
        return createdRecord;
    }
}
