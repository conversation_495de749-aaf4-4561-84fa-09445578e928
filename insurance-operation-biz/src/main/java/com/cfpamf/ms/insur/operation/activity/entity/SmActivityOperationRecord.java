package com.cfpamf.ms.insur.operation.activity.entity;

import com.cfpamf.ms.insur.operation.base.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.OrderBy;
import javax.persistence.Table;

/**
 * 活动操作记录
 * <AUTHOR>
@ApiModel(value="活动操作记录")
@Data
@Table(name = "sm_activity_operation_record")
public class SmActivityOperationRecord {

    /**
     * 活动Id
     */
    @ApiModelProperty(value="活动Id")
    @Column(name = "sa_id")
    private Long saId;

    /**
     * 发布 RELEASE 暂停 SUSPEND 作废 ABOLISH 继续 CONTINUE
     */
    @ApiModelProperty(value="发布 RELEASE 暂停 SUSPEND 作废 ABOLISH 继续 CONTINUE")
    @Column(name = "operation_type")
    private String operationType;

    /**
     * 操作时间
     */
    @ApiModelProperty(value="操作时间")
    @Column(name = "operation_time")
    private LocalDateTime operationTime;

    /**
     * 操作人
     */
    @ApiModelProperty(value="操作人")
    @Column(name = "operator")
    private String operator;
    /**
     * 主键id
     */
    @Id
    @OrderBy("desc")
    protected Integer id;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private LocalDateTime createTime;

}