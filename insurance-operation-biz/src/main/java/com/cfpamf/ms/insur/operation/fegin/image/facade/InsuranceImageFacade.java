package com.cfpamf.ms.insur.operation.fegin.image.facade;

import com.cfpamf.ms.insur.operation.base.response.CommonResult;
import com.cfpamf.ms.insur.operation.fegin.image.vo.ImageUrl;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.Map;

/**
 * digital-finance-gateway.tsg.cfpamf.com
 * <AUTHOR> 2022/10/14 11:20
 */

@FeignClient(name = "insuranceImageFacade", url = "${insurance-image.url}", configuration = InsuranceImageConfiguration.class)
//@FeignClient(name = "insuranceImageFacade", url = "http://digital-finance-gateway.tsg.cfpamf.com/", configuration = InsuranceImageConfiguration.class)
public interface InsuranceImageFacade {

    @PostMapping("image")
    CommonResult<ImageUrl> image(@RequestBody Map<String, Object> paras);
}
