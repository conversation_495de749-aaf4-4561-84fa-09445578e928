package com.cfpamf.ms.insur.operation.fegin.hr.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class DingUserInfoVO {

    @ApiModelProperty(value = "工号")
    String employeeCode;

    @ApiModelProperty(value = "姓名")
    String employeeName;

    @ApiModelProperty(value = "钉钉UserId")
    String dingUserId;

    @ApiModelProperty(value = "unionId")
    String unionId;

}