package com.cfpamf.ms.insur.operation.fegin.wx.request;

import com.cfpamf.ms.insur.operation.fegin.oms.response.MarketingForwardGroupByDateDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class CustomerGroupVisitGroupQueryInput {

    @ApiModelProperty(value = "客户经理编码")
    private String manageCode;

    @ApiModelProperty(value = "素材编码")
    private String marketingCode;

    @ApiModelProperty(value = "页码")
    private Integer pageNum;

    @ApiModelProperty(value = "一页多少条数据")
    private Integer pageSize;
    @ApiModelProperty(value = "素材圈分享的返回列表")
    List<MarketingForwardGroupByDateDTO> marketingForwardGroupByDateDTOList;

}
