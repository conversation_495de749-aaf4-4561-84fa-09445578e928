package com.cfpamf.ms.insur.operation.planbook.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/12/8 11:27
 * @Version 1.0
 */
@Data
public class SmPlanRiskDutyVO {

    @ApiModelProperty(value = "计划id", required = true)
    Integer planId;

    @ApiModelProperty(value = "责任版本号", required = true)
    Integer dutyVersion;

    @ApiModelProperty(value = "险种ID", required = true)
    Integer riskId;

    @ApiModelProperty(value = "责任ID", required = true)
    Integer dutyId;

    @ApiModelProperty(value = "责任key", required = true)
    String dutyKey;

    @ApiModelProperty(value = "险种key", required = true)
    String riskKey;


    /**
     * 字段名称 是否豁免条款 0-否 1-是
     */
    @ApiModelProperty(value = "责任名称", required = true)
    String dutyName;
    /**
     * 字段名称 是否豁免条款 0-否 1-是
     */
    @ApiModelProperty(value = "责任编码", required = true)
    String dutyCode;

    @ApiModelProperty("责任详情")
    String dutyDetail;

    @ApiModelProperty("责任保额")
    List<SysRiskDutyAmountVO> sysRiskDutyAmountVOList;

    @ApiModelProperty("险种保额")
    List<SysRiskAmountVO> sysRiskAmountVOList;

    /**
     * 险种责任id
     */
    @ApiModelProperty("险种责任id")
    Integer sysRiskDutyId;

    /**
     * 责任保费类型 1-同基本保额 2-自定义
     */
    @ApiModelProperty("责任保费类型 1-同基本保额 2-自定义")
    Integer amountType;

    @ApiModelProperty("保额显示值")
    String amountText;

    String dutyParams;

    /**
     * 是否计入险种保额1-是，0-否
     */
    @ApiModelProperty(value = "是否计入险种保额1-是，0-否")
    private Integer includedRiskInsuredAmount;

    /**
     * 是否必选责任1-是，0-否
     */
    @ApiModelProperty(value = "是否必选责任1-是，0-否")
    private Integer mandatory;

    @ApiModelProperty("费率表类型")
    private Integer targetType;

    @Data
    @EqualsAndHashCode
    public static class PlanRiskKey {
        Integer planId;
        String riskKey;
    }

    public static PlanRiskKey toPlanRiskKey(SmPlanRiskDutyVO vo) {
        PlanRiskKey result = new PlanRiskKey();
        result.setRiskKey(vo.getRiskKey());
        result.setPlanId(vo.getPlanId());
        return result;
    }

}
