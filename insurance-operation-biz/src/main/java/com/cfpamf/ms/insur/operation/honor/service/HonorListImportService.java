package com.cfpamf.ms.insur.operation.honor.service;

import com.cfpamf.ms.insur.operation.honor.dao.HonorListImportMapper;
import com.cfpamf.ms.insur.operation.honor.dto.HonorListImportDTO;
import com.cfpamf.ms.insur.operation.honor.po.HonorListImportPo;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.time.LocalDateTime;


/**
 * <AUTHOR>
 */
@Service
@Slf4j
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class HonorListImportService {

    private HonorListImportMapper honorListImportMapper;

    /**
     * 插入荣誉清单导入明细
     * @param dto 荣誉清单导入明细
     * @param operator 操作人
     */
    @Transactional(rollbackFor = Exception.class)
    public void addImportRecord(HonorListImportDTO dto, String operator) throws IOException {
        HonorListImportPo res = new HonorListImportPo();
        res.setFileName(dto.getFileName());
        res.setFileUrl(dto.getFileUrl());
        res.setCreateTime(LocalDateTime.now());
        res.setCreateBy(operator);
        res.setHonorsConfigurationsId(dto.getHonorsConfigurationsId());
        honorListImportMapper.insertUseGeneratedKeys(res);
    }
}
