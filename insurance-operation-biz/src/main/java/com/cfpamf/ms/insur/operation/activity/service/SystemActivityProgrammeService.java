package com.cfpamf.ms.insur.operation.activity.service;

import com.cfpamf.ms.insur.operation.activity.enums.EnumActivityOperation;
import com.cfpamf.ms.insur.operation.activity.enums.EnumActivityState;
import com.cfpamf.ms.insur.operation.activity.form.SystemActivityConstProductForm;
import com.cfpamf.ms.insur.operation.activity.form.SystemActivityProgrammeForm;
import com.cfpamf.ms.insur.operation.activity.form.SystemActivitySearchForm;
import com.cfpamf.ms.insur.operation.activity.form.WxActivitySearchForm;
import com.cfpamf.ms.insur.operation.activity.vo.SystemActivityConstProductVO;
import com.cfpamf.ms.insur.operation.activity.vo.SystemActivityProgrammeVo;
import com.cfpamf.ms.insur.operation.activity.vo.WxActivitySimpleVo;
import com.github.pagehelper.PageInfo;
import org.springframework.scheduling.annotation.Async;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/7/6 17:03
 */
public interface SystemActivityProgrammeService {

    /**
     * 创建系统活动方案
     *
     * @param systemActivityProgrammeForm
     * @return
     */
    public Long create(SystemActivityProgrammeForm systemActivityProgrammeForm);

    /**
     * 搜索活动方案列表
     *
     * @param searchForm
     * @return
     */
    public PageInfo<SystemActivityProgrammeVo> search(SystemActivitySearchForm searchForm);

    /**
     * 查看活动方案详情
     *
     * @param id
     * @return
     */
    public SystemActivityProgrammeVo detail(Long id);

    /**
     * 编辑活动方案
     *
     * @param id
     * @param systemActivityProgrammeForm
     * @return
     */
    public void update(Long id, SystemActivityProgrammeForm systemActivityProgrammeForm);

    /**
     * 启用/禁用活动
     *
     * @param id
     * @param activityFlag
     * @return
     */
    public void updateActivityFlag(Long id, Integer activityFlag);

    /**
     * 搜索微信活动列表
     *
     * @param wxActivitySearchForm
     * @return
     */
    PageInfo<WxActivitySimpleVo> getWxActivityList(WxActivitySearchForm wxActivitySearchForm);

    /**
     * 搜索微信活动列表
     *
     * @param wxActivitySearchForm
     * @return
     */
    PageInfo<WxActivitySimpleVo> getWxActivityListWithoutOpenId(WxActivitySearchForm wxActivitySearchForm);
    /**
     * 获取微信活动详情
     *
     * @param id
     * @param openId
     * @param authorization
     * @return
     */
    SystemActivityProgrammeVo getWxActivityDetail(Long id, String openId, String authorization);

    /**
     * 获取微信活动详情(保险助手)
     *
     * @param id
     * @param authorization
     * @return SystemActivityProgrammeVo
     */
    SystemActivityProgrammeVo getWxHelperActivityDetail(Long id,String regionName, String authorization);



    /**
     * 补偿活动
     *
     * @param activityFlag
     * @param id
     */
    @Async("activity-compensate-history-order")
    public void compensateActivity(Long id, Integer activityFlag);


    /**
     * 更新活动状态
     *
     * @param id
     * @param activityOperation
     * @return
     */
    public void updateActivityState(Long id, EnumActivityOperation activityOperation);

    /**
     * 通过活动状态搜索活动列表
     * 活动状态:未发布-UNPUBLISHED 未开始-NOT_START 活动中-IN_ACTIVITY 活动结束-END 活动暂停-SUSPEND 活动作废-CANCEL
     *
     * @param activityState
     * @return
     */
    public List<SystemActivityProgrammeVo> getSystemActivityProgrammeByState(EnumActivityState activityState);

    List<SystemActivityProgrammeVo> getActivityByStateAndRelativeTime(SystemActivitySearchForm searchForm);

    /**
     * 通过活动Id查询活动配置信息
     *
     * @param id
     * @return
     */
    SystemActivityProgrammeVo getSystemActivityProgrammeById(Long id);

    /**
     * 查询时间范围内产品是否有活动配置
     * @param constProductForm
     * @return
     */
    List<SystemActivityConstProductVO> seachProductActivity(SystemActivityConstProductForm constProductForm);

    void copy(Long id,String activityPlatform);
}
