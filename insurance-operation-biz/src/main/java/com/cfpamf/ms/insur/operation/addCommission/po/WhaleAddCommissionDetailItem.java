package com.cfpamf.ms.insur.operation.addCommission.po;

import com.cfpamf.ms.insur.operation.base.entity.BaseNoUserEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * sm_add_commission_detail_item
 * <AUTHOR>
@Data
@Table(name = "whale_add_commission_detail_item")
public class WhaleAddCommissionDetailItem extends BaseNoUserEntity {


    /**
     * 订单id
     */
    @ApiModelProperty(value="批单号")
    private String endorsementNo;

    /**
     * 保单号
     */
    @ApiModelProperty(value="保单号")
    private String policyNo;

    /**
     * 商品编码
     */
    @ApiModelProperty(value="商品编码")
    private String sellProductCode;

    /**
     * 险种编码
     */
    @ApiModelProperty(value="险种编码")
    private String riskCode;

    /**
     * 期数
     */
    @ApiModelProperty(value="期数")
    private Integer termNum;
    /**
     * 期数
     */
    @ApiModelProperty(value="险种状态")
    private String productStatus;

    /**
     * 加佣类型 ACTIVTY-活动加佣
     */
    @ApiModelProperty(value="加佣类型 ACTIVTY-活动加佣")
    private String commissionType;

    /**
     * 加佣比例
     */
    @ApiModelProperty(value="加佣比例")
    private BigDecimal proportion;
    /**
     * 加佣比例
     */
    @ApiModelProperty(value="保费")
    private BigDecimal amount;
    @ApiModelProperty(value = "加佣金额")
    private BigDecimal addCommissionAmount;
    /**
     * 数据标识 如活动id
     */
    @ApiModelProperty(value="数据标识 如活动id")
    private String dataId;

    /**
     * 唯一标识，唯一索引字段值拼接而成 拼接字符|
     */
    @ApiModelProperty(value="唯一标识，唯一索引字段值拼接而成 拼接字符|")
    private String uuid;
    /**
     * 唯一标识，唯一索引字段值拼接而成 拼接字符|
     */
    @ApiModelProperty(value="数据汇总标识")
    private String dataIndex;
    @ApiModelProperty(value = "结算状态：0核算中，1待结算，2已结算，3作废")
    private Integer settlementState=0;
    @ApiModelProperty(value = "结算批次")
    private String batchNo;
    @ApiModelProperty(value = "被保人编码")
    private String insuredCode;

    @ApiModelProperty("受理状态")
    private Integer acceptanceState;
    /**
     * 活动名称
     */
    @ApiModelProperty(value = "活动名称")
    private String activityName;
    /**
     * 活动名称
     */
    @ApiModelProperty(value = "推送流水号")
    private String requestId;
    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private LocalDateTime createTime;
    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private LocalDateTime updateTime;
}