package com.cfpamf.ms.insur.operation.customer.dao;

import com.cfpamf.ms.insur.operation.base.dao.CommonMapper;
import com.cfpamf.ms.insur.operation.customer.po.CustomerLoanFirstPo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface CustomerLoanFirstMapper extends CommonMapper<CustomerLoanFirstPo> {
    /**
     * 根据信贷客户id变更A类客户状态
     * @param list
     */
    void updateByLoanCustId(@Param("list") List<CustomerLoanFirstPo> list);

    List<String> selectByLoanCustIds(@Param("list")List<CustomerLoanFirstPo> customerLoanFirstPoList);

    /**
     * 初始化A类客户表
     *
     * @return int
     */
    int insertFromDwd();
}
