package com.cfpamf.ms.insur.operation.base.helper;

import org.springframework.util.StringUtils;
import com.cfpamf.common.ms.exception.MSBizNormalException;
import com.cfpamf.common.ms.result.Result;
import com.cfpamf.ms.bms.facade.constants.BmsEnabledFlagEnum;
import com.cfpamf.ms.bms.facade.vo.DictionaryItemVO;
import com.cfpamf.ms.bms.facade.vo.DingTalkUserBmsVO;
import com.cfpamf.ms.bms.facade.vo.EmployeeDingTalkVO;
import com.cfpamf.ms.insur.operation.base.util.HttpRequestUtil;
import com.cfpamf.ms.insur.operation.base.util.LambdaUtils;
import com.cfpamf.ms.insur.operation.fegin.bms.enums.ExcptEnum;
import com.cfpamf.ms.insur.operation.fegin.bms.facede.BmsDictionaryFacade;
import com.cfpamf.ms.insur.operation.fegin.bms.facede.BmsDingTalkFacade;
import com.cfpamf.ms.insur.operation.fegin.bms.facede.BmsFacade;
import com.cfpamf.ms.insur.operation.fegin.bms.facede.BmsPostFacade;
import com.cfpamf.ms.insur.operation.fegin.bms.response.dto.BmsOrgDTO;
import com.cfpamf.ms.insur.operation.fegin.bms.response.vo.ModuleVO;
import com.cfpamf.ms.insur.operation.fegin.bms.response.vo.OrganizationBaseVO;
import com.cfpamf.ms.insur.operation.fegin.bms.response.vo.OrganizationVO;
import com.cfpamf.ms.insur.operation.fegin.bms.response.vo.UserDetailVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.function.Supplier;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/4/30 16:05
 */
@Slf4j
@Component
public class BmsHelper {
    /**
     * 保险系统在bms系统id
     */
    @Value("${bms.system.id}")
    private Integer systemId;
    public static final String ERROR_PREFIX = "BMS接口调用失败";
    /**
     * bms用户接口
     */
    @Autowired
    private BmsFacade bmsFacade;

    @Autowired
    BmsPostFacade postFacade;

    @Autowired
    BmsDingTalkFacade dingTalkFacade;
    @Autowired
    BmsDictionaryFacade bmsDictionaryFacade;

    public static Map<String, BmsOrgDTO> branchOrgMap = null;


    public Map<String, DingTalkUserBmsVO> listDingTalkUserDetailByMobiles(List<String> mobiles) {

        if (CollectionUtils.isEmpty(mobiles)) {
            return Collections.emptyMap();
         }

        List<DingTalkUserBmsVO> vos = callApi(() -> dingTalkFacade.listDingtalkUserDetailByMobiles(org.apache.commons.lang3.StringUtils.join(mobiles, ",")));
        return LambdaUtils.groupByAndToFirstMap(vos, DingTalkUserBmsVO::getMobile);
    }

    /**
     * 获取当前登录用户所有权限
     *
     * @return
     */
    public List<ModuleVO> getContextUserPermissions() {
        Result<List<ModuleVO>> result = bmsFacade.getAuthPermissions(getToken(), 3, null);
        throwExceptionIfFail(result);
        return result.getData();
    }

    /**
     * 获取当前登录用户所有权限
     *
     * @return
     */
    public List<ModuleVO> getContextUserPermissions(String authorization) {
        Result<List<ModuleVO>> result = bmsFacade.getAuthPermissions(authorization, 3, null);
        throwExceptionIfFail(result);
        return result.getData();
    }

    /**
     * 获取用户详情信息
     *
     * @return
     */
    public UserDetailVO getContextUserDetail() {
        return getUserDetailByToken(getToken());
    }

    /**
     * 获取jwt token
     *
     * @return
     */
    private String getToken() {
        String token = HttpRequestUtil.getToken();
        if (StringUtils.isEmpty(token)) {
            throw new MSBizNormalException(ExcptEnum.GATEWAY_ERROR_TOKEN_801009.getCode(), ExcptEnum.GATEWAY_ERROR_TOKEN_801009.getMsg());
        }
        return token;
    }

    /**
     * 获取用户详情信息
     *
     * @return
     */
    public UserDetailVO getUserDetailByToken(String token) {
        Result<UserDetailVO> result = bmsFacade.getContextUserDetailForSafes(token);
        throwExceptionIfFail(result);
        UserDetailVO userDetail = result.getData();
        if (userDetail.getOrgName() != null && !StringUtils.isEmpty(userDetail.getAreaOrgCode())) {
            userDetail.setIsHeadOrg(Boolean.TRUE);
        }
        return userDetail;
    }


    /**
     * 根据orgCode获取当前机构信息,如果当前机构是区域办公室/总部事业部则返回对应的分支机构树上的区域信息/分支事业部，其他不变
     *
     * @param orgCode
     * @return
     */
    public OrganizationBaseVO getBizOrg(String orgCode) {
        Result<OrganizationBaseVO> result = bmsFacade.getBizOrgBaseVoByOrgCode(orgCode);
        log.info("OrganizationBaseVO = {}", result.getData());
        throwExceptionIfFail(result);
        return result.getData();
    }

    /**
     * 如果接口错误跑出异常
     *
     * @param result
     */
    private void throwExceptionIfFail(Result result) {
        if (!result.isSuccess()) {
            throw new MSBizNormalException(result.getErrorCode(), ERROR_PREFIX + result.getErrorMsg());
        }
    }

    /**
     * @param hrParentId
     * @return
     */
    public List<OrganizationBaseVO> listBranchByHrParentId(Integer hrParentId) {
        Result<List<OrganizationBaseVO>> result = bmsFacade.listBranchesByHrParentId("", hrParentId);
        throwExceptionIfFail(result);
        return result.getData();
    }

    public List<String> listBranchCodesByHrParentId(Integer hrParentId) {
        List<OrganizationBaseVO> list = listBranchByHrParentId(hrParentId);
        return CollectionUtils.isEmpty(list) ? null : list.stream().map(OrganizationBaseVO::getOrgCode).collect(Collectors.toList());
    }

    /**
     * 校验机构是否是区域办公室
     *
     * @param hrOrgId
     * @return
     */
    public Boolean checkOrgIsAreaOffice(String hrOrgId) {
        if (hrOrgId == null) {
            return Boolean.FALSE;
        }
        Result<OrganizationVO> organizationVOResult = bmsFacade.getOrganizationByHrOrgId("", Integer.valueOf(hrOrgId));
        throwExceptionIfFail(organizationVOResult);
        log.info("hrOrgId={},orgId = {}", hrOrgId, organizationVOResult.getData().getOrgId());

        Result<Boolean> result = bmsFacade.checkOrgIsAreaOffice("", organizationVOResult.getData().getOrgId());
        log.info("result = {}", result.getData());
        throwExceptionIfFail(result);
        return result.getData();
    }


    /**
     * 取分支机构树对应的区域code
     *
     * @param orgCode
     * @return
     */
    public String getBranchOrgCode(String orgCode) {
        BmsOrgDTO dto = getBranchOrgByOrgCode(orgCode);
        return dto != null ? dto.getOrgCode() : null;
    }

    /**
     * 根据orgCode获取当前机构信息,如果当前机构是区域办公室/总部事业部则返回对应的分支机构树上的区域信息/分支事业部，其他不变
     *
     * @param orgCode
     * @return
     */
    public BmsOrgDTO getBranchOrgByOrgCode(String orgCode) {
        if (orgCode == null) {
            return null;
        }
        if (branchOrgMap == null) {
            branchOrgMap = new HashMap<>(10);
        }
        BmsOrgDTO dto = branchOrgMap.get(orgCode);
        if (dto == null) {
            OrganizationBaseVO organizationBaseVO = getBizOrg(orgCode);
            if (organizationBaseVO != null) {
                dto = new BmsOrgDTO();
                BeanUtils.copyProperties(organizationBaseVO, dto);
                branchOrgMap.put(orgCode, dto);
            }
        }
        return dto;
    }

    /**
     * 根据postName查询员工信息
     *
     * @param postName
     * @return
     */
    public EmployeeDingTalkVO listEmployeeDingUsersByPostName(String postName) {

        return callApi(() -> postFacade.listEmployeeDingUsersByPostName(Collections.singletonList(postName)));
    }



    private <T> T callApi(Supplier<Result<T>> supplier) {

        Result<T> result = supplier.get();
        throwExceptionIfFail(result);
        return result.getData();
    }

    /**
     * 通过字典编码获取字典明细码值
     *
     * @param
     * @return
     */
    public List<String> getValidDictionaryItemCodesByTypeCode(String typeCode) {
        List<DictionaryItemVO> items = getValidDictionaryItemsByTypeCode(typeCode);
        if (CollectionUtils.isEmpty(items)) {
            return Collections.emptyList();
        }
        return items.stream().map(DictionaryItemVO::getItemCode).collect(Collectors.toList());
    }

    /**
     * 获取bms字段配置
     *
     * @param typeCode
     * @return
     */
    public List<DictionaryItemVO> getValidDictionaryItemsByTypeCode(String typeCode) {
        Result<List<DictionaryItemVO>> response = bmsDictionaryFacade.getDictionaryItemsByTypeCode(null, systemId, typeCode);
        List<DictionaryItemVO> result = response.getData();
        if (result == null) {
            result = Collections.emptyList();
        }
        result = result.stream().filter(p -> BmsEnabledFlagEnum.Enabled.getValue().equals(p.getItemStatus())).collect(Collectors.toList());
        return result;
    }

    /**
     * 获取bms字典配置的字典描述
     *
     * @param typeCode
     * @param itemCode
     * @return
     */
    public String getDictionaryItemDesc(String typeCode, String itemCode) {
        List<DictionaryItemVO> dictionaryItemVOList = getValidDictionaryItemsByTypeCode(typeCode);
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(dictionaryItemVOList)) {
            return null;
        }
        return dictionaryItemVOList.stream()
                .filter(dictionaryItemVO -> Objects.equals(dictionaryItemVO.getItemCode(), itemCode))
                .findFirst()
                .map(DictionaryItemVO::getItemDesc).orElse(null);
    }

}
