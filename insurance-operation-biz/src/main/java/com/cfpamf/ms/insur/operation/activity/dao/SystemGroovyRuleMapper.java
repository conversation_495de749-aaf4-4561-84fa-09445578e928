package com.cfpamf.ms.insur.operation.activity.dao;

import com.cfpamf.ms.insur.operation.activity.entity.SystemGroovyRule;
import com.cfpamf.ms.insur.operation.base.dao.CommonMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface SystemGroovyRuleMapper extends CommonMapper<SystemGroovyRule> {
    /**
     * 通过类型获取系统规则
     *
     * @param type
     * @param code
     * @return
     */
    SystemGroovyRule getByTypeAndCode(@Param("type") String type, @Param("code") String code);


    /**
     * 查询某个类型的所有脚本
     *
     * @param type
     * @return
     */
    List<SystemGroovyRule> selectByType(@Param("type") String type);

    /**
     * 通过类型获取系统规则
     *
     * @param id
     * @return
     */
    SystemGroovyRule getById(Long id);
}
