package com.cfpamf.ms.insur.operation.promotion.controller;

import com.alibaba.fastjson.JSON;
import com.cfpamf.common.ms.exception.MSBizNormalException;
import com.cfpamf.common.ms.result.Result;
import com.cfpamf.ms.insur.operation.base.response.CommonResult;
import com.cfpamf.ms.insur.operation.fegin.oms.response.MarketingForwardGroupByDateDTO;
import com.cfpamf.ms.insur.operation.fegin.oms.response.MarketingForwardListQueryOutputDTO;
import com.cfpamf.ms.insur.operation.fegin.oms.response.OmsBaseResponse;
import com.cfpamf.ms.insur.operation.fegin.wx.request.*;
import com.cfpamf.ms.insur.operation.promotion.dto.*;
import com.cfpamf.ms.insur.operation.promotion.job.MaterialJobHandler;
import com.cfpamf.ms.insur.operation.promotion.listener.MaterialNotifyListener;
import com.cfpamf.ms.insur.operation.promotion.service.MarketingService;
import com.cfpamf.ms.insur.operation.promotion.service.MaterialNotifyService;
import com.cfpamf.ms.insur.operation.promotion.service.OmsBaseService;
import com.cfpamf.ms.insur.operation.xj.service.impl.WhalePublicApiBaseService;
import com.google.common.collect.Lists;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Date 2024/4/29 16:16
 * @Version 1.0
 */
@Slf4j
@Api(tags = "在线推广")
@RestController
@RequestMapping("/onlinePromotion")
public class OnlinePromotionController {


    @Resource(name="marketingService")
    private MarketingService marketingService;

    @Autowired
    private WhalePublicApiBaseService whalePublicApiBaseService;

    @Resource(name="omsBaseService")
    private OmsBaseService omsBaseService;

    @PostMapping("/addMaterial")
    @ApiOperation("同步素材圈新增操作")
    public Result addMaterial(@RequestBody MarketingInfoDTO dto) {
        log.info("addMaterial req= {}",JSON.toJSONString(dto));
        //校验参数
        checkParams(dto);
        marketingService.addMaterial(dto);
        log.info("addMaterial res= {}",JSON.toJSONString(dto));
        Result result = new Result();
        result.setSuccess(Boolean.TRUE);
        return result;
    }



    @PostMapping("/putDownMaterial")
    @ApiOperation("同步素材圈下架操作")
    public Result putDownMaterial(@RequestBody PutDownMarketingDTO putDownMarketingDTO) {
        log.info("putDownMaterial req= {}",JSON.toJSONString(putDownMarketingDTO));
        //校验参数
        checkParamsForPutDownMaterial(putDownMarketingDTO);
        marketingService.putDownMaterial(putDownMarketingDTO);
        log.info("putDownMaterial res= {}",JSON.toJSONString(putDownMarketingDTO));
        Result result = new Result();
        result.setSuccess(Boolean.TRUE);
        return result;
    }

    private void checkParams(MarketingInfoDTO dto){
        if(StringUtils.isBlank(dto.getContent()) && CollectionUtils.isEmpty(dto.getMarketingAttachmentAddDTOS())){
            log.warn("素材文本和素材附件不能同时为空 req= {}", JSON.toJSONString(dto));
            throw new MSBizNormalException("", "素材文本和素材附件不能同时为空");
        }
    }

    private void checkParamsForPutDownMaterial(PutDownMarketingDTO dto){
        if(StringUtils.isBlank(dto.getMaterialCode())
                || Objects.isNull(dto.getCategoryCode())
                || StringUtils.isBlank(dto.getSourceSystemCode())
        ){
            log.warn("下架素材操作校验失败必填项不能为空 req= {}", JSON.toJSONString(dto));
            throw new MSBizNormalException("", "下架素材操作校验失败必填项不能为空");
        }
    }

    @ApiOperation(value = "查询返客列表", notes = "查询返客列表")
    @GetMapping(value = "/queryCustVisitInfoList")
    public CommonResult<CustVisitInfoOutput> queryCustVisitInfoList(CustVisitListQueryInput custVisitListQueryInput) {
        CustVisitInfoOutput output = whalePublicApiBaseService.queryCustVisitInfoList(custVisitListQueryInput);
        return CommonResult.successResult(output);
    }

    @ApiOperation(value = "查询返客分组列表", notes = "查询返客分组列表")
    @GetMapping(value = "/queryCustVisitGroupList")
    public CommonResult<CustVisitGroupOutput> queryCustVisitGroupList(CustVisitGroupListQueryInput custVisitGroupListQueryInput) {
        return CommonResult.successResult(marketingService.queryCustVisitGroupList(custVisitGroupListQueryInput));
    }

    @ApiOperation(value = "跟进处理接口", notes = "跟进处理接口")
    @PostMapping(value = "/followHandle")
    public CommonResult<Boolean> followHandle(@RequestBody VisitFollowHandleInput visitFollowHandleInput) {
        whalePublicApiBaseService.followHandle(visitFollowHandleInput);
        return CommonResult.successResult(Boolean.TRUE);
    }

    @Autowired
    private MaterialJobHandler materialJobHandler;
    @ApiOperation(value = "生成素材通知", notes = "生成素材通知")
    @PostMapping(value = "/generateUserMaterialNotify")
    public CommonResult<Boolean> generateUserMaterialNotify() {
        log.info("生成素材通知");
        //MarketingListMqDTO mqDTO = JSON.parseObject(content,MarketingListMqDTO.class);

        materialJobHandler.generateUserMaterialNotify();
        log.info("生成素材通知完成");
        return CommonResult.successResult(Boolean.TRUE);
    }

    @ApiOperation(value = "发送素材通知", notes = "发送素材通知")
    @PostMapping(value = "/sendUserMaterialNotify")
    public CommonResult<Boolean> sendUserMaterialNotify(@RequestBody MaterialMessage msg) {
        log.info("生成素材通知");
        //MarketingListMqDTO mqDTO = JSON.parseObject(content,MarketingListMqDTO.class);

        materialJobHandler.sendUserMaterialNotify();
        log.info("发送素材通知完成");
        return CommonResult.successResult(Boolean.TRUE);
    }

    @Autowired
    private MaterialNotifyService materialNotifyService;
    @ApiOperation(value = "跟进处理接口", notes = "跟进处理接口")
    @PostMapping(value = "/addMaterialNotify")
    public CommonResult<Boolean> addMaterialNotify(@RequestBody MaterialMessage msg) {
        log.info("接收到素材变更通知:{}", JSON.toJSONString(msg));
        //MarketingListMqDTO mqDTO = JSON.parseObject(content,MarketingListMqDTO.class);
        MarketingListMqDTO mqDTO = msg.getBody();
        materialNotifyService.saveNotify(mqDTO);
        log.info("素材变更通知入库完成");
        return CommonResult.successResult(Boolean.TRUE);
    }


}
