package com.cfpamf.ms.insur.operation.addCommission.service.preservation;

import com.alibaba.fastjson.JSONObject;
import com.cfpamf.ms.insur.operation.addCommission.convertor.AddCommissionConvert;
import com.cfpamf.ms.insur.operation.addCommission.dao.*;
import com.cfpamf.ms.insur.operation.addCommission.dto.*;
import com.cfpamf.ms.insur.operation.addCommission.enums.EnumWhaleCorrectProject;
import com.cfpamf.ms.insur.operation.addCommission.enums.PolicyContractStatusEnum;
import com.cfpamf.ms.insur.operation.addCommission.enums.SettlementEventTypeEnum;
import com.cfpamf.ms.insur.operation.addCommission.po.WhaleAddCommissionDetail;
import com.cfpamf.ms.insur.operation.addCommission.po.WhaleAddCommissionDetailItem;
import com.cfpamf.ms.insur.operation.addCommission.po.WhaleAddCommissionSettlementPush;
import com.cfpamf.ms.insur.operation.addCommission.query.WhaleAddCommissionPushQuery;
import com.cfpamf.ms.insur.operation.whale.model.InsuredInfoList;
import com.cfpamf.ms.insur.operation.whale.model.PreservationDetail;
import com.cfpamf.ms.insur.operation.whale.model.ProductInfoList;
import com.cfpamf.ms.insur.operation.whale.model.WhaleContract;
import com.cfpamf.ms.insur.operation.whale.preservation.PreserveSurrenderDetailVo;
import com.cfpamf.ms.insur.operation.whale.preservation.PreserveSurrenderInsuredVo;
import com.cfpamf.ms.insur.operation.whale.preservation.PreserveSurrenderProductVo;
import com.google.common.collect.Sets;
import lombok.AccessLevel;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 保单删除处理服务类
 * 该类用于处理保单删除相关的加佣数据备份和更新操作
 * <AUTHOR>
 */
@Component("personSurrenderPreservation")
@FieldDefaults(level = AccessLevel.PRIVATE)
@Slf4j
public class PersonSurrenderHandleService extends AbstractPreservationHandleService {

    @Autowired
    WhaleAddCommissionDetailItemMapper whaleAddCommissionDetailItemMapper;
    @Autowired
    WhaleAddCommissionDetailMapper whaleAddCommissionDetailMapper;
    @Autowired
    WhaleAddCommissionDetailItemBackUpsMapper whaleAddCommissionDetailItemBackUpsMapper;
    @Autowired
    WhaleAddCommissionDetailBackUpsMapper whaleAddCommissionDetailBackUpsMapper;
    @Autowired
    WhaleAddCommissionSettlementPushMapper whaleAddCommissionSettlementPushMapper;

    /**
     * 初始化推送数据
     * 根据保单号和操作DTO准备加佣结算推送数据
     *
     * @param operationDto  操作DTO，包含操作相关信息
     * @param pushDtos      推送DTO列表，用于收集推送业财的数据
     * @return              准备好的加佣结算推送数据列表
     */
    @Override
    public PreservationSettlementDto doAddCommissionCorrect(PreservationOperationDto operationDto, List<AddCommissionSettlementPushDto> pushDtos, PreservationDto preservationDto) {
        log.info("个险退保加佣记录处理,operationDto:{},pushDto:{}", JSONObject.toJSONString(operationDto), JSONObject.toJSONString(pushDtos));

        PreservationDetail preservationDetail = preservationDto.getPreservationDetail();
        WhaleContract contract = preservationDto.getContract();
        //获取当前保单下对应被保人信息
        List<WhaleAddCommissionDetailItem> whaleAddCommissionDetailItems = new ArrayList<>();
        //原始加佣承保记录，用于存储快照
        List<WhaleAddCommissionDetailItem> sourcesDetailItems = new ArrayList<>();

        List<AddCommissionSettlementPushDto> pushDtoResults = new ArrayList<>();

        String requestId = preservationDto.getRequestId();

        PreserveSurrenderDetailVo preserveSurrenderDetailVo = preservationDetail.getSurrenderDetailVo();
        int renewalTermPeriod = Objects.isNull(preservationDetail.getRenewalTermPeriod()) ? 1 : preservationDetail.getRenewalTermPeriod();

        if (preserveSurrenderDetailVo != null && CollectionUtils.isNotEmpty(preserveSurrenderDetailVo.getInsuredList())) {
            for (int i =1; i <= renewalTermPeriod; i++) {
                for (PreserveSurrenderInsuredVo insuredInfo : preserveSurrenderDetailVo.getInsuredList()) {
                    for (PreserveSurrenderProductVo productInfo : insuredInfo.getInsuredProductList()) {
                        Integer termNum = i;
                        List<AddCommissionSettlementPushDto> dtos = pushDtos.stream().filter(pushDto -> pushDto.getInsuredIdCard().equals(insuredInfo.getInsuredIdCard())
                                        && pushDto.getRiskCode().equals(productInfo.getProductCode())
                                        && pushDto.getTermNum().equals(termNum))
                                .collect(Collectors.toList());
                        log.info("个险退保-承保加佣记录匹配,insuredInfo:{},productInfo:{},dtos:{}", JSONObject.toJSONString(insuredInfo), JSONObject.toJSONString(productInfo), JSONObject.toJSONString(dtos));

                        //判断是否存在退保加佣记录，有则退出不处理
                        if (CollectionUtils.isNotEmpty(dtos) && !dtos.get(0).getPolicyStatus().equals(4)) {
                            WhaleAddCommissionDetailItem detailItem = AddCommissionConvert.INS.cvtItem(dtos.get(0));
                            detailItem.setProductStatus("4");
                            BigDecimal amount = calSurrenderAmount(preservationDetail, contract, productInfo,detailItem.getAmount());
                            detailItem.setAmount(amount.multiply(new BigDecimal(-1)));
                            detailItem.setAddCommissionAmount(detailItem.getAmount().multiply(detailItem.getProportion()).divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP));
                            detailItem.setSettlementState(!Objects.isNull(dtos.get(0).getSettlementState()) && dtos.get(0).getSettlementState()==0 ? 0 : 1);
                            detailItem.setDataIndex(detailItem.getPolicyNo() + "|" + detailItem.getEndorsementNo() + "|" + detailItem.getInsuredCode() + "|" + detailItem.getSellProductCode() + "|" + detailItem.getRiskCode() + "|" + detailItem.getTermNum() + "|" + detailItem.getProductStatus());
                            detailItem.setUuid(detailItem.getPolicyNo() + "|" + detailItem.getEndorsementNo() + "|" + detailItem.getInsuredCode() + "|" + detailItem.getSellProductCode() + "|" + detailItem.getRiskCode() + "|" + detailItem.getTermNum() + "|" + detailItem.getProductStatus() + "|" + detailItem.getDataId());
                            detailItem.setRequestId(!Objects.isNull(dtos.get(0).getSettlementState()) && dtos.get(0).getSettlementState()==0 ? "" :requestId);
                            detailItem.setAcceptanceState(!Objects.isNull(dtos.get(0).getSettlementState()) && dtos.get(0).getSettlementState()==0 ? 0 : 1);
                            whaleAddCommissionDetailItems.add(detailItem);
                            sourcesDetailItems.add(AddCommissionConvert.INS.cvtItem(dtos.get(0)));

                            AddCommissionSettlementPushDto pushDto = AddCommissionConvert.INS.cvtPushDto(detailItem, dtos.get(0), preservationDetail);
                            PolicyContractStatusEnum policyContractStatusEnum = convertProductStatus(EnumWhaleCorrectProject.decode(preservationDetail.getPreservationProject()));
                            pushDto.setSurrenderPremium(productInfo.getSurrenderPremium().multiply(new BigDecimal(-1)));
                            pushDto.setProductStatus(Objects.isNull(policyContractStatusEnum) ? "" : policyContractStatusEnum.getCode());
                            pushDto.setEventTypeCode(Objects.isNull(operationDto.getSettlementEventType()) ? "" : operationDto.getSettlementEventType().getEventCode());
                            pushDtoResults.add(pushDto);
                        }
                    }
                }
            }
        }
        log.info("个险退保加佣明细:{}", JSONObject.toJSONString(whaleAddCommissionDetailItems));

        if (CollectionUtils.isEmpty(whaleAddCommissionDetailItems)) {
            return null;
        }

        // 使用AOP上下文获取当前代理对象
        PersonSurrenderHandleService me = (PersonSurrenderHandleService) AopContext.currentProxy();

        //3、初始化汇总记录比例
        List<WhaleAddCommissionDetail> detailList = convertDetailPo(whaleAddCommissionDetailItems);

        //4.过滤核算中的数据（核算中的额数据不需要推送业财）
        List<AddCommissionSettlementPushDto> result = pushDtoResults.stream()
                .filter(dto->!Objects.isNull(dto.getSettlementState()) && dto.getSettlementState()!=0)
                .collect(Collectors.toList());

        // 执行事务操作
        me.transactional(() -> {
            // 备份明细表数据
            whaleAddCommissionDetailItemBackUpsMapper.insertBackUps(sourcesDetailItems, "保单退保");
            //备份汇总表数据
            whaleAddCommissionDetailBackUpsMapper.insertBackUps(sourcesDetailItems,"保单退保");
            // 新增退费的加佣记录
            whaleAddCommissionDetailItemMapper.batchUpdateProportionPreservation(whaleAddCommissionDetailItems);
            //更新对应uuid的汇总比例
            whaleAddCommissionDetailMapper.batchUpdate(detailList);
            //插入业财结算推送数据明细
            if (!CollectionUtils.isEmpty(result)) {
                List<WhaleAddCommissionSettlementPush> settlementPushes = result.stream().map(dto -> {
                    WhaleAddCommissionSettlementPush settlementPush = new WhaleAddCommissionSettlementPush();
                    BeanUtils.copyProperties(dto, settlementPush);
                    settlementPush.setRequestId(requestId);
                    return settlementPush;
                }).collect(Collectors.toList());
                whaleAddCommissionSettlementPushMapper.insertList(settlementPushes);
            }
        });

        // 返回处理后的推送数据
        PreservationSettlementDto settlementDto = new PreservationSettlementDto();
        settlementDto.setRequestId(requestId);
        settlementDto.setSettlementPushDtos(result);
        log.info("保单退保操作处理完成，结算参数：" + JSONObject.toJSONString(settlementDto));
        return settlementDto;
    }

    private PolicyContractStatusEnum convertProductStatus(EnumWhaleCorrectProject enumWhaleCorrectProject) {
        switch (enumWhaleCorrectProject) {
            case SURRENDER:
                return PolicyContractStatusEnum.CANCELLATION;
            case PROTOCOL_TERMINATION:
                return PolicyContractStatusEnum.AGREEMENT_TERMINATION;
            case HESITATION_SURRENDER:
                return PolicyContractStatusEnum.HESITATION_CANCEL;
            default:
                return null;
        }
    }

    /**
     * 构建操作DTO
     * 根据保全明细中的重做DTO设置操作DTO的相关信息
     *
     * @param operationDto      操作DTO，用于保存操作相关信息
     * @param preservationDto 保全明细，包含重做DTO等信息
     */
    @Override
    public void getOperationDto(PreservationOperationDto operationDto, PreservationDto preservationDto) {
        PreservationDetail preservationDetail = preservationDto.getPreservationDetail();
        if (Objects.isNull(preservationDetail)) {
            log.error("加佣保全-保单退保保全操作明细为空{}",JSONObject.toJSONString(preservationDto));
            throw new UnsupportedOperationException("保单退保保全操作明细为空");
        }
        String policyNo = preservationDetail.getPolicyCode();
        PreservationOperationDetail before = new PreservationOperationDetail();
        before.setPolicyNo(policyNo);
        operationDto.setBefore(before);
        operationDto.setSettlementEventType(convertEventTypeCode(EnumWhaleCorrectProject.decode(preservationDetail.getPreservationProject())));
    }

    /**
     * 执行事务操作
     * 用于需要在一个单独事务中执行的操作
     *
     * @param runnable  需要在事务中执行的代码块
     */
    @Transactional(rollbackFor = Exception.class)
    public void transactional(Runnable runnable) {
        runnable.run();
    }

    public BigDecimal calSurrenderAmount(PreservationDetail preservationDetail,WhaleContract contract, PreserveSurrenderProductVo productInfo,BigDecimal amount) {
        Integer longShortFlag = contract.getContractBaseInfo().getLongShortFlag();
        //标准退保、当前期数大于1，回访成功的情况下就不扣钱
        boolean renewal2Surrender= Objects.equals(preservationDetail.getPreservationProject(), EnumWhaleCorrectProject.SURRENDER.getCode())
                && preservationDetail.getRenewalTermPeriod() > 1
                && Objects.equals(contract.getContractExtendInfo().getRevisitResult(), 1);
        log.info("保单{}当前退保类型：{},当前期数：{},回访结果：{},是否扣费：{}",preservationDetail.getPolicyCode(),preservationDetail.getPreservationProject(),preservationDetail.getRenewalTermPeriod(),contract.getContractExtendInfo().getRevisitResult(),renewal2Surrender);
        //标准退保、缴费类型为
        Optional<ProductInfoList> opt =  contract.getInsuredInfoList().stream().flatMap(x -> x.getProductInfoList().stream()).filter(o->Objects.equals(o.getMainInsurance(),1)).findFirst();
        //长险趸交退保规则-过来犹豫期且回访成功退保，不追推广费。
        boolean onlyOneTime = Objects.equals(preservationDetail.getPreservationProject(), EnumWhaleCorrectProject.SURRENDER.getCode())
                && (opt.isPresent() && DJ_PROJECT_SET.contains(opt.get().getPeriodType()))
                && Objects.equals(contract.getContractExtendInfo().getRevisitResult(), 1)
                && (!Objects.isNull(longShortFlag) && longShortFlag == 1);
        log.info("保单{}当前退保类型：{},缴费方式：{},回访结果：{},是否不追推广费：{}",preservationDetail.getPolicyCode(),preservationDetail.getPreservationProject(),opt.get().getPeriodType(),contract.getContractExtendInfo().getRevisitResult(),onlyOneTime);
        if (renewal2Surrender || onlyOneTime) {
            return BigDecimal.ZERO;
        } else {
            //短险标准退保、解除附加险 按照退保金额扣费
            if ((!Objects.isNull(longShortFlag) && longShortFlag == 0)
                    || Objects.equals(preservationDetail.getPreservationProject(), EnumWhaleCorrectProject.TERMINATION_PRODUCT.getCode())) {
                log.info("保单{}当前退保类型：{},是否短险：{}",preservationDetail.getPolicyCode(),preservationDetail.getPreservationProject(),!Objects.isNull(longShortFlag) && longShortFlag == 1);
                return productInfo.getSurrenderPremium();
            }
            return amount;
        }
    }

    /**
     * 趸交枚举
     */
    private final static Set<String> DJ_PROJECT_SET =
            Sets.newHashSet(
                    "PERIOD_TYPE:4",
                    "PERIOD_TYPE:0",
                    "PERIOD_TYPE:7"
            );
}
