package com.cfpamf.ms.insur.operation.activity.enums;

import com.google.common.collect.Lists;

import java.util.List;

/**
 * 奖励类型
 *
 * <AUTHOR>
 * @date 2021/7/9 14:23
 */
public enum RewardType {
    /**
     * 加佣
     */
    ADD_COMMISSION(Lists.newArrayList(ActivityType.ADD_COMMISSION)),
    /**
     * 送券
     */
    COUPON_DELIVERY(Lists.newArrayList(ActivityType.TICKET)),
    /**
     * 红包
     */
    RED_ENVELOPE(Lists.newArrayList(ActivityType.RED_ENVELOPE)),

    GOAL_ACHIEVE(Lists.newArrayList(ActivityType.GOAL_ACHIEVE)),
    ;

    List<ActivityType> activityTypeList;

    RewardType(List<ActivityType> activityType) {
        this.activityTypeList = activityType;
    }

    /**
     * 校验活动类型
     *
     * @param activityType
     * @return
     */
    public boolean containActivityType(ActivityType activityType) {
        return activityTypeList.contains(activityType);
    }
}
