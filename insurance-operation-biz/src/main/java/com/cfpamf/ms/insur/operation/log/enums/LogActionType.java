package com.cfpamf.ms.insur.operation.log.enums;

/**
 * 操作的类型
 * 添加 修改 删除 查询
 *
 * <AUTHOR>
 */
public enum LogActionType {

    /**
     *
     */
    ADD("新建"),

    /**
     * 更新
     */
    UPDATE("更新"),

    /**
     * 删除
     */
    DELETE("删除"),

    /**
     * 查询
     */
    QUERY("查询"),

    /**
     * 下载
     */
    DOWNLOAD("下载"),

    /**
     * 其他
     */
    OTHER("其他");

    /**
     * 操作类型名称
     */
    private String name;

    LogActionType(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }
}