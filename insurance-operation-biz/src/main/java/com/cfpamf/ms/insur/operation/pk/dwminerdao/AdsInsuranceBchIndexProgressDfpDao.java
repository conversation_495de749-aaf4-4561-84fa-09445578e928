package com.cfpamf.ms.insur.operation.pk.dwminerdao;

import com.cfpamf.ms.insur.operation.pk.pojo.dto.MonthEmpCntDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/8/11 11:11
 */
@Mapper
public interface AdsInsuranceBchIndexProgressDfpDao {

    /**
     * 查询月初员工数
     *
     * @param codeList
     * @return
     */
    List<MonthEmpCntDto> selectMonthEmpOrg(@Param("start") String start, @Param("end") String end, @Param("codeList") List<String> codeList);

    /**
     * 查询区域月初员工数
     * @param start
     * @param end
     * @param codeList
     * @return
     */
    List<MonthEmpCntDto> selectMonthEmpArea(@Param("start") String start, @Param("end") String end, @Param("codeList") List<String> codeList);
}
