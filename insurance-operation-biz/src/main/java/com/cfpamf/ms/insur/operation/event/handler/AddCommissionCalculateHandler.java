package com.cfpamf.ms.insur.operation.event.handler;

import com.cfpamf.ms.insur.operation.base.event.BaseEventHandler;
import com.cfpamf.ms.insur.operation.event.AddCommissionCalculateEvent;
import com.cfpamf.ms.insur.operation.reward.service.impl.AddCommissionRewardServiceImpl;
import com.google.common.eventbus.Subscribe;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2022/5/6 17:00
 */
@Slf4j
@Component
public class AddCommissionCalculateHandler implements BaseEventHandler {

    @Autowired
    AddCommissionRewardServiceImpl addCommissionRewardService;

    /**
     * 计算加佣
     *
     * @param event
     * @return
     */
    @Subscribe
    public void handlerEvent(AddCommissionCalculateEvent event) {
        addCommissionRewardService.calculateAddCommission(event.getSmAddCommissionDetail());
    }

}
