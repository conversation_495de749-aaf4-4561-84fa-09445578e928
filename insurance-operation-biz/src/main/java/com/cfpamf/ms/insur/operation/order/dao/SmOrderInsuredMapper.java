package com.cfpamf.ms.insur.operation.order.dao;

import com.cfpamf.ms.insur.operation.base.dao.CommonMapper;
import com.cfpamf.ms.insur.operation.order.dto.SmOrderInsured;
import com.cfpamf.ms.insur.operation.order.dto.SmOrderInsuredDto;
import com.cfpamf.ms.insur.operation.order.dto.SmOrderItem;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.entity.Example;

import java.util.List;

/**
 * <AUTHOR>
@Mapper
public interface SmOrderInsuredMapper extends CommonMapper<SmOrderInsured> {


    /**
     * 根据保单号查询被保人记录
     * @param policyNo
     * @return
     */
    List<SmOrderInsuredDto> selectByPolicyNo(@Param("policyNo") String policyNo);


    /**
     * 根据保单号查询被保人记录
     * @param policyNo
     * @return
     */
    default List<SmOrderInsured> selectByRenewalOldPolicyNo(String policyNo){
        SmOrderInsured smOrderInsured = new SmOrderInsured();
        smOrderInsured.setPolicyNo(policyNo);
        return select(smOrderInsured);
    }
}
