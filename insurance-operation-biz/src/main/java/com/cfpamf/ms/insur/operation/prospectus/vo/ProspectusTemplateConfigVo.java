package com.cfpamf.ms.insur.operation.prospectus.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 *
 * 计划书模板配置
 * operation_prospectus_template_config
 *
 * <AUTHOR>
@Getter
@Setter
public class ProspectusTemplateConfigVo {

    /**
     * id
     */
    @ApiModelProperty("id")
    private Long id;
    /**
     * 产品id
     */
    @ApiModelProperty("产品id")
    private Integer productId;

    /**
     * 名称
     */
    @ApiModelProperty("名称")
    private String name;

    /**
     * 推荐理由
     */
    @ApiModelProperty("推荐理由")
    private String recommendationReason;

    /**
     * 讲解视频文件路径
     */
    @ApiModelProperty("讲解视频文件路径")
    private String explainVideoFilePath;

    /**
     * 封面文件路径集合
     */
    @ApiModelProperty("封面文件路径集合")
    private List<String> coverFileList;
}