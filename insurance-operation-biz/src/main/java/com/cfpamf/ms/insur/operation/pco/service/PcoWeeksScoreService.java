package com.cfpamf.ms.insur.operation.pco.service;

import org.springframework.util.StringUtils;
import com.alibaba.fastjson.JSON;
import com.cfpamf.ms.insur.operation.base.service.DataAuthService;
import com.cfpamf.ms.insur.operation.pco.dao.PcoExtendInfoMapper;
import com.cfpamf.ms.insur.operation.pco.dao.PcoLevelInfoMapper;
import com.cfpamf.ms.insur.operation.pco.dao.PcoWeeksScoreMapper;
import com.cfpamf.ms.insur.operation.pco.dto.PcoWeeksScoreExcelDTO;
import com.cfpamf.ms.insur.operation.pco.entity.PcoExtendInfo;
import com.cfpamf.ms.insur.operation.pco.enums.PcoLevelEnum;
import com.cfpamf.ms.insur.operation.pco.enums.PcoQualifiedlEnum;
import com.cfpamf.ms.insur.operation.pco.query.WeeksScoreListQuery;
import com.cfpamf.ms.insur.operation.pco.vo.Pco3WeeksScoreInfo;
import com.cfpamf.ms.insur.operation.pco.vo.PcoWeeksScoreVo;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.time.*;
import java.time.temporal.ChronoField;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class PcoWeeksScoreService {

    private PcoWeeksScoreMapper pcoWeeksScoreMapper;

    DataAuthService dataAuthService;

    private PcoLevelInfoMapper pcoLevelInfoMapper;

    private PcoExtendInfoMapper pcoExtendInfoMapper;
    /**
     * 查询PCO周评分列表
     * @param query
     * @return
     */
    public PageInfo<PcoWeeksScoreVo> getWeeksScoreByPage(WeeksScoreListQuery query){
        if (query.getPage() > 0) {
            PageHelper.startPage(query.getPage(), query.getSize());
        }
        dataAuthService.dataAuth(query);
        List<PcoWeeksScoreVo> pcoWeeksScoreVos = pcoWeeksScoreMapper.getWeeksScoreListVos(query);
        return new PageInfo<>(pcoWeeksScoreVos);
    }
    /**
     * 查询PCO周评分列表(填充未打卡周数据)
     * @param query
     * @return
     */
    public PageInfo<PcoWeeksScoreVo> getFillWeeksScoreByPage(WeeksScoreListQuery query){
        if (query.getPage() > 0) {
            PageHelper.startPage(query.getPage(), query.getSize());
        }
        List<PcoWeeksScoreVo> pcoWeeksScoreVos = pcoWeeksScoreMapper.getWeeksScoreListVos(query);
        List<LocalDate> dateMondays = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(pcoWeeksScoreVos)) {
            dateMondays = pcoWeeksScoreVos.stream().map(x -> {
                return x.getStartDate().toLocalDate();
            }).collect(Collectors.toList());
        }

        //获取指定时间段内每周一的日期
        List<LocalDate> mondays = getWeekInTimes(LocalDate.of(2022, 9, 19),LocalDate.now(),1);
        for (LocalDate startDate : mondays) {
            //周数据统计不包含当前周的话，填充一条总分为0的周数据
            if(!dateMondays.contains(startDate)){
                PcoWeeksScoreVo pcoWeeksScoreVo = new PcoWeeksScoreVo();
                pcoWeeksScoreVo.setStartDate(startDate.atStartOfDay());
                if(startDate.compareTo(LocalDate.now().with(TemporalAdjusters.previous(DayOfWeek.MONDAY)))==0){
                    pcoWeeksScoreVo.setEndDate(LocalDate.now().atStartOfDay());
                }else{
                    pcoWeeksScoreVo.setEndDate(startDate.with(TemporalAdjusters.next(DayOfWeek.SUNDAY)).atStartOfDay());
                }
                pcoWeeksScoreVo.setAdjustScore(0);
                pcoWeeksScoreVo.setSelfScore(0);
                pcoWeeksScoreVos.add(pcoWeeksScoreVo);
            }
        }
        pcoWeeksScoreVos = pcoWeeksScoreVos.stream().sorted(Comparator.comparing(PcoWeeksScoreVo::getStartDate).reversed()).collect(Collectors.toList());
        return new PageInfo<>(pcoWeeksScoreVos);
    }

    /**
     * PCO周评分近三周调整得分统计
     * @param query
     * @return
     */
    public PageInfo<Pco3WeeksScoreInfo> get3WeeksScoreInfo(WeeksScoreListQuery query){
        if (query.getPage() > 0) {
            PageHelper.startPage(query.getPage(), query.getSize());
        }
        dataAuthService.dataAuth(query);
        List<Pco3WeeksScoreInfo> pco3WeeksScoreInfos = pcoWeeksScoreMapper.get3WeeksScore(query);
        return new PageInfo<>(pco3WeeksScoreInfos);
    }

    /**
     * PCO周评分列表导出
     * @param query
     * @return
     */
    public List<PcoWeeksScoreExcelDTO> weeksScoreExport(WeeksScoreListQuery query){
        List<PcoWeeksScoreVo> pcoWeeksScoreVos = pcoWeeksScoreMapper.getWeeksScoreListVos(query);
        List<PcoWeeksScoreExcelDTO> pcoNewestLevelExcelDTOS = pcoWeeksScoreVos.stream()
                .map(x->{
                    PcoWeeksScoreExcelDTO pcoWeeksScoreExcelDTO = new PcoWeeksScoreExcelDTO();
                    BeanUtils.copyProperties(x, pcoWeeksScoreExcelDTO);
                    if (!StringUtils.isEmpty(x.getPcoLevel())) {
                        pcoWeeksScoreExcelDTO.setPcoLevel(PcoLevelEnum.deValue(x.getPcoLevel()).getName());
                    }
                    if(x.getBeQualified()!=null) {
                        pcoWeeksScoreExcelDTO.setBeQualified(PcoQualifiedlEnum.deValue(x.getBeQualified()).getName());
                    }
                    return pcoWeeksScoreExcelDTO;
                }).collect(Collectors.toList());
        return pcoNewestLevelExcelDTOS;
    }

    /**
     * 同步PCO周评分
     */
    public void syncWeeksScore(){
        log.info("开始同步周评分信息");
        //获取当前同步打卡记录的周评分总数(不统计周六，周日自评得分)
        List<PcoWeeksScoreVo> pcoWeeksScoreVos = pcoWeeksScoreMapper.getSyncSwForm(LocalDate.now().minusMonths(6L).toString(),LocalDate.now().toString());

        //获取所有周数据
        List<PcoWeeksScoreVo> oldPcoWeeksInfos = pcoWeeksScoreMapper.getWeeksInfo();
        log.info("同步周评分记录[{}]",JSON.toJSONString(pcoWeeksScoreVos));

        List<PcoWeeksScoreVo> insertVos = new ArrayList<>();
        List<PcoWeeksScoreVo> updateVos = new ArrayList<>();
        if(CollectionUtils.isEmpty(oldPcoWeeksInfos)){
            insertVos.addAll(pcoWeeksScoreVos);
        }else if(CollectionUtils.isNotEmpty(pcoWeeksScoreVos)){
            pcoWeeksScoreVos.forEach(x -> {
                List<PcoWeeksScoreVo> existVos = oldPcoWeeksInfos.stream().filter(vo ->
                        vo.getDingUserId().equals(x.getDingUserId()) && vo.getJobCode().equals(x.getJobCode())
                               && vo.getStartDate().toLocalDate().compareTo(x.getStartDate().toLocalDate()) == 0
                ).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(existVos)) {
                    insertVos.add(x);
                } else {
                    x.setId(existVos.get(0).getId());
                    //历史已调整评分的数据，不更新调整评分，只更新自评得分
                    x.setBeAdjust(existVos.get(0).getBeAdjust());
                    updateVos.add(x);
                }
            });
        }
        if(!CollectionUtils.isEmpty(insertVos)){
            log.info("插入周评分统计记录：[{}]", JSON.toJSONString(insertVos));
            pcoWeeksScoreMapper.insertWeeksScoreList(insertVos);
        }
        if (!CollectionUtils.isEmpty(updateVos)) {
            log.info("更新周评分统计记录：[{}]", JSON.toJSONString(updateVos));
            pcoWeeksScoreMapper.updateWeeksScoreList(updateVos);
        }
        //如果为周一，更新上周周评分记录结束时间为周天
        if (LocalDate.now().get(ChronoField.DAY_OF_WEEK)== 1){
            Integer count = pcoWeeksScoreMapper.updateEndDate();
            log.info("更新上周周评分记录（更新结束时间）：[{}]", JSON.toJSONString(count));
        }
    }

    /**
     * 修改周评分信息
     * @param list
     */
    public void updateWeeksScoreList(List<PcoWeeksScoreVo> list){
        if (CollectionUtils.isEmpty(list)){
            return;
        }

        pcoWeeksScoreMapper.update(list);

        //获取上周一日期
        LocalDate todayOfLastWeek = LocalDate.now().minusDays(7);
        LocalDate monday = todayOfLastWeek.with(TemporalAdjusters.previous(DayOfWeek.SUNDAY)).plusDays(1);

        List<PcoExtendInfo> pcoExtendInfoList = new ArrayList<>();

        //如果修改记录为上周数据，则同步更新分级管理中的胜任状态
        list.forEach(x->{
            if(x.getStartDate().toLocalDate().compareTo(monday) == 0){
                pcoLevelInfoMapper.updateBeQualified(x.getJobCode(),x.getBeQualified());

                PcoExtendInfo pcoExtendInfo = new PcoExtendInfo();
                pcoExtendInfo.setJobCode(x.getJobCode());
                pcoExtendInfo.setBeQualified(x.getBeQualified());
                pcoExtendInfoList.add(pcoExtendInfo);
            }
        });

        //更新PCO拓展表中胜任状态为最新调整数据
        if (!CollectionUtils.isEmpty(pcoExtendInfoList)){
            pcoExtendInfoMapper.updateExtendInfo(pcoExtendInfoList);
        }

    }

    /**
     * 获取指定时间内星期几的所有日期
     *
     * @param start 开始日期 2020-04-01
     * @param end   截止日期 2020-05-01
     * @param week  星期几 1
     * @return ArrayList<LocalDate> [2020-04-06, 2020-04-13, 2020-04-20, 2020-04-27]
     */
    public static List getWeekInTimes(LocalDate start, LocalDate end, int week) {
        ArrayList<LocalDate> list = new ArrayList<>();

        long days = ChronoUnit.DAYS.between(start, end)+1;
        log.info("[{}~{})之间共有：{}天", start, end, days);

        Calendar startCalender = GregorianCalendar.from(start.atStartOfDay(ZoneId.systemDefault()));
        for (int i = 0; i < days; i++) {

            // 1代表周日，7代表周六
            if (startCalender.get(Calendar.DAY_OF_WEEK) == week + 1) {
                list.add(
                        LocalDateTime.ofInstant(
                                startCalender.toInstant(),
                                ZoneOffset.systemDefault()
                        ).toLocalDate()
                );
            }
            startCalender.add(Calendar.DATE, 1);
        }
        log.info("[{}~{})之间共有：{}个 星期{},日期{}", start, end, list.size(), week,JSON.toJSONString(list));
        return list;
    }
}
