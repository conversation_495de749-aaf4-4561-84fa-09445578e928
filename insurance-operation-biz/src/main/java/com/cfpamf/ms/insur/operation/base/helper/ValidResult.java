package com.cfpamf.ms.insur.operation.base.helper;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> 2020/3/23 17:38
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ValidResult {

    public final static ValidResult SUCCESS = new ValidResult(true, "success");

    public static ValidResult error(String mes) {
        return new ValidResult(false, mes);
    }

    private boolean result;

    private String errorMsg;
}
