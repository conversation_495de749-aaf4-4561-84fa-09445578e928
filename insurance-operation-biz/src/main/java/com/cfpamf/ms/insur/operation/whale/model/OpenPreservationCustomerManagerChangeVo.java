package com.cfpamf.ms.insur.operation.whale.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2023/10/12
 * @Version 1.0
 */
@Data
public class OpenPreservationCustomerManagerChangeVo {

    /**
     * 合同号
     */
    @ApiModelProperty(value = "合同号")
    private String contractCode;

    /**
     * 保全编码
     */
    @ApiModelProperty(value = "保全编码")
    private String preservationCode;

    /**
     * 渠道编码
     */
    @ApiModelProperty(value = "渠道编码")
    private String channelCode;


    /**
     * 客户经理
     */
    @ApiModelProperty(value = "客户经理")
    private String customerManagerCode;

    /**
     * 客户经理渠道编码
     */
    @ApiModelProperty(value = "客户经理渠道编码")
    private String customerManagerChannelCode;

    /**
     * 客户经理所属分支机构
     */
    @ApiModelProperty(value = "客户经理所属分支机构")
    private String customerManagerOrgCode;

    /**
     * 客户经理渠道机构编码
     */
    @ApiModelProperty(value = "客户经理渠道机构编码")
    private String customerManagerChannelOrgCode;

    /**
     * 客户经理督导
     */
    @ApiModelProperty(value = "客户经理督导")
    private String customerManagerSupervisor;

    /**
     * 渠道编码
     */
    @ApiModelProperty(value = "渠道编码")
    private String policyChannelBranchCode;

    @ApiModelProperty(value = "是否变更所有分单:0=否,1=是")
    private Integer changeSubPolicy;
}
