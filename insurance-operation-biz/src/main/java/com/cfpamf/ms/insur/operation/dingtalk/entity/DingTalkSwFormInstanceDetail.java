package com.cfpamf.ms.insur.operation.dingtalk.entity;

import com.cfpamf.ms.insur.operation.base.entity.BaseNoUserEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import javax.persistence.Transient;

/**
 * <AUTHOR> 2022/9/5 14:23
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class DingTalkSwFormInstanceDetail extends BaseNoUserEntity {


    String formInstanceId;

    String formLabel;

    String formValue;

    String formKey;

    Integer scope;

    @Transient
    @ApiModelProperty("后台专用 是否显示列")
    Boolean adminShow;
}
