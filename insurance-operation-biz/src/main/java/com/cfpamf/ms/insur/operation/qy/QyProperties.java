package com.cfpamf.ms.insur.operation.qy;

import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * <AUTHOR> 2022/8/4 10:01
 */
@ConfigurationProperties("qy")
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class QyProperties {

    /**
     * 企业号的appid
     */
    String corpId;

    /**
     * 微信企业号的app corpSecret
     */
    String corpSecret;

    /**
     * 微信企业号应用ID
     */
    Integer agentId;

    /**
     * 设置微信企业号应用的token
     */
    String token;

    /**
     * 置微信企业号应用的EncodingAESKey
     */
    String aesKey;
}
