package com.cfpamf.ms.insur.operation.activity.form;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR>
 * @date 2021/7/6 16:33
 */
@ApiModel("活动产品规则")
@Getter
@Setter
public class SystemActivityProductRuleForm {
    /**
     * 规则码
     */
    @NotEmpty
    @ApiModelProperty(value = "规则码 GIVE-出单即送")
    private String ruleCode;

    /**
     * 比较符
     */
    @NotEmpty
    @ApiModelProperty(value = "比较符 GREATER_THAN_OR_EQUAL-大于等于 LESS_THAN_OR_EQUAL-小于等于")
    private String compareSymbol;

    /**
     * 参数
     */
    @ApiModelProperty(value = "参数")
    private String params;
}
