package com.cfpamf.ms.insur.operation.dingtalk.enums;

import com.cfpamf.common.ms.exception.MSBizNormalException;
import com.cfpamf.ms.insur.operation.promotion.enums.InsureMarketingCategoryEnum;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.experimental.FieldDefaults;

import java.util.Arrays;
import java.util.Objects;

/**
 * <AUTHOR> 2022/9/26 15:31
 */
@Getter
@FieldDefaults(level = AccessLevel.PRIVATE)
public enum EnumMsgType {

    MARKDOWN("markdown","markdown")
    ;

    private String msgType;

    private String desc;

    EnumMsgType(String msgType, String desc) {
        this.msgType = msgType;
        this.desc = desc;
    }
    public static EnumMsgType decode(String msgType) {
        return Arrays.stream(EnumMsgType.values())
                .filter(x -> Objects.equals(x.getMsgType(), msgType))
                .findFirst().orElseThrow(() -> new MSBizNormalException("", "消息类型不匹配"));
    }
}

