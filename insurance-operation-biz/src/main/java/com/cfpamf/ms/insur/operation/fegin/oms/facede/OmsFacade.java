package com.cfpamf.ms.insur.operation.fegin.oms.facede;


import com.cfpamf.ms.insur.operation.fegin.oms.request.MarketSubmitRequest;
import com.cfpamf.ms.insur.operation.fegin.oms.request.MarketingLaunchRequest;
import com.cfpamf.ms.insur.operation.fegin.oms.response.MarketingForwardGroupByDateDTO;
import com.cfpamf.ms.insur.operation.fegin.oms.response.MarketingForwardListQueryOutputDTO;
import com.cfpamf.ms.insur.operation.fegin.oms.response.MarketingLinkDTO;
import com.cfpamf.ms.insur.operation.fegin.oms.response.OmsBaseResponse;
import com.cfpamf.ms.insur.operation.promotion.dto.MarketingForwardListQueryDTO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 */
@FeignClient(name = "oms-service-base", url = "${oms.api.url}")
public interface OmsFacade {
    @ApiOperation(value = "添加营销素材")
    @PostMapping(value = "/marketing/main/submitNewForInsure")
    OmsBaseResponse<Long> submitNewForInsure(@RequestBody MarketSubmitRequest marketSubmitRequest);

    @ApiOperation("保存/更新")
    @RequestMapping(value = "/marketing-link/saveForInsure", method = RequestMethod.POST)
    OmsBaseResponse<Long> saveForInsure(@RequestBody MarketingLinkDTO saveDTO);

    @ApiOperation(value = "素材下架接口")
    @PostMapping(value = "/marketing/main/launchForInsure")
    void launch(@RequestBody MarketingLaunchRequest marketingLaunchDTO);

    @ApiOperation("分享列表保险域专用")
    @RequestMapping(value = "/publics/marketing/forwardListForInsure", method = RequestMethod.POST)
    OmsBaseResponse<MarketingForwardListQueryOutputDTO> forwardListForInsure(@RequestBody MarketingForwardListQueryDTO marketingForwardListQueryDTO);
}
