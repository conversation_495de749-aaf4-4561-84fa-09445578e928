package com.cfpamf.ms.insur.operation.msg.service.push;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cfpamf.ms.insur.operation.base.event.EventBusEngine;
import com.cfpamf.ms.insur.operation.base.exception.BusinessException;
import com.cfpamf.ms.insur.operation.dingtalk.service.DingTalkRobotService;
import com.cfpamf.ms.insur.operation.fegin.bms.enums.OperationErrorEnum;
import com.cfpamf.ms.insur.operation.fegin.image.facade.InsuranceImageFacade;
import com.cfpamf.ms.insur.operation.msg.enums.EnumMessagePushState;
import com.cfpamf.ms.insur.operation.msg.enums.OpMessageReceiverTypeEnum;
import com.cfpamf.ms.insur.operation.msg.pojo.dto.OpMessageRuleGroovyDTO;
import com.cfpamf.ms.insur.operation.msg.pojo.po.OpMessagePush;
import com.cfpamf.ms.insur.operation.msg.pojo.po.OpMessageRule;
import com.cfpamf.ms.insur.operation.msg.pojo.po.OpMessageRuleReceiver;
import com.cfpamf.ms.insur.operation.msg.service.DingTalkService;
import com.cfpamf.ms.insur.operation.msg.service.MsgPushService;
import com.cfpamf.ms.insur.operation.msg.service.push.model.CacheVo;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Component("TemplateMessagePusher")
public class TemplateMessagePusher extends AbstractPusher{

    @Autowired
    DingTalkService dingTalkService;
    @Autowired
    DingTalkRobotService robotService;

    @Autowired
    InsuranceImageFacade imageFacade;

    @Autowired
    EventBusEngine busEngine;

    @Override
    public void push(OpMessageRule rule, String contextId, boolean delay, List<OpMessageRuleReceiver> opMessageRuleReceivers) {
        if (CollectionUtils.isEmpty(opMessageRuleReceivers)) {
            throw new BusinessException(OperationErrorEnum.MSG_CONFIG_ERROR.getCode(), "没有配置消息接收者！");
        }// fork join pool
        // 当前上下文
        final Map<String, Object> context = Maps.newConcurrentMap();
        // 使用Stream API按数量分组
        int groupSize = 20;
        List<List<OpMessageRuleReceiver>> groupedLists = groupElements(opMessageRuleReceivers,groupSize);

        for (int i = 0; i < groupedLists.size(); i++) {
            List<OpMessageRuleReceiver> receivers = groupedLists.get(i);
            OpMessageRuleGroovyDTO ruleDto = new OpMessageRuleGroovyDTO();
            ruleDto.setContext(context);
            BeanUtils.copyProperties(rule, ruleDto);
            ruleDto.setReceivers(receivers);
            String o = (String) groovyService.executeForCode(MsgPushService.SCRIPT_TYPE, rule.getRuleCode(), ruleDto);
            //String o = this.mockData;

            if (StringUtils.isBlank(o)) {
                log.warn("生成的数据为空[{}]", JSONObject.toJSONString(receivers));
                return;
            }
            //该字段只有钉钉和场景接收类型才有值,因为当前接受者共用一个群场景Id,所以直接取第一个接收者对应的群场景id
            String openConversationId = receivers.get(0).getGroupContextId();
            String outTrackId = setTemplateParams(receivers, contextId, o,rule);
            //如果当前的模板有私有数据变量，切接受者没有获取到任何可用数据时，不给该接受者推送消息。
            JSONObject params = JSONObject.parseObject(o);
            JSONObject privateData = params.getJSONObject("privateData");
            JSONObject publicData = params.getJSONObject("cardData").getJSONObject("cardParamMap");
            List<OpMessageRuleReceiver> dataReceiver = new ArrayList<>();
            if (privateData !=null && privateData.size()>0){
                Set<String> keys =  privateData.keySet();
                dataReceiver.addAll(receivers.stream().filter(receiver -> keys.contains(receiver.getReceiver())).collect(Collectors.toList()));
            }else if (publicData == null || publicData.size() == 0){
                log.warn("当前分组没有可推送的数据：{}", o);
                continue; //当共有数据也没有这不推送
            }else{
                dataReceiver.addAll(receivers);
            }
            //保存待发送的数据
            List<OpMessagePush> pushList = new ArrayList<>();
            dataReceiver.stream().forEach(receiver->{
                // 当前上下文
                OpMessagePush push = saveMessagePushTodo(receiver, o.substring(0,o.length()>5000?5000:o.length()-1), contextId, outTrackId);
                pushList.add(push);
            });
            if(CollectionUtils.isEmpty(pushList)){
                log.warn("没有推送数据：{}", o);
            }
            //推送数据
            final String processQueryKey = sendTemplateMessage(dataReceiver, rule.getCardTemplateId(),outTrackId,openConversationId);

            pushList.stream().forEach(push->{
                updateByMessageId(push, processQueryKey);
            });

        }
    }

    private String setTemplateParams(List<OpMessageRuleReceiver> receivers, String contextId, String o, OpMessageRule rule) {
        return dingTalkService.createCardTemplateInstance(JSONObject.parseObject(o),rule.getCardTemplateId());
    }

    private String sendTemplateMessage(List<OpMessageRuleReceiver> receivers, String cardTemplateId,String outTrackId,String openConversationId){
        List<String> receiverIds = receivers.stream().map(OpMessageRuleReceiver::getReceiver).collect(Collectors.toList());
        return dingTalkService.sendTemplateMessage(receiverIds,cardTemplateId,outTrackId,null,openConversationId);
    }

    @Override
    public void pushAudit(List<OpMessagePush> pushes) {
        String test = String.format("# 一级标题\n## 二级标题\n### 三级标题\n#### 四级标题\n##### 五级标题\n\n正文 **加粗文本** *斜体文本* [钉钉官网链接](https://www.dingtalk.com/) [元气满满]\n\n<font sizeToken=common_h3_text_style__font_size colorTokenV2=common_blue1_color>蓝色三级标题</font>\n\n第一段第一行<br>第一段第二行\n\n第二段第一行<br>第二段第二行\n\n***\n\n> 引用第一段\n>\n>> 嵌套引用第二段\n>\n> 引用第三段\n\n1. 第一个有序列表项\n    * 第一个无序列表项\n        * 第一个嵌套无序列表项\n        * 第二个嵌套无序列表项\n    * 第二个无序列表项\n2. 第二个有序列表项\n3. 第三个有序列表项\n\n| 表格标题1 | 表格标题2 | 表格标题3 |\n| :- | :-: | -: |\n| 左对齐内容1 | 剧中内容1 | 右对齐内容1 |\n| 左对齐内容2 | 剧中内容2 | 右对齐内容2 |\n\n```json\n{\n  \"firstName\": \"John\",\n  \"lastName\": \"Smith\",\n  \"age\": 25\n}\n```");
        System.out.println(test);
    }





    public List<List<OpMessageRuleReceiver>> groupElements(List<OpMessageRuleReceiver> elements, int maxPerGroup) {
        if (elements == null || elements.isEmpty() || maxPerGroup <= 0){
            return null;
        }
        List<List<OpMessageRuleReceiver>> groups = new ArrayList<>();
        int pozition =0;
        while (pozition<elements.size()) {
            List<OpMessageRuleReceiver> group = new ArrayList<>();
            for (int i = 0; i < maxPerGroup && pozition<elements.size(); i++,pozition++) {
                OpMessageRuleReceiver element = elements.get(pozition);
                boolean existsInGroup = !isContained(group,element);
                if (existsInGroup) {
                    group.add(element);
                } else {
                    break;
                }
            }
            log.info("新增分组：{}", JSON.toJSONString(group));
            groups.add(group);
        }

        return groups;
    }

    private boolean isContained(List<OpMessageRuleReceiver> group, OpMessageRuleReceiver element) {
        List<OpMessageRuleReceiver> receivers=group.stream().filter(e -> e.getReceiver().equals(element.getReceiver())).collect(Collectors.toList());
        return !receivers.isEmpty();
    }

    String mockData = "{\"cardData\" : {\n" +
            "\t\t\"cardParamMap\": {}\n" +
            "\t},\n" +
            "\n" +
            "\t\"privateData\" : {\n" +
            "}"+ "}";
}
