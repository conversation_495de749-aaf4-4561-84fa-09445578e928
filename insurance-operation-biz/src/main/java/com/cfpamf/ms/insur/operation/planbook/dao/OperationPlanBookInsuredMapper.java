package com.cfpamf.ms.insur.operation.planbook.dao;

import com.cfpamf.ms.insur.operation.base.dao.CommonMapper;
import com.cfpamf.ms.insur.operation.planbook.entity.OperationPlanBookInsured;
import org.apache.ibatis.annotations.Mapper;
import tk.mybatis.mapper.entity.Example;

/**
 * <AUTHOR>
 */
@Mapper
public interface OperationPlanBookInsuredMapper extends CommonMapper<OperationPlanBookInsured> {

    /**
     * 根据计划书id删除数据
     *
     * @param planBookId
     * @return
     */
    default Integer deleteByPlanBookId(Integer planBookId) {
        Example example = new Example(OperationPlanBookInsured.class);
        example.createCriteria().andEqualTo("planBookId", planBookId);
        return deleteByExample(example);
    }

    /**
     * 查询计划书被保人
     *
     * @param planBookId
     * @return
     */
    default OperationPlanBookInsured queryPlanBookInsuredByPlanBookId(Integer planBookId) {
        Example example = new Example(OperationPlanBookInsured.class);
        example.createCriteria().andEqualTo("planBookId", planBookId);
        return selectOneByExample(example);
    }


}