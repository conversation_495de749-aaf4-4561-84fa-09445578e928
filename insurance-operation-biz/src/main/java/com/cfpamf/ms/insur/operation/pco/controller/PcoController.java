package com.cfpamf.ms.insur.operation.pco.controller;

import com.alibaba.excel.EasyExcel;
import com.cfpamf.common.ms.exception.MSBizNormalException;
import com.cfpamf.ms.insur.operation.base.annotaions.ResponseDecorated;
import com.cfpamf.ms.insur.operation.base.constant.BaseConstants;
import com.cfpamf.ms.insur.operation.base.util.HttpRequestUtil;
import com.cfpamf.ms.insur.operation.base.util.ValidatorUtils;
import com.cfpamf.ms.insur.operation.dingtalk.entity.DingTalkSwFormInstanceDetail;
import com.cfpamf.ms.insur.operation.pco.dao.PcoExtendInfoMapper;
import com.cfpamf.ms.insur.operation.pco.dao.PcoLevelInfoMapper;
import com.cfpamf.ms.insur.operation.pco.dao.PcoWeeksScoreMapper;
import com.cfpamf.ms.insur.operation.pco.dto.PcoLevelAdjustDTO;
import com.cfpamf.ms.insur.operation.pco.dto.PcoNewestLevelExcelDTO;
import com.cfpamf.ms.insur.operation.pco.dto.PcoWeeksScoreExcelDTO;
import com.cfpamf.ms.insur.operation.pco.entity.PcoExtendInfo;
import com.cfpamf.ms.insur.operation.pco.query.*;
import com.cfpamf.ms.insur.operation.pco.service.*;
import com.cfpamf.ms.insur.operation.pco.vo.*;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@Api(value = "PCO分级管理接口", tags = {"PCO分级管理接口"})
@RequestMapping(value = {BaseConstants.ADMIN_VERSION + "/pco"})
@ResponseDecorated
@RestController
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class PcoController {

    PcoWeeksScoreService pcoWeeksScoreService;

    PcoWeeksScoreMapper pcoWeeksScoreMapper;
    PcoExtendInfoService pcoExtendInfoService;

    PcoDayScoreService dayScoreService;

    private PcoLevelService pcoLevelService;
    private PcoLevelInfoMapper pcoLevelInfoMapper;

    private PcoScheduleService pcoScheduleService;

    private PcoExtendInfoMapper pcoExtendInfoMapper;

    @ApiOperation("查询PCO用户信息")
    @GetMapping("list")
    public PageInfo<UserVo> listPco(PcoPageQuery query) {
        return pcoExtendInfoService.listUserByCode(query);
    }

    @ApiOperation("日打卡记录-提交时间倒序")
    @GetMapping("/day/scope")
    public PageInfo<PcoDayFormVo> listPco(PcoDayFormQuery query) {
        query.setOrderBy(null);
        return dayScoreService.listDayScopes(query);

    }

    @ApiOperation("日打卡记录-表单倒序")
    @GetMapping("/day/scope/orderByForm")
    public PageInfo<PcoDayFormVo> listDayScopesDaysOrderByForm(PcoDayFormQuery query) {
        return dayScoreService.listDayScopesDaysOrderByForm(query);

    }


    @ApiOperation("单个打卡记录详情")
    @GetMapping("/day/scope/detail")
    public List<DingTalkSwFormInstanceDetail> listFormDetail(@RequestParam("formInstanceId") String formInstanceId) {
        return dayScoreService.listFormDetail(formInstanceId);

    }

    @ApiOperation("x下载日打卡记录")
    @GetMapping("/day/scope/download")
    public void download(PcoDayFormQuery query, HttpServletResponse response) throws IOException {
        dayScoreService.exportExcel(query, response);
    }

    /**
     * 查询PCO周评分列表
     *
     * @param query
     * @return
     */
    @ApiOperation(value = "查询PCO周评分列表")
    @PostMapping("/WeeksScoreList")
    public PageInfo<PcoWeeksScoreVo> getWeeksScoreByPage(@RequestBody WeeksScoreListQuery query) {
        return pcoWeeksScoreService.getWeeksScoreByPage(query);
    }

    /**
     * 查询PCO周评分列表(填充未打卡周数据)
     *
     * @param query
     * @return
     */
    @ApiOperation(value = "查询PCO周评分列表(填充未打卡周数据)")
    @PostMapping("/fillWeeksScoreList")
    public PageInfo<PcoWeeksScoreVo> getFillWeeksScoreByPage(@RequestBody WeeksScoreListQuery query) {
        return pcoWeeksScoreService.getFillWeeksScoreByPage(query);
    }

    /**
     * PCO周评分列表导出
     *
     * @param query
     * @return
     */
    @ApiOperation(value = "PCO周评分列表导出")
    @GetMapping("/weeksScoreExport")
    public void weeksScoreExport(WeeksScoreListQuery query,HttpServletResponse response) {
        List<PcoWeeksScoreExcelDTO> pcoWeeksScoreExcelDTOS = pcoWeeksScoreService.weeksScoreExport(query);
        try (OutputStream os = response.getOutputStream()) {
            //配置响应头信息
            String fileName = URLEncoder.encode("PCO周评分" + new SimpleDateFormat("yyyyMMdd").format(new Date()) + ".xlsx", StandardCharsets.UTF_8.name());
            response.setHeader("Content-disposition", "attachment; filename=" + fileName);
            response.setContentType("application/octet-stream");
            //构建导出
            EasyExcel.write(response.getOutputStream(), PcoWeeksScoreExcelDTO.class)
                    .sheet("PCO周评分列表").doWrite(pcoWeeksScoreExcelDTOS);
        } catch (Exception e) {
            log.warn("文件下载失败", e);
            throw new MSBizNormalException("", "文件下载失败");
        }
    }

    /**
     * 修改周评分信息
     *
     * @param list
     * @return
     */
    @ApiOperation(value = "修改周评分信息")
    @PostMapping("/updateWeeksScoreList")
    public void updateWeeksScoreList(@RequestBody List<PcoWeeksScoreVo> list) {
        pcoWeeksScoreService.updateWeeksScoreList(list);
    }

    /**
     * PCO周评分近三周调整得分统计
     *
     * @param query
     * @return
     */
    @ApiOperation(value = "PCO周评分近三周调整得分统计")
    @PostMapping("/get3WeeksScoreInfo")
    public PageInfo<Pco3WeeksScoreInfo> get3WeeksScoreInfo(@RequestBody WeeksScoreListQuery query) {
        return pcoWeeksScoreService.get3WeeksScoreInfo(query);
    }

    /**
     * PCO统计周汇总得分
     *
     * @param query
     * @return
     */
    @ApiOperation(value = "PCO统计周汇总得分")
    @PostMapping("/getDetailSumScpoe")
    public List<PcoInstanceDetailScopeVO> getDetailSumScpoe(@RequestBody WeeksScoreListQuery query) {
        return pcoWeeksScoreMapper.sumInstanceDetailByUserAndDate(query);
    }

    @ApiOperation(value = "微信PCO分级入口页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "openId", value = "微信openId", required = false, dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "authorization", value = "authorization", required = true, dataType = "string", paramType = "query")
    })
    @GetMapping("/getPcoList")
    public PcoLevelList getPcoList(@RequestParam( value = "openId", required = false) String openId, @RequestParam String authorization) {
        return pcoLevelService.getPcoList(openId, authorization);
    }

    /**
     * 获取PCO分级列表（最新调整记录列表）
     *
     * @param query
     * @return
     */
    @ApiOperation(value = "获取PCO分级列表（最新调整记录列表）")
    @PostMapping("/getNewestLevelByPage")
    public PageInfo<PcoNewestLevelList> getNewestLevelByPage(@RequestBody PcoLevelQuery query) {
        return pcoLevelService.getNewestLevelByPage(query);
    }

    /**
     * PCO分级列表导出（最新调整记录列表）
     *
     * @param query
     * @return
     */
    @ApiOperation(value = "PCO分级列表导出（最新调整记录列表）")
    @GetMapping("/levelNewestExport")
    public void levelNewestExport(PcoLevelQuery query,HttpServletResponse response) {
        List<PcoNewestLevelExcelDTO> pcoNewestLevelExcelDTOS = pcoLevelService.levelNewestExport(query);
        try (OutputStream os = response.getOutputStream()) {
            //配置响应头信息
            String fileName = URLEncoder.encode("PCO分级列表" + new SimpleDateFormat("yyyyMMdd").format(new Date()) + ".xlsx", StandardCharsets.UTF_8.name());
            response.setHeader("Content-disposition", "attachment; filename=" + fileName);
            response.setContentType("application/octet-stream");
            //构建导出
            EasyExcel.write(response.getOutputStream(), PcoNewestLevelExcelDTO.class)
                    .sheet("PCO分级列表").doWrite(pcoNewestLevelExcelDTOS);
        } catch (Exception e) {
            log.warn("文件下载失败", e);
            throw new MSBizNormalException("", "文件下载失败");
        }
    }

    /**
     * 根据用户Id获取PCO等级调整信息
     *
     * @param query
     * @return
     */
    @ApiOperation(value = "用户PCO等级调整列表")
    @PostMapping("/changeListByUserId")
    public PageInfo<PcoLevelChangeRecord> changeListByUserId(@RequestBody PcoLevelChangeQuery query) {
        return pcoLevelService.changeListByUserId(query);
    }

    @ApiOperation(value = "获取PCO等级详情")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "jobCode", value = "员工岗位", required = true, dataType = "string", paramType = "query"),
    })
    @GetMapping("/getLevelDetail")
    public PcoLevelDetail getLevelDetail(@RequestParam String jobCode) {
        return pcoLevelService.getLevelDetail(jobCode);
    }

    /**
     * 待办事项列表
     *
     * @param query
     * @return
     */
    @ApiOperation(value = "待办事项列表")
    @PostMapping("/listOfSchedule")
    public PageInfo<PcoScheduleInfo> listOfSchedule(@RequestBody PcoScheduleQuery query) {
        return pcoScheduleService.list(query);
    }
    /**
     * 等级调整导入
     *
     * @param dto
     * @return
     */
    @ApiOperation(value = "等级调整导入")
    @PostMapping("/levelAdjust")
    public PcoLevelAdjustResult levelAdjust(@RequestBody PcoLevelAdjustDTO dto) throws Exception{
        //基础校验
        ValidatorUtils.validateParam(dto);
        String userId = HttpRequestUtil.getUserId();
        return pcoLevelService.levelAdjust(dto,userId);
    }

    @ApiOperation(value = "更新胜任状态")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "jobCode", value = "员工岗位编码", required = true, dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "beQualified", value = "胜任状态", required = true, dataType = "int", paramType = "query"),
    })
    @GetMapping("/updateBeQualified")
    public void updateBeQualified(@RequestParam String jobCode,Integer beQualified) {
        pcoLevelInfoMapper.updateBeQualified(jobCode,beQualified);

        List<PcoExtendInfo> pcoExtendInfoList = new ArrayList<>();
        PcoExtendInfo pcoExtendInfo = new PcoExtendInfo();
        pcoExtendInfo.setJobCode(jobCode);
        pcoExtendInfo.setBeQualified(beQualified);
        pcoExtendInfoList.add(pcoExtendInfo);
        pcoExtendInfoMapper.updateExtendInfo(pcoExtendInfoList);
    }

    /**
     * 等级调整导入记录列表
     *
     * @param query
     * @return
     */
    @ApiOperation(value = "等级调整导入记录列表")
    @PostMapping("/importList")
    public PageInfo<PcoLevelImport> importList(@RequestBody PcoLevelImportQuery query) throws Exception{
        return pcoLevelService.listOfImport(query);
    }

    /**
     * 修改是否处理状态
     *
     * @param query
     * @return
     */
    @ApiOperation(value = "修改是否处理状态")
    @PostMapping("/updateBedispose")
    public void updateBedispose(@RequestBody PcoScheduleQuery query){
        pcoScheduleService.updateBedispose(query);
    }
}
