package com.cfpamf.ms.insur.operation.fegin.wx.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @ClassName: CompanyInfoInput
 * @description: 产品险种列表信息
 * @author: haijun.sun
 * @date: 2021-03-22 16:14
 **/
@ApiModel(value = "产品险种操作信息")
@Data
public class ProductList {
    @ApiModelProperty(value = "产品id", example = "1")
    Integer id;
    /**
     * 产品编码
     */
    @ApiModelProperty(value = "产品编码", example = "XZ123456789")
    private String productCode;

    /**
     * 产品名称
     */
    @ApiModelProperty(value = "产品险种名称", required = true, example = "小鲸向海")
    @NotBlank(message = "产品险种名称不能为空")
    private String productName;

    /**
     * 保险公司名称
     */
    @ApiModelProperty(value = "机构编码", required = true, example = "20210303030")
    @NotBlank(message = "机构编码不能为空")
    private String companyCode;

    /**
     * 保险公司名称
     */
    @ApiModelProperty(value = "机构名称", required = true, example = "华夏")
    private String companyName;

    /**
     * 产品大类
     */
    @ApiModelProperty(value = "产品大类[字典]", required = true, example = "PRODUCT:PRODUCTGROUP:1")
    private String productGroup;

    /**
     * 险种类型[字典]
     */
    @ApiModelProperty(value = "险种类型[字典]", required = true, example = "PRODUCT:PROTECTION_TYPE:YL")
    private String productType;

    /**
     * 主附险标记 1为主险 0为附加险
     */
    @ApiModelProperty(value = "主附险标记 1为主险 0为附加险", example = "1")
    private Integer mainProductFlag;

    @ApiModelProperty(value = "产品组合编码")
    private String portfolioCode;

    @ApiModelProperty(value = "产品组合名称")
    private String portfolioName;
}
