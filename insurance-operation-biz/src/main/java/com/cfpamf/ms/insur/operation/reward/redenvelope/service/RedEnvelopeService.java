package com.cfpamf.ms.insur.operation.reward.redenvelope.service;

import com.cfpamf.ms.insur.operation.activity.listener.GrantCouponsOrderMessage;
import com.cfpamf.ms.insur.operation.reward.redenvelope.po.vo.RedEnvelopeVo;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/1/6 15:44
 */
public interface RedEnvelopeService {
    /**
     * 获取红包集合
     *
     *
     * @param saId
     * @param userId
     * @return
     */
    List<RedEnvelopeVo> getRedEnvelope(Long saId, String userId);

    /**
     * 获取红包详情
     *
     * @param id
     * @param userId
     * @return
     */
    RedEnvelopeVo detail(Long id, String userId);

    /**
     * 拆红包
     *
     * @param id
     * @param userId
     * @return
     */
    RedEnvelopeVo openRedEnvelop(Long id, String userId);

    /**
     * 触发订单红包计算
     *
     * @param orderId
     * @param orderIdList
     */
    void triggerCalculation(String orderId, List<String> orderIdList);

    /**
     * 生成红包
     * @param couponsOrderMessage
     * @return
     */
    List<String> sendRedEnvelop(GrantCouponsOrderMessage couponsOrderMessage);
}
