package com.cfpamf.ms.insur.operation.msg.service.push;

import com.alibaba.fastjson.JSONObject;
import com.cfpamf.common.ms.exception.MSBizNormalException;
import com.cfpamf.ms.insur.operation.base.event.EventBusEngine;
import com.cfpamf.ms.insur.operation.base.exception.BusinessException;
import com.cfpamf.ms.insur.operation.base.response.CommonResult;
import com.cfpamf.ms.insur.operation.base.util.DownLoadUtil;
import com.cfpamf.ms.insur.operation.dingtalk.event.DingTalkUserIdErrorEvent;
import com.cfpamf.ms.insur.operation.dingtalk.service.DingTalkRobotService;
import com.cfpamf.ms.insur.operation.fegin.bms.enums.OperationErrorEnum;
import com.cfpamf.ms.insur.operation.fegin.image.facade.InsuranceImageFacade;
import com.cfpamf.ms.insur.operation.msg.enums.EnumMessagePushState;
import com.cfpamf.ms.insur.operation.msg.enums.OpMessageReceiverTypeEnum;
import com.cfpamf.ms.insur.operation.msg.pojo.dto.OpMessageRuleGroovyDTO;
import com.cfpamf.ms.insur.operation.msg.pojo.po.OpMessagePush;
import com.cfpamf.ms.insur.operation.msg.pojo.po.OpMessageRule;
import com.cfpamf.ms.insur.operation.msg.pojo.po.OpMessageRuleReceiver;
import com.cfpamf.ms.insur.operation.msg.service.MsgPushService;
import com.cfpamf.ms.insur.operation.msg.service.push.model.CacheVo;
import com.google.common.collect.Maps;
import lombok.AccessLevel;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.task.AsyncTaskExecutor;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalTime;
import java.util.*;
import java.util.function.Supplier;

/**
 * <AUTHOR> 2021/8/30 11:00
 */
@Component("multiplePusher")
@Slf4j
@FieldDefaults(level = AccessLevel.PRIVATE)
public class MultiplePusher extends AbstractPusher {

    @Autowired
    DingTalkRobotService robotService;
    static final String IMAGE_PARAMS_KEY = "imgParam";

    @Autowired
    InsuranceImageFacade imageFacade;

    @Autowired
    EventBusEngine busEngine;

    @Resource(name = "msg-push-Executor")
    AsyncTaskExecutor msgPushExecutor;

    @Override
    public void push(OpMessageRule rule, String contextId, boolean delay, List<OpMessageRuleReceiver> opMessageRuleReceivers) {

        if (CollectionUtils.isEmpty(opMessageRuleReceivers)) {
            throw new BusinessException(OperationErrorEnum.MSG_CONFIG_ERROR.getCode(), "没有配置消息接收者！");
        }
        // fork join pool
        // 当前上下文
        final Map<String, Object> context = Maps.newConcurrentMap();
        opMessageRuleReceivers
                .forEach(receiver -> {
                    OpMessageRuleGroovyDTO ruleDto = new OpMessageRuleGroovyDTO();
                    ruleDto.setContext(context);
                    BeanUtils.copyProperties(rule, ruleDto);
                    ruleDto.setReceiver(receiver);
                    //把钉钉群配置的json参数添加到groovy上下文对象中 如区域列表,片区列表,分支列表
                    fillDataToOpMessageRuleGroovyDTO(ruleDto,receiver);
                    final Map<String,String> o = (Map<String,String>) groovyService.executeForCode(MsgPushService.SCRIPT_TYPE, rule.getRuleCode(), ruleDto);

                    if (MapUtils.isEmpty(o)) {
                        log.warn("生成的图片为空[{}]", JSONObject.toJSONString(receiver));
                        return;
                    }
                    msgPushExecutor.submit(() -> pushMsg(receiver,contextId,o,rule));
                });

    }

    /**
     * 推送图片
     * @param receiver
     * @param contextId
     * @param o
     * @param rule
     */
    private void pushMsg(OpMessageRuleReceiver receiver, String contextId, Map<String, String> o, OpMessageRule rule) {
        Integer interval = rule.getIntervalMins();
        String title = JSONObject.parseObject(rule.getParams()).getString("title");
        for (String key:o.keySet()) {
            String params = receiver.getParams();
            JSONObject json = new JSONObject();
            if (StringUtils.isNotBlank(params)) {
                JSONObject ps = JSONObject.parseObject(params);
                if (ps.containsKey(IMAGE_PARAMS_KEY)) {
                    json.putAll(ps.getJSONObject(IMAGE_PARAMS_KEY));
                }
            }
            CacheVo imageId = null;
            try {
                imageId = getImageId(receiver, contextId, o.get(key),rule.getImageNature());
            }catch (Exception e){
                log.error(e.getMessage(),e);
                continue;
            }
            // 生成图片
            if (receiver.needAudit()) {
                OpMessagePush query = new OpMessagePush();
                query.setState(EnumMessagePushState.AUDIT.getCode());
                query.setReceiver(receiver.getReceiver());
                query.setMessageRuleId(rule.getId());
                int auditCount = pushMapper.selectCount(query);
                if (auditCount > 0) {
                    log.info("之前推送数据未审核！{}", auditCount);
                    return;
                }
                //如果发送到群 需要先上传到oss 走的企业引用开发 发送到个人使用的机器人接口 直接传地址
                saveMultipleAuditingMessagePush(receiver, contextId, imageId.getImageId(), imageId.getCacheUrl(), rule.getRuleCode(),key);
            } else {
                //保存待发送的数据
                OpMessagePush push = saveMultipleMessagePushTodo(receiver, imageId.getImageId(), contextId, imageId.getCacheUrl(),rule.getRuleCode(),key);
                //推送数据
                Boolean withRobot = false;
                //如果接收者配置过机器人，则用机器人发送。
                if(StringUtils.isNotEmpty(receiver.getRobotToken())&&StringUtils.isNotEmpty(receiver.getRobotTokenSecret())){
                    withRobot = true;
                }
                final String messageId = sendImage(receiver, imageId.getImageId(),imageId.getCacheUrl(),title,withRobot);
                updateByMessageId(push, messageId);
                log.info("推送数据,接受者："+receiver.getReceiverName()+"\n推送时间："+ LocalTime.now().toString());
            }
            if (interval != null && interval != 0){
                try {
                    Thread.sleep(1000L * 60 * interval);//间隔时间 单位分钟
                } catch (InterruptedException e) {
                    log.error(e.getMessage(),e);
                }
            }

        }
    }

    protected CacheVo getImageId(OpMessageRuleReceiver receiver, String contextId, String html,String imageNature) {
        String params = receiver.getParams();
        JSONObject json = new JSONObject();
        if (StringUtils.isNotBlank(params)) {
            JSONObject ps = JSONObject.parseObject(params);
            if (ps.containsKey(IMAGE_PARAMS_KEY)) {
                json.putAll(ps.getJSONObject(IMAGE_PARAMS_KEY));
            }
        }
        json.put("htmlValue", html);
        json.put(IMAGE_NATURE,imageNature);
        String url = apiCall(() -> dynamicsImageService.image(json)).getUrl();
        if (receiver.isPerson()) {
            return new CacheVo(url, url);
        } else {
            //json.put("htmlValue", html);
            //String url = apiCall(() -> imageFacade.image(json)).getUrl();
            byte[] bytes = DownLoadUtil.downloadByUrlToByte(url);
            log.info("图片缓存到oss成功{}", url);
            return new CacheVo(dingTalkService.uploadImage(bytes), url);
        }
    }

    private <T> T apiCall(Supplier<CommonResult<T>> api) {
        CommonResult<T> tCommonResult = api.get();
        if (tCommonResult.isSuccess()) {
            return tCommonResult.getData();
        }
        throw new MSBizNormalException(tCommonResult.getErrorCode(), tCommonResult.getMessage());
    }

    private String sendImage(OpMessageRuleReceiver receiver, String imageMediaId){
        return sendImage(receiver, imageMediaId,null,null,false);
    }
    private String sendImage(OpMessageRuleReceiver receiver, String imageMediaId, String url, String title, Boolean withRobot) {
        if (OpMessageReceiverTypeEnum.CHAT.isMe(receiver.getReceiverType())) {
            //目前仅支持发送到群
            if (withRobot){
                title = Optional.ofNullable(title).orElse("消息推送");
                return dingTalkService.sendChatMediaMessageWithGroupRobot(receiver.getRobotToken(),receiver.getRobotTokenSecret(),title,url);
            }
            return dingTalkService.sendChatMediaMessage(receiver.getReceiver(), imageMediaId);
        } else if (OpMessageReceiverTypeEnum.PERSON.isMe(receiver.getReceiverType())) {
            try {
                return robotService.pushImg2User(Collections.singletonList(receiver.getReceiver()), imageMediaId);
            } catch (BusinessException e) {
                //如果是钉钉userid或者钉钉服务器有问题 那就推送userid错误事件 但是不终止程序
                if (Objects.equals(e.getCode(), OperationErrorEnum.DING_TALK_API_ERROR_USERID.getCode())
                        || Objects.equals(e.getCode(), OperationErrorEnum.DING_TALK_API_ERROR.getCode())) {
                    busEngine.publish(new DingTalkUserIdErrorEvent(Collections.singletonList(receiver.getReceiver())));
                    return null;
                } else {
                    throw e;
                }
            }

        }
        throw new UnsupportedOperationException("目前仅支持发送图片消息到群");
    }

    @Override
    public void pushAudit(List<OpMessagePush> pushes) {

        pushes.forEach(push -> {

            OpMessageRuleReceiver receiver = new OpMessageRuleReceiver();
            receiver.setReceiverType(push.getReceiverType());
            receiver.setReceiver(push.getReceiver());
            final String messageId = sendImage(receiver, push.getContent());
            updateAuditMessagePush(push.getId(), messageId);
        });
    }


}
