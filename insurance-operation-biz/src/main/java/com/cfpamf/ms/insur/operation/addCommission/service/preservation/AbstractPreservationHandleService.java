package com.cfpamf.ms.insur.operation.addCommission.service.preservation;

import com.alibaba.fastjson.JSONObject;
import com.cfpamf.common.ms.exception.MSBizNormalException;
import com.cfpamf.ms.insur.operation.activity.vo.AddCommissionProportionVo;
import com.cfpamf.ms.insur.operation.addCommission.dto.AddCommissionSettlementPushDto;
import com.cfpamf.ms.insur.operation.addCommission.dto.CommissionRedoDTO;
import com.cfpamf.ms.insur.operation.addCommission.enums.EnumGovernBusiness;
import com.cfpamf.ms.insur.operation.addCommission.enums.EnumWhaleCorrectProject;
import com.cfpamf.ms.insur.operation.addCommission.enums.SettlementEventTypeEnum;
import com.cfpamf.ms.insur.operation.addCommission.po.WhaleAddCommissionDetail;
import com.cfpamf.ms.insur.operation.addCommission.po.WhaleAddCommissionDetailItem;
import com.cfpamf.ms.insur.operation.reward.addcommission.dto.WhaleAddCommissionUniqueFlagDTO;
import com.cfpamf.ms.insur.operation.xj.service.impl.WhalePublicApiBaseService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
public abstract class AbstractPreservationHandleService implements PreservationHandleService{

    static final int DATA_ID_SPLIT_LENGTH = 7;

    private static final AtomicInteger counter = new AtomicInteger(0);

    @Override
    public void pushSettlement(List<AddCommissionSettlementPushDto> pushDtos) {
        log.info("推送业财：{}", JSONObject.toJSONString(pushDtos));
    }

    protected List<WhaleAddCommissionDetail> convertDetailPo(List<WhaleAddCommissionDetailItem> items) {

        List<AddCommissionProportionVo> addCommissionProportionVoList = items.stream().map(
                item->{
                    AddCommissionProportionVo vo=new AddCommissionProportionVo();
                    vo.setDataId(item.getDataIndex());
                    vo.setAddCommissionProportion(item.getSettlementState() == 3 ? BigDecimal.ZERO : item.getProportion());
                    vo.setAmount(item.getAmount());
                    vo.setAddCommissionAmount(item.getSettlementState() == 3 ? BigDecimal.ZERO : item.getAddCommissionAmount());
                    return vo;
                }
        ).collect(Collectors.toList());

        Collection<WhaleAddCommissionDetail> smAddCommissionDetailCollection = addCommissionProportionVoList.stream()
                .collect(Collectors.toMap(AddCommissionProportionVo::getDataId,
                        this::convert,
                        (v1, v2) -> {
                            BigDecimal proportion = v1.getProportion();
                            BigDecimal proportion2 = v2.getProportion();
                            v1.setProportion(proportion.add(proportion2));
                            //同单参与多个加佣活动，比例累加，原始保费不变
                            v1.setAmount(v2.getAmount());
                            v1.setAddCommissionAmount(v1.getAddCommissionAmount().add(v2.getAddCommissionAmount()));
                            return v1;
                        }))
                .values();
        return CollectionUtils.isEmpty(smAddCommissionDetailCollection) ? null : new ArrayList<>(smAddCommissionDetailCollection);
    }

    protected WhaleAddCommissionDetail convert(AddCommissionProportionVo addCommissionProportionVo) {
        WhaleAddCommissionDetail whaleAddCommissionDetail = new WhaleAddCommissionDetail();
        WhaleAddCommissionUniqueFlagDTO addCommissionUniqueFlagDTO = parseAddCommissionDataId(addCommissionProportionVo.getDataId());
        BeanUtils.copyProperties(addCommissionUniqueFlagDTO, whaleAddCommissionDetail);
        whaleAddCommissionDetail.setProportion(addCommissionProportionVo.getAddCommissionProportion());
        whaleAddCommissionDetail.setAmount(addCommissionProportionVo.getAmount());
        whaleAddCommissionDetail.setAddCommissionAmount(addCommissionProportionVo.getAddCommissionAmount());
        whaleAddCommissionDetail.setUuid(addCommissionProportionVo.getDataId());
        whaleAddCommissionDetail.setCreateTime(LocalDateTime.now());
        whaleAddCommissionDetail.setUpdateTime(LocalDateTime.now());
        return whaleAddCommissionDetail;
    }


    /**
     * 获取加佣奖励 dataId 解析
     * data = "order_id|policy_no|insured_id_number|plan_id|risk_id|term_num|policystatus";
     */
    protected static WhaleAddCommissionUniqueFlagDTO parseAddCommissionDataId(String dataId) {
        if (StringUtils.isBlank(dataId)) {
            return new WhaleAddCommissionUniqueFlagDTO();
        }
        String[] split = dataId.split("\\|");
        if (split.length < DATA_ID_SPLIT_LENGTH) {
            throw new MSBizNormalException("", "加佣奖励dataId格式错误");
        }
        return new WhaleAddCommissionUniqueFlagDTO(split[0], split[1], split[2], split[3], split[4], split[5], split[6]);
    }

    protected SettlementEventTypeEnum cvtSettlementEventType(CommissionRedoDTO commissionRedoDTO) {
        EnumGovernBusiness enumGovernBusiness = EnumGovernBusiness.decode(commissionRedoDTO.getEnumGovernBusiness());
        switch (enumGovernBusiness) {
            case CHANGE_POLICY_CODE:
                return SettlementEventTypeEnum.CHANGE_POLICY_CODE;
            case CHANGE_ENDORSEMENT_NO:
                return SettlementEventTypeEnum.CHANGE_ENDORSEMENT_NO;
            case REMOVE_POLICY:
                return SettlementEventTypeEnum.REMOVE_POLICY;
            case REMOVE_ENDORSEMENT:
                return SettlementEventTypeEnum.REMOVE_ENDORSEMENT;
            case CHANGE_RENEWAL_FALLBACK:
                return SettlementEventTypeEnum.CHANGE_RENEWAL_FALLBACK;
            default:
                return null;
        }
    }

    /**
     * 保全类型转换成结算事件类型
     * @param preservationProject 保全类型
     * @return
     */
    protected SettlementEventTypeEnum convertEventTypeCode(EnumWhaleCorrectProject preservationProject) {
        switch (preservationProject) {
            case SURRENDER:
                return SettlementEventTypeEnum.STANDARD_SURRENDER;
            case PROTOCOL_TERMINATION:
                return SettlementEventTypeEnum.PROTOCOL_TERMINATION;
            case HESITATION_SURRENDER:
                return SettlementEventTypeEnum.HESITATE_SURRENDER;
            case INSURED_BASE_INFO_CHANGE:
                return SettlementEventTypeEnum.INSURED_BASE_INFO_CHANGE;
            default:
                return null;
        }
    }
}
