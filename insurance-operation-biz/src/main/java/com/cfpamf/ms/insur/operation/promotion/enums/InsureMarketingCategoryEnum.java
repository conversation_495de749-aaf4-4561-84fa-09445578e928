package com.cfpamf.ms.insur.operation.promotion.enums;

import com.cfpamf.common.ms.exception.MSBizNormalException;
import com.cfpamf.ms.insur.operation.base.exception.BusinessException;
import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;

@Getter
public enum InsureMarketingCategoryEnum {

    PRODUCT("Insure_4003","产品海报"),
    AFFAIRS_HOTSPOTS("Insure_1002","时事热点海报"),
    INSURANCE_SCIENCE("Insure_4004","理念科普海报"),
    PAYMENT_OF_CLAIMS("Insure_4001","理赔海报"),
    ;

    private String categoryCode;

    private String categoryName;

    InsureMarketingCategoryEnum(String categoryCode, String categoryName) {
        this.categoryCode = categoryCode;
        this.categoryName = categoryName;
    }
    public static InsureMarketingCategoryEnum decode(String categoryCode) {
        return Arrays.stream(InsureMarketingCategoryEnum.values())
                .filter(x -> Objects.equals(x.getCategoryCode(), categoryCode))
                .findFirst().orElseThrow(() -> new MSBizNormalException("", "素材大类不匹配"));
    }
}
