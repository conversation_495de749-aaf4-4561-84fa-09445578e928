package com.cfpamf.ms.insur.operation.customer.po;

import com.cfpamf.ms.insur.operation.base.po.BasePO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;
import springfox.documentation.spring.web.json.Json;

import javax.persistence.Table;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * 断保客户
 */
@Data
@Table(name = "customer_interruption")
@FieldDefaults(level = AccessLevel.PRIVATE)
public class CustomerInterruptionPo extends BasePO {


    @ApiModelProperty("客户id")
    Long customerId;
    @ApiModelProperty(name = "证件号码")
    String idNumber;

    @ApiModelProperty(name = "证件类型")
    String idType;

    @ApiModelProperty(name = "客户名称")
    String customerName;

    @ApiModelProperty(name = "断保时间")
    LocalDateTime lastInterruptionTime;

    @ApiModelProperty(name = "断保保单")
    String lastPolicyNo;

    @ApiModelProperty(name = "断保订单")
    String lastFhOrderId;

    @ApiModelProperty(name = "断保人管控人")
    String lastCustomerAdmin;

    @ApiModelProperty(name = "断保单推荐人")
    String lastRecommend;

    @ApiModelProperty(name = "续保时间")
    LocalDateTime renewTime;

    @ApiModelProperty(name = "激活状态")
    String renewState;

    @ApiModelProperty(name = "续保员工")
    String renewEmp;

    @ApiModelProperty(name = "跟进状态")
    String followState;

    @ApiModelProperty(name = "")
    String interruptionHistory;

    @ApiModelProperty(name = "扩展信息-标签信息")
    Json extend;

    @ApiModelProperty(name = "是否在贷")
    Integer currentLoan;

    @ApiModelProperty(name = "是否信贷")
    Json loan;
}
