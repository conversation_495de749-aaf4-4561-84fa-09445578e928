package com.cfpamf.ms.insur.operation.base.config;

import com.cfpamf.common.ms.exception.MSBizNormalException;
import com.cfpamf.common.ms.exception.MSException;
import com.cfpamf.common.ms.exception.MSNONeedRetryException;
import com.cfpamf.ms.bms.facade.exception.BizException;
import com.cfpamf.ms.insur.operation.base.annotaions.ResponseDecorated;
import com.cfpamf.ms.insur.operation.base.exception.BusinessException;
import com.cfpamf.ms.insur.operation.base.exception.DefaultBusinessException;
import com.cfpamf.ms.insur.operation.base.response.CommonResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.catalina.connector.ClientAbortException;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.autoconfigure.condition.ConditionalOnWebApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.MethodParameter;
import org.springframework.core.env.Environment;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.validation.BindException;
import org.springframework.web.HttpMediaTypeNotSupportedException;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.method.annotation.ResponseBodyAdvice;
import springfox.documentation.builders.ApiInfoBuilder;
import springfox.documentation.builders.PathSelectors;
import springfox.documentation.builders.RequestHandlerSelectors;
import springfox.documentation.service.ApiInfo;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spring.web.plugins.Docket;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

import javax.validation.ConstraintViolationException;

/**
 * 项目自基础配置
 *
 * <AUTHOR>
 * @date 2021/4/29 14:11
 */
@Slf4j
@Configuration
public class InsuranceAutoConfiguration {


    @Configuration
    @ConditionalOnWebApplication(type = ConditionalOnWebApplication.Type.SERVLET)
    public static class InsuranceWebAutoConfiguration {

        @RestController
        public class HealthController {
            @GetMapping("/health")
            @ApiOperation(value = "健康检查")
            public String check() {
                return "success";
            }
        }

        @ControllerAdvice(annotations = ResponseDecorated.class)
        public static class ResponseDecorator implements ResponseBodyAdvice<Object> {
            @Override
            public boolean supports(MethodParameter returnType, Class<? extends HttpMessageConverter<?>> converterType) {
                return true;
            }

            @Override
            public Object beforeBodyWrite(
                    Object body, MethodParameter returnType, MediaType selectedContentType,
                    Class<? extends HttpMessageConverter<?>> selectedConverterType,
                    ServerHttpRequest request, ServerHttpResponse response
            ) {
                return CommonResult.successResult(body);
            }
        }

        @Slf4j
        @ControllerAdvice()
        public static class ResponseException {

            /**
             * 参数校验异常
             * @param e
             * @return
             */
            @ResponseBody
            @ExceptionHandler(value = ConstraintViolationException.class)
            public CommonResult handler(ConstraintViolationException e) {
                return CommonResult.of(e);

            }
            /**
             * 业务操作异常：用户误操作，参数错误等等
             * @param e
             * @return
             */
            @ResponseBody
            @ExceptionHandler(value = {MSBizNormalException.class})
            public CommonResult handler(MSBizNormalException e) {
                log.info("业务异常", e);
                return CommonResult.of(e);
            }

            /**
             * 业务异常
             * MSNONeedRetryException.class
             * @param e
             * @return
             */
            @ResponseBody
            @ExceptionHandler(value = {MSNONeedRetryException.class})
            public CommonResult handler(MSNONeedRetryException e) {
                log.warn("不需要报警异常", e);
                return CommonResult.of(e);
            }
            /**
             * 业务异常
             * HttpMediaTypeNotSupportedException.class
             * @param e
             * @return
             */
            @ResponseBody
            @ExceptionHandler(value = {HttpMediaTypeNotSupportedException.class})
            public CommonResult handler(HttpMediaTypeNotSupportedException e) {
                log.warn("参数异常", e);
                return CommonResult.of(e);
            }
            /**
             * 业务异常
             * HttpMediaTypeNotSupportedException.class
             * @param e
             * @return
             */
            @ResponseBody
            @ExceptionHandler(value = {ClientAbortException.class})
            public CommonResult handler(ClientAbortException e) {
                log.warn("参数异常", e);
                return CommonResult.of(e);
            }
            /**
             * 业务异常
             * HttpMediaTypeNotSupportedException.class
             * @param e
             * @return
             */
            @ResponseBody
            @ExceptionHandler(value = {HttpRequestMethodNotSupportedException.class})
            public CommonResult handler(HttpRequestMethodNotSupportedException e) {
                log.warn("参数异常", e);
                return CommonResult.of(e);
            }

            @ResponseBody
            @ExceptionHandler(value = BizException.class)
            public CommonResult handler(BizException e) {
                log.warn("业务异常", e);
                return CommonResult.failResult(e);
            }

            @ResponseBody
            @ExceptionHandler(value = BusinessException.class)
            public CommonResult handler(BusinessException e) {
                log.warn("业务异常", e);
                return CommonResult.of(e);
            }

            @ResponseBody
            @ExceptionHandler(value = DefaultBusinessException.class)
            public CommonResult handler(DefaultBusinessException e) {
                log.warn("业务异常", e);
                return CommonResult.of(e);
            }

            @ResponseBody
            @ResponseStatus(HttpStatus.BAD_REQUEST)
            @ExceptionHandler(value = MethodArgumentNotValidException.class)
            public CommonResult handler(MethodArgumentNotValidException e) {
                log.warn("参数校验异常", e);
                return CommonResult.of(e);
            }

            @ResponseBody
            @ResponseStatus(HttpStatus.BAD_REQUEST)
            @ExceptionHandler(value = BindException.class)
            public CommonResult handler(BindException e) {
                log.warn("参数校验异常", e);
                return CommonResult.of(e);
            }

            @ResponseBody
            @ExceptionHandler(value = MSException.class)
            @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
            public CommonResult handler(MSException e) {
                log.error("服务器内部异常", e);
                return CommonResult.of(e);
            }

            @ResponseBody
            @ExceptionHandler(value = Exception.class)
            @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
            public CommonResult handler(Exception e) {
                log.error("服务器内部异常", e);
                return CommonResult.failResult(e);
            }


        }
    }

    @Configuration
    @EnableSwagger2
    @EnableConfigurationProperties(InsuranceProperties.class)
    @ConditionalOnClass(Api.class)
    @ConditionalOnProperty(prefix = "insurance.api-doc", value = "enable", matchIfMissing = true)
    public static class ApiDocConfiguration {

        @Bean
        @ConditionalOnMissingBean
        public Docket createRestApi(InsuranceProperties insuranceProperties, Environment environment) {
            InsuranceProperties.ServiceInfo serviceInfo = insuranceProperties.getServiceInfo();
            return new Docket(DocumentationType.SWAGGER_2)
                    .apiInfo(apiInfo(insuranceProperties, environment))
                    .select()
                    //扫描包路径
                    .apis(RequestHandlerSelectors.withClassAnnotation(Api.class))
                    .paths(PathSelectors.any())
                    .build();
        }

        //构建 api文档的详细信息函数,注意这里的注解引用的是哪个
        private ApiInfo apiInfo(InsuranceProperties insuranceProperties, Environment environment) {
            String applicationName = environment.getProperty("spring.application.name");
            InsuranceProperties.ApiDoc apiDoc = insuranceProperties.getApiDoc();
            return new ApiInfoBuilder()
                    //页面标题
                    .title(applicationName)
                    //描述
                    .description("方便开发人员与测试人员使用。")
                    //版本号
                    .version(apiDoc.getApiVersion())
                    .build();
        }
    }

}
