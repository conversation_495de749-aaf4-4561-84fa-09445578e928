package com.cfpamf.ms.insur.operation.job;

import com.alibaba.fastjson.JSONObject;
import com.cfpamf.ms.insur.operation.customer.service.CustomerInterruptionService;
import com.cfpamf.ms.insur.operation.customer.service.CustomerLoanFirstService;
import com.cfpamf.ms.insur.operation.customer.service.CustomerLoanService;
import com.cfpamf.ms.insur.operation.phoenix.pojo.dto.ManualMatchRenewalPolicyDto;
import com.cfpamf.ms.insur.operation.phoenix.pojo.dto.ManualMatchRenewalPolicyTaskDto;
import com.cfpamf.ms.insur.operation.phoenix.service.PhoenixEmpTodoService;
import com.cfpamf.ms.insur.operation.phoenix.service.PolicyRenewalService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/4/28 13:53
 */
@Slf4j
@Component
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class PhoenixJobHandler {


    PhoenixEmpTodoService phoenixEmpTodoService;


    CustomerInterruptionService customerInterruptionService;


    CustomerLoanService customerLoanService;

    PolicyRenewalService policyRenewalService;

    CustomerLoanFirstService customerLoanFirstService;
    /**
     * 处理客户数据
     */
    @XxlJob("interruption-customer")
    public void handlerCustomer() {
        customerInterruptionService.initInterruption();
    }


    /**
     * 处理客户数据
     */
    @XxlJob("loan-customer")
    public void handlerLoanCustomer() {
        customerLoanService.initLoan();
    }

    /**
     * 处理客户数据
     */
    @XxlJob("loan-first-customer")
    public void handlerLoanFirstCustomer() {
        customerLoanFirstService.insertFromDwd();
    }

    /**
     * 生成异业待办
     */
    @XxlJob("loan-tdo-init")
    public void handlerLoanTdoInit() {
        //待办生成
        phoenixEmpTodoService.initLoanTdo();
    }

    /**
     * 生成断保待办
     */
    @XxlJob("interruption-tdo-init")
    public void handlerTdoInit() {

        String jobParam = XxlJobHelper.getJobParam();
        if (StringUtils.isBlank(jobParam)) {
            jobParam = DateTimeFormatter.ofPattern("yyyyMMdd").format(LocalDate.now().minusDays(1L));
        }
        //待办生成
        phoenixEmpTodoService.initInterruptionTdo(jobParam);
    }

    /**
     * 高中意向断保客户重新生成断保待办
     */
    @XxlJob("interruption-new-tdo-init")
    public void handlerNewTdoInit() {
        //断保待办再生成
        phoenixEmpTodoService.initInterruptionNewTdo();
    }


    /**
     * 高中意向未转化客户重新生成异业待办
     */
    @XxlJob("loan-new-tdo-init")
    public void handlerNewLoanTdoInit() {
        //断保待办再生成
        phoenixEmpTodoService.initLoanNewTdo();
    }

    /**
     * 长时间未跟进断保待办转移
     */
    @XxlJob("interruption-Transfer-tdo")
    public void handlerTransferTdo() {
        //长时间未跟进断保待办转移
        phoenixEmpTodoService.handlerTransferTdo();
    }
    /**
     * 生成续保待办
     */
    @XxlJob("renew-tdo-init")
    public void handlerRenewTdoInit() {

        String jobParam = XxlJobHelper.getJobParam();
        if (StringUtils.isBlank(jobParam)) {
            jobParam = DateTimeFormatter.ofPattern("yyyyMMdd").format(LocalDate.now().minusDays(1L));
        }
        //待办生成
        phoenixEmpTodoService.initRenewTdo();
    }


    /**
     * 提醒处理
     * 通知没逾期+逾期的
     */
    @XxlJob("interruption-tdo-notify")
    public void handlerNotify() {
        phoenixEmpTodoService.notifyTdo();
        //phoenixEmpTodoService.notifyTimeoutTdo();
    }

    /**
     * 约定跟进提醒
     * 约定前一天通知
     */
    @XxlJob("loan-follow-notify")
    public void handlerConventionsNotify() {
        phoenixEmpTodoService.notifyConventions();
    }
    /**
     * 创建AI外呼任务（每天执行）
     */
    @XxlJob("create-ai-call-task")
    public void handlerCreateAiCallTask() {
        phoenixEmpTodoService.createAiCallTask();
    }

    /**
     * 生成A类客户待办
     */
    @XxlJob("loan-A-tdo-init")
    public void handlerLoanFirstTdoInit() {
        //待办生成
        phoenixEmpTodoService.initLoanFirstTdo();
    }

    @XxlJob("autoDeleteNotFollowerInterruption")
    public void autoDeleteNotFollowerInterruptionHandler(){
        List<Long> todoIds = phoenixEmpTodoService.listNotFollowerInterruptionTodoId1000ByPage();
        if(CollectionUtils.isEmpty(todoIds)){
            return;
        }

        phoenixEmpTodoService.deleteNotFollowerInterruption(todoIds);
    }
    @XxlJob("manualMatchRenewalPolicyHandler")
    public void manualMatchRenewalPolicyHandler(){
        String jobParam = XxlJobHelper.getJobParam();
        log.info("开始手工处理续保关系，入参：{}",jobParam);
        if (StringUtils.isBlank(jobParam)) {
            log.info("手工处理数据为空!!!");
            return ;
        }
        ManualMatchRenewalPolicyTaskDto taskDto = JSONObject.parseObject(jobParam, ManualMatchRenewalPolicyTaskDto.class);
        List<ManualMatchRenewalPolicyDto> policyList= taskDto.getPolicyList();
        doManualMatchRenewalPolicyHandler(policyList,taskDto.getRenewalSuccessTime(),taskDto.getValidFlag());
    }

    @XxlJob("manualMatchRenewalPolicyHandler2")
    public void manualMatchRenewalPolicyHandler2(){
        String jobParam = XxlJobHelper.getJobParam();
        log.info("开始手工处理续保关系，入参：{}",jobParam);
        if (StringUtils.isBlank(jobParam)) {
            log.info("手工处理数据为空!!!");
            return ;
        }
        String[] strArr = jobParam.split(",");
        List<ManualMatchRenewalPolicyDto> policyList  =  new ArrayList<>();
        for(String str : strArr){
            ManualMatchRenewalPolicyDto dto = new ManualMatchRenewalPolicyDto();
            String[] arr = str.split("%");
            dto.setOldPolicyNo(arr[0]);
            dto.setNewPolicyNo(arr[1]);
            policyList.add(dto);
        }
        doManualMatchRenewalPolicyHandler(policyList,null,0);

    }

    public void doManualMatchRenewalPolicyHandler(List<ManualMatchRenewalPolicyDto> policyList,String renewalSuccessTime,Integer validFlag){
        if(CollectionUtils.isNotEmpty(policyList)){
            for(ManualMatchRenewalPolicyDto policyDto : policyList){
                try{
                    log.info("手工处理保单续保，老保单：{},新保单：{}",policyDto.getOldPolicyNo(),policyDto.getNewPolicyNo());
                    policyRenewalService.manualMatchRenewalPolicy(policyDto, renewalSuccessTime,validFlag);
                    log.info("完成续保关系建立老保单：{},新保单：{}",policyDto.getOldPolicyNo(),policyDto.getNewPolicyNo());
                }catch (Exception e){
                    log.info("手工处理保单续保处理异常");
                    e.printStackTrace();
                }
            }

        }
    }


}
