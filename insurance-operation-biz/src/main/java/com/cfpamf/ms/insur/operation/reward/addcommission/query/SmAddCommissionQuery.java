package com.cfpamf.ms.insur.operation.reward.addcommission.query;

import com.cfpamf.ms.insur.operation.base.form.DataAuthPageForm;
import com.cfpamf.ms.insur.operation.base.util.CommonUtil;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
 * 小额保险订单查询query
 *
 * <AUTHOR>
 */
@Data
@ApiModel
public class SmAddCommissionQuery extends DataAuthPageForm {

    /**
     * 创建时间From
     */
    @ApiModelProperty(value = "创建时间From")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date createDateStart;

    /**
     * 创建时间To
     */
    @ApiModelProperty(value = "创建时间To")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date createDateEnd;


    /**
     * 记账时间From
     */
    @ApiModelProperty(value = "记账时间From")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date accountDateStart;

    /**
     * 记账时间To
     */
    @ApiModelProperty(value = "记账时间To")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date accountDateEnd;


    /**
     * 订单号
     */
    @ApiModelProperty(value = "订单号")
    private String orderNo;

    /**
     * 保单号
     */
    @ApiModelProperty(value = "保单号")
    private String policyNo;

    /**
     * 产品类型
     */
    @ApiModelProperty(value = "产品类型")
    private String productType;

    /**
     * 投保人
     */
    @ApiModelProperty(value = "投保人")
    private String applicantName;

    @ApiModelProperty(hidden = true, value = "搜索类型 1-名字 2-身份证 3-手机号")
    private Integer applicantType = 1;

    /**
     * 被保人
     */
    @ApiModelProperty(value = "被保人")
    private String insuredName;

    @ApiModelProperty(value = "被保人搜索类型 1-名字 2-身份证 3-手机号")
    private Integer insuredType = 1;


    /**
     * 推荐人工号
     */
    @ApiModelProperty(value = "推荐人工号")
    private String recommendId;

    /**
     * 订单类别
     */
    @ApiModelProperty(value = "订单类别")
    private String productAttrCode;

    /**
     * 保单状态
     */
    @ApiModelProperty(value = "保单状态")
    private String appStatus;

    /**
     * 保险公司Id
     */
    @ApiModelProperty(value = "保险公司Id")
    private String companyId;

    /**
     * 产品Id
     */
    @ApiModelProperty(value = "产品Id")
    private String productId;

    @ApiModelProperty(value = "是否整村推进  0 是，1否")
    private Integer villageActivity;


    @ApiModelProperty(value = "长险标志  1短险，0长险，默认查短险1")
    private Integer longInsurance = 1;

    /**
     * 订单号是否绝对匹配
     */
    private boolean isFullMatch = false;

    /**
     * 产品Id
     */
    @ApiModelProperty(value = "产品Id集合")
    private List<String> productIds;


    public String getOrderNo() {
        return CommonUtil.trim(orderNo);
    }

    public String getPolicyNo() {
        return CommonUtil.trim(policyNo);
    }

    public String getAppStatus() {
        return CommonUtil.trim(appStatus);
    }

    public String getCompanyId() {
        return CommonUtil.trim(companyId);
    }

    public String getRecommendId() {
        return CommonUtil.trim(recommendId);
    }

    public String getApplicantName() {
        return CommonUtil.trim(applicantName);
    }

    public String getInsuredName() {
        return CommonUtil.trim(insuredName);
    }
}
