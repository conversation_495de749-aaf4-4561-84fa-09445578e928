package com.cfpamf.ms.insur.operation.base.aop;

import com.cfpamf.ms.insur.operation.base.helper.DataSourceHolder;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.core.Ordered;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> 2021/7/7 14:33
 */
@Aspect
@Component
@Slf4j
public class DataSourceRouterAspect implements Ordered {


    /**
     * 为所有走读库的事物赋值
     *
     * @param proceedingJoinPoint
     * @return
     */
    @Around("@annotation(com.cfpamf.ms.insur.operation.base.annotaions.DataSourceReadOnly)")
    public Object proceed(ProceedingJoinPoint proceedingJoinPoint) throws Throwable {
        try {
            log.info("---------------set database connection  read only---------------");
            DataSourceHolder.set(DataSourceHolder.DataBaseType.READ);
            //让这个方法执行完毕
            Object result = proceedingJoinPoint.proceed();
            return result;
        } finally {
            DataSourceHolder.clearDataBaseType();
            log.info("---------------clear database connection---------------");
        }
    }

    @Override
    public int getOrder() {
        return 0;
    }

}
