package com.cfpamf.ms.insur.operation.call.feign.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

/**
 * 呼叫记录查询请求DTO
 *
 * <AUTHOR>
 * @date 2025-07-22
 */
@Data
@ApiModel("呼叫记录查询请求")
@FieldDefaults(level = AccessLevel.PRIVATE)
public class RecordQueryRequestDto {

    @ApiModelProperty(value = "主叫号码", example = "13900139000")
    @JsonProperty("From")
    String from;

    @ApiModelProperty(value = "关键值", example = "P202501220001", notes = "用以标识模块中的哪个对应对象，如保单号")
    @JsonProperty("Key")
    String key;

    @ApiModelProperty(value = "贷款类型", example = "124", notes = "小组：1245，个贷：124")
    @JsonProperty("LoanType")
    String loanType;

    @ApiModelProperty(value = "模块", example = "INSURANCE_OPERATION", notes = "用以标识哪个系统调用")
    @JsonProperty("Module")
    String module;

    @ApiModelProperty(value = "第几页", example = "1")
    @JsonProperty("PageIndex")
    Integer pageIndex = 1;

    @ApiModelProperty(value = "每页数量", example = "20")
    @JsonProperty("PageSize")
    Integer pageSize = 20;

    @ApiModelProperty(value = "被叫号码", example = "13800138000")
    @JsonProperty("To")
    String to;
}
