package com.cfpamf.ms.insur.operation.call.feign.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 呼叫记录查询请求DTO
 *
 * <AUTHOR>
 * @date 2025-07-22
 */
@Data
public class RecordQueryRequestDto {

    @ApiModelProperty("主叫号码")
    @JsonProperty("From")
    private String from;

    @ApiModelProperty("关键值")
    @JsonProperty("Key")
    private String key;

    @ApiModelProperty("贷款类型")
    @JsonProperty("LoanType")
    private String loanType;

    @ApiModelProperty("模块")
    @JsonProperty("Module")
    private String module;

    @ApiModelProperty("第几页")
    @JsonProperty("PageIndex")
    private Integer pageIndex = 1;

    @ApiModelProperty("每页数量")
    @JsonProperty("PageSize")
    private Integer pageSize = 20;

    @ApiModelProperty("被叫号码")
    @JsonProperty("To")
    private String to;
}
