package com.cfpamf.ms.insur.operation.prospectus.manager.validator;

import com.cfpamf.ms.insur.operation.base.exception.BusinessException;
import com.cfpamf.ms.insur.operation.prospectus.enums.AgeUnitEnum;
import com.cfpamf.ms.insur.operation.prospectus.enums.GuaranteeUnitEnum;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Objects;


/**
 * 康乐一生
 *
 * <AUTHOR>
 * @date 2021/6/24 15:05
 */
@Component(AbstractProspectusValidator.VALIDATOR_BEAN_NAME_PREFIX + "187")
public class HappyLifeProspectusValidator extends AbstractProspectusValidator {

    public static final int AGE_EIGHTEEN = 18;
    private final int GUARANTEE_LIMIT = 70;
    private final int PLAN_TWO_LIMIT = 2;
    /**
     * 最低保费
     */
    private final int INSURED_PREMIUM_MINIMUM = 1000;
    /**
     * 最低保额100000元
     */
    private final int INSURED_AMOUNT_MINIMUM = 10000;
    private final int INSURED_PAYMENT_PERIOD_LIMIT = 70;
    private final int APPLICANT_PAYMENT_PERIOD_LIMIT = 65;

    /**
     * 被保人年龄
     */

    private final int INSURED_AGE_FIVE = 5;
    private final int INSURED_AGE_SIX = 6;
    private final int INSURED_AGE_FORTY = 40;
    private final int INSURED_AGE_FORTY_ONE = 41;
    private final int INSURED_AGE_FORTY_FIVE = 45;
    private final int INSURED_AGE_FORTY_SIX = 46;
    private final int INSURED_AGE_FIFTY = 50;
    private final int INSURED_AGE_FIFTY_ONE = 51;
    private final int INSURED_AGE_FIFTY_FIVE = 55;
    private final int INSURED_AGE_FIFTY_SIX = 56;
    private final int INSURED_AGE_SIXTY = 60;
    /**
     * 保额
     */
    private final int INSURED_AMOUNT_FOUR_HUNDRED_THOUSAND = 400000;
    private final int INSURED_AMOUNT_THREE_HUNDRED_THOUSAND = 300000;
    private final int INSURED_AMOUNT_TWO_HUNDRED_THOUSAND = 200000;
    private final int INSURED_AMOUNT_EIGHTY_THOUSAND = 80000;
    private final int INSURED_AMOUNT_FIVE_THOUSAND = 50000;

    /**
     * 单张保单最低保费1000元
     */
    @Override
    public void checkInsuredPremium() {
        if (Objects.isNull(insuredPremium)) {
            return;
        }
        if (insuredPremium.compareTo(BigDecimal.valueOf(INSURED_PREMIUM_MINIMUM)) < 0) {
            throw new BusinessException("", "单张保单最低保费1000元");
        }
    }

    /**
     * 最低保额 100000元
     * <p>
     * 最高保额规则：
     * 28天-5周岁（含）：30万
     * 6-40周岁（含）：40万
     * 41-45周岁（含）：30万
     * 46-50周岁（含）：20万
     * 51-55周岁（含）：8万
     * 56—60周岁（含）：5万
     */
    @Override
    public void checkInsuredAmount() {
        //单张保单保额不能低于10000元
        BigDecimal insuredAmount = prospectusProductFactorForm.getInsuredAmount();
        if (insuredAmount.compareTo(BigDecimal.valueOf(INSURED_AMOUNT_MINIMUM)) < 0) {
            throw new BusinessException("", "单张保单保额不能低于10000元");
        }

        Integer insuredAge = prospectusProductFactorForm.getInsuredAge();
        AgeUnitEnum ageUnit = prospectusProductFactorForm.getAgeUnit();
        //
        if (AgeUnitEnum.YEAR.equals(ageUnit)) {
            //0-5周岁（含），单张保单保额不能高于30万
            if (insuredAge >= 0 && insuredAge <= INSURED_AGE_FIVE) {
                if (insuredAmountThanThree(insuredAmount, INSURED_AMOUNT_THREE_HUNDRED_THOUSAND)) {
                    throw new BusinessException("", "0-5周岁（含），单张保单保额不能高于30万");
                }
            }
            //6-40周岁（含）：40万
            if (insuredAge >= INSURED_AGE_SIX && insuredAge <= INSURED_AGE_FORTY) {
                if (insuredAmountThanThree(insuredAmount, INSURED_AMOUNT_FOUR_HUNDRED_THOUSAND)) {
                    throw new BusinessException("", "6-40周岁（含），单张保单保额不能高于40万");
                }
            }
            //41-45周岁（含）：30万
            if (insuredAge >= INSURED_AGE_FORTY_ONE && insuredAge <= INSURED_AGE_FORTY_FIVE) {
                if (insuredAmountThanThree(insuredAmount, INSURED_AMOUNT_THREE_HUNDRED_THOUSAND)) {
                    throw new BusinessException("", "41-45周岁（含），单张保单保额不能高于30万");
                }
            }
            //46-50周岁（含）：20万
            if (insuredAge >= INSURED_AGE_FORTY_SIX && insuredAge <= INSURED_AGE_FIFTY) {
                if (insuredAmountThanThree(insuredAmount, INSURED_AMOUNT_TWO_HUNDRED_THOUSAND)) {
                    throw new BusinessException("", "46-50周岁（含），单张保单保额不能高于20万");
                }
            }
            //51-55周岁（含）：8万
            if (insuredAge >= INSURED_AGE_FIFTY_ONE && insuredAge <= INSURED_AGE_FIFTY_FIVE) {
                if (insuredAmountThanThree(insuredAmount, INSURED_AMOUNT_EIGHTY_THOUSAND)) {
                    throw new BusinessException("", "51-55周岁（含），单张保单保额不能高于8万");
                }
            }
            //56—60周岁（含）：5万
            if (insuredAge >= INSURED_AGE_FIFTY_SIX && insuredAge <= INSURED_AGE_SIXTY) {
                if (insuredAmountThanThree(insuredAmount, INSURED_AMOUNT_FIVE_THOUSAND)) {
                    throw new BusinessException("", "56—60周岁（含），单张保单保额不能高于5万");
                }
            }
        }
    }

    /**
     * 金额比较
     *
     * @param insuredAmount
     * @param compareAmount
     * @return
     */
    private boolean insuredAmountThanThree(BigDecimal insuredAmount, int compareAmount) {
        return insuredAmount.compareTo(BigDecimal.valueOf(compareAmount)) > 0;
    }

    /**
     * 投保人年龄在缴费期满时不能超过70岁
     * 缴费期间满时被保人不可超过65周岁；
     */
    @Override
    public Integer getInsuredPaymentPeriodLimit() {
        return INSURED_PAYMENT_PERIOD_LIMIT;
    }

    @Override
    public Integer getApplicantPaymentPeriodLimit() {
        return APPLICANT_PAYMENT_PERIOD_LIMIT;
    }

    /**
     * 保障期保至70岁仅供投保计划1、计划2：
     * 未选择身故保险金或全残保险金责任，仅能选择保终身
     */
    @Override
    public void checkGuarantee() {
        BigDecimal guaranteeQuantity = prospectusProductFactorForm.getGuaranteeQuantity();
        GuaranteeUnitEnum guaranteeUnit = prospectusProductFactorForm.getGuaranteeUnit();
        if (GuaranteeUnitEnum.YEAR.equals(guaranteeUnit) && guaranteeQuantity.intValue() == GUARANTEE_LIMIT) {
            Integer plan = prospectusProductFactorForm.getPlan();
            if (plan != 1 && plan != PLAN_TWO_LIMIT) {
                throw new BusinessException("", "保障期保至70岁仅供投保计划1、计划2");
            }
        }
        //TODO
    }

    /**
     * 非趸缴：可投保：附加投保人豁免保险费重大疾病保险（2021款）
     * 基本保额>=30万：可投保：少儿接种意外住院津贴保险金、少儿意外医疗保险金、
     */
    @Override
    public void checkAdditionalRisks() {
        //TODO
    }

    /**
     * 大于等于18周岁
     */
    @Override
    public void checkApplicantAge() {
        Integer applicantAge = prospectusProductFactorForm.getApplicantAge();
        if(applicantAge < AGE_EIGHTEEN){
            throw new BusinessException("", "投保人年龄需大于等于18周岁");
        }
    }
}
