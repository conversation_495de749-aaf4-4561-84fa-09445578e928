package com.cfpamf.ms.insur.operation.activity.vo;

import com.alibaba.fastjson.JSON;
import com.cfpamf.ms.insur.operation.activity.enums.RewardType;
import com.cfpamf.ms.insur.operation.prospectus.entity.SmPlan;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import springfox.documentation.spring.web.json.Json;

import javax.persistence.Column;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/7/6 16:15
 */
@ApiModel("活动产品以及规则奖励视图对象")
@Getter
@Setter
public class SystemActivityProductVo {
    /**
     * id
     */
    @ApiModelProperty(value = "id")
    private Long id;

    /**
     * 活动id
     */
    @ApiModelProperty(value = "活动id")
    private Long saId;
    /**
     * 产品名称
     */

    @ApiModelProperty(value = "产品名称")
    private String productName;

    /**
     * 活动id
     */
    @ApiModelProperty(value = "活动id")
    private Long systemActivityProgrammeId;

    /**
     * 产品Id
     */

    @ApiModelProperty(value = "产品Id")
    private Long productId;

    /**
     * '奖励类型Add_Commission-加佣 Coupon_delivery送券',
     */
    @ApiModelProperty(" 奖励类型ADD_COMMISSION-加佣 COUPON_DELIVERY送券")
    private RewardType rewardType;

    /**
     * 适用计划名称集合
     */
    @ApiModelProperty(value = "适用计划名称集合")
    private List<PlanSimpleVo> planList;

    /**
     * 计划Id集合
     */
    @ApiModelProperty(value = "计划Id集合")
    private String planId;

    /**
     * 计划Id集合
     */
    @ApiModelProperty(value = "险种名称集合")
    private String riskName;

    /**
     * 赠送的数量
     */
    @ApiModelProperty(value = "赠送的数量")
    private Integer quantity;

    /**
     * 触发类型
     */
    @ApiModelProperty(value = "触发类型 任务触发-JOB  监听触发-LISTENER")
    private String triggerType;

    /**
     * 活动产品规则
     */
    @ApiModelProperty("活动产品规则视图对象")
    private List<SystemActivityProductRuleVo> systemActivityProductRuleList;

    @JsonIgnore
    public List<Long> getPlanIdList() {
        return JSON.parseArray(planId, Long.class);
    }
    @JsonIgnore
    public List<String> getRiskNameList() {
        return JSON.parseArray(riskName, String.class);
    }
}
