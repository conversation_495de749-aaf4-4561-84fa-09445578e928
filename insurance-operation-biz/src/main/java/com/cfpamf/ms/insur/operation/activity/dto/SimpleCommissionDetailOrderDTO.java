package com.cfpamf.ms.insur.operation.activity.dto;

import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import java.time.LocalDateTime;

/**
 * <AUTHOR> 2022/7/4 14:11
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class SimpleCommissionDetailOrderDTO {

    String orderId;

    Integer productId;

    LocalDateTime paymentTime;

    String recommendId;

    String regionName;

    String regionCode;

    String organizationName;

    String policyNo;

}
