package com.cfpamf.ms.insur.operation.base.bean;

import com.github.pagehelper.PageInfo;
import lombok.Data;

import java.util.List;

/**
 * @description: 高级分页
 * @author: z<PERSON><PERSON><PERSON>
 * @create: 2018-07-06 09:19
 **/
@Data
public class SmyPageInfo<T, S> extends PageInfo<T> {

    /**
     * 汇总信息
     */
    private S summary;

    /**
     * 默认构造函数
     */
    public SmyPageInfo() {
        super();
    }

    /**
     * list构造函数
     */
    public SmyPageInfo(List<T> list) {
        super(list);
    }

    /**
     * list构造函数
     */
    public SmyPageInfo(List<T> list, S summary) {
        super(list);
        this.summary = summary;
    }
}
