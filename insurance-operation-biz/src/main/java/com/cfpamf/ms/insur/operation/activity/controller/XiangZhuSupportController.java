package com.cfpamf.ms.insur.operation.activity.controller;

import com.cfpamf.common.ms.result.Result;
import com.cfpamf.ms.insur.operation.activity.dto.FirstOrderNotifyDTO;
import com.cfpamf.ms.insur.operation.base.annotaions.ResponseDecorated;
import com.cfpamf.ms.insur.operation.base.constant.BaseConstants;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @Date 2024/8/22 11:16
 * @Version 1.0
 */
@Slf4j
@Api(value = "乡助活动支持接口", tags = {"乡助活动支持接口"})
@RequestMapping(value = {BaseConstants.WX_VERSION + "/operation/activity/xiangzhu"})
@ResponseDecorated
@RestController
public class XiangZhuSupportController {

    @ApiOperation("达标活动奖励-查询机构和个人")
    @PostMapping("/summary")
    public Result firstOrderNotify(@RequestBody FirstOrderNotifyDTO firstOrderNotifyDTO) {

        Result result = new Result();
        result.setSuccess(Boolean.TRUE);
        return result;
    }
}
