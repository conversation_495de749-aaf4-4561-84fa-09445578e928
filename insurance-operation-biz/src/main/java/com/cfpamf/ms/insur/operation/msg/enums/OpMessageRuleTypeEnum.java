package com.cfpamf.ms.insur.operation.msg.enums;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.experimental.FieldDefaults;

/**
 * <AUTHOR> 2021/7/15 17:06
 */
@Getter
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public enum OpMessageRuleTypeEnum {
    /**
     * 图片  模板内容必须是html格式
     */
    IMAGE("image", "图片"),
    MULTIPLE_IMAGE("multiple", "多图"),

    LINK("link", "链接"),
    /**
     * markDown格式
     */
    MARKDOWN("markdown", "markDown格式"),
    /**
     * 纯文本格式
     */
    TEXT("text", "图片"),
    ;

    String code;

    String desc;

    public boolean isMe(String code) {
        return this.code.equals(code);
    }
}
