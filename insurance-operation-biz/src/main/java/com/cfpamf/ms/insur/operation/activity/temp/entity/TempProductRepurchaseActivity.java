package com.cfpamf.ms.insur.operation.activity.temp.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

import lombok.Data;

import javax.persistence.Id;
import javax.persistence.OrderBy;
import javax.persistence.Table;

/**
 * temp_product_repurchase_activity
 *
 * <AUTHOR>
@ApiModel(value = "com.cfpamf.ms.insur.operation.activity.temp.entity.TempProductRepurchaseActivity产品计划险种配置表")
@Data
@Table(name = "temp_product_repurchase_activity")
public class TempProductRepurchaseActivity {

    /**
     * 主键id
     */
    @Id
    @OrderBy("desc")
    protected Long id;

    /**
     * 客户数
     */
    @ApiModelProperty(value = "客户数")
    private Integer customerCount;

    /**
     * 奖券数
     */
    @ApiModelProperty(value = "奖券数")
    private Integer lotteryCount;

    /**
     * 用户名称
     */
    @ApiModelProperty(value = "用户名称")
    private String userName;

    /**
     * 机构名称
     */
    @ApiModelProperty(value = "机构名称")
    private String organizationName;

    /**
     * 区域名称
     */
    @ApiModelProperty(value = "区域名称 ")
    private String regionName;

    /**
     * 客户经理名称
     */
    @ApiModelProperty(value = "客户经理名称")
    private String recommendId;

    /**
     * 排名
     */
    @ApiModelProperty(value = "排名")
    private Integer rank;

    /**
     * 创建时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;

}