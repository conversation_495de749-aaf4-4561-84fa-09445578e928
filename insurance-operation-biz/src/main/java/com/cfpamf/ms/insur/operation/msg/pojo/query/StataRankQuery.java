package com.cfpamf.ms.insur.operation.msg.pojo.query;

import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR> 2021/7/14 14:28
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class StataRankQuery {

    @ApiModelProperty("产品id")
    List<Integer> productIds;

    @ApiModelProperty("开始日期")
    LocalDate startDate;

    @ApiModelProperty("结束日期")
    LocalDate endDate;

    @ApiModelProperty("区域名称集合")
    List<String> regionNames;

    @ApiModelProperty("推送行数")
    Integer maxRows;

    @ApiModelProperty("最小单量")
    Integer minCts;

    @ApiModelProperty("最小保费")
    BigDecimal minAmounts;

    @ApiModelProperty("指定排序的列 不指定就是先保费 再单量 ")
    String sortCol;
}
