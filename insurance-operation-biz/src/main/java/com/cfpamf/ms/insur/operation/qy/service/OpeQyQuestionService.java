package com.cfpamf.ms.insur.operation.qy.service;

import com.cfpamf.common.ms.exception.MSBizNormalException;
import com.cfpamf.ms.insur.operation.base.constant.BaseConstants;
import com.cfpamf.ms.insur.operation.base.event.EventBusEngine;
import com.cfpamf.ms.insur.operation.qy.dao.OpeQyQuestionAnswerMapper;
import com.cfpamf.ms.insur.operation.qy.dao.OpeQyQuestionMapper;
import com.cfpamf.ms.insur.operation.qy.dao.OpeQyQuestionOptionMapper;
import com.cfpamf.ms.insur.operation.qy.entity.OpeQyQuestion;
import com.cfpamf.ms.insur.operation.qy.entity.OpeQyQuestionAnswer;
import com.cfpamf.ms.insur.operation.qy.entity.OpeQyQuestionOption;
import com.cfpamf.ms.insur.operation.qy.event.OpeQyPagerEvent;
import com.cfpamf.ms.insur.operation.qy.form.OpeQyQuestionAnswerForm;
import com.cfpamf.ms.insur.operation.qy.form.OpeQyQuestionForm;
import com.cfpamf.ms.insur.operation.qy.form.OpeQyQuestionOptionForm;
import com.cfpamf.ms.insur.operation.qy.form.OpeQyQuestionPagerForm;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.aop.framework.AopContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;
import java.util.Collection;
import java.util.Iterator;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.cfpamf.ms.insur.operation.qy.convter.OpeQyQuestionCvt.INS;

/**
 * <AUTHOR> 2022/8/5 15:41
 */
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@AllArgsConstructor
@Service
@Slf4j
@Validated
public class OpeQyQuestionService {

    OpeQyQuestionMapper questionMapper;

    OpeQyQuestionOptionMapper questionOptionMapper;

    OpeQyQuestionAnswerMapper answerMapper;

    EventBusEngine busEngine;


    /**
     * 目前只有一张问卷
     */
    public void insertPager(OpeQyQuestionPagerForm pagerForm) {

        List<OpeQyQuestion> opeQyQuestions = INS.questionFrom2Po(pagerForm.getQyQuestions());
        opeQyQuestions.forEach(qu -> qu.setPagerId(pagerForm.getPagerId()));
        questionMapper.insertList(opeQyQuestions);

        Iterator<OpeQyQuestion> iterator = opeQyQuestions.iterator();
        List<OpeQyQuestionOption> allOptions = pagerForm.getQyQuestions().stream()
                .map(question -> {
                    OpeQyQuestion next = iterator.next();
                    List<OpeQyQuestionOption> options = INS.optionFrom2Po(question.getOptions());
                    options.forEach(op -> {
                        op.setQuestionId(next.getId());
                        op.setPagerId(pagerForm.getPagerId());
                    });
                    return options;
                }).flatMap(Collection::stream).collect(Collectors.toList());
        questionOptionMapper.insertList(allOptions);

    }

    /**
     * 查询问卷详情
     *
     * @param pagerId
     * @return
     */
    public OpeQyQuestionPagerForm pagerDetail(Long pagerId) {
        List<OpeQyQuestionForm> questionForms = INS.questionPo2Form(questionMapper.selectByPagerId(pagerId));
        List<OpeQyQuestionOptionForm> opeQyQuestionOptionForms = INS.optionPo2Form(questionOptionMapper.selectByPagerId(pagerId));

        //将问题选项分组并复制到问题中
        questionForms.forEach(question -> {
            List<OpeQyQuestionOptionForm> options = opeQyQuestionOptionForms.stream()
                    .filter(option -> option.getQuestionId().equals(question.getId()))
                    .collect(Collectors.toList());
            question.setOptions(options);
        });
        return new OpeQyQuestionPagerForm(pagerId, questionForms);
    }

    /**
     * 保存答案
     *
     * @param answer
     */
    public void saveAnswer(@Valid OpeQyQuestionAnswerForm answer) {
        //判断是否重复
        OpeQyQuestionAnswer query = new OpeQyQuestionAnswer();
        query.setPagerId(answer.getPagerId());
        query.setExternalUserid(answer.getExternalUserid());
        OpeQyQuestionAnswer dbData = answerMapper.selectOne(query);
        if (Objects.nonNull(dbData) && Objects.equals(1, dbData.getAnswerState())) {
            throw new MSBizNormalException(BaseConstants.ERROR_CODE_PARAM_PREFIX, "该用户已填写问卷");
        }
        OpeQyQuestionService that = (OpeQyQuestionService) AopContext.currentProxy();

        OpeQyQuestionAnswer opeQyQuestionAnswer = INS.answerFrom2Po(answer);
        Long id = Objects.isNull(dbData) ? null : dbData.getId();
        opeQyQuestionAnswer.setId(id);
        //保存事物
        log.info("提交问卷[{}]", opeQyQuestionAnswer);
        that.singleTransactional(() -> answerMapper.insertOrUpdate(opeQyQuestionAnswer));

        //如果完成了问卷提交 自动打标签
        if (Objects.equals(opeQyQuestionAnswer.getAnswerState(), 1)) {
            busEngine.publish(new OpeQyPagerEvent(answer.getPagerId(), answer.getExternalUserid()));
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void singleTransactional(Runnable runnable) {
        runnable.run();
    }

    /**
     * chaxu案
     */
    public OpeQyQuestionAnswerForm queryAnswer(Long pagerId, String externalUserid) {
        //判断是否重复
        OpeQyQuestionAnswer query = new OpeQyQuestionAnswer();
        query.setPagerId(pagerId);
        query.setExternalUserid(externalUserid);
        return INS.answerPo2Form(answerMapper.selectOne(query));
    }

}

