package com.cfpamf.ms.insur.operation.visitor.controller;

import com.cfpamf.ms.insur.operation.base.annotaions.ResponseDecorated;
import com.cfpamf.ms.insur.operation.base.config.ThreadUserHolder;
import com.cfpamf.ms.insur.operation.fegin.bms.response.vo.UserDetailVO;
import com.cfpamf.ms.insur.operation.visitor.api.VisitorApi;
import com.cfpamf.ms.insur.operation.visitor.form.ProductVisitRecordSearchForm;
import com.cfpamf.ms.insur.operation.visitor.form.TimeSearchForm;
import com.cfpamf.ms.insur.operation.visitor.vo.*;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/4/30 15:35
 */
@RestController
@ResponseDecorated
@Api(value = "访客信息查询", tags = "访客信息查询")
public class VisitorController implements VisitorApi {

    @Override
    public VisitorSimpleStatisticsVo getVisitorSimpleStatistics(String deadlineString) {
        UserDetailVO userDetailVO = ThreadUserHolder.USER_DETAIL_TL.get();
        return null;
    }

    @Override
    public PageInfo<VisitorRecordVo> searchVisitorRecord(TimeSearchForm timeSearchForm) {
        return null;
    }

    @Override
    public VisitorDetailVo getVisitorDetail(String wxOpenId) {
        return null;
    }

    @Override
    public List<ProductVisitVo> findProductVisitRecord() {
        return null;
    }

    @Override
    public PageInfo<VisitorProductRecordVo> searchVisitorProductRecord(ProductVisitRecordSearchForm productVisitRecordSearchForm) {
        return null;
    }
}
