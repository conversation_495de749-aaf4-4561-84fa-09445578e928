package com.cfpamf.ms.insur.operation.call.feign.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 第三方呼叫服务请求DTO
 *
 * <AUTHOR>
 * @date 2025-07-22
 */
@Data
public class CallServiceRequestDto {

    @ApiModelProperty("被叫显号")
    @JsonProperty("CalleeDisNum")
    private String calleeDisNum;

    @ApiModelProperty("主叫显号")
    @JsonProperty("CallerDisNum")
    private String callerDisNum;

    @ApiModelProperty("客户编号")
    @JsonProperty("ClientId")
    private String clientId;

    @ApiModelProperty("客户名称")
    @JsonProperty("ClientName")
    private String clientName;

    @ApiModelProperty("客户类型")
    @JsonProperty("ClientType")
    private String clientType;

    @ApiModelProperty("员工编号")
    @JsonProperty("EmployeeId")
    private String employeeId;

    @ApiModelProperty("主叫号码")
    @JsonProperty("From")
    private String from;

    @ApiModelProperty("关键值")
    @JsonProperty("Key")
    private String key;

    @ApiModelProperty("信贷员Id")
    @JsonProperty("LoId")
    private String loId;

    @ApiModelProperty("贷款类型")
    @JsonProperty("LoanType")
    private String loanType;

    @ApiModelProperty("模块")
    @JsonProperty("Module")
    private String module;

    @ApiModelProperty("备注")
    @JsonProperty("Remark")
    private String remark;

    @ApiModelProperty("被叫号码")
    @JsonProperty("To")
    private String to;
}
