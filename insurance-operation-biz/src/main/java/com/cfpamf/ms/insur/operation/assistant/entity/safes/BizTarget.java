package com.cfpamf.ms.insur.operation.assistant.entity.safes;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.Date;

@ApiModel(value = "保险业务目标表。",description = "")
@Data
public class BizTarget implements Serializable,Cloneable{
    /** 主键id */
    @ApiModelProperty(value = "主键id",notes = "")
    @Id
    private Integer id ;
    /** 目标类型，类型包含：标准保费（CLASSIC_INSURANCE_AMOUNT）、异业保费配比（INSURANCE_AMOUNT_RATE）、留存率（RETENTION_RATE） */
    @ApiModelProperty(value = "目标类型，类型包含：标准保费（CLASSIC_INSURANCE_AMOUNT）、异业保费配比（INSURANCE_AMOUNT_RATE）、留存率（RETENTION_RATE）",notes = "")
    private String targetName ;
    /** 目标值 */
    @ApiModelProperty(value = "目标值",notes = "")
    private Double targetValue ;
    /** 目标责任人/组织类型，可选值：全国(COUNTRY)、区域（AREA）、片区(DISTRICT)、分支(BRANCH)、员工(EMP)。 */
    @ApiModelProperty(value = "目标责任人/组织类型，可选值：全国(COUNTRY)、区域（AREA）、片区(DISTRICT)、分支(BRANCH)、员工(EMP)。",notes = "")
    private String targetOwnerType ;
    /** 目标责任人/组织的唯一识别编码,对于人，则是工号，对于组织，则是BMS的orgCode。 */
    @ApiModelProperty(value = "目标责任人/组织的唯一识别编码,对于人，则是工号，对于组织，则是BMS的orgCode。",notes = "")
    private String targetOwnerCode ;
    /** 生效年份 */
    @ApiModelProperty(value = "生效年份",notes = "")
    private Integer effectiveYear ;
    /** 生效月份，允许为空，当为空时，代表此目标是年度目标 */
    @ApiModelProperty(value = "生效月份，允许为空，当为空时，代表此目标是年度目标",notes = "")
    private Integer effectiveMonth ;
    /** 创建时间 */
    @ApiModelProperty(value = "创建时间",notes = "")
    private Date createTime ;
    /** 创建人姓名 */
    @ApiModelProperty(value = "创建人姓名",notes = "")
    private String createUserName ;
    /** 创建人工号 */
    @ApiModelProperty(value = "创建人工号",notes = "")
    private String createUserId ;
    /** 修改时间 */
    @ApiModelProperty(value = "修改时间",notes = "")
    private Date updateTime ;
    /** 修改人姓名 */
    @ApiModelProperty(value = "修改人姓名",notes = "")
    private String updateUserName ;
    /** 修改人工号 */
    @ApiModelProperty(value = "修改人工号",notes = "")
    private String updateUserId ;
    /** 是否生效，0生效，1不生效 */
    @ApiModelProperty(value = "是否生效，0生效，1不生效",notes = "")
    private Integer enabledFlag ;

}