package com.cfpamf.ms.insur.operation.log.form;

import com.cfpamf.ms.insur.operation.base.form.PageForm;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.time.LocalDateTime;
import java.util.Date;

/**
 * 系统日志Query
 *
 * <AUTHOR>
 **/
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class SystemLogForm extends PageForm {

    /**
     * 操作开始时间
     */
    private LocalDateTime startDate;

    /**
     * 操作结束时间
     */
    private LocalDateTime endDate;

    /**
     * 操作人
     */
    private String userId;

    /**
     * 操作模块
     */
    private String moduleName;

    /**
     * 操作内容
     */
    private String description;
}
