package com.cfpamf.ms.insur.operation.whale.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;


/**
 * <AUTHOR>
 */
@FieldDefaults(level = AccessLevel.PRIVATE)
@Data
public class ProductInfoList {

    List<BeneficiaryList> beneficiaryList;

    Integer beneficiaryType;

    Integer copies;

    /**
     * 保额
     */
    BigDecimal coverage;

    Integer insuredPeriod;

    String insuredPeriodType;

    Integer mainInsurance;

    Integer paymentPeriod;

    String paymentPeriodType;

    String periodType;

    BigDecimal premium;

    String productCode;

    String productName;

    String productStatus;

    LocalDateTime surrenderTime;
    BigDecimal surrenderAmount;

    /**
     * 结算比例（%）
     */
    @ApiModelProperty(value = "结算比例（%）")
    private String settlementRatio;

    /**
     * 支出比例（%）
     */
    @ApiModelProperty(value = "支出比例（%）")
    private String expenditureRatio;

    public Boolean isMain() {
        return Objects.equals(mainInsurance, 1);
    }

}
