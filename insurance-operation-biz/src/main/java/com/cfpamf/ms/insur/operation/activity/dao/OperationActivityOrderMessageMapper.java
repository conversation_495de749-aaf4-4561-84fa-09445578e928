package com.cfpamf.ms.insur.operation.activity.dao;

import com.cfpamf.ms.insur.operation.activity.entity.OperationActivityOrderMessage;
import com.cfpamf.ms.insur.operation.base.dao.CommonMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface OperationActivityOrderMessageMapper extends CommonMapper<OperationActivityOrderMessage> {

    /**
     * 通过支付时间范围查找订单消息列表
     *
     * @param startTime
     * @param endTime
     * @param activityIdList
     * @return
     */
    List<OperationActivityOrderMessage> getByPayTimeAndActivityId(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime, @Param("activityIdList") List<Long> activityIdList);


    /**
     * 获取订单次数
     *
     * @param fhOrderId
     * @return
     */
    Long getCountByOrderId(@Param("fhOrderId") String fhOrderId);


}