package com.cfpamf.ms.insur.operation.auto.dto;

import com.cfpamf.ms.insur.operation.base.po.BasePO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * 客户断保跟进记录表
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class SystemAutoRateConfigFileDto{

    @ApiModelProperty(name="文件名称")
    String fileName;

    @ApiModelProperty(name="文件地址")
    String fileUrl;
}
