package com.cfpamf.ms.insur.operation.prospectus.service;

import com.cfpamf.ms.insur.operation.prospectus.entity.OperationProductFactor;
import com.cfpamf.ms.insur.operation.prospectus.form.ProspectusProductFactorForm;
import com.cfpamf.ms.insur.operation.prospectus.vo.FactorOptionVo;
import com.cfpamf.ms.insur.operation.prospectus.vo.PlanCoverageAmountVo;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/5/26 11:04
 */
@Service
public interface ProspectusProductFactorService {

    /**
     * 通过产品id获取产品因子
     *
     * @param productId
     * @return
     */

    List<FactorOptionVo> getProductFactor(Long productId);

    /**
     * 计算保费
     *
     * @param prospectusProductFactorForm
     * @return
     */
    BigDecimal calculationPremium(ProspectusProductFactorForm prospectusProductFactorForm);

    /**
     * 获取产品的plan 和计划保障的映射关系
     * key ->planId value->PlanCoverageAmountVOList
     *
     * @param productId
     * @return
     */
    public Map<Integer, List<PlanCoverageAmountVo>> getPlanCoverageAmountList(Long productId);

    /**
     * 获取产品因子
     *
     * @param prospectusProductFactorForm
     * @return
     */
    public OperationProductFactor getOperationProductFactor(ProspectusProductFactorForm prospectusProductFactorForm);
}
