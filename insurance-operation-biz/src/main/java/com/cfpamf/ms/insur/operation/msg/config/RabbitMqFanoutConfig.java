package com.cfpamf.ms.insur.operation.msg.config;

import org.springframework.amqp.core.Exchange;
import org.springframework.amqp.core.ExchangeBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @Date 2022/11/24 11:14
 * @Version 1.0
 */
@Configuration
public class RabbitMqFanoutConfig {

    public static final String EXCHANGE_NAME = "fanout.queue.insur.operation.clear.script.cache.exchange";

    @Bean("CLEAR_SCRIPT_EXCHANGE")
    public Exchange msgDelayExchange() {
        return ExchangeBuilder
                .fanoutExchange(EXCHANGE_NAME)
                .durable(true)
                .build();
    }

}
