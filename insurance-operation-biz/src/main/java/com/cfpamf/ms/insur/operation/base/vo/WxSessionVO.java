package com.cfpamf.ms.insur.operation.base.vo;


import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

/**
 * 微信session信息
 *
 * <AUTHOR>
 **/
@Data
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class WxSessionVO extends WxUserVO implements Serializable {
    private static final long serialVersionUID = -373871580643467655L;

    /**
     * 系统（区分公众号）
     */
    @ApiModelProperty(value = "系统（区分公众号）")
    private String system;

    /**
     * 渠道
     */
    @ApiModelProperty(value = "渠道")
    private String channel;

    /**
     * 登录token
     */
    @ApiModelProperty(value = "登录token")
    private String authorization;

    /**
     * bms token
     */
    @ApiModelProperty(value = "bms授权码")
    private String bmsToken;

    /**
     * 是否显示推广费
     */
    @ApiModelProperty(value = "是否显示推广费")
    private Boolean showCmsRatio;

    /**
     * 是否显示中户保险入口
     */
    @ApiModelProperty(value = "是否显示中户保险入口")
    private Boolean showCicEntry;

    /**
     * 流程审批
     */
    @ApiModelProperty(value = "流程审批")
    private Boolean showProcessApproval;

    /**
     * 代理人类别
     */
    @ApiModelProperty(value = "代理人类别")
    private Integer agentType;

    /**
     * 微信绑定所有用户
     */
    @ApiModelProperty(value = "微信绑定所有用户")
    private List<WxUserVO> wxUsers;

    /**
     * bms systemId
     */
    @ApiModelProperty(value = "bms systemId")
    private Integer bmsSystemId;

    /**
     * bms roleCode
     */
    @ApiModelProperty(value = "bms roleCode")
    private String bmsRoleCode;
}
