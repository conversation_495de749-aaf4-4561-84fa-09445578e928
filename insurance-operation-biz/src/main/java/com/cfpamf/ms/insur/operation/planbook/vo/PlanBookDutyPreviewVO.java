package com.cfpamf.ms.insur.operation.planbook.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2021/12/14 16:34
 * @Version 1.0
 */
@Data
public class PlanBookDutyPreviewVO {

    @ApiModelProperty("险种key")
    private String riskKey;

    @ApiModelProperty("险种名称")
    private String riskName;

    @ApiModelProperty("险种类型 1：主险，2：附加险")
    private String riskType;

    @ApiModelProperty("金额")
    private String amount;

    @ApiModelProperty("责任key")
    private String dutyKey;

    @ApiModelProperty("责任名称")
    private String dutyName;

    @ApiModelProperty("责任试算保费")
    private BigDecimal premium;

    @ApiModelProperty("责任动态描述")
    private String description;
    @ApiModelProperty("责任版本号")
    private Integer dutyVersion;

    @ApiModelProperty("有无病种详情 0:无；1：有")
    private Integer existedRiskDetail;

    @ApiModelProperty("责任id")
    private Integer dutyId;

    @ApiModelProperty("保障分类")
    private String guarantee;

    public static PlanBookRiskPreviewVO from(PlanBookDutyPreviewVO dutyPreviewVO) {
        PlanBookRiskPreviewVO result = new PlanBookRiskPreviewVO();
        result.setRiskKey(dutyPreviewVO.getRiskKey());
        result.setRiskName(dutyPreviewVO.getRiskName());
        result.setRiskType(dutyPreviewVO.getRiskType());
        result.setAmount(dutyPreviewVO.getAmount());
        return result;
    }
}
