package com.cfpamf.ms.insur.operation.base.service;

import com.cfpamf.common.ms.exception.MSBizNormalException;
import com.cfpamf.ms.insur.operation.auth.DataAuth;
import com.cfpamf.ms.insur.operation.auth.DataAuthFacade;
import com.cfpamf.ms.insur.operation.base.form.DataAuthPageForm;
import com.cfpamf.ms.insur.operation.base.response.CommonResult;
import com.cfpamf.ms.insur.operation.base.util.HttpRequestUtil;
import com.cfpamf.ms.insur.operation.fegin.bms.enums.ExcptEnum;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Objects;


/**
 * <AUTHOR> 2022/9/8 11:34
 */
@Service
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class DataAuthService {

    DataAuthFacade dataAuthFacade;


    /**
     * 数据权限处理
     *
     * @param page
     */
    public void dataAuth(DataAuthPageForm page) {

        DataAuth dataAuth = dataAuthByToken(HttpRequestUtil.getToken());
        if (Objects.nonNull(dataAuth.getRegionName())) {
            page.setRegionName(dataAuth.getRegionName());
        }

        if (Objects.nonNull(dataAuth.getOrgName())) {
            page.setOrgName(dataAuth.getOrgName());
        }

        if (Objects.nonNull(dataAuth.getUserId())) {
            page.setUserId(dataAuth.getUserId());
        }
    }

    /**
     * 数据权限
     *
     * @param token
     * @return
     */
    public DataAuth dataAuthByToken(String token) {
        if (StringUtils.isEmpty(token)) {
            throw new MSBizNormalException(ExcptEnum.BMS_AUTH_ERROR_801001.getCode(), "没有数据权限");
        }
        CommonResult<DataAuth> dataAuthCommonResult = dataAuthFacade.dataAuth(token);
        if (dataAuthCommonResult.isSuccess()) {
            return dataAuthCommonResult.getData();
        }
        throw new MSBizNormalException(ExcptEnum.BMS_AUTH_ERROR_801001.getCode(), "调用数据权限接口失败:" + dataAuthCommonResult.getMessage());
    }
}
