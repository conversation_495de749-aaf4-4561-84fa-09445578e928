package com.cfpamf.ms.insur.operation.qy.service;

import com.cfpamf.ms.insur.operation.qy.convter.OpeQyLabelRuleCvt;
import com.cfpamf.ms.insur.operation.qy.dao.OpeQyLabelRuleMapper;
import com.cfpamf.ms.insur.operation.qy.entity.OpeQyLabelRule;
import com.cfpamf.ms.insur.operation.qy.form.AnswerOptionForm;
import com.cfpamf.ms.insur.operation.qy.form.OpeQyLabelRuleForm;
import com.cfpamf.ms.insur.operation.qy.form.OpeQyQuestionAnswerForm;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.cp.bean.external.contact.FollowedUser;
import me.chanjar.weixin.cp.bean.external.contact.WxCpExternalContactInfo;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR> 2022/8/11 11:12
 */
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@AllArgsConstructor
@Service
@Slf4j
public class OpeQyLabelRuleService {

    WxCpProxyService proxyService;
    OpeQyQuestionService qyQuestionService;

    OpeQyLabelRuleMapper ruleMapper;

    /**
     * 自动打标签
     *
     * @param pagerId        问卷id
     * @param externalUserid 企微客户id
     */
    public void autoLabel(Long pagerId, String externalUserid) {

        log.info("自动打标签{} {}", pagerId, externalUserid);
        List<OpeQyLabelRule> opeQyLabelRules = ruleMapper.selectByPagerId(pagerId);
        if (CollectionUtils.isEmpty(opeQyLabelRules)) {
            return;
        }

        OpeQyQuestionAnswerForm answer = qyQuestionService.queryAnswer(pagerId, externalUserid);
        if (!canAddLabel(answer)) {
            return;
        }
        List<OpeQyLabelRuleForm> rules = OpeQyLabelRuleCvt.INS.questionPo2Form(opeQyLabelRules);


        List<String> addLabelIds = rules.stream()
                .filter(rule -> isEligible(answer, rule))
                .map(OpeQyLabelRuleForm::getLabelId).collect(Collectors.toList());

        if (!CollectionUtils.isEmpty(addLabelIds)) {
            addLabel(addLabelIds, externalUserid);
        }
    }


    /**
     * 添加标签
     *
     * @param addLabelIds
     * @param externalUserid
     */
    public void addLabel(List<String> addLabelIds, String externalUserid) {

        WxCpExternalContactInfo contactInfo = proxyService.contactDetail(externalUserid);

        for (FollowedUser fu : contactInfo.getFollowedUsers()) {
            List<String> addTags = addLabelIds.stream().filter(addTag -> Arrays.stream(fu.getTags()).noneMatch(tag -> Objects.equals(tag.getTagId(), addTag)))
                    .collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(addTags)) {
                log.info("给{} 插入{}个标签", externalUserid, addTags.size());
                proxyService.markTag(fu.getUserId(), externalUserid, addTags.toArray(new String[]{}), null);
            }

        }
    }

    private boolean isEligible(OpeQyQuestionAnswerForm answer, OpeQyLabelRuleForm rule) {

        List<AnswerOptionForm> answerDetail = answer.getAnswerDetail();

        //是否满足规则
        return rule.getLabelRule().getOptionIds().
                stream().allMatch(opId -> answerDetail.stream().anyMatch(s -> s.getOptionIds().contains(opId)));
    }

    /**
     * 判断是否可以打标签
     *
     * @param answerForm
     * @return
     */
    private boolean canAddLabel(OpeQyQuestionAnswerForm answerForm) {

        return Objects.equals(answerForm.getAnswerUserType(), 1) || Objects.equals(answerForm.getAnswerUserType(), 2);

    }

}
