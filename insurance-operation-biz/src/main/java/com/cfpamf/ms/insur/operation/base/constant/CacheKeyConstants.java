package com.cfpamf.ms.insur.operation.base.constant;

/**
 * 缓存key
 *
 * <AUTHOR>
 */
public class CacheKeyConstants {
    /**
     * 计划书产品因子
     */
    public final static String PROSPECTUS_PRODUCT_FACTOR = "PROSPECTUS_PRODUCT_FACTOR";

    /**
     * 脚本类型和编码缓存
     */
    public final static String GROOVY_TYPE_CODE = "GROOVY_TYPE_CODE";


    /**
     * 脚本类型缓存
     */
    public final static String GROOVY_TYPE = "GROOVY_TYPE_CODE";

    /**
     * 活动产品缓存
     */
    public final static String ACTIVITY_PRODUCT = "ACTIVITY_PRODUCT";
    /**
     * 活动产品规则缓存
     */
    public final static String ACTIVITY_PRODUCT_RULE = "ACTIVITY_PRODUCT_RULE";

    public final static String MSG_QUERY_CACHE = "ins:operation:msg:query";

    public final static String GOAL_ACHIEVE_CACHE = "ACTIVITY_GOAL_ACHIEVE_CACHE";

}
