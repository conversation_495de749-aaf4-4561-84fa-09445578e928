package com.cfpamf.ms.insur.operation.addCommission.dao;

import com.cfpamf.ms.insur.operation.addCommission.dto.AddCommissionSettlementPushDto;
import com.cfpamf.ms.insur.operation.addCommission.po.WhaleAddCommissionSettlementPush;
import com.cfpamf.ms.insur.operation.addCommission.po.WhalePreservationSettlementPushLog;
import com.cfpamf.ms.insur.operation.base.dao.CommonMapper;
import org.apache.ibatis.annotations.Mapper;
import tk.mybatis.mapper.entity.Example;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface WhaleAddCommissionSettlementPushMapper extends CommonMapper<WhaleAddCommissionSettlementPush> {

    List<AddCommissionSettlementPushDto> getListByRequestId(String requestId);
}
