package com.cfpamf.ms.insur.operation.fegin.wx.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class CustVisitGroupInfo {

    /**
     * 素材类型
     */
    @ApiModelProperty(value = "素材类型")
    private String marketingType;

    /**
     * 素材分类
     */
    @ApiModelProperty(value = "素材分类")
    private String marketingCategory;


    /**
     * 文案
     */
    @ApiModelProperty(value = "文案")
    private String content;

    /**
     * 互动人数
     */
    @ApiModelProperty(value = "互动人数")
    private String people;

    /**
     * 素材分类
     */
    @ApiModelProperty(value = "微信头像信息")
    private List<String> avatarUrlList;

    /**
     * 分享时间
     */
    @ApiModelProperty(value = "分享时间")
    private String shareTime;

    /**
     * 素材圈编码
     */
    @ApiModelProperty(value = "素材圈编码")
    private String marketingCode;

    @ApiModelProperty(value = "缩略图")
    private String firstPicUrl;

    @ApiModelProperty("一级分类编码")
    private String lv1CategoryCode;
    @ApiModelProperty("二级分类编码")
    private String lv2CategoryCode;
}
