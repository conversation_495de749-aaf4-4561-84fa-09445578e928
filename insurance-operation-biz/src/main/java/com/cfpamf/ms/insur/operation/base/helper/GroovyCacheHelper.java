package com.cfpamf.ms.insur.operation.base.helper;


import com.google.common.collect.Maps;
import lombok.experimental.UtilityClass;

import java.util.Objects;
import java.util.concurrent.ConcurrentMap;
import java.util.function.Supplier;

/**
 * 缓存帮组类  线程不安全
 *
 * <AUTHOR> 2020/3/23 14:48
 */
@UtilityClass
public class GroovyCacheHelper {

    public static ConcurrentMap<String, Object> CACHE_MAP = Maps.newConcurrentMap();

    public static <T> T getOrCache(String key, Supplier<T> lazy) {
        Object o = CACHE_MAP.get(key);
        if (o != null) {
            return (T) o;
        }
        T t = lazy.get();
        if (Objects.nonNull(t)) {
            CACHE_MAP.put(key, t);
        }
        return t;
    }

    public static void clearAll() {
        CACHE_MAP.clear();
    }

    public static void removeCache(String key) {
        CACHE_MAP.remove(key);
    }
}
