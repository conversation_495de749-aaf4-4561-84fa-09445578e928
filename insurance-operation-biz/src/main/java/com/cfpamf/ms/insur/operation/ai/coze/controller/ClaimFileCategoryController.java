package com.cfpamf.ms.insur.operation.ai.coze.controller;

import com.alibaba.fastjson.JSONObject;
import com.cfpamf.ms.insur.operation.ai.coze.CozeExecutor;
import com.cfpamf.ms.insur.operation.ai.coze.params.ClaimFileCategoryParams;
import com.cfpamf.ms.insur.operation.base.response.CommonResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/4/17
 * @Version 1.0
 */

@Slf4j
@Api(tags = "理赔材料归类")
@RestController
@RequestMapping(value = {"/ai/claim"})
public class ClaimFileCategoryController {


    @Autowired
    private CozeExecutor cozeExecutor;

    @ApiOperation("材料归类调用coze工作流")
    @PostMapping("/classify")
    public CommonResult<List<JSONObject>> aiCallBack(@RequestBody ClaimFileCategoryParams params) {
        return CommonResult.successResult(cozeExecutor.doWorkFlow(params));
    }

}
