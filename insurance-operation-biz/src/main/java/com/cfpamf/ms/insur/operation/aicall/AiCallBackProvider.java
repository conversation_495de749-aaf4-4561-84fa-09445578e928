package com.cfpamf.ms.insur.operation.aicall;

import com.cfpamf.ms.insur.operation.aicall.service.CallBackServer;
import com.cfpamf.ms.insur.operation.aicall.vo.CallBackVo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

/***
 * <AUTHOR>
 */
@Component
public class AiCallBackProvider {

    @Autowired
    ApplicationContext applicationContext;

    public CallBackVo processCallBack(String callBackResult, String provider){
        CallBackServer callBackServer = (CallBackServer) applicationContext.getBean(provider);
        CallBackVo callBackVo = callBackServer.processCallBack(callBackResult);
        return callBackVo;
    }
}
