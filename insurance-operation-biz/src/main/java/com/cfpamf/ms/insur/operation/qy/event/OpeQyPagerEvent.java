package com.cfpamf.ms.insur.operation.qy.event;

import com.cfpamf.ms.insur.operation.base.event.BaseEvent;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;

/**
 * <AUTHOR> 2022/8/11 14:15
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
@AllArgsConstructor
@NoArgsConstructor
public class OpeQyPagerEvent implements BaseEvent {

    Long pagerId;

    String externalUserid;
}
