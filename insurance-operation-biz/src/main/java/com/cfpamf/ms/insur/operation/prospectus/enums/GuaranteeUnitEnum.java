package com.cfpamf.ms.insur.operation.prospectus.enums;

/**
 * <AUTHOR>
 * @date 2021/5/24 18:59
 */
public enum GuaranteeUnitEnum {
    /**
     * 年
     */
    YEAR("年"),
    /**月
     *
     */
    MONTH("月"),
    /**
     *天
     */
    DAY("天"),
    /**
     *岁
     */
    TO_AGE("岁"),
    /**
     *终身
     */
    FOREVER("终身");
    String name;

    GuaranteeUnitEnum(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }
}
