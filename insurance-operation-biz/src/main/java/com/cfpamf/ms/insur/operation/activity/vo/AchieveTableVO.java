package com.cfpamf.ms.insur.operation.activity.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2022/5/9 10:08
 * @Version 1.0
 */
@Data
@ApiModel
public class AchieveTableVO {

    @ApiModelProperty("用户姓名")
    private String userName;

    @ApiModelProperty("用户id")
    private String userId;

    @ApiModelProperty("个人业绩")
    private String amount;

    @ApiModelProperty("目标业绩")
    private String goalAmount;

}
