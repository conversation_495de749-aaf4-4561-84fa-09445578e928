package com.cfpamf.ms.insur.operation.msg.pojo.dto;

import com.cfpamf.ms.insur.operation.msg.anno.MsgColumn;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

/**
 * <AUTHOR>
 * @Date 2021/9/29 8:51
 * @Version 1.0
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class OrgCustomColumnDTO {
    @MsgColumn(name = "地区", defaultShow = true)
    String regionName;
    @MsgColumn(name = "机构", defaultShow = true)
    String organizationName;
    @MsgColumn(name = "金额", defaultShow = true)
    String amts;
    @MsgColumn(name = "预获团建费", defaultShow = true)
    String proportion;
}
