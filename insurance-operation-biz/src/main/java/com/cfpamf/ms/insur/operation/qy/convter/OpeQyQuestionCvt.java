package com.cfpamf.ms.insur.operation.qy.convter;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.cfpamf.ms.insur.operation.qy.entity.OpeQyQuestion;
import com.cfpamf.ms.insur.operation.qy.entity.OpeQyQuestionAnswer;
import com.cfpamf.ms.insur.operation.qy.entity.OpeQyQuestionOption;
import com.cfpamf.ms.insur.operation.qy.form.*;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> 2022/6/23 10:42
 */
@Mapper(imports = {JSONObject.class,
        JSONArray.class,
        OpeQyQuestionOptionParamForm.class,
        OpeQyQuestionParamForm.class,
        AnswerOptionForm.class})
public interface OpeQyQuestionCvt {

    OpeQyQuestionCvt INS = Mappers.getMapper(OpeQyQuestionCvt.class);

    /**
     * 表单对象转换成po
     *
     * @param s
     * @return
     */
    @Mappings({
            @Mapping(target = "params", expression = "java(JSONObject.toJSONString(s.getParams()))")
    })
    OpeQyQuestion questionFrom2Po(OpeQyQuestionForm s);

    /**
     * 列表转化
     *
     * @param fs
     * @return
     */
    default List<OpeQyQuestion> questionFrom2Po(List<OpeQyQuestionForm> fs) {
        return fs.stream().map(this::questionFrom2Po).collect(Collectors.toList());
    }

    /**
     * 表单对象转换成po
     *
     * @param s
     * @return
     */
    @Mappings({
            @Mapping(target = "params", expression = "java(JSONObject.parseObject(s.getParams(), OpeQyQuestionParamForm.class))")
    })
    OpeQyQuestionForm questionPo2Form(OpeQyQuestion s);

    /**
     * 列表转化
     *
     * @param fs
     * @return
     */
    default List<OpeQyQuestionForm> questionPo2Form(List<OpeQyQuestion> fs) {
        return fs.stream().map(this::questionPo2Form).collect(Collectors.toList());
    }

    /**
     * po对象转换成Form
     *
     * @param s
     * @return
     */
    @Mappings({
            @Mapping(target = "params", expression = "java(JSONObject.parseObject(s.getParams(), OpeQyQuestionOptionParamForm.class))")
    })
    OpeQyQuestionOptionForm optionPo2Form(OpeQyQuestionOption s);

    default List<OpeQyQuestionOptionForm> optionPo2Form(List<OpeQyQuestionOption> ts) {
        return ts.stream().map(this::optionPo2Form).collect(Collectors.toList());
    }

    /**
     * 表单对象转换成po
     *
     * @param s
     * @return
     */
    @Mappings({
            @Mapping(target = "params", expression = "java(JSONObject.toJSONString(s.getParams()))")
    })
    OpeQyQuestionOption optionFrom2Po(OpeQyQuestionOptionForm s);

    /**
     * 列表转化
     *
     * @param fs
     * @return
     */
    default List<OpeQyQuestionOption> optionFrom2Po(List<OpeQyQuestionOptionForm> fs) {
        return fs.stream().map(this::optionFrom2Po).collect(Collectors.toList());
    }

    /**
     * @param s
     * @return
     */
    @Mappings({
            @Mapping(target = "answerDetail", expression = "java(JSONObject.toJSONString(s.getAnswerDetail()))")
    })
    OpeQyQuestionAnswer answerFrom2Po(OpeQyQuestionAnswerForm s);

    @Mappings({
            @Mapping(target = "answerDetail", expression = "java(JSONArray.parseArray(s.getAnswerDetail(), AnswerOptionForm.class))")
    })
    OpeQyQuestionAnswerForm answerPo2Form(OpeQyQuestionAnswer s);
}
