package com.cfpamf.ms.insur.operation.pco.service;

import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.cfpamf.ms.insur.operation.base.service.DataAuthService;
import com.cfpamf.ms.insur.operation.base.util.LambdaUtils;
import com.cfpamf.ms.insur.operation.dingtalk.dao.DingTalkSwFormInstanceDetailMapper;
import com.cfpamf.ms.insur.operation.dingtalk.dao.DingTalkSwFormInstanceMapper;
import com.cfpamf.ms.insur.operation.dingtalk.entity.DingTalkSwFormInstanceDetail;
import com.cfpamf.ms.insur.operation.pco.entity.Dictionary;
import com.cfpamf.ms.insur.operation.pco.query.PcoDayFormQuery;
import com.cfpamf.ms.insur.operation.pco.vo.PcoDayFormVo;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Example;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URL;
import java.net.URLEncoder;
import java.time.format.TextStyle;
import java.util.*;
import java.util.stream.Collectors;

import static com.alibaba.excel.EasyExcel.write;
import static com.alibaba.excel.EasyExcel.writerSheet;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class PcoDayScoreService {


    DingTalkSwFormInstanceMapper instanceMapper;

    DingTalkSwFormInstanceDetailMapper detailMapper;

    DataAuthService dataAuthService;

    ObjectMapper mapper;


    /**
     * 日打卡记录
     *
     * @param pageQuery
     * @return
     */
    public PageInfo<PcoDayFormVo> listDayScopes(PcoDayFormQuery pageQuery) {

        pageQuery.init();
        dataAuthService.dataAuth(pageQuery);
        PageInfo<PcoDayFormVo> res;
        if (!pageQuery.isAll()) {
            res = PageHelper.startPage(pageQuery.getPageNo(), pageQuery.getPageSize())
                    .doSelectPageInfo(() -> instanceMapper.selectPcoForms(pageQuery));
        } else {
            res = new PageInfo<>(instanceMapper.selectPcoForms(pageQuery));
        }
        setFormDetails(res.getList());
        return res;
    }

    public PageInfo<PcoDayFormVo> listDayScopesDaysOrderByForm(PcoDayFormQuery pageQuery) {

        pageQuery.setOrderBy("form_date");
        return listDayScopes(pageQuery);
    }

    public List<DingTalkSwFormInstanceDetail> listFormDetail(String formInstanceId) {
        return listDingTalkSwFormInstanceDetails(Collections.singletonList(formInstanceId));
    }


    private void setFormDetails(List<PcoDayFormVo> list) {
        if (!CollectionUtils.isEmpty(list)) {

            List<String> collect = list.stream().map(PcoDayFormVo::getFormInstanceId)
                    .collect(Collectors.toList());

            List<DingTalkSwFormInstanceDetail> details = listDingTalkSwFormInstanceDetails(collect);
            Map<String, List<DingTalkSwFormInstanceDetail>> map = LambdaUtils.groupBy(details
                    //过滤隐藏字段
                    .stream()
                    .filter(s -> !StringUtils.startsWith(s.getFormKey(), "HiddenField"))
                    .peek(s -> s.setAdminShow(!StringUtils.equals(s.getFormLabel(), "日期")))
                    .collect(Collectors.toList()), DingTalkSwFormInstanceDetail::getFormInstanceId);
            for (PcoDayFormVo formVo : list) {
                List<DingTalkSwFormInstanceDetail> forms = map.getOrDefault(formVo.getFormInstanceId(), Collections.emptyList());
                forms.sort(Comparator.comparing(DingTalkSwFormInstanceDetail::getId));
                formVo.setForms(forms);
                formVo.setWeek(formVo.getFormDate().getDayOfWeek().getDisplayName(TextStyle.FULL, Locale.SIMPLIFIED_CHINESE));
                formVo.setIsWrite("是");
            }
        }
    }

    private List<DingTalkSwFormInstanceDetail> listDingTalkSwFormInstanceDetails(List<String> collect) {
        Example example = new Example(DingTalkSwFormInstanceDetail.class);
        example.createCriteria().andIn("formInstanceId", collect);
        List<DingTalkSwFormInstanceDetail> details = detailMapper.selectByExample(example);
        return details;
    }


    /**
     * 下载excel
     *
     * @param pageQuery
     * @param response
     * @throws IOException
     */
    public void exportExcel(PcoDayFormQuery pageQuery, HttpServletResponse response) throws IOException {
        pageQuery.setAll(true);
        PageInfo<PcoDayFormVo> pcoDayFormVoPageInfo = listDayScopes(pageQuery);
        String fileName = "pco每日打卡记录" + System.currentTimeMillis() + ".xlsx";
        // 方案1

        List<Dictionary> dictionaries = instanceMapper.selectLabels();

        Map<String, String> labelMap = LambdaUtils.toMap(dictionaries, Dictionary::getCode, Dictionary::getName);
        List<PcoDayFormVo> list = pcoDayFormVoPageInfo.getList();
        List<Map<String, Object>> maps = list.stream()
                .map(s -> {
                    //组装模板参数
                    Map<String, Object> map = mapper.convertValue(s, Map.class);

                    for (int i = 0; i < s.getForms().size(); i++) {
                        DingTalkSwFormInstanceDetail dingTalkSwFormInstanceDetail = s.getForms().get(i);
                        map.put("pco_form_" + i, dingTalkSwFormInstanceDetail.getFormValue());
                        map.put("pco_form_" + i + "_scope", dingTalkSwFormInstanceDetail.getScope());
                    }
                    return map;
                }).collect(Collectors.toList());

        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        fileName = URLEncoder.encode(fileName, "UTF-8").replaceAll("\\+", "%20");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
        //模板地址用一个写死的oss地址 方便调整
        ExcelWriter excelWriter = write(response.getOutputStream()).withTemplate(
                new URL("https://safesfiles.oss-cn-beijing.aliyuncs.com/public/template/pco_day_form.xlsx").openStream()).build();
        WriteSheet writeSheet = writerSheet().build();
        excelWriter.fill(labelMap, writeSheet);
        excelWriter.fill(maps, writeSheet);
        excelWriter.finish();
    }

}
