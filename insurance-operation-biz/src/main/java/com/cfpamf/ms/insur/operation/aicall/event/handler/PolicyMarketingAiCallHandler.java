package com.cfpamf.ms.insur.operation.aicall.event.handler;

import com.alibaba.fastjson.JSONObject;
import com.cfpamf.ms.insur.operation.aicall.event.AiCallBackEvent;
import com.cfpamf.ms.insur.operation.base.event.BaseEventHandler;
import com.cfpamf.ms.insur.operation.fegin.xiaowhale.facade.ImsPublicFacade;
import com.google.common.eventbus.Subscribe;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@Slf4j
@AllArgsConstructor
public class PolicyMarketingAiCallHandler implements BaseEventHandler {
    @Autowired
    ImsPublicFacade imsPublicFacade;
    @Subscribe
    public void handlerEvent(AiCallBackEvent event){
        if("POLICY_NOTICE_STEP2".equals(event.getTaskType())){
            log.info("AiCallBackEvent事件触发：{}", JSONObject.toJSONString(event));
            String result = imsPublicFacade.sendCallBackSmsMessage(event.getOutSourceId().toString());
            log.info("回调结果：{}", result);
        }
    }
}
