package com.cfpamf.ms.insur.operation.msg.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR> 2021/7/13 14:46
 */
@ConfigurationProperties("dingtalk")
@Data
public class DingTalkProperties {

    String agentId;

    String appKey;

    String appSecret;

    String templateAppKey;

    String templateAppSecret;

    String templateRobotCode;
}
