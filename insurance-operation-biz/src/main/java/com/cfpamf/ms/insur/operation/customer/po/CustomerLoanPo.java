package com.cfpamf.ms.insur.operation.customer.po;

import com.cfpamf.ms.insur.operation.base.po.BasePO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import javax.persistence.Column;
import javax.persistence.Table;

/**
 * <AUTHOR>
 * @since Value(YearOfEra, 4, 19, EXCEEDS_PAD)'-'Value(MonthOfYear,2)'-'Value(DayOfMonth,2)' 'Value(HourOfDay,2)':'Value(MinuteOfHour,2)':'Value(DayOfMonth,2)
 */

@ApiModel("信贷客户")
@Table(name = "customer_loan")
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class CustomerLoanPo extends BasePO {


    @ApiModelProperty("客户id")
    Integer customerId;

    @ApiModelProperty("证件号码")
    String idNumber;

    @ApiModelProperty("客户名称")
    String customerName;

    @ApiModelProperty("贷款行业")
    String custIndustry;

    @ApiModelProperty("统一社会信用代码")
    String licenseNum;

    @ApiModelProperty("是否经营贷")
    Integer businessLoan;

    @ApiModelProperty("23年结清最大放款金额")
    @Column(name = "2023_settle_max_loan_amt")
    java.math.BigDecimal settleMaxLoanAmt2023;

    @ApiModelProperty("23年最新的年盈余")
    java.math.BigDecimal custNewAnnualSurplus;

    @ApiModelProperty("管控客户经理")
    String customerAdmin;

    @ApiModelProperty("转化时间")
    java.time.LocalDateTime conversionTime;

    @ApiModelProperty("转化订单")
    String conversionOrderId;

    @ApiModelProperty("转化状态")
    Integer conversionState;

    @ApiModelProperty("转化员工")
    String conversionEmp;

    @ApiModelProperty("二类计划")
    String twoPlan;

    @ApiModelProperty("行业排序 一类+二类 20 仅二类30 仅一类40 ")
    Integer industrySort;

    @ApiModelProperty("标签排序")
    Integer tagSort;

    @ApiModelProperty("金额排序")
    java.math.BigDecimal amtSort;

    @ApiModelProperty("是否生成待办")
    Integer todoState;

    @ApiModelProperty("是否在贷")
    Integer currentLoan;

}
