package com.cfpamf.ms.insur.operation.whale.preservation;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
@ApiModel("退保险种信息")
public class PreserveSurrenderProductVo {


    @ApiModelProperty(value = "计划编码")
    private String planCode;

    @ApiModelProperty(value = "计划")
    private String planName;

    @ApiModelProperty(value = "险种编码")
    private String productCode;

    @ApiModelProperty(value = "保单险种编号")
    private String policyProductCode;

    @ApiModelProperty(value = "险种名称")
    private String productName;

    @ApiModelProperty(value = "承保险种保费")
    private BigDecimal premium;

    @ApiModelProperty(value = "险种层的退保保费(非团险特有)")
    private BigDecimal surrenderPremium;

    @ApiModelProperty(value = "是否为主险：0=否，1=是")
    private Integer mainInsurance;

}
