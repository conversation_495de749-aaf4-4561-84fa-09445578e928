package com.cfpamf.ms.insur.operation.phoenix.service;

import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cfpamf.cmis.common.utils.DateUtils;
import com.cfpamf.common.ms.exception.MSBizNormalException;
import com.cfpamf.ms.common.insur.BizException;
import com.cfpamf.ms.insur.operation.aicall.AiCallProvider;
import com.cfpamf.ms.insur.operation.aicall.dao.AiFollowRecordMapper;
import com.cfpamf.ms.insur.operation.aicall.dao.AiProviderConfigurationMapper;
import com.cfpamf.ms.insur.operation.aicall.dao.RobortConfigurationMapper;
import com.cfpamf.ms.insur.operation.aicall.entity.AiFollowRecordPo;
import com.cfpamf.ms.insur.operation.aicall.entity.AiProviderConfiguration;
import com.cfpamf.ms.insur.operation.aicall.entity.RobortConfiguration;
import com.cfpamf.ms.insur.operation.aicall.enums.AIIntentEnums;
import com.cfpamf.ms.insur.operation.aicall.exceptions.CreateCallTaskException;
import com.cfpamf.ms.insur.operation.aicall.exceptions.UpdateCallTaskStatusException;
import com.cfpamf.ms.insur.operation.aicall.vo.AiCallTask;
import com.cfpamf.ms.insur.operation.aicall.vo.AiCallTaskDetail;
import com.cfpamf.ms.insur.operation.base.service.DataAuthService;
import com.cfpamf.ms.insur.operation.base.util.HttpRequestUtil;
import com.cfpamf.ms.insur.operation.base.util.LambdaUtils;
import com.cfpamf.ms.insur.operation.customer.dao.CustomerInterruptionMapper;
import com.cfpamf.ms.insur.operation.customer.enums.EnumServiceMode;
import com.cfpamf.ms.insur.operation.fegin.bms.enums.ExcptEnum;
import com.cfpamf.ms.insur.operation.msg.pojo.dto.EmpTextMsgDTO;
import com.cfpamf.ms.insur.operation.msg.service.CommonMsgPushService;
import com.cfpamf.ms.insur.operation.phoenix.config.WechatConfigProperties;
import com.cfpamf.ms.insur.operation.phoenix.dao.PhoenixEmpTodoMapper;
import com.cfpamf.ms.insur.operation.phoenix.dao.PhoenixWxMsgMapper;
import com.cfpamf.ms.insur.operation.phoenix.enums.EnumPhoenixWxMsgWxState;
import com.cfpamf.ms.insur.operation.phoenix.enums.EnumRenewalIntention;
import com.cfpamf.ms.insur.operation.phoenix.enums.EnumTodoBizType;
import com.cfpamf.ms.insur.operation.phoenix.enums.EnumTodoState;
import com.cfpamf.ms.insur.operation.phoenix.pojo.domain.PhoenixAiCallTaskDetail;
import com.cfpamf.ms.insur.operation.phoenix.pojo.domain.PhoenixEmpTodoStat;
import com.cfpamf.ms.insur.operation.phoenix.pojo.dto.PhoenixEmpRenewTodo;
import com.cfpamf.ms.insur.operation.phoenix.pojo.excel.PhoenixEmpTodoBackExcel;
import com.cfpamf.ms.insur.operation.phoenix.pojo.po.PhoenixEmpTodo;
import com.cfpamf.ms.insur.operation.phoenix.pojo.po.PhoenixWxMsg;
import com.cfpamf.ms.insur.operation.phoenix.pojo.query.PhoenixEmpTodoBackQuery;
import com.cfpamf.ms.insur.operation.phoenix.pojo.query.PhoenixEmpTodoQuery;
import com.cfpamf.ms.insur.operation.phoenix.pojo.vo.*;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.xxl.job.core.context.XxlJobHelper;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.mp.bean.template.WxMpTemplateData;
import me.chanjar.weixin.mp.bean.template.WxMpTemplateMessage;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Example;

import javax.servlet.http.HttpServletResponse;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.text.MessageFormat;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/5/6 09:30
 */
@Service
@Slf4j
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class PhoenixEmpTodoService {

    PhoenixEmpTodoMapper phoenixEmpTodoMapper;

    WxApiService wxApiService;

    PhoenixWxMsgMapper phoenixWxMsgMapper;

    WechatConfigProperties configProperties;

    CustomerInterruptionMapper customerInterruptionMapper;

    CommonMsgPushService commonMsgPushService;

    DataAuthService dataAuthService;

    AiCallProvider aiCallProvider;

    RobortConfigurationMapper robortConfigurationMapper;

    AiProviderConfigurationMapper aiProviderConfigurationMapper;

    AiFollowRecordMapper aiFollowRecordMapper;

    /**
     * 列表查询
     *
     * @param query
     * @return
     */
    public PhoenixEmpTodoCountVo countTodo(PhoenixEmpTodoQuery query) {
        String userId = HttpRequestUtil.getUserId();
        Integer interruptionTodoCount = phoenixEmpTodoMapper.selectPersonTodoCount(userId,Arrays.asList("INTERRUPTION"));
        Integer renewShortTodoCount = phoenixEmpTodoMapper.selectPersonTodoCount(userId,Arrays.asList("RENEW_SHORT"));
        Integer renewLongTodoCount = phoenixEmpTodoMapper.selectPersonTodoCount(userId,Arrays.asList("RENEW_LONG"));
        PhoenixEmpTodoCountVo vo = new PhoenixEmpTodoCountVo();
        vo.setJobNumber(userId);
        vo.setInterruptionTodoCount(interruptionTodoCount);
        vo.setShortRenewalTodoCount(renewShortTodoCount);
        vo.setLongRenewalTermTodoCount(renewLongTodoCount);
        return vo;
    }

    /**
     * 列表查询
     *
     * @param query
     * @return
     */
    public PageInfo<PhoenixEmpTodoVO> list(PhoenixEmpTodoQuery query) {
        String userId = HttpRequestUtil.getUserId();
        query.setUserId(userId);
        PageInfo<PhoenixEmpTodoVO> info = PageHelper.startPage(query.getPage(), query.getSize())
                .doSelectPageInfo(() -> phoenixEmpTodoMapper.selectByQuery(query));
        return info;
    }

    /**
     * 列表查询V1
     * @param query
     * @return
     */
    public PageInfo<PhoenixEmpTodoVO> listV1(PhoenixEmpTodoQuery query) {
        String userId = HttpRequestUtil.getUserId();
        query.setUserId(userId);
        String orderType = query.getOrderType();
        if(StringUtils.isNotBlank(orderType)){
            orderType = orderType.toLowerCase();
            query.setOrderType(orderType);
        }
        String bizType = query.getBizType();
        if(StringUtils.isBlank(bizType)){
            throw new MSBizNormalException(ExcptEnum.PARAMS_ERROR.getCode(),"业务类型不能为空");
        }
        switch (bizType){
            case "RENEW_SHORT":
                return queryShortRenewalTodoList(query);
            case "RENEW_LONG":
                return queryLongRenewalTermTodoList(query);
            case "INTERRUPTION":
                return queryInterruptionTodoList(query);
            default:
                throw new MSBizNormalException(ExcptEnum.PARAMS_ERROR.getCode(),"业务类型参数错误:"+bizType);
        }
    }

    /**
     * 列表查询V1
     * @param query
     * @return
     */
    public PhoenixEmpTodoTabCountVo countTabTodo(PhoenixEmpTodoQuery query) {
        String userId = HttpRequestUtil.getUserId();
        query.setUserId(userId);
        String bizType = query.getBizType();
        if(StringUtils.isBlank(bizType)){
            throw new MSBizNormalException(ExcptEnum.PARAMS_ERROR.getCode(),"业务类型不能为空");
        }
        switch (bizType){
            case "RENEW_SHORT":
                return countShortRenewalTabTodo(query);
            case "RENEW_LONG":
                return countLongRenewalTermTabTodo(query);
            case "INTERRUPTION":
                return countInterruptionTabTodo(query);
            default:
                throw new MSBizNormalException(ExcptEnum.PARAMS_ERROR.getCode(),"业务类型参数错误:"+bizType);
        }
    }

    private PhoenixEmpTodoTabCountVo countShortRenewalTabTodo(PhoenixEmpTodoQuery query){
        query.setTabType("red_tab");
        Integer redTab = phoenixEmpTodoMapper.countShortRenewalTabTodo(query);
        query.setTabType("yellow_tab");
        Integer yellowTab = phoenixEmpTodoMapper.countShortRenewalTabTodo(query);
        PhoenixEmpTodoTabCountVo vo = new PhoenixEmpTodoTabCountVo();
        vo.setJobNumber(query.getUserId());
        vo.setYellowTab(yellowTab);
        vo.setRedTab(redTab);
        return vo;
    }

    private PhoenixEmpTodoTabCountVo countLongRenewalTermTabTodo(PhoenixEmpTodoQuery query){
        query.setTabType("red_tab");
        Integer redTab = phoenixEmpTodoMapper.countLongRenewalTermTabTodo(query);
        query.setTabType("yellow_tab");
        Integer yellowTab = phoenixEmpTodoMapper.countLongRenewalTermTabTodo(query);
        PhoenixEmpTodoTabCountVo vo = new PhoenixEmpTodoTabCountVo();
        vo.setJobNumber(query.getUserId());
        vo.setYellowTab(yellowTab);
        vo.setRedTab(redTab);
        return vo;
    }

    private PhoenixEmpTodoTabCountVo countInterruptionTabTodo(PhoenixEmpTodoQuery query){
        query.setTabType("red_tab");
        Integer redTab = phoenixEmpTodoMapper.countInterruptionTabTodo(query);
        query.setTabType("yellow_tab");
        Integer yellowTab = phoenixEmpTodoMapper.countInterruptionTabTodo(query);
        PhoenixEmpTodoTabCountVo vo = new PhoenixEmpTodoTabCountVo();
        vo.setJobNumber(query.getUserId());
        vo.setYellowTab(yellowTab);
        vo.setRedTab(redTab);
        return vo;
    }

    private PageInfo<PhoenixEmpTodoVO> queryInterruptionTodoList(PhoenixEmpTodoQuery query){
        log.info("长期险待办任务列表查询:{}",JSON.toJSONString(query));
        Integer page = query.getPage();
        Integer size = query.getSize();
        PageInfo<PhoenixEmpTodoVO> data = PageHelper.startPage(page, size).doSelectPageInfo(() -> phoenixEmpTodoMapper.interruptionTodoList(query));
        List<PhoenixEmpTodoVO> todoList = data.getList();
        if(CollectionUtils.isEmpty(todoList)){
            return data;
        }
        MessageFormat r1 = new MessageFormat("已断保{0}天");
        LocalDateTime now = LocalDateTime.now();
        todoList.stream().forEach(vo->{
            LocalDateTime invalidTime = vo.getLastInterruptionTime();
            if(invalidTime==null){
                return;
            }
            long daysBetween = ChronoUnit.DAYS.between(invalidTime,now);
            daysBetween=Math.abs(daysBetween);
            String remark = r1.format(new Object[]{daysBetween});
            vo.setRemark(remark);
        });
        return data;
    }

    private PageInfo<PhoenixEmpTodoVO> queryLongRenewalTermTodoList(PhoenixEmpTodoQuery query){
        log.info("长期险待办任务列表查询:{}",JSON.toJSONString(query));
        Integer page = query.getPage();
        Integer size = query.getSize();
        PageInfo<PhoenixEmpTodoVO> data = PageHelper.startPage(page, size).doSelectPageInfo(() -> phoenixEmpTodoMapper.longRenewalTermTodoList(query));
        List<PhoenixEmpTodoVO> todoList = data.getList();
        if(CollectionUtils.isEmpty(todoList)){
            return data;
        }
        MessageFormat r1 = new MessageFormat("剩余{0}天到期");
        MessageFormat r2 = new MessageFormat("宽限期剩余{0}天到期");
        LocalDateTime now = LocalDateTime.now();
        todoList.stream().forEach(vo->{
            LocalDateTime invalidTime = vo.getDueTime();
            Integer graceDays = vo.getGraceDays();
            if(invalidTime==null){
                return;
            }
            if(invalidTime.isAfter(now)){
                long daysBetween = ChronoUnit.DAYS.between(now,invalidTime);
                daysBetween=Math.abs(daysBetween);
                String remark = r1.format(new Object[]{daysBetween});
                vo.setRemark(remark);
            }
            else{
                long daysBetween = ChronoUnit.DAYS.between(invalidTime,now);
                daysBetween=Math.abs(daysBetween);
                String remark = r2.format(new Object[]{graceDays-daysBetween});
                vo.setRemark(remark);
            }
        });
        return data;
    }

    private PageInfo<PhoenixEmpTodoVO> queryShortRenewalTodoList(PhoenixEmpTodoQuery query){
        log.info("续保待办任务列表查询:{}",JSON.toJSONString(query));
        Integer page = query.getPage();
        Integer size = query.getSize();
        PageInfo<PhoenixEmpTodoVO> data = PageHelper.startPage(page, size).doSelectPageInfo(() -> phoenixEmpTodoMapper.shortRenewalTodoList(query));
        List<PhoenixEmpTodoVO> todoList = data.getList();
        if(CollectionUtils.isEmpty(todoList)){
            return data;
        }
        MessageFormat r1 = new MessageFormat("剩余{0}天到期");
        MessageFormat r2 = new MessageFormat("宽限期剩余{0}天到期");
        LocalDateTime now = LocalDateTime.now();
        todoList.stream().forEach(vo->{
            LocalDateTime invalidTime = vo.getInvalidTime();
            LocalDateTime renewalEndTime = vo.getRenewalEndTime();
            if(invalidTime==null){
                return;
            }
            if(invalidTime.isAfter(now)){
                long daysBetween = ChronoUnit.DAYS.between(now,invalidTime);
                daysBetween=Math.abs(daysBetween);
                String remark = r1.format(new Object[]{daysBetween});
                vo.setRemark(remark);
            }
            else if(renewalEndTime!=null){
                long daysBetween = ChronoUnit.DAYS.between(now,renewalEndTime);
                daysBetween=Math.abs(daysBetween);
                String remark = r2.format(new Object[]{daysBetween});
                vo.setRemark(remark);
            }
        });
        return data;
    }


    public Integer queryPersonTodoCount(String userId, List<String> bizTypes){
        return phoenixEmpTodoMapper.selectPersonTodoCount(userId,bizTypes);
    }

    /**
     * 生成续保待办
     */
    public void initRenewTdo() {

        int i1 = phoenixEmpTodoMapper.insertRenewShortTdo();
        log.info("成功生成短险续保待办{}行", i1);
        XxlJobHelper.log("成功生成短险续保待办{}行", i1);
        int i2 = phoenixEmpTodoMapper.insertRenewLongTdo();
        log.info("成功生成长险续期待办{}行", i2);
        XxlJobHelper.log("成功生成长险续期待办{}行", i2);
        phoenixEmpTodoMapper.updateRenewShortTdo();

        int i3 = phoenixEmpTodoMapper.updateRenewShortTdoCustomerAdmin();
        log.info("待办管护经理变更处理{}行", i3);
        phoenixEmpTodoMapper.updateRenewLongTdo();
    }

    /**
     * 生成断保待办
     */
    public void initInterruptionTdo(String pt) {

        int i = phoenixEmpTodoMapper.insertInterruptionTdo(pt);
        log.info("成功生成断保待办{}行", i);
    }

    /**
     * 生成异业转化待办
     */
    @Transactional(rollbackFor = Exception.class)
    public void initLoanTdo() {
        String batchNo = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd")) + EnumTodoBizType.LOAN.name();
        int i = phoenixEmpTodoMapper.insertLoanTdo(batchNo);
        log.info("生成异业转化待办{}行", i);
    }


    /**
     * 完成待办
     *
     * @param bizType  业务类型
     * @param targetId 目标id
     */
    public void finishTodo(EnumTodoBizType bizType, String targetId,Integer followSortNo,String intention) {
        log.info("完成待办-{} {}：{}:{}", bizType,followSortNo, targetId,intention);
        if (StringUtils.isBlank(targetId)) {
            return;
        }

        Example example = new Example(PhoenixEmpTodo.class);
        example.createCriteria()
                .andEqualTo("targetId", targetId)
                .andEqualTo("state", "TODO")
                .andEqualTo("bizType", bizType);
        List<PhoenixEmpTodo> select = phoenixEmpTodoMapper.selectByExample(example);
        for (PhoenixEmpTodo todo : select) {

            PhoenixEmpTodo phoenixEmpTodo = new PhoenixEmpTodo();
            phoenixEmpTodo.setId(todo.getId());
            phoenixEmpTodo.setFollowSortNo(followSortNo);
            if (EnumRenewalIntention.UNWILLING.getCode().equals(intention) || EnumRenewalIntention.TRANSFER.getCode().equals(intention)) {
                phoenixEmpTodo.setState(EnumTodoState.DONE);
                phoenixEmpTodo.setRemark(EnumRenewalIntention.UNWILLING.getCode().equals(intention)?"续保跟进":"续保");
                phoenixEmpTodo.setFinishTime(LocalDateTime.now());
            }
            phoenixEmpTodoMapper.updateByPrimaryKeySelective(phoenixEmpTodo);
        }
    }


    /**
     * 批量完成待办
     *
     * @param bizType
     * @param targetIds
     * @param remark
     */
    public void finishTodo(EnumTodoBizType bizType, List<String> targetIds, String remark) {


        log.info("完成待办-{} {}：{}", bizType, remark, targetIds);
        if (CollectionUtils.isEmpty(targetIds)) {
            return;
        }
        PhoenixEmpTodo update = new PhoenixEmpTodo();
        update.setState(EnumTodoState.DONE);
        update.setRemark(remark);
        update.setFinishTime(LocalDateTime.now());


        Example example = new Example(PhoenixEmpTodo.class);
        example.createCriteria()
                .andIn("targetId", targetIds)
                .andEqualTo("state", EnumTodoState.TODO)
                .andEqualTo("bizType", bizType);

        phoenixEmpTodoMapper.updateByExampleSelective(update, example);

    }

    /**
     * 完成待办
     *
     * @param bizType  业务类型
     * @param targetId 目标id
     * @param remark   备注
     */
    public void finishTodo(EnumTodoBizType bizType, String targetId, String remark) {
        log.info("完成待办-{} {}：{}", bizType, remark, targetId);
        if (StringUtils.isBlank(targetId)) {
            return;
        }

        Example example = new Example(PhoenixEmpTodo.class);
        example.createCriteria()
                .andEqualTo("targetId", targetId)
                .andEqualTo("state", EnumTodoState.TODO)
                .andEqualTo("bizType", bizType);
        List<PhoenixEmpTodo> select = phoenixEmpTodoMapper.selectByExample(example);
        for (PhoenixEmpTodo todo : select) {

            PhoenixEmpTodo phoenixEmpTodo = new PhoenixEmpTodo();
            phoenixEmpTodo.setId(todo.getId());
            phoenixEmpTodo.setState(EnumTodoState.DONE);
            phoenixEmpTodo.setRemark(remark);
            phoenixEmpTodo.setFinishTime(LocalDateTime.now());
            phoenixEmpTodoMapper.updateByPrimaryKeySelective(phoenixEmpTodo);
        }
    }
    /**
     * 完成待办
     *
     * @param bizType  业务类型
     * @param targetId 目标id
     * @param remark   备注
     * @param followSortNo  跟进排序
     * @param intention 跟进意向
     */
    public void finishTodo(EnumTodoBizType bizType, String targetId, String remark,Integer followSortNo,String intention) {
        log.info("完成待办-{} {}：{}", bizType, remark, targetId);
        if (StringUtils.isBlank(targetId)) {
            return;
        }

        Example example = new Example(PhoenixEmpTodo.class);
        example.createCriteria()
                .andEqualTo("targetId", targetId)
                .andEqualTo("state", EnumTodoState.TODO)
                .andEqualTo("bizType", bizType);
        List<PhoenixEmpTodo> select = phoenixEmpTodoMapper.selectByExample(example);
        for (PhoenixEmpTodo todo : select) {

            PhoenixEmpTodo phoenixEmpTodo = new PhoenixEmpTodo();
            phoenixEmpTodo.setId(todo.getId());
            phoenixEmpTodo.setFollowSortNo(followSortNo);
            if(!EnumServiceMode.LOSE_CONTACT.getCode().equals(intention)) {
                phoenixEmpTodo.setState(EnumTodoState.DONE);
                phoenixEmpTodo.setRemark(remark);
                phoenixEmpTodo.setFinishTime(LocalDateTime.now());
            }
            phoenixEmpTodoMapper.updateByPrimaryKeySelective(phoenixEmpTodo);
        }
    }

    /**
     * 提醒待办数量
     */
    public void notifyTdo() {
        List<PhoenixEmpTodoStat> stats = phoenixEmpTodoMapper.selectEmpTdoNum();
        List<PhoenixEmpTodoStat> todoStatList = phoenixEmpTodoMapper.selectMongoEmpTdoNum();
        stats.addAll(todoStatList);
        Map<String, List<PhoenixEmpTodoStat>> empMap = LambdaUtils.groupBy(stats, PhoenixEmpTodoStat::getJobNumber);

//        // 钉钉异步通知
//        List<EmpTextMsgDTO> msgs = empMap.entrySet().stream().map(entry -> {
//
//            List<String> policyNos = phoenixEmpTodoMapper.selectByJobNumber(entry.getKey());
//            List<PhoenixEmpTodoStat> val = entry.getValue();
//
//            Integer shortAndLongAmt = 0;
//            if (!policyNos.isEmpty()) {
//                if (phoenixEmpTodoMapper.selectShortAmt(policyNos) != null) {
//                    shortAndLongAmt += phoenixEmpTodoMapper.selectShortAmt(policyNos);
//                }
//
//                if (phoenixEmpTodoMapper.selectLongAmt(policyNos) != null) {
//                    shortAndLongAmt += phoenixEmpTodoMapper.selectLongAmt(policyNos);
//                }
//            }
//            StringBuffer sb = new StringBuffer("【保险客户跟进通知】\n您有");
//            Optional<PhoenixEmpTodoStat> any = val.stream().filter(s -> Objects.equals(s.getBizType(), EnumTodoBizType.INTERRUPTION)).findAny();
//            any.ifPresent(s -> {
//                sb.append(s.getTdoNum()).append("个断保");
//            });
//            int sum = val.stream().filter(s -> Objects.equals(s.getBizType(), EnumTodoBizType.RENEW_LONG)
//                            || Objects.equals(s.getBizType(), EnumTodoBizType.RENEW_SHORT)).
//                    mapToInt(PhoenixEmpTodoStat::getTdoNum).sum();
//            if (sum > 0) {
//                if (any.isPresent()) {
//                    sb.append("、");
//                }
//                sb.append(sum).append("个续期/续投");
//            }
//            sb.append("客户待跟进，请当日前往小鲸向海公众号-个人中心-待办事项处理。");
//            if (any.isPresent()) {
//                sb.append("据测算预计可给您带来").append(BigDecimal.valueOf(shortAndLongAmt + any.get().getTdoNum() * 0.15 * 130).setScale(2,BigDecimal.ROUND_HALF_UP)).append("元保费，请当日前往小鲸向海公众号-个人中心-待办事项处理。");
//            } else {
//                sb.append("据测算预计可给您带来").append(shortAndLongAmt).append("元保费，请当日前往小鲸向海公众号-个人中心-待办事项处理。");
//            }
//            return new EmpTextMsgDTO(entry.getKey(), sb.toString());
//        }).collect(Collectors.toList());
//
//        commonMsgPushService.sendRobotTextAsync(msgs);

        // 微信同步统计
        empMap.forEach((key, val) -> {

            StringBuffer sb = new StringBuffer("您有");

            Optional<PhoenixEmpTodoStat> any = val.stream().filter(s -> Objects.equals(s.getBizType(), EnumTodoBizType.INTERRUPTION)).findAny();
            any.ifPresent(s -> sb.append(s.getTdoNum()).append("个断保"));
            int sum = val.stream().filter(s -> Objects.equals(s.getBizType(), EnumTodoBizType.RENEW_LONG)
                            || Objects.equals(s.getBizType(), EnumTodoBizType.RENEW_SHORT)).
                    mapToInt(PhoenixEmpTodoStat::getTdoNum).sum();
            if (sum > 0) {
                if (any.isPresent()) {
                    sb.append("、");
                }
                sb.append(sum).append("个续期/续投");
            }
            sb.append("客户待跟进。");
            PhoenixEmpTodoStat s = val.get(0);
            WxMpTemplateMessage templateMessage = new WxMpTemplateMessage();
            buildWechatWhaleData(templateMessage, sb.toString());
            templateMessage.setTemplateId(configProperties.getTempMessageChannelCommon());
            templateMessage.setToUser(s.getWxOpenId());
            templateMessage.setUrl(configProperties.getTodoInterruptionUrl());
            boolean b = wxApiService.sendTemplateMsg(templateMessage);
            //保存发送记录
            PhoenixWxMsg msg = new PhoenixWxMsg();
            msg.setState(b ? EnumPhoenixWxMsgWxState.SUCCESS : EnumPhoenixWxMsgWxState.ERROR);
            msg.setTemplateId(configProperties.getTempMessageChannelCommon());
            msg.setJobNumber(s.getJobNumber());
            msg.setOpenId(s.getWxOpenId());
            phoenixWxMsgMapper.insertUseGeneratedKeys(msg);
        });
    }

    public void notifyTimeoutTdo() {
        List<PhoenixEmpTodoStat> stats = phoenixEmpTodoMapper.selectTimeoutEmpTdoNum();
        Map<String, List<PhoenixEmpTodoStat>> empMap = LambdaUtils.groupBy(stats, PhoenixEmpTodoStat::getJobNumber);

        // 钉钉异步通知
        List<EmpTextMsgDTO> msgs = empMap.entrySet().stream().map(entry -> {

            List<String> policyNos = phoenixEmpTodoMapper.selectByJobNumber(entry.getKey());
            List<PhoenixEmpTodoStat> val = entry.getValue();
            Integer shortAndLongAmt = 0;
            if (!policyNos.isEmpty()) {
                if (phoenixEmpTodoMapper.selectShortAmt(policyNos) != null) {
                    shortAndLongAmt += phoenixEmpTodoMapper.selectShortAmt(policyNos);
                }

                if (phoenixEmpTodoMapper.selectLongAmt(policyNos) != null) {
                    shortAndLongAmt += phoenixEmpTodoMapper.selectLongAmt(policyNos);
                }
            }
            StringBuffer sb = new StringBuffer("【保险客户跟进通知】\n您有");
            Optional<PhoenixEmpTodoStat> any = val.stream().filter(s -> Objects.equals(s.getBizType(), EnumTodoBizType.INTERRUPTION)).findAny();
            any.ifPresent(s -> sb.append(s.getTdoNum()).append("个断保"));
            int sum = val.stream().filter(s -> Objects.equals(s.getBizType(), EnumTodoBizType.RENEW_LONG)
                            || Objects.equals(s.getBizType(), EnumTodoBizType.RENEW_SHORT)).
                    mapToInt(PhoenixEmpTodoStat::getTdoNum).sum();
            if (sum > 0) {
                if (any.isPresent()) {
                    sb.append("、");
                }
                sb.append(sum).append("个续期/续投");
            }
            sb.append("客户待跟进，请当日前往小鲸向海公众号-个人中心-待办事项处理。");
            if (any.isPresent()) {
                sb.append("据测算预计可给您带来").append(BigDecimal.valueOf(shortAndLongAmt + any.get().getTdoNum() * 0.15 * 130).setScale(2,BigDecimal.ROUND_HALF_UP).toString()).append("元保费，请当日前往小鲸向海公众号-个人中心-待办事项处理。");
            } else {
                sb.append("据测算预计可给您带来").append(shortAndLongAmt).append("元保费，请当日前往小鲸向海公众号-个人中心-待办事项处理。");
            }
            any.ifPresent(s -> sb.append("温馨提示：您有").append(s.getTdoTimeoutNum()).append("个断保待办将在今天24点逾期，届时客户将被分配给机构PCO跟进，请知悉"));
            return new EmpTextMsgDTO(entry.getKey(), sb.toString());
        }).collect(Collectors.toList());

        commonMsgPushService.sendRobotTextAsync(msgs);
    }

    private WxMpTemplateMessage buildWechatWhaleData(WxMpTemplateMessage wtm, String message) {
        wtm.addData(new WxMpTemplateData("keyword1", DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:dd").format(LocalDateTime.now())));
        wtm.addData(new WxMpTemplateData("keyword2", "客户跟进"));
        wtm.addData(new WxMpTemplateData("keyword3", message));
        return wtm;
    }

    public PageInfo<PhoenixEmpTodoBackVo> listBack(PhoenixEmpTodoBackQuery query) {
        dataAuthService.dataAuth(query);

        List<PhoenixEmpTodoBackVo> phoenixEmpTodoBackDtoList = phoenixEmpTodoMapper.list(query);
        if (!CollectionUtils.isEmpty(phoenixEmpTodoBackDtoList)) {

            for (PhoenixEmpTodoBackVo phoenixEmpTodoBackVo : phoenixEmpTodoBackDtoList) {
                String todoProperty = phoenixEmpTodoBackVo.getTodoProperty();
                JSONObject jsonObject = JSON.parseObject(todoProperty);
                Integer renewalTerm = (Integer) jsonObject.get("renewalTerm");
                String orderId = phoenixEmpTodoMapper.selectOrdIdByRenewalTerm(renewalTerm);
                phoenixEmpTodoBackVo.setOrderId(orderId);
            }
            PageInfo<PhoenixEmpTodoBackVo> info = new PageInfo<>(phoenixEmpTodoBackDtoList);

            if (query.isQueryPage()) {
                info.setPageNum(query.getPageNo());
                info.setSize(query.getPageSize());
                if(query.getForbidCountSwitch()){
                    info.setTotal(0);
                    info.setHasNextPage(true);
                }else{
                    info.setTotal(phoenixEmpTodoMapper.countPhoenixTodo(query));
                    info.setHasNextPage(query.getPageNo() * query.getPageSize() < info.getTotal());
                }
            }
            return info;
        }
        return new PageInfo<>();
    }

    public void downloadBackList(PhoenixEmpTodoBackQuery query, HttpServletResponse response) {
        dataAuthService.dataAuth(query);
        List<PhoenixEmpTodoBackVo> list = phoenixEmpTodoMapper.listAll(query);
        List<PhoenixEmpTodoBackExcel> list1 = new ArrayList<>(list.size());
        List<AiFollowRecordPo> recordPos = aiFollowRecordMapper.getListByOutSourceId(list.stream().map(PhoenixEmpTodoBackVo::getPhoenixId).collect(Collectors.toList()));

        if (!CollectionUtils.isEmpty(list)) {
            for (PhoenixEmpTodoBackVo phoenixEmpTodoBackVo : list) {
                PhoenixEmpTodoBackExcel phoenixEmpTodoBackExcel = new PhoenixEmpTodoBackExcel();

                if (!CollectionUtils.isEmpty(recordPos)) {
                    Map<Long, AiFollowRecordPo> map = recordPos.stream().collect(
                            Collectors.toMap(AiFollowRecordPo::getOutSourceId, record -> record));
                    if (map.containsKey(phoenixEmpTodoBackVo.getPhoenixId())) {
                        BeanUtils.copyProperties(map.get(phoenixEmpTodoBackVo.getPhoenixId()), phoenixEmpTodoBackVo);
                    }
                }

                phoenixEmpTodoBackExcel.copyProperties(phoenixEmpTodoBackVo);
                String customerAdminName = phoenixEmpTodoBackExcel.getCustomerAdminName();
                String customerAdminId = phoenixEmpTodoBackVo.getCustomerAdminId();
                if (customerAdminName != null && customerAdminId != null) {
                    phoenixEmpTodoBackExcel.setCustomerAdminName(customerAdminName.concat("-").concat(customerAdminId));
                }
                phoenixEmpTodoBackExcel.setIntent(StringUtils.isEmpty(phoenixEmpTodoBackExcel.getIntent()) ? ""
                        : AIIntentEnums.dict(phoenixEmpTodoBackExcel.getIntent()));


                phoenixEmpTodoBackExcel.setClaim(phoenixEmpTodoBackVo.getClaim());
                phoenixEmpTodoBackExcel.setFinishClaim(phoenixEmpTodoBackVo.getFinishClaim());
                list1.add(phoenixEmpTodoBackExcel);
            }
        }
        try (OutputStream os = response.getOutputStream()) {
            String fileName = URLEncoder.encode("待办列表" + new SimpleDateFormat("yyyyMMdd").format(new Date()) + ".xlsx", StandardCharsets.UTF_8.name());
            response.setHeader("Content-disposition", "attachment; filename=" + fileName);
            response.setContentType("application/octet-stream");
            EasyExcel.write(response.getOutputStream(), PhoenixEmpTodoBackExcel.class)
                    .sheet("待办列表").doWrite(list1);
        } catch (Exception e) {
            throw new BizException("文件下载失败", e);
        }
    }

    public void initInterruptionNewTdo() {
        int i = phoenixEmpTodoMapper.insertListNewTodo();
        log.info("成功生成断保待办{}行", i);
    }

    @Transactional(rollbackFor = Exception.class)
    public void handlerTransferTdo() {
        List<PhoenixEmpRenewTodo> empRenewTodoList = phoenixEmpTodoMapper.selectByRenewTodo();
        if(empRenewTodoList.size()==0){
            return;
        }
        phoenixEmpTodoMapper.insertCustomerAdminLog(empRenewTodoList);
        for (PhoenixEmpRenewTodo phoenixEmpRenewTodo : empRenewTodoList) {
            phoenixEmpTodoMapper.updateOrderCustomerAdminV3(phoenixEmpRenewTodo.getOldAdminId(), phoenixEmpRenewTodo.getNewAdminId(), phoenixEmpRenewTodo.getNewAdminJobCode(), phoenixEmpRenewTodo.getNewAdminId()
                    , phoenixEmpRenewTodo.getNewCustomerAdminOrgCode(), phoenixEmpRenewTodo.getIdNumber(), phoenixEmpRenewTodo.getCustomerId());
        }
        phoenixEmpTodoMapper.insertListPcoTransferTdo(empRenewTodoList);
        phoenixEmpTodoMapper.updateListTransferTdo();
    }

    /**
     * 创建AI外呼任务
     * 1、筛选待办：所有该次断保后未添加过人工跟进记录/断保后人工跟进记录都=联系不上
     * 2、按完成计划从近往远创建（根据机器人个数判断每天筛选多少数据）
     */
    public void createAiCallTask() {
        //获取机器人配置个数
        RobortConfiguration robortConfiguration = new RobortConfiguration();
        robortConfiguration.setEnabledFlag(0);
        List<RobortConfiguration> robotConfigurationList=robortConfigurationMapper.select(robortConfiguration);
        if (CollectionUtils.isEmpty(robotConfigurationList)) {
            return;
        }
        log.info("机器人配置信息：{}",JSONObject.toJSONString(robotConfigurationList));

        //断保机器人个数
        List<RobortConfiguration> interruptionRobortList = robotConfigurationList.stream()
                .filter(x->EnumTodoBizType.INTERRUPTION.name().equals(x.getTaskType()))
                .collect(Collectors.toList());
        log.info("断保机器人配置信息：{}",JSONObject.toJSONString(interruptionRobortList));
        //续保机器人个数
        List<RobortConfiguration> renewRobortList = robotConfigurationList.stream()
                .filter(x->EnumTodoBizType.RENEW_SHORT.name().equals(x.getTaskType()))
                .collect(Collectors.toList());
        log.info("续保机器人配置信息：{}",JSONObject.toJSONString(interruptionRobortList));

        //获取机器人拨打个数
        AiProviderConfiguration queryParam = new AiProviderConfiguration();
        queryParam.setAiProvider(robotConfigurationList.get(0).getAiProvider());
        AiProviderConfiguration aiProviderConfiguration = aiProviderConfigurationMapper.selectOne(queryParam);
        if (Objects.isNull(aiProviderConfiguration) || Objects.isNull(aiProviderConfiguration.getCountPerTask())) {
            return;
        }
        log.info("机器人拨打个数：{}",JSONObject.toJSONString(aiProviderConfiguration));
        Integer count = aiProviderConfiguration.getCountPerTask();

        //筛选断保需要AI外呼的待办任务
        List<PhoenixAiCallTaskDetail> details = phoenixEmpTodoMapper.getAIPhoenixInfo((CollectionUtils.isEmpty(interruptionRobortList)?0:interruptionRobortList.size())*count);

        //续保AI外呼待办任务
        List<PhoenixAiCallTaskDetail> renewDetails = phoenixEmpTodoMapper.getRenewAIPhoenixInfo((CollectionUtils.isEmpty(renewRobortList)?0:renewRobortList.size())*count);
        if (CollectionUtils.isEmpty(renewDetails) && CollectionUtils.isEmpty(details)) {
            return;
        }
        log.info("断保外呼待办：{}，续保外呼待办：{}",JSONObject.toJSONString(details),JSONObject.toJSONString(renewDetails));

        AiCallTask aiCallTask = initAiCallTask(details);
        AiCallTask aiRenewCallTask = initAiCallTask(renewDetails);

        //创建AI外呼任务
        try {
            //断保外呼
            aiCall(aiCallTask);
            //续保外呼
            aiCall(aiRenewCallTask);
        } catch (CreateCallTaskException e) {
            log.warn("智能外呼任务创建失败",e);
            throw new MSBizNormalException(ExcptEnum.AI_CALL_ERROR.getCode(), "智能外呼任务创建失败",e);
        } catch (UpdateCallTaskStatusException e) {
            log.warn("智能外呼任务创建失败",e);
            throw new MSBizNormalException(ExcptEnum.AI_CALL_ERROR.getCode(), "智能外呼任务创建失败",e);
        }
    }

    /**
     * 外呼
     * @param aiCallTask
     */
    private void aiCall(AiCallTask aiCallTask) throws CreateCallTaskException, UpdateCallTaskStatusException {
        if (!Objects.isNull(aiCallTask)) {
            aiCallProvider.createAiCallTask(aiCallTask);
        }
    }

    /**
     * 初始化AI外呼任务参数
     * @param details
     * @return
     */
    private AiCallTask initAiCallTask(List<PhoenixAiCallTaskDetail> details) {
        if(CollectionUtils.isEmpty(details)) {
            return null;
        }

        AiCallTask aiCallTask = new AiCallTask();
        aiCallTask.setTaskType(details.get(0).getBizType());
        aiCallTask.setTaskName("外呼任务"+System.currentTimeMillis());
        aiCallTask.setDialStartDate(LocalDate.now().toString());

        log.info("AI外呼任务参数：{}",JSON.toJSONString(details));

        List<AiCallTaskDetail> aiCallTaskDetails = details.stream().map(x->{
            AiCallTaskDetail detail = new AiCallTaskDetail();
            BeanUtils.copyProperties(x,detail);
            Map<String,String> map = new HashMap<>(3);
            map.put("productName",initProductName(x.getProductName(),x.getProductType()));
            map.put("customerName",x.getCustomerName());
            map.put("salutation",x.getSalutation());
            map.put("endTime",x.getEndTime());
            map.put("产品名称",initProductName(x.getProductName(),x.getProductType()));
            map.put("姓名",x.getCustomerName());
            detail.setVarMap(map);
            return detail;
        }).collect(Collectors.toList());

        aiCallTask.setAiCallTaskDetailList(aiCallTaskDetails);
        return aiCallTask;
    }

    /**
     * 初始化产品名称
     * 1、产品类型为车险的，返回车险
     * 2、产品名称带“驾乘险的”，返回驾乘意外险
     * 3、产品名称带“车险“的，返回车险
     * 4、产品名称带“（”的，去除（及（内的内容
     * @param productName 产品名称
     * @return String
     */
    public String initProductName(String productName,String productType) {
        if (StringUtils.isNotBlank(productType) && "CX".equals(productType)) {
            return "车险";
        }
        if (StringUtils.isNotBlank(productName) && productName.contains("驾乘")) {
            return "驾乘意外险";
        } else if (productName.contains("车险")) {
            return "车险";
        } else if (productName.contains("(") || productName.contains("（")){
            return productName.replaceAll("\\(.*?\\)", "")
                    .replaceAll("（.*?）", "")
                    .replaceAll("（.*?\\)", "")
                    .replaceAll("\\(.*?）", "");
        } else {
            return productName;
        }
    }

    public void notifyConventions() {
        List<WxLoanFollowList> wxLoanFollowLists = phoenixEmpTodoMapper.getBefore1DayList();
        String timeFormat = "{0}月{1}日";
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(wxLoanFollowLists)) {
            for (WxLoanFollowList wxLoanFollowList : wxLoanFollowLists) {
                String nextTime = MessageFormat.format(timeFormat,wxLoanFollowList.getNextTime().getMonthValue()
                        , wxLoanFollowList.getNextTime().getDayOfMonth());

                WxMpTemplateMessage templateMessage = new WxMpTemplateMessage();
                templateMessage.setTemplateId(configProperties.getFollowMessage());
                templateMessage.setToUser(wxLoanFollowList.getWxOpenId());
                templateMessage.setUrl(getCustomerDetailUrl(wxLoanFollowList.getCustomerId()));
                templateMessage.addData(new WxMpTemplateData("first", String.format("")));
                templateMessage.addData(new WxMpTemplateData("keyword1", wxLoanFollowList.getCustomerName(), "#0000FF"));
                templateMessage.addData(new WxMpTemplateData("keyword2", "A类客户转化", "#0000FF"));
                templateMessage.addData(new WxMpTemplateData("keyword3", "您与客户约定" + nextTime + "微信联系", "#0000FF"));
                templateMessage.addData(new WxMpTemplateData("remark", ""));
                wxApiService.sendTemplateMsg(templateMessage);
            }
        }
        log.info("约定通知 完成：" + wxLoanFollowLists.size());
    }

    public String getCustomerDetailUrl(String customerId) {
        try {
            String url = URLEncoder.encode(String.format(configProperties.getCustomerLoanDetailUrl(), customerId));
            return String.format(configProperties.getFrontCustomerDetailUrl(), url,"");
        } catch (Exception e) {
            log.error("", e);
        }
        return null;
    }
    public void initLoanNewTdo() {
        int i = phoenixEmpTodoMapper.insertLoanNewTodo();
        log.info("成功生成异业待办{}行", i);
    }

    public Integer listBackCnt(PhoenixEmpTodoBackQuery query) {
        dataAuthService.dataAuth(query);
        return phoenixEmpTodoMapper.countPhoenixTodo(query);
    }

    public void initLoanFirstTdo() {
        int i = phoenixEmpTodoMapper.insertLoanFirstNewTodo();
        log.info("成功生成A类客户待办{}行", i);
        phoenixEmpTodoMapper.updateLoanFirstTdo();
    }
    public void deleteNotFollowerInterruption(List<Long> ids){
        phoenixEmpTodoMapper.deleteNotFollowerInterruption(ids);
    }

    public List<Long> listNotFollowerInterruptionTodoId1000ByPage(){
        return phoenixEmpTodoMapper.queryNotFollowerInterruptionTodoId();
    }
}

