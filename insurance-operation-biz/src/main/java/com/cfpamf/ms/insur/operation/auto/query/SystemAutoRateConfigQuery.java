package com.cfpamf.ms.insur.operation.auto.query;

import com.cfpamf.ms.insur.operation.base.form.DataAuthPageForm;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.ToString;
import lombok.experimental.FieldDefaults;

/**
 * 微信产品Query
 *
 * <AUTHOR>
 **/
@Data
@ToString(callSuper = true)
@FieldDefaults(level = AccessLevel.PRIVATE)
public class SystemAutoRateConfigQuery extends DataAuthPageForm {

    /**
     * 产品主键
     */
    @ApiModelProperty(value = "产品主键")
    Integer productId;
}
