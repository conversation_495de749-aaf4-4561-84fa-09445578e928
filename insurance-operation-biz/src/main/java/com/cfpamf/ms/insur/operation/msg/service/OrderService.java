package com.cfpamf.ms.insur.operation.msg.service;

import com.cfpamf.ms.insur.operation.base.annotaions.DataSourceReadOnly;
import com.cfpamf.ms.insur.operation.msg.dao.OrderMapper;
import com.cfpamf.ms.insur.operation.msg.dwdao.DwGoalMapper;
import com.cfpamf.ms.insur.operation.msg.dwdao.DwRankMonthMapper;
import com.cfpamf.ms.insur.operation.msg.pojo.dto.RegionGoalDTO;
import com.cfpamf.ms.insur.operation.msg.pojo.query.RegionGoalTraceQuery;
import com.cfpamf.ms.insur.operation.msg.pojo.query.StatOrderQuery;
import com.cfpamf.ms.insur.operation.msg.pojo.query.StataRankQuery;
import com.cfpamf.ms.insur.operation.msg.pojo.vo.StatOrderDTO;
import com.cfpamf.ms.insur.operation.msg.pojo.vo.StatRankVO;
import lombok.AccessLevel;
import lombok.experimental.FieldDefaults;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR> 2021/7/7 14:01
 */
@FieldDefaults(level = AccessLevel.PRIVATE)
@Service
public class OrderService {

    @Autowired
    OrderMapper orderMapper;

    @Autowired
    DwRankMonthMapper rankMonthMapper;

    @Autowired
    DwGoalMapper dwGoalMapper;

    @DataSourceReadOnly
    public StatOrderDTO statBase(StatOrderQuery query) {
        final StatOrderDTO statOrderDto = orderMapper.selectOrder(query);
        final StatOrderDTO qq = orderMapper.selectOrder(query);
        return statOrderDto;
    }


    /**
     * 区域排名
     *
     * @return
     */
    public List<StatRankVO> listRankArea(LocalDate rptDate, int limit) {
        return rankMonthMapper.listRankArea(initRankQuery(rptDate, limit));
    }

    /**
     * 分支排名
     *
     * @return
     */
    public List<StatRankVO> listRankBch(LocalDate rptDate, int limit) {
        return rankMonthMapper.listRankBch(initRankQuery(rptDate, limit));
    }

    /**
     * 员工排名
     *
     * @return
     */
    public List<StatRankVO> listRankEmp(LocalDate rptDate, int limit) {
        return rankMonthMapper.listRankEmp(initRankQuery(rptDate, limit));
    }

    private StataRankQuery initRankQuery(LocalDate start, int limit) {
        final StataRankQuery query = new StataRankQuery();
        query.setStartDate(start);
        query.setMaxRows(limit);
        return query;
    }

    /**
     *
     * @param query
     * @return
     */
    public List<RegionGoalDTO> listRegionGoalByTime(RegionGoalTraceQuery query){
        return dwGoalMapper.listRegionGoalByTime(query);
    }

}
