package com.cfpamf.ms.insur.operation.addCommission.service.preservation;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cfpamf.common.ms.exception.MSBizNormalException;
import com.cfpamf.ms.insur.operation.addCommission.dao.WhaleAddCommissionSettlementPushMapper;
import com.cfpamf.ms.insur.operation.addCommission.dto.*;
import com.cfpamf.ms.insur.operation.addCommission.enums.SettlementEventTypeEnum;
import com.cfpamf.ms.insur.operation.addCommission.po.WhaleAddCommissionSettlementPush;
import com.cfpamf.ms.insur.operation.base.util.DateUtils;
import com.cfpamf.ms.insur.operation.fegin.bms.enums.ExcptEnum;
import com.cfpamf.ms.insur.operation.whale.model.PreservationDetail;
import com.cfpamf.ms.insur.operation.whale.model.WhaleContract;
import com.cfpamf.ms.insur.operation.whale.preservation.PreservationInsuredBaseInfo;
import com.cfpamf.ms.insur.operation.whale.preservation.PreservationInsuredChangeForm;
import com.cfpamf.ms.insur.operation.xj.service.impl.WhalePublicApiBaseService;
import lombok.AccessLevel;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 被保人信息变更处理服务类
 * 该类用于处理保险操作中被保人信息变更的保存操作
 * 继承自AbstractPreservationHandleService，实现了insuredInfoChangePreservation组件的功能
 *
 * <AUTHOR>
 */
@Component("insuredInfoChangePreservation")
@FieldDefaults(level = AccessLevel.PRIVATE)
@Slf4j
public class InsuredChangeHandleService extends AbstractPreservationHandleService {

    @Autowired
    WhalePublicApiBaseService whalePublicApiBaseService;
    @Autowired
    WhaleAddCommissionSettlementPushMapper whaleAddCommissionSettlementPushMapper;

    /**
     * 初始化推送数据
     * 对比保单号和被保人信息的变更，更新推送数据中的被保人信息
     *
     * @param operationDto 操作数据传输对象，包含变更前后的被保人信息
     * @param pushDtos 推送数据列表
     * @return 更新后的推送数据列表
     */
    @Override
    public PreservationSettlementDto doAddCommissionCorrect(PreservationOperationDto operationDto, List<AddCommissionSettlementPushDto> pushDtos, PreservationDto preservationDto) {
        log.info("被保人信息变更加佣记录处理,operationDto:{},pushDto:{}", JSONObject.toJSONString(operationDto), JSONObject.toJSONString(pushDtos));

        PreservationDetail preservationDetail = preservationDto.getPreservationDetail();
        PreservationOperationDetail after = operationDto.getAfter();
        PreservationOperationDetail before = operationDto.getBefore();

        String afterIdCard = after.getInsuredIdCard();
        String afterInsuredName = after.getInsuredName();
        String beforeIdCard = before.getInsuredIdCard();
        String beforeInsuredName = before.getInsuredName();

        // 只有当推送数据不为空且被保人信息发生变更时，才更新推送数据中的被保人信息
        if (CollectionUtils.isNotEmpty(pushDtos) &&
                (!Objects.equals(afterIdCard, beforeIdCard) || !Objects.equals(afterInsuredName, beforeInsuredName))) {
            pushDtos.forEach(item -> {
                item.setInsuredIdCard(afterIdCard);
                item.setInsuredName(afterInsuredName);
                item.setEventTypeCode(operationDto.getSettlementEventType().getEventCode());
                item.setPreservationEffectTime(preservationDetail.getPreservationEffectTime());
                item.setApplyTime(DateUtils.toDateString(preservationDetail.getCreateTime()));
                item.setAccountTime(DateUtils.toDateString(preservationDetail.getCreateTime()));
            });
        }

        String requestId = preservationDto.getRequestId();

        //插入业财结算推送数据明细
        //过滤核算中的数据（核算中的额数据不需要推送业财）
        List<AddCommissionSettlementPushDto> result = pushDtos.stream()
                .filter(dto->!Objects.isNull(dto.getSettlementState()) && dto.getSettlementState()!=0)
                .collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(result)) {
            List<WhaleAddCommissionSettlementPush> settlementPushes = result.stream().map(dto -> {
                WhaleAddCommissionSettlementPush settlementPush = new WhaleAddCommissionSettlementPush();
                BeanUtils.copyProperties(dto, settlementPush);
                settlementPush.setRequestId(requestId);
                return settlementPush;
            }).collect(Collectors.toList());
            whaleAddCommissionSettlementPushMapper.insertList(settlementPushes);
        }

        PreservationSettlementDto settlementDto = new PreservationSettlementDto();
        settlementDto.setRequestId(requestId);
        settlementDto.setSettlementPushDtos(result);
        log.info("被保人信息变更加佣记录推送结算数据：{}", JSONObject.toJSONString(settlementDto));
        return settlementDto;
    }

    /**
     * 填充操作数据传输对象
     * 从保存的详细信息中提取被保人变更信息，填充到操作数据传输对象中
     *
     * @param operationDto 操作数据传输对象，用于接收被保人变更信息
     * @param preservationDto 保存的详细信息，包含保单号和被保人变更表单
     */
    @Override
    public void getOperationDto(PreservationOperationDto operationDto, PreservationDto preservationDto) {
        PreservationDetail preservationDetail = preservationDto.getPreservationDetail();
        if (Objects.isNull(preservationDetail)) {
            log.error("加佣保全-被保人信息变更保全操作明细为空{}",JSONObject.toJSONString(preservationDto));
            throw new UnsupportedOperationException("被保人信息变更保全操作明细为空");
        }

        String policyNo = preservationDetail.getPolicyCode();
        PreservationInsuredChangeForm insuredChangeForm = preservationDetail.getInsuredChangeForm();
        PreservationInsuredBaseInfo after = insuredChangeForm.getAfter();
        PreservationInsuredBaseInfo before = insuredChangeForm.getBefore();

        // 确保变更后的被保人信息非空
        if (after == null) {
            log.info("被保人变更后信息为空:{}", JSON.toJSONString(preservationDetail));
            throw new MSBizNormalException(ExcptEnum.PARAMS_ERROR.getCode(), "被保人变更信息为空");
        }
        // 确保变更前的被保人信息非空
        if (before == null) {
            log.info("被保人变更前信息为空:{}", JSON.toJSONString(preservationDetail));
            throw new MSBizNormalException(ExcptEnum.PARAMS_ERROR.getCode(), "被保人变更信息为空");
        }

        String beforeIdCard = before.getInsuredIdCard();
        String beforeInsuredName = before.getInsuredName();
        String afterIdCard = after.getInsuredIdCard();
        String afterInsuredName = after.getInsuredName();

        // 只有当被保人姓名或身份证号发生变更时，才设置变更前后的详细信息到操作数据传输对象
        if (!Objects.equals(afterInsuredName, beforeInsuredName) || !Objects.equals(afterIdCard, beforeIdCard)) {
            operationDto.setBefore(new PreservationOperationDetail(policyNo, "", beforeInsuredName,
                    beforeIdCard, before.getInsuredCode()));
            operationDto.setAfter(new PreservationOperationDetail(policyNo, "", afterInsuredName,
                    afterIdCard, after.getInsuredCode()));
            operationDto.setSettlementEventType(SettlementEventTypeEnum.INSURED_BASE_INFO_CHANGE);
        }
    }
}
