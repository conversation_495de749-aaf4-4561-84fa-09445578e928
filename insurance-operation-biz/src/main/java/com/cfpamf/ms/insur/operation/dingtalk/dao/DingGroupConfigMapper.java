package com.cfpamf.ms.insur.operation.dingtalk.dao;

import java.util.List;

import com.cfpamf.ms.insur.operation.base.dao.CommonMapper;
import com.cfpamf.ms.insur.operation.dingtalk.form.DingGroupConfigForm;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.data.domain.Pageable;
import com.cfpamf.ms.insur.operation.dingtalk.entity.DingGroupConfig;

/**
 * ;(ding_group_config)表数据库访问层
 * <AUTHOR> 赵小凯
 * @date : 2023-9-14
 */
@Mapper
public interface DingGroupConfigMapper extends CommonMapper<DingGroupConfig> {
    public List<DingGroupConfig> queryAllByPage(DingGroupConfigForm form);

    List<DingGroupConfig> queryByGroupIdAndGroupContextId(@Param("groupId") String groupId, @Param("groupContextId") String groupContextId);
}