package com.cfpamf.ms.insur.operation.fegin.bms.enums;

import com.cfpamf.common.ms.enums.MSEnum;

/**
 * 系统自定义异常
 *
 * <AUTHOR>
 */
public enum ExcptEnum implements MSEnum {

    /**
     * http 404
     */
    HTTP_PARAM_ERROR_404("404", "http请求的接口不存在"),

    /**
     * http 参数 错误
     */
    HTTP_PARAM_ERROR_000097("000097", "http请求参数错误"),

    /**
     * http method 错误
     */
    HTTP_METHOD_ERROR_000098("000098", "http请求错误"),

    /**
     * 泛华接口不稳定失败
     */
    FH_ERROR_000099("000099", "系统繁忙请稍后重试"),

    /**
     * 解密失败
     */
    DECRYPT_ERROR_100000("100000", "解密失败"),

    /**
     * BMS系统授权失败
     */
    BMS_AUTH_FAIL_100001("100001", "用户名或者密码错误"),

    /**
     * 泛华授权失败
     */
    FANHUA_AUTH_FAIL_200001("200001", "泛华授权失败"),

    /**
     * 泛华 access token 获取失败
     */
    FANHUA_ACCESS_TOKEN_FAIL_200011("200011", "泛华 access token 获取失败"),

    /**
     * 泛华车辆平台信息获取失败
     */
    FANHUA_GET_CAR_FAIL_200002("200002", "泛华车辆平台信息获取失败"),

    /**
     * 产品没有上线
     */
    PRODUCT_NOT_ONLINE_201001("201001", "该产品已下线或不可用"),

    /**
     * 产品已经上线不能修改或者删除
     */
    PRODUCT_IS_ONLINE_201002("201002", "产品已经上线不能修改或者删除"),

    /**
     * 产品计划配置不正确
     */
    PRODUCT_PLAN_ERROR_201003("201003", "产品计划配置不正确"),

    /**
     * 产品计划价格配置不正确
     */
    PRODUCT_PLAN_PRICE_ERROR_201004("201004", "产品计划价格配置不正确"),
    PRODUCT_PLAN_ORG_ERROR_201006("201004", "销售区域限制未配置"),

    /**
     * 产品计划名称必须唯一
     */
    PRODUCT_ID_UNIQUE_201005("201005", "产品计划名称必须唯一"),

    /**
     * 产品计划泛华ID必须唯一
     */
    PRODUCT_ID_UNIQUE_201015("201015", "产品计划泛华ID必须唯一"),

    /**
     * 产品提成配置不正确
     */
    COMMISSION_ERROR_201025("201025", "产品提成配置不正确"),

    /**
     * 名称或者编码不唯一
     */
    CODE_UNIQUE_201006("201006", "名称或者编码不唯一"),

    /**
     * 产品保险条款配置不正确
     */
    PRODUCT_ERROR_201007("201007", "产品保险条款配置不正确"),

    /**
     * 泛华订单信息获取失败
     */
    PRODUCT_ERROR_201008("201008", "泛华订单信息获取失败"),

    /**
     * 泛华订单不存在
     */
    PRODUCT_ERROR_201009("201009", "泛华订单不存在"),

    /**
     * token获取异常
     */
    PRODUCT_ERROR_201010("201010", "token获取异常"),

    /**
     * 产品计划获取失败
     */
    PRODUCT_ERROR_201011("201011", "产品计划获取失败"),

    /**
     * 提成配置重复
     */
    COMMISSION_ERROR_201012("201012", "提成配置有重复时间段"),

    /**
     * 支付佣金不能大于计算佣金
     */
    COMMISSION_ERROR_201013("201013", "支付佣金不能大于等于结算佣金"),

    /**
     * 佣金比例配置错误
     */
    COMMISSION_ERROR_201014("201014", "佣金比例配置错误"),

    /**
     * 授权失败
     */
    PERMISSION_DENIED_401000("401000", "授权失败"),

    /**
     * 没有登陆权限
     */
    PERMISSION_DENIED_401001("401001", "没有登陆权限"),

    /**
     * 验证码输入错误
     */
    VERIFICATION_CODE_ERROR_401002("401002", "验证码无效，请刷新重试"),

    /**
     * HttpsURLConnection 加载失败
     */
    HTTPS_URL_CONNECTION_ERROR_403001("403001", "HttpsURLConnection 加载失败"),

    /**
     * SSLSocket 加载失败
     */
    SSL_SCOCKET_ERROR_403002("403002", "SSLSocket 加载失败"),

    /**
     * 加密字符串不能为空
     */
    ENCRYPT_ERROR_501001("501001", "加密字符串不能为空"),

    /**
     * RSA加密泛华头部信息失败
     */
    ENCRYPT_ERROR_501011("501011", "RSA加密泛华头部信息失败"),

    /**
     * Excel版本不支持
     */
    EXCEL_VERSION_NOT_SUPPORT_501002("501002", "Excel版本不支持"),

    /**
     * OSSClient加载失败
     */
    OSS_ERROR_501003("501003", "OSSClient加载失败"),

    /**
     * 验证码生成失败
     */
    FILE_READ_ERROR_501004("501004", "验证码生成失败"),

    /**
     * 操作超时
     */
    OPERATION_TIMEOUT_504005("501005", "操作超时"),

    /**
     * 文件下载失败
     */
    FILE_DOWNLOAD_FAIL_501006("501006", "文件下载失败"),

    /**
     * 文件上传失败
     */
    FILE_UPLOAD_FAIL_501007("501007", "文件上传失败"),

    /**
     * Excel文件格式解析失败
     */
    FILE_EXCEL_READ_501008("501008", "Excel文件格式解析失败"),

    /**
     * 填写保单操作超时授权失效
     */
    INVALID_TOKEN_501009("501009", "请勿重复提交"),

    /**
     * 微信授权失效
     */
    INVALID_TOKEN_501019("501019", "微信授权失效"),

    /**
     * 订单创建失败
     */
    ORDER_ERROR_501010("5010010", "订单创建失败"),

    /**
     * 产品配置错误
     */
    PRODUCT_ERROR_501011("5010011", "产品配置错误"),

    /**
     * 产品职业分类配置错误
     */
    PRODUCT_OCPN_ERROR_501111("501111", "产品职业分类配置错误"),

    /**
     * 微信授权失败
     */
    WEIXIN_AUTH_FAIL_601000("601000", "微信授权失败"),

    /**
     * 微信js api授权失败
     */
    WEIXIN_AUTH_FAIL_601010("601010", "微信js api授权失败"),

    /**
     * 微信非法授权回调
     */
    WEIXIN_AUTH_FAIL_601001("601001", "微信非法授权回调"),

    /**
     * 该微信账号已经绑定员工
     */
    WEIXIN_BIND_DUPLICATE_601002("601002", "该微信账号已经绑定员工"),

    /**
     * 用户名或密码错误，微信绑定失败
     */
    WEIXIN_BIND_ERROR_601003("601003", "用户名或密码错误，微信绑定失败"),

    /**
     * 微信openId无效
     */
    TOKEN_AUTH_FAIL_601005("601005", "微信openId无效"),

    /**
     * 微信绑定失败
     */
    TOKEN_BIND_FAIL_601006("601006", "微信绑定失败"),

    /**
     * 该员工已经绑定其他微信账号
     */
    WEIXIN_BIND_DUPLICATE_601007("601007", "该员工已经绑定其他微信账号"),

    WEIXIN_PARAM_601007("601008", "微信转换openid 每次最多一百个"),

    /**
     * 计划名称或者泛华产品Id不能为空
     */
    PLAN_FIELD_NOT_EMPYT_701001("701001", "计划名称或者泛华产品Id不能为空"),

    /**
     * 该保险公司已经被使用，不能删除！
     */
    PLAN_FIELD_NOT_EMPYT_701002("701002", "该保险公司已经被使用，不能删除！"),

    /**
     * OMS授权失败
     */
    BMS_AUTH_ERROR_801001("801001", "BMS授权失败"),

    /**
     * OMS授权失败
     */
    EVIL_ORDER_ERROR_801002("801002", "恶意刷单账户已被锁定"),

    /**
     * 用户已经存在
     */
    USER_EXISTS_ERROR_801003("801003", "用户已经存在"),

    /**
     * 二维码生成失败
     */
    QRCODE_ERROR_801004("801004", "二维码生成失败"),

    /**
     * 二维码白框边距太大，生成失败
     */
    QRCODE_ERROR_801005("801005", "二维码白框边距太大，生成失败"),

    /**
     * 保单下载失败
     */
    POLICY_ERROR_801005("801005", "保单下载失败"),

    /**
     * 改产品已经收藏
     */
    COLLECTION_ERROR_801006("801006", "该产品已经收藏"),

    /**
     * 订单已经存在
     */
    ORDER_ERROR_801007("801007", "订单号已存在，请勿重复录入"),

    /**
     * 业务繁忙请稍后重试
     */
    ORDER_ERROR_801008("801008", "业务繁忙请稍后重试"),

    /**
     * 无法获取bms token
     */
    GATEWAY_ERROR_TOKEN_801009("801009", "无法获取网关token"),

    /**
     * 无法获取网关token
     */
    NO_DATA_PERMISSION_801010("801010", "没有数据权限"),

    /**
     * 岗位角色配置出错
     */
    POST_ROLE_ERROR("801011", "岗位角色配置出错"),

    /**
     * 理赔工作流加载失败
     */
    CLAIM_WORK_FLOW_ERROR("801012", "理赔工作流加载失败"),

    /**
     * 理赔申请失败
     */
    CLAIM_APPLY_ERROR("801013", "当前有未结案的理赔申请"),
    /**
     * 退保申请失败
     */
    CLAIM_CANCEL_ERROR("801023", "当前保单已经申请过退保"),

    /**
     * 身份证识别失败
     */
    OCR_ID_CARD_ERROR("801014", "身份证识别失败"),

    /**
     * 身份证识别失败
     */
    OCR_NO_PERMISSION_ERROR("801015", "没有扫描身份证识别权限"),

    /**
     * 今日使用识别次数已达上限
     */
    OCR_TIMES_OVER_LIMIT_ERROR("801016", "今日使用识别次数已达上限"),

    /**
     * 员工工号错误
     */
    USER_NOT_EXIST("801016", "员工工号错误"),

    /**
     * 更换前后客户经理工号相同
     */
    CUSTOMER_ADMIN_SAME("801017", "变更前后负责员工不能相同"),

    /**
     * 导出行数超过Excel最大行数限制
     */
    EXCEL_MAX_LIMIT_ERROR("801018", "导出行数超过Excel最大行数限制"),

    /**
     * 账户不存在
     */
    ACCOUNT_NOT_EXIST("801019", "账户不存在"),

    /**
     * 没有生成海报的权限
     */
    POSTER_ACCESS_DENY("801020", "没有生成海报的权限"),

    /**
     * 该产品海报已经存在配置
     */
    POSTER_ADD_ERROR("801021", "该产品海报已经存在配置"),

    /**
     * 该产品海报配置错误
     */
    POSTER_SETTING_ERROR("801022", "该产品海报配置错误"),

    /**
     * 生成海报失败
     */
    POSTER_GEN_ERROR("801023", "生成海报失败"),

    /**
     * 微信发送模板消息失败失败
     */
    WX_TEMPLATE_MESSAGE_ERROR("801024", "微信发送模板消息失败失败"),

    /**
     * 产品渠道参数错误
     */
    PRODUCT_CHANNEL_ERROR("801025", "产品渠道参数错误"),

    /**
     * XmlMapper stringToBean 错误
     */
    STRING_TO_BEAN_ERROR("801026", "XmlMapper stringToBean 错误"),

    /**
     * XmlMapper beanToString 错误
     */
    BEAN_TO_STRING_ERROR("801027", "XmlMapper beanToString 错误"),

    /**
     * 中华联合加密失败
     */
    CIC_ENCRYPT_ERROR("801028", "中华联合加密失败"),

    /**
     * 中华联合解密失败
     */
    CIC_DECRYPT_ERROR("801029", "中华联合解密失败"),

    /**
     * 中华联合订单创建失败
     */
    CIC_ORDER_ERROR("801030", "中华联合订单创建失败"),

    /**
     * API接口权限鉴权失败
     */
    API_AUTH_ERROR("801031", "没有该模块接口权限"),

    /**
     * 文件命名重复
     */
    FILE_NAME_DUPLICATE("801032", "文件命名重复"),

    /**
     * 参数错误
     */
    PARAMS_ERROR("801033", "参数错误"),

    EXCEL_TEMPLATE_ERROR("801033", "excel文件格式错误"),

    /**
     * 被保人投保该产品已达上限，保存失败
     */
    OVER_PRODUCT_LIMIT("801034", "被保人投保该产品已达上限，保存失败"),

    /**
     * 编码
     */
    OVER_PRODUCT_LIMIT_2("801035", "被保人投保该产品已达上限，不能支付"),

    PARAMS_STATE_ERROR("801036", "数据状态错误"),

    BIZ_CODE_ERROR("801037", "生成推荐码失败"),

    /**
     * 用户没有接口权限
     */
    INVALID_USER_AUTH_844448("844448", "微信用户没有接口权限"),

    /**
     * 该手机号已注册代理人
     */
    AGENT_MOBILE_REGISTER_801034("801034", "账号已经存在,请直接登陆。"),

    /**
     * 该手机号没有注册
     */
    AGENT_MOBILE_NOT_REGISTER_801035("801035", "该手机号没有注册"),

    /**
     * 该身份证号已经注册
     */
    AGENT_IDCARD_ERROR_801135("801135", "该身份证号已经注册"),

    /**
     * 代理人邀请码错误
     */
    AGENT_INVITE_CODE_ERROR_801036("801036", "代理人邀请码错误,请重新输入!"),

    /**
     * 邀请码错误,暂不支持普通代理人邀请码
     */
    AGENT_LEVEL_ERROR_801136("801136", "邀请码错误, 暂不支持普通代理人邀请码"),

    /**
     * 代理人还未实名认证
     */
    AGENT_NOT_REAL_CHECK_801036("801036", "代理人还未实名认证"),

    /**
     * 代理人的父级代理人错误
     */
    AGENT_PARENT_ERROR_801136("801136", "代理人的父级代理人错误"),

    /**
     * 代理人不存在
     */
    AGENT_EXISTS_ERROR_801151("801151", "代理人账号不存在"),

    /**
     * 实名认证失败
     */
    REAL_CHECK_FAIL_801037("801037", "实名认证失败"),

    /**
     * 内部员工无需实名认证
     */
    INNER_USER_NOT_ALLOW_FAIL_801137("801137", "内部员工无需实名认证"),

    /**
     * 验证码发送太频繁
     */
    VERIFY_CODE_TOO_MANY_801038("801038", "验证码发送太频繁"),

    /**
     * 微信已绑定内部员工账号，不能注册
     */
    AGENT_ACCOUNT_ERROR_EMPLOYEE_801039("801039", "微信已绑定内部员工账号，不能注册"),

    /**
     * 微信已绑定代理人账号，不能注册
     */
    AGENT_ACCOUNT_ERROR_AGENT_801039("801039", "微信已绑定代理人账号，不能注册"),

    /**
     * 团队名称已被占用，请修改
     */
    AGENT_TEAM_NAME_ERROR_80119("801139", "团队名称已被占用，请修改"),

    /**
     * 不符合团险报价要求
     */
    QUOTE_ERROR_801040("801040", "不符合团险报价要求"),

    /**
     * 团险费率表格式错误
     */
    PRODUCT_TEMPLATE_ERROR_801041("801041", "团险费率表格式错误"),

    /**
     * 产品配置错误
     */
    PRODUCT_CONFIG_ERROR_801042("801042", "产品配置错误"),
    /**
     * 保单号不存在，请核对后查询
     */
    POLICY_SEARCH_NOT_EXIST_801043("801043", "保单号不存在，请核对后查询"),

    /**
     * 保单号与被保人证件号匹配不上，请核对后查询
     */
    POLICY_SEARCH_ID_NUMBER_ERROR_801044("801044", "保单号与被保人证件号匹配不上，请核对后查询"),

    /**
     * 产品配置错误
     */
    PRODUCT_CONFIG_ERROR_8020001("8020001", "该产品维护升级中"),
    /**
     * 推送时间段重复
     */
    SYSTEM_PUSH_SETTING_TIME_CONFLICT("801045", "推送时间段重复"),

    /**
     * 保单批改失败
     */
    POLICY_NOT_EXIST("801046", "保单批改失败"),

    /**
     * 默认代理人等级不能删除
     */
    AGENT_LEVEL_ERROR("801047", "默认代理人等级不能删除"),

    /**
     * 分布式锁获取失败
     */
    DLOCK_GET_ERROR("801048", "分布式锁获取失败"),

    /**
     * 理赔工作流初始化失败
     */
    CLAIM_WORKFLOW_ERROR("801049", "理赔工作流初始化失败"),

    /**
     * 只有赔付和拒赔的理赔才可以更改结案结果
     */
    CLAIM_UPDATE_ERROR("801050", "只有赔付和拒赔的理赔才可以更改结案结果"),

    /**
     * 理赔机构负责人未绑定微信公众号
     */
    CLAIM_ADMIN_UNBIND_ERROR("801051", "理赔机构负责人未绑定微信公众号"),

    /**
     * 产品介绍图片和保障项目金额配置不能都为空
     */
    PRODUCT_ERROR_IMAGE("801052", "产品介绍图片和保障项目金额配置不能都为空"),

    /**
     * 产品保障项目配置错误
     */
    PRODUCT_CVG_ERROR("801152", "产品保障项目配置错误"),

    /**
     * 未支付成功订单不支持补录订单
     */
    SMUPT_ORDER_ERROR_UNPAY("801053", "未支付成功订单不支持补录订单"),

    /**
     * 当前理赔没有配置理赔专员
     */
    CLAIM_SETTLEMENT_ERROR("801054", "当前理赔没有配置理赔专员"),

    /**
     * 订单支付回调查询支付状态失败
     */
    ORDER_PAY_BACK_ERROR("801055", "订单支付回调查询支付状态失败"),

    /**
     * 身份证与姓名不一致, 请您核对
     */
    REAL_CHECK_FAIL_801056("801056", "身份证与姓名不一致, 请您核对"),

    /**
     * jobCode与orgPath不匹配, 请您核对
     */
    JOB_CODE_ORG_PATH_NOT_MATCH("801057", "岗位编码与机构树不匹配"),

    /**
     * 岗位不存在
     */
    JOB_CODE_NOT_EXIST("801058", "岗位不存在"),

    /**
     * 岗位任期已结束
     */
    JOB_CODE_SERVICE_TIME_IS_CLOSE("801059", "岗位任期已结束"),

    SEND_MAIL_ERROR_FILE("801201", "发送邮件异常，请您前往邮箱确认邮件是否已经发送成功。如已发送，请取消勾选自动发送邮件，已免重复发送。"),
    SENTINEL_ERROR("801301", "接口隔离"),
    GROOVY_SCRIPT_ERROR("801302", "脚本规则执行出错"),
    DATA_EXISTS_ERROR_801303("801303", "数据已存在无需重新录入"),
    DATA_ERROR_801304("801304", "数据异常"),
    WX_MEDIAID_DOWNLOAD_FILE_ERROR_801304("801304", "微信通过mediaId下载失败，请检查mediaId是否正确"),
    THIRD_REQUEST_ERROR("900001", "调用第三方连接出错"),
    SYS_CONFIG_ERROR_900002("900002", "系统配置错误"),
    CUST_CENTER_ERROR("900003", "客户中心接口调用失败"),
    AI_CALL_ERROR("900004", "智能外呼任务异常"),
    LOAN_FIRST_CUST_ERROR("900005", "A类客户转化异常"),

    /**
     * 订单请求支付失败
     */
    ORDER_PAY_REQUEST_ERROR("801055", "订单请求支付失败"),
    /**
     * 智能核保问卷导入参数为空
     */
    QUESTIONNAIRE_IMPORT_PARAM_IS_NULL_802001("802001", "智能核保导入文件存在空串"),


    ;

    /**
     * 编码
     */
    private String code;

    /**
     * 名称
     */
    private String msg;

    ExcptEnum(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    @Override
    public String getCode() {
        return code;
    }

    @Override
    public String getMsg() {
        return msg;
    }

    @Override
    public String toString() {
        return "ExcptEnum{" +
                "code='" + code + '\'' +
                ", msg='" + msg + '\'' +
                '}';
    }
}
