package com.cfpamf.ms.insur.operation.phoenix.pojo.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class PhoenixEmpTodoCountVo {

    @ApiModelProperty("工号")
    String jobNumber;

    @ApiModelProperty("断保跟进任务统计")
    int interruptionTodoCount;

    @ApiModelProperty("续保跟进任务统计")
    int shortRenewalTodoCount;

    @ApiModelProperty("长期险续期跟进任务统计")
    int longRenewalTermTodoCount;
}
