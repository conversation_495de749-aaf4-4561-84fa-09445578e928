package com.cfpamf.ms.insur.operation.pco.service;

import org.springframework.util.StringUtils;
import com.cfpamf.ms.insur.operation.base.service.DataAuthService;
import com.cfpamf.ms.insur.operation.pco.dao.PcosScheduleMapper;
import com.cfpamf.ms.insur.operation.pco.query.PcoScheduleQuery;
import com.cfpamf.ms.insur.operation.pco.vo.PcoScheduleInfo;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class PcoScheduleService {

    private PcosScheduleMapper pcosScheduleMapper;
    DataAuthService dataAuthService;

    /**
     * 获取待办事项列表
     * @param query
     * @return
     */
    public PageInfo<PcoScheduleInfo> list(PcoScheduleQuery query){
        dataAuthService.dataAuth(query);
        //总部不显示待办事项
        if(!StringUtils.isEmpty(query.getRegionName())) {
            if (query.getPage() > 0) {
                PageHelper.startPage(query.getPage(), query.getSize());
            }
            List<PcoScheduleInfo> pcoScheduleInfos = pcosScheduleMapper.list(query);
            return new PageInfo<>(pcoScheduleInfos);
        }
        return new PageInfo<>(new ArrayList<PcoScheduleInfo>());

    }

    /**
     * 修改是否处理状态
     * @param query
     * @return
     */
    public void updateBedispose(PcoScheduleQuery query){
        pcosScheduleMapper.updateBedispose(query);
    }


}
