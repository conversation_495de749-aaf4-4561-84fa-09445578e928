package com.cfpamf.ms.insur.operation.pco.validation;

import com.cfpamf.ms.insur.operation.pco.dao.PcoLevelInfoMapper;
import com.cfpamf.ms.insur.operation.pco.dto.PcoLevelAdjustExcelDTO;
import com.cfpamf.ms.insur.operation.pco.vo.DingUserInfo;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.validation.ConstraintViolation;
import javax.validation.Validator;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class PcoLevelValidation {

    Validator validator;

    private PcoLevelInfoMapper pcoLevelInfoMapper;

    /**
     * 调整记录校验规则
     * @param dtos
     * @return
     */
    public List<ValidationResult<PcoLevelAdjustExcelDTO>> valid(List<PcoLevelAdjustExcelDTO> dtos,Map<String, List<DingUserInfo>> dingUserMap) {
        //基本校验  true --> success
        List<ValidationResult<PcoLevelAdjustExcelDTO>> allRes = dtos.stream()
                .map(adjust -> {
                    //excel数据基础校验
                    return validateBaseData(adjust);
                }).collect(Collectors.toList());

        Map<Boolean, List<ValidationResult<PcoLevelAdjustExcelDTO>>> baseResMap = allRes.stream().collect(Collectors.partitioningBy(ValidationResult::isSuccess));
        //成功记录
        List<ValidationResult<PcoLevelAdjustExcelDTO>> success = baseResMap.getOrDefault(Boolean.TRUE, Collections.emptyList());
        List<PcoLevelAdjustExcelDTO> successData = success.stream().map(ValidationResult::getSource).collect(Collectors.toList());

        //校验pco信息（pco工号是否存在，姓名工号是否匹配等）
        validatePcoInfo(success,dingUserMap);

        //校验pco数据是否重复
        return allRes;
    }

    /**
     * 校验pco信息（pco工号是否存在，姓名工号是否匹配等）
     * @param success
     */
    private void validatePcoInfo(List<ValidationResult<PcoLevelAdjustExcelDTO>> success,Map<String, List<DingUserInfo>> dingUserMap) {
        if (CollectionUtils.isEmpty(success)) {
            return;
        }
        List<String> userIds = new ArrayList<>();
        success.forEach(
            validation -> {
                PcoLevelAdjustExcelDTO dto = validation.getSource();
                List<DingUserInfo> dingUserInfo = dingUserMap.get(dto.getUserId());
                if(CollectionUtils.isEmpty(dingUserInfo)){
                    validation.addMessage("未匹配到pco信息");
                }else{
                    if(!dingUserInfo.stream().anyMatch(user->dto.getUserName().equals(user.getUserName())
                            && dto.getRegionName().equals(user.getRegionName())
                            && dto.getOrgName().equals(user.getOrgName()))){
                        validation.addMessage("pco姓名工号区域分支不匹配");
                    }
                }
                String key = dto.getUserId()+"-"+dto.getUserName()+"-"+dto.getRegionName()+"-"+dto.getOrgName();
                if (userIds.contains(key)){
                    validation.addMessage("pco数据重复");
                }
                userIds.add(key);
            }
        );
    }

    /**
     * 基础校验
     * @param adjust
     * @return
     */
    private ValidationResult<PcoLevelAdjustExcelDTO> validateBaseData(PcoLevelAdjustExcelDTO adjust) {
        //第一次校验
        Set<ConstraintViolation<PcoLevelAdjustExcelDTO>> validate = validator.validate(adjust);
        //基础校验 非空 简单正则等
        if (!CollectionUtils.isEmpty(validate)) {
            String collect = validate.stream()
                    .map(ConstraintViolation::getMessage)
                    .collect(Collectors.joining(","));
            //校验出问题
            return new ValidationResult<>(collect, adjust);
        }
        return  ValidationResult.success(adjust);
    }
}
