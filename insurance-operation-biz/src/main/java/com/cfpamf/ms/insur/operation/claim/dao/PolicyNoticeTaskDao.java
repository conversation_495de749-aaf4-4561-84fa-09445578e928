package com.cfpamf.ms.insur.operation.claim.dao;

import com.cfpamf.ms.insur.operation.claim.dto.PolicyNoticeTask;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface PolicyNoticeTaskDao {
    List<PolicyNoticeTask> queryPolicyNoticeTaskList(@Param("pt") String pt, @Param("limit") Integer limit);

    PolicyNoticeTask selectById(Long id);
}
