package com.cfpamf.ms.insur.operation.phoenix.dao;

import com.cfpamf.ms.insur.operation.aicall.vo.AiCallTaskDetail;
import com.cfpamf.ms.insur.operation.base.dao.CommonMapper;
import com.cfpamf.ms.insur.operation.phoenix.pojo.domain.PhoenixAiCallTaskDetail;
import com.cfpamf.ms.insur.operation.phoenix.pojo.domain.PhoenixEmpTodoStat;
import com.cfpamf.ms.insur.operation.phoenix.pojo.dto.PhoenixEmpRenewTodo;
import com.cfpamf.ms.insur.operation.phoenix.pojo.po.PhoenixEmpTodo;
import com.cfpamf.ms.insur.operation.phoenix.pojo.query.PhoenixEmpTodoBackQuery;
import com.cfpamf.ms.insur.operation.phoenix.pojo.query.PhoenixEmpTodoQuery;
import com.cfpamf.ms.insur.operation.phoenix.pojo.vo.PhoenixEmpTodoBackVo;
import com.cfpamf.ms.insur.operation.phoenix.pojo.vo.PhoenixEmpTodoVO;
import com.cfpamf.ms.insur.operation.phoenix.pojo.vo.WxLoanFollowList;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR> 2022/12/12 14:47
 */
@Mapper
public interface PhoenixEmpTodoMapper extends CommonMapper<PhoenixEmpTodo> {

    /**
     * 为每个客户经历生成10行待办
     *
     * @return
     */
    int insertInterruptionTdo(@Param("pt") String pt);


    /**
     * 查询员工待办数量
     *
     * @return
     */
    List<PhoenixEmpTodoStat> selectEmpTdoNum();

    /**
     * 查询列表
     * @param query
     * @return
     */
    List<PhoenixEmpTodoVO> selectByQuery(PhoenixEmpTodoQuery query);
    /**
     * 短期险续保待办列表
     * @param query 查询参数
     * @return 待办清单
     */
    List<PhoenixEmpTodoVO> shortRenewalTodoList(PhoenixEmpTodoQuery query);

    Integer countShortRenewalTabTodo(PhoenixEmpTodoQuery query);

    /**
     * 长期险续保待办列表
     * @param query 查询参数
     * @return 待办清单
     */
    List<PhoenixEmpTodoVO> longRenewalTermTodoList(PhoenixEmpTodoQuery query);

    Integer countLongRenewalTermTabTodo(PhoenixEmpTodoQuery query);
    /**
     * 断保待办列表
     * @param query 查询参数
     * @return 待办清单
     */
    List<PhoenixEmpTodoVO> interruptionTodoList(PhoenixEmpTodoQuery query);

    Integer countInterruptionTabTodo(PhoenixEmpTodoQuery query);

    Integer selectPersonTodoCount(String userId,List<String> bizTypes);
    /**
     * 生成短线续保的待办
     *
     * @return
     */
    int insertRenewShortTdo();

    /**
     * 更新待办表中的管护经理
     * @return
     */
    int updateRenewShortTdoCustomerAdmin();

    /**
     * 生成长险续保的待办
     */
    int insertRenewLongTdo();

    /**
     * 将短险续保过期数据修改成超时
     * @return
     */
    int updateRenewShortTdo();

    /**
     * 将长险续期过期数据修改成超时
     * @return
     */
    int updateRenewLongTdo();

    /**
     *
     * @return
     */
    List<PhoenixEmpTodoBackVo> list(PhoenixEmpTodoBackQuery query);
    /**
     *全部导出
     * @return
     */
    List<PhoenixEmpTodoBackVo> listAll(PhoenixEmpTodoBackQuery query);
    /**
     *
     * @return
     */
    String selectOrdIdByRenewalTerm(Integer renewalTerm);

    /**
     *
     * @return
     */
    int insertListNewTodo();

    void insertListPcoTransferTdo(@Param("empRenewTodoList") List<PhoenixEmpRenewTodo> empRenewTodoList);

    void updateListTransferTdo();

    List<PhoenixEmpRenewTodo> selectByRenewTodo();

    void updateOrderCustomerAdminV3(String oldAdminId, String newAdminId, String newAdminJobCode, String newAdminMainJobNumber, String newCustomerAdminOrgCode, String idNumber, Integer customerId);

    void insertCustomerAdminLog(@Param("empRenewTodoList") List<PhoenixEmpRenewTodo> empRenewTodoList);

    List<PhoenixEmpTodoStat> selectMongoEmpTdoNum();

    PhoenixEmpTodoStat selectTodoById(@Param("id") Long id);

    List<PhoenixEmpTodoStat> selectTimeoutEmpTdoNum();

    Integer selectShortAmt(@Param("policyNos") List<String> policyNos);

    Integer selectLongAmt(@Param("policyNos") List<String> policyNos);
    List<String> selectByJobNumber(@Param("jobNumber") String jobNumber);

    /**
     * 筛选Ai外呼待办
     * @param limit
     * @return
     */
    List<PhoenixAiCallTaskDetail> getAIPhoenixInfo(@Param("limit") Integer limit);
    /**
     * 筛选续保Ai外呼待办
     * @param limit
     * @return
     */
    List<PhoenixAiCallTaskDetail> getRenewAIPhoenixInfo(@Param("limit") Integer limit);

    /**
     * 获取配置机器人个数
     * @return
     */
    Integer selectRobotCount();

    /**
     *  生成异业转化代办
     * @return
     */
    int insertLoanTdo(@Param("batchNo") String batchNo);

    List<WxLoanFollowList> getBefore1DayList();

    int insertLoanNewTodo();

    Integer countPhoenixTodo(PhoenixEmpTodoBackQuery query);

    /**
     * 根据id更新跟进排序字段
     * @param followSortNo 排序号
     * @param id 主键
     */
    void updateFollowSortNo(@Param("followSortNo")Integer followSortNo, @Param("id")Integer id);

    void deleteNotFollowerInterruption(@Param("ids") List<Long> ids);

    List<Long> queryNotFollowerInterruptionTodoId();


    int insertLoanFirstNewTodo();

    /**
     * A类客户待办超时处理
     * 超过一个月未处理的自动移除待办
     */
    void updateLoanFirstTdo();


}

