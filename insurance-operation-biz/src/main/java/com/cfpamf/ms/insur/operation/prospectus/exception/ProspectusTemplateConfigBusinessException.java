package com.cfpamf.ms.insur.operation.prospectus.exception;

import com.cfpamf.common.ms.exception.MSBizNormalException;
import com.cfpamf.ms.insur.operation.base.exception.BusinessException;

/**
 * <AUTHOR>
 * @date 2021/5/20 10:13
 */
public class ProspectusTemplateConfigBusinessException extends MSBizNormalException {
    public ProspectusTemplateConfigBusinessException(String code, String message) {
        super(code, message);
    }

    public final static ProspectusTemplateConfigBusinessException PROSPECTUS_TEMPLATE_CONFIG_COVER_SIZE_ERROR = new ProspectusTemplateConfigBusinessException("prospectus.template.config.cover.size.error", "计划书模板配置模板文件数量不能超过七个");
}
