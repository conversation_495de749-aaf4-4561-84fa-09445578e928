package com.cfpamf.ms.insur.operation.honor.dto;

import com.cfpamf.ms.insur.operation.pco.util.CellReader;
import com.cfpamf.ms.insur.operation.pco.util.ExcelField;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import javax.validation.constraints.DecimalMax;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Map;

/**
 * <AUTHOR>
 * @description
 * @Version 1.0
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class HonorListExcelDTO implements Serializable {

    @NotNull(message = "排名不能为空")
    @DecimalMin(value = "0",message = "排名格式错误")
    @ExcelField(value="排名", excelReder = CellReader.INTEGER)
    Integer rankNum;

    @ExcelField("区域名称")
    String areaName;

    @ExcelField("区域编码")
    String areaCode;

    @ExcelField("分支名称")
    String bchName;

    @ExcelField("分支编码")
    String bchCode;

    @ExcelField("员工姓名")
    String empName;

    @ExcelField("员工工号")
    String empCode;

    @DecimalMin(value = "0",message = "标准保费格式错误")
    @ExcelField(value="标准保费", excelReder = CellReader.DECIMAL)
    BigDecimal assessConvertInsuranceAmt;

    @DecimalMin(value = "0",message = "人均标准保费格式错误")
    @ExcelField(value="人均标准保费", excelReder = CellReader.DECIMAL)
    BigDecimal assessConvertInsuranceAmtAvg;

    @DecimalMin(value = "0",message = "规模保费格式错误")
    @ExcelField(value="规模保费", excelReder = CellReader.DECIMAL)
    BigDecimal insuranceAmt;

    @DecimalMin(value = "0",message = "人均规模保费格式错误")
    @ExcelField(value="人均规模保费", excelReder = CellReader.DECIMAL)
    BigDecimal insuranceAmtAvg;

    @DecimalMin(value = "0",message = "留存率格式错误")
    @ExcelField(value="留存率", excelReder = CellReader.DECIMAL)
    BigDecimal insuranceRetentionRate;

    @DecimalMin(value = "0",message = "交叉销售比格式错误")
    @ExcelField(value="交叉销售比", excelReder = CellReader.DECIMAL)
    BigDecimal offlineLoanInsuranceRate;

    @ExcelField("错误信息")
    String errorMsg;

    Map userMap;

}
