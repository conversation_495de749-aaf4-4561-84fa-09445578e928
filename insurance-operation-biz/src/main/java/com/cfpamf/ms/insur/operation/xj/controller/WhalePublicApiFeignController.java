package com.cfpamf.ms.insur.operation.xj.controller;

import com.cfpamf.common.ms.result.Result;
import com.cfpamf.ms.insur.operation.base.response.CommonResult;
import com.cfpamf.ms.insur.operation.fegin.wx.request.WxaCodeCreateInput;
import com.cfpamf.ms.insur.operation.fegin.wx.response.ProductList;
import com.cfpamf.ms.insur.operation.fegin.wx.response.SellProductListOut;
import com.cfpamf.ms.insur.operation.xj.service.WhalePublicApiService;
import com.cfpamf.ms.insur.operation.xj.service.impl.WhalePublicApiBaseService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/4/29 16:16
 * @Version 1.0
 */
@Slf4j
@Api(tags = "小鲸publicApi服务")
@RestController
@RequestMapping("/whale/publicApi")
public class WhalePublicApiFeignController {

    @Autowired
    private WhalePublicApiService whalePublicApiService;

    @Autowired
    private WhalePublicApiBaseService whalePublicApiBaseService;

    @ApiOperation("获取微信二维码链接")
    @RequestMapping(value = "/{appId}/wxaCodeCreateByPage", method = RequestMethod.POST)
    Result<String> wxaCodeCreateByPage(@PathVariable @ApiParam(name = "appId", value = "appId") String appId, @RequestBody WxaCodeCreateInput wxaCodeCreateInput){
        return new Result(true,null,whalePublicApiService.wxaCodeCreateByPage(appId, wxaCodeCreateInput));
    }

    /**
     * 获取险种下拉列表
     */
    @ApiOperation(value = "险种下拉列表查询", notes = "险种下拉列表查询")
    @PostMapping("/queryProductList")
    public CommonResult<List<ProductList>> queryProductList(@RequestParam(required = false) @ApiParam(name = "portfolioCode", value = "组合编码") String portfolioCode,
                                                            @RequestParam(required = false) @ApiParam(name = "productName", value = "险种名称") String productName) {
        log.info("获取险种下拉列表查询，查询条件={},{}", portfolioCode,productName);
        List<ProductList> list = whalePublicApiBaseService.queryProductList(portfolioCode,productName);
        return CommonResult.successResult(list);
    }

    /**
     * 获取商品下拉列表
     */
    @ApiOperation(value = "商品下拉列表查询", notes = "商品下拉列表查询")
    @PostMapping("/querySellProductList")
    public CommonResult<List<SellProductListOut>> querySellProductList(@RequestParam(required = false) @ApiParam(name = "sellProductName", value = "商品名称") String sellProductName,
                                                                       @RequestParam(required = false) @ApiParam(name = "sellProductCode", value = "商品编码") String sellProductCode) {
        log.info("获取险种下拉列表查询，查询条件={}",sellProductName);
        List<SellProductListOut> list = whalePublicApiBaseService.querySellProductList(sellProductName,sellProductCode);
        return CommonResult.successResult(list);
    }
}
