package com.cfpamf.ms.insur.operation.pk.service;

import com.cfpamf.common.ms.exception.MSBizNormalException;
import com.cfpamf.ms.insur.operation.base.constant.BaseConstants;
import com.cfpamf.ms.insur.operation.base.util.LambdaUtils;
import com.cfpamf.ms.insur.operation.fegin.bms.enums.ExcptEnum;
import com.cfpamf.ms.insur.operation.phoenix.service.WhaleApiService;
import com.cfpamf.ms.insur.operation.pk.consts.PkConst;
import com.cfpamf.ms.insur.operation.pk.converter.PkMatchOpponentsConverter;
import com.cfpamf.ms.insur.operation.pk.dao.OrganizationDao;
import com.cfpamf.ms.insur.operation.pk.dao.PkDao;
import com.cfpamf.ms.insur.operation.pk.enums.EnumIndicatorType;
import com.cfpamf.ms.insur.operation.pk.pojo.dto.HistoryIndicatorsDto;
import com.cfpamf.ms.insur.operation.pk.pojo.dto.InsIndicators;
import com.cfpamf.ms.insur.operation.pk.pojo.dto.MonthEmpCntDto;
import com.cfpamf.ms.insur.operation.pk.pojo.dto.PkMatchOpponentsDto;
import com.cfpamf.ms.insur.operation.pk.pojo.po.Organization;
import com.cfpamf.ms.insur.operation.pk.pojo.req.IndicatorsRequest;
import com.cfpamf.ms.insur.operation.pk.pojo.req.PkMatchOpponentsRequest;
import com.cfpamf.ms.insur.operation.pk.pojo.resp.IndicatorsResponse;
import com.cfpamf.ms.insur.operation.pk.pojo.resp.IndicatorsVO;
import com.cfpamf.ms.insur.operation.pk.pojo.resp.PageResp;
import com.cfpamf.ms.insur.operation.pk.pojo.resp.PkMatchOpponentsResponse;
import com.cfpamf.ms.insur.operation.pk.safepgdao.AdsInsuranceIndexProgressDao;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import javax.validation.Valid;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * pk 指标查询
 *
 * <AUTHOR>
 * @since 2023/8/8 13:52
 */
@Service
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class PkIndicatorsQueryService {

    /**
     * pk 相关的
     */
    PkDao pkDao;

    /**
     * 机构
     */
    OrganizationDao organizationDao;

    /**
     * 数仓集市
     */
    AdsInsuranceIndexProgressDao adsInsuranceIndexProgressDao;

    WhaleApiService whaleApiService;

    /**
     * 随机/手动匹配对手
     *
     * @param request
     * @return
     */
    public PageResp<PkMatchOpponentsResponse> matchOpponents(@Valid PkMatchOpponentsRequest request) {


        PageResp<PkMatchOpponentsResponse> resp = new PageResp<>();
        resp.setRecords(Collections.emptyList());
        PageInfo<PkMatchOpponentsDto> pageInfo;

        PkMatchOpponentsDto matchUserData;

        List<String> blackList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(request.getBlackUserCodeList())) {
            blackList.addAll(request.getBlackUserCodeList());
        }
        request.setBlackUserCodeList(blackList);
        if (request.isArea()) {
            blackList.add(request.getRegionCode());
            matchUserData = adsInsuranceIndexProgressDao.selectAreaHistoryIndicatorsMe(request);
            Optional.ofNullable(matchUserData)
                    .map(PkMatchOpponentsDto::getValue)
                    .filter(s -> s.compareTo(BigDecimal.ZERO) > 0)
                    .ifPresent(request::setMatchValue);
            if (Objects.isNull(request.getMatchValue())) {
                return resp;
            }
            pageInfo = PageHelper.startPage(request.getPageNum(), request.getPageSize()).doSelectPageInfo(() -> adsInsuranceIndexProgressDao.selectAreaHistoryIndicatorsMatch(request));
        } else if (request.isOrg()) {
            blackList.add(request.getBranchCode());
            matchUserData = adsInsuranceIndexProgressDao.selectOrgHistoryIndicatorsMe(request);
            Optional.ofNullable(matchUserData)
                    .map(PkMatchOpponentsDto::getValue)
                    .filter(s -> s.compareTo(BigDecimal.ZERO) > 0)
                    .ifPresent(request::setMatchValue);
            if (Objects.isNull(request.getMatchValue())) {
                return resp;
            }
            pageInfo = PageHelper.startPage(request.getPageNum(), request.getPageSize()).doSelectPageInfo(() -> adsInsuranceIndexProgressDao.selectOrgHistoryIndicatorsMatch(request));
        } else if (request.isEmp()) {
            blackList.add(request.getUserCode());
            matchUserData = adsInsuranceIndexProgressDao.selectEmpHistoryIndicatorsMe(request);
            Optional.ofNullable(matchUserData)
                    .map(PkMatchOpponentsDto::getValue)
                    .filter(s -> s.compareTo(BigDecimal.ZERO) > 0)
                    .ifPresent(request::setMatchValue);
            if (Objects.isNull(request.getMatchValue())) {
                return resp;
            }
            pageInfo = PageHelper.startPage(request.getPageNum(), request.getPageSize()).doSelectPageInfo(() -> adsInsuranceIndexProgressDao.selectEmpHistoryIndicatorsMathch(request));
        } else {
            throw new MSBizNormalException(ExcptEnum.PARAMS_ERROR.getCode(), "不支持的PK类型：" + request.getType());
        }
        if (Objects.isNull(matchUserData)) {
            matchUserData = new PkMatchOpponentsDto();
            matchUserData.setUserCode(request.getUserCode());
            matchUserData.setBranchCode(request.getBranchCode());
            matchUserData.setRegionCode(request.getRegionCode());
            matchUserData.setValue(BigDecimal.ZERO);
        }

        matchUserData.setType(request.getType());
        matchUserData.setIndicatorType(request.getIndicatorsType());

        PkMatchOpponentsResponse matchUserResp = PkMatchOpponentsConverter.convertToResponse(matchUserData);

        resp.setSize(pageInfo.getSize());
        resp.setTotal(pageInfo.getTotal());
        resp.setRecords(pageInfo.getList().stream().peek(dto -> {
                    dto.setIndicatorType(request.getIndicatorsType());
                    dto.setType(request.getType());
                }).map(PkMatchOpponentsConverter::convertToResponse)
                .peek(res0 -> {
                    res0.setOriginatorIndicatorsVO(matchUserResp.getRecipientIndicatorsVO());
                })
                .collect(Collectors.toList()));
        return resp;
    }


    /**
     * 查询员工的历史战力值  - pg
     *
     * @param indicatorsRequest
     * @return
     */
    public List<IndicatorsResponse> queryPkHistoryIndicators(@Valid IndicatorsRequest indicatorsRequest) {

        List<HistoryIndicatorsDto> historyIndicators;

        if (indicatorsRequest.isArea()) {
            historyIndicators = adsInsuranceIndexProgressDao.selectAreaHistoryIndicators(indicatorsRequest.getStartDateStr(), indicatorsRequest.getEndDateStr(), indicatorsRequest.getCodeList());
        } else if (indicatorsRequest.isOrg()) {
            historyIndicators = adsInsuranceIndexProgressDao.selectOrgHistoryIndicators(indicatorsRequest.getStartDateStr(), indicatorsRequest.getEndDateStr(), indicatorsRequest.getCodeList());
        } else if (indicatorsRequest.isEmp()) {
            historyIndicators = adsInsuranceIndexProgressDao.selectEmpHistoryIndicators(indicatorsRequest.getStartDateStr(), indicatorsRequest.getEndDateStr(), indicatorsRequest.getCodeList());
        } else {
            throw new MSBizNormalException(ExcptEnum.PARAMS_ERROR.getCode(), "不支持的PK类型：" + indicatorsRequest.getType());
        }

        //判断是否有平均数据
        final Map<String, Integer> msCmtCnt = indicatorsRequest.hasAvg() ? monthEmp(indicatorsRequest) : Collections.emptyMap();
        return historyIndicators.stream()
                .map(merge -> {
                    IndicatorsResponse response = new IndicatorsResponse();
                    response.setCode(merge.getCode());
                    response.setName(merge.getName());
                    response.setType(indicatorsRequest.getType());
                    response.setIndicatorsVOList(merge2IndicatorsList(merge, indicatorsRequest.getIndicatorTypeList(), msCmtCnt.get(merge.getCode()), indicatorsRequest.isNear3Month()));
                    return response;
                }).collect(Collectors.toList());
    }


    public List<InsIndicators> getReceiptPerformanceHistory(@Valid IndicatorsRequest indicatorsRequest, String indicatorType, boolean avg) {

        List<HistoryIndicatorsDto> historyIndicators;

        if (indicatorsRequest.isArea()) {
            historyIndicators = adsInsuranceIndexProgressDao.selectAreaHistoryIndicators(indicatorsRequest.getStartDateStr(), indicatorsRequest.getEndDateStr(), indicatorsRequest.getCodeList());
        } else if (indicatorsRequest.isOrg()) {
            historyIndicators = adsInsuranceIndexProgressDao.selectOrgHistoryIndicators(indicatorsRequest.getStartDateStr(), indicatorsRequest.getEndDateStr(), indicatorsRequest.getCodeList());
        } else if (indicatorsRequest.isEmp()) {
            historyIndicators = adsInsuranceIndexProgressDao.selectEmpHistoryIndicators(indicatorsRequest.getStartDateStr(), indicatorsRequest.getEndDateStr(), indicatorsRequest.getCodeList());
        } else {
            throw new MSBizNormalException(ExcptEnum.PARAMS_ERROR.getCode(), "不支持的PK类型：" + indicatorsRequest.getType());
        }

        //判断是否有平均数据
        final Map<String, Integer> msCmtCnt = indicatorsRequest.hasAvg() ? monthEmp(indicatorsRequest) : Collections.emptyMap();
        return historyIndicators.stream()
                .map(merge -> {
                    InsIndicators indicators = new InsIndicators();
                    indicators.setType(indicatorsRequest.getType());
                    indicators.setIndicatorType(indicatorType);
                    indicators.setCode(merge.getCode());
                    EnumIndicatorType type = EnumIndicatorType.valueOfCode(indicatorType);
                    if (type.isAvg()) {
                        indicators.setValue(merge.getActualReceiptPerformanceAvg(msCmtCnt.get(merge.getCode())));
                    }else{
                        indicators.setValue(merge.getActualReceiptPerformance());
                    }
                    return indicators;
                }).collect(Collectors.toList());
    }


    public List<InsIndicators> getRegCust(@Valid IndicatorsRequest request, String indicatorType, boolean avg) {

        List<InsIndicators> insIndicators = whaleApiService.userCount(request, avg);

        final Map<String, Integer> msCmtCnt = avg ? monthEmp(request) : Collections.emptyMap();

        for (InsIndicators indicator : insIndicators) {
            EnumIndicatorType enumType = EnumIndicatorType.valueOfCode(indicatorType);
            if (enumType.isAvg()) {
                indicator.setValue(calAvg(msCmtCnt.get(indicator.getCode()), indicator.getValue()));
            }
        }
        return insIndicators;
    }

    private BigDecimal calAvg(Integer msEmpCnt, BigDecimal value) {
        if (Objects.isNull(msEmpCnt) || msEmpCnt <= 0) {
            return BigDecimal.ZERO;
        }
        if (Objects.isNull(value)) {
            return null;
        }
        return value.setScale(1, RoundingMode.HALF_UP).divide(new BigDecimal(msEmpCnt), RoundingMode.HALF_UP);
    }

    /**
     * 查询实时PK指标
     *
     * @param indicatorsRequest
     * @return
     */
    public List<IndicatorsResponse> queryPkRealTimeIndicators(@Valid IndicatorsRequest indicatorsRequest) {

        List<InsIndicators> indicators = indicatorsRequest.getIndicatorTypeList().stream().map(indicatorType -> {

            EnumIndicatorType type = EnumIndicatorType.valueOfCode(indicatorType);
            switch (type) {
                case AMOUNT:
                    return getNormalAmount(indicatorsRequest, indicatorType, false);
                case AMOUNT_AVG:
                    return getNormalAmount(indicatorsRequest, indicatorType, true);
                case RECEIPT_PERFORMANCE:
                    return getReceiptPerformanceHistory(indicatorsRequest, indicatorType, false);
                case RECEIPT_PERFORMANCE_AVG:
                    return getReceiptPerformanceHistory(indicatorsRequest, indicatorType, true);
                case REG_CUST:
                    return getRegCust(indicatorsRequest, indicatorType, false);
                case REG_CUST_AVG:
                    return getRegCust(indicatorsRequest, indicatorType, true);
                default:
                    throw new MSBizNormalException(ExcptEnum.PARAMS_ERROR.getCode(), "不支持的PK指标查询：" + type);
            }
        }).flatMap(Collection::stream).collect(Collectors.toList());

        Map<String, List<InsIndicators>> map = LambdaUtils.groupBy(indicators, InsIndicators::getCode);

        // 转成获客牛需要的数据
        return map.entrySet().stream().map(entry -> {
            IndicatorsResponse indicatorsResponse = new IndicatorsResponse();
            indicatorsResponse.setType(indicatorsRequest.getType());
            InsIndicators insIndicators = entry.getValue().get(0);
            indicatorsResponse.setCode(entry.getKey());
            indicatorsResponse.setName(insIndicators.getName());
            indicatorsResponse.setIndicatorsVOList(entry.getValue().stream().map(ins -> {
                EnumIndicatorType indicatorType = EnumIndicatorType.valueOfCode(ins.getIndicatorType());
                return new IndicatorsVO(indicatorType, ins.getValue(), false);
            }).collect(Collectors.toList()));
            return indicatorsResponse;

        }).collect(Collectors.toList());
    }

    /**
     * 查询实时标准保费
     *
     * @param indicatorsRequest
     * @return
     */
    private List<InsIndicators> getNormalAmount(IndicatorsRequest indicatorsRequest, String indicatorType, boolean avg) {
        List<InsIndicators> insIndicators;
        if (Objects.equals(PkConst.PK_TYPE_EMP, indicatorsRequest.getType())) {
            insIndicators = pkDao.selectEmpAmount(indicatorsRequest);
        } else if (Objects.equals(PkConst.PK_TYPE_ORG, indicatorsRequest.getType())) {
            insIndicators = pkDao.selectOrgAmount(indicatorsRequest);
        } else if (Objects.equals(PkConst.PK_TYPE_AREA, indicatorsRequest.getType())) {

            Example example = new Example(Organization.class);
            example.createCriteria().andIn("orgCode", indicatorsRequest.getCodeList()).andEqualTo("enabledFlag", 0);
            List<Organization> organizations = organizationDao.selectByExample(example);
            insIndicators = organizations.stream().map(or -> {
                InsIndicators indicators = pkDao.selectAreaAmount(indicatorsRequest.getStartDate(), indicatorsRequest.getEndDateNextDay(), or.getOrgPath());
                if (Objects.nonNull(indicators)) {
                    indicators.setName(or.getOrgName());
                    indicators.setCode(or.getOrgCode());
                }
                return indicators;
            }).filter(Objects::nonNull).collect(Collectors.toList());

        } else {
            throw new MSBizNormalException(ExcptEnum.PARAMS_ERROR.getCode(), "不支持的PK类型：" + indicatorsRequest.getType());
        }
        insIndicators.forEach(s -> s.setIndicatorType(indicatorType));

        if (avg) {
            Map<String, Integer> stringIntegerMap = monthEmp(indicatorsRequest);
            insIndicators.forEach(s -> {
                Integer empCnt = stringIntegerMap.getOrDefault(s.getCode(), 1);
                if (empCnt > 0) {
                    // 设置平均值
                    s.setValue(s.getValue().setScale(4, RoundingMode.HALF_UP).divide(new BigDecimal(empCnt), RoundingMode.HALF_UP));
                }
            });

        }
        return insIndicators;
    }

    /**
     * 月初人力均值
     *
     * @param indicatorsRequest
     * @return
     */
    private Map<String, Integer> monthEmp(IndicatorsRequest indicatorsRequest) {

        if (indicatorsRequest.isArea()) {
            return monthEmpArea(indicatorsRequest);
        } else if (indicatorsRequest.isOrg()) {
            return monthEmpOrg(indicatorsRequest);
        }
        return Collections.emptyMap();
    }


    private Map<String, Integer> monthEmpOrg(IndicatorsRequest indicatorsRequest) {


        // 如果开始时间等于今天 就开始时间就往前推一天
        String start = indicatorsRequest.startIsToday() ?
                BaseConstants.FMT_DATE.format(indicatorsRequest.getStartDate().minusDays(1L)) :
                BaseConstants.FMT_DATE.format(indicatorsRequest.getStartDate());
        String end = BaseConstants.FMT_DATE.format(indicatorsRequest.getEndDate());

        List<MonthEmpCntDto> monthEmpCntDtos = adsInsuranceIndexProgressDao.selectMonthEmpOrg(start, end, indicatorsRequest.getCodeList());

        return LambdaUtils.toMap(monthEmpCntDtos, MonthEmpCntDto::getCode, MonthEmpCntDto::getEmpCnt);
    }

    private Map<String, Integer> monthEmpArea(IndicatorsRequest indicatorsRequest) {

        // 如果开始时间等于今天 就开始时间就往前推一天
        String start = indicatorsRequest.startIsToday() ?
                BaseConstants.FMT_DATE.format(indicatorsRequest.getStartDate().minusDays(1L)) :
                BaseConstants.FMT_DATE.format(indicatorsRequest.getStartDate());
        String end = BaseConstants.FMT_DATE.format(indicatorsRequest.getEndDate());

        List<MonthEmpCntDto> monthEmpCntDtos = adsInsuranceIndexProgressDao.selectMonthEmpArea(start, end, indicatorsRequest.getCodeList());

        return LambdaUtils.toMap(monthEmpCntDtos, MonthEmpCntDto::getCode, MonthEmpCntDto::getEmpCnt);
    }

    private List<IndicatorsVO> merge2IndicatorsList(HistoryIndicatorsDto dto, List<String> types, Integer msEmpCnt, boolean isNear3Month) {

        return types.stream()
                .map(typeCode -> {
                    EnumIndicatorType indicatorType = EnumIndicatorType.valueOfCode(typeCode);
                    switch (indicatorType) {
                        case AMOUNT:
                            return new IndicatorsVO(EnumIndicatorType.AMOUNT, dto.getNormInsuranceAmt(), isNear3Month);
                        case AMOUNT_AVG:
                            return new IndicatorsVO(EnumIndicatorType.AMOUNT_AVG, dto.getNormInsuranceAmtAvg(msEmpCnt), isNear3Month);
                        case RECEIPT_PERFORMANCE:
                            return new IndicatorsVO(EnumIndicatorType.RECEIPT_PERFORMANCE, dto.getActualReceiptPerformance(), isNear3Month);
                        case RECEIPT_PERFORMANCE_AVG:
                            return new IndicatorsVO(EnumIndicatorType.RECEIPT_PERFORMANCE_AVG, dto.getActualReceiptPerformanceAvg(msEmpCnt), isNear3Month);
                        case REG_CUST:
                            return new IndicatorsVO(EnumIndicatorType.REG_CUST, dto.getLandCustomerCnt(), isNear3Month);
                        case REG_CUST_AVG:
                            return new IndicatorsVO(EnumIndicatorType.REG_CUST_AVG, dto.getLandCustomerCntAvg(msEmpCnt), isNear3Month);
                        case MANGE_CUST:
                            return new IndicatorsVO(EnumIndicatorType.MANGE_CUST, dto.getManageCustCnt(), isNear3Month);
                        case ORG_MONTH:
                            return new IndicatorsVO(EnumIndicatorType.ORG_MONTH, dto.getSetMonths15rule(), isNear3Month);
                        case COMPANY_YEAR:
                            return new IndicatorsVO(EnumIndicatorType.COMPANY_YEAR, dto.getJobMonths(), isNear3Month);
                        default:
                            throw new MSBizNormalException(ExcptEnum.PARAMS_ERROR.getCode(), "不支持的code");
                    }
                }).collect(Collectors.toList());
    }


}
