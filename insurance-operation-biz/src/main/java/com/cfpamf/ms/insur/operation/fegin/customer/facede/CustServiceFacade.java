package com.cfpamf.ms.insur.operation.fegin.customer.facede;

import com.cfpamf.common.ms.result.Result;
import com.cfpamf.ms.insur.operation.fegin.customer.request.CustBaseQueryByIdNoRequest;
import com.cfpamf.ms.insur.operation.fegin.customer.response.CustBaseInfoVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @Date 2022/4/29 14:29
 * @Version 1.0
 */
@FeignClient(name = "ms-service-customer", url = "${ms-service-customer.url}")
public interface CustServiceFacade {
    @PostMapping(value = "/cust/baseInfoByIdNo")
    Result<CustBaseInfoVo> baseInfoByIdNo(@RequestBody CustBaseQueryByIdNoRequest request);
}
