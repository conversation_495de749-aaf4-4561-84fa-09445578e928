package com.cfpamf.ms.insur.operation.whale.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2023/6/27 1:30 上午
 * @Version 1.0
 */
@FieldDefaults(level = AccessLevel.PRIVATE)
@Data
public class PreservationAddOrSubtract {
    @ApiModelProperty(value = "序号")
    private String id;

    @ApiModelProperty(value = "增减员类型 加人/减人", example = "加人")
    private String type;

    @ApiModelProperty(value = "主被保人标识")
    private String mainInsuredFlag;

    @ApiModelProperty(value = "对应主被保人")
    private String mainInsuredName;

    @ApiModelProperty(value = "被保人姓名", example = "张三")
    private String insuredName;

    @ApiModelProperty(value = "与主被保人关系", example = "配偶")
    private String firstInsuredRelation;

    @ApiModelProperty(value = "被保人性别 0：女 1：男")
    private String insuredGender;

    @ApiModelProperty(value = "被保人出生日期")
    private String insuredBirthday;

    @ApiModelProperty(value = "被保人证件类型")
    private String insuredIdType;

    @ApiModelProperty(value = "被保人证件号码")
    private String insuredIdCard;

    @ApiModelProperty(value = "证件有效期")
    private String insuredIdCardValidityEnd;

    @ApiModelProperty(value = "国籍")
    private String insuredNation;

    @ApiModelProperty(value = "地址")
    private String insuredAddress;

    @ApiModelProperty(value = "保险计划编码")
    private String planCode;

    @ApiModelProperty(value = "保费")
    private BigDecimal singlePremium;

    @ApiModelProperty(value = "职业代码")
    private String insuredCareer;

    @ApiModelProperty(value = "职业类别")
    private String insuredOccupationalCategory;

    @ApiModelProperty(value = "主被保人证件号码")
    private String mainInsuredIdCard;

    @ApiModelProperty(value = "联系方式")
    private String insuredMobile;

    @ApiModelProperty(value = "电子邮箱")
    private String insuredEmail;

    @ApiModelProperty(value = "工作单位")
    private String insuredCompany;

    @ApiModelProperty(value = "被保人电子保单")
    private String insuredPolicyUrl;

    @ApiModelProperty("推荐人编码")
    private String referrerWno;

    @ApiModelProperty(value = "推荐人名字(可选)", required = false)
    private String referrerName;

    @ApiModelProperty("渠道推荐人编码")
    private String channelReferrerWno;

    @ApiModelProperty(value = "渠道推荐人名字(可选)", required = false)
    private String channelReferrerName;

    @ApiModelProperty("渠道分支编码")
    private String channelBranchCode;

    @ApiModelProperty(value = "渠道分支名字(可选)", required = false)
    private String channelBranchName;

    @ApiModelProperty("销售渠道编码")
    private String sellChannelCode;


    @ApiModelProperty(value = "客户经理")
    private String customerManagerCode;

    @ApiModelProperty(value = "客户经理所属分支机构")
    private String customerManagerOrgCode;

    @ApiModelProperty(value = "客户经理渠道机构编码")
    private String customerManagerChannelOrgCode;

    @ApiModelProperty(value = "客户经理督导")
    private String customerManagerSupervisor;

    @ApiModelProperty(value = "客户经理渠道编码")
    private String customerManagerChannelCode;

    List<ProductInfoList> productInfoList;
    public BigDecimal sumPremium() {
        if (CollectionUtils.isEmpty(productInfoList)) {
            return BigDecimal.ZERO;
        } else {
            return productInfoList.stream()
                    .map(ProductInfoList::getPremium)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
        }
    }

    public BigDecimal sumAmount() {
        if (CollectionUtils.isEmpty(productInfoList)) {
            return BigDecimal.ZERO;
        } else {
            return productInfoList.stream()
                    .map(ProductInfoList::getCoverage)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
        }
    }
}
