package com.cfpamf.ms.insur.operation.msg.service;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.cfpamf.common.ms.exception.MSBizNormalException;
import com.cfpamf.ms.bms.facade.vo.EmployeeDingTalkVO;
import com.cfpamf.ms.insur.operation.base.annotaions.DataSourceReadOnly;
import com.cfpamf.ms.insur.operation.base.helper.BmsHelper;
import com.cfpamf.ms.insur.operation.base.helper.LockHelper;
import com.cfpamf.ms.insur.operation.fegin.bms.enums.ExcptEnum;
import com.cfpamf.ms.insur.operation.msg.dao.DingTalkUserMapper;
import com.cfpamf.ms.insur.operation.msg.enums.OpMessageReceiverTypeEnum;
import com.cfpamf.ms.insur.operation.msg.pojo.dto.DingTalkUserDTO;
import com.cfpamf.ms.insur.operation.msg.pojo.dto.OpMessageRuleReceiverGroovyDTO;
import com.cfpamf.ms.insur.operation.msg.pojo.po.DingTalkUser;
import com.cfpamf.ms.insur.operation.msg.pojo.po.OpMessageRule;
import com.cfpamf.ms.insur.operation.msg.pojo.po.OpMessageRuleReceiver;
import com.cfpamf.ms.insur.operation.msg.pojo.query.DingTalkUserQuery;
import lombok.AccessLevel;
import lombok.experimental.FieldDefaults;
import org.apache.commons.lang3.StringUtils;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR> 2021/9/1 15:46
 */
@FieldDefaults(level = AccessLevel.PRIVATE)
@Service
public class EmpPostService {

    static DateTimeFormatter FMT_BATCH = DateTimeFormatter.ofPattern("yyyyMMdd");

    static final String PUSH_REGION_KEY = "regions";

    static final String PUSH_ORG_KEY = "orgs";

    @Autowired
    BmsHelper helper;
    @Autowired
    DingTalkUserMapper dingTalkUserMapper;

    @Autowired
    LockHelper lockHelper;

    @Autowired
    JdbcTemplate jdbcTemplate;


    /**
     * 根据岗位 获取需要推送的人
     *
     * @param rule
     * @param receiver
     * @param postName
     * @return
     */
    public List<OpMessageRuleReceiverGroovyDTO> postNameCvt(OpMessageRule rule, OpMessageRuleReceiver receiver, String postName) {
        return postEmployees(rule, receiver, postName)
                .stream().map(dingTalk -> {
                    OpMessageRuleReceiverGroovyDTO re = new OpMessageRuleReceiverGroovyDTO();
                    re.setReceiverType(OpMessageReceiverTypeEnum.PERSON.getCode());
                    re.setReceiver(dingTalk.getDingTalkUserId());
                    re.setReceiverName(dingTalk.getUserName());
                    re.setMessageRuleId(rule.getId());
                    re.setAuditState(receiver.getAuditState());
                    re.setParams(receiver.getParams());
                    JSONObject json = new JSONObject();
                    if (StringUtils.isNotBlank(receiver.getParams())) {
                        json = JSONObject.parseObject(receiver.getParams());
                    }
                    re.setOtherParams(json.fluentPut("jobNumber", dingTalk.getJobNumber()));
                    return re;
                }).collect(Collectors.toList());

    }

    /**
     * 查询员工数据
     *
     * @param rule
     */
    public List<DingTalkUserDTO> postEmployees(OpMessageRule rule, OpMessageRuleReceiver receiver, String postName) {

        String batchNo = genTodayBatchNo();
        int postNums = dingTalkUserMapper.selectCountByBatchAndPost(postName, batchNo);
        if (postNums == 0) {
            //  今天还没数据 开始同步
            postNums = syncPostUser(postName, batchNo);
        }

        if (postNums == 0) {
            throw new MSBizNormalException(ExcptEnum.PARAMS_ERROR.getCode(), "改岗位没有数据：" + postName);
        }
        JSONObject jsonObject = JSONObject.parseObject(receiver.getParams());
        DingTalkUserQuery query = new DingTalkUserQuery();
        query.setBatchNo(batchNo);
        //过滤区域 机构
        query.setRegions(jsonObject.getObject(PUSH_REGION_KEY, new TypeReference<List<String>>() {
        }));
        query.setOrgs(jsonObject.getObject(PUSH_ORG_KEY, new TypeReference<List<String>>() {
        }));
        query.setPostName(postName);
        return dingTalkUserMapper.selectByPostAndArea(query);
    }

    /**
     * 同步岗位数据
     *
     * @param postName
     * @return
     */
    public int syncPostToday(String postName) {
        EmpPostService postService = (EmpPostService) AopContext.currentProxy();
        return postService.syncPostUser(postName, genTodayBatchNo());
    }

    /**
     * 强制走bms查询钉钉信息
     *
     * @param postName
     * @return
     */
    public EmployeeDingTalkVO listPostByBms(String postName) {
        return helper.listEmployeeDingUsersByPostName(postName);
    }

    /**
     * 同步钉钉数据
     *
     * @param postName
     */
    @Transactional(rollbackFor = Exception.class)
    public int syncPostUser(String postName, String batchNo) {
        return lockHelper.lockSupplier("sync:post:" + postName, () -> {
            int count = dingTalkUserMapper.selectCountByBatchAndPost(postName, batchNo);

            if (count > 0) {
                return count;
            }
            EmployeeDingTalkVO employeeDingTalkVO = helper.listEmployeeDingUsersByPostName(postName);
            List<DingTalkUser> users = employeeDingTalkVO.getDingTalkUserBaseBmsVOS()
                    .stream().map(bmsDing -> {
                        DingTalkUser dingTalkUser = new DingTalkUser();
                        dingTalkUser.setDingTalkUserId(bmsDing.getDingTalkUserId());
                        dingTalkUser.setDingId(bmsDing.getDingId());
                        dingTalkUser.setPostName(postName);
                        dingTalkUser.setBatchNo(batchNo);
                        dingTalkUser.setUserName(bmsDing.getName());
                        dingTalkUser.setJobNumber(bmsDing.getJobNumber());
                        dingTalkUser.setDingUserId(bmsDing.getDingUserId());
                        dingTalkUser.setMobile(bmsDing.getMobile());
                        dingTalkUser.setAvatar(bmsDing.getAvatar());
                        return dingTalkUser;
                    }).collect(Collectors.toList());
            DingTalkUser delete = new DingTalkUser();
            delete.setBatchNo(batchNo);
            delete.setPostName(postName);
            dingTalkUserMapper.delete(delete);
            dingTalkUserMapper.insertList(users);
            return users.size();
        });
    }

    private String genTodayBatchNo() {
        return FMT_BATCH.format(LocalDate.now());
    }

    @DataSourceReadOnly
    public Map<String, Object> selectMap(String sql) {
        return jdbcTemplate.queryForMap(sql);
    }
}
