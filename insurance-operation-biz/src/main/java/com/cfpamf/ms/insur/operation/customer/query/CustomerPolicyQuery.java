package com.cfpamf.ms.insur.operation.customer.query;

import com.cfpamf.ms.insur.operation.base.form.PageForm;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.FieldDefaults;

/**
 * 微信产品Query
 *
 * <AUTHOR>
 **/
@Data
@ToString(callSuper = true)
@FieldDefaults(level = AccessLevel.PRIVATE)
public class CustomerPolicyQuery {
    /**
     * 页码
     */
    @ApiModelProperty(value = "页码 从1开始")
    private int page;

    /**
     * 每页条数
     */
    @ApiModelProperty(value = "每页条数")
    private int size = 20;

    /**
     * 投保人身份证
     */
    @ApiModelProperty(value = "身份证号")
    String idNumber;
    /**
     * 投保人身份证
     */
    Boolean queryHistory;

    /**
     * 微信openId
     */
    @ApiModelProperty(value = "微信openId")
    String openId;

    /**
     * 微信token
     */
    @ApiModelProperty(value = "微信token")
    String authorization;

    /**
     * 是否为被保人
     */
    @ApiModelProperty(value = "是否为被保人")
    boolean isInsured;
}
