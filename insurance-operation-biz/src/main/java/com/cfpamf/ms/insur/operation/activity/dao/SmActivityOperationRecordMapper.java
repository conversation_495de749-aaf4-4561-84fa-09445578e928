package com.cfpamf.ms.insur.operation.activity.dao;

import com.cfpamf.ms.insur.operation.activity.entity.SmActivityOperationRecord;
import com.cfpamf.ms.insur.operation.base.dao.CommonMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 活动操作记录表
 *
 * <AUTHOR>
 */
@Mapper
public interface SmActivityOperationRecordMapper extends CommonMapper<SmActivityOperationRecord> {

    /**
     * 获取上一次的操作记录
     * @param saId
     * @param operationType
     * @return
     */
    SmActivityOperationRecord getByLastOperationRecord(@Param("saId")Long saId,@Param("operationType") String operationType);
}