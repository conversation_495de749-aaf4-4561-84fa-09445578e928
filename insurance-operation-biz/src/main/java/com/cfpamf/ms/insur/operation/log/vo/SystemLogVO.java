package com.cfpamf.ms.insur.operation.log.vo;

import lombok.Data;

import java.util.Date;

/**
 * 系统日志VO
 *
 * <AUTHOR>
 **/
@Data
public class SystemLogVO {

    /**
     * 日志Id
     */
    private Integer logId;

    /**
     * 用户Id
     */
    private String userId;

    /**
     * 用户Id
     */
    private String userName;

    /**
     * 登录IP
     */
    private String userIp;

    /**
     * 操作种类
     */
    private String actionType;

    /**
     * 模块名称
     */
    private String moduleName;

    /**
     * 操作描述
     */
    private String description;

    /**
     * 调用方法名称
     */
    private String method;

    /**
     * 调用URL
     */
    private String url;

    /**
     * 调用方法参数
     */
    private String parameters;

    /**
     * 操作结果
     */
    private String result;

    /**
     * 错误消息
     */
    private String message;

    /**
     * 操作时间
     */
    private Date createTime;

    /**
     * 耗时
     */
    private Long costTime;
}
