package com.cfpamf.ms.insur.operation.promotion.dao;

import com.cfpamf.ms.insur.operation.base.dao.CommonMapper;
import com.cfpamf.ms.insur.operation.promotion.entity.OperationSynMaterialInfoEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 同步素材圈信息表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-04-29 19:33:41
 */
@Mapper
public interface OperationSynMaterialInfoMapper extends CommonMapper<OperationSynMaterialInfoEntity> {


    @Select("SELECT p.* FROM operation_syn_material_info p where p.material_code=#{materialCode} and p.source_system_code=#{sourceSystemCode} and p.material_category=#{materialCategory} and p.material_response_id is not null and p.material_status = #{status} and p.deleted = 0 order by p.id desc limit 1;")
    OperationSynMaterialInfoEntity queryByMaterialCodeSystemCode(@Param("materialCode") String materialCode , @Param("sourceSystemCode") String sourceSystemCode,@Param("materialCategory") Integer materialCategory,@Param("status") Integer status);

    @Select("SELECT p.material_link_id FROM operation_syn_material_info p where p.product_code=#{productCode} and p.material_link_id is not null order by p.id desc limit 1")
    Long queryLinkIdByProductCode(@Param("productCode") String productCode);

    @Select("SELECT material_link_id FROM operation_syn_material_info p where p.material_level_code=#{materialLevelCode} and p.material_link_id is not null order by p.id desc limit 1")
    Long queryLinkIdByMaterialCategory(@Param("materialLevelCode") String materialLevelCode);

    Integer logicDelete(@Param("materialCode") String materialCode , @Param("sourceSystemCode") String sourceSystemCode);

    int updateMaterialResponseId(@Param("id") Integer id, @Param("materialLinkId") Integer materialLinkId , @Param("materialResponseId") Long materialResponseId, @Param("materialStatus") Integer materialStatus);

    @Select("SELECT material_link_id FROM operation_syn_material_info p where p.material_level_code=#{materialLevelCode} and (p.product_code is null or p.product_code = '') and p.material_link_id is not null order by p.id desc limit 1")
    Long queryDefaultLinkId(@Param("materialLevelCode") String materialLevelCode);
}
