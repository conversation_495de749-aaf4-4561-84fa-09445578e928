package com.cfpamf.ms.insur.operation.pco.vo;

import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import java.util.Date;

/**
 * <AUTHOR> 2022/9/8 14:39
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class UserVo {

    private String regionName;


    private String regionCode;

    private String orgCode;

    /**
     * 机构
     */
    private String organizationName;

    /**
     * 机构
     */
    private String organizationFullName;

    /**
     * 授权用户Id
     */
    private String userId;


    /**
     * 授权用户名
     */
    private String userName;

    /**
     * 岗位名称
     */
    private String postName;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 状态
     */
    private String status;


    private String jobCode;

    private String mainJobNumber;

}
