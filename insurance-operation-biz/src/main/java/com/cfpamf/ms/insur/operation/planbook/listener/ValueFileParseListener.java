package com.cfpamf.ms.insur.operation.planbook.listener;

import com.alibaba.fastjson.JSON;
import com.cfpamf.ms.insur.operation.base.mq.RabbitMqUtils;
import com.cfpamf.ms.insur.operation.msg.service.AuthUserDingTalkService;
import com.cfpamf.ms.insur.operation.msg.service.DingTalkService;
import com.rabbitmq.client.Channel;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.Collections;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Date 2022/1/6
 * @Version 1.0
 */
@Component
@Slf4j
public class ValueFileParseListener {

    /**
     * 推送文件解析状态到mq服务通知钉钉
     */
    public static final String VALUE_EXCHANGE_INS = "operation.value.exchange";
    public static final String VALUE_QUEUE_INS = "operation.value.queue";
    public static final String VALUE_ROUTING_INS = "operation.value.routing";
    @Resource private RabbitMqUtils rabbitMqUtils;

    @Resource private DingTalkService dingTalkService;
    @Resource private AuthUserDingTalkService authUserDingTalkService;

    @RabbitListener(bindings = @QueueBinding(value = @Queue(value = VALUE_QUEUE_INS, durable = "true"),
                                             exchange = @Exchange(value = VALUE_EXCHANGE_INS)),
                    containerFactory = "insuranceOperationFactory")
    public void msgNotify(@Payload @Valid TextMsgContent content, @Header(AmqpHeaders.DELIVERY_TAG) long deliveryTag,
                          Channel channel) {
        try{
            log.info("消息消费，消息内容{}", JSON.toJSONString(content));

            Map<String, String> jobNumberMap = authUserDingTalkService.getJobNumberDingUserIdMap(Collections.singletonList(content.getUserNumber()));
            if (Objects.nonNull(jobNumberMap) && jobNumberMap.containsKey(content.getUserNumber())) {
                dingTalkService.sendTextMessage(jobNumberMap.get(content.getUserNumber()), content.getTextContent());
            } else {
                log.warn(content.getUserNumber() + "文件解析消息通知失败");
            }
        }catch(Exception e) {
            log.warn("消息发送异常-{}", e);
        } finally {
            rabbitMqUtils.manualAcknowledgeMode(true, deliveryTag, channel, false);
        }
    }

    @Data
    static public class TextMsgContent {
        private String userNumber;
        private String textContent;
    }

}
