
package com.cfpamf.ms.insur.operation.order.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Id;

/**
 * Created by zhen<PERSON>jing  on 2022-06-28 11:03:20
 *
 * <AUTHOR>
 */
@Data
@ApiModel("被保人")
public class SmOrderInsuredDto {

    @Id
    private Integer id;

    private String endorId;

    @ApiModelProperty("泛华订单Id")
    private String fhOrderId;

    @ApiModelProperty("被保险人姓名")
    private String personName;
    @ApiModelProperty("产品类型")
    private String productAttrCode;

    @ApiModelProperty("被保险人性别")
    private String personGender;

    @ApiModelProperty("被保险人证件类型")
    private String idType;

    @ApiModelProperty("被保险人证件号")
    private String idNumber;

    @ApiModelProperty("身份证有效期开始时间")
    private String idPeriodStart;

    @ApiModelProperty("身份证有效期结束时间")
    private String idPeriodEnd;

    @ApiModelProperty("被保险人生日")
    private String birthday;

    @ApiModelProperty("被保险人手机号")
    private String cellPhone;

    @ApiModelProperty("被保险人Email")
    private String email;

    @ApiModelProperty("年收入")
    private String annualIncome;

    @ApiModelProperty("航班号")
    private String flightNo;

    @ApiModelProperty("起飞时间")
    private java.time.LocalDateTime flightTime;

    @ApiModelProperty("职业编码")
    private String occupationCode;

    @ApiModelProperty("职业类别")
    private String occupationGroup;

    @ApiModelProperty("职业")
    private String occupation;

    @ApiModelProperty("旅游目的地")
    private String destinationCountryText;

    @ApiModelProperty("地区")
    private String area;

    @ApiModelProperty("地址")
    private String address;

    @ApiModelProperty("承保状态")
    private String appStatus;

    @ApiModelProperty("保单号")
    private String policyNo;

    @ApiModelProperty("保单下载地址")
    private String downloadUrl;

    @ApiModelProperty("学生类别")
    private String studentType;

    @ApiModelProperty("学校类型")
    private String schoolType;

    @ApiModelProperty("学校属性")
    private String schoolNature;

    @ApiModelProperty("学校名称")
    private String schoolName;

    @ApiModelProperty("班级")
    private String schoolClass;

    @ApiModelProperty("学号")
    private String studentId;

    @ApiModelProperty("户籍类型")
    private String hdrType;

    @ApiModelProperty("承保时间")
    private java.time.LocalDateTime insuredTime;

    @ApiModelProperty("退保时间")
    private java.time.LocalDateTime surrenderTime;

    @ApiModelProperty("是否有社保")
    private String isSecurity;

    @ApiModelProperty("是否删除")
    private Integer enabledFlag;
}

