package com.cfpamf.ms.insur.operation.planbook.form;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2021/12/12 19:25
 * @Version 1.0
 */
@Data
public class OperationPlanBookSettingForm {
    private Integer id;

    /**
     * 关联计划书
     */
    @ApiModelProperty(value = "关联计划书Id")
    private Integer planBookId;

    /**
     * 计划书名称
     */
    @ApiModelProperty(value = "计划书名称")
    private String planBookName;

    /**
     * 封面
     */
    @ApiModelProperty(value = "封面")
    private String coverLink;

    /**
     * 产品视频讲解（0:否，1:是）
     */
    @ApiModelProperty(value = "产品视频讲解（0:否，1:是）")
    private Short productVideoSwitch;

    /**
     * 推荐理由
     */
    @ApiModelProperty(value = "推荐理由")
    private Short recommendReasonSwitch;

}
