package com.cfpamf.ms.insur.operation.prospectus.entity;

import com.cfpamf.ms.insur.operation.base.entity.BaseEntity;
import com.cfpamf.ms.insur.operation.prospectus.annotaions.FactorField;
import com.cfpamf.ms.insur.operation.prospectus.enums.GuaranteeUnitEnum;
import com.cfpamf.ms.insur.operation.prospectus.enums.InsuredAmountUnitEnum;
import com.cfpamf.ms.insur.operation.prospectus.enums.PaymentPeriodUnitEnum;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.Table;
import java.math.BigDecimal;

/**
 * operation_product_factor
 *
 * <AUTHOR>
@Getter
@Setter
@Table(name = "operation_product_factor")
public class OperationProductFactor extends BaseEntity {
    /**
     * 产品id
     */
    private Long productId;

    /**
     * 计划id
     */
    private Long planId;
    /**
     * 计划
     */
    @FactorField(description = "计划")
    private Integer plan;
    /**
     * 性别
     */
    @FactorField(description = "性别")
    private String gender;

    /**
     * 被保人年纪
     */
    @FactorField(description = "年龄")
    private Integer age;

    /**
     * 保障类型单位
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "guarantee_unit")
    @FactorField(valueFiled = "guaranteeQuantity", description = "保障类型")
    private GuaranteeUnitEnum guaranteeUnit;

    /**
     * 保障类型量
     */
    private BigDecimal guaranteeQuantity;

    /**
     * 缴费期间单位
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "payment_period_unit")
    @FactorField(valueFiled = "paymentPeriodQuantity", description = "缴费期间单位")
    private PaymentPeriodUnitEnum paymentPeriodUnit;

    /**
     * 缴费期间量
     */
    private BigDecimal paymentPeriodQuantity;

    /**
     * 保费单位
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "insured_amount_unit")
    @FactorField(description = "保费单位")
    private InsuredAmountUnitEnum insuredAmountUnit;

    /**
     * 保费
     */
    private BigDecimal insuredAmountQuantity;

}