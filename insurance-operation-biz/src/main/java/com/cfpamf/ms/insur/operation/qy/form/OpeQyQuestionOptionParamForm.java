
package com.cfpamf.ms.insur.operation.qy.form;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

/**
 * Created by zhengjing  on 2022-08-05 15:21:46
 *
 * <AUTHOR>
 */
@ApiModel("企业微信问卷题目选项参数")
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class OpeQyQuestionOptionParamForm {

    @ApiModelProperty("自定义编码")
    String code;

    @ApiModelProperty("其他描述")
    String desc;

    @ApiModelProperty("其他扩展信息")
    String other;

}

