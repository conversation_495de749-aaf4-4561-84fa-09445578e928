package com.cfpamf.ms.insur.operation.pco.util;

import com.cfpamf.common.ms.util.DateUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellType;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.Objects;
import java.util.function.Function;

/**
 * <AUTHOR>
 */

public enum CellReader implements Function<Cell, Object> {

    /**
     * 行号
     */
    ROW_NUM() {
        @Override
        public Object apply(Cell cell) {
            //不会调用 针对这个做了特殊处理
            return cell.getRow().getRowNum() + 1;
        }
    },
    /**
     * 字符
     */
    STRING() {
        @Override
        public Object apply(Cell cell) {
            return getStrVal(cell);
        }
    },
    /**
     * short
     */
    SHORT() {
        @Override
        public Object apply(Cell cell) {
            String strVal = getStrVal(cell);
            return StringUtils.isBlank(strVal) ? null :
                    Short.valueOf(strVal);
        }
    },

    /**
     * 日期格式的字符
     */
    STRING_DATE() {
        @Override
        public Object apply(Cell cell) {
            if (Objects.isNull(cell)) {
                return null;
            }
            if (cell.getCellTypeEnum() == CellType.NUMERIC) {
                return DateUtil.format(cell.getDateCellValue(), DateUtil.CN_YEAR_MONTH_DAY_FORMAT);
            }
            return getStrVal(cell);
        }
    },
    /**
     * 日期格式的时间
     */
    STRING_DATE_TIME() {
        @Override
        public Object apply(Cell cell) {
            if (Objects.isNull(cell)) {
                return null;
            }
            if (cell.getCellTypeEnum() == CellType.NUMERIC) {
                return DateUtil.format(cell.getDateCellValue(), DateUtil.CN_LONG_FORMAT);
            }
            return getStrVal(cell);
        }
    },
    /**
     * 数字
     */
    DECIMAL() {
        @Override
        public Object apply(Cell cell) {
            if (cell == null) {
                return null;
            }
            if (cell.getCellTypeEnum() == CellType.NUMERIC) {
                return BigDecimal.valueOf(cell.getNumericCellValue());
            }
            String strVal = getStrVal(cell);
            return StringUtils.isBlank(strVal) ? null : new BigDecimal(strVal);
        }
    },
    /**
     * 整性
     */
    INTEGER() {
        @Override
        public Object apply(Cell cell) {
            String strVal = getStrVal(cell);
            return StringUtils.isBlank(strVal) ? null : Integer.valueOf(strVal);
        }
    },
    /**
     * bool
     */
    CUST_BOOL() {
        @Override
        public Object apply(Cell cell) {
            String strVal = getStrVal(cell);
            return StringUtils.isBlank(strVal) ? null :
                    "是".equals(strVal) ? 1 : 0;
        }
    };


    public static String getStrVal(Cell cell) {
        if (cell == null) {
            return null;
        }
        String cellValue;
        switch (cell.getCellTypeEnum()) {
            case NUMERIC:
                DecimalFormat df = new DecimalFormat("#");
                String s = df.format(cell.getNumericCellValue());
                String floatSuffix = ".0";
                if (s.endsWith(floatSuffix)) {
                    s = s.substring(0, s.length() - 2);
                }
                cellValue = s;
                break;
            case STRING:
                cellValue = cell.getStringCellValue();
                break;
            case FORMULA:
                cellValue = cell.getCellFormula();
                break;
            case BOOLEAN:
                cellValue = Boolean.toString(cell.getBooleanCellValue());
                break;
            case ERROR:
                cellValue = Byte.toString(cell.getErrorCellValue());
                break;
            default:
                cellValue = null;
                break;
        }
        return StringUtils.trim(cellValue);
    }
}
