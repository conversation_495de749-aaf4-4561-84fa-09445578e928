package com.cfpamf.ms.insur.operation.base.aop;

import org.springframework.util.StringUtils;
import com.alibaba.fastjson.JSON;
import com.cfpamf.ms.insur.operation.base.exception.BusinessException;
import com.cfpamf.ms.insur.operation.base.util.HttpRequestUtil;
import com.cfpamf.ms.insur.operation.base.util.RedisUtil;
import com.cfpamf.ms.insur.operation.base.vo.WxSessionVO;
import com.cfpamf.ms.insur.operation.fegin.bms.enums.ExcptEnum;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.Ordered;
import org.springframework.stereotype.Component;

import java.util.Objects;

import static com.cfpamf.ms.insur.operation.base.util.HttpRequestUtil.getReferer;
import static com.cfpamf.ms.insur.operation.base.util.HttpRequestUtil.getUserAgent;

/**
 * 微信鉴权切面
 *
 * <AUTHOR>
 * @date 2022/1/7 14:33
 */
@Aspect
@Component
@Slf4j
public class WxTokenAuthAspect implements Ordered {
    @Autowired
    RedisUtil<String, String> redisUtil;

    @Pointcut("@annotation(com.cfpamf.ms.insur.operation.base.annotaions.WxTokenAuth)")
    public void wxTokenAuth() {
    }

    @Before("wxTokenAuth()")
    public void before(JoinPoint joinPoint) {
        String token = HttpRequestUtil.getToken();
        String wxOpenId = HttpRequestUtil.getWxOpenId();
        if (StringUtils.isEmpty(wxOpenId)) {
            log.warn("微信用户token失效 wxOpenId={}, token={}", wxOpenId, token);
            log.warn("微信用户token失效 user-agent={}, referer={}", getUserAgent(), getReferer());
            throw new BusinessException(ExcptEnum.INVALID_TOKEN_501019);
        }
        WxSessionVO session = getWxSession(wxOpenId);
        if (session == null || !Objects.equals(session.getAuthorization(), token)) {
            log.warn("微信用户token失效 wxOpenId={}, token={}, session={}", wxOpenId, token, JSON.toJSONString(session));
            log.warn("微信用户token失效 user-agent= {}, referer={}", getUserAgent(), getReferer());
            throw new BusinessException(ExcptEnum.INVALID_TOKEN_501019);
        }
    }

    /**
     * 获取微信session
     *
     * @param wxOpenId
     * @return
     */
    private WxSessionVO getWxSession(String wxOpenId) {
        String json = redisUtil.get(wxOpenId);
        if (!StringUtils.isEmpty(json)) {
            return JSON.parseObject(json, WxSessionVO.class);
        }
        return null;
    }

    @Override
    public int getOrder() {
        return Integer.MAX_VALUE;
    }
}
