package com.cfpamf.ms.insur.operation.reward.addcommission.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import com.cfpamf.ms.insur.operation.base.annotaions.ExportField;
import com.cfpamf.ms.insur.operation.base.annotaions.Mask;
import com.cfpamf.ms.insur.operation.base.constant.BaseConstants;
import com.cfpamf.ms.insur.operation.base.enums.EnumInsuredAppStatus;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Objects;

/**
 * 小额保险订单vo
 *
 * <AUTHOR>
 */
@Data
@ApiModel
@ContentRowHeight(18)
@HeadRowHeight(20)
@ColumnWidth(30)
public class SmAddCommissionListExcelDto {

    /**
     * 记账时间
     */
    @ExcelProperty("记账时间")
    @ApiModelProperty(value = "记账时间")
    private Date accountTime;

    /**
     * 订单创建日期
     */
    @ExcelProperty("订单创建日期")
    @ApiModelProperty(value = "订单创建日期")
    private Date createTime;

    /**
     * 订单号
     */
    @ExcelProperty("订单号")
    @ApiModelProperty(value = "订单号")
    private String fhOrderId;


    /**
     * 保单号
     */
    @ExcelProperty("保单号")
    @ApiModelProperty(value = "保单号")
    private String policyNo;

    /**
     * 保单号
     */
    @ExcelProperty("批改单号")
    @ApiModelProperty(value = "批改单号")
    private String endorsementNo;


    /**
     * 推荐人所属区域
     */
    @ExcelProperty("推荐人所属区域")
    @ApiModelProperty(value = "所属区域")
    private String recommendRegionName;

    /**
     * 推荐人所属机构
     */
    @ExcelProperty("推荐人所属机构")
    @ApiModelProperty(value = "所属机构")
    private String recommendOrganizationName;

    /**
     * 推荐人员工编号
     */
    @ExcelProperty("推荐人工号")
    @ApiModelProperty(value = "推荐人工号")
    private String recommendId;

    /**
     * 推荐人姓名
     */
    @ExcelProperty("推荐人姓名")
    @ApiModelProperty(value = "推荐人姓名")
    private String recommendUserName;

    /**
     * 投保产品名称
     */
    @ExcelProperty("投保产品名称")
    @ApiModelProperty(value = "投保产品名称")
    private String productName;

    /**
     * 投保计划名称
     */
    @ExcelProperty("计划名称")
    @ApiModelProperty(value = "投保计划名称")
    private String planName;

    @ApiModelProperty("产品类型")
    @ExcelProperty("产品类型")
    private String productTypeName;

    /**
     * 投保人姓名
     */
    @ExcelProperty("投保人姓名")
    @ApiModelProperty(value = "投保人姓名")
    private String applicantPersonName;

    /**
     * 被保人姓名
     */
    @ExcelProperty("被保人姓名")
    @ApiModelProperty(value = "被保人姓名")
    private String insuredPersonName;


    /**
     * 订单金额
     */
    @ExcelProperty("保费")
    @ApiModelProperty(value = "订单金额")
    private BigDecimal totalAmount;

    /**
     * 加佣比例
     */
    @ExcelProperty("加佣比例")
    @ApiModelProperty(value = "加佣比例")
    private BigDecimal addCommissionProportion;

    /**
     * 加佣金额
     */
    @ExcelProperty("加佣金额")
    @ApiModelProperty(value = "加佣金额")
    private BigDecimal addCommissionAmount;

    /**
     * 保单状态
     */
    @ExcelProperty("保单状态")
    @ApiModelProperty(value = "保单状态")
    private String appStatusName;


    @ApiModelProperty(value = "是否整村推进")
    @ExcelProperty("是否整村推进")
    private String villageActivity;


    @ApiModelProperty("订单类型 0-普通订单 1-分销订单")
    @ExcelProperty("是否分销单")
    private String orderType;


}
