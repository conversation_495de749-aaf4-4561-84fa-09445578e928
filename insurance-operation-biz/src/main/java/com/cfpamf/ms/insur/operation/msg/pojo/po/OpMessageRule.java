
package com.cfpamf.ms.insur.operation.msg.pojo.po;

import com.cfpamf.ms.insur.operation.base.entity.BaseNoUserEntity;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import javax.persistence.Table;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Objects;

/**
 * Created by zhengjing  on 2021-07-14 16:27:43
 *
 * <AUTHOR>
 */
@ApiModel("营销工具-消息推送规则")
@Table(name = "op_message_rule")
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class OpMessageRule extends BaseNoUserEntity {

    /**
     * 字段名称 规则编码
     */
    @NotEmpty(message = "规则编码不能为空")
    @ApiModelProperty(value = "规则编码 预设值 不同的code代表推送不同的值")
    String ruleCode;

    /**
     * 字段名称 规则名称
     */
    @ApiModelProperty(value = "规则名称")
    String ruleName;

    @NotNull(message = "推送格式不能为空")
    @ApiModelProperty("推送的数据类型 image-图片 markdown-md格式 text-文本格式")
    String messageType;

    @NotNull(message = "推送的模板内容不能为空")
    @ApiModelProperty("推送的消息内容模板 freemarker模板")
    String messageTemplate;

    /**
     * 字段名称 开始日期
     */
    @NotNull(message = "开始日期不能为空")
    @ApiModelProperty(value = "开始日期")
    java.time.LocalDate startDate;
    /**
     * 字段名称 结束时间 没有就是永远
     */
    @ApiModelProperty(value = "结束时间 没有就是永远")
    java.time.LocalDate endDate;
    /**
     * 字段名称 推送的cron表达式
     */
    @NotNull(message = "推送时间配置不能为空")
    @ApiModelProperty(value = "推送的cron表达式")
    String cronRule;
    /**
     * 字段名称 参数
     */
    @ApiModelProperty(value = "参数")
    String params;

    @ApiModelProperty("状态 1-启用 0-停用")
    Integer state;
    /**
     * 字段名称 规则描述
     */
    @ApiModelProperty(value = "规则描述")
    String remark;
    /**
     * 字段名称
     */
    @ApiModelProperty(value = "")
    String createBy;
    /**
     * 字段名称
     */
    @ApiModelProperty(value = "")
    String updateBy;

    /**
     * 绑定的自定义组件的编码
     */
    @ApiModelProperty(value = "绑定的自定义组件")
    String bindComponent;

    /**
     * 绑定的自定义组件的编码
     */
    @ApiModelProperty(value = "多张图片推送间隔时长，仅多图推送有效")
    Integer intervalMins;

    @ApiModelProperty(value = "钉钉模板消息，模板Id")
    String cardTemplateId;

    @ApiModelProperty(value = "钉钉模板消息，发送机器人")
    String robotCode;

    @ApiModelProperty("图片性质 动态图dynamics 静态图static,默认 static")
    String imageNature;


    @JsonIgnore
    public boolean isActive() {
        return Objects.equals(1, state);
    }
}

