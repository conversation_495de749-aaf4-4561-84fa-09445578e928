package com.cfpamf.ms.insur.operation.qy.controller;

import com.cfpamf.ms.insur.operation.base.annotaions.ResponseDecorated;
import com.cfpamf.ms.insur.operation.base.constant.BaseConstants;
import com.cfpamf.ms.insur.operation.qy.form.QyJsSdkForm;
import com.cfpamf.ms.insur.operation.qy.service.WxCpProxyService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.experimental.FieldDefaults;
import me.chanjar.weixin.common.bean.WxJsapiSignature;
import me.chanjar.weixin.cp.bean.WxCpAgentJsapiSignature;
import me.chanjar.weixin.cp.bean.WxCpOauth2UserInfo;
import me.chanjar.weixin.cp.bean.external.contact.WxCpExternalContactInfo;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR> 2022/8/4 10:47
 */
@RestController
@RequestMapping(value = {BaseConstants.PUB_VERSION + "/qy/tool"})
@Api(value = "企业微信", tags = {"企业微信相关接口"})
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@AllArgsConstructor
@ResponseDecorated
public class QyToolController {

    WxCpProxyService wxCpProxyService;

    @ApiOperation("js签名")
    @PostMapping("/jsapi/signature")
    public WxJsapiSignature jsapiSignature(@RequestBody QyJsSdkForm form) {
        return wxCpProxyService.jsapiSignature(form);
    }

    @ApiOperation("agent js签名")
    @PostMapping("/agent/jsapi/signature")
    public WxCpAgentJsapiSignature agentJsapiSignature(@RequestBody QyJsSdkForm form) {
        return wxCpProxyService.agentJsapiSignature(form);
    }

    @ApiOperation("获取用户信息")
    @GetMapping("/oauth2/userinfo")
    public WxCpOauth2UserInfo userinfo(@RequestParam("code") String code) {
        return wxCpProxyService.userInfo(code);
    }

    @ApiOperation("获取外部联系人信息")
    @GetMapping("/externalcontact/contactdetail")
    public WxCpExternalContactInfo contractDetail(@RequestParam("userid") String userid) {
        return wxCpProxyService.contactDetail(userid);
    }
}
