package com.cfpamf.ms.insur.operation.customer.service;

import com.alibaba.fastjson.JSON;
import com.cfpamf.common.ms.exception.MSBizNormalException;
import com.cfpamf.ms.insur.operation.base.enums.EnumInsuredAppStatus;
import com.cfpamf.ms.insur.operation.base.service.CustService;
import com.cfpamf.ms.insur.operation.customer.dao.*;
import com.cfpamf.ms.insur.operation.customer.dto.CustomerDto;
import com.cfpamf.ms.insur.operation.customer.dto.PolicyInfoNotifyMessage;
import com.cfpamf.ms.insur.operation.customer.po.*;
import com.cfpamf.ms.insur.operation.fegin.bms.enums.ExcptEnum;
import com.cfpamf.ms.insur.operation.fegin.customer.request.CustBaseQueryByIdNoRequest;
import com.cfpamf.ms.insur.operation.fegin.customer.response.CustBaseInfoVo;
import com.cfpamf.ms.insur.operation.msg.dao.OrderMapper;
import com.cfpamf.ms.insur.operation.order.dao.SmOrderApplicantMapper;
import com.cfpamf.ms.insur.operation.order.dao.SmOrderInsuredMapper;
import com.cfpamf.ms.insur.operation.order.dto.SmOrderDto;
import com.cfpamf.ms.insur.operation.phoenix.enums.EnumTodoBizType;
import com.cfpamf.ms.insur.operation.phoenix.service.PhoenixEmpTodoService;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class CustomerCustFirstService {

    OrderMapper orderMapper;

    SmOrderInsuredMapper smOrderInsuredMapper;

    SmOrderApplicantMapper smOrderApplicantMapper;

    CustService custService;

    CustomerLoanFirstMapper customerLoanFirstMapper;

    PhoenixEmpTodoService phoenixEmpTodoService;

    CustomerLoanFirstFollowMapper customerLoanFirstFollowMapper;
    /**
     * 记录客户投保订单明细
     *
     * @param message
     */
    @Transactional(rollbackFor = Exception.class)
    public void handleMessage(PolicyInfoNotifyMessage message) {
        SmOrderDto orderDto = orderMapper.getOrderInfoByOrderId(message.getFhOrderId());
        log.info("A类客户转化-订单信息：{}", JSON.toJSONString(orderDto));
        if (Objects.isNull(orderDto)) {
            return;
        }

        //承保成功记录转化保单并移除A类待办
        if (EnumInsuredAppStatus.SUCCESS.getCode().equals(orderDto.getAppStatus())) {
            List<CustomerDto> customerDtos = new ArrayList<>();
            //投保人转化
            applicantCust(orderDto,customerDtos);
            //被保人转化
            insuredCust(orderDto,customerDtos);

            List<CustomerLoanFirstPo>  loanFirstList = getLoanFirstIdNumbers(orderDto,customerDtos);
            if (!CollectionUtils.isEmpty(loanFirstList)) {
                //更新A类客户信息（首次转化保单）
                customerLoanFirstMapper.updateByLoanCustId(loanFirstList);
                //移除待办
                List<String> targetIds = loanFirstList.stream().map(CustomerLoanFirstPo::getLoanCustId).collect(Collectors.toList());
                phoenixEmpTodoService.finishTodo(EnumTodoBizType.LOAN_FIRST,targetIds, "已转化");
                //插入跟进记录
                customerLoanFirstFollowMapper.insertCustFollow(loanFirstList);
            }
        }
    }

    private List<CustomerLoanFirstPo> getLoanFirstIdNumbers(SmOrderDto orderDto,List<CustomerDto> idNumbers) {
        List<CustomerLoanFirstPo> customerLoanFirstPoList = new ArrayList<>();
        idNumbers.stream().distinct().forEach(x->{
            String loanCustId = getLoanCustId(x.getIdNumber());
            if (!StringUtils.isEmpty(loanCustId)) {
                CustomerLoanFirstPo po = new CustomerLoanFirstPo();
                po.setIdNumber(x.getIdNumber());
                po.setCustomerId(x.getCustomerId());
                po.setLoanCustId(loanCustId);
                po.setConversionOrderId(orderDto.getFhOrderId());
                po.setConversionState(1);
                po.setConversionEmp(orderDto.getCustomerAdminId());
                po.setConversionTime(orderDto.getAccountTime());
                customerLoanFirstPoList.add(po);
            }
        });

        if (!CollectionUtils.isEmpty(customerLoanFirstPoList)) {
            List<String> filterList = customerLoanFirstMapper.selectByLoanCustIds(customerLoanFirstPoList);
            if (!CollectionUtils.isEmpty(filterList)) {
                return customerLoanFirstPoList.stream().filter(x->filterList.contains(x.getLoanCustId())).collect(Collectors.toList());
            }
            return customerLoanFirstPoList;
        }
        return new ArrayList<>();
    }

    private void insuredCust(SmOrderDto orderDto, List<CustomerDto> customerDtos) {
        List<CustomerDto> idNumberList = smOrderInsuredMapper.getIdNumberByOrderId(orderDto.getFhOrderId());
        if (!CollectionUtils.isEmpty(idNumberList)) {
            customerDtos.addAll(idNumberList);
        }
    }

    private void applicantCust(SmOrderDto orderDto, List<CustomerDto> customerDtos) {
        CustomerDto idNumber = smOrderApplicantMapper.getIdNumberByOrderId(orderDto.getFhOrderId());
        if (!Objects.isNull(idNumber)) {
            customerDtos.add(idNumber);
        }
    }

    public String getLoanCustId(String idNumber) {
        CustBaseQueryByIdNoRequest request = new CustBaseQueryByIdNoRequest();
        request.setIdNo(idNumber);
        CustBaseInfoVo custBaseInfoVo = custService.baseInfoByIdNo(request);
        return Objects.isNull(custBaseInfoVo)?"":Objects.isNull(custBaseInfoVo.getCustDetail())?"":custBaseInfoVo.getCustDetail().getLoanCustId();
    }
}
