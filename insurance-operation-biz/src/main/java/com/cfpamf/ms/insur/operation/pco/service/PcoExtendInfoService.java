package com.cfpamf.ms.insur.operation.pco.service;

import com.cfpamf.ms.bms.facade.vo.DingTalkUserBmsVO;
import com.cfpamf.ms.insur.operation.base.helper.BmsHelper;
import com.cfpamf.ms.insur.operation.dingtalk.service.DingTalkSdkMissService;
import com.cfpamf.ms.insur.operation.msg.dao.UserPostMapper;
import com.cfpamf.ms.insur.operation.msg.enums.EnumEmpPost;
import com.cfpamf.ms.insur.operation.msg.pojo.po.UserPostSimple;
import com.cfpamf.ms.insur.operation.msg.service.DingTalkService;
import com.cfpamf.ms.insur.operation.pco.dao.PcoExtendInfoMapper;
import com.cfpamf.ms.insur.operation.pco.entity.PcoExtendInfo;
import com.cfpamf.ms.insur.operation.pco.enums.PcoLevelEnum;
import com.cfpamf.ms.insur.operation.pco.query.PcoPageQuery;
import com.cfpamf.ms.insur.operation.pco.vo.UserVo;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR> 2022/9/7 09:25
 */
@Slf4j
@Service
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class PcoExtendInfoService {
    static final String PCO_POST_CODE = "2357";
    PcoExtendInfoMapper extendInfoMapper;

    DingTalkService dingTalkService;

    UserPostMapper postMapper;

    DingTalkSdkMissService dingTalkSdkMissService;

    BmsHelper bmsHelper;


    /**
     * 查询用户信息
     *
     * @param pageForm
     * @return
     */
    public PageInfo<UserVo> listUserByCode(PcoPageQuery pageForm) {
        pageForm.init();
        return PageHelper.startPage(pageForm.getPageNo(), pageForm.getPageSize())
                .doSelectPageInfo(() -> extendInfoMapper.selectUserByPost(pageForm.getKeyword(), PCO_POST_CODE));
    }


    /**
     * 查询缺少钉钉信息的id
     *
     * @return
     */
    public List<String> selectMissInfo() {
        return extendInfoMapper.selectMissInfoDingUser();
    }

    public void syncDingUserByPost() {
        List<UserPostSimple> simples = postMapper.selectPcoByNotSync(EnumEmpPost.PCO.getCode());

        List<String> notMobiles = simples.stream()
                .map(UserPostSimple::getMobile)
                .distinct().filter(StringUtils::isNotBlank)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(notMobiles)) {
            log.info("syncDingUserByPost 需要同步的信息为空");
            return;

        }
        Map<String, DingTalkUserBmsVO> map = bmsHelper.listDingTalkUserDetailByMobiles(notMobiles);

        List<PcoExtendInfo> infos = simples.stream()
                .filter(s -> StringUtils.isNotBlank(s.getMobile()))
                // 数据库没有的数据
                .filter(s -> Objects.isNull(s.getId()))
                .map(s -> {
                    DingTalkUserBmsVO dingTalkUser = map.getOrDefault(s.getMobile(), new DingTalkUserBmsVO());
                    PcoExtendInfo extendInfo = initPcoExtend();
                    extendInfo.setDingUserId(dingTalkUser.getDingTalkUserId());
                    extendInfo.setJobNumber(s.getJobNumber());
                    extendInfo.setJobCode(s.getJobCode());
                    if (StringUtils.isNotBlank(extendInfo.getDingUserId())) {
                        extendInfo.setAvatar(dingTalkUser.getAvatar());
                    } else {
                        // 存起来让功能正常
                        extendInfo.setDingUserId("");
                        log.warn("syncDingUserByPost 查询钉钉ID失败[{}-{}]", s.getMainJobNumber(), s.getMobile());
                    }
                    return extendInfo;
                }).collect(Collectors.toList());

        // 之前初始化了 但是没有id的数据
        List<PcoExtendInfo> update = simples.stream()
                .filter(s -> StringUtils.isNotBlank(s.getMobile()))
                // 数据库没有的数据
                .filter(s -> Objects.nonNull(s.getId())).map(s -> {
                    DingTalkUserBmsVO dingTalkUser = map.getOrDefault(s.getMobile(), new DingTalkUserBmsVO());
                    PcoExtendInfo info = new PcoExtendInfo();
                    info.setId(s.getId());
                    info.setDingUserId(dingTalkUser.getDingTalkUserId());
                    if (StringUtils.isNotBlank(info.getDingUserId())) {
                        info.setAvatar(dingTalkUser.getAvatar());
                    }
                    return info;
                }).filter(s -> Objects.nonNull(s.getDingUserId())).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(infos)) {
            int i = extendInfoMapper.insertList(infos);
            log.info("成功同步[{}]行记录", i);
        }

        //修改
        if (!CollectionUtils.isEmpty(update)) {
            update.forEach(extendInfoMapper::updateByPrimaryKeySelective);
            log.info("成功同步[{}]行记录", update.size());
        }

    }

    public void syncDingUserInfo() {
        List<String> userIds = selectMissInfo();
        if (CollectionUtils.isEmpty(userIds)) {
            return;
        }
        List<PcoExtendInfo> pcoInfo = userIds.parallelStream()
                .map(dingTalkService::getUserInfo)
                .filter(Objects::nonNull)
                .map(s -> {
                    PcoExtendInfo pcoExtendInfo = initPcoExtend();
                    pcoExtendInfo.setJobNumber(s.getJobnumber());
                    if (Objects.isNull(s.getJobnumber())) {
                        log.warn("钉钉返回工号为空：[{}]", s.getUserid());
                        pcoExtendInfo.setJobNumber("");
                    }
                    pcoExtendInfo.setDingUserId(s.getUserid());
                    pcoExtendInfo.setAvatar(s.getAvatar());
                    return pcoExtendInfo;
                }).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(pcoInfo)) {
            log.warn("部分数据未查询到钉钉用户信息:[{}]", userIds);
            return;
        }
        int i = extendInfoMapper.insertList(pcoInfo);
        int i1 = extendInfoMapper.updateJobCode();
        log.info("新增PCO数据 [{}],修改job code成功数量[{}]", i, i1);
    }

    private PcoExtendInfo initPcoExtend() {
        PcoExtendInfo pcoExtendInfo = new PcoExtendInfo();
        pcoExtendInfo.setJobNumber("");
        pcoExtendInfo.setPcoLevel(PcoLevelEnum.CLEVEL.getValue());
        pcoExtendInfo.setJobCode("");
        pcoExtendInfo.setBeQualified(1);
        pcoExtendInfo.setNewestAreaScope(-1);
        pcoExtendInfo.setNewestSelfScope(-1);
        pcoExtendInfo.setOtherInfo("{}");
        return pcoExtendInfo;
    }


}
