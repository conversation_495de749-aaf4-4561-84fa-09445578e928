package com.cfpamf.ms.insur.operation.promotion.service.impl;

import com.cfpamf.ms.insur.operation.activity.entity.AuthUser;
import com.cfpamf.ms.insur.operation.promotion.dao.MaterialChangeNotifyDetailMapper;
import com.cfpamf.ms.insur.operation.promotion.dao.MaterialChangeUserNotifyDetailMapper;
import com.cfpamf.ms.insur.operation.promotion.entity.MaterialChangeNotifyDetailEntity;
import com.cfpamf.ms.insur.operation.promotion.entity.MaterialChangeUserNotifyDetailEntity;
import com.cfpamf.ms.insur.operation.promotion.service.MaterialUserNotifyService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
public class MaterialUserNotifyServiceImpl implements MaterialUserNotifyService {
    @Autowired
    private MaterialChangeUserNotifyDetailMapper materialChangeUserNotifyDetailMapper;
    @Autowired
    MaterialChangeNotifyDetailMapper materialChangeNotifyDetailMapper;
    @Override
    @Transactional
    public void generateUserNotify(List<AuthUser> userList, MaterialChangeNotifyDetailEntity notify) {
        if(CollectionUtils.isEmpty(userList)){
            return ;
        }
        List<MaterialChangeUserNotifyDetailEntity> userNotifyList = userList.stream().map(u->{
            MaterialChangeUserNotifyDetailEntity userNotify = new MaterialChangeUserNotifyDetailEntity();
            BeanUtils.copyProperties(notify,userNotify);
            userNotify.setNotifyId(notify.getNotifyId());
            userNotify.setMarketingId(notify.getMarketingId());
            userNotify.setChangeTime(notify.getCreateTime());
            userNotify.setUserName(u.getUserName());
            userNotify.setWxOpenId(u.getWxOpenId());
            userNotify.setUserId(u.getUserId());
            userNotify.setOrgCode(u.getOrgCode());
            userNotify.setOrgName(u.getOrganizationName());
            userNotify.setSendStatus(0);

            return userNotify ;
        }).collect(Collectors.toList());


        materialChangeUserNotifyDetailMapper.insertList(userNotifyList);
        notify.setGenerateStatus(1);
        materialChangeNotifyDetailMapper.updateByPrimaryKeySelective(notify);
    }
}
