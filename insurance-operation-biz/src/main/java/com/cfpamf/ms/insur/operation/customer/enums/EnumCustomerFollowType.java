package com.cfpamf.ms.insur.operation.customer.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @Date 2023/04/19 15:37
 * @Version 1.0
 */
@AllArgsConstructor
@Getter
public enum EnumCustomerFollowType {

    /**
     * 服务方式 wechat:微信,phone:电话,offline_visit:线下拜访,lose_contact:联系不上
     */
    INTERRUPTION("INTERRUPTION","断保捞回"),
    AI_CALL("AI_CALL","智能外呼"),
    LOAN("LOAN","A类大额客户"),
    LOAN_FIRST("LOAN_FIRST","A类客户转化");

    String code;
    String desc;
    public static String dict(String code) {
        if(Objects.isNull(code)){
            return "";
        }
        return Stream.of(values())
                .filter(em -> em.getCode().equals(code))
                .findFirst().map(EnumCustomerFollowType::getDesc)
                .orElse("");
    }
}
