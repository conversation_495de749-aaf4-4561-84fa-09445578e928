package com.cfpamf.ms.insur.operation.pco.service;

import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.cfpamf.ms.insur.operation.base.util.LambdaUtils;
import com.cfpamf.ms.insur.operation.dingtalk.entity.DingTalkSwFormInstanceDetail;
import com.cfpamf.ms.insur.operation.pco.entity.Dictionary;
import com.cfpamf.ms.insur.operation.pco.vo.PcoDayFormVo;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.github.jsonzou.jmockdata.JMockData;
import com.github.jsonzou.jmockdata.TypeReference;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import java.io.File;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.alibaba.excel.EasyExcelFactory.write;
import static com.alibaba.excel.EasyExcelFactory.writerSheet;

/**
 * <AUTHOR> 2022/9/15 14:04
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class TempTEst {

    public static void main(String[] args) {
        List<Dictionary> dictionaries = JMockData.mock(new TypeReference<List<Dictionary>>() {
        });

        ObjectMapper mapper = new ObjectMapper();
        mapper.registerModule(new JavaTimeModule());
        Map<String, String> labelMap = LambdaUtils.toMap(dictionaries, Dictionary::getCode, Dictionary::getName);
        List<PcoDayFormVo> list = JMockData.mock(new TypeReference<List<PcoDayFormVo>>() {
        });
        List<Map<String, Object>> maps = list.stream()
                .map(s -> {
                    //组装模板参数
                    Map<String, Object> map = mapper.convertValue(s, Map.class);
                    s.getForms().sort(Comparator.comparing(DingTalkSwFormInstanceDetail::getId));
                    for (int i = 0; i < s.getForms().size(); i++) {
                        DingTalkSwFormInstanceDetail dingTalkSwFormInstanceDetail = s.getForms().get(i);
                        map.put("pco_form_" + i, dingTalkSwFormInstanceDetail.getFormValue());
                        map.put("pco_form_" + i + "_scope", dingTalkSwFormInstanceDetail.getScope());
                    }
                    return map;
                }).collect(Collectors.toList());

        //模板地址用一个写死的oss地址 方便调整
        ExcelWriter excelWriter = write(new File("test-easy-temp.xlsx")).withTemplate(new File("/Users/<USER>/gitwork/zhnx/insurance-operation/insurance-operation-biz/src/main/resources/template/pco_day_form.xlsx")).build();
        WriteSheet writeSheet = writerSheet().build();
        excelWriter.fill(labelMap, writeSheet);
        excelWriter.fill(maps, writeSheet);
        excelWriter.finish();
    }
}
