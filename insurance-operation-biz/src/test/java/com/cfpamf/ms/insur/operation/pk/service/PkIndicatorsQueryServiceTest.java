package com.cfpamf.ms.insur.operation.pk.service;

import com.cfpamf.ms.insur.operation.phoenix.service.WhaleApiService;
import com.cfpamf.ms.insur.operation.pk.dao.OrganizationDao;
import com.cfpamf.ms.insur.operation.pk.dao.PkDao;
import com.cfpamf.ms.insur.operation.pk.pojo.dto.HistoryIndicatorsDto;
import com.cfpamf.ms.insur.operation.pk.pojo.dto.InsIndicators;
import com.cfpamf.ms.insur.operation.pk.pojo.dto.MonthEmpCntDto;
import com.cfpamf.ms.insur.operation.pk.pojo.dto.PkMatchOpponentsDto;
import com.cfpamf.ms.insur.operation.pk.pojo.req.IndicatorsRequest;
import com.cfpamf.ms.insur.operation.pk.pojo.req.PkMatchOpponentsRequest;
import com.cfpamf.ms.insur.operation.pk.pojo.resp.IndicatorsResponse;
import com.cfpamf.ms.insur.operation.pk.pojo.resp.IndicatorsVO;
import com.cfpamf.ms.insur.operation.pk.pojo.resp.PageResp;
import com.cfpamf.ms.insur.operation.pk.pojo.resp.PkMatchOpponentsResponse;
import com.cfpamf.ms.insur.operation.pk.safepgdao.AdsInsuranceIndexProgressDao;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.mockito.Mockito.when;

@RunWith(SpringJUnit4ClassRunner.class)
public class PkIndicatorsQueryServiceTest {

    @Mock
    private PkDao mockPkDao;
    @Mock
    private OrganizationDao mockOrganizationDao;
    @Mock
    private AdsInsuranceIndexProgressDao mockAdsInsuranceIndexProgressDao;
    @Mock
    private WhaleApiService mockWhaleApiService;

    private PkIndicatorsQueryService pkIndicatorsQueryServiceUnderTest;

    @Before
    public void setUp() throws Exception {
        pkIndicatorsQueryServiceUnderTest = new PkIndicatorsQueryService(mockPkDao, mockOrganizationDao,
                mockAdsInsuranceIndexProgressDao, mockWhaleApiService);
    }

    @Test
    public void testMatchOpponents() {
        // Setup
        final PkMatchOpponentsRequest request = new PkMatchOpponentsRequest();
        request.setType("2");
        request.setUserCode("userCode");
        request.setBranchCode("branchCode");
        request.setRegionCode("regionCode");
        request.setBlackUserCodeList(Arrays.asList("value"));
        request.setIndicatorsType("40");
        request.setIndicatorsMaxValue(new BigDecimal("0.00"));
        request.setIndicatorsMinValue(new BigDecimal("0.00"));
        request.setMatchName("matchName");
        request.setStartDate(LocalDate.of(2020, 1, 1));
        request.setEndDate(LocalDate.of(2020, 1, 1));
        request.setPageNum(0);
        request.setPageSize(0);
        request.setMatchValue(new BigDecimal("0.00"));

        final PageResp<PkMatchOpponentsResponse> expectedResult = new PageResp<>();
        expectedResult.setRecords(Arrays.asList());
        expectedResult.setTotal(0L);
        expectedResult.setSize(0L);
        expectedResult.setCurrent(0L);
        expectedResult.setOptimizeCountSql(false);
        expectedResult.setSearchCount(false);
        expectedResult.setHitCount(false);
        expectedResult.setCountId("countId");
        expectedResult.setMaxLimit(0L);

        // Configure AdsInsuranceIndexProgressDao.selectAreaHistoryIndicatorsMe(...).
        final PkMatchOpponentsDto pkMatchOpponentsDto = new PkMatchOpponentsDto();
        pkMatchOpponentsDto.setType("2");
        pkMatchOpponentsDto.setUserCode("userCode");
        pkMatchOpponentsDto.setUserName("userName");
        pkMatchOpponentsDto.setBranchCode("branchCode");
        pkMatchOpponentsDto.setBranchName("branchName");
        pkMatchOpponentsDto.setRegionCode("regionCode");
        pkMatchOpponentsDto.setRegionName("regionName");
        pkMatchOpponentsDto.setIndicatorType("2");
        pkMatchOpponentsDto.setValue(new BigDecimal("0.00"));
        when(mockAdsInsuranceIndexProgressDao.selectAreaHistoryIndicatorsMe(new PkMatchOpponentsRequest()))
                .thenReturn(pkMatchOpponentsDto);

        // Configure AdsInsuranceIndexProgressDao.selectAreaHistoryIndicatorsMatch(...).
        final PkMatchOpponentsDto pkMatchOpponentsDto1 = new PkMatchOpponentsDto();
        pkMatchOpponentsDto1.setType("2");
        pkMatchOpponentsDto1.setUserCode("userCode");
        pkMatchOpponentsDto1.setUserName("userName");
        pkMatchOpponentsDto1.setBranchCode("branchCode");
        pkMatchOpponentsDto1.setBranchName("branchName");
        pkMatchOpponentsDto1.setRegionCode("regionCode");
        pkMatchOpponentsDto1.setRegionName("regionName");
        pkMatchOpponentsDto1.setIndicatorType("2");
        pkMatchOpponentsDto1.setValue(new BigDecimal("0.00"));
        final List<PkMatchOpponentsDto> pkMatchOpponentsDtos = Arrays.asList(pkMatchOpponentsDto1);
        when(mockAdsInsuranceIndexProgressDao.selectAreaHistoryIndicatorsMatch(
                new PkMatchOpponentsRequest())).thenReturn(pkMatchOpponentsDtos);

        pkIndicatorsQueryServiceUnderTest.matchOpponents(request);

    }

    @Test
    public void testMatchOpponents_AdsInsuranceIndexProgressDaoSelectAreaHistoryIndicatorsMeReturnsNull() {
        // Setup
        final PkMatchOpponentsRequest request = new PkMatchOpponentsRequest();
        request.setType("2");
        request.setUserCode("userCode");
        request.setBranchCode("branchCode");
        request.setRegionCode("regionCode");
        request.setBlackUserCodeList(Arrays.asList("value"));
        request.setIndicatorsType("40");
        request.setIndicatorsMaxValue(new BigDecimal("0.00"));
        request.setIndicatorsMinValue(new BigDecimal("0.00"));
        request.setMatchName("matchName");
        request.setStartDate(LocalDate.of(2020, 1, 1));
        request.setEndDate(LocalDate.of(2020, 1, 1));
        request.setPageNum(0);
        request.setPageSize(0);
        request.setMatchValue(new BigDecimal("0.00"));

        final PageResp<PkMatchOpponentsResponse> expectedResult = new PageResp<>();
        expectedResult.setRecords(Arrays.asList());
        expectedResult.setTotal(0L);
        expectedResult.setSize(0L);
        expectedResult.setCurrent(0L);
        expectedResult.setOptimizeCountSql(false);
        expectedResult.setSearchCount(false);
        expectedResult.setHitCount(false);
        expectedResult.setCountId("countId");
        expectedResult.setMaxLimit(0L);

        when(mockAdsInsuranceIndexProgressDao.selectAreaHistoryIndicatorsMe(new PkMatchOpponentsRequest()))
                .thenReturn(null);

        // Configure AdsInsuranceIndexProgressDao.selectAreaHistoryIndicatorsMatch(...).
        final PkMatchOpponentsDto pkMatchOpponentsDto = new PkMatchOpponentsDto();
        pkMatchOpponentsDto.setType("2");
        pkMatchOpponentsDto.setUserCode("userCode");
        pkMatchOpponentsDto.setUserName("userName");
        pkMatchOpponentsDto.setBranchCode("branchCode");
        pkMatchOpponentsDto.setBranchName("branchName");
        pkMatchOpponentsDto.setRegionCode("regionCode");
        pkMatchOpponentsDto.setRegionName("regionName");
        pkMatchOpponentsDto.setIndicatorType("2");
        pkMatchOpponentsDto.setValue(new BigDecimal("0.00"));
        final List<PkMatchOpponentsDto> pkMatchOpponentsDtos = Arrays.asList(pkMatchOpponentsDto);
        when(mockAdsInsuranceIndexProgressDao.selectAreaHistoryIndicatorsMatch(
                new PkMatchOpponentsRequest())).thenReturn(pkMatchOpponentsDtos);

        // Run the test
        final PageResp<PkMatchOpponentsResponse> result = pkIndicatorsQueryServiceUnderTest.matchOpponents(request);

    }

    @Test
    public void testMatchOpponents_AdsInsuranceIndexProgressDaoSelectOrgHistoryIndicatorsMeReturnsNull() {
        // Setup
        final PkMatchOpponentsRequest request = new PkMatchOpponentsRequest();
        request.setType("2");
        request.setUserCode("userCode");
        request.setBranchCode("branchCode");
        request.setRegionCode("regionCode");
        request.setBlackUserCodeList(Arrays.asList("value"));
        request.setIndicatorsType("40");
        request.setIndicatorsMaxValue(new BigDecimal("0.00"));
        request.setIndicatorsMinValue(new BigDecimal("0.00"));
        request.setMatchName("matchName");
        request.setStartDate(LocalDate.of(2020, 1, 1));
        request.setEndDate(LocalDate.of(2020, 1, 1));
        request.setPageNum(0);
        request.setPageSize(0);
        request.setMatchValue(new BigDecimal("0.00"));

        final PageResp<PkMatchOpponentsResponse> expectedResult = new PageResp<>();
        expectedResult.setRecords(Arrays.asList());
        expectedResult.setTotal(0L);
        expectedResult.setSize(0L);
        expectedResult.setCurrent(0L);
        expectedResult.setOptimizeCountSql(false);
        expectedResult.setSearchCount(false);
        expectedResult.setHitCount(false);
        expectedResult.setCountId("countId");
        expectedResult.setMaxLimit(0L);

        when(mockAdsInsuranceIndexProgressDao.selectOrgHistoryIndicatorsMe(new PkMatchOpponentsRequest()))
                .thenReturn(null);

        // Configure AdsInsuranceIndexProgressDao.selectOrgHistoryIndicatorsMatch(...).
        final PkMatchOpponentsDto pkMatchOpponentsDto = new PkMatchOpponentsDto();
        pkMatchOpponentsDto.setType("2");
        pkMatchOpponentsDto.setUserCode("userCode");
        pkMatchOpponentsDto.setUserName("userName");
        pkMatchOpponentsDto.setBranchCode("branchCode");
        pkMatchOpponentsDto.setBranchName("branchName");
        pkMatchOpponentsDto.setRegionCode("regionCode");
        pkMatchOpponentsDto.setRegionName("regionName");
        pkMatchOpponentsDto.setIndicatorType("2");
        pkMatchOpponentsDto.setValue(new BigDecimal("0.00"));
        final List<PkMatchOpponentsDto> pkMatchOpponentsDtos = Arrays.asList(pkMatchOpponentsDto);
        when(mockAdsInsuranceIndexProgressDao.selectOrgHistoryIndicatorsMatch(
                new PkMatchOpponentsRequest())).thenReturn(pkMatchOpponentsDtos);

        // Run the test
        final PageResp<PkMatchOpponentsResponse> result = pkIndicatorsQueryServiceUnderTest.matchOpponents(request);
    }

    @Test
    public void testMatchOpponents_AdsInsuranceIndexProgressDaoSelectEmpHistoryIndicatorsMeReturnsNull() {
        // Setup
        final PkMatchOpponentsRequest request = new PkMatchOpponentsRequest();
        request.setType("2");
        request.setUserCode("userCode");
        request.setBranchCode("branchCode");
        request.setRegionCode("regionCode");
        request.setBlackUserCodeList(Arrays.asList("value"));
        request.setIndicatorsType("40");
        request.setIndicatorsMaxValue(new BigDecimal("0.00"));
        request.setIndicatorsMinValue(new BigDecimal("0.00"));
        request.setMatchName("matchName");
        request.setStartDate(LocalDate.of(2020, 1, 1));
        request.setEndDate(LocalDate.of(2020, 1, 1));
        request.setPageNum(0);
        request.setPageSize(0);
        request.setMatchValue(new BigDecimal("0.00"));

        final PageResp<PkMatchOpponentsResponse> expectedResult = new PageResp<>();
        expectedResult.setRecords(Arrays.asList());
        expectedResult.setTotal(0L);
        expectedResult.setSize(0L);
        expectedResult.setCurrent(0L);
        expectedResult.setOptimizeCountSql(false);
        expectedResult.setSearchCount(false);
        expectedResult.setHitCount(false);
        expectedResult.setCountId("countId");
        expectedResult.setMaxLimit(0L);

        when(mockAdsInsuranceIndexProgressDao.selectEmpHistoryIndicatorsMe(new PkMatchOpponentsRequest()))
                .thenReturn(null);

        // Configure AdsInsuranceIndexProgressDao.selectEmpHistoryIndicatorsMathch(...).
        final PkMatchOpponentsDto pkMatchOpponentsDto = new PkMatchOpponentsDto();
        pkMatchOpponentsDto.setType("2");
        pkMatchOpponentsDto.setUserCode("userCode");
        pkMatchOpponentsDto.setUserName("userName");
        pkMatchOpponentsDto.setBranchCode("branchCode");
        pkMatchOpponentsDto.setBranchName("branchName");
        pkMatchOpponentsDto.setRegionCode("regionCode");
        pkMatchOpponentsDto.setRegionName("regionName");
        pkMatchOpponentsDto.setIndicatorType("2");
        pkMatchOpponentsDto.setValue(new BigDecimal("0.00"));
        final List<PkMatchOpponentsDto> pkMatchOpponentsDtos = Arrays.asList(pkMatchOpponentsDto);
        when(mockAdsInsuranceIndexProgressDao.selectEmpHistoryIndicatorsMathch(
                new PkMatchOpponentsRequest())).thenReturn(pkMatchOpponentsDtos);

        // Run the test
        final PageResp<PkMatchOpponentsResponse> result = pkIndicatorsQueryServiceUnderTest.matchOpponents(request);

    }

    @Test
    public void testQueryPkHistoryIndicators() {
        // Setup
        final IndicatorsRequest indicatorsRequest = new IndicatorsRequest();
        indicatorsRequest.setType("2");
        indicatorsRequest.setCodeList(Arrays.asList("value"));
        indicatorsRequest.setIndicatorTypeList(Arrays.asList("40"));
        indicatorsRequest.setStartDate(LocalDate.of(2020, 1, 1));
        indicatorsRequest.setEndDate(LocalDate.of(2020, 1, 1));

        final IndicatorsResponse response = new IndicatorsResponse();
        response.setType("2");
        response.setCode("code");
        response.setName("name");
        final IndicatorsVO indicatorsVO = new IndicatorsVO();
        indicatorsVO.setType("2");
        indicatorsVO.setTypeName("typeName");
        indicatorsVO.setUnit("unit");
        indicatorsVO.setValue(new BigDecimal("0.00"));
        response.setIndicatorsVOList(Arrays.asList(indicatorsVO));
        final List<IndicatorsResponse> expectedResult = Arrays.asList(response);

        // Configure AdsInsuranceIndexProgressDao.selectAreaHistoryIndicators(...).
        final HistoryIndicatorsDto dto = new HistoryIndicatorsDto();
        dto.setName("name");
        dto.setCode("code");
        dto.setLandCustomerCnt(new BigDecimal("0.00"));
        dto.setNormInsuranceAmt(new BigDecimal("0.00"));
        dto.setActualReceiptPerformance(new BigDecimal("0.00"));
        dto.setMsEmpCnt(0);
        dto.setManageCustCnt(new BigDecimal("0.00"));
        dto.setSetMonths15rule(new BigDecimal("0.00"));
        dto.setJobMonths(new BigDecimal("0.00"));
        final List<HistoryIndicatorsDto> historyIndicatorsDtos = Arrays.asList(dto);
        when(mockAdsInsuranceIndexProgressDao.selectAreaHistoryIndicators("start", "end",
                Arrays.asList("value"))).thenReturn(historyIndicatorsDtos);

        // Configure AdsInsuranceIndexProgressDao.selectOrgHistoryIndicators(...).
        final HistoryIndicatorsDto dto1 = new HistoryIndicatorsDto();
        dto1.setName("name");
        dto1.setCode("code");
        dto1.setLandCustomerCnt(new BigDecimal("0.00"));
        dto1.setNormInsuranceAmt(new BigDecimal("0.00"));
        dto1.setActualReceiptPerformance(new BigDecimal("0.00"));
        dto1.setMsEmpCnt(0);
        dto1.setManageCustCnt(new BigDecimal("0.00"));
        dto1.setSetMonths15rule(new BigDecimal("0.00"));
        dto1.setJobMonths(new BigDecimal("0.00"));
        final List<HistoryIndicatorsDto> historyIndicatorsDtos1 = Arrays.asList(dto1);
        when(mockAdsInsuranceIndexProgressDao.selectOrgHistoryIndicators("start", "end",
                Arrays.asList("value"))).thenReturn(historyIndicatorsDtos1);

        // Configure AdsInsuranceIndexProgressDao.selectEmpHistoryIndicators(...).
        final HistoryIndicatorsDto dto2 = new HistoryIndicatorsDto();
        dto2.setName("name");
        dto2.setCode("code");
        dto2.setLandCustomerCnt(new BigDecimal("0.00"));
        dto2.setNormInsuranceAmt(new BigDecimal("0.00"));
        dto2.setActualReceiptPerformance(new BigDecimal("0.00"));
        dto2.setMsEmpCnt(0);
        dto2.setManageCustCnt(new BigDecimal("0.00"));
        dto2.setSetMonths15rule(new BigDecimal("0.00"));
        dto2.setJobMonths(new BigDecimal("0.00"));
        final List<HistoryIndicatorsDto> historyIndicatorsDtos2 = Arrays.asList(dto2);
        when(mockAdsInsuranceIndexProgressDao.selectEmpHistoryIndicators("start", "end",
                Arrays.asList("value"))).thenReturn(historyIndicatorsDtos2);

        // Configure AdsInsuranceIndexProgressDao.selectMonthEmpArea(...).
        final MonthEmpCntDto monthEmpCntDto = new MonthEmpCntDto();
        monthEmpCntDto.setCode("code");
        monthEmpCntDto.setEmpCnt(0);
        final List<MonthEmpCntDto> monthEmpCntDtos = Arrays.asList(monthEmpCntDto);
        when(mockAdsInsuranceIndexProgressDao.selectMonthEmpArea("start", "end", Arrays.asList("value")))
                .thenReturn(monthEmpCntDtos);

        // Configure AdsInsuranceIndexProgressDao.selectMonthEmpOrg(...).
        final MonthEmpCntDto monthEmpCntDto1 = new MonthEmpCntDto();
        monthEmpCntDto1.setCode("code");
        monthEmpCntDto1.setEmpCnt(0);
        final List<MonthEmpCntDto> monthEmpCntDtos1 = Arrays.asList(monthEmpCntDto1);
        when(mockAdsInsuranceIndexProgressDao.selectMonthEmpOrg("start", "end", Arrays.asList("value")))
                .thenReturn(monthEmpCntDtos1);

        // Run the test
        final List<IndicatorsResponse> result = pkIndicatorsQueryServiceUnderTest.queryPkHistoryIndicators(
                indicatorsRequest);

    }

    @Test
    public void testQueryPkHistoryIndicators_AdsInsuranceIndexProgressDaoSelectAreaHistoryIndicatorsReturnsNoItems() {
        // Setup
        final IndicatorsRequest indicatorsRequest = new IndicatorsRequest();
        indicatorsRequest.setType("2");
        indicatorsRequest.setCodeList(Arrays.asList("value"));
        indicatorsRequest.setIndicatorTypeList(Arrays.asList("40"));
        indicatorsRequest.setStartDate(LocalDate.of(2020, 1, 1));
        indicatorsRequest.setEndDate(LocalDate.of(2020, 1, 1));

        when(mockAdsInsuranceIndexProgressDao.selectAreaHistoryIndicators("start", "end",
                Arrays.asList("value"))).thenReturn(Collections.emptyList());

        // Configure AdsInsuranceIndexProgressDao.selectMonthEmpArea(...).
        final MonthEmpCntDto monthEmpCntDto = new MonthEmpCntDto();
        monthEmpCntDto.setCode("code");
        monthEmpCntDto.setEmpCnt(0);
        final List<MonthEmpCntDto> monthEmpCntDtos = Arrays.asList(monthEmpCntDto);
        when(mockAdsInsuranceIndexProgressDao.selectMonthEmpArea("start", "end", Arrays.asList("value")))
                .thenReturn(monthEmpCntDtos);

        // Configure AdsInsuranceIndexProgressDao.selectMonthEmpOrg(...).
        final MonthEmpCntDto monthEmpCntDto1 = new MonthEmpCntDto();
        monthEmpCntDto1.setCode("code");
        monthEmpCntDto1.setEmpCnt(0);
        final List<MonthEmpCntDto> monthEmpCntDtos1 = Arrays.asList(monthEmpCntDto1);
        when(mockAdsInsuranceIndexProgressDao.selectMonthEmpOrg("start", "end", Arrays.asList("value")))
                .thenReturn(monthEmpCntDtos1);

        // Run the test
        final List<IndicatorsResponse> result = pkIndicatorsQueryServiceUnderTest.queryPkHistoryIndicators(
                indicatorsRequest);

    }

    @Test
    public void testQueryPkHistoryIndicators_AdsInsuranceIndexProgressDaoSelectOrgHistoryIndicatorsReturnsNoItems() {
        // Setup
        final IndicatorsRequest indicatorsRequest = new IndicatorsRequest();
        indicatorsRequest.setType("2");
        indicatorsRequest.setCodeList(Arrays.asList("value"));
        indicatorsRequest.setIndicatorTypeList(Arrays.asList("40"));
        indicatorsRequest.setStartDate(LocalDate.of(2020, 1, 1));
        indicatorsRequest.setEndDate(LocalDate.of(2020, 1, 1));

        when(mockAdsInsuranceIndexProgressDao.selectOrgHistoryIndicators("start", "end",
                Arrays.asList("value"))).thenReturn(Collections.emptyList());

        // Configure AdsInsuranceIndexProgressDao.selectMonthEmpArea(...).
        final MonthEmpCntDto monthEmpCntDto = new MonthEmpCntDto();
        monthEmpCntDto.setCode("code");
        monthEmpCntDto.setEmpCnt(0);
        final List<MonthEmpCntDto> monthEmpCntDtos = Arrays.asList(monthEmpCntDto);
        when(mockAdsInsuranceIndexProgressDao.selectMonthEmpArea("start", "end", Arrays.asList("value")))
                .thenReturn(monthEmpCntDtos);

        // Configure AdsInsuranceIndexProgressDao.selectMonthEmpOrg(...).
        final MonthEmpCntDto monthEmpCntDto1 = new MonthEmpCntDto();
        monthEmpCntDto1.setCode("code");
        monthEmpCntDto1.setEmpCnt(0);
        final List<MonthEmpCntDto> monthEmpCntDtos1 = Arrays.asList(monthEmpCntDto1);
        when(mockAdsInsuranceIndexProgressDao.selectMonthEmpOrg("start", "end", Arrays.asList("value")))
                .thenReturn(monthEmpCntDtos1);

        // Run the test
        final List<IndicatorsResponse> result = pkIndicatorsQueryServiceUnderTest.queryPkHistoryIndicators(
                indicatorsRequest);

    }

    @Test
    public void testQueryPkHistoryIndicators_AdsInsuranceIndexProgressDaoSelectEmpHistoryIndicatorsReturnsNoItems() {
        // Setup
        final IndicatorsRequest indicatorsRequest = new IndicatorsRequest();
        indicatorsRequest.setType("2");
        indicatorsRequest.setCodeList(Arrays.asList("value"));
        indicatorsRequest.setIndicatorTypeList(Arrays.asList("40"));
        indicatorsRequest.setStartDate(LocalDate.of(2020, 1, 1));
        indicatorsRequest.setEndDate(LocalDate.of(2020, 1, 1));

        when(mockAdsInsuranceIndexProgressDao.selectEmpHistoryIndicators("start", "end",
                Arrays.asList("value"))).thenReturn(Collections.emptyList());

        // Configure AdsInsuranceIndexProgressDao.selectMonthEmpArea(...).
        final MonthEmpCntDto monthEmpCntDto = new MonthEmpCntDto();
        monthEmpCntDto.setCode("code");
        monthEmpCntDto.setEmpCnt(0);
        final List<MonthEmpCntDto> monthEmpCntDtos = Arrays.asList(monthEmpCntDto);
        when(mockAdsInsuranceIndexProgressDao.selectMonthEmpArea("start", "end", Arrays.asList("value")))
                .thenReturn(monthEmpCntDtos);

        // Configure AdsInsuranceIndexProgressDao.selectMonthEmpOrg(...).
        final MonthEmpCntDto monthEmpCntDto1 = new MonthEmpCntDto();
        monthEmpCntDto1.setCode("code");
        monthEmpCntDto1.setEmpCnt(0);
        final List<MonthEmpCntDto> monthEmpCntDtos1 = Arrays.asList(monthEmpCntDto1);
        when(mockAdsInsuranceIndexProgressDao.selectMonthEmpOrg("start", "end", Arrays.asList("value")))
                .thenReturn(monthEmpCntDtos1);

        // Run the test
        final List<IndicatorsResponse> result = pkIndicatorsQueryServiceUnderTest.queryPkHistoryIndicators(
                indicatorsRequest);

    }

    @Test
    public void testQueryPkHistoryIndicators_AdsInsuranceIndexProgressDaoSelectMonthEmpAreaReturnsNoItems() {
        // Setup
        final IndicatorsRequest indicatorsRequest = new IndicatorsRequest();
        indicatorsRequest.setType("2");
        indicatorsRequest.setCodeList(Arrays.asList("value"));
        indicatorsRequest.setIndicatorTypeList(Arrays.asList("40"));
        indicatorsRequest.setStartDate(LocalDate.of(2020, 1, 1));
        indicatorsRequest.setEndDate(LocalDate.of(2020, 1, 1));

        final IndicatorsResponse response = new IndicatorsResponse();
        response.setType("2");
        response.setCode("code");
        response.setName("name");
        final IndicatorsVO indicatorsVO = new IndicatorsVO();
        indicatorsVO.setType("2");
        indicatorsVO.setTypeName("typeName");
        indicatorsVO.setUnit("unit");
        indicatorsVO.setValue(new BigDecimal("0.00"));
        response.setIndicatorsVOList(Arrays.asList(indicatorsVO));
        final List<IndicatorsResponse> expectedResult = Arrays.asList(response);

        // Configure AdsInsuranceIndexProgressDao.selectAreaHistoryIndicators(...).
        final HistoryIndicatorsDto dto = new HistoryIndicatorsDto();
        dto.setName("name");
        dto.setCode("code");
        dto.setLandCustomerCnt(new BigDecimal("0.00"));
        dto.setNormInsuranceAmt(new BigDecimal("0.00"));
        dto.setActualReceiptPerformance(new BigDecimal("0.00"));
        dto.setMsEmpCnt(0);
        dto.setManageCustCnt(new BigDecimal("0.00"));
        dto.setSetMonths15rule(new BigDecimal("0.00"));
        dto.setJobMonths(new BigDecimal("0.00"));
        final List<HistoryIndicatorsDto> historyIndicatorsDtos = Arrays.asList(dto);
        when(mockAdsInsuranceIndexProgressDao.selectAreaHistoryIndicators("start", "end",
                Arrays.asList("value"))).thenReturn(historyIndicatorsDtos);

        when(mockAdsInsuranceIndexProgressDao.selectMonthEmpArea("start", "end", Arrays.asList("value")))
                .thenReturn(Collections.emptyList());

        // Run the test
        final List<IndicatorsResponse> result = pkIndicatorsQueryServiceUnderTest.queryPkHistoryIndicators(
                indicatorsRequest);

    }

    @Test
    public void testQueryPkHistoryIndicators_AdsInsuranceIndexProgressDaoSelectMonthEmpOrgReturnsNoItems() {
        // Setup
        final IndicatorsRequest indicatorsRequest = new IndicatorsRequest();
        indicatorsRequest.setType("2");
        indicatorsRequest.setCodeList(Arrays.asList("value"));
        indicatorsRequest.setIndicatorTypeList(Arrays.asList("40"));
        indicatorsRequest.setStartDate(LocalDate.of(2020, 1, 1));
        indicatorsRequest.setEndDate(LocalDate.of(2020, 1, 1));

        final IndicatorsResponse response = new IndicatorsResponse();
        response.setType("2");
        response.setCode("code");
        response.setName("name");
        final IndicatorsVO indicatorsVO = new IndicatorsVO();
        indicatorsVO.setType("2");
        indicatorsVO.setTypeName("typeName");
        indicatorsVO.setUnit("unit");
        indicatorsVO.setValue(new BigDecimal("0.00"));
        response.setIndicatorsVOList(Arrays.asList(indicatorsVO));
        final List<IndicatorsResponse> expectedResult = Arrays.asList(response);

        // Configure AdsInsuranceIndexProgressDao.selectAreaHistoryIndicators(...).
        final HistoryIndicatorsDto dto = new HistoryIndicatorsDto();
        dto.setName("name");
        dto.setCode("code");
        dto.setLandCustomerCnt(new BigDecimal("0.00"));
        dto.setNormInsuranceAmt(new BigDecimal("0.00"));
        dto.setActualReceiptPerformance(new BigDecimal("0.00"));
        dto.setMsEmpCnt(0);
        dto.setManageCustCnt(new BigDecimal("0.00"));
        dto.setSetMonths15rule(new BigDecimal("0.00"));
        dto.setJobMonths(new BigDecimal("0.00"));
        final List<HistoryIndicatorsDto> historyIndicatorsDtos = Arrays.asList(dto);
        when(mockAdsInsuranceIndexProgressDao.selectAreaHistoryIndicators("start", "end",
                Arrays.asList("value"))).thenReturn(historyIndicatorsDtos);

        when(mockAdsInsuranceIndexProgressDao.selectMonthEmpOrg("start", "end", Arrays.asList("value")))
                .thenReturn(Collections.emptyList());

        // Run the test
        final List<IndicatorsResponse> result = pkIndicatorsQueryServiceUnderTest.queryPkHistoryIndicators(
                indicatorsRequest);

    }

    @Test
    public void testGetReceiptPerformanceHistory() {
        // Setup
        final IndicatorsRequest indicatorsRequest = new IndicatorsRequest();
        indicatorsRequest.setType("2");
        indicatorsRequest.setCodeList(Arrays.asList("value"));
        indicatorsRequest.setIndicatorTypeList(Arrays.asList("40"));
        indicatorsRequest.setStartDate(LocalDate.of(2020, 1, 1));
        indicatorsRequest.setEndDate(LocalDate.of(2020, 1, 1));

        final InsIndicators insIndicators = new InsIndicators();
        insIndicators.setType("2");
        insIndicators.setCode("code");
        insIndicators.setName("name");
        insIndicators.setIndicatorType("40");
        insIndicators.setValue(new BigDecimal("0.00"));
        final List<InsIndicators> expectedResult = Arrays.asList(insIndicators);

        // Configure AdsInsuranceIndexProgressDao.selectAreaHistoryIndicators(...).
        final HistoryIndicatorsDto dto = new HistoryIndicatorsDto();
        dto.setName("name");
        dto.setCode("code");
        dto.setLandCustomerCnt(new BigDecimal("0.00"));
        dto.setNormInsuranceAmt(new BigDecimal("0.00"));
        dto.setActualReceiptPerformance(new BigDecimal("0.00"));
        dto.setMsEmpCnt(0);
        dto.setManageCustCnt(new BigDecimal("0.00"));
        dto.setSetMonths15rule(new BigDecimal("0.00"));
        dto.setJobMonths(new BigDecimal("0.00"));
        final List<HistoryIndicatorsDto> historyIndicatorsDtos = Arrays.asList(dto);
        when(mockAdsInsuranceIndexProgressDao.selectAreaHistoryIndicators("start", "end",
                Arrays.asList("value"))).thenReturn(historyIndicatorsDtos);

        // Configure AdsInsuranceIndexProgressDao.selectOrgHistoryIndicators(...).
        final HistoryIndicatorsDto dto1 = new HistoryIndicatorsDto();
        dto1.setName("name");
        dto1.setCode("code");
        dto1.setLandCustomerCnt(new BigDecimal("0.00"));
        dto1.setNormInsuranceAmt(new BigDecimal("0.00"));
        dto1.setActualReceiptPerformance(new BigDecimal("0.00"));
        dto1.setMsEmpCnt(0);
        dto1.setManageCustCnt(new BigDecimal("0.00"));
        dto1.setSetMonths15rule(new BigDecimal("0.00"));
        dto1.setJobMonths(new BigDecimal("0.00"));
        final List<HistoryIndicatorsDto> historyIndicatorsDtos1 = Arrays.asList(dto1);
        when(mockAdsInsuranceIndexProgressDao.selectOrgHistoryIndicators("start", "end",
                Arrays.asList("value"))).thenReturn(historyIndicatorsDtos1);

        // Configure AdsInsuranceIndexProgressDao.selectEmpHistoryIndicators(...).
        final HistoryIndicatorsDto dto2 = new HistoryIndicatorsDto();
        dto2.setName("name");
        dto2.setCode("code");
        dto2.setLandCustomerCnt(new BigDecimal("0.00"));
        dto2.setNormInsuranceAmt(new BigDecimal("0.00"));
        dto2.setActualReceiptPerformance(new BigDecimal("0.00"));
        dto2.setMsEmpCnt(0);
        dto2.setManageCustCnt(new BigDecimal("0.00"));
        dto2.setSetMonths15rule(new BigDecimal("0.00"));
        dto2.setJobMonths(new BigDecimal("0.00"));
        final List<HistoryIndicatorsDto> historyIndicatorsDtos2 = Arrays.asList(dto2);
        when(mockAdsInsuranceIndexProgressDao.selectEmpHistoryIndicators("start", "end",
                Arrays.asList("value"))).thenReturn(historyIndicatorsDtos2);

        // Configure AdsInsuranceIndexProgressDao.selectMonthEmpArea(...).
        final MonthEmpCntDto monthEmpCntDto = new MonthEmpCntDto();
        monthEmpCntDto.setCode("code");
        monthEmpCntDto.setEmpCnt(0);
        final List<MonthEmpCntDto> monthEmpCntDtos = Arrays.asList(monthEmpCntDto);
        when(mockAdsInsuranceIndexProgressDao.selectMonthEmpArea("start", "end", Arrays.asList("value")))
                .thenReturn(monthEmpCntDtos);

        // Configure AdsInsuranceIndexProgressDao.selectMonthEmpOrg(...).
        final MonthEmpCntDto monthEmpCntDto1 = new MonthEmpCntDto();
        monthEmpCntDto1.setCode("code");
        monthEmpCntDto1.setEmpCnt(0);
        final List<MonthEmpCntDto> monthEmpCntDtos1 = Arrays.asList(monthEmpCntDto1);
        when(mockAdsInsuranceIndexProgressDao.selectMonthEmpOrg("start", "end", Arrays.asList("value")))
                .thenReturn(monthEmpCntDtos1);

        // Run the test
        final List<InsIndicators> result = pkIndicatorsQueryServiceUnderTest.getReceiptPerformanceHistory(
                indicatorsRequest, "40", false);

    }

    @Test
    public void testGetReceiptPerformanceHistory_AdsInsuranceIndexProgressDaoSelectAreaHistoryIndicatorsReturnsNoItems() {
        // Setup
        final IndicatorsRequest indicatorsRequest = new IndicatorsRequest();
        indicatorsRequest.setType("2");
        indicatorsRequest.setCodeList(Arrays.asList("value"));
        indicatorsRequest.setIndicatorTypeList(Arrays.asList("40"));
        indicatorsRequest.setStartDate(LocalDate.of(2020, 1, 1));
        indicatorsRequest.setEndDate(LocalDate.of(2020, 1, 1));

        when(mockAdsInsuranceIndexProgressDao.selectAreaHistoryIndicators("start", "end",
                Arrays.asList("value"))).thenReturn(Collections.emptyList());

        // Configure AdsInsuranceIndexProgressDao.selectMonthEmpArea(...).
        final MonthEmpCntDto monthEmpCntDto = new MonthEmpCntDto();
        monthEmpCntDto.setCode("code");
        monthEmpCntDto.setEmpCnt(0);
        final List<MonthEmpCntDto> monthEmpCntDtos = Arrays.asList(monthEmpCntDto);
        when(mockAdsInsuranceIndexProgressDao.selectMonthEmpArea("start", "end", Arrays.asList("value")))
                .thenReturn(monthEmpCntDtos);

        // Configure AdsInsuranceIndexProgressDao.selectMonthEmpOrg(...).
        final MonthEmpCntDto monthEmpCntDto1 = new MonthEmpCntDto();
        monthEmpCntDto1.setCode("code");
        monthEmpCntDto1.setEmpCnt(0);
        final List<MonthEmpCntDto> monthEmpCntDtos1 = Arrays.asList(monthEmpCntDto1);
        when(mockAdsInsuranceIndexProgressDao.selectMonthEmpOrg("start", "end", Arrays.asList("value")))
                .thenReturn(monthEmpCntDtos1);

        // Run the test
        final List<InsIndicators> result = pkIndicatorsQueryServiceUnderTest.getReceiptPerformanceHistory(
                indicatorsRequest, "40", false);

    }

    @Test
    public void testGetReceiptPerformanceHistory_AdsInsuranceIndexProgressDaoSelectOrgHistoryIndicatorsReturnsNoItems() {
        // Setup
        final IndicatorsRequest indicatorsRequest = new IndicatorsRequest();
        indicatorsRequest.setType("2");
        indicatorsRequest.setCodeList(Arrays.asList("value"));
        indicatorsRequest.setIndicatorTypeList(Arrays.asList("40"));
        indicatorsRequest.setStartDate(LocalDate.of(2020, 1, 1));
        indicatorsRequest.setEndDate(LocalDate.of(2020, 1, 1));

        when(mockAdsInsuranceIndexProgressDao.selectOrgHistoryIndicators("start", "end",
                Arrays.asList("value"))).thenReturn(Collections.emptyList());

        // Configure AdsInsuranceIndexProgressDao.selectMonthEmpArea(...).
        final MonthEmpCntDto monthEmpCntDto = new MonthEmpCntDto();
        monthEmpCntDto.setCode("code");
        monthEmpCntDto.setEmpCnt(0);
        final List<MonthEmpCntDto> monthEmpCntDtos = Arrays.asList(monthEmpCntDto);
        when(mockAdsInsuranceIndexProgressDao.selectMonthEmpArea("start", "end", Arrays.asList("value")))
                .thenReturn(monthEmpCntDtos);

        // Configure AdsInsuranceIndexProgressDao.selectMonthEmpOrg(...).
        final MonthEmpCntDto monthEmpCntDto1 = new MonthEmpCntDto();
        monthEmpCntDto1.setCode("code");
        monthEmpCntDto1.setEmpCnt(0);
        final List<MonthEmpCntDto> monthEmpCntDtos1 = Arrays.asList(monthEmpCntDto1);
        when(mockAdsInsuranceIndexProgressDao.selectMonthEmpOrg("start", "end", Arrays.asList("value")))
                .thenReturn(monthEmpCntDtos1);

        // Run the test
        final List<InsIndicators> result = pkIndicatorsQueryServiceUnderTest.getReceiptPerformanceHistory(
                indicatorsRequest, "40", false);

    }

    @Test
    public void testGetReceiptPerformanceHistory_AdsInsuranceIndexProgressDaoSelectEmpHistoryIndicatorsReturnsNoItems() {
        // Setup
        final IndicatorsRequest indicatorsRequest = new IndicatorsRequest();
        indicatorsRequest.setType("2");
        indicatorsRequest.setCodeList(Arrays.asList("value"));
        indicatorsRequest.setIndicatorTypeList(Arrays.asList("40"));
        indicatorsRequest.setStartDate(LocalDate.of(2020, 1, 1));
        indicatorsRequest.setEndDate(LocalDate.of(2020, 1, 1));

        when(mockAdsInsuranceIndexProgressDao.selectEmpHistoryIndicators("start", "end",
                Arrays.asList("value"))).thenReturn(Collections.emptyList());

        // Configure AdsInsuranceIndexProgressDao.selectMonthEmpArea(...).
        final MonthEmpCntDto monthEmpCntDto = new MonthEmpCntDto();
        monthEmpCntDto.setCode("code");
        monthEmpCntDto.setEmpCnt(0);
        final List<MonthEmpCntDto> monthEmpCntDtos = Arrays.asList(monthEmpCntDto);
        when(mockAdsInsuranceIndexProgressDao.selectMonthEmpArea("start", "end", Arrays.asList("value")))
                .thenReturn(monthEmpCntDtos);

        // Configure AdsInsuranceIndexProgressDao.selectMonthEmpOrg(...).
        final MonthEmpCntDto monthEmpCntDto1 = new MonthEmpCntDto();
        monthEmpCntDto1.setCode("code");
        monthEmpCntDto1.setEmpCnt(0);
        final List<MonthEmpCntDto> monthEmpCntDtos1 = Arrays.asList(monthEmpCntDto1);
        when(mockAdsInsuranceIndexProgressDao.selectMonthEmpOrg("start", "end", Arrays.asList("value")))
                .thenReturn(monthEmpCntDtos1);

        // Run the test
        final List<InsIndicators> result = pkIndicatorsQueryServiceUnderTest.getReceiptPerformanceHistory(
                indicatorsRequest, "40", false);

    }

    @Test
    public void testGetReceiptPerformanceHistory_AdsInsuranceIndexProgressDaoSelectMonthEmpAreaReturnsNoItems() {
        // Setup
        final IndicatorsRequest indicatorsRequest = new IndicatorsRequest();
        indicatorsRequest.setType("2");
        indicatorsRequest.setCodeList(Arrays.asList("value"));
        indicatorsRequest.setIndicatorTypeList(Arrays.asList("40"));
        indicatorsRequest.setStartDate(LocalDate.of(2020, 1, 1));
        indicatorsRequest.setEndDate(LocalDate.of(2020, 1, 1));

        final InsIndicators insIndicators = new InsIndicators();
        insIndicators.setType("2");
        insIndicators.setCode("code");
        insIndicators.setName("name");
        insIndicators.setIndicatorType("40");
        insIndicators.setValue(new BigDecimal("0.00"));
        final List<InsIndicators> expectedResult = Arrays.asList(insIndicators);

        // Configure AdsInsuranceIndexProgressDao.selectAreaHistoryIndicators(...).
        final HistoryIndicatorsDto dto = new HistoryIndicatorsDto();
        dto.setName("name");
        dto.setCode("code");
        dto.setLandCustomerCnt(new BigDecimal("0.00"));
        dto.setNormInsuranceAmt(new BigDecimal("0.00"));
        dto.setActualReceiptPerformance(new BigDecimal("0.00"));
        dto.setMsEmpCnt(0);
        dto.setManageCustCnt(new BigDecimal("0.00"));
        dto.setSetMonths15rule(new BigDecimal("0.00"));
        dto.setJobMonths(new BigDecimal("0.00"));
        final List<HistoryIndicatorsDto> historyIndicatorsDtos = Arrays.asList(dto);
        when(mockAdsInsuranceIndexProgressDao.selectAreaHistoryIndicators("start", "end",
                Arrays.asList("value"))).thenReturn(historyIndicatorsDtos);

        when(mockAdsInsuranceIndexProgressDao.selectMonthEmpArea("start", "end", Arrays.asList("value")))
                .thenReturn(Collections.emptyList());

        // Run the test
        final List<InsIndicators> result = pkIndicatorsQueryServiceUnderTest.getReceiptPerformanceHistory(
                indicatorsRequest, "40", false);


    }

    @Test
    public void testGetReceiptPerformanceHistory_AdsInsuranceIndexProgressDaoSelectMonthEmpOrgReturnsNoItems() {
        // Setup
        final IndicatorsRequest indicatorsRequest = new IndicatorsRequest();
        indicatorsRequest.setType("2");
        indicatorsRequest.setCodeList(Arrays.asList("value"));
        indicatorsRequest.setIndicatorTypeList(Arrays.asList("40"));
        indicatorsRequest.setStartDate(LocalDate.of(2020, 1, 1));
        indicatorsRequest.setEndDate(LocalDate.of(2020, 1, 1));

        final InsIndicators insIndicators = new InsIndicators();
        insIndicators.setType("2");
        insIndicators.setCode("code");
        insIndicators.setName("name");
        insIndicators.setIndicatorType("40");
        insIndicators.setValue(new BigDecimal("0.00"));
        final List<InsIndicators> expectedResult = Arrays.asList(insIndicators);

        // Configure AdsInsuranceIndexProgressDao.selectAreaHistoryIndicators(...).
        final HistoryIndicatorsDto dto = new HistoryIndicatorsDto();
        dto.setName("name");
        dto.setCode("code");
        dto.setLandCustomerCnt(new BigDecimal("0.00"));
        dto.setNormInsuranceAmt(new BigDecimal("0.00"));
        dto.setActualReceiptPerformance(new BigDecimal("0.00"));
        dto.setMsEmpCnt(0);
        dto.setManageCustCnt(new BigDecimal("0.00"));
        dto.setSetMonths15rule(new BigDecimal("0.00"));
        dto.setJobMonths(new BigDecimal("0.00"));
        final List<HistoryIndicatorsDto> historyIndicatorsDtos = Arrays.asList(dto);
        when(mockAdsInsuranceIndexProgressDao.selectAreaHistoryIndicators("start", "end",
                Arrays.asList("value"))).thenReturn(historyIndicatorsDtos);

        when(mockAdsInsuranceIndexProgressDao.selectMonthEmpOrg("start", "end", Arrays.asList("value")))
                .thenReturn(Collections.emptyList());

        // Run the test
        final List<InsIndicators> result = pkIndicatorsQueryServiceUnderTest.getReceiptPerformanceHistory(
                indicatorsRequest, "40", false);

    }

    @Test
    public void testGetRegCust() {
        // Setup
        final IndicatorsRequest request = new IndicatorsRequest();
        request.setType("2");
        request.setCodeList(Arrays.asList("value"));
        request.setIndicatorTypeList(Arrays.asList("40"));
        request.setStartDate(LocalDate.of(2020, 1, 1));
        request.setEndDate(LocalDate.of(2020, 1, 1));

        final InsIndicators insIndicators = new InsIndicators();
        insIndicators.setType("2");
        insIndicators.setCode("code");
        insIndicators.setName("name");
        insIndicators.setIndicatorType("40");
        insIndicators.setValue(new BigDecimal("0.00"));
        final List<InsIndicators> expectedResult = Arrays.asList(insIndicators);

        // Configure WhaleApiService.userCount(...).
        final InsIndicators insIndicators2 = new InsIndicators();
        insIndicators2.setType("2");
        insIndicators2.setCode("code");
        insIndicators2.setName("name");
        insIndicators2.setIndicatorType("40");
        insIndicators2.setValue(new BigDecimal("0.00"));
        final List<InsIndicators> insIndicators1 = Arrays.asList(insIndicators2);
        when(mockWhaleApiService.userCount(new IndicatorsRequest(), false)).thenReturn(insIndicators1);

        // Configure AdsInsuranceIndexProgressDao.selectMonthEmpArea(...).
        final MonthEmpCntDto monthEmpCntDto = new MonthEmpCntDto();
        monthEmpCntDto.setCode("code");
        monthEmpCntDto.setEmpCnt(0);
        final List<MonthEmpCntDto> monthEmpCntDtos = Arrays.asList(monthEmpCntDto);
        when(mockAdsInsuranceIndexProgressDao.selectMonthEmpArea("start", "end", Arrays.asList("value")))
                .thenReturn(monthEmpCntDtos);

        // Configure AdsInsuranceIndexProgressDao.selectMonthEmpOrg(...).
        final MonthEmpCntDto monthEmpCntDto1 = new MonthEmpCntDto();
        monthEmpCntDto1.setCode("code");
        monthEmpCntDto1.setEmpCnt(0);
        final List<MonthEmpCntDto> monthEmpCntDtos1 = Arrays.asList(monthEmpCntDto1);
        when(mockAdsInsuranceIndexProgressDao.selectMonthEmpOrg("start", "end", Arrays.asList("value")))
                .thenReturn(monthEmpCntDtos1);

        // Run the test
        final List<InsIndicators> result = pkIndicatorsQueryServiceUnderTest.getRegCust(request, "40",
                false);

    }

    @Test
    public void testGetRegCust_WhaleApiServiceReturnsNoItems() {
        // Setup
        final IndicatorsRequest request = new IndicatorsRequest();
        request.setType("2");
        request.setCodeList(Arrays.asList("value"));
        request.setIndicatorTypeList(Arrays.asList("40"));
        request.setStartDate(LocalDate.of(2020, 1, 1));
        request.setEndDate(LocalDate.of(2020, 1, 1));

        when(mockWhaleApiService.userCount(new IndicatorsRequest(), false)).thenReturn(Collections.emptyList());

        // Configure AdsInsuranceIndexProgressDao.selectMonthEmpArea(...).
        final MonthEmpCntDto monthEmpCntDto = new MonthEmpCntDto();
        monthEmpCntDto.setCode("code");
        monthEmpCntDto.setEmpCnt(0);
        final List<MonthEmpCntDto> monthEmpCntDtos = Arrays.asList(monthEmpCntDto);
        when(mockAdsInsuranceIndexProgressDao.selectMonthEmpArea("start", "end", Arrays.asList("value")))
                .thenReturn(monthEmpCntDtos);

        // Configure AdsInsuranceIndexProgressDao.selectMonthEmpOrg(...).
        final MonthEmpCntDto monthEmpCntDto1 = new MonthEmpCntDto();
        monthEmpCntDto1.setCode("code");
        monthEmpCntDto1.setEmpCnt(0);
        final List<MonthEmpCntDto> monthEmpCntDtos1 = Arrays.asList(monthEmpCntDto1);
        when(mockAdsInsuranceIndexProgressDao.selectMonthEmpOrg("start", "end", Arrays.asList("value")))
                .thenReturn(monthEmpCntDtos1);

        // Run the test
        final List<InsIndicators> result = pkIndicatorsQueryServiceUnderTest.getRegCust(request, "40",
                false);

    }
//
//    @Test
//    public void testGetRegCust_AdsInsuranceIndexProgressDaoSelectMonthEmpAreaReturnsNoItems() {
//        // Setup
//        final IndicatorsRequest request = new IndicatorsRequest();
//        request.setType("2");
//        request.setCodeList(Arrays.asList("value"));
//        request.setIndicatorTypeList(Arrays.asList("40"));
//        request.setStartDate(LocalDate.of(2020, 1, 1));
//        request.setEndDate(LocalDate.of(2020, 1, 1));
//
//        final InsIndicators insIndicators = new InsIndicators();
//        insIndicators.setType("2");
//        insIndicators.setCode("code");
//        insIndicators.setName("name");
//        insIndicators.setIndicatorType("40");
//        insIndicators.setValue(new BigDecimal("0.00"));
//        final List<InsIndicators> expectedResult = Arrays.asList(insIndicators);
//
//        // Configure WhaleApiService.userCount(...).
//        final InsIndicators insIndicators2 = new InsIndicators();
//        insIndicators2.setType("2");
//        insIndicators2.setCode("code");
//        insIndicators2.setName("name");
//        insIndicators2.setIndicatorType("40");
//        insIndicators2.setValue(new BigDecimal("0.00"));
//        final List<InsIndicators> insIndicators1 = Arrays.asList(insIndicators2);
//        when(mockWhaleApiService.userCount(new IndicatorsRequest(), false)).thenReturn(insIndicators1);
//
//        when(mockAdsInsuranceIndexProgressDao.selectMonthEmpArea("start", "end", Arrays.asList("value")))
//                .thenReturn(Collections.emptyList());
//
//        // Run the test
//        final List<InsIndicators> result = pkIndicatorsQueryServiceUnderTest.getRegCust(request, "40",
//                false);
//
//    }

//    @Test
//    public void testGetRegCust_AdsInsuranceIndexProgressDaoSelectMonthEmpOrgReturnsNoItems() {
//        // Setup
//        final IndicatorsRequest request = new IndicatorsRequest();
//        request.setType("2");
//        request.setCodeList(Arrays.asList("value"));
//        request.setIndicatorTypeList(Arrays.asList("40"));
//        request.setStartDate(LocalDate.of(2020, 1, 1));
//        request.setEndDate(LocalDate.of(2020, 1, 1));
//
//        final InsIndicators insIndicators = new InsIndicators();
//        insIndicators.setType("2");
//        insIndicators.setCode("code");
//        insIndicators.setName("name");
//        insIndicators.setIndicatorType("40");
//        insIndicators.setValue(new BigDecimal("0.00"));
//        final List<InsIndicators> expectedResult = Arrays.asList(insIndicators);
//
//        // Configure WhaleApiService.userCount(...).
//        final InsIndicators insIndicators2 = new InsIndicators();
//        insIndicators2.setType("2");
//        insIndicators2.setCode("code");
//        insIndicators2.setName("name");
//        insIndicators2.setIndicatorType("40");
//        insIndicators2.setValue(new BigDecimal("0.00"));
//        final List<InsIndicators> insIndicators1 = Arrays.asList(insIndicators2);
//        when(mockWhaleApiService.userCount(new IndicatorsRequest(), false)).thenReturn(insIndicators1);
//
//        when(mockAdsInsuranceIndexProgressDao.selectMonthEmpOrg("start", "end", Arrays.asList("value")))
//                .thenReturn(Collections.emptyList());
//
//        // Run the test
//        final List<InsIndicators> result = pkIndicatorsQueryServiceUnderTest.getRegCust(request, "40",
//                false);
//
//        // Verify the results
//        assertThat(result).isEqualTo(expectedResult);
//    }

//    @Test
//    public void testQueryPkRealTimeIndicators() {
//        // Setup
//        final IndicatorsRequest indicatorsRequest = new IndicatorsRequest();
//        indicatorsRequest.setType("2");
//        indicatorsRequest.setCodeList(Arrays.asList("value"));
//        indicatorsRequest.setIndicatorTypeList(Arrays.asList("40"));
//        indicatorsRequest.setStartDate(LocalDate.of(2020, 1, 1));
//        indicatorsRequest.setEndDate(LocalDate.of(2020, 1, 1));
//
//        final IndicatorsResponse response = new IndicatorsResponse();
//        response.setType("2");
//        response.setCode("code");
//        response.setName("name");
//        final IndicatorsVO indicatorsVO = new IndicatorsVO();
//        indicatorsVO.setType("2");
//        indicatorsVO.setTypeName("typeName");
//        indicatorsVO.setUnit("unit");
//        indicatorsVO.setValue(new BigDecimal("0.00"));
//        response.setIndicatorsVOList(Arrays.asList(indicatorsVO));
//        final List<IndicatorsResponse> expectedResult = Arrays.asList(response);
//
//        // Configure PkDao.selectEmpAmount(...).
//        final InsIndicators insIndicators1 = new InsIndicators();
//        insIndicators1.setType("2");
//        insIndicators1.setCode("code");
//        insIndicators1.setName("name");
//        insIndicators1.setIndicatorType("40");
//        insIndicators1.setValue(new BigDecimal("0.00"));
//        final List<InsIndicators> insIndicators = Arrays.asList(insIndicators1);
//        when(mockPkDao.selectEmpAmount(new IndicatorsRequest())).thenReturn(insIndicators);
//
//        // Configure PkDao.selectOrgAmount(...).
//        final InsIndicators insIndicators3 = new InsIndicators();
//        insIndicators3.setType("2");
//        insIndicators3.setCode("code");
//        insIndicators3.setName("name");
//        insIndicators3.setIndicatorType("40");
//        insIndicators3.setValue(new BigDecimal("0.00"));
//        final List<InsIndicators> insIndicators2 = Arrays.asList(insIndicators3);
//        when(mockPkDao.selectOrgAmount(new IndicatorsRequest())).thenReturn(insIndicators2);
//
//        // Configure OrganizationDao.selectByExample(...).
//        final Organization organization = new Organization();
//        organization.setBatchNo("batchNo");
//        organization.setHrOrgId("hrOrgId");
//        organization.setHrParentId("hrParentId");
//        organization.setOrgType(0);
//        organization.setOrgName("name");
//        organization.setEnabledFlag(0);
//        organization.setOrgCode("code");
//        organization.setOrgPath("orgPath");
//        final List<Organization> organizations = Arrays.asList(organization);
//        when(mockOrganizationDao.selectByExample(any(Object.class))).thenReturn(organizations);
//
//        // Configure PkDao.selectAreaAmount(...).
//        final InsIndicators insIndicators4 = new InsIndicators();
//        insIndicators4.setType("2");
//        insIndicators4.setCode("code");
//        insIndicators4.setName("name");
//        insIndicators4.setIndicatorType("40");
//        insIndicators4.setValue(new BigDecimal("0.00"));
//        when(mockPkDao.selectAreaAmount(LocalDate.of(2020, 1, 1), LocalDate.of(2020, 1, 1), "orgPath"))
//                .thenReturn(insIndicators4);
//
//        // Configure AdsInsuranceIndexProgressDao.selectMonthEmpArea(...).
//        final MonthEmpCntDto monthEmpCntDto = new MonthEmpCntDto();
//        monthEmpCntDto.setCode("code");
//        monthEmpCntDto.setEmpCnt(0);
//        final List<MonthEmpCntDto> monthEmpCntDtos = Arrays.asList(monthEmpCntDto);
//        when(mockAdsInsuranceIndexProgressDao.selectMonthEmpArea("start", "end", Arrays.asList("value")))
//                .thenReturn(monthEmpCntDtos);
//
//        // Configure AdsInsuranceIndexProgressDao.selectMonthEmpOrg(...).
//        final MonthEmpCntDto monthEmpCntDto1 = new MonthEmpCntDto();
//        monthEmpCntDto1.setCode("code");
//        monthEmpCntDto1.setEmpCnt(0);
//        final List<MonthEmpCntDto> monthEmpCntDtos1 = Arrays.asList(monthEmpCntDto1);
//        when(mockAdsInsuranceIndexProgressDao.selectMonthEmpOrg("start", "end", Arrays.asList("value")))
//                .thenReturn(monthEmpCntDtos1);
//
//        // Configure AdsInsuranceIndexProgressDao.selectAreaHistoryIndicators(...).
//        final HistoryIndicatorsDto dto = new HistoryIndicatorsDto();
//        dto.setName("name");
//        dto.setCode("code");
//        dto.setLandCustomerCnt(new BigDecimal("0.00"));
//        dto.setNormInsuranceAmt(new BigDecimal("0.00"));
//        dto.setActualReceiptPerformance(new BigDecimal("0.00"));
//        dto.setMsEmpCnt(0);
//        dto.setManageCustCnt(new BigDecimal("0.00"));
//        dto.setSetMonths15rule(new BigDecimal("0.00"));
//        dto.setJobMonths(new BigDecimal("0.00"));
//        final List<HistoryIndicatorsDto> historyIndicatorsDtos = Arrays.asList(dto);
//        when(mockAdsInsuranceIndexProgressDao.selectAreaHistoryIndicators("start", "end",
//                Arrays.asList("value"))).thenReturn(historyIndicatorsDtos);
//
//        // Configure AdsInsuranceIndexProgressDao.selectOrgHistoryIndicators(...).
//        final HistoryIndicatorsDto dto1 = new HistoryIndicatorsDto();
//        dto1.setName("name");
//        dto1.setCode("code");
//        dto1.setLandCustomerCnt(new BigDecimal("0.00"));
//        dto1.setNormInsuranceAmt(new BigDecimal("0.00"));
//        dto1.setActualReceiptPerformance(new BigDecimal("0.00"));
//        dto1.setMsEmpCnt(0);
//        dto1.setManageCustCnt(new BigDecimal("0.00"));
//        dto1.setSetMonths15rule(new BigDecimal("0.00"));
//        dto1.setJobMonths(new BigDecimal("0.00"));
//        final List<HistoryIndicatorsDto> historyIndicatorsDtos1 = Arrays.asList(dto1);
//        when(mockAdsInsuranceIndexProgressDao.selectOrgHistoryIndicators("start", "end",
//                Arrays.asList("value"))).thenReturn(historyIndicatorsDtos1);
//
//        // Configure AdsInsuranceIndexProgressDao.selectEmpHistoryIndicators(...).
//        final HistoryIndicatorsDto dto2 = new HistoryIndicatorsDto();
//        dto2.setName("name");
//        dto2.setCode("code");
//        dto2.setLandCustomerCnt(new BigDecimal("0.00"));
//        dto2.setNormInsuranceAmt(new BigDecimal("0.00"));
//        dto2.setActualReceiptPerformance(new BigDecimal("0.00"));
//        dto2.setMsEmpCnt(0);
//        dto2.setManageCustCnt(new BigDecimal("0.00"));
//        dto2.setSetMonths15rule(new BigDecimal("0.00"));
//        dto2.setJobMonths(new BigDecimal("0.00"));
//        final List<HistoryIndicatorsDto> historyIndicatorsDtos2 = Arrays.asList(dto2);
//        when(mockAdsInsuranceIndexProgressDao.selectEmpHistoryIndicators("start", "end",
//                Arrays.asList("value"))).thenReturn(historyIndicatorsDtos2);
//
//        // Configure WhaleApiService.userCount(...).
//        final InsIndicators insIndicators6 = new InsIndicators();
//        insIndicators6.setType("2");
//        insIndicators6.setCode("code");
//        insIndicators6.setName("name");
//        insIndicators6.setIndicatorType("40");
//        insIndicators6.setValue(new BigDecimal("0.00"));
//        final List<InsIndicators> insIndicators5 = Arrays.asList(insIndicators6);
//        when(mockWhaleApiService.userCount(new IndicatorsRequest(), false)).thenReturn(insIndicators5);
//
//        // Run the test
//        final List<IndicatorsResponse> result = pkIndicatorsQueryServiceUnderTest.queryPkRealTimeIndicators(
//                indicatorsRequest);
//
//    }

//    @Test
//    public void testQueryPkRealTimeIndicators_PkDaoSelectEmpAmountReturnsNoItems() {
//        // Setup
//        final IndicatorsRequest indicatorsRequest = new IndicatorsRequest();
//        indicatorsRequest.setType("2");
//        indicatorsRequest.setCodeList(Arrays.asList("value"));
//        indicatorsRequest.setIndicatorTypeList(Arrays.asList("40"));
//        indicatorsRequest.setStartDate(LocalDate.of(2020, 1, 1));
//        indicatorsRequest.setEndDate(LocalDate.of(2020, 1, 1));
//
//        final IndicatorsResponse response = new IndicatorsResponse();
//        response.setType("2");
//        response.setCode("code");
//        response.setName("name");
//        final IndicatorsVO indicatorsVO = new IndicatorsVO();
//        indicatorsVO.setType("2");
//        indicatorsVO.setTypeName("typeName");
//        indicatorsVO.setUnit("unit");
//        indicatorsVO.setValue(new BigDecimal("0.00"));
//        response.setIndicatorsVOList(Arrays.asList(indicatorsVO));
//        final List<IndicatorsResponse> expectedResult = Arrays.asList(response);
//        when(mockPkDao.selectEmpAmount(new IndicatorsRequest())).thenReturn(Collections.emptyList());
//
//        // Configure AdsInsuranceIndexProgressDao.selectMonthEmpArea(...).
//        final MonthEmpCntDto monthEmpCntDto = new MonthEmpCntDto();
//        monthEmpCntDto.setCode("code");
//        monthEmpCntDto.setEmpCnt(0);
//        final List<MonthEmpCntDto> monthEmpCntDtos = Arrays.asList(monthEmpCntDto);
//        when(mockAdsInsuranceIndexProgressDao.selectMonthEmpArea("start", "end", Arrays.asList("value")))
//                .thenReturn(monthEmpCntDtos);
//
//        // Configure AdsInsuranceIndexProgressDao.selectMonthEmpOrg(...).
//        final MonthEmpCntDto monthEmpCntDto1 = new MonthEmpCntDto();
//        monthEmpCntDto1.setCode("code");
//        monthEmpCntDto1.setEmpCnt(0);
//        final List<MonthEmpCntDto> monthEmpCntDtos1 = Arrays.asList(monthEmpCntDto1);
//        when(mockAdsInsuranceIndexProgressDao.selectMonthEmpOrg("start", "end", Arrays.asList("value")))
//                .thenReturn(monthEmpCntDtos1);
//
//        // Run the test
//        final List<IndicatorsResponse> result = pkIndicatorsQueryServiceUnderTest.queryPkRealTimeIndicators(
//                indicatorsRequest);
//
//    }

//    @Test
//    public void testQueryPkRealTimeIndicators_PkDaoSelectOrgAmountReturnsNoItems() {
//        // Setup
//        final IndicatorsRequest indicatorsRequest = new IndicatorsRequest();
//        indicatorsRequest.setType("2");
//        indicatorsRequest.setCodeList(Arrays.asList("value"));
//        indicatorsRequest.setIndicatorTypeList(Arrays.asList("40"));
//        indicatorsRequest.setStartDate(LocalDate.of(2020, 1, 1));
//        indicatorsRequest.setEndDate(LocalDate.of(2020, 1, 1));
//
//        final IndicatorsResponse response = new IndicatorsResponse();
//        response.setType("2");
//        response.setCode("code");
//        response.setName("name");
//        final IndicatorsVO indicatorsVO = new IndicatorsVO();
//        indicatorsVO.setType("2");
//        indicatorsVO.setTypeName("typeName");
//        indicatorsVO.setUnit("unit");
//        indicatorsVO.setValue(new BigDecimal("0.00"));
//        response.setIndicatorsVOList(Arrays.asList(indicatorsVO));
//        final List<IndicatorsResponse> expectedResult = Arrays.asList(response);
//        when(mockPkDao.selectOrgAmount(new IndicatorsRequest())).thenReturn(Collections.emptyList());
//
//        // Configure AdsInsuranceIndexProgressDao.selectMonthEmpArea(...).
//        final MonthEmpCntDto monthEmpCntDto = new MonthEmpCntDto();
//        monthEmpCntDto.setCode("code");
//        monthEmpCntDto.setEmpCnt(0);
//        final List<MonthEmpCntDto> monthEmpCntDtos = Arrays.asList(monthEmpCntDto);
//        when(mockAdsInsuranceIndexProgressDao.selectMonthEmpArea("start", "end", Arrays.asList("value")))
//                .thenReturn(monthEmpCntDtos);
//
//        // Configure AdsInsuranceIndexProgressDao.selectMonthEmpOrg(...).
//        final MonthEmpCntDto monthEmpCntDto1 = new MonthEmpCntDto();
//        monthEmpCntDto1.setCode("code");
//        monthEmpCntDto1.setEmpCnt(0);
//        final List<MonthEmpCntDto> monthEmpCntDtos1 = Arrays.asList(monthEmpCntDto1);
//        when(mockAdsInsuranceIndexProgressDao.selectMonthEmpOrg("start", "end", Arrays.asList("value")))
//                .thenReturn(monthEmpCntDtos1);
//
//        // Run the test
//        final List<IndicatorsResponse> result = pkIndicatorsQueryServiceUnderTest.queryPkRealTimeIndicators(
//                indicatorsRequest);
//
//    }

//    @Test
//    public void testQueryPkRealTimeIndicators_OrganizationDaoReturnsNoItems() {
//        // Setup
//        final IndicatorsRequest indicatorsRequest = new IndicatorsRequest();
//        indicatorsRequest.setType("2");
//        indicatorsRequest.setCodeList(Arrays.asList("value"));
//        indicatorsRequest.setIndicatorTypeList(Arrays.asList("40"));
//        indicatorsRequest.setStartDate(LocalDate.of(2020, 1, 1));
//        indicatorsRequest.setEndDate(LocalDate.of(2020, 1, 1));
//
//        final IndicatorsResponse response = new IndicatorsResponse();
//        response.setType("2");
//        response.setCode("code");
//        response.setName("name");
//        final IndicatorsVO indicatorsVO = new IndicatorsVO();
//        indicatorsVO.setType("2");
//        indicatorsVO.setTypeName("typeName");
//        indicatorsVO.setUnit("unit");
//        indicatorsVO.setValue(new BigDecimal("0.00"));
//        response.setIndicatorsVOList(Arrays.asList(indicatorsVO));
//        final List<IndicatorsResponse> expectedResult = Arrays.asList(response);
//        when(mockOrganizationDao.selectByExample(any(Object.class))).thenReturn(Collections.emptyList());
//
//        // Configure AdsInsuranceIndexProgressDao.selectMonthEmpArea(...).
//        final MonthEmpCntDto monthEmpCntDto = new MonthEmpCntDto();
//        monthEmpCntDto.setCode("code");
//        monthEmpCntDto.setEmpCnt(0);
//        final List<MonthEmpCntDto> monthEmpCntDtos = Arrays.asList(monthEmpCntDto);
//        when(mockAdsInsuranceIndexProgressDao.selectMonthEmpArea("start", "end", Arrays.asList("value")))
//                .thenReturn(monthEmpCntDtos);
//
//        // Configure AdsInsuranceIndexProgressDao.selectMonthEmpOrg(...).
//        final MonthEmpCntDto monthEmpCntDto1 = new MonthEmpCntDto();
//        monthEmpCntDto1.setCode("code");
//        monthEmpCntDto1.setEmpCnt(0);
//        final List<MonthEmpCntDto> monthEmpCntDtos1 = Arrays.asList(monthEmpCntDto1);
//        when(mockAdsInsuranceIndexProgressDao.selectMonthEmpOrg("start", "end", Arrays.asList("value")))
//                .thenReturn(monthEmpCntDtos1);
//
//        // Run the test
//        final List<IndicatorsResponse> result = pkIndicatorsQueryServiceUnderTest.queryPkRealTimeIndicators(
//                indicatorsRequest);
//
//    }

//    @Test
//    public void testQueryPkRealTimeIndicators_AdsInsuranceIndexProgressDaoSelectMonthEmpAreaReturnsNoItems() {
//        // Setup
//        final IndicatorsRequest indicatorsRequest = new IndicatorsRequest();
//        indicatorsRequest.setType("2");
//        indicatorsRequest.setCodeList(Arrays.asList("value"));
//        indicatorsRequest.setIndicatorTypeList(Arrays.asList("40"));
//        indicatorsRequest.setStartDate(LocalDate.of(2020, 1, 1));
//        indicatorsRequest.setEndDate(LocalDate.of(2020, 1, 1));
//
//        final IndicatorsResponse response = new IndicatorsResponse();
//        response.setType("2");
//        response.setCode("code");
//        response.setName("name");
//        final IndicatorsVO indicatorsVO = new IndicatorsVO();
//        indicatorsVO.setType("2");
//        indicatorsVO.setTypeName("typeName");
//        indicatorsVO.setUnit("unit");
//        indicatorsVO.setValue(new BigDecimal("0.00"));
//        response.setIndicatorsVOList(Arrays.asList(indicatorsVO));
//        final List<IndicatorsResponse> expectedResult = Arrays.asList(response);
//
//        // Configure PkDao.selectEmpAmount(...).
//        final InsIndicators insIndicators1 = new InsIndicators();
//        insIndicators1.setType("2");
//        insIndicators1.setCode("code");
//        insIndicators1.setName("name");
//        insIndicators1.setIndicatorType("40");
//        insIndicators1.setValue(new BigDecimal("0.00"));
//        final List<InsIndicators> insIndicators = Arrays.asList(insIndicators1);
//        when(mockPkDao.selectEmpAmount(new IndicatorsRequest())).thenReturn(insIndicators);
//
//        when(mockAdsInsuranceIndexProgressDao.selectMonthEmpArea("start", "end", Arrays.asList("value")))
//                .thenReturn(Collections.emptyList());
//
//        // Run the test
//        final List<IndicatorsResponse> result = pkIndicatorsQueryServiceUnderTest.queryPkRealTimeIndicators(
//                indicatorsRequest);
//
//    }

//    @Test
//    public void testQueryPkRealTimeIndicators_AdsInsuranceIndexProgressDaoSelectMonthEmpOrgReturnsNoItems() {
//        // Setup
//        final IndicatorsRequest indicatorsRequest = new IndicatorsRequest();
//        indicatorsRequest.setType("2");
//        indicatorsRequest.setCodeList(Arrays.asList("value"));
//        indicatorsRequest.setIndicatorTypeList(Arrays.asList("40"));
//        indicatorsRequest.setStartDate(LocalDate.of(2020, 1, 1));
//        indicatorsRequest.setEndDate(LocalDate.of(2020, 1, 1));
//
//        final IndicatorsResponse response = new IndicatorsResponse();
//        response.setType("2");
//        response.setCode("code");
//        response.setName("name");
//        final IndicatorsVO indicatorsVO = new IndicatorsVO();
//        indicatorsVO.setType("2");
//        indicatorsVO.setTypeName("typeName");
//        indicatorsVO.setUnit("unit");
//        indicatorsVO.setValue(new BigDecimal("0.00"));
//        response.setIndicatorsVOList(Arrays.asList(indicatorsVO));
//        final List<IndicatorsResponse> expectedResult = Arrays.asList(response);
//
//        // Configure PkDao.selectEmpAmount(...).
//        final InsIndicators insIndicators1 = new InsIndicators();
//        insIndicators1.setType("2");
//        insIndicators1.setCode("code");
//        insIndicators1.setName("name");
//        insIndicators1.setIndicatorType("40");
//        insIndicators1.setValue(new BigDecimal("0.00"));
//        final List<InsIndicators> insIndicators = Arrays.asList(insIndicators1);
//        when(mockPkDao.selectEmpAmount(new IndicatorsRequest())).thenReturn(insIndicators);
//
//        when(mockAdsInsuranceIndexProgressDao.selectMonthEmpOrg("start", "end", Arrays.asList("value")))
//                .thenReturn(Collections.emptyList());
//
//        // Run the test
//        final List<IndicatorsResponse> result = pkIndicatorsQueryServiceUnderTest.queryPkRealTimeIndicators(
//                indicatorsRequest);
//
//    }

    @Test
    public void testQueryPkRealTimeIndicators_AdsInsuranceIndexProgressDaoSelectAreaHistoryIndicatorsReturnsNoItems() {
        // Setup
        final IndicatorsRequest indicatorsRequest = new IndicatorsRequest();
        indicatorsRequest.setType("2");
        indicatorsRequest.setCodeList(Arrays.asList("value"));
        indicatorsRequest.setIndicatorTypeList(Arrays.asList("40"));
        indicatorsRequest.setStartDate(LocalDate.of(2020, 1, 1));
        indicatorsRequest.setEndDate(LocalDate.of(2020, 1, 1));

        final IndicatorsResponse response = new IndicatorsResponse();
        response.setType("2");
        response.setCode("code");
        response.setName("name");
        final IndicatorsVO indicatorsVO = new IndicatorsVO();
        indicatorsVO.setType("40");
        indicatorsVO.setTypeName("typeName");
        indicatorsVO.setUnit("unit");
        indicatorsVO.setValue(new BigDecimal("0.00"));
        response.setIndicatorsVOList(Arrays.asList(indicatorsVO));
        final List<IndicatorsResponse> expectedResult = Arrays.asList(response);
        when(mockAdsInsuranceIndexProgressDao.selectAreaHistoryIndicators(Mockito.any(), Mockito.any(),
                Mockito.any())).thenReturn(Collections.emptyList());

        // Configure AdsInsuranceIndexProgressDao.selectMonthEmpArea(...).
        final MonthEmpCntDto monthEmpCntDto = new MonthEmpCntDto();
        monthEmpCntDto.setCode("code");
        monthEmpCntDto.setEmpCnt(0);
        final List<MonthEmpCntDto> monthEmpCntDtos = Arrays.asList(monthEmpCntDto);
        when(mockAdsInsuranceIndexProgressDao.selectMonthEmpArea("start", "end", Arrays.asList("value")))
                .thenReturn(monthEmpCntDtos);

        // Configure AdsInsuranceIndexProgressDao.selectMonthEmpOrg(...).
        final MonthEmpCntDto monthEmpCntDto1 = new MonthEmpCntDto();
        monthEmpCntDto1.setCode("code");
        monthEmpCntDto1.setEmpCnt(0);
        final List<MonthEmpCntDto> monthEmpCntDtos1 = Arrays.asList(monthEmpCntDto1);
        when(mockAdsInsuranceIndexProgressDao.selectMonthEmpOrg(Mockito.any(), Mockito.any(), Mockito.any()))
                .thenReturn(monthEmpCntDtos1);


    }
//
//    @Test
//    public void testQueryPkRealTimeIndicators_AdsInsuranceIndexProgressDaoSelectOrgHistoryIndicatorsReturnsNoItems() {
//        // Setup
//        final IndicatorsRequest indicatorsRequest = new IndicatorsRequest();
//        indicatorsRequest.setType("2");
//        indicatorsRequest.setCodeList(Arrays.asList("value"));
//        indicatorsRequest.setIndicatorTypeList(Arrays.asList("40"));
//        indicatorsRequest.setStartDate(LocalDate.of(2020, 1, 1));
//        indicatorsRequest.setEndDate(LocalDate.of(2020, 1, 1));
//
//        final IndicatorsResponse response = new IndicatorsResponse();
//        response.setType("2");
//        response.setCode("code");
//        response.setName("name");
//        final IndicatorsVO indicatorsVO = new IndicatorsVO();
//        indicatorsVO.setType("2");
//        indicatorsVO.setTypeName("typeName");
//        indicatorsVO.setUnit("unit");
//        indicatorsVO.setValue(new BigDecimal("0.00"));
//        response.setIndicatorsVOList(Arrays.asList(indicatorsVO));
//        final List<IndicatorsResponse> expectedResult = Arrays.asList(response);
//        when(mockAdsInsuranceIndexProgressDao.selectOrgHistoryIndicators("start", "end",
//                Arrays.asList("value"))).thenReturn(Collections.emptyList());
//
//        // Configure AdsInsuranceIndexProgressDao.selectMonthEmpArea(...).
//        final MonthEmpCntDto monthEmpCntDto = new MonthEmpCntDto();
//        monthEmpCntDto.setCode("code");
//        monthEmpCntDto.setEmpCnt(0);
//        final List<MonthEmpCntDto> monthEmpCntDtos = Arrays.asList(monthEmpCntDto);
//        when(mockAdsInsuranceIndexProgressDao.selectMonthEmpArea("start", "end", Arrays.asList("value")))
//                .thenReturn(monthEmpCntDtos);
//
//        // Configure AdsInsuranceIndexProgressDao.selectMonthEmpOrg(...).
//        final MonthEmpCntDto monthEmpCntDto1 = new MonthEmpCntDto();
//        monthEmpCntDto1.setCode("code");
//        monthEmpCntDto1.setEmpCnt(0);
//        final List<MonthEmpCntDto> monthEmpCntDtos1 = Arrays.asList(monthEmpCntDto1);
//        when(mockAdsInsuranceIndexProgressDao.selectMonthEmpOrg("start", "end", Arrays.asList("value")))
//                .thenReturn(monthEmpCntDtos1);
//
//        // Run the test
//        final List<IndicatorsResponse> result = pkIndicatorsQueryServiceUnderTest.queryPkRealTimeIndicators(
//                indicatorsRequest);
//
//    }

//    @Test
//    public void testQueryPkRealTimeIndicators_AdsInsuranceIndexProgressDaoSelectEmpHistoryIndicatorsReturnsNoItems() {
//        // Setup
//        final IndicatorsRequest indicatorsRequest = new IndicatorsRequest();
//        indicatorsRequest.setType("2");
//        indicatorsRequest.setCodeList(Arrays.asList("value"));
//        indicatorsRequest.setIndicatorTypeList(Arrays.asList("40"));
//        indicatorsRequest.setStartDate(LocalDate.of(2020, 1, 1));
//        indicatorsRequest.setEndDate(LocalDate.of(2020, 1, 1));
//
//        final IndicatorsResponse response = new IndicatorsResponse();
//        response.setType("2");
//        response.setCode("code");
//        response.setName("name");
//        final IndicatorsVO indicatorsVO = new IndicatorsVO();
//        indicatorsVO.setType("2");
//        indicatorsVO.setTypeName("typeName");
//        indicatorsVO.setUnit("unit");
//        indicatorsVO.setValue(new BigDecimal("0.00"));
//        response.setIndicatorsVOList(Arrays.asList(indicatorsVO));
//        final List<IndicatorsResponse> expectedResult = Arrays.asList(response);
//        when(mockAdsInsuranceIndexProgressDao.selectEmpHistoryIndicators("start", "end",
//                Arrays.asList("value"))).thenReturn(Collections.emptyList());
//
//        // Configure AdsInsuranceIndexProgressDao.selectMonthEmpArea(...).
//        final MonthEmpCntDto monthEmpCntDto = new MonthEmpCntDto();
//        monthEmpCntDto.setCode("code");
//        monthEmpCntDto.setEmpCnt(0);
//        final List<MonthEmpCntDto> monthEmpCntDtos = Arrays.asList(monthEmpCntDto);
//        when(mockAdsInsuranceIndexProgressDao.selectMonthEmpArea("start", "end", Arrays.asList("value")))
//                .thenReturn(monthEmpCntDtos);
//
//        // Configure AdsInsuranceIndexProgressDao.selectMonthEmpOrg(...).
//        final MonthEmpCntDto monthEmpCntDto1 = new MonthEmpCntDto();
//        monthEmpCntDto1.setCode("code");
//        monthEmpCntDto1.setEmpCnt(0);
//        final List<MonthEmpCntDto> monthEmpCntDtos1 = Arrays.asList(monthEmpCntDto1);
//        when(mockAdsInsuranceIndexProgressDao.selectMonthEmpOrg("start", "end", Arrays.asList("value")))
//                .thenReturn(monthEmpCntDtos1);
//
//        // Run the test
//        final List<IndicatorsResponse> result = pkIndicatorsQueryServiceUnderTest.queryPkRealTimeIndicators(
//                indicatorsRequest);
//
//    }

//    @Test
//    public void testQueryPkRealTimeIndicators_WhaleApiServiceReturnsNoItems() {
//        // Setup
//        final IndicatorsRequest indicatorsRequest = new IndicatorsRequest();
//        indicatorsRequest.setType("2");
//        indicatorsRequest.setCodeList(Arrays.asList("value"));
//        indicatorsRequest.setIndicatorTypeList(Arrays.asList("40"));
//        indicatorsRequest.setStartDate(LocalDate.of(2020, 1, 1));
//        indicatorsRequest.setEndDate(LocalDate.of(2020, 1, 1));
//
//        final IndicatorsResponse response = new IndicatorsResponse();
//        response.setType("2");
//        response.setCode("code");
//        response.setName("name");
//        final IndicatorsVO indicatorsVO = new IndicatorsVO();
//        indicatorsVO.setType("2");
//        indicatorsVO.setTypeName("typeName");
//        indicatorsVO.setUnit("unit");
//        indicatorsVO.setValue(new BigDecimal("0.00"));
//        response.setIndicatorsVOList(Arrays.asList(indicatorsVO));
//        final List<IndicatorsResponse> expectedResult = Arrays.asList(response);
//        when(mockWhaleApiService.userCount(new IndicatorsRequest(), false)).thenReturn(Collections.emptyList());
//
//        // Configure AdsInsuranceIndexProgressDao.selectMonthEmpArea(...).
//        final MonthEmpCntDto monthEmpCntDto = new MonthEmpCntDto();
//        monthEmpCntDto.setCode("code");
//        monthEmpCntDto.setEmpCnt(0);
//        final List<MonthEmpCntDto> monthEmpCntDtos = Arrays.asList(monthEmpCntDto);
//        when(mockAdsInsuranceIndexProgressDao.selectMonthEmpArea("start", "end", Arrays.asList("value")))
//                .thenReturn(monthEmpCntDtos);
//
//        // Configure AdsInsuranceIndexProgressDao.selectMonthEmpOrg(...).
//        final MonthEmpCntDto monthEmpCntDto1 = new MonthEmpCntDto();
//        monthEmpCntDto1.setCode("code");
//        monthEmpCntDto1.setEmpCnt(0);
//        final List<MonthEmpCntDto> monthEmpCntDtos1 = Arrays.asList(monthEmpCntDto1);
//        when(mockAdsInsuranceIndexProgressDao.selectMonthEmpOrg("start", "end", Arrays.asList("value")))
//                .thenReturn(monthEmpCntDtos1);
//
//        // Run the test
//        final List<IndicatorsResponse> result = pkIndicatorsQueryServiceUnderTest.queryPkRealTimeIndicators(
//                indicatorsRequest);
//
//    }

    @Test
    public void testB() {

        List<byte[]> as = new ArrayList<>();

        byte[][] array = as.stream().toArray(byte[][]::new);
    }
}
