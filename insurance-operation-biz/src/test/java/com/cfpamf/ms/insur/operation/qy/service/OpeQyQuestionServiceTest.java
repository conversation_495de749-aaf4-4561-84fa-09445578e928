package com.cfpamf.ms.insur.operation.qy.service;

import com.cfpamf.ms.insur.operation.base.event.EventBusEngine;
import com.cfpamf.ms.insur.operation.qy.convter.OpeQyQuestionCvt;
import com.cfpamf.ms.insur.operation.qy.dao.OpeQyQuestionAnswerMapper;
import com.cfpamf.ms.insur.operation.qy.dao.OpeQyQuestionMapper;
import com.cfpamf.ms.insur.operation.qy.dao.OpeQyQuestionOptionMapper;
import com.cfpamf.ms.insur.operation.qy.entity.OpeQyQuestion;
import com.cfpamf.ms.insur.operation.qy.entity.OpeQyQuestionOption;
import com.cfpamf.ms.insur.operation.qy.form.OpeQyQuestionAnswerForm;
import com.cfpamf.ms.insur.operation.qy.form.OpeQyQuestionForm;
import com.cfpamf.ms.insur.operation.qy.form.OpeQyQuestionOptionForm;
import com.cfpamf.ms.insur.operation.qy.form.OpeQyQuestionPagerForm;
import com.github.jsonzou.jmockdata.JMockData;
import com.github.jsonzou.jmockdata.TypeReference;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.aop.framework.AopContext;

import java.util.List;

/**
 * <AUTHOR> 2022/8/12 10:49
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({AopContext.class})
@PowerMockIgnore({"javax.management.*", "javax.swing.*"})
public class OpeQyQuestionServiceTest {

    @InjectMocks
    OpeQyQuestionService service;

    @Mock
    OpeQyQuestionMapper questionMapper;
    @Mock
    OpeQyQuestionOptionMapper questionOptionMapper;
    @Mock
    OpeQyQuestionAnswerMapper answerMapper;
    @Mock
    EventBusEngine busEngine;

    @Before
    public void setUp() throws Exception {
        PowerMockito.mockStatic(AopContext.class);
        PowerMockito.when(AopContext.currentProxy()).thenReturn(service);

        List<OpeQyQuestion> questions = OpeQyQuestionCvt.INS.questionFrom2Po(JMockData.mock(new TypeReference<List<OpeQyQuestionForm>>() {
        }));

        //questionOptionMapper
        List<OpeQyQuestionOption> options = OpeQyQuestionCvt.INS.optionFrom2Po(JMockData.mock(new TypeReference<List<OpeQyQuestionOptionForm>>() {
        }));
        Mockito.when(questionMapper.selectByPagerId(Mockito.any()))
                .thenReturn(questions);
        Mockito.when(questionOptionMapper.selectByPagerId(Mockito.any()))
                .thenReturn(options);
    }

    @Test
    public void insertPager() {
        service.insertPager(JMockData.mock(OpeQyQuestionPagerForm.class));
    }

    @Test
    public void pagerDetail() {
        service.pagerDetail(1L);
    }

    @Test
    public void saveAnswer() {
        service.saveAnswer(JMockData.mock(OpeQyQuestionAnswerForm.class));
    }


    @Test
    public void queryAnswer() {
        service.queryAnswer(1L, "23");
    }
}
