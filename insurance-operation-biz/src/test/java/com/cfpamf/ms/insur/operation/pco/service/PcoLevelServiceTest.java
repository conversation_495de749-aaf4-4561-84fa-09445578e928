package com.cfpamf.ms.insur.operation.pco.service;

import com.cfpamf.ms.insur.operation.base.form.DataAuthPageForm;
import com.cfpamf.ms.insur.operation.base.helper.WxCheckAuthorityHelper;
import com.cfpamf.ms.insur.operation.base.service.DataAuthService;
import com.cfpamf.ms.insur.operation.base.vo.WxSessionVO;
import com.cfpamf.ms.insur.operation.pco.dao.PcoExtendInfoMapper;
import com.cfpamf.ms.insur.operation.pco.dao.PcoLevelInfoMapper;
import com.cfpamf.ms.insur.operation.pco.dao.PcosScheduleMapper;
import com.cfpamf.ms.insur.operation.pco.dto.PcoLevelAdjustDTO;
import com.cfpamf.ms.insur.operation.pco.dto.PcoLevelAdjustExcelDTO;
import com.cfpamf.ms.insur.operation.pco.dto.PcoNewestLevelExcelDTO;
import com.cfpamf.ms.insur.operation.pco.entity.PcoExtendInfo;
import com.cfpamf.ms.insur.operation.pco.query.*;
import com.cfpamf.ms.insur.operation.pco.validation.PcoLevelValidation;
import com.cfpamf.ms.insur.operation.pco.validation.ValidationResult;
import com.cfpamf.ms.insur.operation.pco.vo.*;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.pagehelper.PageInfo;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class PcoLevelServiceTest {

    @Mock
    private ObjectMapper mockObjectMapper;
    @Mock
    private PcoLevelValidation mockPcoLevelValidation;
    @Mock
    private PcoLevelImportService mockPcoLevelImportService;
    @Mock
    private PcoLevelInfoMapper mockPcoLevelInfoMapper;
    @Mock
    private PcosScheduleMapper mockPcosScheduleMapper;
    @Mock
    private WxCheckAuthorityHelper mockWxCheckAuthorityHelper;
    @Mock
    private PcoWeeksScoreService mockPcoWeeksScoreService;
    @Mock
    private DataAuthService mockDataAuthService;
    @Mock
    private PcoExtendInfoMapper mockPcoExtendInfoMapper;

    private PcoLevelService pcoLevelServiceUnderTest;

    @Before
    public void setUp() throws Exception {
        pcoLevelServiceUnderTest = new PcoLevelService(mockObjectMapper, mockPcoLevelValidation,
                mockPcoLevelImportService, mockPcoLevelInfoMapper, mockPcosScheduleMapper, mockWxCheckAuthorityHelper,
                mockPcoWeeksScoreService, mockDataAuthService, mockPcoExtendInfoMapper);
    }

    @Test
    public void testGetPcoList() {
        // Setup
        final PcoLevelList expectedResult = new PcoLevelList();
        final DingUserInfo dingUserInfo = new DingUserInfo();
        dingUserInfo.setRegionName("regionName");
        dingUserInfo.setOrgName("orgName");
        dingUserInfo.setUserName("userName");
        dingUserInfo.setUserId("userId");
        dingUserInfo.setJobCode("jobCode");
        expectedResult.setDingUserInfo(dingUserInfo);
        final PcoLevelStats pcoLevelStats = new PcoLevelStats();
        expectedResult.setPcoLevelStats(pcoLevelStats);
        final PcoScheduleInfo pcoScheduleInfo = new PcoScheduleInfo();
        expectedResult.setPcoScheduleInfos(Arrays.asList(pcoScheduleInfo));

        // Configure WxCheckAuthorityHelper.checkAuthority(...).
        final WxSessionVO sessionVO = new WxSessionVO();
        sessionVO.setUserId("userId");
        sessionVO.setJobCode("jobCode");
        sessionVO.setSystem("system");
        sessionVO.setChannel("channel");
        sessionVO.setAuthorization("authorization");
        when(mockWxCheckAuthorityHelper.checkAuthority("openId", "authorization")).thenReturn(sessionVO);

        // Configure PcoLevelInfoMapper.getDingUserByJobCode(...).
        final DingUserInfo dingUserInfo1 = new DingUserInfo();
        dingUserInfo1.setRegionName("regionName");
        dingUserInfo1.setOrgName("orgName");
        dingUserInfo1.setUserName("userName");
        dingUserInfo1.setUserId("userId");
        dingUserInfo1.setJobCode("jobCode");
        when(mockPcoLevelInfoMapper.getDingUserByJobCode("jobCode")).thenReturn(dingUserInfo1);

        // Run the test
        final PcoLevelList result = pcoLevelServiceUnderTest.getPcoList("openId", "authorization");

    }

    @Test
    public void testGetPcoList_PcosScheduleMapperReturnsNoItems() {
        // Setup
        final PcoLevelList expectedResult = new PcoLevelList();
        final DingUserInfo dingUserInfo = new DingUserInfo();
        dingUserInfo.setRegionName("regionName");
        dingUserInfo.setOrgName("orgName");
        dingUserInfo.setUserName("userName");
        dingUserInfo.setUserId("userId");
        dingUserInfo.setJobCode("jobCode");
        expectedResult.setDingUserInfo(dingUserInfo);
        final PcoLevelStats pcoLevelStats = new PcoLevelStats();
        expectedResult.setPcoLevelStats(pcoLevelStats);
        final PcoScheduleInfo pcoScheduleInfo = new PcoScheduleInfo();
        expectedResult.setPcoScheduleInfos(Arrays.asList(pcoScheduleInfo));

        // Configure WxCheckAuthorityHelper.checkAuthority(...).
        final WxSessionVO sessionVO = new WxSessionVO();
        sessionVO.setUserId("userId");
        sessionVO.setJobCode("jobCode");
        sessionVO.setSystem("system");
        sessionVO.setChannel("channel");
        sessionVO.setAuthorization("authorization");
        when(mockWxCheckAuthorityHelper.checkAuthority("openId", "authorization")).thenReturn(sessionVO);

        // Configure PcoLevelInfoMapper.getDingUserByJobCode(...).
        final DingUserInfo dingUserInfo1 = new DingUserInfo();
        dingUserInfo1.setRegionName("regionName");
        dingUserInfo1.setOrgName("orgName");
        dingUserInfo1.setUserName("userName");
        dingUserInfo1.setUserId("userId");
        dingUserInfo1.setJobCode("jobCode");
        when(mockPcoLevelInfoMapper.getDingUserByJobCode("jobCode")).thenReturn(dingUserInfo1);

        // Run the test
        final PcoLevelList result = pcoLevelServiceUnderTest.getPcoList("openId", "authorization");
    }

    @Test
    public void testGetNewestLevelByPage() {
        // Setup
        final PcoLevelQuery query = new PcoLevelQuery();
        query.setRegionName("regionName");
        query.setPage(0);
        query.setSize(0);
        query.setUser("user");
        query.setJobCode("jobCode");

        // Run the test
        final PageInfo<PcoNewestLevelList> result = pcoLevelServiceUnderTest.getNewestLevelByPage(query);
    }

    @Test
    public void testGetNewestLevelByPage_PcoLevelInfoMapperReturnsNoItems() {
        // Setup
        final PcoLevelQuery query = new PcoLevelQuery();
        query.setRegionName("regionName");
        query.setPage(0);
        query.setSize(0);
        query.setUser("user");
        query.setJobCode("jobCode");

        // Run the test
        final PageInfo<PcoNewestLevelList> result = pcoLevelServiceUnderTest.getNewestLevelByPage(query);
    }

    @Test
    public void testChangeListByUserId() {
        // Setup
        final PcoLevelChangeQuery query = new PcoLevelChangeQuery();
        query.setPage(0);
        query.setSize(0);
        query.setJobCode("jobCode");

        // Run the test
        final PageInfo<PcoLevelChangeRecord> result = pcoLevelServiceUnderTest.changeListByUserId(query);
    }

    @Test
    public void testChangeListByUserId_PcoLevelInfoMapperReturnsNoItems() {
        // Setup
        final PcoLevelChangeQuery query = new PcoLevelChangeQuery();
        query.setPage(0);
        query.setSize(0);
        query.setJobCode("jobCode");
    }

    @Test
    public void testGetLevelDetail() {
        // Setup
        final PcoLevelDetail expectedResult = new PcoLevelDetail();
        final DingUserInfo dingUserInfo = new DingUserInfo();
        dingUserInfo.setRegionName("regionName");
        dingUserInfo.setOrgName("orgName");
        dingUserInfo.setUserName("userName");
        dingUserInfo.setUserId("userId");
        dingUserInfo.setJobCode("jobCode");
        expectedResult.setDingUserInfo(dingUserInfo);
        final PcoLevelChangeRecord newestLevelInfo = new PcoLevelChangeRecord();
        expectedResult.setNewestLevelInfo(newestLevelInfo);
        final PcoWeeksScoreVo pcoWeeksScoreVo = new PcoWeeksScoreVo();
        expectedResult.setPcoWeeksScoreVos(Arrays.asList(pcoWeeksScoreVo));

        // Configure PcoLevelInfoMapper.getDingUserByJobCode(...).
        final DingUserInfo dingUserInfo1 = new DingUserInfo();
        dingUserInfo1.setRegionName("regionName");
        dingUserInfo1.setOrgName("orgName");
        dingUserInfo1.setUserName("userName");
        dingUserInfo1.setUserId("userId");
        dingUserInfo1.setJobCode("jobCode");
        when(mockPcoLevelInfoMapper.getDingUserByJobCode("jobCode")).thenReturn(dingUserInfo1);

        // Configure PcoLevelInfoMapper.listByJobCode(...).
        final PcoLevelChangeRecord pcoLevelChangeRecord = new PcoLevelChangeRecord();
        pcoLevelChangeRecord.setPcoLevel("A");
        pcoLevelChangeRecord.setOnlineScore(0);
        pcoLevelChangeRecord.setOfflineScore(0);
        pcoLevelChangeRecord.setPerformance(new BigDecimal("0.00"));
        pcoLevelChangeRecord.setBeQualified(0);
        final List<PcoLevelChangeRecord> pcoLevelChangeRecords = Arrays.asList(pcoLevelChangeRecord);
        when(mockPcoLevelInfoMapper.listByJobCode("jobCode", 1)).thenReturn(pcoLevelChangeRecords);

        // Run the test
        final PcoLevelDetail result = pcoLevelServiceUnderTest.getLevelDetail("jobCode");
    }

    @Test
    public void testGetLevelDetail_PcoLevelInfoMapperListByJobCodeReturnsNoItems() {
        // Setup
        final PcoLevelDetail expectedResult = new PcoLevelDetail();
        final DingUserInfo dingUserInfo = new DingUserInfo();
        dingUserInfo.setRegionName("regionName");
        dingUserInfo.setOrgName("orgName");
        dingUserInfo.setUserName("userName");
        dingUserInfo.setUserId("userId");
        dingUserInfo.setJobCode("jobCode");
        expectedResult.setDingUserInfo(dingUserInfo);
        final PcoLevelChangeRecord newestLevelInfo = new PcoLevelChangeRecord();
        expectedResult.setNewestLevelInfo(newestLevelInfo);
        final PcoWeeksScoreVo pcoWeeksScoreVo = new PcoWeeksScoreVo();
        expectedResult.setPcoWeeksScoreVos(Arrays.asList(pcoWeeksScoreVo));

        // Configure PcoLevelInfoMapper.getDingUserByJobCode(...).
        final DingUserInfo dingUserInfo1 = new DingUserInfo();
        dingUserInfo1.setRegionName("regionName");
        dingUserInfo1.setOrgName("orgName");
        dingUserInfo1.setUserName("userName");
        dingUserInfo1.setUserId("userId");
        dingUserInfo1.setJobCode("jobCode");
        when(mockPcoLevelInfoMapper.getDingUserByJobCode("jobCode")).thenReturn(dingUserInfo1);

        when(mockPcoLevelInfoMapper.listByJobCode("jobCode", 1)).thenReturn(Collections.emptyList());

        // Run the test
        final PcoLevelDetail result = pcoLevelServiceUnderTest.getLevelDetail("jobCode");
    }

    @Test
    public void testLevelAdjust() throws Exception {
        // Setup
        final PcoLevelAdjustDTO dto = new PcoLevelAdjustDTO();
        dto.setLocalFile(false);
        dto.setFileUrl("fileUrl");
        dto.setFileName("fileName");

        final PcoLevelAdjustResult expectedResult = new PcoLevelAdjustResult();
        expectedResult.setTotalNumber(0);
        expectedResult.setSuccessNumber(0);
        expectedResult.setErrorNumber(0);
        expectedResult.setErrUrl("errorUrl");


        // Configure PcoLevelValidation.valid(...).
        final PcoLevelAdjustExcelDTO pcoLevelAdjustExcelDTO2 = new PcoLevelAdjustExcelDTO();
        pcoLevelAdjustExcelDTO2.setUserName("userName");
        pcoLevelAdjustExcelDTO2.setUserId("userId");
        pcoLevelAdjustExcelDTO2.setRegionName("regionName");
        pcoLevelAdjustExcelDTO2.setOrgName("orgName");
        pcoLevelAdjustExcelDTO2.setPcoLevel("A");
        pcoLevelAdjustExcelDTO2.setBeQualified("beQualified");
        pcoLevelAdjustExcelDTO2.setClaimTimelinessRatio(new BigDecimal("0.00"));
        pcoLevelAdjustExcelDTO2.setErrorMsg("message");
        final List<ValidationResult<PcoLevelAdjustExcelDTO>> validationResults = Arrays.asList(
                new ValidationResult<>("message", pcoLevelAdjustExcelDTO2));
    }

    @Test
    public void testLevelAdjust_PcoLevelInfoMapperQueryPcoInfoByUserIdReturnsNoItems() throws Exception {
        // Setup
        final PcoLevelAdjustDTO dto = new PcoLevelAdjustDTO();
        dto.setLocalFile(false);
        dto.setFileUrl("fileUrl");
        dto.setFileName("fileName");

        final PcoLevelAdjustResult expectedResult = new PcoLevelAdjustResult();
        expectedResult.setTotalNumber(0);
        expectedResult.setSuccessNumber(0);
        expectedResult.setErrorNumber(0);
        expectedResult.setErrUrl("errorUrl");

        // Configure PcoLevelValidation.valid(...).
        final PcoLevelAdjustExcelDTO pcoLevelAdjustExcelDTO2 = new PcoLevelAdjustExcelDTO();
        pcoLevelAdjustExcelDTO2.setUserName("userName");
        pcoLevelAdjustExcelDTO2.setUserId("userId");
        pcoLevelAdjustExcelDTO2.setRegionName("regionName");
        pcoLevelAdjustExcelDTO2.setOrgName("orgName");
        pcoLevelAdjustExcelDTO2.setPcoLevel("A");
        pcoLevelAdjustExcelDTO2.setBeQualified("beQualified");
        pcoLevelAdjustExcelDTO2.setClaimTimelinessRatio(new BigDecimal("0.00"));
        pcoLevelAdjustExcelDTO2.setErrorMsg("message");
        final List<ValidationResult<PcoLevelAdjustExcelDTO>> validationResults = Arrays.asList(
                new ValidationResult<>("message", pcoLevelAdjustExcelDTO2));
    }

    @Test
    public void testLevelAdjust_ObjectMapperReturnsNoItems() throws Exception {
        // Setup
        final PcoLevelAdjustDTO dto = new PcoLevelAdjustDTO();
        dto.setLocalFile(false);
        dto.setFileUrl("fileUrl");
        dto.setFileName("fileName");

        final PcoLevelAdjustResult expectedResult = new PcoLevelAdjustResult();
        expectedResult.setTotalNumber(0);
        expectedResult.setSuccessNumber(0);
        expectedResult.setErrorNumber(0);
        expectedResult.setErrUrl("errorUrl");

        // Configure PcoLevelValidation.valid(...).
        final PcoLevelAdjustExcelDTO pcoLevelAdjustExcelDTO1 = new PcoLevelAdjustExcelDTO();
        pcoLevelAdjustExcelDTO1.setUserName("userName");
        pcoLevelAdjustExcelDTO1.setUserId("userId");
        pcoLevelAdjustExcelDTO1.setRegionName("regionName");
        pcoLevelAdjustExcelDTO1.setOrgName("orgName");
        pcoLevelAdjustExcelDTO1.setPcoLevel("A");
        pcoLevelAdjustExcelDTO1.setBeQualified("beQualified");
        pcoLevelAdjustExcelDTO1.setClaimTimelinessRatio(new BigDecimal("0.00"));
        pcoLevelAdjustExcelDTO1.setErrorMsg("message");
        final List<ValidationResult<PcoLevelAdjustExcelDTO>> validationResults = Arrays.asList(
                new ValidationResult<>("message", pcoLevelAdjustExcelDTO1));
    }

    @Test
    public void testLevelAdjust_PcoLevelValidationReturnsNoItems() throws Exception {
        // Setup
        final PcoLevelAdjustDTO dto = new PcoLevelAdjustDTO();
        dto.setLocalFile(false);
        dto.setFileUrl("fileUrl");
        dto.setFileName("fileName");

        final PcoLevelAdjustResult expectedResult = new PcoLevelAdjustResult();
        expectedResult.setTotalNumber(0);
        expectedResult.setSuccessNumber(0);
        expectedResult.setErrorNumber(0);
        expectedResult.setErrUrl("errorUrl");
    }

    @Test
    public void testLevelAdjust_PcoLevelImportServiceAddImportRecordThrowsJsonProcessingException() throws Exception {
        // Setup
        final PcoLevelAdjustDTO dto = new PcoLevelAdjustDTO();
        dto.setLocalFile(false);
        dto.setFileUrl("fileUrl");
        dto.setFileName("fileName");

        // Configure PcoLevelValidation.valid(...).
        final PcoLevelAdjustExcelDTO pcoLevelAdjustExcelDTO2 = new PcoLevelAdjustExcelDTO();
        pcoLevelAdjustExcelDTO2.setUserName("userName");
        pcoLevelAdjustExcelDTO2.setUserId("userId");
        pcoLevelAdjustExcelDTO2.setRegionName("regionName");
        pcoLevelAdjustExcelDTO2.setOrgName("orgName");
        pcoLevelAdjustExcelDTO2.setPcoLevel("A");
        pcoLevelAdjustExcelDTO2.setBeQualified("beQualified");
        pcoLevelAdjustExcelDTO2.setClaimTimelinessRatio(new BigDecimal("0.00"));
        pcoLevelAdjustExcelDTO2.setErrorMsg("message");
        final List<ValidationResult<PcoLevelAdjustExcelDTO>> validationResults = Arrays.asList(
                new ValidationResult<>("message", pcoLevelAdjustExcelDTO2));
    }

    @Test
    public void testLevelAdjust_PcoLevelImportServiceAddImportErrorRecordThrowsIOException() throws Exception {
        // Setup
        final PcoLevelAdjustDTO dto = new PcoLevelAdjustDTO();
        dto.setLocalFile(false);
        dto.setFileUrl("fileUrl");
        dto.setFileName("fileName");

        // Configure PcoLevelValidation.valid(...).
        final PcoLevelAdjustExcelDTO pcoLevelAdjustExcelDTO2 = new PcoLevelAdjustExcelDTO();
        pcoLevelAdjustExcelDTO2.setUserName("userName");
        pcoLevelAdjustExcelDTO2.setUserId("userId");
        pcoLevelAdjustExcelDTO2.setRegionName("regionName");
        pcoLevelAdjustExcelDTO2.setOrgName("orgName");
        pcoLevelAdjustExcelDTO2.setPcoLevel("A");
        pcoLevelAdjustExcelDTO2.setBeQualified("beQualified");
        pcoLevelAdjustExcelDTO2.setClaimTimelinessRatio(new BigDecimal("0.00"));
        pcoLevelAdjustExcelDTO2.setErrorMsg("message");
        final List<ValidationResult<PcoLevelAdjustExcelDTO>> validationResults = Arrays.asList(
                new ValidationResult<>("message", pcoLevelAdjustExcelDTO2));

    }

    @Test
    public void testListOfImport() {
        // Setup
        final PcoLevelImportQuery query = new PcoLevelImportQuery();
        query.setPage(0);
        query.setSize(0);
        query.setStartDate(LocalDate.of(2020, 1, 1));
        query.setEndDate(LocalDate.of(2020, 1, 1));

        // Configure PcoLevelImportService.list(...).
        final PcoLevelImport pcoLevelImport = new PcoLevelImport();
        pcoLevelImport.setFileName("fileName");
        pcoLevelImport.setFileUrl("fileUrl");
        pcoLevelImport.setSuccess(0);
        pcoLevelImport.setError(0);
        pcoLevelImport.setErrorUrl("errorUrl");
        final List<PcoLevelImport> pcoLevelImports = Arrays.asList(pcoLevelImport);
        final PcoLevelImportQuery query1 = new PcoLevelImportQuery();
        query1.setPage(0);
        query1.setSize(0);
        query1.setStartDate(LocalDate.of(2020, 1, 1));
        query1.setEndDate(LocalDate.of(2020, 1, 1));
        when(mockPcoLevelImportService.list(query1)).thenReturn(pcoLevelImports);

        // Run the test
        final PageInfo<PcoLevelImport> result = pcoLevelServiceUnderTest.listOfImport(query);

        // Verify the results
    }

    @Test
    public void testListOfImport_PcoLevelImportServiceReturnsNoItems() {
        // Setup
        final PcoLevelImportQuery query = new PcoLevelImportQuery();
        query.setPage(0);
        query.setSize(0);
        query.setStartDate(LocalDate.of(2020, 1, 1));
        query.setEndDate(LocalDate.of(2020, 1, 1));

        // Configure PcoLevelImportService.list(...).
        final PcoLevelImportQuery query1 = new PcoLevelImportQuery();
        query1.setPage(0);
        query1.setSize(0);
        query1.setStartDate(LocalDate.of(2020, 1, 1));
        query1.setEndDate(LocalDate.of(2020, 1, 1));
        when(mockPcoLevelImportService.list(query1)).thenReturn(Collections.emptyList());

        // Run the test
        final PageInfo<PcoLevelImport> result = pcoLevelServiceUnderTest.listOfImport(query);

        // Verify the results
    }

    @Test
    public void testLevelNewestExport() {
        // Setup
        final PcoLevelQuery query = new PcoLevelQuery();
        query.setRegionName("regionName");
        query.setPage(0);
        query.setSize(0);
        query.setUser("user");
        query.setJobCode("jobCode");

        final PcoNewestLevelExcelDTO pcoNewestLevelExcelDTO = new PcoNewestLevelExcelDTO();
        pcoNewestLevelExcelDTO.setRegionName("regionName");
        pcoNewestLevelExcelDTO.setOrgName("orgName");
        pcoNewestLevelExcelDTO.setUserName("userName");
        pcoNewestLevelExcelDTO.setPcoLevel("name");
        pcoNewestLevelExcelDTO.setBeQualified("name");
        final List<PcoNewestLevelExcelDTO> expectedResult = Arrays.asList(pcoNewestLevelExcelDTO);
    }

    @Test
    public void testLevelNewestExport_PcoLevelInfoMapperReturnsNoItems() {
        // Setup
        final PcoLevelQuery query = new PcoLevelQuery();
        query.setRegionName("regionName");
        query.setPage(0);
        query.setSize(0);
        query.setUser("user");
        query.setJobCode("jobCode");
    }
}
