package com.cfpamf.ms.insur.operation.util;

import com.alibaba.fastjson.util.ParameterizedTypeImpl;
import com.cfpamf.ms.insur.operation.base.dao.CommonMapper;
import com.cfpamf.ms.insur.operation.base.util.SpringFactoryUtil;
import com.github.jsonzou.jmockdata.JMockData;
import com.github.jsonzou.jmockdata.TypeReference;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;
import org.junit.Before;
import org.mockito.InjectMocks;
import org.mockito.MockSettings;
import org.mockito.Mockito;
import org.springframework.context.ApplicationContext;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

import static java.lang.reflect.Modifier.isFinal;
import static java.lang.reflect.Modifier.isStatic;

/**
 * <AUTHOR> 2021/8/9 11:57
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class JMockBaseTest {
    protected ApplicationContext mock;

    @Before
    public void setUp() throws Exception {

        mock = Mockito.mock(ApplicationContext.class);
        Mockito.when(mock.getBean((Class<Object>) Mockito.any()))
                .thenAnswer(a -> Mockito.mock(a.getArgument(0)));

//        Mockito.when(mock.getBean(Mockito.anyString(), (Class<Object>) Mockito.any()))
//                .thenAnswer(a -> Mockito.mock(a.getArgumentAt(1, Class.class)));
        new SpringFactoryUtil().setApplicationContext(mock);

        for (Field declaredField : this.getClass().getDeclaredFields()) {
            final InjectMocks annotation = declaredField.getAnnotation(InjectMocks.class);
            if (Objects.nonNull(annotation)) {
                if (!declaredField.isAccessible()) {
                    declaredField.setAccessible(true);
                }
                resetMapper(declaredField.get(this));
            }
        }

    }

    protected void resetMapper(Object testClz) {
        try {
            for (Field declaredField : testClz.getClass().getDeclaredFields()) {

                int modifiers = declaredField.getModifiers();
                if (!(isFinal(modifiers) || isStatic(modifiers))) {
                    if (!declaredField.isAccessible()) {
                        declaredField.setAccessible(true);
                    }

                    if (CommonMapper.class.isAssignableFrom(declaredField.getType())) {
                        if (!declaredField.isAccessible()) {
                            declaredField.setAccessible(true);
                        }
                        declaredField.set(testClz, initMapper(declaredField.getType()));
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    protected <T> T initMapper(Class<T> clz) {
        MockSettings mockSettings = Mockito.withSettings();
        mockSettings.defaultAnswer(invocation -> {

            ParameterizedType type = (ParameterizedType) clz.getGenericInterfaces()[0];
            Method method = invocation.getMethod();
            Class<?> returnType = method.getReturnType();
            if (returnType != void.class && returnType != Void.class) {

                Type genericReturnType = method.getGenericReturnType();

                Map<String, Type> val = new HashMap<>(1);
                val.put("val", genericReturnType);
                Object mock = null;
                if (Objects.equals(returnType.toString(), genericReturnType.toString())) {
                    mock = JMockData.mock(returnType);
                } else {
                    String s = genericReturnType.toString();
                    if (s.contains("<T>")) {
                        if (genericReturnType instanceof ParameterizedType) {
                            ParameterizedType tmp = (ParameterizedType) genericReturnType;
                            genericReturnType = new ParameterizedTypeImpl(
                                    type.getActualTypeArguments(), tmp.getOwnerType(), tmp.getRawType());
                            val.put("val", genericReturnType);
                        }

                    } else if (s.equals("T")) {
                        val.put("val", type.getActualTypeArguments()[0]);
                    }
                    mock = JMockData.mock(new TypeReference<Object>() {
                        @Override
                        public Type getType() {
                            return val.get("val");
                        }

                    });
                }
                return mock;
            }
            return null;
        });
        return Mockito.mock(clz, mockSettings);
    }

}
