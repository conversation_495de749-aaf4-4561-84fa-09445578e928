package com.cfpamf.ms.insur.operation.msg.service;

import com.cfpamf.ms.insur.operation.base.helper.LockHelper;
import com.cfpamf.ms.insur.operation.msg.dao.OpMessagePushPoolMapper;
import com.cfpamf.ms.insur.operation.msg.pojo.dto.OpMessageRuleDTO;
import com.cfpamf.ms.insur.operation.msg.pojo.po.OpMessageRule;
import com.cfpamf.ms.insur.operation.msg.pojo.po.OpMessageRuleReceiver;
import com.cfpamf.ms.insur.operation.msg.service.push.ImagePusher;
import com.cfpamf.ms.insur.operation.util.JMockBaseTest;
import com.github.jsonzou.jmockdata.JMockData;
import com.github.jsonzou.jmockdata.TypeReference;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.springframework.context.ApplicationContext;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.io.IOException;
import java.time.Instant;
import java.util.Collections;
import java.util.List;

import static org.mockito.Mockito.*;

@RunWith(SpringJUnit4ClassRunner.class)
public class MsgPushServiceTest extends JMockBaseTest {
    @InjectMocks
    MsgPushService msgPushService;
    @org.mockito.Mock
    com.cfpamf.ms.insur.operation.msg.dao.OpMessageRuleMapper ruleMapper;
    @org.mockito.Mock
    com.cfpamf.ms.insur.operation.base.service.SystemGroovyService groovyService;
    @org.mockito.Mock
    com.cfpamf.ms.insur.operation.msg.service.DingTalkService dingTalkService;
    @org.mockito.Mock
    com.cfpamf.ms.insur.operation.msg.dao.OpMessageRuleReceiverMapper receiverMapper;
    @org.mockito.Mock
    com.cfpamf.ms.insur.operation.msg.dao.OpMessagePushMapper pushMapper;
    @org.mockito.Mock
    org.springframework.amqp.rabbit.core.RabbitTemplate rabbitTemplate;
    @org.mockito.Mock
    com.fasterxml.jackson.databind.ObjectMapper objectMapper;

    @org.mockito.Mock
    OpMessagePushPoolMapper pushPoolMapper;

    @org.mockito.Mock
    LockHelper lockHelper;

    @Mock
    ApplicationContext context;

    @Mock
    ImagePusher pusher;

    @Override
    @Before
    public void setUp() throws Exception {

        super.setUp();
        when(context.getBeansOfType(any()))
                .thenReturn(Collections.singletonMap("imagePusher", pusher));

        //?
        doAnswer(an -> {
            Runnable run = an.getArgument(1);
            run.run();
            return null;
        }).when(lockHelper).lockRun(any(), any(Runnable.class));

    }

    @Test
    public void detail() {
        try {
            msgPushService.detail(JMockData.mock(java.lang.Long.class));
        } catch (Exception e) {

        }
    }

    @Test
    public void listConfig() {
        try {
            msgPushService.listConfig(JMockData.mock(com.cfpamf.ms.insur.operation.msg.pojo.query.OpMessageRuleQuery.class));
        } catch (Exception e) {

        }
    }

    @Test
    public void saveConfig() {
        final OpMessageRuleDTO mock = JMockData.mock(OpMessageRuleDTO.class);
        mock.getRule().setCronRule("1 2 8 * * ?");
        mock.setAutoPush(Boolean.TRUE);
        msgPushService.saveConfig(mock, ",OVZyQ,");
    }

    @Test
    public void updateConfig() {
        try {
            msgPushService.updateConfig(JMockData.mock(com.cfpamf.ms.insur.operation.msg.pojo.po.OpMessageRule.class), ",xTatN,");
        } catch (Exception e) {

        }
    }

    @Test
    public void saveReceivers() {
        msgPushService.saveReceivers(JMockData.mock(java.lang.Long.class), JMockData.mock(new TypeReference<List<OpMessageRuleReceiver>>() {
        }));
    }

    @Test
    public void initNextPush() {

        msgPushService.initNextPush(JMockData.mock(com.cfpamf.ms.insur.operation.msg.pojo.po.OpMessageRule.class));
    }

    @Test
    public void initNextPush1() {
        try {
            msgPushService.initNextPush(JMockData.mock(java.lang.Long.class));
        } catch (Exception e) {

        }
    }

    @Test
    public void isNotEnd() {
        msgPushService.isNotEnd(Instant.now(), JMockData.mock(java.time.LocalDate.class));
    }

    @Test
    public void debugGroovy() {
        try {
            msgPushService.debugGroovy(",yaePq,", JMockData.mock(java.lang.Long.class));
        } catch (Exception e) {

        }
    }

    @Test
    public void debugTemp() throws Exception {
        try {
            msgPushService.debugTemp(",IEDEI,", JMockData.mock(java.util.Map.class));
        } catch (Exception e) {

        }
    }

    @Test
    public void pushMessage() throws IOException {
        msgPushService.pushMessage(JMockData.mock(java.lang.Long.class), JMockData.mock(boolean.class));
    }


    @Test
    public void pushMessageCore() throws IOException {
        OpMessageRule mock = JMockData.mock(OpMessageRule.class);
        mock.setMessageType("image");
        msgPushService.pushMessageCore(mock, "1234", true);
    }

    @Test
    public void sendDelayMessage() {
        msgPushService.sendDelayMessage("", 20582);
    }

    @Test
    public void saveMessagePush() {
        try {
            msgPushService.saveMessagePush(JMockData.mock(com.cfpamf.ms.insur.operation.msg.pojo.po.OpMessageRuleReceiver.class), ",qFAxu,", ",SPwFP,", "contextId");
        } catch (Exception e) {

        }
    }

    @Test
    public void sendImage() {
        try {
            msgPushService.sendImage(JMockData.mock(com.cfpamf.ms.insur.operation.msg.pojo.po.OpMessageRuleReceiver.class), ",IIaTH,");
        } catch (Exception e) {

        }
    }

    @Test
    public void cronNextDateFive() {
        MsgPushService.cronNextDateFive("2 2 8 * * *", JMockData.mock(java.util.Date.class));
    }

    @Test
    public void cronNextDate() {
        MsgPushService.cronNextDate("2 2 8 * * *", JMockData.mock(java.util.Date.class));
    }

    @Test
    public void clearCache() {
        try {
            msgPushService.clearCache(",qUCBn,");
        } catch (Exception e) {

        }
    }
}
