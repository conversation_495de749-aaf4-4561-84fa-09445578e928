package com.cfpamf.ms.insur.operation.dingtalk.service;

import com.alibaba.fastjson.JSONObject;
import lombok.AccessLevel;
import lombok.experimental.FieldDefaults;
import lombok.Data;


/**
 * <AUTHOR>
 * @since 2023/10/8 09:52
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class TestSet {

    public static void main(String[] args) {
        String str = "####&nbsp\n" +
                "\n" +
                "[火][向右]让我们一起来看看分支风采吧~&nbsp&nbsp\n" +
                "[向右]分支风云榜-业绩榜：&nbsp内蒙区域-乌兰浩特分支】以当月人均标保26601元，拿下业绩榜第一名！其中【河北、广东、甘肃、辽宁、内蒙、山东、山西、海南、云南】区域各显神通，共30家分支入围业绩榜单。河北区域的分支达50%的霸榜率！[赞]\n" +
                "![img](http://safesfiles.oss-cn-beijing.aliyuncs.com/puppeteer/html-img/1695868935263.png)\n";

        JSONObject object = new JSONObject();
        object.fluentPut("title", "B类业务8月风云榜荣誉榜单-分支战报来袭").
                fluentPut("text", str);
        System.err.println( new JSONObject().fluentPut("msgParam",object.toJSONString()).toJSONString());
    }
}
