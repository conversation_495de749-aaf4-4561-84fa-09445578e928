package com.cfpamf.ms.insur.operation.activity.enums;

import org.junit.Assert;
import org.junit.Test;

import java.math.BigDecimal;

/**
 * <AUTHOR> 2022/6/29 11:40
 */
public class EnumMathOperatorTest {

    @Test
    public void gt() {
        //50000 > 5000
        Assert.assertTrue(EnumMathOperator.GT.execute(new BigDecimal("5000"), new BigDecimal("50000")));
        Assert.assertTrue(EnumMathOperator.GT.execute(new BigDecimal("5000"), new BigDecimal("5000")));
        Assert.assertFalse(EnumMathOperator.GT.execute(new BigDecimal("5000"), new BigDecimal("500")));

    }


    @Test
    public void between() {
        //50000 > 5000
        Assert.assertTrue(EnumMathOperator.BETWEEN.execute("[5000,50000]", new BigDecimal("50000")));
        Assert.assertTrue(EnumMathOperator.BETWEEN.execute("[100,100000]", new BigDecimal("50000")));
        Assert.assertFalse(EnumMathOperator.BETWEEN.execute("[10,10000]", new BigDecimal("50000")));

    }

}
