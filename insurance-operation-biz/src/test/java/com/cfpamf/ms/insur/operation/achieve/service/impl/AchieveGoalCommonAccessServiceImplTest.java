package com.cfpamf.ms.insur.operation.achieve.service.impl;

import com.cfpamf.ms.bms.facade.vo.DictionaryItemVO;
import com.cfpamf.ms.insur.operation.achieve.query.BaseAchieveQuery;
import com.cfpamf.ms.insur.operation.activity.enums.ActivityObject;
import com.cfpamf.ms.insur.operation.activity.enums.ActivityType;
import com.cfpamf.ms.insur.operation.activity.service.SmAchieveActivityService;
import com.cfpamf.ms.insur.operation.activity.service.SystemActivityProgrammeService;
import com.cfpamf.ms.insur.operation.activity.vo.PopUpVO;
import com.cfpamf.ms.insur.operation.activity.vo.SystemActivityProductRuleVo;
import com.cfpamf.ms.insur.operation.activity.vo.SystemActivityProductVo;
import com.cfpamf.ms.insur.operation.activity.vo.SystemActivityProgrammeVo;
import com.cfpamf.ms.insur.operation.base.form.DataAuthPageForm;
import com.cfpamf.ms.insur.operation.base.helper.BmsHelper;
import com.cfpamf.ms.insur.operation.base.service.SystemGroovyService;
import com.cfpamf.ms.insur.operation.base.util.HttpRequestUtil;
import com.cfpamf.ms.insur.operation.base.util.SpringFactoryUtil;
import com.cfpamf.ms.insur.operation.fegin.bms.response.vo.UserDetailVO;
import com.cfpamf.ms.insur.operation.util.JMockBaseTest;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

@RunWith(PowerMockRunner.class)
@PrepareForTest({HttpRequestUtil.class, SpringFactoryUtil.class})
public class AchieveGoalCommonAccessServiceImplTest extends JMockBaseTest {

    @Mock
    private SystemActivityProgrammeService mockActivityProgrammeService;
    @Mock
    private SmAchieveActivityService mockAchieveActivityService;
    @Mock
    private BmsHelper mockBmsHelper;
    @Mock
    private SystemGroovyService mockSystemGroovyService;

    @InjectMocks private AchieveGoalCommonAccessServiceImpl achieveGoalCommonAccessServiceUnderTest;

    @Test
    public void testGetHomePopData() {
        // Setup
        final BaseAchieveQuery query = new BaseAchieveQuery();
        query.setActivityId(0L);
        query.setUserId("userId");
        query.setRuleCode("ruleCode");
        query.setType("type");

        when(mockAchieveActivityService.getPopUp("activityId")).thenReturn("result");

        // Configure SystemActivityProgrammeService.detail(...).
        final SystemActivityProgrammeVo activityProgrammeVo = new SystemActivityProgrammeVo();
        activityProgrammeVo.setId(0L);
        activityProgrammeVo.setActivityCode("activityCode");
        activityProgrammeVo.setTitle("title");
        activityProgrammeVo.setType(ActivityType.NORMAL);
        activityProgrammeVo.setActivityObject(ActivityObject.STAFF_MANAGER);
        activityProgrammeVo.setBackgroundImageUrl("backgroundImageUrl");
        activityProgrammeVo.setRegionList(Arrays.asList("value"));
        activityProgrammeVo.setRegions("regions");
        activityProgrammeVo.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        activityProgrammeVo.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        activityProgrammeVo.setLastConfigUser("lastConfigUser");
        activityProgrammeVo.setActiveFlag(0);
        final SystemActivityProductVo activityProductVo = new SystemActivityProductVo();
        final SystemActivityProductRuleVo systemActivityProductRuleVo = new SystemActivityProductRuleVo();
        systemActivityProductRuleVo.setRuleCode("ruleCode");
        activityProductVo.setSystemActivityProductRuleList(Arrays.asList(systemActivityProductRuleVo));
        activityProgrammeVo.setSystemActivityProductList(Arrays.asList(activityProductVo));
        when(mockActivityProgrammeService.detail(0L)).thenReturn(activityProgrammeVo);

        when(mockSystemGroovyService.executeForCode("OPERATION_ACTIVI", "ruleCode", "args")).thenReturn("result");

        // Run the test
        final PopUpVO<Object> result = achieveGoalCommonAccessServiceUnderTest.getHomePopData(query);
    }

    @Test
    public void testGetDetailData() {
        // Setup
        final BaseAchieveQuery query = new BaseAchieveQuery();
        query.setActivityId(0L);
        query.setUserId("userId");
        query.setRuleCode("ruleCode");
        query.setType("type");

        // Configure SystemActivityProgrammeService.detail(...).
        final SystemActivityProgrammeVo activityProgrammeVo = new SystemActivityProgrammeVo();
        activityProgrammeVo.setId(0L);
        activityProgrammeVo.setActivityCode("activityCode");
        activityProgrammeVo.setTitle("title");
        activityProgrammeVo.setType(ActivityType.NORMAL);
        activityProgrammeVo.setActivityObject(ActivityObject.STAFF_MANAGER);
        activityProgrammeVo.setBackgroundImageUrl("backgroundImageUrl");
        activityProgrammeVo.setRegionList(Arrays.asList("value"));
        activityProgrammeVo.setRegions("regions");
        activityProgrammeVo.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        activityProgrammeVo.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        activityProgrammeVo.setLastConfigUser("lastConfigUser");
        activityProgrammeVo.setActiveFlag(0);
        final SystemActivityProductVo activityProductVo = new SystemActivityProductVo();
        final SystemActivityProductRuleVo systemActivityProductRuleVo = new SystemActivityProductRuleVo();
        systemActivityProductRuleVo.setRuleCode("ruleCode");
        activityProductVo.setSystemActivityProductRuleList(Arrays.asList(systemActivityProductRuleVo));
        activityProgrammeVo.setSystemActivityProductList(Arrays.asList(activityProductVo));
        when(mockActivityProgrammeService.detail(0L)).thenReturn(activityProgrammeVo);

        when(mockSystemGroovyService.executeForCode("OPERATION_ACTIVI", "ruleCode", "args")).thenReturn("result");

        // Run the test
        final Object result = achieveGoalCommonAccessServiceUnderTest.getDetailData(query);

        // Verify the results
    }

    @Test
    public void testHandlerParams() {
        // Setup
        final BaseAchieveQuery params = new BaseAchieveQuery();
        params.setActivityId(0L);
        params.setUserId("userId");
        params.setRuleCode("ruleCode");
        params.setType("type");

        final SystemActivityProgrammeVo activityProgrammeVo = new SystemActivityProgrammeVo();
        activityProgrammeVo.setId(0L);
        activityProgrammeVo.setActivityCode("activityCode");
        activityProgrammeVo.setTitle("title");
        activityProgrammeVo.setType(ActivityType.NORMAL);
        activityProgrammeVo.setActivityObject(ActivityObject.STAFF_MANAGER);
        activityProgrammeVo.setBackgroundImageUrl("backgroundImageUrl");
        activityProgrammeVo.setRegionList(Arrays.asList("value"));
        activityProgrammeVo.setRegions("regions");
        activityProgrammeVo.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        activityProgrammeVo.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        activityProgrammeVo.setLastConfigUser("lastConfigUser");
        activityProgrammeVo.setActiveFlag(0);
        final SystemActivityProductVo activityProductVo = new SystemActivityProductVo();
        final SystemActivityProductRuleVo systemActivityProductRuleVo = new SystemActivityProductRuleVo();
        systemActivityProductRuleVo.setRuleCode("ruleCode");
        activityProductVo.setSystemActivityProductRuleList(Arrays.asList(systemActivityProductRuleVo));
        activityProgrammeVo.setSystemActivityProductList(Arrays.asList(activityProductVo));

        // Run the test
        achieveGoalCommonAccessServiceUnderTest.handlerParams(params, activityProgrammeVo,new DataAuthPageForm(),new UserDetailVO());

        // Verify the results
    }

    @Test
    public void testGetAcId() {
        // Setup
        // Configure BmsHelper.getValidDictionaryItemsByTypeCode(...).
        final DictionaryItemVO dictionaryItemVO = new DictionaryItemVO();
        dictionaryItemVO.setId(0);
        dictionaryItemVO.setTypeId(0);
        dictionaryItemVO.setItemName("itemName");
        dictionaryItemVO.setItemCode("itemCode");
        dictionaryItemVO.setOrderNo(0);
        dictionaryItemVO.setItemDesc("itemDesc");
        dictionaryItemVO.setItemStatus(0);
        final List<DictionaryItemVO> dictionaryItemVOS = Arrays.asList(dictionaryItemVO);
        when(mockBmsHelper.getValidDictionaryItemsByTypeCode("ACHIEVE_AC_COLLECTION")).thenReturn(dictionaryItemVOS);

        // Run the test
        final String result = achieveGoalCommonAccessServiceUnderTest.getAcId("itemName");

        // Verify the results
        assertThat(result).isEqualTo("itemCode");
    }

    @Test
    public void testGetAcId_BmsHelperReturnsNoItems() {
        // Setup
        when(mockBmsHelper.getValidDictionaryItemsByTypeCode("ACHIEVE_AC_COLLECTION")).thenReturn(Collections.emptyList());

        // Run the test
        final String result = achieveGoalCommonAccessServiceUnderTest.getAcId("code");

        // Verify the results
        assertThat(result).isEqualTo(null);
    }

    @Test
    public void testIsValid() {
        // Setup
        final SystemActivityProgrammeVo activityProgrammeVo = new SystemActivityProgrammeVo();
        activityProgrammeVo.setId(0L);
        activityProgrammeVo.setActivityCode("activityCode");
        activityProgrammeVo.setTitle("title");
        activityProgrammeVo.setType(ActivityType.NORMAL);
        activityProgrammeVo.setActivityObject(ActivityObject.STAFF_MANAGER);
        activityProgrammeVo.setBackgroundImageUrl("backgroundImageUrl");
        activityProgrammeVo.setRegionList(Arrays.asList("value"));
        activityProgrammeVo.setRegions("regions");
        activityProgrammeVo.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        activityProgrammeVo.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        activityProgrammeVo.setLastConfigUser("lastConfigUser");
        activityProgrammeVo.setActiveFlag(0);
        final SystemActivityProductVo activityProductVo = new SystemActivityProductVo();
        final SystemActivityProductRuleVo systemActivityProductRuleVo = new SystemActivityProductRuleVo();
        systemActivityProductRuleVo.setRuleCode("ruleCode");
        activityProductVo.setSystemActivityProductRuleList(Arrays.asList(systemActivityProductRuleVo));
        activityProgrammeVo.setSystemActivityProductList(Arrays.asList(activityProductVo));

        // Run the test
        final boolean result = achieveGoalCommonAccessServiceUnderTest.isValid(activityProgrammeVo);

        // Verify the results
        assertThat(result).isFalse();
    }
}
