package com.cfpamf.ms.insur.operation.qy.service;

import com.cfpamf.ms.insur.operation.qy.convter.OpeQyLabelRuleCvt;
import com.cfpamf.ms.insur.operation.qy.dao.OpeQyLabelRuleMapper;
import com.cfpamf.ms.insur.operation.qy.form.OpeQyLabelRuleForm;
import com.cfpamf.ms.insur.operation.qy.form.OpeQyQuestionAnswerForm;
import com.github.jsonzou.jmockdata.JMockData;
import com.github.jsonzou.jmockdata.TypeReference;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR> 2022/8/12 10:39
 */
@RunWith(SpringJUnit4ClassRunner.class)
public class OpeQyLabelRuleServiceTest {

    @InjectMocks
    OpeQyLabelRuleService ruleService;

    @Mock
    WxCpProxyService proxyService;
    @Mock
    OpeQyQuestionService qyQuestionService;
    @Mock
    OpeQyLabelRuleMapper ruleMapper;

    @Before
    public void setUp() throws Exception {
    }

    @Test
    public void autoLabel() {
        ruleService.autoLabel(1L, "userid");
    }

    @Test
    public void autoLabelCan() {
        OpeQyQuestionAnswerForm mock = JMockData.mock(OpeQyQuestionAnswerForm.class);
        mock.setAnswerUserType(1);

        List<OpeQyLabelRuleForm> rules = JMockData.mock(new TypeReference<List<OpeQyLabelRuleForm>>() {
        });

        Mockito.when(ruleMapper.selectByPagerId(Mockito.any())).thenReturn(OpeQyLabelRuleCvt.INS.ruleFrom2Po(rules));

        Mockito.when(qyQuestionService.queryAnswer(Mockito.any(), Mockito.any()))
                .thenReturn(mock);
        ruleService.autoLabel(1L, "userid");
    }

    @Test
    public void addLabel() {
        ruleService.addLabel(Arrays.asList("1", "2"), "userid");
    }
}
