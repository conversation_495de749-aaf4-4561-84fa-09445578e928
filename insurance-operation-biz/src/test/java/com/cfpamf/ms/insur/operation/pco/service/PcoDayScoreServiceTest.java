package com.cfpamf.ms.insur.operation.pco.service;

import com.cfpamf.ms.insur.operation.base.service.DataAuthService;
import com.cfpamf.ms.insur.operation.dingtalk.dao.DingTalkSwFormInstanceDetailMapper;
import com.cfpamf.ms.insur.operation.dingtalk.dao.DingTalkSwFormInstanceMapper;
import com.cfpamf.ms.insur.operation.dingtalk.entity.DingTalkSwFormInstanceDetail;
import com.cfpamf.ms.insur.operation.pco.query.PcoDayFormQuery;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.jsonzou.jmockdata.JMockData;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import tk.mybatis.mapper.entity.Config;
import tk.mybatis.mapper.mapperhelper.EntityHelper;

import java.io.IOException;

/**
 * <AUTHOR> 2022/9/9 17:11
 */
@RunWith(SpringJUnit4ClassRunner.class)
public class PcoDayScoreServiceTest {

    @InjectMocks
    PcoDayScoreService dayScoreService;

    @Mock
    DingTalkSwFormInstanceMapper instanceMapper;
    @Mock
    DingTalkSwFormInstanceDetailMapper detailMapper;
    @Mock
    DataAuthService dataAuthService;
    @Mock
    ObjectMapper mapper;

    @Before
    public void setUp() throws Exception {
        EntityHelper.initEntityNameMap(DingTalkSwFormInstanceDetail.class, new Config());
    }

    @Test
    public void listDayScopes() {
        PcoDayFormQuery mock = JMockData.mock(PcoDayFormQuery.class);
        mock.setAll(false);
        dayScoreService.listDayScopes(mock);
    }

    @Test
    public void listFormDetail() {
        dayScoreService.listFormDetail("mock");
    }

    @Test
    public void exportExcel() throws IOException {

        PcoDayFormQuery mock = JMockData.mock(PcoDayFormQuery.class);
        MockHttpServletResponse httpServletResponse = new MockHttpServletResponse();
        try {
            dayScoreService.exportExcel(mock, httpServletResponse);
        } catch (Exception e) {
            //skil
        }
    }


}
