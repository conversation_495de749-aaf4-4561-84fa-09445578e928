package com.cfpamf.ms.insur.operation.customer.service;

import com.cfpamf.ms.insur.operation.customer.dao.*;
import com.cfpamf.ms.insur.operation.customer.dto.OrderCustomerDto;
import com.cfpamf.ms.insur.operation.customer.dto.PolicyInfoNotifyMessage;
import com.cfpamf.ms.insur.operation.customer.po.CustomerInterruptionPo;
import com.cfpamf.ms.insur.operation.customer.po.CustomerInterruptionPolicyPo;
import com.cfpamf.ms.insur.operation.msg.dao.OrderMapper;
import com.cfpamf.ms.insur.operation.order.dto.SmOrderDto;
import com.cfpamf.ms.insur.operation.phoenix.enums.EnumTodoBizType;
import com.cfpamf.ms.insur.operation.phoenix.service.PhoenixEmpTodoService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class CustomerRenewServiceTest {

    @Mock
    private OrderMapper mockOrderMapper;
    @Mock
    private CustomerInterruptionMapper mockCustomerInterruptionMapper;
    @Mock
    private CustomerInterruptionPolicyMapper mockCustomerInterruptionPolicyMapper;
    @Mock
    private PhoenixEmpTodoService mockPhoenixEmpTodoService;
    @Mock
    private CustomerInterruptionFollowMapper customerInterruptionFollowMapper;

    private CustomerRenewService customerRenewServiceUnderTest;

    private CustomerLoanFollowMapper customerLoanFollowMapper;
    private CustomerLoanMapper customerLoanMapper;

    private CustomerLoanPolicyMapper customerLoanPolicyMapper;
    @Before
    public void setUp() throws Exception {
        customerRenewServiceUnderTest = new CustomerRenewService(mockOrderMapper, mockCustomerInterruptionMapper,
                mockCustomerInterruptionPolicyMapper, mockPhoenixEmpTodoService,customerInterruptionFollowMapper
        ,customerLoanFollowMapper,customerLoanMapper,customerLoanPolicyMapper);
    }

    @Test
    public void testHandleMessage() {
        // Setup
        final PolicyInfoNotifyMessage message = new PolicyInfoNotifyMessage();
        message.setFhOrderId("fhOrderId");
        message.setProductId(0);
        message.setPlanId(0);
        message.setQty("qty");
        message.setTotalAmount(new BigDecimal("0.00"));

        // Configure OrderMapper.getOrderInfoByOrderId(...).
        final SmOrderDto smOrderDto = new SmOrderDto();
        smOrderDto.setFhOrderId("fhOrderId");
        smOrderDto.setProductId(0);
        smOrderDto.setTotalAmount(new BigDecimal("0.00"));
        smOrderDto.setCustomerAdminId("customerAdminId");
        smOrderDto.setProductAttrCode("productAttrCode");
        when(mockOrderMapper.getOrderInfoByOrderId("fhOrderId")).thenReturn(smOrderDto);

        // Run the test
        customerRenewServiceUnderTest.handleMessage(message);
    }

    @Test
    public void testHandleMessage_OrderMapperGetInsurInfoByOrderIdReturnsNoItems() {
        // Setup
        final PolicyInfoNotifyMessage message = new PolicyInfoNotifyMessage();
        message.setFhOrderId("fhOrderId");
        message.setProductId(0);
        message.setPlanId(0);
        message.setQty("qty");
        message.setTotalAmount(new BigDecimal("0.00"));

        // Configure OrderMapper.getOrderInfoByOrderId(...).
        final SmOrderDto smOrderDto = new SmOrderDto();
        smOrderDto.setFhOrderId("fhOrderId");
        smOrderDto.setProductId(0);
        smOrderDto.setTotalAmount(new BigDecimal("0.00"));
        smOrderDto.setCustomerAdminId("customerAdminId");
        smOrderDto.setProductAttrCode("productAttrCode");
        when(mockOrderMapper.getOrderInfoByOrderId("fhOrderId")).thenReturn(smOrderDto);

        // Run the test
        customerRenewServiceUnderTest.handleMessage(message);
    }

    @Test
    public void testHandleMessage_OrderMapperGetItemInfoByOrderIdReturnsNoItems() {
        // Setup
        final PolicyInfoNotifyMessage message = new PolicyInfoNotifyMessage();
        message.setFhOrderId("fhOrderId");
        message.setProductId(0);
        message.setPlanId(0);
        message.setQty("qty");
        message.setTotalAmount(new BigDecimal("0.00"));

        // Configure OrderMapper.getOrderInfoByOrderId(...).
        final SmOrderDto smOrderDto = new SmOrderDto();
        smOrderDto.setFhOrderId("fhOrderId");
        smOrderDto.setProductId(0);
        smOrderDto.setTotalAmount(new BigDecimal("0.00"));
        smOrderDto.setCustomerAdminId("customerAdminId");
        smOrderDto.setProductAttrCode("productAttrCode");
        when(mockOrderMapper.getOrderInfoByOrderId("fhOrderId")).thenReturn(smOrderDto);


        // Run the test
        customerRenewServiceUnderTest.handleMessage(message);
    }
}
