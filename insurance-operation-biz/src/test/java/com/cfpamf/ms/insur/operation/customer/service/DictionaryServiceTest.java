package com.cfpamf.ms.insur.operation.customer.service;

import com.cfpamf.ms.insur.operation.base.vo.DictionaryVO;
import com.cfpamf.ms.insur.operation.customer.dao.DictionaryMapper;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class DictionaryServiceTest {

    @Mock
    private DictionaryMapper mockMapper;

    @InjectMocks
    private DictionaryService dictionaryServiceUnderTest;

    @Test
    public void testGetDictionarys() {
        // Setup
        final DictionaryVO dictionaryVO = new DictionaryVO();
        dictionaryVO.setId(0);
        dictionaryVO.setType("type");
        dictionaryVO.setCode("code");
        dictionaryVO.setName("name");
        dictionaryVO.setSorting(0);
        final List<DictionaryVO> expectedResult = Arrays.asList(dictionaryVO);

        // Configure DictionaryMapper.listDictionarys(...).
        final DictionaryVO dictionaryVO1 = new DictionaryVO();
        dictionaryVO1.setId(0);
        dictionaryVO1.setType("type");
        dictionaryVO1.setCode("code");
        dictionaryVO1.setName("name");
        dictionaryVO1.setSorting(0);
        final List<DictionaryVO> dictionaryVOS = Arrays.asList(dictionaryVO1);
        when(mockMapper.listDictionarys("type", "keyWord", false)).thenReturn(dictionaryVOS);

        // Run the test
        final List<DictionaryVO> result = dictionaryServiceUnderTest.getDictionarys("type", "keyWord", false);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testGetDictionarys_DictionaryMapperReturnsNoItems() {
        // Setup
        when(mockMapper.listDictionarys("type", "keyWord", false)).thenReturn(Collections.emptyList());

        // Run the test
        final List<DictionaryVO> result = dictionaryServiceUnderTest.getDictionarys("type", "keyWord", false);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }
}
