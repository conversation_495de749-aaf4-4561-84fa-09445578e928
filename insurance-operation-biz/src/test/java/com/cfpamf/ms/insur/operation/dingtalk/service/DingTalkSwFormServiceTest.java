package com.cfpamf.ms.insur.operation.dingtalk.service;

import com.aliyun.dingtalkswform_1_0.Client;
import com.aliyun.dingtalkswform_1_0.models.ListFormInstancesResponse;
import com.aliyun.dingtalkswform_1_0.models.ListFormInstancesResponseBody;
import com.cfpamf.ms.insur.operation.base.service.SystemGroovyService;
import com.cfpamf.ms.insur.operation.dingtalk.dao.DingTalkSwFormInstanceDetailMapper;
import com.cfpamf.ms.insur.operation.dingtalk.dao.DingTalkSwFormInstanceMapper;
import com.cfpamf.ms.insur.operation.dingtalk.dao.DingTalkSwFormMapper;
import com.cfpamf.ms.insur.operation.dingtalk.entity.DingTalkSwForm;
import com.cfpamf.ms.insur.operation.dingtalk.entity.DingTalkSwFormInstance;
import com.cfpamf.ms.insur.operation.dingtalk.entity.DingTalkSwFormInstanceDetail;
import com.cfpamf.ms.insur.operation.msg.service.DingTalkService;
import com.cfpamf.ms.insur.operation.pco.service.PcoExtendInfoService;
import com.cfpamf.ms.insur.operation.pco.service.PcoWeeksScoreService;
import com.github.jsonzou.jmockdata.JMockData;
import com.github.jsonzou.jmockdata.TypeReference;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.aop.framework.AopContext;
import tk.mybatis.mapper.entity.Config;
import tk.mybatis.mapper.mapperhelper.EntityHelper;

import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR> 2022/9/9 17:18
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({AopContext.class})
@PowerMockIgnore({"javax.management.*", "javax.swing.*"})
public class DingTalkSwFormServiceTest {

    @Mock
    DingTalkSwFormService service;

    @Mock
    Client client;
    @Mock
    DingTalkService dingTalkService;
    @Mock
    DingTalkSwFormMapper mapper;
    @Mock
    DingTalkSwFormInstanceMapper instanceMapper;

    @Mock
    DingTalkSwFormInstanceDetailMapper instanceDetailMapper;

    @Mock
    SystemGroovyService groovyService;

    @Mock
    PcoExtendInfoService pcoExtendInfoService;

    @Mock
    PcoWeeksScoreService pcoWeeksScoreService;


    @Before
    public void setUp() throws Exception {
        EntityHelper.initEntityNameMap(DingTalkSwForm.class, new Config());
        EntityHelper.initEntityNameMap(DingTalkSwFormInstanceDetail.class, new Config());
        PowerMockito.mockStatic(AopContext.class);
        PowerMockito.when(AopContext.currentProxy()).thenReturn(service);

        Mockito.when(groovyService.executeForCode(Mockito.anyString(), Mockito.anyString(), Mockito.any()))
                .thenReturn(1);

    }

    @Test
    public void syncAllFormDetail() {

        try {
            service.syncAllFormDetail(LocalDate.MAX);
        } catch (Exception e) {
            // skip
        }
    }

    @Test
    public void syncSwFormDetail() throws Exception {
        ListFormInstancesResponse mock = JMockData.mock(ListFormInstancesResponse.class);
        mock.getBody().getResult().setHasMore(Boolean.FALSE);

        ListFormInstancesResponse response = JMockData.mock(ListFormInstancesResponse.class);

        response.getBody().setSuccess(Boolean.TRUE);
        response.getBody().getResult().setHasMore(Boolean.FALSE);
        response.getBody().getResult().getList()
                .forEach(item -> {
                    item.setCreateTime("2011-12-03T10:15:30");
                    ListFormInstancesResponseBody.ListFormInstancesResponseBodyResultListForms listFormInstancesResponseBodyResultListForms = item.getForms()
                            .get(0);
                    listFormInstancesResponseBodyResultListForms.setLabel("日期");
                    listFormInstancesResponseBodyResultListForms.setValue("2022-01-01");
                });
        Mockito.when(client.listFormInstancesWithOptions(Mockito.anyString(), Mockito.any(), Mockito.any(), Mockito.any()))
                .thenReturn(response);
        service.syncSwFormDetail("code", 0, LocalDate.MAX);
    }

    @Test
    public void saveInstanceDetail() {
        service.saveInstanceDetail(JMockData.mock(new TypeReference<List<DingTalkSwFormInstanceDetail>>() {
        }));
    }

    @Test
    public void callScope() {
        service.callScope(JMockData.mock(DingTalkSwFormInstance.class), JMockData.mock(DingTalkSwFormInstanceDetail.class));
    }

    @Test
    public void syncSwFroms() {
        service.syncSwFroms(0L);
    }
}
