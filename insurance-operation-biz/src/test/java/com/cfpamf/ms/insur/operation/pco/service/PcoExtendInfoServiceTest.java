package com.cfpamf.ms.insur.operation.pco.service;

import com.cfpamf.ms.insur.operation.base.helper.BmsHelper;
import com.cfpamf.ms.insur.operation.dingtalk.service.DingTalkSdkMissService;
import com.cfpamf.ms.insur.operation.msg.dao.UserPostMapper;
import com.cfpamf.ms.insur.operation.msg.pojo.po.UserPostSimple;
import com.cfpamf.ms.insur.operation.msg.service.DingTalkService;
import com.cfpamf.ms.insur.operation.pco.dao.PcoExtendInfoMapper;
import com.cfpamf.ms.insur.operation.pco.entity.PcoExtendInfo;
import com.cfpamf.ms.insur.operation.pco.query.PcoPageQuery;
import com.cfpamf.ms.insur.operation.pco.vo.UserVo;
import com.dingtalk.api.response.OapiUserGetResponse;
import com.github.pagehelper.PageInfo;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class PcoExtendInfoServiceTest {

    @Mock
    private PcoExtendInfoMapper mockExtendInfoMapper;
    @Mock
    private DingTalkService mockDingTalkService;
    @Mock
    private UserPostMapper mockPostMapper;
    @Mock
    private DingTalkSdkMissService mockDingTalkSdkMissService;
    @Mock
    private BmsHelper mockBmsHelper;

    private PcoExtendInfoService pcoExtendInfoServiceUnderTest;

    @Before
    public void setUp() throws Exception {
        pcoExtendInfoServiceUnderTest = new PcoExtendInfoService(mockExtendInfoMapper, mockDingTalkService,
                mockPostMapper, mockDingTalkSdkMissService, mockBmsHelper);
    }

    @Test
    public void testListUserByCode() {
        // Setup
        final PcoPageQuery pageForm = new PcoPageQuery();
        pageForm.setPageNo(0);
        pageForm.setPageSize(0);
        pageForm.setKeyword("keyword");

        // Run the test
        final PageInfo<UserVo> result = pcoExtendInfoServiceUnderTest.listUserByCode(pageForm);

        // Verify the results
        verify(mockExtendInfoMapper).selectUserByPost("keyword", "2357");
    }

    @Test
    public void testSelectMissInfo() {
        // Setup
        when(mockExtendInfoMapper.selectMissInfoDingUser()).thenReturn(Arrays.asList("value"));

        // Run the test
        final List<String> result = pcoExtendInfoServiceUnderTest.selectMissInfo();

        // Verify the results
        assertThat(result).isEqualTo(Arrays.asList("value"));
    }

    @Test
    public void testSelectMissInfo_PcoExtendInfoMapperReturnsNoItems() {
        // Setup
        when(mockExtendInfoMapper.selectMissInfoDingUser()).thenReturn(Collections.emptyList());

        // Run the test
        final List<String> result = pcoExtendInfoServiceUnderTest.selectMissInfo();

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testSyncDingUserByPost() {
        // Run the test
        pcoExtendInfoServiceUnderTest.syncDingUserByPost();

        // Verify the results
    }

    @Test
    public void testSyncDingUserByPost_UserPostMapperReturnsNoItems() {

        // Run the test
        pcoExtendInfoServiceUnderTest.syncDingUserByPost();

        // Verify the results
    }

    @Test
    public void testSyncDingUserInfo() {
        // Setup
        when(mockExtendInfoMapper.selectMissInfoDingUser()).thenReturn(Arrays.asList("value"));

        // Configure DingTalkService.getUserInfo(...).
        final OapiUserGetResponse oapiUserGetResponse = new OapiUserGetResponse();
        oapiUserGetResponse.setActive(false);
        oapiUserGetResponse.setAvatar("avatar");
        oapiUserGetResponse.setDepartment(Arrays.asList(0L));
        oapiUserGetResponse.setJobnumber("");
        oapiUserGetResponse.setUserid("");

        // Run the test
        pcoExtendInfoServiceUnderTest.syncDingUserInfo();

        // Verify the results
    }

    @Test
    public void testSyncDingUserInfo_PcoExtendInfoMapperSelectMissInfoDingUserReturnsNoItems() {
        // Setup
        when(mockExtendInfoMapper.selectMissInfoDingUser()).thenReturn(Collections.emptyList());

        // Run the test
        pcoExtendInfoServiceUnderTest.syncDingUserInfo();

        // Verify the results
    }
}
