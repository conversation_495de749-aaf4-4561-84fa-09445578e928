package com.cfpamf.ms.insur.operation.activity.service;

import com.cfpamf.ms.insur.operation.activity.dao.SmCommissionDetailPerformanceMapper;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.time.LocalDateTime;

import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class SmCommissionDetailPerformanceServiceTest {

    @Mock
    private SmCommissionDetailPerformanceMapper mockMapper;

    private SmCommissionDetailPerformanceService smCommissionDetailPerformanceServiceUnderTest;

    @Before
    public void setUp() throws Exception {
        smCommissionDetailPerformanceServiceUnderTest = new SmCommissionDetailPerformanceService(mockMapper);
    }

    @Test
    public void testPerformanceAuto() {
        // Setup
        mockMapper.insertAutoPolicy(LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        mockMapper.insertAutoPolicyBoundaryValue(LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        mockMapper.insertAutoPolicyCancel(LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        // Run the test
        smCommissionDetailPerformanceServiceUnderTest.performanceAuto(LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                LocalDateTime.of(2020, 1, 1, 0, 0, 0));

    }

    @Test
    public void testPerformanceNotCx() {
        // Setup
        mockMapper.insertPolicyNotCx(LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        // Run the test
        smCommissionDetailPerformanceServiceUnderTest.performanceNotCx(LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                LocalDateTime.of(2020, 1, 1, 0, 0, 0));
    }
}
