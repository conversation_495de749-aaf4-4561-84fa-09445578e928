package com.cfpamf.ms.insur.operation.customer.service;

import com.cfpamf.ms.insur.operation.activity.dao.SmCommissionDetailMapper;
import com.cfpamf.ms.insur.operation.base.bean.SmyPageInfo;
import com.cfpamf.ms.insur.operation.base.form.DataAuthPageForm;
import com.cfpamf.ms.insur.operation.base.helper.WxCheckAuthorityHelper;
import com.cfpamf.ms.insur.operation.base.service.DataAuthService;
import com.cfpamf.ms.insur.operation.base.vo.WxSessionVO;
import com.cfpamf.ms.insur.operation.customer.dao.CustomerInterruptionPolicyMapper;
import com.cfpamf.ms.insur.operation.customer.dao.CustomerMapper;
import com.cfpamf.ms.insur.operation.customer.dto.CustomerPolicyDto;
import com.cfpamf.ms.insur.operation.customer.dto.WxCmsSmyDto;
import com.cfpamf.ms.insur.operation.customer.dto.WxInterruptionCustomerDto;
import com.cfpamf.ms.insur.operation.customer.query.CustomerPolicyQuery;
import com.cfpamf.ms.insur.operation.customer.query.WxInterruptionCustomerQuery;
import com.github.pagehelper.PageInfo;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class CustomerServiceTest {

    @Mock
    private CustomerMapper mockCustomerMapper;
    @Mock
    private WxCheckAuthorityHelper mockWxCheckAuthorityHelper;
    @Mock
    private CustomerInterruptionPolicyMapper mockCustomerInterruptionPolicyMapper;
    @Mock
    private DataAuthService mockDataAuthService;

    private CustomerService customerServiceUnderTest;

    @Mock
    private SmCommissionDetailMapper smCommissionDetailMapper;

    @Before
    public void setUp() throws Exception {
//        customerServiceUnderTest = new CustomerService(mockCustomerMapper, mockWxCheckAuthorityHelper,
//                mockCustomerInterruptionPolicyMapper, mockDataAuthService,smCommissionDetailMapper,null,null);
    }

    @Test
    public void testGetBreakCustomerListByPage() {
        // Setup
        final WxInterruptionCustomerQuery query = new WxInterruptionCustomerQuery();
        query.setPageNo(0);
        query.setPageSize(0);
        query.setUserId("userId");
        query.setOpenId("openId");
        query.setAuthorization("authorization");

        // Run the test
        final PageInfo<WxInterruptionCustomerDto> result = customerServiceUnderTest.getBreakCustomerListByPage(query);

    }

    @Test
    public void testGetBreakCustomerListByPage_CustomerMapperReturnsNoItems() {
        // Setup
        final WxInterruptionCustomerQuery query = new WxInterruptionCustomerQuery();
        query.setPageNo(0);
        query.setPageSize(0);
        query.setUserId("userId");
        query.setOpenId("openId");
        query.setAuthorization("authorization");

        // Run the test
        final PageInfo<WxInterruptionCustomerDto> result = customerServiceUnderTest.getBreakCustomerListByPage(query);
    }

    @Test
    public void testGetPolicyNoAll() {
        // Setup
        final CustomerPolicyQuery query = new CustomerPolicyQuery();
        query.setPage(0);
        query.setSize(0);
        query.setIdNumber("idNumber");
        query.setOpenId("openId");
        query.setAuthorization("authorization");

        final CustomerPolicyDto customerPolicyDto = new CustomerPolicyDto();
        customerPolicyDto.setPayStatus("payStatus");
        customerPolicyDto.setAppStatus("-1");
        customerPolicyDto.setEndTime("endTime");
        customerPolicyDto.setFullEndTime("2023-01-01 00:00:00");
        customerPolicyDto.setShowStatusName("");
        final WxCmsSmyDto wxCmsSmyDto = new WxCmsSmyDto();
        wxCmsSmyDto.setOrderAmount(new BigDecimal("0.00"));
        wxCmsSmyDto.setOrderQty(0);
        wxCmsSmyDto.setInterruptionQty(0);
        wxCmsSmyDto.setRenewQty(0);
        final SmyPageInfo<CustomerPolicyDto, WxCmsSmyDto> expectedResult = new SmyPageInfo<>(
                Arrays.asList(customerPolicyDto), wxCmsSmyDto);

        // Configure WxCheckAuthorityHelper.checkAuthority(...).
        final WxSessionVO sessionVO = new WxSessionVO();
        sessionVO.setUserId("userId");
        sessionVO.setSystem("system");
        sessionVO.setChannel("channel");
        sessionVO.setAuthorization("authorization");
        sessionVO.setBmsToken("bmsToken");
        when(mockWxCheckAuthorityHelper.checkAuthority("openId", "authorization")).thenReturn(sessionVO);

        // Configure CustomerMapper.listAppPolicyListAll(...).
        final CustomerPolicyDto customerPolicyDto1 = new CustomerPolicyDto();
        customerPolicyDto1.setPayStatus("payStatus");
        customerPolicyDto1.setAppStatus("-1");
        customerPolicyDto1.setEndTime("endTime");
        customerPolicyDto1.setFullEndTime("2023-01-01 00:00:00");
        customerPolicyDto1.setShowStatusName("");
        final List<CustomerPolicyDto> customerPolicyDtos = Arrays.asList(customerPolicyDto1);
        final CustomerPolicyQuery query1 = new CustomerPolicyQuery();
        query1.setPage(0);
        query1.setSize(0);
        query1.setIdNumber("idNumber");
        query1.setOpenId("openId");
        query1.setAuthorization("authorization");
        when(mockCustomerMapper.listAppPolicyListAll(query1)).thenReturn(customerPolicyDtos);

        // Configure CustomerMapper.listAppPolicyCount(...).
        final WxCmsSmyDto wxCmsSmyDto1 = new WxCmsSmyDto();
        wxCmsSmyDto1.setOrderAmount(new BigDecimal("0.00"));
        wxCmsSmyDto1.setOrderQty(0);
        wxCmsSmyDto1.setInterruptionQty(0);
        wxCmsSmyDto1.setRenewQty(0);
        final CustomerPolicyQuery query2 = new CustomerPolicyQuery();
        query2.setPage(0);
        query2.setSize(0);
        query2.setIdNumber("idNumber");
        query2.setOpenId("openId");
        query2.setAuthorization("authorization");
        when(mockCustomerMapper.listAppPolicyCount(query2)).thenReturn(wxCmsSmyDto1);

        // Run the test
        final SmyPageInfo<CustomerPolicyDto, WxCmsSmyDto> result = customerServiceUnderTest.getPolicyNoAll(query);
    }

    @Test
    public void testGetPolicyNoAll_CustomerMapperListAppPolicyListAllReturnsNoItems() {
        // Setup
        final CustomerPolicyQuery query = new CustomerPolicyQuery();
        query.setPage(0);
        query.setSize(0);
        query.setIdNumber("idNumber");
        query.setOpenId("openId");
        query.setAuthorization("authorization");

        final CustomerPolicyDto customerPolicyDto = new CustomerPolicyDto();
        customerPolicyDto.setPayStatus("payStatus");
        customerPolicyDto.setAppStatus("-1");
        customerPolicyDto.setEndTime("endTime");
        customerPolicyDto.setFullEndTime("2023-01-01 00:00:00");
        customerPolicyDto.setShowStatusName("");
        final WxCmsSmyDto wxCmsSmyDto = new WxCmsSmyDto();
        wxCmsSmyDto.setOrderAmount(new BigDecimal("0.00"));
        wxCmsSmyDto.setOrderQty(0);
        wxCmsSmyDto.setInterruptionQty(0);
        wxCmsSmyDto.setRenewQty(0);
        final SmyPageInfo<CustomerPolicyDto, WxCmsSmyDto> expectedResult = new SmyPageInfo<>(
                Arrays.asList(customerPolicyDto), wxCmsSmyDto);

        // Configure WxCheckAuthorityHelper.checkAuthority(...).
        final WxSessionVO sessionVO = new WxSessionVO();
        sessionVO.setUserId("userId");
        sessionVO.setSystem("system");
        sessionVO.setChannel("channel");
        sessionVO.setAuthorization("authorization");
        sessionVO.setBmsToken("bmsToken");
        when(mockWxCheckAuthorityHelper.checkAuthority("openId", "authorization")).thenReturn(sessionVO);

        // Configure CustomerMapper.listAppPolicyListAll(...).
        final CustomerPolicyQuery query1 = new CustomerPolicyQuery();
        query1.setPage(0);
        query1.setSize(0);
        query1.setIdNumber("idNumber");
        query1.setOpenId("openId");
        query1.setAuthorization("authorization");
        when(mockCustomerMapper.listAppPolicyListAll(query1)).thenReturn(Collections.emptyList());

        // Run the test
        final SmyPageInfo<CustomerPolicyDto, WxCmsSmyDto> result = customerServiceUnderTest.getPolicyNoAll(query);
    }

    @Test
    public void testDataSummarization() {
        // Setup
        final WxCmsSmyDto expectedResult = new WxCmsSmyDto();
        expectedResult.setOrderAmount(new BigDecimal("0.00"));
        expectedResult.setOrderQty(0);
        expectedResult.setInterruptionQty(0);
        expectedResult.setRenewQty(0);

        // Configure WxCheckAuthorityHelper.checkAuthority(...).
        final WxSessionVO sessionVO = new WxSessionVO();
        sessionVO.setUserId("userId");
        sessionVO.setSystem("system");
        sessionVO.setChannel("channel");
        sessionVO.setAuthorization("authorization");
        sessionVO.setBmsToken("bmsToken");
        when(mockWxCheckAuthorityHelper.checkAuthority("openId", "authorization")).thenReturn(sessionVO);

        // Configure CustomerInterruptionPolicyMapper.getQtyByRenewEmp(...).
        final WxCmsSmyDto wxCmsSmyDto = new WxCmsSmyDto();
        wxCmsSmyDto.setOrderAmount(new BigDecimal("0.00"));
        wxCmsSmyDto.setOrderQty(0);
        wxCmsSmyDto.setInterruptionQty(0);
        wxCmsSmyDto.setRenewQty(0);
        when(mockCustomerInterruptionPolicyMapper.getQtyByRenewEmp("userId")).thenReturn(wxCmsSmyDto);

        when(mockCustomerMapper.getInterruptionQty("userId", 1)).thenReturn(0);

        // Run the test
        final WxCmsSmyDto result = customerServiceUnderTest.dataSummarization("openId", "authorization");
    }

    @Test
    public void testGetBackCustomerListByPage() {
        // Setup
        final WxInterruptionCustomerQuery query = new WxInterruptionCustomerQuery();
        query.setPageNo(0);
        query.setPageSize(0);
        query.setUserId("userId");
        query.setOpenId("openId");
        query.setAuthorization("authorization");

        // Run the test
        final PageInfo<WxInterruptionCustomerDto> result = customerServiceUnderTest.getBackCustomerListByPage(query);
    }

    @Test
    public void testGetBackCustomerListByPage_CustomerMapperReturnsNoItems() {
        // Setup
        final WxInterruptionCustomerQuery query = new WxInterruptionCustomerQuery();
        query.setPageNo(0);
        query.setPageSize(0);
        query.setUserId("userId");
        query.setOpenId("openId");
        query.setAuthorization("authorization");

        // Run the test
        final PageInfo<WxInterruptionCustomerDto> result = customerServiceUnderTest.getBackCustomerListByPage(query);
    }
}
