package com.cfpamf.ms.insur.operation.msg.service;

import com.cfpamf.ms.bms.facade.vo.EmployeeDingTalkVO;
import com.cfpamf.ms.insur.operation.base.helper.BmsHelper;
import com.cfpamf.ms.insur.operation.base.helper.LockHelper;
import com.cfpamf.ms.insur.operation.msg.dao.DingTalkUserMapper;
import com.cfpamf.ms.insur.operation.msg.pojo.po.OpMessageRule;
import com.cfpamf.ms.insur.operation.msg.pojo.po.OpMessageRuleReceiver;
import com.github.jsonzou.jmockdata.JMockData;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.function.Supplier;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doAnswer;

/**
 * <AUTHOR> 2021/9/3 15:47
 */
@RunWith(SpringJUnit4ClassRunner.class)
public class EmpPostServiceTest {
    @InjectMocks
    EmpPostService empPostService;

    @Mock
    BmsHelper helper;
    @Mock
    DingTalkUserMapper dingTalkUserMapper;

    @Mock
    LockHelper lockHelper;

    @Before
    public void setUp() throws Exception {
        doAnswer(an -> {
            Supplier run = an.getArgument(1);
            return run.get();
        }).when(lockHelper).lockSupplier(any(), any(Supplier.class));
        Mockito.when(helper.listEmployeeDingUsersByPostName(Mockito.anyString()))
                .thenReturn(JMockData.mock(EmployeeDingTalkVO.class));
    }

    @Test
    public void postNameCvt() {
        OpMessageRule rule = new OpMessageRule();
        rule.setParams("{\"pushRegions\":[\"湖南区域\"]}");
        OpMessageRuleReceiver mock = JMockData.mock(OpMessageRuleReceiver.class);
        mock.setParams("{\"regions\":[\"湖南区域\",\"山西区域\"]}");
        empPostService.postNameCvt(rule, mock, "");
    }

    @Test
    public void postEmployees() {
        OpMessageRule rule = new OpMessageRule();
        rule.setParams("{\"pushRegions\":[\"湖南区域\"]}");
        OpMessageRuleReceiver mock = JMockData.mock(OpMessageRuleReceiver.class);
        mock.setParams("{\"regions\":[\"湖南区域\",\"山西区域\"]}");
        empPostService.postEmployees(rule, mock, "");
    }

    @Test
    public void syncPostUser() {
        Mockito.when(dingTalkUserMapper.selectCountByBatchAndPost(Mockito.anyString(), Mockito.anyString()))
                .thenReturn(0);
        empPostService.syncPostUser("", "");
    }
}
