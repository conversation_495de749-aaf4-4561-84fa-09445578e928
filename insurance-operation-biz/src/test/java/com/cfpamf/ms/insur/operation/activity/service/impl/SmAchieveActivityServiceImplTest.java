package com.cfpamf.ms.insur.operation.activity.service.impl;

import com.cfpamf.ms.insur.operation.activity.dao.AuthUserMapper;
import com.cfpamf.ms.insur.operation.activity.dao.SmAchieveActivityMapper;
import com.cfpamf.ms.insur.operation.activity.entity.AuthUser;
import com.cfpamf.ms.insur.operation.activity.entity.SmAchieveEmpCount;
import com.cfpamf.ms.insur.operation.activity.entity.SmActivityReward;
import com.cfpamf.ms.insur.operation.activity.enums.EnumActivityTriggerType;
import com.cfpamf.ms.insur.operation.activity.enums.RewardType;
import com.cfpamf.ms.insur.operation.activity.form.AchieveActivityParams;
import com.cfpamf.ms.insur.operation.activity.service.SystemActivityProgrammeService;
import com.cfpamf.ms.insur.operation.activity.vo.*;
import com.cfpamf.ms.insur.operation.base.helper.BmsHelper;
import com.cfpamf.ms.insur.operation.base.service.SystemGroovyService;
import com.cfpamf.ms.insur.operation.base.util.RedisUtil;
import com.cfpamf.ms.insur.operation.reward.dto.RewardDTO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class SmAchieveActivityServiceImplTest {

    @Mock
    private SystemGroovyService mockSystemGroovyService;
    @Mock
    private SystemActivityProgrammeService mockActivityProgrammeService;
    @Mock
    private AuthUserMapper mockAuthUserMapper;
    @Mock
    private RedisUtil<String, String> mockRedisUtil;
    @Mock
    private SmAchieveActivityMapper mockAchieveActivityMapper;
    @Mock
    private BmsHelper mockBmsHelper;

    @Mock
    private SmAchieveActivityServiceImpl smAchieveActivityServiceImplUnderTest;

    @Test
    public void testQuerySummaryData() {
        // Setup
        final AchieveActivityParams params = new AchieveActivityParams();
        params.setActivityId(0L);
        params.setUserId("userId");
        params.setOrganizationName("organizationName");
        params.setRegionName("regionName");
        params.setStartTime("startTime");
        params.setEndTime("endTime");
        params.setQueryList(false);
        params.setQueryOrg(false);
        params.setRuleCode("ruleCode");
        params.setActivityState("activeState");

        final AchieveSummaryVO expectedResult = new AchieveSummaryVO();
        expectedResult.setPersonAmount("personAmount");
        expectedResult.setOrgAmount("orgAmount");
        expectedResult.setOrgCount(0);
        expectedResult.setGoalAmount("goalAmount");
        expectedResult.setOrgName("organizationName");
        expectedResult.setUserName("userName");
        expectedResult.setCanPopUp(false);

        // Configure SmAchieveActivityMapper.queryByOrganizationName(...).
        final SmAchieveEmpCount smAchieveEmpCount = new SmAchieveEmpCount();
        smAchieveEmpCount.setCreateBy("createBy");
        smAchieveEmpCount.setEnabledFlag(0);
        smAchieveEmpCount.setRegionName("regionName");
        smAchieveEmpCount.setOrganizationName("organizationName");
        smAchieveEmpCount.setCount("0");
        smAchieveEmpCount.setOrganizationCode("organizationCode");
        smAchieveEmpCount.setRegionCode("regionCode");
        final List<SmAchieveEmpCount> smAchieveEmpCounts = Arrays.asList(smAchieveEmpCount);
        mockAchieveActivityMapper.queryByOrganizationName("organizationName", "regionName");

        // Configure SystemGroovyService.executeForCode(...).
        final AchieveActivityParams args = new AchieveActivityParams();
        args.setActivityId(0L);
        args.setUserId("userId");
        args.setOrganizationName("organizationName");
        args.setRegionName("regionName");
        args.setStartTime("startTime");
        args.setEndTime("endTime");
        args.setQueryList(false);
        args.setQueryOrg(false);
        args.setRuleCode("ruleCode");
        args.setActivityState("activeState");
        mockSystemGroovyService.executeForCode("OPERATION_ACTIVI", "ruleCode", args);

        // Run the test
        smAchieveActivityServiceImplUnderTest.querySummaryData(params);
    }

    @Test
    public void testQuerySummaryData_SmAchieveActivityMapperReturnsNoItems() {
        // Setup
        final AchieveActivityParams params = new AchieveActivityParams();
        params.setActivityId(0L);
        params.setUserId("userId");
        params.setOrganizationName("organizationName");
        params.setRegionName("regionName");
        params.setStartTime("startTime");
        params.setEndTime("endTime");
        params.setQueryList(false);
        params.setQueryOrg(false);
        params.setRuleCode("ruleCode");
        params.setActivityState("activeState");

        final AchieveSummaryVO expectedResult = new AchieveSummaryVO();
        expectedResult.setPersonAmount("personAmount");
        expectedResult.setOrgAmount("orgAmount");
        expectedResult.setOrgCount(0);
        expectedResult.setGoalAmount("goalAmount");
        expectedResult.setOrgName("organizationName");
        expectedResult.setUserName("userName");
        expectedResult.setCanPopUp(false);

        mockAchieveActivityMapper.queryByOrganizationName("organizationName", "regionName");

        // Configure SystemGroovyService.executeForCode(...).
        final AchieveActivityParams args = new AchieveActivityParams();
        args.setActivityId(0L);
        args.setUserId("userId");
        args.setOrganizationName("organizationName");
        args.setRegionName("regionName");
        args.setStartTime("startTime");
        args.setEndTime("endTime");
        args.setQueryList(false);
        args.setQueryOrg(false);
        args.setRuleCode("ruleCode");
        args.setActivityState("activeState");
        mockSystemGroovyService.executeForCode("OPERATION_ACTIVI", "ruleCode", args);

        // Run the test
        smAchieveActivityServiceImplUnderTest.querySummaryData(params);
    }

    @Test
    public void testQuerySummaryData_SystemGroovyServiceReturnsNull() {
        // Setup
        final AchieveActivityParams params = new AchieveActivityParams();
        params.setActivityId(0L);
        params.setUserId("userId");
        params.setOrganizationName("organizationName");
        params.setRegionName("regionName");
        params.setStartTime("startTime");
        params.setEndTime("endTime");
        params.setQueryList(false);
        params.setQueryOrg(false);
        params.setRuleCode("ruleCode");
        params.setActivityState("activeState");

        final AchieveSummaryVO expectedResult = new AchieveSummaryVO();
        expectedResult.setPersonAmount("personAmount");
        expectedResult.setOrgAmount("orgAmount");
        expectedResult.setOrgCount(0);
        expectedResult.setGoalAmount("goalAmount");
        expectedResult.setOrgName("organizationName");
        expectedResult.setUserName("userName");
        expectedResult.setCanPopUp(false);

        // Configure SmAchieveActivityMapper.queryByOrganizationName(...).
        final SmAchieveEmpCount smAchieveEmpCount = new SmAchieveEmpCount();
        smAchieveEmpCount.setCreateBy("createBy");
        smAchieveEmpCount.setEnabledFlag(0);
        smAchieveEmpCount.setRegionName("regionName");
        smAchieveEmpCount.setOrganizationName("organizationName");
        smAchieveEmpCount.setCount("0");
        smAchieveEmpCount.setOrganizationCode("organizationCode");
        smAchieveEmpCount.setRegionCode("regionCode");
        final List<SmAchieveEmpCount> smAchieveEmpCounts = Arrays.asList(smAchieveEmpCount);
        mockAchieveActivityMapper.queryByOrganizationName("organizationName", "regionName");

        // Configure SystemGroovyService.executeForCode(...).
        final AchieveActivityParams args = new AchieveActivityParams();
        args.setActivityId(0L);
        args.setUserId("userId");
        args.setOrganizationName("organizationName");
        args.setRegionName("regionName");
        args.setStartTime("startTime");
        args.setEndTime("endTime");
        args.setQueryList(false);
        args.setQueryOrg(false);
        args.setRuleCode("ruleCode");
        args.setActivityState("activeState");
        mockSystemGroovyService.executeForCode("OPERATION_ACTIVI", "ruleCode", args);

        // Run the test
        smAchieveActivityServiceImplUnderTest.querySummaryData(params);
    }

    @Test
    public void testHandlerParams() {
        // Setup
        final AchieveActivityParams params = new AchieveActivityParams();
        params.setActivityId(0L);
        params.setUserId("userId");
        params.setOrganizationName("organizationName");
        params.setRegionName("regionName");
        params.setStartTime("startTime");
        params.setEndTime("endTime");
        params.setQueryList(false);
        params.setQueryOrg(false);
        params.setRuleCode("ruleCode");
        params.setActivityState("activeState");

        // Configure AuthUserMapper.getByUserId(...).
        final AuthUser authUser = new AuthUser();
        authUser.setCreateBy("createBy");
        authUser.setEnabledFlag(0);
        authUser.setRegionName("regionName");
        authUser.setOrganizationName("organizationName");
        authUser.setRegionCode("regionCode");
        authUser.setOrgCode("orgCode");
        mockAuthUserMapper.getByUserId("userId");

        // Configure SystemActivityProgrammeService.detail(...).
        final SystemActivityProgrammeVo systemActivityProgrammeVo = new SystemActivityProgrammeVo();
        systemActivityProgrammeVo.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        systemActivityProgrammeVo.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        systemActivityProgrammeVo.setActiveState("activeState");
        final SystemActivityProductVo systemActivityProductVo = new SystemActivityProductVo();
        final SystemActivityProductRuleVo systemActivityProductRuleVo = new SystemActivityProductRuleVo();
        systemActivityProductRuleVo.setRuleCode("ruleCode");
        systemActivityProductVo.setSystemActivityProductRuleList(Arrays.asList(systemActivityProductRuleVo));
        systemActivityProgrammeVo.setSystemActivityProductList(Arrays.asList(systemActivityProductVo));
        mockActivityProgrammeService.detail(0L);

        // Run the test
        smAchieveActivityServiceImplUnderTest.handlerParams(params);
    }

    @Test
    public void testQueryTableData() {
        // Setup
        final AchieveActivityParams params = new AchieveActivityParams();
        params.setActivityId(0L);
        params.setUserId("userId");
        params.setOrganizationName("organizationName");
        params.setRegionName("regionName");
        params.setStartTime("startTime");
        params.setEndTime("endTime");
        params.setQueryList(false);
        params.setQueryOrg(false);
        params.setRuleCode("ruleCode");
        params.setActivityState("activeState");

        final AchieveTableVO achieveTableVO = new AchieveTableVO();
        achieveTableVO.setUserName("userName");
        achieveTableVO.setUserId("userId");
        achieveTableVO.setAmount("amount");
        achieveTableVO.setGoalAmount("goalAmount");
        final List<AchieveTableVO> expectedResult = Arrays.asList(achieveTableVO);

        // Configure SmAchieveActivityMapper.queryByOrganizationName(...).
        final SmAchieveEmpCount smAchieveEmpCount = new SmAchieveEmpCount();
        smAchieveEmpCount.setCreateBy("createBy");
        smAchieveEmpCount.setEnabledFlag(0);
        smAchieveEmpCount.setRegionName("regionName");
        smAchieveEmpCount.setOrganizationName("organizationName");
        smAchieveEmpCount.setCount("0");
        smAchieveEmpCount.setOrganizationCode("organizationCode");
        smAchieveEmpCount.setRegionCode("regionCode");
        final List<SmAchieveEmpCount> smAchieveEmpCounts = Arrays.asList(smAchieveEmpCount);
        mockAchieveActivityMapper.queryByOrganizationName("organizationName", "regionName");

        // Configure SystemGroovyService.executeForCode(...).
        final AchieveActivityParams args = new AchieveActivityParams();
        args.setActivityId(0L);
        args.setUserId("userId");
        args.setOrganizationName("organizationName");
        args.setRegionName("regionName");
        args.setStartTime("startTime");
        args.setEndTime("endTime");
        args.setQueryList(false);
        args.setQueryOrg(false);
        args.setRuleCode("ruleCode");
        args.setActivityState("activeState");
        mockSystemGroovyService.executeForCode("OPERATION_ACTIVI", "ruleCode", args);

        // Run the test
        smAchieveActivityServiceImplUnderTest.queryTableData(params);
    }

    @Test
    public void testQueryTableData_SmAchieveActivityMapperReturnsNoItems() {
        // Setup
        final AchieveActivityParams params = new AchieveActivityParams();
        params.setActivityId(0L);
        params.setUserId("userId");
        params.setOrganizationName("organizationName");
        params.setRegionName("regionName");
        params.setStartTime("startTime");
        params.setEndTime("endTime");
        params.setQueryList(false);
        params.setQueryOrg(false);
        params.setRuleCode("ruleCode");
        params.setActivityState("activeState");

        final AchieveTableVO achieveTableVO = new AchieveTableVO();
        achieveTableVO.setUserName("userName");
        achieveTableVO.setUserId("userId");
        achieveTableVO.setAmount("amount");
        achieveTableVO.setGoalAmount("goalAmount");
        final List<AchieveTableVO> expectedResult = Arrays.asList(achieveTableVO);
        mockAchieveActivityMapper.queryByOrganizationName("organizationName", "regionName");

        // Configure SystemGroovyService.executeForCode(...).
        final AchieveActivityParams args = new AchieveActivityParams();
        args.setActivityId(0L);
        args.setUserId("userId");
        args.setOrganizationName("organizationName");
        args.setRegionName("regionName");
        args.setStartTime("startTime");
        args.setEndTime("endTime");
        args.setQueryList(false);
        args.setQueryOrg(false);
        args.setRuleCode("ruleCode");
        args.setActivityState("activeState");
        mockSystemGroovyService.executeForCode("OPERATION_ACTIVI", "ruleCode", args);

        // Run the test
        smAchieveActivityServiceImplUnderTest.queryTableData(params);
    }

    @Test
    public void testQueryTableData_SystemGroovyServiceReturnsNull() {
        // Setup
        final AchieveActivityParams params = new AchieveActivityParams();
        params.setActivityId(0L);
        params.setUserId("userId");
        params.setOrganizationName("organizationName");
        params.setRegionName("regionName");
        params.setStartTime("startTime");
        params.setEndTime("endTime");
        params.setQueryList(false);
        params.setQueryOrg(false);
        params.setRuleCode("ruleCode");
        params.setActivityState("activeState");

        // Configure SmAchieveActivityMapper.queryByOrganizationName(...).
        final SmAchieveEmpCount smAchieveEmpCount = new SmAchieveEmpCount();
        smAchieveEmpCount.setCreateBy("createBy");
        smAchieveEmpCount.setEnabledFlag(0);
        smAchieveEmpCount.setRegionName("regionName");
        smAchieveEmpCount.setOrganizationName("organizationName");
        smAchieveEmpCount.setCount("0");
        smAchieveEmpCount.setOrganizationCode("organizationCode");
        smAchieveEmpCount.setRegionCode("regionCode");
        final List<SmAchieveEmpCount> smAchieveEmpCounts = Arrays.asList(smAchieveEmpCount);
        mockAchieveActivityMapper.queryByOrganizationName("organizationName", "regionName");

        // Configure SystemGroovyService.executeForCode(...).
        final AchieveActivityParams args = new AchieveActivityParams();
        args.setActivityId(0L);
        args.setUserId("userId");
        args.setOrganizationName("organizationName");
        args.setRegionName("regionName");
        args.setStartTime("startTime");
        args.setEndTime("endTime");
        args.setQueryList(false);
        args.setQueryOrg(false);
        args.setRuleCode("ruleCode");
        args.setActivityState("activeState");
        mockSystemGroovyService.executeForCode("OPERATION_ACTIVI", "ruleCode", args);

        // Run the test
        smAchieveActivityServiceImplUnderTest.queryTableData(params);
    }

    @Test
    public void testEditPopUp() {
        // Setup
        mockRedisUtil.getExpire("key");

        // Run the test
        smAchieveActivityServiceImplUnderTest.editPopUp("activityId", "state");

        // Verify the results
        mockRedisUtil.hmSet("key", "hashKey", "state");
        mockRedisUtil.expire("key", 86400L);
    }

    @Test
    public void testQueryOrgData() {
        // Setup
        final AchieveActivityParams params = new AchieveActivityParams();
        params.setActivityId(0L);
        params.setUserId("userId");
        params.setOrganizationName("organizationName");
        params.setRegionName("regionName");
        params.setStartTime("startTime");
        params.setEndTime("endTime");
        params.setQueryList(false);
        params.setQueryOrg(false);
        params.setRuleCode("ruleCode");
        params.setActivityState("activeState");

        final AchieveSummaryVO expectedResult = new AchieveSummaryVO();
        expectedResult.setPersonAmount("personAmount");
        expectedResult.setOrgAmount("orgAmount");
        expectedResult.setOrgCount(0);
        expectedResult.setGoalAmount("goalAmount");
        expectedResult.setOrgName("organizationName");
        expectedResult.setUserName("userName");
        expectedResult.setCanPopUp(false);

        // Configure SmAchieveActivityMapper.queryByOrganizationName(...).
        final SmAchieveEmpCount smAchieveEmpCount = new SmAchieveEmpCount();
        smAchieveEmpCount.setCreateBy("createBy");
        smAchieveEmpCount.setEnabledFlag(0);
        smAchieveEmpCount.setRegionName("regionName");
        smAchieveEmpCount.setOrganizationName("organizationName");
        smAchieveEmpCount.setCount("0");
        smAchieveEmpCount.setOrganizationCode("organizationCode");
        smAchieveEmpCount.setRegionCode("regionCode");
        final List<SmAchieveEmpCount> smAchieveEmpCounts = Arrays.asList(smAchieveEmpCount);
        mockAchieveActivityMapper.queryByOrganizationName("organizationName", "regionName");

        mockRedisUtil.hmGet("key", "hashKey");

        // Configure SystemGroovyService.executeForCode(...).
        final AchieveActivityParams args = new AchieveActivityParams();
        args.setActivityId(0L);
        args.setUserId("userId");
        args.setOrganizationName("organizationName");
        args.setRegionName("regionName");
        args.setStartTime("startTime");
        args.setEndTime("endTime");
        args.setQueryList(false);
        args.setQueryOrg(false);
        args.setRuleCode("ruleCode");
        args.setActivityState("activeState");
        mockSystemGroovyService.executeForCode("OPERATION_ACTIVI", "ruleCode", args);

        // Run the test
        smAchieveActivityServiceImplUnderTest.queryOrgData(params);
    }

    @Test
    public void testQueryOrgData_SmAchieveActivityMapperReturnsNoItems() {
        // Setup
        final AchieveActivityParams params = new AchieveActivityParams();
        params.setActivityId(0L);
        params.setUserId("userId");
        params.setOrganizationName("organizationName");
        params.setRegionName("regionName");
        params.setStartTime("startTime");
        params.setEndTime("endTime");
        params.setQueryList(false);
        params.setQueryOrg(false);
        params.setRuleCode("ruleCode");
        params.setActivityState("activeState");

        final AchieveSummaryVO expectedResult = new AchieveSummaryVO();
        expectedResult.setPersonAmount("personAmount");
        expectedResult.setOrgAmount("orgAmount");
        expectedResult.setOrgCount(0);
        expectedResult.setGoalAmount("goalAmount");
        expectedResult.setOrgName("organizationName");
        expectedResult.setUserName("userName");
        expectedResult.setCanPopUp(false);

        mockAchieveActivityMapper.queryByOrganizationName("organizationName", "regionName");
        mockRedisUtil.hmGet("key", "hashKey");

        // Configure SystemGroovyService.executeForCode(...).
        final AchieveActivityParams args = new AchieveActivityParams();
        args.setActivityId(0L);
        args.setUserId("userId");
        args.setOrganizationName("organizationName");
        args.setRegionName("regionName");
        args.setStartTime("startTime");
        args.setEndTime("endTime");
        args.setQueryList(false);
        args.setQueryOrg(false);
        args.setRuleCode("ruleCode");
        args.setActivityState("activeState");
        mockSystemGroovyService.executeForCode("OPERATION_ACTIVI", "ruleCode", args);

        // Run the test
        smAchieveActivityServiceImplUnderTest.queryOrgData(params);
    }

    @Test
    public void testQueryOrgData_RedisUtilReturnsNull() {
        // Setup
        final AchieveActivityParams params = new AchieveActivityParams();
        params.setActivityId(0L);
        params.setUserId("userId");
        params.setOrganizationName("organizationName");
        params.setRegionName("regionName");
        params.setStartTime("startTime");
        params.setEndTime("endTime");
        params.setQueryList(false);
        params.setQueryOrg(false);
        params.setRuleCode("ruleCode");
        params.setActivityState("activeState");

        final AchieveSummaryVO expectedResult = new AchieveSummaryVO();
        expectedResult.setPersonAmount("personAmount");
        expectedResult.setOrgAmount("orgAmount");
        expectedResult.setOrgCount(0);
        expectedResult.setGoalAmount("goalAmount");
        expectedResult.setOrgName("organizationName");
        expectedResult.setUserName("userName");
        expectedResult.setCanPopUp(false);

        // Configure SmAchieveActivityMapper.queryByOrganizationName(...).
        final SmAchieveEmpCount smAchieveEmpCount = new SmAchieveEmpCount();
        smAchieveEmpCount.setCreateBy("createBy");
        smAchieveEmpCount.setEnabledFlag(0);
        smAchieveEmpCount.setRegionName("regionName");
        smAchieveEmpCount.setOrganizationName("organizationName");
        smAchieveEmpCount.setCount("0");
        smAchieveEmpCount.setOrganizationCode("organizationCode");
        smAchieveEmpCount.setRegionCode("regionCode");
        final List<SmAchieveEmpCount> smAchieveEmpCounts = Arrays.asList(smAchieveEmpCount);
        mockAchieveActivityMapper.queryByOrganizationName("organizationName", "regionName");

        mockRedisUtil.hmGet("key", "hashKey");

        // Configure SystemGroovyService.executeForCode(...).
        final AchieveActivityParams args = new AchieveActivityParams();
        args.setActivityId(0L);
        args.setUserId("userId");
        args.setOrganizationName("organizationName");
        args.setRegionName("regionName");
        args.setStartTime("startTime");
        args.setEndTime("endTime");
        args.setQueryList(false);
        args.setQueryOrg(false);
        args.setRuleCode("ruleCode");
        args.setActivityState("activeState");
        mockSystemGroovyService.executeForCode("OPERATION_ACTIVI", "ruleCode", args);

        // Run the test
        smAchieveActivityServiceImplUnderTest.queryOrgData(params);
    }

    @Test
    public void testQueryOrgData_SystemGroovyServiceReturnsNull() {
        // Setup
        final AchieveActivityParams params = new AchieveActivityParams();
        params.setActivityId(0L);
        params.setUserId("userId");
        params.setOrganizationName("organizationName");
        params.setRegionName("regionName");
        params.setStartTime("startTime");
        params.setEndTime("endTime");
        params.setQueryList(false);
        params.setQueryOrg(false);
        params.setRuleCode("ruleCode");
        params.setActivityState("activeState");

        final AchieveSummaryVO expectedResult = new AchieveSummaryVO();
        expectedResult.setPersonAmount("personAmount");
        expectedResult.setOrgAmount("orgAmount");
        expectedResult.setOrgCount(0);
        expectedResult.setGoalAmount("goalAmount");
        expectedResult.setOrgName("organizationName");
        expectedResult.setUserName("userName");
        expectedResult.setCanPopUp(false);

        // Configure SmAchieveActivityMapper.queryByOrganizationName(...).
        final SmAchieveEmpCount smAchieveEmpCount = new SmAchieveEmpCount();
        smAchieveEmpCount.setCreateBy("createBy");
        smAchieveEmpCount.setEnabledFlag(0);
        smAchieveEmpCount.setRegionName("regionName");
        smAchieveEmpCount.setOrganizationName("organizationName");
        smAchieveEmpCount.setCount("0");
        smAchieveEmpCount.setOrganizationCode("organizationCode");
        smAchieveEmpCount.setRegionCode("regionCode");
        final List<SmAchieveEmpCount> smAchieveEmpCounts = Arrays.asList(smAchieveEmpCount);
        mockAchieveActivityMapper.queryByOrganizationName("organizationName", "regionName");

        mockRedisUtil.hmGet("key", "hashKey");

        // Configure SystemGroovyService.executeForCode(...).
        final AchieveActivityParams args = new AchieveActivityParams();
        args.setActivityId(0L);
        args.setUserId("userId");
        args.setOrganizationName("organizationName");
        args.setRegionName("regionName");
        args.setStartTime("startTime");
        args.setEndTime("endTime");
        args.setQueryList(false);
        args.setQueryOrg(false);
        args.setRuleCode("ruleCode");
        args.setActivityState("activeState");
        mockSystemGroovyService.executeForCode("OPERATION_ACTIVI", "ruleCode", args);

        // Run the test
        smAchieveActivityServiceImplUnderTest.queryOrgData(params);
    }

    @Test
    public void testQueryPopOrgData() {
        // Setup
        final AchieveActivityParams params = new AchieveActivityParams();
        params.setActivityId(0L);
        params.setUserId("userId");
        params.setOrganizationName("organizationName");
        params.setRegionName("regionName");
        params.setStartTime("startTime");
        params.setEndTime("endTime");
        params.setQueryList(false);
        params.setQueryOrg(false);
        params.setRuleCode("ruleCode");
        params.setActivityState("activeState");

        final AchieveSummaryVO expectedResult = new AchieveSummaryVO();
        expectedResult.setPersonAmount("personAmount");
        expectedResult.setOrgAmount("orgAmount");
        expectedResult.setOrgCount(0);
        expectedResult.setGoalAmount("goalAmount");
        expectedResult.setOrgName("organizationName");
        expectedResult.setUserName("userName");
        expectedResult.setCanPopUp(false);

        // Configure SystemGroovyService.executeForCode(...).
        final AchieveActivityParams args = new AchieveActivityParams();
        args.setActivityId(0L);
        args.setUserId("userId");
        args.setOrganizationName("organizationName");
        args.setRegionName("regionName");
        args.setStartTime("startTime");
        args.setEndTime("endTime");
        args.setQueryList(false);
        args.setQueryOrg(false);
        args.setRuleCode("ruleCode");
        args.setActivityState("activeState");
        mockSystemGroovyService.executeForCode("OPERATION_ACTIVI", "ruleCode", args);

        // Run the test
        smAchieveActivityServiceImplUnderTest.queryPopOrgData(params);
    }

    @Test
    public void testQueryPopOrgData_SystemGroovyServiceReturnsNull() {
        // Setup
        final AchieveActivityParams params = new AchieveActivityParams();
        params.setActivityId(0L);
        params.setUserId("userId");
        params.setOrganizationName("organizationName");
        params.setRegionName("regionName");
        params.setStartTime("startTime");
        params.setEndTime("endTime");
        params.setQueryList(false);
        params.setQueryOrg(false);
        params.setRuleCode("ruleCode");
        params.setActivityState("activeState");

        final AchieveSummaryVO expectedResult = new AchieveSummaryVO();
        expectedResult.setPersonAmount("personAmount");
        expectedResult.setOrgAmount("orgAmount");
        expectedResult.setOrgCount(0);
        expectedResult.setGoalAmount("goalAmount");
        expectedResult.setOrgName("organizationName");
        expectedResult.setUserName("userName");
        expectedResult.setCanPopUp(false);

        // Configure SystemGroovyService.executeForCode(...).
        final AchieveActivityParams args = new AchieveActivityParams();
        args.setActivityId(0L);
        args.setUserId("userId");
        args.setOrganizationName("organizationName");
        args.setRegionName("regionName");
        args.setStartTime("startTime");
        args.setEndTime("endTime");
        args.setQueryList(false);
        args.setQueryOrg(false);
        args.setRuleCode("ruleCode");
        args.setActivityState("activeState");
        mockSystemGroovyService.executeForCode("OPERATION_ACTIVI", "ruleCode", args);

        // Run the test
        smAchieveActivityServiceImplUnderTest.queryPopOrgData(params);
    }

    @Test
    public void testGetPopUp() {
        // Setup
        mockRedisUtil.hmGet("key", "hashKey");

        // Run the test
        smAchieveActivityServiceImplUnderTest.getPopUp("activityId");
    }

    @Test
    public void testGetPopUp_RedisUtilReturnsNull() {
        // Setup
        mockRedisUtil.hmGet("key", "hashKey");

        // Run the test
        smAchieveActivityServiceImplUnderTest.getPopUp("activityId");
    }

    @Test
    public void testIsRegion() {
        // Setup
        // Configure SmAchieveActivityMapper.queryByOrganizationName(...).
        final SmAchieveEmpCount smAchieveEmpCount = new SmAchieveEmpCount();
        smAchieveEmpCount.setCreateBy("createBy");
        smAchieveEmpCount.setEnabledFlag(0);
        smAchieveEmpCount.setRegionName("regionName");
        smAchieveEmpCount.setOrganizationName("organizationName");
        smAchieveEmpCount.setCount("0");
        smAchieveEmpCount.setOrganizationCode("organizationCode");
        smAchieveEmpCount.setRegionCode("regionCode");
        final List<SmAchieveEmpCount> smAchieveEmpCounts = Arrays.asList(smAchieveEmpCount);
        mockAchieveActivityMapper.queryByOrganizationName("organizationName", "regionName");

        // Run the test
        smAchieveActivityServiceImplUnderTest.isRegion("organizationName", "regionName");
    }

    @Test
    public void testIsRegion_SmAchieveActivityMapperReturnsNoItems() {
        // Setup
        mockAchieveActivityMapper.queryByOrganizationName("organizationName", "regionName");

        // Run the test
        smAchieveActivityServiceImplUnderTest.isRegion("organizationName", "regionName");
    }

    @Test
    public void testGetAcId() {
        // Setup
        mockBmsHelper.getValidDictionaryItemCodesByTypeCode("ACHIEVE_ACTIVITY_SA_ID");

        // Run the test
        smAchieveActivityServiceImplUnderTest.getAcId();
    }

    @Test
    public void testGetAcId_BmsHelperReturnsNoItems() {
        // Setup
        mockBmsHelper.getValidDictionaryItemCodesByTypeCode("ACHIEVE_ACTIVITY_SA_ID");

        // Run the test
        smAchieveActivityServiceImplUnderTest.getAcId();
    }

    @Test
    public void testImportFile() {
        // Setup
        // Configure AuthUserMapper.distinctRegion(...).
        final AuthUser authUser = new AuthUser();
        authUser.setCreateBy("createBy");
        authUser.setEnabledFlag(0);
        authUser.setRegionName("regionName");
        authUser.setOrganizationName("organizationName");
        authUser.setRegionCode("regionCode");
        authUser.setOrgCode("orgCode");
        final List<AuthUser> authUsers = Arrays.asList(authUser);
        mockAuthUserMapper.distinctRegion();

        // Run the test
        smAchieveActivityServiceImplUnderTest.importFile("fileUrl");

        // Verify the results
        // Confirm SmAchieveActivityMapper.delete(...).
        final SmAchieveEmpCount t = new SmAchieveEmpCount();
        t.setCreateBy("createBy");
        t.setEnabledFlag(0);
        t.setRegionName("regionName");
        t.setOrganizationName("organizationName");
        t.setCount("0");
        t.setOrganizationCode("organizationCode");
        t.setRegionCode("regionCode");
        mockAchieveActivityMapper.delete(t);
    }

    @Test
    public void testImportFile_AuthUserMapperReturnsNoItems() {
        // Setup
        mockAuthUserMapper.distinctRegion();

        // Run the test
        smAchieveActivityServiceImplUnderTest.importFile("fileUrl");

        // Verify the results
        // Confirm SmAchieveActivityMapper.delete(...).
        final SmAchieveEmpCount t = new SmAchieveEmpCount();
        t.setCreateBy("createBy");
        t.setEnabledFlag(0);
        t.setRegionName("regionName");
        t.setOrganizationName("organizationName");
        t.setCount("0");
        t.setOrganizationCode("organizationCode");
        t.setRegionCode("regionCode");
        mockAchieveActivityMapper.delete(t);
    }

    @Test
    public void testIsValid() {
        // Setup
        final AchieveActivityParams achieveActivityParams = new AchieveActivityParams();
        achieveActivityParams.setActivityId(0L);
        achieveActivityParams.setUserId("userId");
        achieveActivityParams.setOrganizationName("organizationName");
        achieveActivityParams.setRegionName("regionName");
        achieveActivityParams.setStartTime("startTime");
        achieveActivityParams.setEndTime("endTime");
        achieveActivityParams.setQueryList(false);
        achieveActivityParams.setQueryOrg(false);
        achieveActivityParams.setRuleCode("ruleCode");
        achieveActivityParams.setActivityState("activeState");

        // Run the test
        smAchieveActivityServiceImplUnderTest.isValid(achieveActivityParams);
    }

    @Test
    public void testAwardByListener() {
        smAchieveActivityServiceImplUnderTest.awardByListener(Arrays.asList(
                new SmActivityReward(new RewardDTO(), EnumActivityTriggerType.JOB, RewardType.ADD_COMMISSION, 0L, 0L)));
    }

    @Test
    public void testAwardByJob() {
        smAchieveActivityServiceImplUnderTest.awardByJob(new SystemActivityProgrammeVo(), new HashMap<>());
    }

    @Test
    public void testTakeBackReward() {
        smAchieveActivityServiceImplUnderTest.takeBackReward(0L, false);
    }
}
