package com.cfpamf.ms.insur.operation.util;

import com.alibaba.fastjson.JSONObject;
import lombok.AccessLevel;
import lombok.experimental.FieldDefaults;
import lombok.Data;
import org.junit.Test;

import java.time.LocalDate;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;

/**
 * <AUTHOR>
 * @since 2023/6/6 16:15
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class FomTest {


    @Test
    public void testDate(){
        System.err.println(YearMonth.from(LocalDate.now().minusDays(20L)));

    }
    @Test
    public void testmm() {

        System.err.println(DateTimeFormatter.ofPattern("MM月dd日").format(LocalDate.now().minusDays(1L)));
    }

    @Test
    public void tesJson() {
        com.alibaba.fastjson.JSONObject jsonObject = new JSONObject();
        jsonObject.put("htmlValue", "<!DOCTYPE html>\n" +
                "<html lang=\"en\">\n" +
                "<head>\n" +
                "  <meta charset=\"UTF-8\">\n" +
                "  <meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge\">\n" +
                "  <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n" +
                "  <title>断保客户跟进情况</title>\n" +
                "  <style>\n" +
                "    body {\n" +
                "      margin: 0;\n" +
                "    }\n" +
                "    .list-table {\n" +
                "      /* width: 1150px; */\n" +
                "      background-color: #fff;\n" +
                "      padding: 25px 10px;\n" +
                "      border-radius: 2px;\n" +
                "    }\n" +
                "    table {\n" +
                "      border-collapse: collapse;\n" +
                "      border-spacing: 0;\n" +
                "      table-layout: fixed;\n" +
                "      border-radius: 10px;\n" +
                "      background: #fff;\n" +
                "      max-width: none;\n" +
                "      min-width: 100%;\n" +
                "    }\n" +
                "    table tr {\n" +
                "      width: 100%;\n" +
                "    }\n" +
                "    table tr>td {\n" +
                "      font-size: 14px;\n" +
                "      text-align: center;\n" +
                "    }\n" +
                "    table tr > td:not(:first-child){\n" +
                "      text-align: right;\n" +
                "    }\n" +
                "    table tr>th {\n" +
                "      font-size: 14px;\n" +
                "      text-align: center;\n" +
                "    }\n" +
                "    table tr>td,\n" +
                "    th {\n" +
                "      padding: 3px;\n" +
                "      border: 1px solid #000;\n" +
                "    }\n" +
                "    .table-title {\n" +
                "      font-size: 22px;\n" +
                "    }\n" +
                "    .pink-bg {\n" +
                "      background: rgb(241, 214, 214);\n" +
                "    }\n" +
                "    .green-bg {\n" +
                "      background: rgb(236, 241, 224) !important;\n" +
                "    }\n" +
                "    .blue-tr>th {\n" +
                "      background: rgb(217, 229, 240);\n" +
                "    }\n" +
                "    .w75 {\n" +
                "      width: 80px;\n" +
                "    }\n" +
                "    .fs-16 {\n" +
                "      font-size: 16px;\n" +
                "    }\n" +
                "    .progress-bar {\n" +
                "      position: relative;\n" +
                "      width: 100%;\n" +
                "      height: 20px;\n" +
                "    }\n" +
                "    .progress {\n" +
                "      position: absolute;\n" +
                "      top: 0;\n" +
                "      left: 0;\n" +
                "      width: 0;\n" +
                "      height: 100%;\n" +
                "      text-align: right;\n" +
                "    }\n" +
                "    .breakIns-gradient {\n" +
                "      background: linear-gradient(to right, #0A4D68,#92b4c2);\n" +
                "    }\n" +
                "    .follow-gradient {\n" +
                "      background: linear-gradient(to right, #088395, #98ced8);\n" +
                "    }\n" +
                "    .renew-gradient {\n" +
                "      background: linear-gradient(to right, #05BFDB, #a8d5dc);\n" +
                "    }\n" +
                "    .sum-gradient {\n" +
                "      background: linear-gradient(to right, #00FFCA, #77b0a5);\n" +
                "    }\n" +
                "    .progress-text {\n" +
                "      position: relative;\n" +
                "      z-index: 99;\n" +
                "    }\n" +
                "    .green {\n" +
                "      background-color: #8ddd90;\n" +
                "    }\n" +
                "    .deep-green {\n" +
                "      background-color: #32b738;\n" +
                "    }\n" +
                "    .yellow {\n" +
                "      background-color: #FFEB3B;\n" +
                "    }\n" +
                "    .red {\n" +
                "      background-color: #fa494b;\n" +
                "    }\n" +
                "  </style>\n" +
                "</head>\n" +
                "<body>\n" +
                "<div class=\"list-table\">\n" +
                "  <!-- 区域/分支 table -->\n" +
                "  <table id=\"myTable\">\n" +
                "    <thead>\n" +
                "    <tr>\n" +
                "      <th colspan=\"19\" class=\"table-title pink-bg\">断保客户跟进情况 <span\n" +
                "              class=\"fs-16\">（截止统计日期：06月07日）</span>\n" +
                "      </th>\n" +
                "    </tr>\n" +
                "    <tr class=\"blue-tr\">\n" +
                "      <th class=\"w75\" rowspan=\"2\">区域</th>\n" +
                "      <th colspan=\"4\" class=\"green-bg\">断保客户数</th>\n" +
                "      <th colspan=\"3\">跟进客户数</th>\n" +
                "      <th class=\"green-bg\" colspan=\"3\">激活客户数</th>\n" +
                "      <th colspan=\"3\">激活保费</th>\n" +
                "      <th class=\"green-bg\" colspan=\"5\">跟进情况（当年）</th>\n" +
                "    </tr>\n" +
                "    <tr class=\"blue-tr\">\n" +
                "      <th class=\"green-bg\">当日</th>\n" +
                "      <th class=\"green-bg\">当月</th>\n" +
                "      <th class=\"green-bg\">当年</th>\n" +
                "      <th class=\"green-bg\">累计</th>\n" +
                "      <th>当日</th>\n" +
                "      <th>当月</th>\n" +
                "      <th>当年</th>\n" +
                "      <th class=\"green-bg\">当日</th>\n" +
                "      <th class=\"green-bg\">当月</th>\n" +
                "      <th class=\"green-bg\">当年</th>\n" +
                "      <th>当日</th>\n" +
                "      <th>当月</th>\n" +
                "      <th>当年(万元)</th>\n" +
                "      <th class=\"green-bg\">高意向</th>\n" +
                "      <th class=\"green-bg\">中意向</th>\n" +
                "      <th class=\"green-bg\">低意向</th>\n" +
                "      <th class=\"green-bg\">无意向</th>\n" +
                "      <th class=\"green-bg\">联系不上</th>\n" +
                "    </tr>\n" +
                "    </thead>\n" +
                "    <tbody id=\"table-body\">\n" +
                "    </tbody>\n" +
                "  </table>\n" +
                "</div>\n" +
                "<script>\n" +
                "  // 表格数据\n" +
                "  const tableData =  [{\"regionname\":\"云南区域\",\"un_renew_day\":0,\"un_renew_week\":180,\"un_renew_month\":148,\"un_renew_year\":5155,\"renew_day\":25,\"renew_week\":114,\"renew_month\":96,\"renew_year\":455,\"policy_day\":26,\"policy_week\":119,\"policy_month\":99,\"policy_year\":463,\"sum_day\":8146.0,\"sum_week\":47020.5,\"sum_month\":22878.0,\"sum_year\":13.2,\"follow_count_day\":0,\"lose_contact_count_day\":0,\"high_count_day\":0,\"median_count_day\":0,\"low_count_day\":0,\"none_count_day\":0,\"follow_count_week\":30,\"lose_contact_count_week\":0,\"high_count_week\":0,\"median_count_week\":4,\"low_count_week\":12,\"none_count_week\":14,\"follow_count_month\":0,\"lose_contact_count_month\":0,\"high_count_month\":0,\"median_count_month\":0,\"low_count_month\":0,\"none_count_month\":0,\"follow_count_year\":117,\"lose_contact_count_year\":2,\"high_count_year\":6,\"median_count_year\":16,\"low_count_year\":34,\"none_count_year\":59,\"un_renew_total\":4.5},{\"regionname\":\"内蒙区域\",\"un_renew_day\":0,\"un_renew_week\":1264,\"un_renew_month\":987,\"un_renew_year\":27995,\"renew_day\":51,\"renew_week\":416,\"renew_month\":330,\"renew_year\":2168,\"policy_day\":50,\"policy_week\":413,\"policy_month\":323,\"policy_year\":2281,\"sum_day\":7950.0,\"sum_week\":85408.20000000001,\"sum_month\":56536.0,\"sum_year\":58.89,\"follow_count_day\":5,\"lose_contact_count_day\":0,\"high_count_day\":0,\"median_count_day\":1,\"low_count_day\":0,\"none_count_day\":4,\"follow_count_week\":83,\"lose_contact_count_week\":3,\"high_count_week\":11,\"median_count_week\":9,\"low_count_week\":7,\"none_count_week\":53,\"follow_count_month\":60,\"lose_contact_count_month\":3,\"high_count_month\":7,\"median_count_month\":8,\"low_count_month\":4,\"none_count_month\":38,\"follow_count_year\":376,\"lose_contact_count_year\":5,\"high_count_year\":39,\"median_count_year\":44,\"low_count_year\":51,\"none_count_year\":237,\"un_renew_total\":26.15},{\"regionname\":\"四川区域\",\"un_renew_day\":0,\"un_renew_week\":370,\"un_renew_month\":354,\"un_renew_year\":6634,\"renew_day\":11,\"renew_week\":88,\"renew_month\":69,\"renew_year\":221,\"policy_day\":11,\"policy_week\":83,\"policy_month\":66,\"policy_year\":214,\"sum_day\":2568.0,\"sum_week\":23989.0,\"sum_month\":17130.0,\"sum_year\":5.55,\"follow_count_day\":10,\"lose_contact_count_day\":0,\"high_count_day\":0,\"median_count_day\":0,\"low_count_day\":7,\"none_count_day\":3,\"follow_count_week\":95,\"lose_contact_count_week\":3,\"high_count_week\":3,\"median_count_week\":2,\"low_count_week\":11,\"none_count_week\":76,\"follow_count_month\":79,\"lose_contact_count_month\":3,\"high_count_month\":3,\"median_count_month\":2,\"low_count_month\":11,\"none_count_month\":60,\"follow_count_year\":555,\"lose_contact_count_year\":8,\"high_count_year\":32,\"median_count_year\":25,\"low_count_year\":66,\"none_count_year\":424,\"un_renew_total\":5.36},{\"regionname\":\"山东区域\",\"un_renew_day\":0,\"un_renew_week\":447,\"un_renew_month\":371,\"un_renew_year\":6250,\"renew_day\":14,\"renew_week\":110,\"renew_month\":69,\"renew_year\":342,\"policy_day\":15,\"policy_week\":100,\"policy_month\":67,\"policy_year\":318,\"sum_day\":4047.0,\"sum_week\":50467.41,\"sum_month\":24438.010000000002,\"sum_year\":13.87,\"follow_count_day\":5,\"lose_contact_count_day\":0,\"high_count_day\":0,\"median_count_day\":0,\"low_count_day\":0,\"none_count_day\":5,\"follow_count_week\":144,\"lose_contact_count_week\":0,\"high_count_week\":16,\"median_count_week\":4,\"low_count_week\":15,\"none_count_week\":109,\"follow_count_month\":127,\"lose_contact_count_month\":0,\"high_count_month\":15,\"median_count_month\":2,\"low_count_month\":10,\"none_count_month\":100,\"follow_count_year\":415,\"lose_contact_count_year\":2,\"high_count_year\":44,\"median_count_year\":23,\"low_count_year\":58,\"none_count_year\":288,\"un_renew_total\":6.04},{\"regionname\":\"山西区域\",\"un_renew_day\":1,\"un_renew_week\":941,\"un_renew_month\":770,\"un_renew_year\":20077,\"renew_day\":54,\"renew_week\":335,\"renew_month\":287,\"renew_year\":1207,\"policy_day\":52,\"policy_week\":326,\"policy_month\":279,\"policy_year\":1193,\"sum_day\":11649.0,\"sum_week\":88180.89999999998,\"sum_month\":75553.59,\"sum_year\":44.02,\"follow_count_day\":55,\"lose_contact_count_day\":1,\"high_count_day\":2,\"median_count_day\":3,\"low_count_day\":14,\"none_count_day\":35,\"follow_count_week\":324,\"lose_contact_count_week\":2,\"high_count_week\":13,\"median_count_week\":36,\"low_count_week\":105,\"none_count_week\":168,\"follow_count_month\":188,\"lose_contact_count_month\":1,\"high_count_month\":8,\"median_count_month\":16,\"low_count_month\":33,\"none_count_month\":130,\"follow_count_year\":1247,\"lose_contact_count_year\":17,\"high_count_year\":87,\"median_count_year\":117,\"low_count_year\":236,\"none_count_year\":790,\"un_renew_total\":16.17},{\"regionname\":\"广东区域\",\"un_renew_day\":1,\"un_renew_week\":231,\"un_renew_month\":203,\"un_renew_year\":5260,\"renew_day\":11,\"renew_week\":84,\"renew_month\":67,\"renew_year\":296,\"policy_day\":11,\"policy_week\":86,\"policy_month\":69,\"policy_year\":297,\"sum_day\":1858.0,\"sum_week\":21391.0,\"sum_month\":17004.0,\"sum_year\":8.61,\"follow_count_day\":0,\"lose_contact_count_day\":0,\"high_count_day\":0,\"median_count_day\":0,\"low_count_day\":0,\"none_count_day\":0,\"follow_count_week\":65,\"lose_contact_count_week\":0,\"high_count_week\":8,\"median_count_week\":2,\"low_count_week\":6,\"none_count_week\":49,\"follow_count_month\":31,\"lose_contact_count_month\":0,\"high_count_month\":2,\"median_count_month\":0,\"low_count_month\":4,\"none_count_month\":25,\"follow_count_year\":155,\"lose_contact_count_year\":6,\"high_count_year\":20,\"median_count_year\":10,\"low_count_year\":19,\"none_count_year\":100,\"un_renew_total\":3.4},{\"regionname\":\"江苏区域\",\"un_renew_day\":0,\"un_renew_week\":69,\"un_renew_month\":61,\"un_renew_year\":246,\"renew_day\":1,\"renew_week\":2,\"renew_month\":2,\"renew_year\":11,\"policy_day\":1,\"policy_week\":2,\"policy_month\":2,\"policy_year\":12,\"sum_day\":788.0,\"sum_week\":888.0,\"sum_month\":888.0,\"sum_year\":0.3,\"follow_count_day\":0,\"lose_contact_count_day\":0,\"high_count_day\":0,\"median_count_day\":0,\"low_count_day\":0,\"none_count_day\":0,\"follow_count_week\":0,\"lose_contact_count_week\":0,\"high_count_week\":0,\"median_count_week\":0,\"low_count_week\":0,\"none_count_week\":0,\"follow_count_month\":0,\"lose_contact_count_month\":0,\"high_count_month\":0,\"median_count_month\":0,\"low_count_month\":0,\"none_count_month\":0,\"follow_count_year\":1,\"lose_contact_count_year\":0,\"high_count_year\":0,\"median_count_year\":0,\"low_count_year\":0,\"none_count_year\":1,\"un_renew_total\":0.35},{\"regionname\":\"江西区域\",\"un_renew_day\":0,\"un_renew_week\":85,\"un_renew_month\":76,\"un_renew_year\":1737,\"renew_day\":3,\"renew_week\":28,\"renew_month\":20,\"renew_year\":63,\"policy_day\":3,\"policy_week\":29,\"policy_month\":21,\"policy_year\":62,\"sum_day\":1200.0,\"sum_week\":8425.0,\"sum_month\":6168.0,\"sum_year\":1.82,\"follow_count_day\":0,\"lose_contact_count_day\":0,\"high_count_day\":0,\"median_count_day\":0,\"low_count_day\":0,\"none_count_day\":0,\"follow_count_week\":3,\"lose_contact_count_week\":0,\"high_count_week\":1,\"median_count_week\":0,\"low_count_week\":0,\"none_count_week\":2,\"follow_count_month\":3,\"lose_contact_count_month\":0,\"high_count_month\":1,\"median_count_month\":0,\"low_count_month\":0,\"none_count_month\":2,\"follow_count_year\":14,\"lose_contact_count_year\":2,\"high_count_year\":1,\"median_count_year\":2,\"low_count_year\":0,\"none_count_year\":9,\"un_renew_total\":1.07},{\"regionname\":\"河北区域\",\"un_renew_day\":1,\"un_renew_week\":2638,\"un_renew_month\":2311,\"un_renew_year\":54958,\"renew_day\":177,\"renew_week\":1216,\"renew_month\":1021,\"renew_year\":4315,\"policy_day\":170,\"policy_week\":1184,\"policy_month\":991,\"policy_year\":4215,\"sum_day\":42901.29,\"sum_week\":339435.36999999994,\"sum_month\":290856.37,\"sum_year\":120.81,\"follow_count_day\":45,\"lose_contact_count_day\":0,\"high_count_day\":1,\"median_count_day\":5,\"low_count_day\":3,\"none_count_day\":36,\"follow_count_week\":545,\"lose_contact_count_week\":5,\"high_count_week\":18,\"median_count_week\":31,\"low_count_week\":79,\"none_count_week\":412,\"follow_count_month\":462,\"lose_contact_count_month\":2,\"high_count_month\":15,\"median_count_month\":26,\"low_count_month\":69,\"none_count_month\":350,\"follow_count_year\":1995,\"lose_contact_count_year\":17,\"high_count_year\":158,\"median_count_year\":119,\"low_count_year\":389,\"none_count_year\":1312,\"un_renew_total\":53.32},{\"regionname\":\"河南区域\",\"un_renew_day\":0,\"un_renew_week\":487,\"un_renew_month\":365,\"un_renew_year\":8676,\"renew_day\":20,\"renew_week\":136,\"renew_month\":103,\"renew_year\":529,\"policy_day\":20,\"policy_week\":142,\"policy_month\":106,\"policy_year\":547,\"sum_day\":6697.0,\"sum_week\":45186.0,\"sum_month\":33415.0,\"sum_year\":15.35,\"follow_count_day\":2,\"lose_contact_count_day\":0,\"high_count_day\":0,\"median_count_day\":1,\"low_count_day\":0,\"none_count_day\":1,\"follow_count_week\":12,\"lose_contact_count_week\":0,\"high_count_week\":4,\"median_count_week\":1,\"low_count_week\":4,\"none_count_week\":3,\"follow_count_month\":7,\"lose_contact_count_month\":0,\"high_count_month\":0,\"median_count_month\":1,\"low_count_month\":4,\"none_count_month\":2,\"follow_count_year\":74,\"lose_contact_count_year\":0,\"high_count_year\":9,\"median_count_year\":8,\"low_count_year\":5,\"none_count_year\":52,\"un_renew_total\":7.9},{\"regionname\":\"海南区域\",\"un_renew_day\":0,\"un_renew_week\":71,\"un_renew_month\":61,\"un_renew_year\":1973,\"renew_day\":5,\"renew_week\":43,\"renew_month\":32,\"renew_year\":149,\"policy_day\":5,\"policy_week\":48,\"policy_month\":34,\"policy_year\":165,\"sum_day\":1900.0,\"sum_week\":18556.0,\"sum_month\":13774.0,\"sum_year\":6.62,\"follow_count_day\":0,\"lose_contact_count_day\":0,\"high_count_day\":0,\"median_count_day\":0,\"low_count_day\":0,\"none_count_day\":0,\"follow_count_week\":12,\"lose_contact_count_week\":0,\"high_count_week\":0,\"median_count_week\":1,\"low_count_week\":1,\"none_count_week\":10,\"follow_count_month\":10,\"lose_contact_count_month\":0,\"high_count_month\":0,\"median_count_month\":1,\"low_count_month\":0,\"none_count_month\":9,\"follow_count_year\":67,\"lose_contact_count_year\":0,\"high_count_year\":3,\"median_count_year\":4,\"low_count_year\":6,\"none_count_year\":54,\"un_renew_total\":2.87},{\"regionname\":\"湖北区域\",\"un_renew_day\":0,\"un_renew_week\":48,\"un_renew_month\":46,\"un_renew_year\":737,\"renew_day\":0,\"renew_week\":6,\"renew_month\":4,\"renew_year\":25,\"policy_day\":0,\"policy_week\":6,\"policy_month\":4,\"policy_year\":24,\"sum_day\":0.0,\"sum_week\":1449.0,\"sum_month\":1100.0,\"sum_year\":0.9,\"follow_count_day\":2,\"lose_contact_count_day\":0,\"high_count_day\":0,\"median_count_day\":0,\"low_count_day\":0,\"none_count_day\":2,\"follow_count_week\":2,\"lose_contact_count_week\":0,\"high_count_week\":0,\"median_count_week\":0,\"low_count_week\":0,\"none_count_week\":2,\"follow_count_month\":2,\"lose_contact_count_month\":0,\"high_count_month\":0,\"median_count_month\":0,\"low_count_month\":0,\"none_count_month\":2,\"follow_count_year\":13,\"lose_contact_count_year\":0,\"high_count_year\":0,\"median_count_year\":0,\"low_count_year\":0,\"none_count_year\":13,\"un_renew_total\":0.65},{\"regionname\":\"湖南区域\",\"un_renew_day\":2,\"un_renew_week\":507,\"un_renew_month\":453,\"un_renew_year\":10121,\"renew_day\":27,\"renew_week\":158,\"renew_month\":117,\"renew_year\":602,\"policy_day\":22,\"policy_week\":153,\"policy_month\":112,\"policy_year\":540,\"sum_day\":4959.0,\"sum_week\":38216.0,\"sum_month\":25532.0,\"sum_year\":15.75,\"follow_count_day\":0,\"lose_contact_count_day\":0,\"high_count_day\":0,\"median_count_day\":0,\"low_count_day\":0,\"none_count_day\":0,\"follow_count_week\":12,\"lose_contact_count_week\":0,\"high_count_week\":2,\"median_count_week\":5,\"low_count_week\":1,\"none_count_week\":4,\"follow_count_month\":3,\"lose_contact_count_month\":0,\"high_count_month\":1,\"median_count_month\":0,\"low_count_month\":0,\"none_count_month\":2,\"follow_count_year\":406,\"lose_contact_count_year\":19,\"high_count_year\":15,\"median_count_year\":12,\"low_count_year\":40,\"none_count_year\":320,\"un_renew_total\":9.44},{\"regionname\":\"甘肃区域\",\"un_renew_day\":0,\"un_renew_week\":810,\"un_renew_month\":683,\"un_renew_year\":19995,\"renew_day\":78,\"renew_week\":373,\"renew_month\":299,\"renew_year\":1445,\"policy_day\":77,\"policy_week\":378,\"policy_month\":300,\"policy_year\":1453,\"sum_day\":20755.42,\"sum_week\":97880.19,\"sum_month\":74982.42,\"sum_year\":37.07,\"follow_count_day\":2,\"lose_contact_count_day\":0,\"high_count_day\":1,\"median_count_day\":0,\"low_count_day\":0,\"none_count_day\":1,\"follow_count_week\":277,\"lose_contact_count_week\":7,\"high_count_week\":8,\"median_count_week\":4,\"low_count_week\":41,\"none_count_week\":217,\"follow_count_month\":174,\"lose_contact_count_month\":5,\"high_count_month\":6,\"median_count_month\":4,\"low_count_month\":37,\"none_count_month\":122,\"follow_count_year\":1227,\"lose_contact_count_year\":28,\"high_count_year\":102,\"median_count_year\":41,\"low_count_year\":210,\"none_count_year\":846,\"un_renew_total\":15.23},{\"regionname\":\"辽宁区域\",\"un_renew_day\":0,\"un_renew_week\":1023,\"un_renew_month\":827,\"un_renew_year\":21410,\"renew_day\":71,\"renew_week\":477,\"renew_month\":380,\"renew_year\":1566,\"policy_day\":73,\"policy_week\":466,\"policy_month\":370,\"policy_year\":1565,\"sum_day\":12000.0,\"sum_week\":98951.0,\"sum_month\":78022.0,\"sum_year\":31.65,\"follow_count_day\":0,\"lose_contact_count_day\":0,\"high_count_day\":0,\"median_count_day\":0,\"low_count_day\":0,\"none_count_day\":0,\"follow_count_week\":46,\"lose_contact_count_week\":0,\"high_count_week\":5,\"median_count_week\":6,\"low_count_week\":6,\"none_count_week\":29,\"follow_count_month\":21,\"lose_contact_count_month\":0,\"high_count_month\":4,\"median_count_month\":6,\"low_count_month\":5,\"none_count_month\":6,\"follow_count_year\":194,\"lose_contact_count_year\":8,\"high_count_year\":43,\"median_count_year\":32,\"low_count_year\":20,\"none_count_year\":91,\"un_renew_total\":14.44},{\"regionname\":\"重庆区域\",\"un_renew_day\":0,\"un_renew_week\":9,\"un_renew_month\":7,\"un_renew_year\":228,\"renew_day\":2,\"renew_week\":2,\"renew_month\":2,\"renew_year\":9,\"policy_day\":2,\"policy_week\":2,\"policy_month\":2,\"policy_year\":7,\"sum_day\":500.0,\"sum_week\":500.0,\"sum_month\":500.0,\"sum_year\":0.19,\"follow_count_day\":0,\"lose_contact_count_day\":0,\"high_count_day\":0,\"median_count_day\":0,\"low_count_day\":0,\"none_count_day\":0,\"follow_count_week\":11,\"lose_contact_count_week\":0,\"high_count_week\":0,\"median_count_week\":0,\"low_count_week\":0,\"none_count_week\":11,\"follow_count_month\":1,\"lose_contact_count_month\":0,\"high_count_month\":0,\"median_count_month\":0,\"low_count_month\":0,\"none_count_month\":1,\"follow_count_year\":113,\"lose_contact_count_year\":0,\"high_count_year\":3,\"median_count_year\":0,\"low_count_year\":2,\"none_count_year\":108,\"un_renew_total\":0.21},{\"regionname\":\"合计\",\"un_renew_day\":5,\"un_renew_week\":9180,\"un_renew_month\":7723,\"un_renew_year\":191452,\"renew_day\":550,\"renew_week\":3588,\"renew_month\":2898,\"renew_year\":13403,\"policy_day\":538,\"policy_week\":3541,\"policy_month\":2848,\"policy_year\":13381,\"sum_day\":127918.70999999999,\"sum_week\":965943.5700000002,\"sum_month\":738777.3900000001,\"sum_year\":374.6,\"follow_count_day\":126,\"lose_contact_count_day\":1,\"high_count_day\":4,\"median_count_day\":10,\"low_count_day\":24,\"none_count_day\":87,\"follow_count_week\":1661,\"lose_contact_count_week\":20,\"high_count_week\":89,\"median_count_week\":105,\"low_count_week\":288,\"none_count_week\":1159,\"follow_count_month\":1168,\"lose_contact_count_month\":14,\"high_count_month\":62,\"median_count_month\":66,\"low_count_month\":177,\"none_count_month\":849,\"follow_count_year\":6969,\"lose_contact_count_year\":114,\"high_count_year\":562,\"median_count_year\":453,\"low_count_year\":1136,\"none_count_year\":4704,\"un_renew_total\":167.08}]\n" +
                "  const allData = tableData.filter(b => b[\"regionname\"] !== '合计')\n" +
                "  function genPre (col, arr) {\n" +
                "    let maxVal = Math.max(...arr.map(a => a[col]))\n" +
                "    arr.forEach(item => {\n" +
                "      if (maxVal > 0) {\n" +
                "        item[col + '_pre'] = Math.round(item[col] / maxVal * 100)\n" +
                "      }\n" +
                "    })\n" +
                "  }\n" +
                "  let unNumKey = [\"regionname\", \"dn\", \"un\", \"org\", \"userid\", \"orgcode\"]\n" +
                "  for (const allDataKey in tableData[0]) {\n" +
                "    if (!isNaN(tableData[0][allDataKey])) {\n" +
                "      genPre(allDataKey, allData)\n" +
                "    }\n" +
                "  }\n" +
                "  const tableBody = document.getElementById(\"table-body\");\n" +
                "  const tableConfig = [\n" +
                "    { title: '区域', field: 'regionname' },\n" +
                "    { title: '当日', field: 'un_renew_day', type: 'progress',belong:'breakIns' },\n" +
                "    { title: '当月', field: 'un_renew_month', type: 'progress',belong:'breakIns' },\n" +
                "    { title: '当年', field: 'un_renew_year', type: 'level',belong:'breakIns' },\n" +
                "    { title: '累计', field: 'un_renew_total', type: 'level' ,belong:'breakIns'},\n" +
                "    { title: '当日', field: 'follow_count_day', type: 'progress',belong:'follow' },\n" +
                "    { title: '当月', field: 'follow_count_month', type: 'progress',belong:'follow' },\n" +
                "    { title: '当年', field: 'follow_count_year', type: 'level',belong:'follow' },\n" +
                "    { title: '当日', field: 'renew_day', type: 'progress',belong:'renew' },\n" +
                "    { title: '当月', field: 'renew_month', type: 'progress',belong:'renew' },\n" +
                "    { title: '当年', field: 'renew_year', type: 'level' ,belong:'renew'},\n" +
                "    { title: '当日', field: 'sum_day', type: 'progress' ,belong:'sum'},\n" +
                "    { title: '当月', field: 'sum_month', type: 'progress',belong:'sum' },\n" +
                "    { title: '当年', field: 'sum_year', type: 'level' ,belong:'sum'},\n" +
                "    { title: '高', field: 'high_count_year' },\n" +
                "    { title: '中', field: 'median_count_year' },\n" +
                "    { title: '低', field: 'low_count_year' },\n" +
                "    { title: '无意向', field: 'none_count_year' },\n" +
                "    { title: '联系不上', field: 'lose_contact_count_year' },\n" +
                "  ]\n" +
                "  // 获取比例值颜色\n" +
                "  function getColorLevel (num) {\n" +
                "    if (num >= 75) {\n" +
                "      return 'red';\n" +
                "    } else if (num >= 50) {\n" +
                "      return 'yellow';\n" +
                "    } else if (num >= 25) {\n" +
                "      return 'deep-green';\n" +
                "    } else {\n" +
                "      return 'green';\n" +
                "    }\n" +
                "  }\n" +
                "  // 获取table元素\n" +
                "  let table = document.getElementById('myTable');\n" +
                "  for (let i = 0; i < tableData.length; i++) {\n" +
                "    // 创建行\n" +
                "    let row = table.insertRow();\n" +
                "    for (let j = 0; j < tableConfig.length; j++) {\n" +
                "      const currentConfig = tableConfig[j];\n" +
                "      const type = currentConfig.type;\n" +
                "      // 创建单元格\n" +
                "      let cell = row.insertCell();\n" +
                "      const curValue = tableData[i][currentConfig.field];\n" +
                "      const curRatio = tableData[i][`${currentConfig.field}_pre`];\n" +
                "      // 添加代码到单元格中\n" +
                "      cell.innerHTML = curValue;\n" +
                "      if (i !== tableData.length - 1) {\n" +
                "        if (type === 'progress') {\n" +
                "          const belong = currentConfig.belong\n" +
                "          cell.innerHTML = `\n" +
                "                        <div class=\"progress-bar\">\n" +
                "                            <div class=\"progress ${belong}-gradient\" style=\"width: ${curRatio}%\"></div>\n" +
                "                            <span class=\"progress-text\">${curValue}</span>\n" +
                "                        </div>`;\n" +
                "        } else if (type === 'level') {\n" +
                "          const tdColor = getColorLevel(curRatio);\n" +
                "          cell.classList.add(tdColor);\n" +
                "        }\n" +
                "      }\n" +
                "    }\n" +
                "  }\n" +
                "</script>\n" +
                "<script>\n" +
                "</script>\n" +
                "</body>\n" +
                "</html>")
        ;
        System.err.println(jsonObject);
    }
}
