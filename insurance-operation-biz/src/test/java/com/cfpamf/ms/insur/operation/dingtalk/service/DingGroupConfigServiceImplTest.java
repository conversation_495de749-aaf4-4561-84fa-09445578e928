package com.cfpamf.ms.insur.operation.dingtalk.service;

import com.cfpamf.ms.insur.operation.dingtalk.dao.DingGroupConfigMapper;
import com.cfpamf.ms.insur.operation.dingtalk.entity.DingGroupConfig;
import com.cfpamf.ms.insur.operation.dingtalk.form.DingGroupConfigForm;
import com.cfpamf.ms.insur.operation.msg.dao.OpMessageRuleReceiverMapper;
import com.cfpamf.ms.insur.operation.msg.pojo.po.OpMessageRuleReceiver;
import com.github.pagehelper.PageInfo;
import org.apache.ibatis.session.RowBounds;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.*;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class DingGroupConfigServiceImplTest {

    @Mock
    private DingGroupConfigMapper mockDingGroupConfigMapper;
    @Mock
    private OpMessageRuleReceiverMapper mockOpMessageRuleReceiverMapper;

    @InjectMocks
    private DingGroupConfigServiceImpl dingGroupConfigServiceImplUnderTest;

    @Test
    public void testQueryById() {
        // Setup
        final DingGroupConfig expectedResult = new DingGroupConfig();
        expectedResult.setId(0L);
        expectedResult.setGroupId("groupId");
        expectedResult.setGroupName("groupName");
        expectedResult.setRemark("remark");
        expectedResult.setUpdatedTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        // Configure DingGroupConfigMapper.selectByPrimaryKey(...).
        final DingGroupConfig dingGroupConfig = new DingGroupConfig();
        dingGroupConfig.setId(0L);
        dingGroupConfig.setGroupId("groupId");
        dingGroupConfig.setGroupName("groupName");
        dingGroupConfig.setRemark("remark");
        dingGroupConfig.setUpdatedTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        when(mockDingGroupConfigMapper.selectByPrimaryKey(0L)).thenReturn(dingGroupConfig);

        // Run the test
        final DingGroupConfig result = dingGroupConfigServiceImplUnderTest.queryById(0L);

        // Verify the results
        assertThat(result.getGroupId()).isEqualTo(expectedResult.getGroupId());
    }

    @Test
    public void testPaginQuery() {
        // Setup
        final DingGroupConfigForm dingGroupConfigForm = new DingGroupConfigForm();
        dingGroupConfigForm.setPageNo(0);
        dingGroupConfigForm.setPageSize(0);
        dingGroupConfigForm.setId(0L);
        dingGroupConfigForm.setGroupId("groupId");
        dingGroupConfigForm.setGroupName("groupName");

        // Configure DingGroupConfigMapper.queryAllByPage(...).
        final DingGroupConfig dingGroupConfig = new DingGroupConfig();
        dingGroupConfig.setId(0L);
        dingGroupConfig.setGroupId("groupId");
        dingGroupConfig.setGroupName("groupName");
        dingGroupConfig.setRemark("remark");
        dingGroupConfig.setUpdatedTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<DingGroupConfig> dingGroupConfigs = Arrays.asList(dingGroupConfig);
        final DingGroupConfigForm form = new DingGroupConfigForm();
        form.setPageNo(0);
        form.setPageSize(0);
        form.setId(0L);
        form.setGroupId("groupId");
        form.setGroupName("groupName");
        when(mockDingGroupConfigMapper.queryAllByPage(form)).thenReturn(dingGroupConfigs);

        // Run the test
        final PageInfo<DingGroupConfig> result = dingGroupConfigServiceImplUnderTest.paginQuery(dingGroupConfigForm);

        // Verify the results
        assertThat(result.getPageSize()).isGreaterThan(0);
    }

    @Test
    public void testPaginQuery_DingGroupConfigMapperReturnsNoItems() {
        // Setup


        final DingGroupConfigForm form = new DingGroupConfigForm();
        form.setPageNo(0);
        form.setPageSize(0);
        form.setId(0L);
        form.setGroupId("groupId");
        form.setGroupName("groupName");
        when(mockDingGroupConfigMapper.queryAllByPage(form)).thenReturn(Collections.emptyList());

        // Run the test
        final PageInfo<DingGroupConfig> result = dingGroupConfigServiceImplUnderTest.paginQuery(form);

        // Verify the results
        assertThat(result.getSize()).isEqualTo(0);
    }

    @Test
    public void testInsert() {
        // Setup
        final DingGroupConfig dingGroupConfig = new DingGroupConfig();
        dingGroupConfig.setId(0L);
        dingGroupConfig.setGroupId("groupId");
        dingGroupConfig.setGroupName("groupName");
        dingGroupConfig.setRemark("remark");
        dingGroupConfig.setUpdatedTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        final DingGroupConfig expectedResult = new DingGroupConfig();
        expectedResult.setId(0L);
        expectedResult.setGroupId("groupId");
        expectedResult.setGroupName("groupName");
        expectedResult.setRemark("remark");
        expectedResult.setUpdatedTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        when(mockDingGroupConfigMapper.insert(dingGroupConfig)).thenReturn(1);
        // Run the test
        final DingGroupConfig result = dingGroupConfigServiceImplUnderTest.insert(dingGroupConfig);

        // Verify the results
        assertThat(result.getGroupId()).isEqualTo(expectedResult.getGroupId());

    }

    @Test
    public void testUpdate() {
        // Setup
        final DingGroupConfig dingGroupConfig = new DingGroupConfig();
        dingGroupConfig.setId(0L);
        dingGroupConfig.setGroupId("groupId");
        dingGroupConfig.setGroupName("groupName");
        dingGroupConfig.setRemark("remark");
        dingGroupConfig.setUpdatedTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        final DingGroupConfig expectedResult = new DingGroupConfig();
        expectedResult.setId(0L);
        expectedResult.setGroupId("groupId");
        expectedResult.setGroupName("groupName");
        expectedResult.setRemark("remark");
        expectedResult.setUpdatedTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        // Configure DingGroupConfigMapper.selectByPrimaryKey(...).
        final DingGroupConfig dingGroupConfig1 = new DingGroupConfig();
        dingGroupConfig1.setId(0L);
        dingGroupConfig1.setGroupId("groupId");
        dingGroupConfig1.setGroupName("groupName");
        dingGroupConfig1.setRemark("remark");
        dingGroupConfig1.setUpdatedTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        when(mockDingGroupConfigMapper.selectByPrimaryKey(0L)).thenReturn(dingGroupConfig1);

        // Run the test
        final DingGroupConfig result = dingGroupConfigServiceImplUnderTest.update(dingGroupConfig);

        // Verify the results
        assertThat(result.getGroupId()).isEqualTo(expectedResult.getGroupId());

    }

}
