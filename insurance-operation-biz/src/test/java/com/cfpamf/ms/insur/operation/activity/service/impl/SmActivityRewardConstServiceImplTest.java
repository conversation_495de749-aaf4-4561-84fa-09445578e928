package com.cfpamf.ms.insur.operation.activity.service.impl;

import com.cfpamf.ms.insur.operation.activity.dao.SmActivityRewardMapper;
import com.cfpamf.ms.insur.operation.activity.dao.SmCommissionDetailMapper;
import com.cfpamf.ms.insur.operation.activity.dto.ActivityConstRuleParam;
import com.cfpamf.ms.insur.operation.activity.dto.SimpleCommissionDetailOrderDTO;
import com.cfpamf.ms.insur.operation.activity.entity.SmActivityReward;
import com.cfpamf.ms.insur.operation.activity.enums.*;
import com.cfpamf.ms.insur.operation.activity.form.SmActivityCommissionQueryForm;
import com.cfpamf.ms.insur.operation.activity.form.SystemActivityConstRuleForm;
import com.cfpamf.ms.insur.operation.activity.form.SystemActivityConstRuleWrapperForm;
import com.cfpamf.ms.insur.operation.activity.form.SystemActivitySearchForm;
import com.cfpamf.ms.insur.operation.activity.service.SystemActivityProgrammeService;
import com.cfpamf.ms.insur.operation.activity.vo.SystemActivityProgrammeVo;
import com.cfpamf.ms.insur.operation.base.service.SystemGroovyService;
import com.cfpamf.ms.insur.operation.reward.dto.RewardDTO;
import com.cfpamf.ms.insur.operation.reward.service.impl.AddCommissionRewardConstServiceImpl;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class SmActivityRewardConstServiceImplTest {

    @Mock
    private SmActivityRewardMapper mockSmActivityRewardMapper;
    @Mock
    private SystemGroovyService mockSystemGroovyService;
    @Mock
    private SystemActivityProgrammeService mockActivityProgrammeService;
    @Mock
    private SmCommissionDetailMapper mockDetailMapper;
    @Mock
    private AddCommissionRewardConstServiceImpl mockRewardService;

    @Mock
    private SmActivityRewardConstServiceImpl smActivityRewardConstServiceImplUnderTest;

    @Test
    public void testHandleAllConstActivity() {

        // Run the test
        smActivityRewardConstServiceImplUnderTest.handleAllConstActivity();
    }

    @Test
    public void testHandleAllConstActivity_SystemActivityProgrammeServiceReturnsNoItems() {

        // Run the test
        smActivityRewardConstServiceImplUnderTest.handleAllConstActivity();
    }

    @Test
    public void testHandleConstActivityBySaId() {

        // Run the test
        smActivityRewardConstServiceImplUnderTest.handleConstActivityBySaId(0L);
    }

    @Test
    public void testHandleConstActivity() {
        // Setup
        final SystemActivityProgrammeVo constActivity = new SystemActivityProgrammeVo();
        constActivity.setId(0L);
        constActivity.setConfigType(EnumActivityConfigType.GROOVY);
        constActivity.setRegionList(Arrays.asList("value"));
        constActivity.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        constActivity.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final SystemActivityConstRuleWrapperForm constRule = new SystemActivityConstRuleWrapperForm();
        final SystemActivityConstRuleForm systemActivityConstRuleForm = new SystemActivityConstRuleForm();
        systemActivityConstRuleForm.setSaId(0L);
        systemActivityConstRuleForm.setProductIds(Arrays.asList(0));
        systemActivityConstRuleForm.setRuleType(EnumActivityConstRuleType.ORDER_PREMIUM);
        systemActivityConstRuleForm.setJointType(EnumActivityConstJointType.AND);
        constRule.setConstRules(Arrays.asList(systemActivityConstRuleForm));
        constActivity.setConstRule(constRule);

        // Run the test
        smActivityRewardConstServiceImplUnderTest.handleConstActivity(constActivity);
    }

    @Test
    public void testHandleConstActivityOrders() {
        // Setup
        final SystemActivityProgrammeVo constActivity = new SystemActivityProgrammeVo();
        constActivity.setId(0L);
        constActivity.setConfigType(EnumActivityConfigType.GROOVY);
        constActivity.setRegionList(Arrays.asList("value"));
        constActivity.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        constActivity.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final SystemActivityConstRuleWrapperForm constRule = new SystemActivityConstRuleWrapperForm();
        final SystemActivityConstRuleForm systemActivityConstRuleForm = new SystemActivityConstRuleForm();
        systemActivityConstRuleForm.setSaId(0L);
        systemActivityConstRuleForm.setProductIds(Arrays.asList(0));
        systemActivityConstRuleForm.setRuleType(EnumActivityConstRuleType.ORDER_PREMIUM);
        systemActivityConstRuleForm.setJointType(EnumActivityConstJointType.AND);
        constRule.setConstRules(Arrays.asList(systemActivityConstRuleForm));
        constActivity.setConstRule(constRule);

        // Run the test
        smActivityRewardConstServiceImplUnderTest.handleConstActivityOrders(constActivity);

    }

    @Test
    public void testHandleConstActivityOrders_SmCommissionDetailMapperReturnsNoItems() {
        // Setup
        final SystemActivityProgrammeVo constActivity = new SystemActivityProgrammeVo();
        constActivity.setId(0L);
        constActivity.setConfigType(EnumActivityConfigType.GROOVY);
        constActivity.setRegionList(Arrays.asList("value"));
        constActivity.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        constActivity.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final SystemActivityConstRuleWrapperForm constRule = new SystemActivityConstRuleWrapperForm();
        final SystemActivityConstRuleForm systemActivityConstRuleForm = new SystemActivityConstRuleForm();
        systemActivityConstRuleForm.setSaId(0L);
        systemActivityConstRuleForm.setProductIds(Arrays.asList(0));
        systemActivityConstRuleForm.setRuleType(EnumActivityConstRuleType.ORDER_PREMIUM);
        systemActivityConstRuleForm.setJointType(EnumActivityConstJointType.AND);
        constRule.setConstRules(Arrays.asList(systemActivityConstRuleForm));
        constActivity.setConstRule(constRule);

        // Run the test
        smActivityRewardConstServiceImplUnderTest.handleConstActivityOrders(constActivity);

        // Verify the results
    }

    @Test
    public void testHandlerMessageActivity() {
        // Setup
        final SimpleCommissionDetailOrderDTO order = new SimpleCommissionDetailOrderDTO();
        order.setOrderId("orderId");
        order.setProductId(0);
        order.setPaymentTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        order.setRecommendId("recommendId");
        order.setRegionName("regionName");

        final SystemActivityProgrammeVo constActivity = new SystemActivityProgrammeVo();
        constActivity.setId(0L);
        constActivity.setConfigType(EnumActivityConfigType.GROOVY);
        constActivity.setRegionList(Arrays.asList("value"));
        constActivity.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        constActivity.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final SystemActivityConstRuleWrapperForm constRule = new SystemActivityConstRuleWrapperForm();
        final SystemActivityConstRuleForm systemActivityConstRuleForm = new SystemActivityConstRuleForm();
        systemActivityConstRuleForm.setSaId(0L);
        systemActivityConstRuleForm.setProductIds(Arrays.asList(0));
        systemActivityConstRuleForm.setRuleType(EnumActivityConstRuleType.ORDER_PREMIUM);
        systemActivityConstRuleForm.setJointType(EnumActivityConstJointType.AND);
        constRule.setConstRules(Arrays.asList(systemActivityConstRuleForm));
        constActivity.setConstRule(constRule);

        // Run the test
        smActivityRewardConstServiceImplUnderTest.handlerMessageActivity(order, constActivity);
    }

    @Test
    public void testSendReward() {
        // Setup
        final RewardDTO rewardDTO = new RewardDTO();
        rewardDTO.setDataId("dataId");
        rewardDTO.setDataType("dataType");
        rewardDTO.setProportion(new BigDecimal("0.00"));
        rewardDTO.setUuid("f0853011-d1e1-4ee5-8e14-322d90ad52f7");
        rewardDTO.setSystemActivityProductId(0L);
        final List<SmActivityReward> saRewards = Arrays.asList(
                new SmActivityReward(rewardDTO, EnumActivityTriggerType.JOB, RewardType.ADD_COMMISSION, 0L, 0L));
        final SimpleCommissionDetailOrderDTO order = new SimpleCommissionDetailOrderDTO();
        order.setOrderId("orderId");
        order.setProductId(0);
        order.setPaymentTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        order.setRecommendId("recommendId");
        order.setRegionName("regionName");

        // Run the test
        smActivityRewardConstServiceImplUnderTest.sendReward(saRewards, order, 0L);
    }
}
