package com.cfpamf.ms.insur.operation.msg.service.push;

import com.cfpamf.ms.insur.operation.base.service.SystemGroovyService;
import com.cfpamf.ms.insur.operation.base.util.AliYunOssUtil;
import com.cfpamf.ms.insur.operation.msg.dao.OpMessagePushMapper;
import com.cfpamf.ms.insur.operation.msg.dao.OpMessageRuleReceiverMapper;
import com.cfpamf.ms.insur.operation.msg.enums.OpMessageReceiverTypeEnum;
import com.cfpamf.ms.insur.operation.msg.pojo.po.OpMessageRule;
import com.cfpamf.ms.insur.operation.msg.pojo.po.OpMessageRuleReceiver;
import com.cfpamf.ms.insur.operation.msg.service.DingTalkService;
import com.cfpamf.ms.insur.operation.msg.util.Html2ImageUtils;
import com.github.jsonzou.jmockdata.JMockData;
import com.github.jsonzou.jmockdata.TypeReference;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.aop.framework.AopContext;

import java.util.List;

/**
 * <AUTHOR> 2021/8/30 15:46
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({AopContext.class, Html2ImageUtils.class, AliYunOssUtil.class})
@PowerMockIgnore({"javax.management.*", "javax.swing.*"})
public class ImagePusherTest {
    @InjectMocks
    ImagePusher pusher;

    @Mock
    SystemGroovyService groovyService;

    @Mock
    DingTalkService dingTalkService;

    @Mock
    OpMessageRuleReceiverMapper receiverMapper;

    @Mock
    OpMessagePushMapper pushMapper;


    @Before
    public void setUp() throws Exception {
        PowerMockito.mockStatic(AopContext.class);
        PowerMockito.mockStatic(Html2ImageUtils.class);
        PowerMockito.mockStatic(AliYunOssUtil.class);
        PowerMockito.when(AopContext.currentProxy()).thenReturn(pusher);
        Mockito.when(groovyService.executeForCode(Mockito.anyString(),
                        Mockito.anyString(), Mockito.any()))
                .thenReturn("<span>1234</span>");
    }

    @Test
    public void push() {
        List<OpMessageRuleReceiver> mock = JMockData.mock(new TypeReference<List<OpMessageRuleReceiver>>() {
        });
        mock.forEach(re -> {
            re.setReceiverType(OpMessageReceiverTypeEnum.CHAT.getCode());
            re.setParams("{\"imgParam\":{\"width\":1000}}");
        });
        pusher.push(JMockData.mock(OpMessageRule.class),
                "conte-id", true, mock);
    }
}
