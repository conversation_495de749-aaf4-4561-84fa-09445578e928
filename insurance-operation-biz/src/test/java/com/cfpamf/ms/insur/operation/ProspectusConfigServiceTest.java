package com.cfpamf.ms.insur.operation;

import com.cfpamf.ms.insur.operation.base.helper.WxCheckAuthorityHelper;
import com.cfpamf.ms.insur.operation.prospectus.dao.ProductMapper;
import com.cfpamf.ms.insur.operation.prospectus.dao.ProspectusConfigMapper;
import com.cfpamf.ms.insur.operation.prospectus.dao.ProspectusMapper;
import com.cfpamf.ms.insur.operation.prospectus.entity.ProspectusConfig;
import com.cfpamf.ms.insur.operation.prospectus.entity.SmProduct;
import com.cfpamf.ms.insur.operation.prospectus.service.impl.ProspectusConfigServiceImpl;
import com.cfpamf.ms.insur.operation.prospectus.vo.ProspectusConfigVo;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

/**
 * <AUTHOR>
 * @date 2021/6/30 16:53
 */
public class ProspectusConfigServiceTest {

    @InjectMocks
    ProspectusConfigServiceImpl prospectusConfigService;
    @Mock
    private ProspectusConfigMapper prospectusConfigMapper;
    @Mock
    private ProspectusMapper prospectusMapper;
    @Mock
    private ProductMapper productMapper;
    @Mock
    private WxCheckAuthorityHelper wxCheckAuthorityHelper;

    @Before
    public void before() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 查看计划书配置详情-无异常测试
     */
    @Test
    public void prospectusConfigServiceGetByIdTest() {
        ProspectusConfig prospectusConfig = new ProspectusConfig();
        prospectusConfig.setName("OperationActivityCalculationRule");
        prospectusConfig.setId(1L);
        prospectusConfig.setProductId(1L);

        SmProduct smProduct = new SmProduct();
        smProduct.setId(1L);
        smProduct.setProductName("OperationActivityCalculationRule");

        Mockito.when(prospectusConfigMapper.getById(1L)).thenReturn(prospectusConfig);
        Mockito.when(productMapper.getById(1L)).thenReturn(smProduct);


        ProspectusConfigVo prospectusConfigVo = prospectusConfigService.getById(1L);

        Assert.assertEquals(prospectusConfigVo.getName(), "OperationActivityCalculationRule");
        Assert.assertEquals(prospectusConfigVo.getProductName(), "OperationActivityCalculationRule");
    }
}
