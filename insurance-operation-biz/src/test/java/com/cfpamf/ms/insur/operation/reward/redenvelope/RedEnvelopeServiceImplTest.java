package com.cfpamf.ms.insur.operation.reward.redenvelope;

import com.cfpamf.common.ms.exception.MSBizNormalException;
import com.cfpamf.ms.insur.operation.activity.dao.SystemActivityProgrammeMapper;
import com.cfpamf.ms.insur.operation.activity.entity.SystemActivityProgramme;
import com.cfpamf.ms.insur.operation.base.constant.BusinessConstants;
import com.cfpamf.ms.insur.operation.base.helper.BmsHelper;
import com.cfpamf.ms.insur.operation.base.service.RedisLockTemplate;
import com.cfpamf.ms.insur.operation.reward.redenvelope.dao.SystemRedEnvelopeMapper;
import com.cfpamf.ms.insur.operation.reward.redenvelope.helper.RedEnvelopePool;
import com.cfpamf.ms.insur.operation.reward.redenvelope.po.entity.SystemRedEnvelope;
import com.cfpamf.ms.insur.operation.reward.redenvelope.po.vo.RedEnvelopeVo;
import com.cfpamf.ms.insur.operation.reward.redenvelope.service.impl.RedEnvelopeServiceImpl;
import org.assertj.core.util.Lists;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/1/12 13:07
 */
@RunWith(SpringJUnit4ClassRunner.class)
public class RedEnvelopeServiceImplTest {
    @Mock
    SystemRedEnvelopeMapper systemRedEnvelopeMapper;
    @Mock
    RedisLockTemplate redisLockTemplate;
    @Mock
    SystemActivityProgrammeMapper systemActivityProgrammeMapper;

    private Long redEnvelopeGracePeriodDay = 10L;
    @Mock
    RedEnvelopePool redEnvelopePool;
    @Mock
    BmsHelper bmsHelper;
    @InjectMocks
    RedEnvelopeServiceImpl redEnvelopeService;

    @Test
    public void  getRedEnvelopeTest(){
        Mockito.when(bmsHelper.getValidDictionaryItemCodesByTypeCode(BusinessConstants.BMS_DICTIONARY_RED_ENVELOPE_SA_ID)).thenReturn(Lists.newArrayList("1"));
        SystemRedEnvelope systemRedEnvelope = new SystemRedEnvelope();
        systemRedEnvelope.setDataId("dataId");
        Mockito.when(systemRedEnvelopeMapper.getBySaIdAndUserId(1L, "userId")).thenReturn(Lists.newArrayList(systemRedEnvelope));
        List<RedEnvelopeVo> envelopeVos = redEnvelopeService.getRedEnvelope(1L, "userId");
        Assert.assertEquals(envelopeVos.get(0).getDataId(),"dataId");
    }

    @Test
    public void  getRedEnvelopeNullListTest(){
        Mockito.when(bmsHelper.getValidDictionaryItemCodesByTypeCode(BusinessConstants.BMS_DICTIONARY_RED_ENVELOPE_SA_ID)).thenReturn(Lists.newArrayList());
        SystemRedEnvelope systemRedEnvelope = new SystemRedEnvelope();
        systemRedEnvelope.setDataId("dataId");
        Mockito.when(systemRedEnvelopeMapper.getBySaIdAndUserId(1L, "userId")).thenReturn(Lists.newArrayList(systemRedEnvelope));
        List<RedEnvelopeVo> envelopeVos = redEnvelopeService.getRedEnvelope(1L, "userId");
        Assert.assertEquals(envelopeVos.size(),1L);
    }

    @Test
    public void  detailNullTest(){
        SystemRedEnvelope systemRedEnvelope = new SystemRedEnvelope();
        systemRedEnvelope.setDataId("dataId");
        systemRedEnvelope.setDataId("dataId");
        Mockito.when( systemRedEnvelopeMapper.selectByPrimaryKey(1L)).thenReturn(systemRedEnvelope);
        RedEnvelopeVo redEnvelopeVo = redEnvelopeService.detail(1L, "userId");
        Assert.assertNull(redEnvelopeVo);
    }

    @Test
    public void  detailTest(){
        SystemRedEnvelope systemRedEnvelope = new SystemRedEnvelope();
        systemRedEnvelope.setDataId("dataId");
        systemRedEnvelope.setUserId("userId");
        Mockito.when( systemRedEnvelopeMapper.selectByPrimaryKey(1L)).thenReturn(systemRedEnvelope);
        RedEnvelopeVo redEnvelopeVo = redEnvelopeService.detail(1L, "userId");
        Assert.assertEquals(redEnvelopeVo.getDataId(),"dataId");
    }

    @Test
    public void  openRedEnvelopTest(){
        SystemRedEnvelope systemRedEnvelope = new SystemRedEnvelope();
        systemRedEnvelope.setDataId("dataId");
        systemRedEnvelope.setUserId("userId");
        systemRedEnvelope.setSaId(2L);
        Mockito.when( systemRedEnvelopeMapper.selectByPrimaryKey(1L)).thenReturn(systemRedEnvelope);
        SystemActivityProgramme systemActivityProgramme = new SystemActivityProgramme();
        systemActivityProgramme.setEndTime(LocalDateTime.now());
        Mockito.when( systemActivityProgrammeMapper.selectByPrimaryKey(2L)).thenReturn(systemActivityProgramme);


    }

}
