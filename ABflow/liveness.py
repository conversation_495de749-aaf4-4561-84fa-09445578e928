#!/usr/bin/env python3
# coding=utf-8
# Author: ZhangQin
# Time: 2021/4/30 15:11
import sys
import requests
import json
class CustomError(Exception):
    def __init__(self,ErrorInfo):
        super().__init__(self)
        self.errorinfo=ErrorInfo
    def __str__(self):
        return self.errorinfo

def main(argv):
    url = "http://pangu.cdfinance.com.cn/api/k8s/k8straffic/"
    payload = json.dumps({
      "namespace": argv['namespace'],
      "service": argv['service'],
      "env": argv['env']
    })
    headers = {
      'Authorization': 'Token %s' % argv['token'],
      'Content-Type': 'application/json'
    }
    response = requests.request("POST", url, headers=headers, data=payload, timeout=600)
    if response.status_code == 200:
        return response.status_code
    else:
        raise CustomError(response.status_code)

if __name__ == "__main__":
   main(dict([arg.split('=', maxsplit=1) for arg in sys.argv[1:]]))