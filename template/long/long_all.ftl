<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>长期险24年应收未缴订单跟进情况</title>
    <style>
        * {
            margin: 0;
            padding: 0;
        }

        #root {
            width: 1125px;
            background-color: #2F68FE;
        }

        header {
            position: relative;
            width: 100%;
            height: 203px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }

        .title {
            text-align: center;
            font-size: 46px;
            color: #ffffff;
            font-weight: bold;
        }

        .header-bg {
            position: absolute;
            top: 0;
            right: 0;
            width: 828px;
            height: 203px;
        }

        .push {
            position: relative;
            background-color: #ffffff;
            padding: 20px;
            border-radius: 30px 30px 0 0;
            box-sizing: border-box;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            border-spacing: 0;
            table-layout: fixed;
            border-radius: 30px 30px 0 0;
            background: #2F68FE;
            max-width: none;
            min-width: 100%;
        }

        table tr {
            width: 100%;
        }

        table tr>th {
            color: #ffffff;
            text-align: center;
        }

        table tr>td {
            text-align: center;
            font-weight: bold;
            word-wrap: break-word;
            background-color: rgb(235, 240, 254);
            color: #333333;
        }
        tbody tr:nth-child(even) td{
            background-color: rgb(245, 245, 245);
        }
        tbody tr:last-child td{
            color: #E8380D;
            background-color: rgb(250, 236, 232);
        }

        table tr>td,
        th {
            padding: 8px 3px;
            border: 3px solid #ffffff;
            font-size: 22px;
        }

        .footer {
            width: 1116px;
            height: 22px;
            margin: 0 auto;
        }
        .blue1{
            background: rgb(65, 129, 250);
        }
        .blue2{
            background: rgb(88, 143, 251);
        }
        .blue3{
            background: rgb(111, 158, 251);
        }
        .blue4{
            background: rgb(134, 173, 252);
        }
    </style>
</head>


<body>
<div id="root">
    <header>
        <img class="header-bg" src="https://safesfiles.oss-cn-beijing.aliyuncs.com/safes/ZHNX33656/long_ins_202401311023.png" >
        <div class="title">
            <div>长期险24年应收未缴订单跟进情况</div>
            <span>（截止${pt!}）</span>
        </div>
    </header>
    <div class="push">
        <table>
            <thead>
            <tr>
                <th rowspan="2">区域</th>
                <th rowspan="2">未收保单数</th>
                <th rowspan="2">当年未收保费</th>
                <th colspan="4">跟进情况</th>
            </tr>
            <tr>
                <th class="blue1">愿意续期</th>
                <th class="blue2">意愿不明</th>
                <th class="blue3">不愿续期</th>
                <th class="blue4">未跟进</th>
            </tr>
            </thead>
            <tbody id="table-body">

            </tbody>
        </table>
    </div>

    <img class="footer" src="https://safesfiles.oss-cn-beijing.aliyuncs.com/safes/ZHNX33656/longIns_footer_202401311023.png" >
</div>
</body>

<script>
    // 服务端数据
    const datas = ${datasJson}

    <#noparse>
    // 替换字段正则
    const reg = new RegExp("\\[([^\\[\\]]*?)\\]", 'gm')
    const template = `
    <tr>
      <td>[area_name]</td>
      <td>[un_cts]</td>
      <td>[un_amt]</td>
      <td>[follow_1]</td>
      <td>[follow_3]</td>
      <td>[follow_2]</td>
      <td>[follow_null]</td>
    </tr>
`;

    let replaceHtmlString = ''
    datas.forEach(row => replaceHtmlString += template.replace(reg, function (node, key) { return row[key]; }))

    document.querySelector('#table-body').innerHTML = replaceHtmlString
    </#noparse>
</script>

</html>