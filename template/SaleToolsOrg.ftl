<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <title>新榜有我之机构榜</title>
    <style type="text/css">
        body {
            background-color: #db0d0d;
            margin: 0;
            width: 750px;
        }

        .h1{
            color:white;
            display: block;
            font-size: 23px;
            font-weight: bold;
            /*margin-top: 30px;*/
            text-align: center;
            margin-left: 30px;
        }

        .p-top{
            margin-top: 10px;
            margin-bottom: 10px;
            color:white;
        }


        p{
            margin-top: 5px;
            margin-bottom: 5px;
        }

        .box {
            padding: 0 30px 30px 30px;
            text-align: center;
        }

        .table {
            font-size: 16px;
            margin-bottom: 30px;
            font-family: "Microsoft YaHei";
            width: 100%;
            background-color: rgb(229,186,163) !important;
            /*border-radius: 100px;*/
        }

        .tr {
            width: 100%;
        }

        .th {
            /*background-color: white;*/
            height: 30px;
            /*border: 1px solid #e6e6e6;*/
            border-top: none;
            text-align: center;
            color: black;
            padding-left: 15px;
            padding-bottom: 5px;
            font-size: 20px;
        }

        .th_bottom {
            margin: 10px;
            text-align: left;
            padding-left: 70px;
            padding-bottom: 20px;
            color: black;
            font-size: 20px;
        }

        .bottom-text {
            font-size: 16px;
            margin-top: 20px;
            width: 100%;
        }

        .no-data {
            color: #606266;
            padding: 25px;
            background-color: #ffffff;
            text-align: center;
        }
        .self{
            color: #382fb7;
            font-weight: bold;
        }
    </style>
</head>
<body>
<div style="width: 750px">
    <img width="750px" src="https://safesfiles.oss-cn-beijing.aliyuncs.com/safes/ZHNX30835/%E6%96%B0%E6%A6%9C%E6%9C%89%E6%88%91%E4%B9%8B%E6%9C%BA%E6%9E%84%E6%A6%9C%EF%BC%88db0d0d%EF%BC%89_202109101353.png"/>
    <div class="box">
        <#--        <p class="h1">新榜有我之机构榜</p>-->
        <#--        <p class="h1" style="margin-top: 30px;">产品：${productName}</p>-->
        <#--        <p class="h1">统计时间：${dateRange}</p>-->
        <p class="h1">${productName}产品、统计时间：${dateRange}</p>
        <div>
            <table class="table" cellpadding="0" cellspacing="0">
                <tr>
                    <th class="th" colspan="5" style="font-size: 22px;padding: 12px">
                    </th>
                </tr>
                <tr class="tr">
                    <th class="th">排名</th>
                    <th class="th">区域</th>
                    <th class="th">机构</th>
                    <th class="th">人均业绩/元</th>
                    <th class="th">员工参与度</th>
                </tr>

                <#list topKList as data>
                    <tr class="tr">
                        <td <#if data?counter == currentPos + 1 > style="color:#382f7b;font-weight: bold" </#if> class="th">${data?counter}</td>
                        <td <#if data?counter == currentPos + 1 > style="color:#382f7b;font-weight: bold" </#if> class="th">${data.regionName!}</td>
                        <td <#if data?counter == currentPos + 1 > style="color:#382f7b;font-weight: bold" </#if> class="th">${data.organizationName!}</td>
                        <td <#if data?counter == currentPos + 1 > style="color:#382f7b;font-weight: bold" </#if> class="th">${data.averageAmts!?round}</td>
                        <th <#if data?counter == currentPos + 1 > style="color:#382f7b;font-weight: bold" </#if> class="th">${data.partDegree!?string("0.00%")}</th>
                    </tr>
                <#else>
                    <!-- 空状态 -->
                    <tr class="tr">
                        <td colspan="5">
                            <div class="no-data">
                                <img src="https://oms-2018.oss-cn-hangzhou.aliyuncs.com/template/no-data.png"/>
                                <div class="mt-10">暂无数据</div>
                            </div>
                        </td>
                    </tr>
                </#list>

                <#if showDiscardBefore>
                    <th colspan="5" style="color: black;"> ···································· </th>
                </#if>

                <#list discardRangeList as data>
                    <tr class="tr">
                        <td <#if startPos + data?counter == currentPos + 1 > style="color:#382f7b;font-weight: bold" </#if> class="th">${startPos + data?counter}</td>
                        <td <#if startPos + data?counter == currentPos + 1 > style="color:#382f7b;font-weight: bold" </#if> class="th">${data.regionName!}</td>
                        <td <#if startPos + data?counter == currentPos + 1 > style="color:#382f7b;font-weight: bold" </#if> class="th">${data.organizationName!}</td>
                        <td <#if startPos + data?counter == currentPos + 1 > style="color:#382f7b;font-weight: bold" </#if> class="th">${data.averageAmts?round}</td>
                        <th <#if startPos + data?counter == currentPos + 1 > style="color:#382f7b;font-weight: bold" </#if> class="th">${data.partDegree!?string("0.00%")}</th>
                    </tr>
                </#list>
                <#if showDiscardAfter>
                    <th colspan="5" style="color: black;"> ···································· </th>
                </#if>
                <tr>
                    <th class="th_bottom" colspan="5" >
                        <p>总结分析：</p>
                        <p>今日排名较昨天<#if changeRank! lt 0 >下降<#else>上升</#if>${changeRank!?abs}名次；</p>
                        <p>今日数据距离第${showRankText}获奖还差${balance!}元；</p>
                    </th>
                </tr>
            </table>
        </div>

    </div>
</div>
</body>
</html>
