<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <title>测试</title>
    <style type="text/css">
        body {
            background-color: #ea4621;
            margin: 0px;
            width: 750px;
        }

        .box {
            padding: 0px 30px 30px 30px;
        }

        .title {
            font-size: 22px;
            padding: 8px
        }

        .table-box {
            border-radius: 10px;
            overflow: hidden;
        }

        .table {
            width: 100%;
            font-size: 16px;
            margin-bottom: 30px;
            font-family: "Microsoft YaHei";
        }

        .th {
            background-color: #ffffff;
            height: 30px;
            border: 1px solid #e6e6e6;
            border-top: none;
            text-align: center;
            color: #333333;
        }

        .bottom-text {
            font-size: 16px;
            margin-top: 20px;
        }

        .no-data {
            color: #606266;
            padding: 25px;
            background-color: #ffffff;
            text-align: center;
        }

        .mt-10 {
            margin-top: 10px;
        }

        .hint-top {
            position: absolute;
            top: 10px;
            right: 10px;
            font-size: 16px;
        }
    </style>
</head>
<body>
<div style="position: relative">
    <img src="https://safesfiles-test.oss-cn-beijing.aliyuncs.com/safes/ZHNX08073/sfjs_202107261730.png" width="100%"/>
    <div class="box">
        <table class="table" cellpadding="0" cellspacing="0">
            <tr>
                <th class="th" colspan="6" style="font-size: 22px;padding: 12px">
                    顺福金生达成奖预获奖数据展示(数据截止至${now})
                </th>
            </tr>
            <tr class="tr">
                <th class="th">序号</th>
                <th class="th">区域</th>
                <th class="th">机构</th>
                <th class="th">姓名</th>
                <th class="th">业绩/元</th>
                <th class="th">入围十万达成奖</th>
            </tr>
            <#list datas as data>
                <tr class="tr">
                    <td class="th">${data?counter}</td>
                    <td class="th">${data.regionName!}</td>
                    <td class="th">${data.orgName!}</td>
                    <td class="th">${data.userName!}</td>
                    <td class="th">${data.amts!}</td>
                    <#if data.exp gt 0>
                        <td class="th">-${data.exp!}</td></#if>
                    <#if data.exp == 0>
                        <th class="th" style="color: red;">达成</th>
                    </#if>
                </tr>
            <#else>
                <!-- 空状态 -->
                <tr class="tr">
                    <td colspan="5">
                        <div class="no-data">
                            <img src="https://oms-2018.oss-cn-hangzhou.aliyuncs.com/template/no-data.png"/>
                            <div class="mt-10">暂无数据</div>
                        </div>
                    </td>
                </tr>
            </#list>
        </table>
        <div class="bottom-text">
            备注：1、精英奖与达成奖不重复得奖；<br/>
            2、以上内容以活动产品推广业绩为基数，不同缴费年限推广业绩均不折算，按首年保费计入活动方案。并以核销回执后15天为准。   </div>
    </div>
</div>
</body>
</html>
