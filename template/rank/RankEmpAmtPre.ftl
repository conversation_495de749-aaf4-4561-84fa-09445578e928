<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>个人风云榜-业绩预获榜</title>
    <style>
        * {
            margin: 0;
            padding: 0;
        }

        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
        }

        .copywriting {
            padding: 20px;
            font-size: 28px;
            background-color: #fff;
        }

        .poster {
            position: relative;
            max-width: 750px;
            margin: 0 auto;
            background-color: #af1e1c;
            overflow: hidden;
        }

        .poster img {
            width: 100%;
            height: auto;
        }

        .poster-month {
            position: absolute;
            top: 110px;
            right: 96px;
            width: 80px;
            text-align: center;
            font-size: 64px;
            font-family: math;
            color: #f6d993;
        }


        .poster-bottom {
            text-align: center;
            color: #f6d993;
            font-size: 30px;
        }

        .poster-info,
        .poster-bottom {
            text-align: center;
            color: #e2c06d;
            font-size: 28px;
        }

        .poster-bottom {
            margin: 10px 0 30px;
        }
        .poster-center{
            margin: 20px 40px 10px;
        }

        .poster-organization {
            margin: 10px 40px;
            background-color: #9f1515;
            border-radius: 16px;
            overflow: hidden;
        }

        .poster-organization table {
            width: 100%;
            border-collapse: collapse;
            color: #eeeeeef0;
        }


        .poster-organization table thead th{
            padding: 6px 0;
            font-size: 30px;
            background: linear-gradient(to bottom,#ead0a4,#dfb267,#ead0a4); /* 表头背景色 */
            color: #af1e1c;
        }
        table tbody tr{
            border-bottom: 1px solid #eeeeee42;
        }

        .poster-organization table tbody td {
            padding: 6px 0;
            font-size: 26px;
            text-align: center;
        }

        .high-light {
            color: #dd3737;
            font-weight: bolder;
        }

        .title-bold {
            color: green;
            font-weight: bolder;
        }

        .ml-20 {
            margin-left: 40px;
        }

        .bold {
            font-weight: bolder;
        }

        .emoji-img {
            width: 44px;
            height: 44px;
            margin-right: 6px;
            vertical-align: sub;
        }

        .poster-info {
            width: 100%;
            margin-bottom: 16px;
        }

        footer{
            padding: 0 20px 20px;
            background-color: #fff;
            font-size: 28px;
        }

        .ml-15 {
            margin-left: 30px;
        }

    </style>
</head>

<body>
<div class="poster">
    <div class="poster-header">
        <img src="https://safesfiles.oss-cn-beijing.aliyuncs.com/safes/ZHNX09760/absdsfdg.png" alt="个人风云榜-预获榜">
    </div>
    <div class="poster-month" id="month-2"></div>
    <div class="poster-info">
        <span>统计口径：当月标准业绩/元</span>
        <span class="ml-15">统计时间：<span id="month-3"></span></span>
    </div>

    <div class="poster-organization" id="organization-rank">
        <table border="0">
            <thead>
            <tr>
                <th>序号</th>
                <th>区域</th>
                <th>分支</th>
                <th>姓名</th>
                <th>标准业绩</th>
            </tr>
            </thead>
            <tbody id="table-tbody">

            </tbody>
        </table>
    </div>

    <div class="poster-bottom">
        <span>再/接/再/厉</span>
        <span class="ml-20">再/创/佳/绩</span>
    </div>
</div>

<div class="copywriting">
    <div class="copywriting-header">
        <p>
            <img src="https://safesfiles.oss-cn-beijing.aliyuncs.com/safes/ZHNX33656/火_202309192057.png" class="emoji-img">
            <span class="bold">超越自我，追求卓越！</span>令人期待的优秀个人来啦!
        </p>
        <p>
            <img src="https://safesfiles.oss-cn-beijing.aliyuncs.com/safes/ZHNX33656/火_202309192057.png" class="emoji-img">
            <span class="high-light">B类业务<span class="title-bold"><span id="month-1"></span>月个人风云榜</span>预获榜单</span>出炉啦!
        </p>
        <p>
            <img src="https://safesfiles.oss-cn-beijing.aliyuncs.com/safes/ZHNX33656/火_202309192057.png" class="emoji-img">
            精彩战绩奉上！群雄涿鹿，奋再向前！
        </p>
        <p>
            <img src="https://safesfiles.oss-cn-beijing.aliyuncs.com/safes/ZHNX33656/努力_202309192033.png" class="emoji-img">一起来看看吧~
        </p>
    </div>
</div>

<footer>
    未入榜的伙伴千万不要气馁！加油冲刺！！
    <img src="https://safesfiles.oss-cn-beijing.aliyuncs.com/safes/ZHNX33656/努力_202309192033.png" class="emoji-img">
</footer>
</body>

<script>
    const doms = {
        areaDom : document.getElementById('table-tbody'),
        month1Dom:document.getElementById('month-1'),
        month2Dom:document.getElementById('month-2'),
        month3Dom:document.getElementById('month-3'),
        copywritingDom:document.getElementById('copywriting-rank')
    }

    const data = ${datasJson}

    const { datas,maxAreaName,yearMonth,areaNames,maxAreaPre } = data

    <#noparse>
    // 月份
    // 月份
    const pt =  datas[0].pt
    doms.month1Dom.innerHTML = yearMonth.monthValue
    doms.month2Dom.innerHTML = yearMonth.monthValue
    doms.month3Dom.innerHTML =`${pt.substr(4,2)}月${pt.substr(6)}日`


    // 填充列表模块
    doms.areaDom.innerHTML = datas && datas.map((item,index) => {
        return `
        <tr>
          <td>${index+1}</td>
          <td>${formatText(item.area_name)}</td>
          <td>${formatText(item.bch_name)}</td>
          <td>${formatText(item.emp_name)}</td>
          <td>${formatText(item.rank_value)}万</td>
        </tr>
    `
    }).join("")

    function formatText(text){
        return text == null ? '-' : text
    }
    </#noparse>
</script>

</html>