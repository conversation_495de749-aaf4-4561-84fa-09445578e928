<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>区域风云榜-月度评选</title>
    <style>
        * {
            margin: 0;
            padding: 0;
        }

        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
        }

        .copywriting{
            padding: 20px 6px 10px;
            font-size: 15px;
            background-color: #fff;
        }
        .copywriting-header p{
            display: flex;
            align-items: center;
        }
        #listed-area p {
            display: flex;
            flex-wrap: wrap;
            align-items: center;
        }
        .poster {
            position: relative;
            max-width: 375px;
            margin: 0 auto;
            background: url('https://safesfiles.oss-cn-beijing.aliyuncs.com/safes/ZHNX33656/月区域风云榜-背景_202309200941.jpeg') no-repeat;
            background-position: center;
            background-size: cover;
            overflow: hidden;
        }

        .poster img {
            width: 100%;
            height: auto;
        }

        .poster-month {
            position: absolute;
            top: 58px;
            right: 60px;
            font-size: 35px;
            font-family: math;
            color: #f6d993;
        }

        .poster-info,
        .poster-bottom {
            text-align: center;
            color: #f6d993;
            font-size: 15px;
        }

        .poster-info {
            width: 100%;
            margin-top: 5px;
        }

        .poster-bottom {
            margin-bottom: 10px;
        }

        .poster-area {
            width: 100%;
        }


        .poster-area-item {
            margin: 20px 35px 30px;
            padding: 6px;
            background: #b31616;
            border-radius: 30px 8px 8px 30px;
            font-size: 15px;
        }

        .area-item-data {
            position: relative;
            padding: 11px 5px;
            border: 1px solid #f6d993;
            border-radius: 8px;
            color: #f9f6f6;
        }

        .area-item-data__left {
            position: absolute;
            top: -13px;
            left: -15px;
            width: 105px;
            height: 105px;
            line-height: 105px;
            text-align: center;
            background: url('https://safesfiles.oss-cn-beijing.aliyuncs.com/safes/ZHNX33656/月区域风云榜1_202309191416.png') no-repeat;
            background-size: contain;
            font-size: 24px;
            color: #b31616;
            font-weight: bolder;
        }

        .area-item-data__right {
            margin-left: 85px;
            text-align: center;
        }

        .larger-text {
            font-size: 20px;
        }

        .high-light {
            color: #dd3737;
            font-weight: bolder;
        }

        .title-bold {
            color: green;
            font-weight: bolder;
        }

        .premium-text {
            font-size: 18px;
            color: green;
        }

        .ml-6 {
            margin-left: 6px;
        }

        .ml-20 {
            margin-left: 20px;
        }

        .bold {
            font-weight: bolder;
        }
        .mt-5{
            margin-top: 5px;
        }
        .emoji-img{
            width: 22px;
            height: 22px;
            margin-right: 3px;
        }
    </style>
</head>

<body>
<div class="copywriting">
    <div class="copywriting-header ml-6">
        <p><img src="https://safesfiles.oss-cn-beijing.aliyuncs.com/safes/ZHNX33656/烟花 (1)_202309191558.png" class="emoji-img">统一思想、明确思路、大步前行！</p>
        <p><img src="https://safesfiles.oss-cn-beijing.aliyuncs.com/safes/ZHNX33656/燃脂_202309191527.png" class="emoji-img"><span class="high-light">B类业务${month}月风云榜荣誉榜单（团队版）</span>新鲜出炉!</p>
        <p class="title-bold"><img src="https://safesfiles.oss-cn-beijing.aliyuncs.com/safes/ZHNX33656/向右_202309191527.png" class="emoji-img">区域风云榜：</p>
    </div>
    <div id="listed-area">

    </div>
</div>
<div class="poster">
    <div class="poster-header">
        <img src="https://safesfiles.oss-cn-beijing.aliyuncs.com/safes/ZHNX33656/月区域风云榜_202309201007.png" alt="">
    </div>
    <div class="poster-month">${month}</div>
    <div class="poster-info">
        <span>统计口径：当月人均标准业绩/元</span>
        <span class="ml-20">统计时间：${month}月</span>
    </div>
    <div class="poster-area" id="area-list">

    </div>
    <div class="poster-bottom">
        <span>再/接/再/厉</span>
        <span class="ml-20">再/创/佳/绩</span>
    </div>
</div>
</body>

<script>

    const areaDom = document.getElementById('area-list')
    const areaListedDom = document.getElementById('listed-area')

    // actualPerformance：实际业绩，rank 排名，listedNumber 上榜次数
    const areaData = ${datasJson}

    <#noparse>
    // 填充区域文案部分
    areaListedDom.innerHTML = areaData && areaData.map(item=>{
        const areaName = `【${item.area_name}】`
        const dianzanImg = `<img src="https://safesfiles.oss-cn-beijing.aliyuncs.com/safes/ZHNX33656/点赞_202309191527.png" class="emoji-img">`
        // 排名第一区域
        if(item.rank === 1){
            // 是否多次上榜
            const message = item.qualifyTimes > 1 ? `榜首之位无可撼动！<span class="bold">当年${item.qualifyTimes}个月入榜！</span>怎一个牛字了得！${dianzanImg}` : `<span class="bold">首次入榜</span>位列第一！${dianzanImg}`
            return `
      <p>
          <span class="high-light">${areaName}</span>月人均标保突破<span class="premium-text">${item.sm_norm_insurance_amt_avg}</span>元，
            ${message}
        </p>
      `
        }else{
            // 2,3 名多次上榜
            const message = item.qualifyTimes > 1 ? `位列第${item.rank}，同时今年${item.qualifyTimes}个月上榜！` : `竿头日上，<span class="bold">首次入榜</span>位列第${item.rank}！`
            return `<p><span class="high-light">${areaName}</span>${message}${dianzanImg}</p>`
        }
    }).join("")

    // 填充区域列表模块
    areaDom.innerHTML = areaData && areaData.map(item => {
        const areaName =item.area_name && item.area_name.slice(0,-2)
        return `
    <div class="poster-area-item">
          <div class="area-item-data">
            <div class="area-item-data__left">${areaName}</div>
            <div class="area-item-data__right">
              <p>人均标准业绩 <span class="larger-text">${item.sm_norm_insurance_amt_avg}元</span> </p>
              <p class="mt-5">实收业绩：${item.sm_norm_insurance_amt}万</p>
            </div>
          </div>
        </div>
    `
    }).join("")
    </#noparse>
</script>

</html>