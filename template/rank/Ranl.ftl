<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=5.0">
  <title>分支风云榜-业绩预获榜</title>
  <style>
    * {
      margin: 0;
      padding: 0;
    }

    body {
      font-family: Arial, sans-serif;
      margin: 0;
      padding: 0;
    }

    .copywriting {
      padding: 20px;
      font-size: 28px;
      background-color: #fff;
    }

    .poste r {
      position: relative;
      max-width: 750px;
      margin: 0 auto;
      background-color: #af1e1c;
      overflow: hidden;
    }

    .poster img {
      width: 100%;
      height: auto;
    }

    .poster-month {
      position: absolute;
      top: 80px;
      right: 116px;
      width: 72px;
      text-align: center;
      font-size: 60px;
      font-family: math;
      color: #f6d993;
    }
    .poster-statistics-time{
      position: absolute;
      top: 396px;
      right: 100px;
      font-size: 30px;
      font-family: math;
      color: #f6d993;
    }

    .poster-info,
    .poster-bottom {
      text-align: center;
      color: #f6d993;
      font-size: 28px;
    }

    .poster-info {
      width: 100%;
      margin-bottom: 16px;
    }

    .poster-bottom {
      margin: 10px 0 30px;
    }
    .poster-center{
      margin: 20px 40px 10px;
    }

    .poster-organization {
      margin: 10px 40px;
      background-color: #9f1515;
      border-radius: 16px;
      overflow: hidden;
    }

    .poster-organization table {
      width: 100%;
      border-collapse: collapse;
      color: #eeeeeef0;
    }


    .poster-organization table thead th{
      padding: 6px 0;
      font-size: 30px;
      background: linear-gradient(to bottom,#ead0a4,#dfb267,#ead0a4); /* 表头背景色 */
      color: #af1e1c;
    }
    table tbody tr{
      border-bottom: 1px solid #eeeeee42;
    }

    .poster-organization table tbody td {
      padding: 6px 0;
      font-size: 26px;
      text-align: center;
    }

    .high-light {
      color: #dd3737;
      font-weight: bolder;
    }

    .title-bold {
      color: green;
      font-weight: bolder;
    }

    .ml-6 {
      margin-left: 12px;
    }

    .ml-15 {
      margin-left: 30px;
    }

    .bold {
      font-weight: bolder;
    }

    .mt-5 {
      margin-top: 10px;
    }

    .emoji-img {
      width: 44px;
      height: 44px;
      margin-right: 2px;
      vertical-align: sub;
    }

  </style>
</head>

<body>
  <div class="poster">
    <div class="poster-header">
      <img src="https://safesfiles.oss-cn-beijing.aliyuncs.com/safes/ZHNX33656/分支风云榜服务榜预获_202309251617.png" alt="">
    </div>
    <div class="poster-month" id="month-2"></div>
    <div class="poster-info">
      <span>统计口径：当月小程序注册客户数/个</span>
      <span class="ml-15">统计时间：<span id="month-3"></span>月</span>
    </div>

    <div class="poster-organization" id="organization-rank">
      <table border="0">
        <thead>
          <tr>
            <th>序号</th>
            <th>区域</th>
            <th>分支</th>
            <th>当月小程序客户认证数</th>
          </tr>
        </thead>
        <tbody id="table-tbody">

        </tbody>
      </table>
    </div>

    <div class="poster-bottom">
      <span>再/接/再/厉</span>
      <span class="ml-15">再/创/佳/绩</span>
    </div>
  </div>

  <div class="copywriting">
    <div class="copywriting-header">
      <p>
        <img src="https://safesfiles.oss-cn-beijing.aliyuncs.com/safes/ZHNX33656/火_202309192057.png" class="emoji-img">
        <span class="high-light">B类业务<span class="title-bold"><span id="month-1"></span>月分支风云榜（服务榜）</span></span>新鲜出炉!
      </p>
      <p>
        <img src="https://safesfiles.oss-cn-beijing.aliyuncs.com/safes/ZHNX33656/火_202309192057.png" class="emoji-img">
        B类业务火热进行中
        <img src="https://safesfiles.oss-cn-beijing.aliyuncs.com/safes/ZHNX33656/努力_202309192033.png" class="emoji-img">
        目前排名第一的是<span class="high-light" id="first-area"></span> ，
        当月注册客户数已达 <span class="title-bold"><span id="first-area-value"></span>人</span>。
      </p>
      <p>
        <img src="https://safesfiles.oss-cn-beijing.aliyuncs.com/safes/ZHNX33656/庆祝_202309192056.png" class="emoji-img">
        后面的伙伴们也都奋力冲刺！
        <!-- 其中<span class="bold">【<span id="ranked-area"></span>】区域</span>
        各显神通，共<span class="bold" id="ranked-number"></span>入围榜单。 -->
        其中<span class="title-bold" id="max-area"></span>分支更是高达
        <span class="high-light" id="max-area-ratio"></span>的霸榜率！详情请查看分支荣誉榜单~
      </p>
      <p class="bold">
        <img src="https://safesfiles.oss-cn-beijing.aliyuncs.com/safes/ZHNX33656/火_202309192057.png" class="emoji-img">
          让我们抓住机遇，一起全力冲刺吧！2023有您更精彩！
      </p>
    </div>
  </div>
</body>

<script>
  const doms = {
    areaDom : document.getElementById('table-tbody'),
    firstAreaDom : document.getElementById('first-area'),
    firstAreaValueDom : document.getElementById('first-area-value'),
    maxAreaRatioDom:document.getElementById('max-area-ratio'),
    maxAreaValueDom:document.getElementById('max-area'),
    month1Dom:document.getElementById('month-1'),
    month2Dom:document.getElementById('month-2'),
    month3Dom:document.getElementById('month-3'),
    // rankedNumberDom:document.getElementById('ranked-number'),
    // rankedAreaDom : document.querySelector('#ranked-area'),
  }

  const data = ${datasJson}

  const { datas,maxAreaName,yearMonth,areaNames,maxAreaPre } = data

<#noparse>
  // 位列第一的分支名称
  const firstOrgList = (datas || []).filter(v => v.rank_num === 1)
  const firstOrgName = firstOrgList.map(v => v.area_name + '-' +v.bch_name).join("、");
  doms.firstAreaDom.innerHTML = firstOrgName

  // 第一分支标保
  doms.firstAreaValueDom.innerHTML = firstOrgList[0].rank_value

  // 上榜区域
//   const areaNameList = (areaNames || []).map(v=> v.slice(0,-2))
//   doms.rankedAreaDom.innerHTML = areaNameList.join('、')

  // 上榜区域数量
//   doms.rankedNumberDom.innerHTML = datas.length + '家分支'

  // 最大霸榜率
  const maxAreaValue = maxAreaPre ? parseInt((maxAreaPre / datas?.length) * 100 ) : 0
  doms.maxAreaRatioDom.innerHTML = maxAreaValue + '%'
  doms.maxAreaValueDom.innerHTML = maxAreaName

  // 月份
  doms.month1Dom.innerHTML = yearMonth.monthValue
  doms.month2Dom.innerHTML = yearMonth.monthValue
  doms.month3Dom.innerHTML = yearMonth.monthValue

  // 填充列表模块
  doms.areaDom.innerHTML = datas && datas.map((item,index) => {
    return `
        <tr>
          <td>${index+1}</td>
          <td>${item.area_name}</td>
          <td>${item.bch_name}</td>
          <td>${item.rank_value}</td>
        </tr>
    `
  }).join("")

  </#noparse>
</script>

</html>
