<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>断保客户跟进情况-分支</title>
    <style>
        body {
            margin: 0;
        }

        .list-table {
            background-color: #fff;
            padding: 25px 10px;
            border-radius: 2px;
        }

        table {
            border-collapse: collapse;
            border-spacing: 0;
            table-layout: fixed;
            border-radius: 10px;
            background: #fff;
            max-width: none;
            min-width: 100%;
        }

        table tr {
            width: 100%;
        }

        table tr>td {
            font-size: 15px;
            text-align: center;
        }

        table tr>td:not(:first-child) {
            text-align: right;
        }

        table tr>th {
            font-size: 15px;
            text-align: center;
        }

        table tr>td,
        th {
            padding: 3px;
            border: 1px solid #000;
        }

        .table-title {
            font-size: 22px;
            color: #fff;
            font-weight: bolder;
        }

        .w90 {
            width: 90px;
        }

        .fs-16 {
            font-size: 16px;
        }

        .progress-bar {
            position: relative;
            width: 100%;
            height: 20px;
        }

        .progress {
            position: absolute;
            top: 0;
            left: 0;
            width: 0;
            height: 100%;
            text-align: right;
        }

        .breakIns-gradient {
            background: linear-gradient(to right, #67ceee, #b1e8f8);
        }

        .follow-gradient {
            background: linear-gradient(to right, #f5d298, #f0c886);
        }

        .renew-gradient {
            background: linear-gradient(to right, #6995dd, #b1cefc);
        }

        .sum-gradient {
            background: linear-gradient(to right, #8480eb, #d8d7f7);
        }

        .progress-text {
            position: relative;
            z-index: 99;
        }

        .green {
            background-color: #b7eb8f;
        }

        .deep-green {
            background-color: #73d13d;
            background-color: rgba(23, 213, 101);
        }

        .yellow {
            background-color: #ffd591;
        }

        .red {
            background-color: #ff7875;
        }

        .title-bg {
            background: rgb(98, 157, 252);
        }

        .skyblue-bg {
            background: #b1e8f8;
        }

        .yellow-bg {
            background: #ffe8c4;
        }

        .purple-bg {
            background: #b1cefc;
        }

        .pink-bg {
            background: #d8d7f7;
        }

        .blue-bg {
            background: #bbd5ee;
        }

        .gray-bg {
            background: rgb(203, 219, 227);
        }
    </style>
</head>


<body>
<div class="list-table">
    <!-- 区域/分支 table -->
    <table id="myTable">
        <thead>
        <tr>
            <th colspan="19" class="table-title title-bg">${titleName}断保客户跟进情况 <span
                        class="fs-16">（截止统计日期：${rptDate!}）</span>
            </th>
        </tr>
        <tr class="blue-tr">
            <th rowspan="2" class="w90 gray-bg">员工</th>
            <th colspan="4" class="skyblue-bg">断保客户数</th>
            <th colspan="3" class="yellow-bg">跟进客户数</th>
            <th colspan="3" class="purple-bg">激活客户数</th>
            <th colspan="3" class="pink-bg">激活保费</th>
            <th colspan="5" class="blue-bg">跟进情况（当年）</th>
        </tr>
        <tr class="blue-tr">
            <th class="skyblue-bg">当日</th>
            <th class="skyblue-bg">当月</th>
            <th class="skyblue-bg">当年</th>
            <th class="skyblue-bg">累计</th>
            <th class="yellow-bg">当日</th>
            <th class="yellow-bg">当月</th>
            <th class="yellow-bg">当年</th>
            <th class="purple-bg">当日</th>
            <th class="purple-bg">当月</th>
            <th class="purple-bg">当年</th>
            <th class="pink-bg">当日</th>
            <th class="pink-bg">当月</th>
            <th class="pink-bg">当年</th>
            <th class="blue-bg">高意向</th>
            <th class="blue-bg">中意向</th>
            <th class="blue-bg">低意向</th>
            <th class="blue-bg">无意向</th>
            <th class="blue-bg">联系不上</th>
        </tr>
        </thead>
        <tbody id="table-body">

        </tbody>
    </table>
</div>

</body>

<script>
    // 表格数据
    const tableData = ${jsonDatas}

    <#noparse>
    tableData.forEach(s=>{
        for (const sKey in s) {
            let val = s[sKey]
            if (!isNaN(val) && sKey !== 'sum_year' && sKey !== 'un_renew_total') {
                s[sKey] = Math.round(val)
            }
        }
    })
    const allData = tableData.filter(b => b["un"] !== '合计')

    function genPre(col, arr) {
        let maxVal = Math.max(...arr.map(a => a[col]))
        arr.forEach(item => {

            if (maxVal > 0) {
                item[col + '_pre'] = Math.round(item[col] / maxVal * 100)
            }
        })
    }

    let unNumKey = ["regionname", "dn", "un", "org", "userid", "orgcode"]
    for (const allDataKey in tableData[0]) {
        if (!isNaN(tableData[0][allDataKey])) {
            genPre(allDataKey, allData)
        }
    }

    const tableBody = document.getElementById("table-body");

    const tableConfig = [
        {title: '员工', field: 'un'},
        {title: '当日', field: 'un_renew_day', type: 'progress', belong: 'breakIns'},
        {title: '当月', field: 'un_renew_month', type: 'progress', belong: 'breakIns'},
        {title: '当年', field: 'un_renew_year', type: 'level', belong: 'breakIns'},
        {title: '累计', field: 'un_renew_total', type: 'level', belong: 'breakIns'},
        {title: '当日', field: 'follow_count_day', type: 'progress', belong: 'follow'},
        {title: '当月', field: 'follow_count_month', type: 'progress', belong: 'follow'},
        {title: '当年', field: 'follow_count_year', type: 'level', belong: 'follow'},
        {title: '当日', field: 'renew_day', type: 'progress', belong: 'renew'},
        {title: '当月', field: 'renew_month', type: 'progress', belong: 'renew'},
        {title: '当年', field: 'renew_year', type: 'level', belong: 'renew'},
        {title: '当日', field: 'sum_day', type: 'progress', belong: 'sum'},
        {title: '当月', field: 'sum_month', type: 'progress', belong: 'sum'},
        {title: '当年', field: 'sum_year', type: 'level', belong: 'sum'},
        {title: '高', field: 'high_count_year'},
        {title: '中', field: 'median_count_year'},
        {title: '低', field: 'low_count_year'},
        {title: '无意向', field: 'none_count_year'},
        {title: '联系不上', field: 'lose_contact_count_year'},
    ]


    function getColorLevel(data, field, name,fn) {
        let list = [...data];

        // 使用sort()方法和自定义比较函数按字段进行排序
        list.sort((a, b) => b[field] - a[field]);

        // 计算列表的长度和每个组的大小
        let length = list.length;
        let segmentSize = Math.floor(length / 4);

        let colorClass = '';

        for (let i = 0; i < list.length; i++) {
            const regionname = list[i][fn];

            if (regionname === name) {
                if (i < segmentSize) {
                    colorClass = 'green';
                } else if (i < 2 * segmentSize) {
                    colorClass = 'deep-green';
                } else if (i < 3 * segmentSize) {
                    colorClass = 'yellow';
                } else {
                    colorClass = 'red';
                }
                break;
            }
        }

        return colorClass;
    }

    // 获取table元素
    let table = document.getElementById('myTable');

    function fillHtmlData(data, config, table,fn) {
        for (let i = 0; i < data.length; i++) {
            // 创建行
            let row = table.insertRow();

            for (let j = 0; j < config.length; j++) {
                const currentConfig = config[j];
                const type = currentConfig.type;

                // 创建单元格
                let cell = row.insertCell();
                const field = currentConfig.field
                const curValue = data[i][field];
                const curRatio = data[i][`${field}_pre`];

                // 添加代码到单元格中
                if (curValue === 'null') {
                    cell.innerHTML = '-'
                } else {
                    cell.innerHTML = curValue || '-'
                }

                if (i !== data.length - 1) {
                    if (type === 'progress') {
                        const belong = currentConfig.belong

                        cell.innerHTML = `
                        <div class="progress-bar">
                            <div class="progress ${belong}-gradient" style="width: ${curRatio}%"></div>
                            <span class="progress-text">${curValue}</span>
                        </div>`;
                    } else if (type === 'level') {
                        const tdColor = getColorLevel(data, field, data[i][fn],fn);
                        cell.classList.add(tdColor);
                    }
                }
            }
        }
    }

    fillHtmlData(tableData, tableConfig, table, 'un')
    </#noparse>
</script>
</html>