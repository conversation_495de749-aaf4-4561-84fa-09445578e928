<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <title>中和宝贝计划个人排名</title>
    <style type="text/css">
        body {
            background-color: #6ab95e;
            margin: 0;
            font-family: "Heiti SC";
            width: 750px;
        }

        .box {
            padding: 0 30px 30px 30px;
        }

        .table {
            font-size: 16px;
            margin-bottom: 30px;
            width: 100%;
        }

        .tr {
            width: 100%;
        }

        .table-box {
            border-radius: 10px;
            overflow: hidden;
        }
        .th {
            background-color: white;
            height: 30px;
            border: 1px solid #e6e6e6;
            border-top: none;
            text-align: center;
            color: #333333;
        }

        .bottom-text {
            font-size: 16px;
            margin-top: 20px;
            width: 100%;
        }

        .no-data {
            color: #606266;
            padding: 25px;
            background-color: #ffffff;
            text-align: center;
        }
    </style>
</head>
<body>
<#assign rmbs=[1200, 800, 800, 400, 400, 400,400, 400,400, 400,200,200,200,200,200,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]>
<div>
    <img src="https://safesfiles.oss-cn-beijing.aliyuncs.com/safes/ZHNX30835/开学季宝贝计划_202108231612.jpg" width="750px"/>
    <div class="box">
        <table class="table table-box" cellpadding="0" cellspacing="0">
            <tr>
                <th class="th" colspan="6" style="font-size: 22px;padding: 12px">
                    中和宝贝计划个人排名(数据截止至${now})
                </th>
            </tr>
            <tr class="tr">
                <th class="th">排名</th>
                <th class="th">区域</th>
                <th class="th">机构</th>
                <th class="th">姓名</th>
                <th class="th">业绩/元</th>
                <th class="th">预获等价商品/元</th>
            </tr>
            <#list datas as data>
                <tr class="tr">
                    <td class="th" <#if data?counter lte 15>style="color: red" </#if>>${data?counter}</td>
                    <td class="th">${data.regionName!}</td>
                    <td class="th">${data.orgName!}</td>
                    <td class="th">${data.userName!}</td>
                    <td class="th">${data.amts?round}</td>
                    <#if data.exp lte 0 && data?counter lt rmbs?size >
                        <td class="th" <#if data?counter lte 15>style="color: red" </#if>>${rmbs[data?index]}</td>
                    <#else>
                        <td class="th">0</td>
                    </#if>
                </tr>
            <#else>
                <!-- 空状态 -->
                <tr class="tr">
                    <td colspan="6">
                        <div class="no-data">
                            <img src="https://oms-2018.oss-cn-hangzhou.aliyuncs.com/template/no-data.png"/>
                            <div class="mt-10">暂无数据</div>
                        </div>
                    </td>
                </tr>
            </#list>
        </table>
        <div class="bottom-text">
            备注：方案具体细则请查看【BX-TZ-202131号-关于开展“开学季·宝贝计划”营销推广活动的通知】
        </div>
    </div>
</div>
</body>
</html>
