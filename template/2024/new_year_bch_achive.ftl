<!-- 分支达成喜报 -->
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <title>分支达成喜报</title>
    <style>
        @font-face {
            font-family: "钉钉进步体 Regular";
            src: url("https://oms-2018-test.oss-cn-hangzhou.aliyuncs.com/template/DingTalk_JinBuTi_Regular.ttf");
        }
        @font-face {
            font-family: "阿里妈妈东方大楷 Regular";
            src: url("https://oms-2018-test.oss-cn-hangzhou.aliyuncs.com/template/Alimama_DongFangDaKai_Regular.ttf");
        }

        * {
            margin: 0;
            padding: 0;
        }
        #root {
            position: relative;
            width: 750px;
            font-weight: bold;
        }
        .bg {
            width: 100%;
            height: 100%;
            display: block;
        }
        .branch-bg{
            position: absolute;
            top: 445px;
            left: 50%;
            transform: translateX(-50%);
            width: 503px;
            height: 100px;
        }
        .branch{
            font-family:"阿里妈妈东方大楷 Regular";
            font-size: 34px;
            position: absolute;
            top: 474px;
            left: 50%;
            transform: translateX(-50%);
            white-space: nowrap;
            color:#ffffff;
        }
        .line{
            position: absolute;
            left: 50%;
            transform: translateX(-50%);
            width: 620px;
            text-align: center;
            font-size: 32px;
            color: rgb(195, 17, 15);
        }
        .line1{
            top: 578px;
            font-size: 37px;
        }
        .line2{
            top: 660px;
            color: rgb(31, 112, 138);
            font-size: 48px;
        }
        .line3{
            top: 755px;
            font-size: 42px;
        }
        .rank{
            top: 836px;
            font-size: 60px;
            color: rgb(31, 112, 138);
            font-family: cursive;
        }
        .horizontal-line {
            position: absolute;
            top: 952px;
            left: 50%;
            transform: translateX(-50%);
            width: 400px;
            height: 1px;
            background-color: rgb(195, 17, 15);
        }
        .rewards{
            top: 1000px;
            font-size: 30px;
        }
        .blue {
            color: rgb(31, 112, 138);
        }

    </style>
</head>
<body>
<div id="root">
    <img src="https://safesfiles.oss-cn-beijing.aliyuncs.com/safes/ZHNX33656/xibao_bg_202401311406.png" class="bg" />
    <img class="branch-bg" src="https://safesfiles.oss-cn-beijing.aliyuncs.com/safes/ZHNX33656/fz_bg_202401311413.png" alt="">
    <div class="branch">${area_name}-${bch_name}</div>
    <div class="line line1">
        在【画龙点鲸 福保迎春】活动中
    </div>
    <div class="line line2"> 分支达成人均 </div>
    <div class="line line3">
        非主营客户活动产品 ≥<span class="blue">21单</span>
    </div>
    <div class="line rank">“第${rank}家达成分支”</div>
    <div class="horizontal-line"></div>
    <div class="line rewards">
        分支每人可获得“<span class="blue">小鲸定制纪念版手表</span>”一块
    </div>
</div>
</body>
</html>

