<!-- 分支达成喜报 -->
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <title>个人达成喜报</title>
    <style>
        @font-face {
            font-family: "钉钉进步体 Regular";
            src: url("https://oms-2018-test.oss-cn-hangzhou.aliyuncs.com/template/DingTalk_JinBuTi_Regular.ttf");
        }
        @font-face {
            font-family: "阿里妈妈东方大楷 Regular";
            src: url("https://oms-2018-test.oss-cn-hangzhou.aliyuncs.com/template/Alimama_DongFangDaKai_Regular.ttf");
        }

        * {
            margin: 0;
            padding: 0;
        }
        #root {
            position: relative;
            width: 750px;
            font-weight: bold;
        }
        .bg {
            width: 100%;
            height: 100%;
            display: block;
        }
        .prize-bg {
            position: absolute;
            top: 426px;
            left: 50%;
            transform: translateX(-50%);
            width: 476px;
            height: 417px;
        }
        .avatar {
            position: absolute;
            top: 474px;
            left: 50%;
            transform: translateX(-50%);
            margin-left: -8px;
            width: 225px;
            height: 225px;
            border-radius: 50%;
        }
        .branch-bg{
            position: absolute;
            top: 426px;
            left: 50%;
            transform: translateX(-50%);
            width: 424px;
            height: 89px;
        }
        .branch{
            font-family:"阿里妈妈东方大楷 Regular";
            font-size: 44px;
            position: absolute;
            top: 462px;
            left: 50%;
            transform: translateX(-50%);
            white-space: nowrap;
            color:#ffffff;
            font-weight: normal;
        }
        .line{
            position: absolute;
            left: 50%;
            transform: translateX(-50%);
            width: 620px;
            text-align: center;
            font-size: 32px;
            color: rgb(195, 17, 15);
        }
        .person {
            position: absolute;
            top: 819px;
            left: 50%;
            transform: translateX(-50%);
            display: inline-block;
            width: 600px;
            text-align: center;
        }
        .person-name {
            display: inline-block;
            padding: 5px 30px;
            background-color: bisque;
            background: radial-gradient(circle, rgb(255,218,169) 70%, #ffffff 100%);
            font-size: 30px;
            color: rgb(195, 17, 15);
        }
        .line3{
            top: 885px;
            font-size: 30px;
        }
        .rank{
            top: 943px;
            width: 420px;
            height: 65px;
        }
        .horizontal-line {
            position: absolute;
            top: 1023px;
            left: 50%;
            transform: translateX(-50%);
            width: 300px;
            height: 1px;
            background-color: rgb(195, 17, 15);
        }
        .rewards{
            top: 1038px;
            font-size: 24px;
        }
        .blue {
            color: rgb(31, 112, 138);
        }
        .number {
            font-family: "钉钉进步体 Regular";
            font-size: 40px;
        }
        .tips {
            color: #333333;
            font-size: 16px;
        }

    </style>
</head>
<body>
<div id="root">
    <img src="https://safesfiles.oss-cn-beijing.aliyuncs.com/safes/ZHNX33656/person_xb_202401311555.png" class="bg" />

    <img src="https://safesfiles.oss-cn-beijing.aliyuncs.com/safes/ZHNX33656/prize_202401311555.png" class="prize-bg" />
    <img src="${avatar?default("https://safesfiles.oss-cn-beijing.aliyuncs.com/safes/ZHNX33656/默认头像_202309211954.png")}" class="avatar">
    <div class="person">
        <div class="person-name">${area_name}-${bch_name}-${recommend_emp_name}</div>
    </div>

    <div class="line line3">
        <#if (total_insured - total_surrender) lte 100>
            <span class="blue">单月累计</span>非主营客户活动单量 ≥<span class="number blue">50单</span>
        <#else>    <span class="blue">活动累计</span>非主营客户活动单量 ≥<span class="number blue">100单</span>
        </#if>

    </div>
    <#if (total_insured - total_surrender) lte 100>
        <img class="line rank" src="https://safesfiles.oss-cn-beijing.aliyuncs.com/safes/ZHNX33656/person_success_202402012028.png">
    <#else>
        <img class="line rank" src="https://safesfiles.oss-cn-beijing.aliyuncs.com/safes/ZHNX33656/activity_success_202402012028.png">
    </#if>

    <div class="horizontal-line"></div>
    <div class="line rewards">
        <div>个人可获得 “<span class="blue">小鲸定制纪念版手表</span>”一块</div>
        <span class="tips">（每人仅限一份，分支与个人奖励不重复获得）</span>
    </div>
</div>
</body>
</html>

