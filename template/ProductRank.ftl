<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <title>测试</title>
    <style type="text/css">
        body {
            background-color: #ec4a24;
            margin: 0px;
            width: 750px;
            font-family: "Microsoft YaHei";
        }

        .box {
            padding: 0px 30px 30px 30px;
        }

        .table {
            width: 100%;
            font-size: 16px;
            margin-bottom: 30px;
        }


        .th {
            background-color: #ffffff;
            height: 30px;
            border: 1px solid #e6e6e6;
            border-top: none;
            text-align: center;
            color: #333333;
        }

        .bottom-text {
            font-size: 16px;
            margin-top: 20px;
        }

        .no-data {
            color: #606266;
            padding: 25px;
            background-color: #ffffff;
            text-align: center;
        }

        .mt-10 {
            margin-top: 10px;
        }

    </style>
</head>
<body>

<div style="position: relative">
    <img src="https://safesfiles.oss-cn-beijing.aliyuncs.com/safes/ZHNX30835/9月顺福金生头图（ec4a24）_202109161440.png"
         width="100%"/>
    <div class="box">
        <table class="table" cellpadding="0" cellspacing="0">
            <tr>
                <th class="th" colspan="7" style="font-size: 20px;padding: 8px">
                    顺福金生活动个人预获奖券数前30展示(8月1日-${now})
                </th>
            </tr>
            <tr class="tr">
                <th class="th">序号</th>
                <th class="th">区域</th>
                <th class="th">机构</th>
                <th class="th">姓名</th>
                <th class="th">业绩/元</th>
                <th class="th">预获奖券数</th>
            </tr>
            <#assign wrct=0>
            <#list datas as data>
                <#if data.userName?? && wrct lt 30 >
                    <#assign wrct++>
                <tr class="tr">
                    <td class="th">${wrct}</td>
                    <td class="th">${data.regionName!}</td>
                    <td class="th">${data.orgName!}</td>
                    <td class="th">${data.userName!}</td>
                    <td class="th">${data.amts!}</td>
                    <td class="th" style="color: red">${data.cts!}</td>
                </tr>
                </#if>
            <#else>
                <!-- 空状态 -->
                <tr class="tr">
                    <td colspan="7">
                        <div class="no-data">
                            <img src="https://oms-2018.oss-cn-hangzhou.aliyuncs.com/template/no-data.png"/>
                            <div class="mt-10">暂无数据</div>
                        </div>
                    </td>
                </tr>
            </#list>
        </table>
        <div class="bottom-text">
            因版面展示有限，最多仅展示排名前30名单。详情请查看【BX-TZ-202138号关于开展九月顺福金生营销推广活动的通知（以本邮件为准）】
        </div>
    </div>
</div>
</body>
</html>
