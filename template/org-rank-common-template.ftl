<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <style type="text/css">
        body {
            background-color: ${backgroundColor};
            margin: 0;
            width: 750px;
        }

        .box {
            padding: 0 30px 30px 30px;
        }

        .table {
            font-size: 16px;
            margin-bottom: 30px;
            font-family: "Microsoft YaHei";
            width: 100%;
        }

        .tr {
            width: 100%;
        }

        .th {
            background-color: white;
            height: 30px;
            border: 1px solid #e6e6e6;
            border-top: none;
            text-align: center;
            color: #333333;
        }

        .bottom-text {
            font-size: 16px;
            margin-top: 20px;
            width: 100%;
        }

        .no-data {
            color: #606266;
            padding: 25px;
            background-color: #ffffff;
            text-align: center;
        }
    </style>
</head>
<body>
<#assign rmbs=rmbs>
<#assign fields=customFields>
<div>
    <img src=${headUrl} width="750px"/>
    <div class="box">
        <table class="table" cellpadding="0" cellspacing="0">
            <tr>
                <th class="th" colspan="5" style="font-size: 22px;padding: 12px">
                    ${productName}${startDate?substring(5,7)?number}月前${maxRows}机构预获奖励展示(数据截止至${now})
                </th>
            </tr>

            <tr class="tr">
                <th class="th"> 序号 </th>
                <#list fields as field>
                    <#if field.defaultShow==true ></#if>
                    <th class="th"> ${field.name} </th>
                </#list>
            </tr>

            <#list datas as data>
                    <tr class="tr">
                        <td class="th">${data?counter}</td>
                        <#if fieldItems?seq_contains('regionName') >
                            <td class="th">${data.regionName!}</td>
                        </#if>
                        <#if fieldItems?seq_contains('organizationName') >
                            <td class="th">${data.organizationName!}</td>
                        </#if>
                        <#if fieldItems?seq_contains('amts') >
                            <td class="th">${data.amts!}</td>
                        </#if>
                        <#if fieldItems?seq_contains('proportion')>
                            <th class="th" style="color: red;">
                                <#list rmbs as rmb>
                                    <#if data.amts gte rmb.min?number && data.amts lt rmb.max?number>
                                        <#assign totalAmount = "${data.amts?c}">
                                        <#assign percent = "${rmb.g?number}">
                                        <#assign result = "${totalAmount?number*percent?number}">
                                        ${result?replace(",","")?number?round}
                                    </#if>
                                </#list>
                            </th>
                        </#if>
                    </tr>
            <#else>
                <!-- 空状态 -->
                <tr class="tr">
                    <td colspan="5">
                        <div class="no-data">
                            <img src="https://oms-2018.oss-cn-hangzhou.aliyuncs.com/template/no-data.png"/>
                            <div class="mt-10">暂无数据</div>
                        </div>
                    </td>
                </tr>
            </#list>
        </table>
        <div class="bottom-text">
            ${bottomContent}
        </div>
    </div>
</div>
</body>
</html>
