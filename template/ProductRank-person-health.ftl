<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <title>健康险营销活动测试</title>
    <style type="text/css">
        body {
            background-color: #2A9EF6;
            margin: 0;
            width: 750px;
        }

        .box {
            padding: 0 30px 30px 30px;
        }

        .table {
            font-size: 16px;
            margin-bottom: 30px;
            font-family: "Microsoft YaHei";
            width: 100%;
        }

        .tr{
            width: 100%;
        }

        .th {
            background-color: white;
            height: 30px;
            border: 1px solid #e6e6e6;
            border-top: none;
            text-align: center;
            color: #333333;
        }

        .bottom-text {
            font-size: 16px;
            margin-top: 20px;
            width: 100%;
        }

        .no-data {
            color: #606266;
            padding: 25px;
            background-color: #ffffff;
            text-align: center;
        }
    </style>
</head>
<body>
<div>
    <img src="https://safesfiles.oss-cn-beijing.aliyuncs.com/safes/ZHNX30835/健康险抬头_202108201744.png" width="750px"/>
    <div class="box">
        <table class="table" cellpadding="0" cellspacing="0">
            <tr>
                <th class="th" colspan="7" style="font-size: 22px;padding: 12px">
                    八月健康险产品营销活动优秀个人奖(数据统计${rptBeginDate}至${rptEndDate})
                </th>
            </tr>
            <tr class="tr">
                <th class="th">序号</th>
                <th class="th">区域</th>
                <th class="th">机构</th>
                <th class="th">姓名</th>
                <th class="th">业绩/元</th>
                <th class="th">入围差额(标准：${standard}元)</th>
                <th class="th">预获奖/元</th>
            </tr>
            <#list datas as data>
                <tr class="tr">
                    <td class="th">${data_index+1}</td>
                    <td class="th">${data.regionName!}</td>
                    <td class="th">${data.organizationName!}</td>
                    <td class="th">${data.userName!}</td>
                    <td class="th">${data.amt?string("0")!}</td>
                    <#if data.exp gt 0>
                        <td class="th">-${data.exp?string("0")!}</td></#if>
                    <#if data.exp == 0>
                        <th class="th" style="color: red;">入围</th>
                    </#if>

                    <#if data_index==0 && data.exp ==0>
                        <td class="th" style="color: red;">2000</td>
                    <#elseif data_index == 1 && data.exp == 0>
                        <td class="th" style="color: red;">1500</td>
                    <#elseif data_index == 2 && data.exp == 0>
                        <td class="th" style="color: red;">1000</td>
                    <#elseif data_index gte 3 && data_index lte 9 && data.exp ==0>
                        <td class="th" style="color: red;">800</td>
                    <#elseif data_index gte 10 && data_index lte 14 && data.exp ==0>
                        <td class="th" style="color: red;">500</td>
                    <#else>
                        <td class="th"></td>
                    </#if>
                </tr>
            <#else>
                <!-- 空状态 -->
                <tr class="tr">
                    <td colspan="7">
                        <div class="no-data">
                            <img src="https://oms-2018.oss-cn-hangzhou.aliyuncs.com/template/no-data.png"/>
                            <div class="mt-10">暂无数据</div>
                        </div>
                    </td>
                </tr>
            </#list>
        </table>
        <div class="bottom-text">
            备注：以上数据均为折算后业绩
        </div>
    </div>

</div>
</body>
</html>