import com.cfpamf.common.ms.util.DateUtil
import com.cfpamf.ms.insur.operation.msg.pojo.dto.OpMessageRuleGroovyDTO
import com.cfpamf.ms.insur.operation.msg.util.Html2ImageUtils
import org.apache.commons.collections4.CollectionUtils

import java.time.LocalDate
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter
import java.util.stream.Collectors

String execute(OpMessageRuleGroovyDTO rule) {
    def (Map<String, Object> allParams, String dataZSetCacheKey, String dataMapCacheKey, String lastDayZSetCacheKey) = cacheData(rule)

    def showLine = Integer.valueOf(allParams['showLine'])
    if (showLine <= 0) {
        showLine = 1
    }
    //从redis取出前K名
    Set<String> topK = redisUtil.reverseRangeByScore(dataZSetCacheKey, 0, showLine-1)
    Set<String> discardRange = new LinkedHashSet<>()

    boolean showDiscardBefore = true
    boolean showDiscardAfter = true

//    def orgCode = allParams['orgCode']
    def amountRange = allParams['amountRange']
    println('------------------amountRange is: {}-----------------')
    println(amountRange)

    String orgInfoSql = "SELECT au.regionName, au.organizationName, au.regionCode FROM auth_user au WHERE au.userId = '${rule.getReceiver().otherParams['jobNumber']}'"
    def orgCode = empPostService.selectMap(orgInfoSql)['regionCode']

    //获取当前机构的名次
    def redisRankPos = redisUtil.zReverseSetRank(dataZSetCacheKey, orgCode)
    def currentPos
    //在redis里面找不到，那就是最后一名咯
    if(redisRankPos == null) {
        currentPos = redisUtil.countZSetCount(dataZSetCacheKey) + 1
    }else{
        currentPos = redisUtil.zReverseSetRank(dataZSetCacheKey, orgCode) + 1
    }

    //计算显示到第几名
    //如果该人到名次在topk之内
    def after = Integer.valueOf(allParams['after'])
    def before = Integer.valueOf(allParams['before'])
    if (currentPos <= showLine) {
        if(currentPos + after > showLine) {
            //继续显示到currentPos + allParams['after']行
            topK = redisUtil.reverseRangeByScore(dataZSetCacheKey, 0, currentPos + showLine)
        }
        showDiscardBefore = false
        showDiscardAfter = true
    }else {
        //如果该人不在topK内,但是剩余展示的会在topK内
        if (currentPos - before <= showLine){
            showDiscardAfter = true
            showDiscardBefore = false
            topK = redisUtil.reverseRangeByScore(dataZSetCacheKey, 0, currentPos + showLine)

        }else if (currentPos + after >= redisUtil.countZSetCount(dataZSetCacheKey)) {
            showDiscardAfter = false
            showDiscardBefore = true
            discardRange = redisUtil.reverseRangeByScore(dataZSetCacheKey, currentPos - before - 1, currentPos + after - 1)
        }else {
            discardRange = redisUtil.reverseRangeByScore(dataZSetCacheKey, currentPos - before - 1, currentPos + after - 1)
        }
    }

    //取出topK的详细数据，存入进去
    println(topK)
    List topKList = new ArrayList()
    for(key in topK) {
        topKList.add(redisUtil.hmGet(dataMapCacheKey, key))
    }

    List discardRangeList = new ArrayList()
    for(key in discardRange) {
        discardRangeList.add(redisUtil.hmGet(dataMapCacheKey, key))
    }

    //凭空捏造一条数据
    if(redisRankPos == null && CollectionUtils.isNotEmpty(discardRange)&&CollectionUtils.isNotEmpty(topKList)) {
        def orgQuerySql = "SELECT au.regionName, au.organizationName FROM auth_user au WHERE au.userId = '${rule.getReceiver().otherParams['jobNumber']}'"
        Map<String, Object> fakeData = msgStatService.selectMap(orgQuerySql)
        fakeData.put('averageAmts', 0)
        fakeData.put('partDegree',0)
        println(fakeData)
        discardRangeList.add(fakeData)
    }

    def selfData = redisUtil.hmGet(dataMapCacheKey, orgCode)

    //计算机构的档次
    def switchPosition = 0
    def comparedAmount= 0
    def showRankText = ''
    def selfAmts = Optional.ofNullable(selfData).map({ item -> Double.valueOf(item['amts']) }).orElse(0)
    for (int index = 0; index < amountRange.size(); index++) {
        def min = Double.valueOf(amountRange[index]['min'])
        def max = Double.valueOf(amountRange[index]['max'])
        if(index == 0) {
            comparedAmount = min
            amountRange[index]['showText']
        }
        if ( selfAmts >= min && selfAmts < max) {
            switchPosition++
            showRankText = amountRange[index]['showText']
            if (switchPosition == amountRange.size() - 1) {
                comparedAmount = min
            }else{
                comparedAmount = max
            }
        }
        break
    }

    allParams.put('topKList', topKList)
    allParams.put('discardRangeList', discardRangeList)
    allParams.put('showDiscardAfter', showDiscardAfter)
    allParams.put('showDiscardBefore', showDiscardBefore)
    allParams.put('startPos',currentPos - before)
    allParams.put('currentPos',currentPos)
    allParams.put('balance', comparedAmount - selfAmts)
    allParams.put('showRankText', showRankText)

    //获取今日和昨日排名
    def lastDayRedisPos = redisUtil.zReverseSetRank(lastDayZSetCacheKey, orgCode)

    if (redisRankPos == null) {
        allParams.put('changeRank', redisUtil.countZSetCount(dataZSetCacheKey) - currentPos + 1)
    }else{
        def lastDayPos = lastDayRedisPos + 1
        allParams.put('changeRank',currentPos - lastDayPos)
    }

    return Html2ImageUtils.processTemplateInfoString(rule.getMessageTemplate(), allParams)
}

private List cacheData(OpMessageRuleGroovyDTO rule) {
    synchronized (msgStatService) {
        Map<String, Object> allParams = jacksonObjectMapper.readValue(rule.getParams(), Map.class)

        allParams.put("now", DateUtil.format(new Date(), "yyyy年MM月dd日"))

        // key = "{}-{}-{}-{}:TREESET-SALE_TOOLS_ORG-productId-YYYYMMDD"   缓存存入当前的排名到redis中去，并且生效日期为2天，结构为treeset
        //所有到数据都存入到redis中去，结构为list，生效日期为2天,结构为MAP key = "{}-{}-{}-{}:HASH-ORG-productId-YYYYMMDD"
        Map<String, Object> redisCacheMap = new HashMap<>()
        String currentDay = DateUtil.format(new Date(), "yyyyMMdd")

        String baseMapCacheKey = '{}-{}-{}-{}:HASH-SALE_TOOLS_ORG-'
        String baseZSetCacheKey = '{}-{}-{}-{}:ZSET-SALE_TOOLS_ORG-'

        String dataMapCacheKey = baseMapCacheKey + allParams['productId'] + "-" + currentDay
        String dataZSetCacheKey = baseZSetCacheKey + allParams['productId'] + "-" + currentDay

        String lastDay = LocalDate.now().plusDays(-1L).format(DateTimeFormatter.ofPattern("yyyyMMdd"))
        String lastDayDataMapCacheKey = baseMapCacheKey + allParams['productId'] + "-" + lastDay
        String lastDayZSetCacheKey = baseZSetCacheKey + allParams['productId'] + "-" + lastDay

        def expiredTime = 48 * 60 * 60L

        def orgEmpCacheKey = '{}-{}:HASH-SALE_ORG_EMP_COUNT-' + currentDay
        def lastOrgEmpCacheKey = '{}-{}:HASH-SALE_ORG_EMP_COUNT-' + lastDay
        if (!redisUtil.exists(orgEmpCacheKey)) {
            cacheOrgCount(orgEmpCacheKey)
        }
        if (!redisUtil.exists(lastOrgEmpCacheKey)) {
            cacheOrgCount(lastOrgEmpCacheKey)
        }
        if (!redisUtil.exists(dataMapCacheKey)) {
            String rank = constructSql(allParams)
            allParams['datas'] = msgStatService.selectMaps(rank)
            for (data in allParams['datas']) {
                def empCount = redisUtil.hmGet(orgEmpCacheKey, data['orgCode'])
                if (empCount == null) {
                    data['averageAmts'] = 0
                    data['partDegree'] = 0
                }else {
                    data['averageAmts'] = data['amts'] / redisUtil.hmGet(orgEmpCacheKey, data['orgCode'])
                    data['partDegree'] = data['participate'] / redisUtil.hmGet(orgEmpCacheKey, data['orgCode'])
                    redisCacheMap.put(data['orgCode'], data)
                    redisUtil.zAdd(dataZSetCacheKey, data['orgCode'], data['averageAmts'])
                }

            }
            redisUtil.expire(dataZSetCacheKey, expiredTime)
            redisUtil.putAllHash(dataMapCacheKey, redisCacheMap)
            redisUtil.expire(dataMapCacheKey, expiredTime)
        }
        if (!redisUtil.exists(lastDayDataMapCacheKey)) {
            Map<String, Object> lastDayParams = new HashMap<>()
            Map<String, Object> lastRedisCacheMap = new HashMap<>()

            lastDayParams.put("endDate", LocalDateTime.now().withHour(0)
                    .withSecond(0).withMinute(0)
                    .format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")))
            lastDayParams.put("startDate", allParams.get("startDate"))
            lastDayParams.put("productId", allParams.get("productId"))

            String rank = constructSql(lastDayParams)
            lastDayParams['datas'] = msgStatService.selectMaps(rank)

            for (data in lastDayParams['datas']) {
                if (redisUtil.hmGet(orgEmpCacheKey, data['orgCode']) != null) {
                    data['averageAmts'] = data['amts'] / redisUtil.hmGet(orgEmpCacheKey, data['orgCode'])
                    data['partDegree'] = data['participate'] / redisUtil.hmGet(orgEmpCacheKey, data['orgCode'])
                    lastRedisCacheMap.put(data['orgCode'], data)
                    redisUtil.zAdd(lastDayZSetCacheKey, data['orgCode'], data['averageAmts'])
                }

            }
            redisUtil.expire(lastDayZSetCacheKey, expiredTime)
            redisUtil.putAllHash(lastDayDataMapCacheKey, redisCacheMap)
            redisUtil.expire(lastDayDataMapCacheKey, expiredTime)

        }
        [allParams, dataZSetCacheKey, dataMapCacheKey, lastDayZSetCacheKey]
    }


}


String constructSql(Map<String, Object> allParams) {
    String rank ="${-> """  
    SELECT 
    u.regionName,
           u.organizationName,
           so.fhOrderId,
           so.productId,
           sum(so.totalAmount) amts,
           u.orgCode,
           COUNT(DISTINCT so.recommendId) participate
    FROM sm_order so
             left join sm_order_insured soi on so.fhOrderId = soi.fhOrderId
             left join auth_user u on u.userid = so.recommendId and u.enabled_flag = 0
    where
       so.paymentTime >= '${allParams.startDate}'
        and so.paymentTime < '${allParams.endDate}'
      and so.payStatus = '2'
      and soi.appStatus = '1'
      and so.productId = '${allParams.productId}'
      and u.orgCode is not null
    group by u.orgCode order by amts desc
    """}"

    if(allParams['minAmts'] != null && allParams['minAmts'] != ''){
        rank = "$rank having amts >= ${allParams.minAmts}"
    }
    if(allParams['maxRows'] != null && allParams['maxRows'] != ''){
        rank = "$rank limit ${allParams.maxRows}"
    }
    return rank
}

String cacheOrgCount(def cacheKey) {
    String orgCountQuery = "${-> """  
    SELECT 
    count(*), bch_code
    FROM dim.employee_main_post_detail_dfp
    GROUP BY bch_code;
    """}"
    List<Map<String, Object>> orgEmpCountList = msgStatService.selectDwMaps(orgCountQuery)
    if (CollectionUtils.isNotEmpty(orgEmpCountList)) {
        Map<String, Object> cacheMap = orgEmpCountList.stream()
                .filter({ item -> item['bch_code'] != null })
                .collect(
                        Collectors.toMap(
                                { item -> item['bch_code'] }
                                , { item -> item['count'] }
                        )
                )
        redisUtil.putAllHash(cacheKey, cacheMap)
        redisUtil.expire(cacheKey, 24 * 60 * 60)
    }

}