package groovy.msg

import com.cfpamf.ms.insur.operation.msg.pojo.po.OpMessageRule
import com.cfpamf.ms.insur.operation.msg.util.Html2ImageUtils

import java.time.LocalDate

BigDecimal addPre(BigDecimal x,BigDecimal y){
    if( x != null && y!= null){
        return x.subtract(y).divide(y,2, BigDecimal.ROUND_DOWN)
    }
    return BigDecimal.ZERO
}
//活动整体数据跟踪
String execute(OpMessageRule rule) {
    Map<String, Object> allParams = jacksonObjectMapper.readValue(rule.getParams(), Map.class)
    LocalDate today = LocalDate.now()
    LocalDate actEndDate = LocalDate.now()
    def statEndDate = today.isAfter(actEndDate) ? actEndDate : today
    allParams['statEndDate'] = statEndDate
    def startDate = LocalDate.parse(allParams.startDate as CharSequence)
    def endDate = statEndDate
    String lazySql = "${-> """
        SELECT count(1) cts, sum(`totalAmount`) amts
from sm_order
WHERE `productId` in (${allParams.productIds.join(',')})
  and `paymentTime` >= '${startDate.toString()}'
  and `paymentTime` <  str_to_date('${endDate.toString()}', '%Y-%m-%d')
  and `payStatus` = '2'
    """}"
    allParams['actData'] = msgStatService.selectMap(lazySql)
    // 去年同期
    startDate = LocalDate.parse(allParams.startDate as CharSequence).minusYears(1L)
    endDate = statEndDate.minusYears(1L)
    allParams['yesteryear'] = msgStatService.selectMap(lazySql)

    allParams['yoy'] = addPre(allParams['actData'].amts, allParams['yesteryear'].amts)
    // 上月数据
    startDate = LocalDate.parse(allParams.startDate as CharSequence).minusMonths(1L)
    endDate = statEndDate.minusMonths(1L)
    allParams['lastMonth'] = msgStatService.selectMap(lazySql)
    allParams['mom'] = addPre(allParams['actData'].amts, allParams['lastMonth'].amts)
    System.err.println(allParams)
    return Html2ImageUtils.processTemplateInfoString(rule.getMessageTemplate(), allParams)
}

