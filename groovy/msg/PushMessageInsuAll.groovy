import com.cfpamf.ms.insur.operation.msg.pojo.dto.OpMessageRuleGroovyDTO
import com.cfpamf.ms.insur.operation.msg.util.Html2ImageUtils
import org.apache.commons.lang3.StringUtils

import java.time.LocalDate

String execute(OpMessageRuleGroovyDTO rule) {
    Map<String, Object> allParams = jacksonObjectMapper.readValue(rule.getParams(), Map.class)

    def userParam = rule.getReceiver().otherParams;
    def rptDate = StringUtils.isBlank(allParams.rptDate)?LocalDate.now().minusDays(1L): LocalDate.parse(allParams.rptDate)

    String yes ="""
 select rpt_date                        as rptDate,
               COALESCE(insured_cust_day, 0)   as insuredCustDay,
               COALESCE(insured_cnt_day, 0)    as insuredCntDay,
               COALESCE(insured_amt_day, 0)    as insuredAmtDay,
               COALESCE(insured_cust_month, 0) as insuredCustMonth,
               COALESCE(insured_cnt_month, 0)  as insuredCntMonth,
               COALESCE(insured_amt_month, 0)  as insuredAmtMonth,
               COALESCE(insured_cust_year, 0)  as insuredCustYear,
               COALESCE(insured_cnt_year, 0)   as insuredCntYear,
               COALESCE(insured_amt_year, 0)   as insuredAmtYear,
               COALESCE(insured_cust_all, 0)   as insuredCustAll,
               COALESCE(insured_cnt_all, 0)    as insuredCntAll,
               COALESCE(insured_amt_all, 0)    as insuredAmtAll
        from rpt.insurance_order_all_stat_dfp
 where rpt_date ='${rptDate}'
"""

    return Html2ImageUtils.processTemplateInfoString(rule.getMessageTemplate(), allParams)
}
