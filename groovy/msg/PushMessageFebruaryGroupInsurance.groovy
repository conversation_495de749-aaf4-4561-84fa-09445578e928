import com.cfpamf.ms.insur.operation.msg.pojo.dto.OpMessageRuleGroovyDTO
import com.cfpamf.ms.insur.operation.msg.util.Html2ImageUtils
import org.apache.commons.lang3.StringUtils

import java.time.LocalDate

String execute(OpMessageRuleGroovyDTO rule) {
    Map<String, Object> allParams = jacksonObjectMapper.readValue(rule.getParams(), Map.class)
    String sql ="""
SELECT
obj_new.regionName,
obj_new.organizationName,
obj_new.amount,
obj_new.rownum,
CASE

WHEN obj_new.rownum > 20 
AND obj_new.amount >= 10000 THEN
- 1 
WHEN obj_new.rownum = 1 THEN
2000 
WHEN obj_new.rownum = 2 THEN
1500 
WHEN obj_new.rownum = 3 THEN
1000 
WHEN obj_new.rownum >= 4 
AND obj_new.rownum <= 10 THEN 800 WHEN obj_new.rownum >= 11 
AND obj_new.rownum <= 15 THEN 500 WHEN obj_new.rownum >= 16 
AND obj_new.rownum <= 20 THEN
300 ELSE 0 
END AS reward,
obj_new.distance 
FROM
(
SELECT
obj.regionName,
obj.organizationName,
obj.amount,
@rownum := @rownum + 1 AS num_tmp,
@incrnum :=
CASE

WHEN @rowtotal = obj.amount THEN
@incrnum 
WHEN @rowtotal := obj.amount THEN
@rownum 
END AS rownum,
CASE

WHEN @rownum = 1 THEN
@one := obj.amount 
WHEN @rownum = 2 THEN
@two := obj.amount 
WHEN @rownum = 3 THEN
@three := obj.amount 
WHEN @rownum = 10 THEN
@four := obj.amount 
WHEN @rownum = 15 THEN
@five := obj.amount 
WHEN @rownum = 20 THEN
@six := obj.amount 
END AS temp,
round((
CASE

WHEN obj.amount < 10000 THEN
10000 - obj.amount 
WHEN @incrnum = 1 THEN
0 
WHEN @incrnum = 2 THEN
@one - obj.amount 
WHEN @incrnum = 3 THEN
@two - obj.amount 
WHEN @incrnum <= 10 AND @incrnum >= 4 THEN
@three - obj.amount 
WHEN @incrnum <= 15 AND @incrnum >= 11 THEN
@four - obj.amount 
WHEN @incrnum <= 20 AND @incrnum >= 16 THEN
@five - obj.amount ELSE @six - obj.amount 
END 
),
2 
) AS distance 
FROM
(
SELECT
t2.regionName,
t2.organizationName,
sum((
CASE

WHEN t7.total_amount IS NOT NULL THEN
t7.total_amount ELSE t1.unitPrice * t1.qty 
END 
)) AS amount 
FROM
sm_order t1
LEFT JOIN auth_user t2 ON t1.recommendId = t2.userId
LEFT JOIN sm_order_insured t3 ON t1.fhOrderId = t3.fhOrderId
LEFT JOIN sm_order_item t7 ON t3.fhOrderId = t7.fh_order_id 
AND t3.idNumber = t7.id_number 
AND t3.appStatus = t7.app_status 
WHERE
t1.create_time >= '2022-02-01 00:00:00' 
AND t1.create_time <= '2022-03-31 23:59:59' 
AND t1.payStatus = 2 
AND t1.productId IN ( 251, 169, 180, 219 ) 
AND t1.recommendId IS NOT NULL 
AND t2.regionName IS NOT NULL 
AND t3.appStatus = '1' 
GROUP BY
t2.`orgCode` 
ORDER BY
amount DESC 
LIMIT 40 
) AS obj,(
SELECT
@rownum := 0,
@rowtotal := NULL,
@incrnum := 0,
@one := NULL,
@two := NULL,
@three := NULL,
@four := NULL,
@five := NULL,
@six := NULL 
) r 
) AS obj_new
"""
    System.err.println(sql)
    def maps =     msgStatService.selectMaps(sql)
    allParams.put("rptDateDay", LocalDate.now().getDayOfMonth())
    allParams.put("rptDateMonth", LocalDate.now().getMonthValue())
    allParams.put("datas", maps)
    return Html2ImageUtils.processTemplateInfoString(rule.getMessageTemplate(), allParams)
}
