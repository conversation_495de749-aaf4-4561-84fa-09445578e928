import com.alibaba.fastjson.JSON
import org.springframework.util.CollectionUtils
import com.cfpamf.ms.insur.operation.msg.pojo.dto.OpMessageRuleGroovyDTO
import com.cfpamf.ms.insur.operation.msg.util.Html2ImageUtils

Map execute(OpMessageRuleGroovyDTO rule) {
    log.info("==========================start1================================")
    def resultMap = [:]  // 使用 Map 保存结果
    def areaCodeListString = rule.areaCodeListString
    def sql = """
    select tmp.recommendId from (
      select DISTINCT(t.recommendId )  as recommendId
        from sm_order t 
        LEFT JOIN sm_product t1 on t.productid = t1.id
        LEFT JOIN auth_user t2 on t.recommendId = t2.userId
          WHERE
          t.create_time >= CURDATE()
          and t.create_time < DATE_ADD(CURDATE(), INTERVAL 1 DAY)
          and LOCATE('_', t.fhOrderId) = 0
          and t.recommendId is not null
          and t2.regionCode in (${areaCodeListString})
    ) tmp
    left join op_message_push t1 on tmp.recommendId=t1.biz_code
    and t1.receiver_Name = '${rule.receiver.receiverName}'
    and t1.message_rule_id='${rule.id}'
    where t1.id is null
    """
    log.info("==========================start================================"+sql)
    //查询当天出单的推荐人列表(去重)
    def queryResult = msgStatService.selectMaps(sql);
    if(CollectionUtils.isEmpty(queryResult)){
        return resultMap;
    }
    //当天出单推荐人列表
    def recommendIdList = queryResult.collect{x-> return x["recommendId"]}
    log.info("=============== {}",JSON.toJSONString(recommendIdList))
    //当天出单推荐人字符串列表
    def recommendIdStr = buildRecommendIdSql(recommendIdList)
    log.info("==========================start================================"+recommendIdStr)
    //过滤非首单推荐人列表
    def firstOrderRecommendIdList = filterRecommendId(recommendIdStr,recommendIdList)
    log.info("firstOrderRecommendIdList=============== {}",JSON.toJSONString(firstOrderRecommendIdList))
    //如果首次出单推荐人列表不为空,则查询首单保单数据
    if(!CollectionUtils.isEmpty(firstOrderRecommendIdList)){
        recommendIdStr = buildRecommendIdSql(firstOrderRecommendIdList)
        //查询首单推荐人列表
        def recommendIdResult = findFirstOrder(recommendIdStr)
        def htmlString = null
        recommendIdResult.forEach {
            def recommendEmpId = it["recommendId"]
            def dingInfos = wecatService.getDingTalkInfo([recommendEmpId])
            def we = dingInfos.find { dingInfo -> Objects.equals(dingInfo.wechatUserId, recommendEmpId) }
            if (Objects.nonNull(we)) {
                it["avatar"] = we.avatar
            }
            htmlString = Html2ImageUtils.processTemplateInfoString(rule.getMessageTemplate(), it)
            log.info("test================"+htmlString);
            resultMap.put(it["recommendId"], htmlString)
        }
    }
    return resultMap
}

String buildRecommendIdSql(def queryResult){
    log.info("22222=============== {}",JSON.toJSONString(queryResult))
    if(CollectionUtils.isEmpty(queryResult)){
        return null
    }
    log.info("33=============== {}",JSON.toJSONString(queryResult))
    def sb = new StringBuffer("");
    queryResult.forEach{
        sb.append("'"+it+"',")
    }
    return sb.substring(0,sb.length()-1).toString()
}

//查询首单推荐人列表
def findFirstOrder(def recommendIdListStr){
    def sqlSafes = """
       SELECT t2.regionName,t2.organizationName, t2.userName,tmp.recommendId,
        COALESCE(t3.avatar,'https://safesfiles.oss-cn-beijing.aliyuncs.com/safes/ZHNX33656/默认头像_202309211954.png') as avatar,tmp.fhOrderId,tmp.totalAmount,
        ROUND((tmp.totalAmount)/10000,1) as wanmoney,
        t1.productShortName
        FROM (
        select a.recommendId,a.fhOrderId,a.totalAmount,a.productId from sm_order a INNER JOIN (
			select 
				t.`recommendId` ,min(t.`create_time` ) as create_time
			from sm_order t 
			WHERE 
			  t.create_time >= CURDATE() 
			  and t.create_time < DATE_ADD(CURDATE(), INTERVAL 1 DAY) 
			  and LOCATE('_', t.fhOrderId) = 0 
			  and t.recommendId in (${recommendIdListStr})
			  GROUP BY t.`recommendId` 
			) b on a.recommendId = b.recommendId and a.create_time=b.create_time
		where a.create_time >= CURDATE() 
		  and a.create_time < DATE_ADD(CURDATE(), INTERVAL 1 DAY) 
		  and LOCATE('_', a.fhOrderId) = 0 
		  and a.recommendId in (${recommendIdListStr})
        ) tmp
        LEFT JOIN sm_product t1 on tmp.productId=t1.id
        LEFT JOIN auth_user t2 ON tmp.recommendId = t2.userId
        left join (
			SELECT job_number, avatar
			FROM ding_talk_user 
			GROUP BY job_number
		) t3 on t2.userId=t3.job_number 
    """

    log.info("sqlSafes=============== {}",sqlSafes)
    def findFirstOrderResult = msgStatService.selectMaps(sqlSafes);
    log.info("findFirstOrderResult=============== {}",JSON.toJSONString(findFirstOrderResult))
    return findFirstOrderResult
}

//过滤非首单或者已经推送过的推荐人
def filterRecommendId(def recommendIdStr, def recommendIdList){
    def sqlSafes = """
        select DISTINCT(t.`recommendId`) as recommendId from sm_order t WHERE t.create_time < CURDATE() and LOCATE('_', t.fhOrderId) = 0 and t.recommendId in (${recommendIdStr})
    """
    log.info("sqlSafes=============== {}",sqlSafes)
    //非首单推荐人列表
    def recommendIdResult = msgStatService.selectMaps(sqlSafes);
    def noFirstRecommendIdList = recommendIdResult.collect{x-> return x["recommendId"]}
    if(CollectionUtils.isEmpty(recommendIdResult)){
        //如果非首单推荐人列表为空，则返回所有推荐人列表
        return recommendIdList
    }else{
        //剔除非首单推荐人列表
        return recommendIdList.minus(noFirstRecommendIdList)
    }
}