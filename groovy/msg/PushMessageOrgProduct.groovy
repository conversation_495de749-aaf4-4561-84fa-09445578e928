import com.cfpamf.common.ms.util.DateUtil
import com.cfpamf.ms.insur.operation.msg.pojo.dto.OpMessageRuleGroovyDTO
import com.cfpamf.ms.insur.operation.msg.util.Html2ImageUtils

import java.time.LocalDate

String execute(OpMessageRuleGroovyDTO rule) {
    Map<String, Object> allParams = jacksonObjectMapper.readValue(rule.getParams(), Map.class)
    allParams.put("now", DateUtil.format(new Date(),"MM月dd日 HH:mm"))
    String rank ="${-> """  
SELECT u.regionName,
       u.organizationName,
       so.fhOrderId,
       so.productId,
       so.recommendId,
       sum(so.totalAmount) amts
FROM sm_order so
         left join sm_order_insured soi on so.fhOrderId = soi.fhOrderId
         left join auth_user u on u.userid = so.recommendId and u.enabled_flag = 0
where
   so.paymentTime >= '${allParams.startDate}'
    and so.paymentTime < '${allParams.endDate}'
  and so.payStatus = '2'
  and soi.appStatus = '1'
  and so.productId in (${allParams.productIds.join(',')})
group by organizationName
"""}"
    if(allParams['minAmts'] != null && allParams['minAmts'] != ''){
        rank = "$rank having amts >= ${allParams.minAmts}"
    }
    rank = "$rank order by amts desc"
    if(allParams['maxRows'] != null && allParams['maxRows'] != ''){
        rank = "$rank limit ${allParams.maxRows}"
    }
    allParams['datas'] = msgStatService.selectMaps(rank)
    allParams.put("rptBeginDate", allParams.get("startDate").toString().substring(5))
    allParams.put("rptEndDate", LocalDate.now().toString().substring(5))
    return Html2ImageUtils.processTemplateInfoString(rule.getMessageTemplate(), allParams)
}
