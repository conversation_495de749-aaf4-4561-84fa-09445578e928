import com.alibaba.fastjson.JSON
import com.cfpamf.ms.insur.operation.msg.pojo.dto.OpMessageRuleGroovyDTO
import com.cfpamf.ms.insur.operation.msg.util.Html2ImageUtils
import org.apache.commons.collections4.CollectionUtils


Map execute(OpMessageRuleGroovyDTO rule) {
    def ftlParam = [:]

    def sql = """
SELECT t.* 
FROM (
SELECT t2.regionName, t2.organizationName, t2.userName, t2.userId,
COALESCE(t3.avatar,'https://safesfiles.oss-cn-beijing.aliyuncs.com/safes/ZHNX33656/默认头像_202309211954.png') as avatar,t.fhOrderId,totalAmount,
t1.productShortName
FROM sm_order t
LEFT JOIN sm_product t1 on t.productId=t1.id
LEFT JOIN auth_user t2 ON t.recommendId = t2.userId
left join (
			SELECT job_number, avatar
			FROM ding_talk_user 
			GROUP BY job_number
		) t3 on t2.userId=t3.job_number 
WHERE t.create_time >= CURDATE() 
and t.create_time < DATE_ADD(CURDATE(), INTERVAL 1 DAY) 
and LOCATE('_', t.fhOrderId) = 0
and t.recommendId is not null
and t2.regionName != '总部'
HAVING totalAmount>=200000) t
left join op_message_push t1 on t.fhOrderId=SUBSTRING_INDEX(t1.biz_code, '-', -1) 
and t1.receiver_Name = '${rule.receiver.receiverName}'
and t1.message_rule_id='${rule.id}'
where t1.id is null;
    """

    ftlParam["datas"] = msgStatService.selectMaps(sql)

    if (CollectionUtils.isEmpty(ftlParam["datas"] as Collection<?>)) {
        return null
    }

    def resultMap = [:]  // 使用 Map 保存结果

    ftlParam["datas"].each { data ->
        def userId = data["userId"] + "-" + data["fhOrderId"]
        def ftlMap = ["datasJson": JSON.toJSONString(data)]
        println ftlMap
        // 存储 userId 对应的数据行
        resultMap[userId] = Html2ImageUtils.processTemplateInfoString(rule.getMessageTemplate(), ftlMap)
    }
    println resultMap
    return resultMap
}

def findFirstOrderWhere(def recommendIdListStr){
    def sqlSafes = """
       SELECT t2.organizationName, t2.userName,tmp.recommendId,
        COALESCE()
    """
}