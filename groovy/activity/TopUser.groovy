import com.alibaba.fastjson.JSON
import com.cfpamf.ms.insur.operation.msg.pojo.dto.OpMessageRuleGroovyDTO
import com.cfpamf.ms.insur.operation.msg.util.Html2ImageUtils
import org.apache.commons.collections4.CollectionUtils


Map execute(OpMessageRuleGroovyDTO rule) {
    def ftlParam = [:]

    def sql = """
SELECT t.regionName, t.organizationName, t.userName, t.userId, 
 t.amounts
FROM (
    SELECT t2.regionName, t2.organizationName, t2.userName, t2.userId,
           SUM(t.conversion_amount) AS amounts,
           DENSE_RANK() OVER (ORDER BY SUM(t.conversion_amount) DESC) AS r
    FROM (
		SELECT *
		FROM sm_commission_detail
        WHERE account_time >= DATE_SUB(CURDATE(), INTERVAL 1 DAY) 
AND account_time < CURDATE()
		) t 
    LEFT JOIN sm_order t1 ON t.order_id = t1.fhOrderId
    LEFT JOIN auth_user t2 ON t1.recommendId = t2.userId
    WHERE t.account_time >= DATE_SUB(CURDATE(), INTERVAL 1 DAY) 
    AND t.account_time < CURDATE() and t2.regionName != '总部'
    GROUP BY t1.recommendId
) AS t
WHERE t.r = 1;
    """

    ftlParam["datas"] = msgStatService.selectMaps(sql)

    if (CollectionUtils.isEmpty(ftlParam["datas"] as Collection<?>)) {
        return null
    }
    def top3=ftlParam["datas"]
    def dingInfos = wecatService.getDingTalkInfo(top3*.userId)
    println dingInfos
    (ftlParam["datas"] as Collection<?>).stream().forEach {
        def we = dingInfos.find { dingInfo -> Objects.equals(dingInfo.wechatUserId, it.userId) }
        if (Objects.nonNull(we)){
            it["avatar"] = we.avatar
        }
    }
    def resultMap = [:]  // 使用 Map 保存结果

    ftlParam["datas"].each { data ->
        def userId = data["userId"]
        def ftlMap = ["datasJson": JSON.toJSONString(data)]
        println ftlMap
        // 存储 userId 对应的数据行
        resultMap[userId] = Html2ImageUtils.processTemplateInfoString(rule.getMessageTemplate(), ftlMap)
    }
    println resultMap
    return resultMap
}
