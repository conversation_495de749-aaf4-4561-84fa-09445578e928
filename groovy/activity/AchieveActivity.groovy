package groovy.activity

import com.cfpamf.ms.insur.operation.activity.form.AchieveActivityParams

/**
 * <AUTHOR>
 * @Date 2022/5/8 22:39
 * @Version 1.0
 */
Map<String, Object> execute(AchieveActivityParams params) {
    Map<String, Object> result = new HashMap<>();

    //查询机构下排名列表
    if (params.getQueryList()) {
        String tableSql = """
            SELECT SUM(IFNULL(t1.conversion_amount,0)) AS amount,
            t3.userName
            FROM 
            sm_achieve_emp_count t4 
            LEFT JOIN auth_user t3 ON t4.organization_name = t3.organizationName
            LEFT JOIN sm_order t2 ON t2.recommendId = t3.userId
            LEFT JOIN sm_commission_detail t1 ON t1.order_id = t2.fhOrderId AND t1.update_time BETWEEN '${params.startTime}' AND '${params.endTime}'
            WHERE
             t3.organizationName = '${params.organizationName}'
             AND t3.regionName = '${params.regionName}'
             AND t3.enabled_flag = 0
            AND t3.`status` != '8' AND t3.mainJobNumber = t3.userId
            AND t4.id IS NOT NULL
            GROUP BY t3.userId
        """
        result.put("tableData", msgStatService.selectMaps(tableSql))
    } else {
        if (!params.getQueryOrg()) {
            String personSql =
                    """
            SELECT SUM(IFNULL(t1.conversion_amount,0)) AS amount,
            t3.userName
            FROM
            auth_user t3 
            LEFT JOIN sm_order t2 ON t3.userId = t2.recommendId
            LEFT JOIN sm_commission_detail t1 ON t1.order_id = t2.fhOrderId AND t1.update_time BETWEEN '${params.startTime}' AND '${params.endTime}'
            LEFT JOIN sm_achieve_emp_count t4 ON t4.organization_name = t3.organizationName
            WHERE
            t3.userid = '${params.userId}'
            AND t3.regionName = '${params.regionName}'
            AND t3.enabled_flag = 0
            """
            result.put("personData", msgStatService.selectMaps(personSql))
        }
        String orgSql =
                """
            SELECT SUM(IFNULL(temp.conversion_amount,0)) AS amount,
            t4.organization_name as organizationName,
            t4.region_name as regionName,
            t4.count
            FROM 
            auth_user t3 LEFT JOIN
            (SELECT t2.recommendId, t1.conversion_amount FROM sm_order t2 INNER JOIN sm_commission_detail t1 ON t1.order_id = t2.fhOrderId WHERE t1.update_time BETWEEN '${params.startTime}' AND '${params.endTime}') temp ON t3.userId = temp.recommendId
            LEFT JOIN sm_achieve_emp_count t4 ON t4.organization_name = t3.organizationName
            WHERE
            t3.organizationName = '${params.organizationName}'
            AND t3.regionName = '${params.regionName}'
            AND t3.enabled_flag = 0
            AND t4.id IS NOT NULL
            GROUP BY t4.organization_name
            """
        result.put("orgData", msgStatService.selectMaps(orgSql))
    }
    result.put("goalAmount", "5000")
    return result
}
