package groovy.activity

import com.google.common.collect.Lists
import com.google.common.collect.Maps
import com.cfpamf.ms.insur.operation.activity.util.ActivitySqlUtils
import com.cfpamf.ms.insur.operation.activity.dto.ActivityConstRuleParam



List<Map<String, Object>> execute(ActivityConstRuleParam param) {
    def activity = param.constActivity()
    String sql = """  with cnt as (select  recommend_id,sum(case when policy_status = '4' then -1 else 1 end) policyCnt
           from dwa_ep_policy_product_info t
             where sell_product_id = 253
             and account_time >= '${activity.startTime}' and account_time < '${activity.endTime}' 
             and is_activity_order = 0
          group by recommend_id ) 
        select t.recommend_id,policy_no,10 as proportion,
                    CONCAT_WS('|',policy_no,endorsement_no,insured_code,sell_product_id,risk_id,term_num,policy_status) AS dataId,
                    CONCAT_WS('|',policy_no,endorsement_no,insured_code,sell_product_id ,risk_id,term_num,policy_status,${activity.id}) AS  uuid,
                    add_amount as amount
                    FROM dwa_ep_policy_product_info t
        left join cnt on cnt.recommend_id = t.recommend_id 
        where sell_product_id = 253 
        and account_time >= '${activity.startTime}' and account_time < '${activity.endTime}' 
        and cnt.policyCnt >= 10
        and is_activity_order = 0"""
    return msgStatService.selectMaps(sql)
}
