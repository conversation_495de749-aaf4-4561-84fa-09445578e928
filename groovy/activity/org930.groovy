import com.cfpamf.ms.insur.operation.msg.pojo.dto.OpMessageRuleGroovyDTO;
import com.cfpamf.ms.insur.operation.msg.service.MsgStatService;
import com.cfpamf.ms.insur.operation.msg.util.Html2ImageUtils
import org.springframework.util.CollectionUtils;


String execute(OpMessageRuleGroovyDTO rule) {
    MsgStatService service = msgStatService;

    List<String> dates = new ArrayList<>();
    def userParam = rule.getReceiver().otherParams;
    log.info("rule paras {}", rule.getParams());
    def data = [:];

    def newOrgCount = """select count(*) isNewOrg FROM 
    insurance.ads_insurance_bch_index_progress_dfp
    where bch_leader_id = '${userParam.mainJobNumber}' 
    and EXTRACT(YEAR FROM first_loan_date::date)=EXTRACT(YEAR FROM now()::date)""";
    def ps = msgStatService.selectDwMinerMap(newOrgCount);
    data.isNewOrg = ps.isNewOrg;

    def reward = """
    SELECT  first_loan_date,
    case when EXTRACT(YEAR FROM first_loan_date::date)=EXTRACT(YEAR FROM now()::date)
    then true else false end isNewOrg,
    cast(COALESCE(ROUND(sy_land_customer_cnt/sy_ms_emp_cnt::NUMERIC, 3),0) as decimal(10, 1)) as "perMonth",
    cast(COALESCE(ROUND(sy_land_customer_cnt/sy_ms_emp_cnt::NUMERIC, 3),0) as decimal(10, 1))-30 as "perMonthDifference",
    case when EXTRACT(YEAR FROM first_loan_date::date)=EXTRACT(YEAR FROM now()::date)
    then cast(COALESCE(ROUND(sy_land_customer_cnt/sy_ms_emp_cnt::NUMERIC, 3)*(EXTRACT(MONTH FROM now()::date)-EXTRACT(MONTH FROM first_loan_date::date)+1),0) as decimal(10, 1))
    else 
    cast(COALESCE(ROUND(sy_land_customer_cnt/sy_ms_emp_cnt::NUMERIC, 3)*EXTRACT(MONTH FROM now()::date),0) as decimal(10, 1)) end as "perLandCustomerCnt",
    case when EXTRACT(YEAR FROM first_loan_date::date)=EXTRACT(YEAR FROM now()::date)
    then cast(COALESCE(ROUND(sy_land_customer_cnt/sy_ms_emp_cnt::NUMERIC, 3)*(EXTRACT(MONTH FROM now()::date)-EXTRACT(MONTH FROM first_loan_date::date)+1),0) as decimal(10, 1))-150
    else 
    cast(COALESCE(ROUND(sy_land_customer_cnt/sy_ms_emp_cnt::NUMERIC, 3)*EXTRACT(MONTH FROM now()::date),0) as decimal(10, 1))-150 end as "difference",
    bch_leader_name AS "bchLeaderName",
    bch_name "bchName"
    FROM insurance.ads_insurance_bch_index_progress_dfp
    where bch_leader_id = '${userParam.mainJobNumber}'
    """;
    data.putAll(userParam)
    data.datas = service.selectDwMinerMaps(reward);
    if (CollectionUtils.isEmpty(data.datas)) {
        log.error("ads_insurance_bch_index_progress_dfp 没查到数据[{}]", userParam.mainJobNumber);
        return null;
    }
    log.info("push params {} {}", data)
    return Html2ImageUtils.processTemplateInfoString(rule.getMessageTemplate(), data);
}
