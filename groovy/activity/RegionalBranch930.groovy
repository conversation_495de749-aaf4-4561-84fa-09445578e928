import com.cfpamf.ms.insur.operation.msg.pojo.dto.OpMessageRuleGroovyDTO
import com.cfpamf.ms.insur.operation.msg.service.MsgStatService
import com.cfpamf.ms.insur.operation.msg.util.Html2ImageUtils
import org.springframework.util.CollectionUtils


String execute(OpMessageRuleGroovyDTO rule) {
    MsgStatService service = msgStatService;

    List<String> dates = new ArrayList<>();
    def userParam = rule.getReceiver().otherParams;
    def regionName = userParam.orgName.replace("管理部", "")
    log.info("rule paras {}", rule.getParams())
    def data = [:]
    def reward = """
        with tmp as (
        SELECT  first_loan_date,
        case when EXTRACT(YEAR FROM first_loan_date::date)=EXTRACT(YEAR FROM now()::date)
        then 1 else 0 end isNewOrg,
        cast(COALESCE(ROUND(sy_land_customer_cnt/sy_ms_emp_cnt::NUMERIC, 3),0) as decimal(10, 1)) as perMonth,
        case when EXTRACT(YEAR FROM first_loan_date::date)=EXTRACT(YEAR FROM now()::date)
        then cast(COALESCE(ROUND(sy_land_customer_cnt/sy_ms_emp_cnt::NUMERIC, 3)*(EXTRACT(MONTH FROM now()::date)-EXTRACT(MONTH FROM first_loan_date::date)+1),0) as decimal(10, 1))
        else 
        cast(COALESCE(ROUND(sy_land_customer_cnt/sy_ms_emp_cnt::NUMERIC, 3)*EXTRACT(MONTH FROM now()::date),0) as decimal(10, 1)) end as perLandCustomerCnt,
        bch_name bchName,
        area_name AS areaName
        FROM insurance.ads_insurance_bch_index_progress_dfp)
        select bchName "bchName",perLandCustomerCnt "perLandCustomerCnt",areaName "areaName",perLandCustomerCnt-150 difference,
        isNewOrg,perMonth,perMonth-30 perMonthDifference from tmp
        where areaName =  '${regionName}'
        order by perLandCustomerCnt desc
    """
    data.datas = service.selectDwMinerMaps(reward)
    if (CollectionUtils.isEmpty(data.datas)) {
        log.error("ads_insurance_bch_index_progress_dfp 没查到数据[{}]", regionName)
        return null
    }
    data.putAll(userParam)
    log.info("push params {} {}", userParam,data.datas)
    return Html2ImageUtils.processTemplateInfoString(rule.getMessageTemplate(), data)
}
