import java.time.LocalDate
import java.time.YearMonth
import com.cfpamf.ms.insur.operation.base.constant.BaseConstants
import org.apache.commons.lang3.StringUtils

Object execute(String params) {

    Map<String, Object> allParams = jacksonObjectMapper.readValue(params, Map.class);

    return getDetailData(allParams);
}

/**
 * 弹窗机构数据
 * @param params
 * @return
 */
Map<String, Object> getOrgPopUpData(Map<String, Object> params) {
    int currentMonth = YearMonth.now().getMonthValue();
    String lastDay = LocalDate.now().minusDays(1).format(BaseConstants.FMT_DATE);
    String orgName = params.get("orgName");
    String regionName = params.get("regionName");
    String userId = params.get("userId");
    Map<String, Object> mockData = new HashMap<>();
    String sql
    if (params.roleType == 1) {
//        mockData.put("orgName",orgName);
//        mockData.put("activityPolicyCount",100);
//        mockData.put("activityPolicyTarget",110);
//        mockData.put("activityPolicyCountAvg",20);
//        mockData.put("activityPolicyAvgTarget",20);

        sql = """ 
            select a.area_name regionName,
                   a.bch_name orgName, 
                   a.activityPolicyCount, 
                   round(a.activityPolicyTarget,1) activityPolicyTarget,
                   case when personCount=0 then 0 else round(a.activityPolicyCount/personCount,1) end activityPolicyCountAvg,
                   jinxiActivityPolicyCount,
                   case when personCount = 0 then 0 else round(a.jinxiActivityPolicyCount/personCount,1) end jinxiActivityPolicyCountAvg,
                   round(a.personCount,1) personCount,
                   27 activityPolicyAvgTarget
            FROM  (select area_name,
                  temp.bch_name,
                  sum(temp.total_insured-temp.total_surrender) activityPolicyCount,
                  sum(temp.activity1_total_insured-temp.activity1_total_surrender + zhongan_cancer_insured_jun-zhongan_cancer_surrender_jun + guotai_cancer_insured_jun-guotai_cancer_surrender_jun + jingxi_zhongan_hejiahuan_insured_jun-jingxi_zhongan_hejiahuan_surrender_jun) jinxiActivityPolicyCount,
                  max(temp.bch_manage_avg_cnt*27) activityPolicyTarget,
                  max(temp.bch_manage_avg_cnt) personCount
            from phoenix.temp_safes_stats_new_year_2024 temp where temp.pt = '${lastDay}' GROUP by temp.area_name,temp.bch_name) a where a.bch_name='${orgName}'
            """;
        Map<String, Object> result = msgStatService.selectSafesPgMap(sql);
        if (result == null){
            result = new HashMap<>();
            result.put("regionname",regionName);
            result.put("orgname",orgName);
            result.put("activitypolicycount",0);
            result.put("activitypolicytarget",0);
            result.put("activitypolicycountavg",0);
            result.put("jinxiactivitypolicycountavg",0);
            result.put("activitypolicyavgtarget",27);
            result.put("personcount",0);

        }

        return result
    }
    if(params.roleType == 2){
//        mockData.put("regionName",regionName);
//        mockData.put("activityPolicyCount",100);
//        mockData.put("activityPolicyTarget",110);
//        mockData.put("activityPolicyCountAvg",20);
//        mockData.put("activityPolicyAvgTarget",20);
        //return mockData;
        sql = """
            select a.area_name regionName, 
                   a.activityPolicyCount, 
                   round(a.activityPolicyTarget,1) activityPolicyTarget,
                   round(a.activityPolicyCount/personCount,1) activityPolicyCountAvg,
                   jinxiActivityPolicyCount,
                   round(a.jinxiActivityPolicyCount/personCount,1) jinxiActivityPolicyCountAvg,
                   round(a.personCount,1) personCount,
                   27 activityPolicyAvgTarget
            FROM  (select area_name,
                  sum(temp.total_insured-temp.total_surrender) activityPolicyCount,
                  sum(temp.activity1_total_insured - temp.activity1_total_surrender + zhongan_cancer_insured_jun-zhongan_cancer_surrender_jun + guotai_cancer_insured_jun-guotai_cancer_surrender_jun + jingxi_zhongan_hejiahuan_insured_jun-jingxi_zhongan_hejiahuan_surrender_jun) jinxiActivityPolicyCount,
                  max(temp.area_manage_avg_cnt)*27 activityPolicyTarget,
                  max(temp.area_manage_avg_cnt) personCount
            from phoenix.temp_safes_stats_new_year_2024 temp where temp.pt = '${lastDay}' GROUP by area_name) a where a.area_name='${regionName}'
            """;
        Map<String, Object> result = msgStatService.selectSafesPgMap(sql);
        if (result == null){
            result = new HashMap<>();
            result.put("regionname",regionName);
            result.put("activitypolicycount",0);
            result.put("activitypolicytarget",0);
            result.put("activitypolicycountavg",0);
            result.put("jinxiactivitypolicycountavg",0);
            result.put("activitypolicyavgtarget",27);
            result.put("personcount",0);
        }
        return result;
    }

    if (params.roleType == 3) {
        Map<String, Object> result = new HashMap<>();
//        mockData.put("orgName",orgName);
//        mockData.put("activityPolicyCount",100);
//        mockData.put("activityPolicyTarget",110);
//        mockData.put("activityPolicyCountAvg",20);
//        mockData.put("activityPolicyAvgTarget",20);
//
//        mockData.put("personCount01",30);
//        mockData.put("personCount02",40);
//        mockData.put("personCount03",0);
//        return mockData;
        String sqlPerson = """ 
        select temp.recommend_emp_id,
               temp.recommend_emp_name,
               temp.total_insured-temp.total_surrender activityPolicyCount,
               temp.pt,
               temp.district_name districtname,
               (temp.total_insured_apr - temp.total_surrender_apr) personcount01,
               (temp.total_insured_may - temp.total_surrender_may) personcount02,
               (temp.total_insured_jun - temp.total_surrender_jun) personcount03,
               (temp.total_insured - temp.total_surrender) personcounttotal,
               (temp.activity1_total_insured - temp.activity1_total_surrender + zhongan_cancer_insured_jun-zhongan_cancer_surrender_jun + guotai_cancer_insured_jun-guotai_cancer_surrender_jun + jingxi_zhongan_hejiahuan_insured_jun-jingxi_zhongan_hejiahuan_surrender_jun) jinxiActivitypolicycount
        from phoenix.temp_safes_stats_new_year_2024 temp where temp.pt in ('${lastDay}') and temp.area_name = '${regionName}'
               and temp.bch_name = '${orgName}' and temp.recommend_emp_id = '${userId}' order by temp.pt limit 1
    """;

        Map<String, Object> personCountResult = msgStatService.selectSafesPgMap(sqlPerson);


        sql = """ 
            select a.area_name regionName,
                   a.bch_name orgName, 
                   a.activityPolicyCount, 
                   round(a.activityPolicyTarget,1) activityPolicyTarget,
                   case when personCount=0 then 0 else round(a.activityPolicyCount/personCount,1) end activityPolicyCountAvg,
                   round(a.personCount,1) personCount,
                   27 activityPolicyAvgTarget
            FROM  (select area_name,
                  temp.bch_name,
                  sum(temp.total_insured-temp.total_surrender) activityPolicyCount,
                  sum(temp.activity1_total_insured - temp.activity1_total_surrender + zhongan_cancer_insured_jun-zhongan_cancer_surrender_jun + guotai_cancer_insured_jun-guotai_cancer_surrender_jun + jingxi_zhongan_hejiahuan_insured_jun-jingxi_zhongan_hejiahuan_surrender_jun) jinxiActivityPolicyCount,
                  max(temp.bch_manage_avg_cnt)*27 activityPolicyTarget,
                  max(temp.bch_manage_avg_cnt) personCount
            from phoenix.temp_safes_stats_new_year_2024 temp where temp.pt = '${lastDay}' GROUP by temp.area_name,temp.bch_name) a where a.bch_name='${orgName}'
            """;
        result = msgStatService.selectSafesPgMap(sql);
        if (result == null){
            result = new HashMap<>();
            result.put("regionname",regionName);
            result.put("orgname",orgName);
            result.put("activitypolicycount",0);
            result.put("activitypolicytarget",0);
            result.put("activitypolicycountavg",0);
            result.put("activitypolicyavgtarget",27);
            result.put("personcount",0);

        }
        //计算每月数据
        if(personCountResult == null){
            result.put("personcount01",0);
            result.put("personcount02",0);
            result.put("personcount03",0);
            result.put("personcounttotal",0);
            result.put("jinxiactivitypolicycount",0);
            return result;
        }
        result.put("personcount01",personCountResult["personcount01"]);
        result.put("personcount02",personCountResult["personcount02"]);
        result.put("personcount03",personCountResult["personcount03"]);
        result.put("personcounttotal",personCountResult["personcounttotal"]);
        result.put("jinxiactivitypolicycount",personCountResult["jinxiactivitypolicycount"]);
        result.put("districtname",personCountResult["districtname"]);
        return result;
    }

    //全国
    if(params.roleType == 4){
        sql = """
            select a.area_name regionName, 
                   a.activityPolicyCount, 
                   round(a.activityPolicyTarget,1) activityPolicyTarget,
                   round(a.jinxiactivityPolicyTarget,1) jinxiactivityPolicyTarget,
                   round(a.activityPolicyCount/personCount,1) activityPolicyCountAvg,
                   jinxiActivityPolicyCount,
                   round(a.jinxiActivityPolicyCount/personCount,1) jinxiActivityPolicyCountAvg,
                   round(a.personCount,1) personCount,
                   27 activityPolicyAvgTarget,
                   round(activityPolicyCount/activityPolicyTarget*100,1) activityPrePolicies,
                   round(jinxiActivityPolicyCount/jinxiactivityPolicyTarget*100,1) ajinxiActivityPrePolicies
            FROM  (select area_name,
                  sum(temp.total_insured-temp.total_surrender) activityPolicyCount,
                  sum(temp.activity1_total_insured - temp.activity1_total_surrender + zhongan_cancer_insured_jun-zhongan_cancer_surrender_jun + guotai_cancer_insured_jun-guotai_cancer_surrender_jun + jingxi_zhongan_hejiahuan_insured_jun-jingxi_zhongan_hejiahuan_surrender_jun) jinxiActivityPolicyCount,
                  max(temp.area_manage_avg_cnt)*27 activityPolicyTarget,
                  max(temp.area_manage_avg_cnt)*21 jinxiactivityPolicyTarget,
                  max(temp.area_manage_avg_cnt) personCount
            from phoenix.temp_safes_stats_new_year_2024 temp where temp.pt = '${lastDay}' and area_name is not null GROUP by area_name) a
            order by round(a.jinxiActivityPolicyCount/personCount,1) desc
            """;
        List<Map<String, Object>> activity1RegionDetailList = msgStatService.selectSafesPgMaps(sql);
        if (activity1RegionDetailList == null) {
            activity1RegionDetailList = new ArrayList<>()
        }

        activity2sql = """
            select a.area_name regionName, 
                   a.activityPolicyCount, 
                   round(a.activityPolicyTarget,1) activityPolicyTarget,
                   round(a.jinxiactivityPolicyTarget,1) jinxiactivityPolicyTarget,
                   round(a.activityPolicyCount/personCount,1) activityPolicyCountAvg,
                   jinxiActivityPolicyCount,
                   round(a.jinxiActivityPolicyCount/personCount,1) jinxiActivityPolicyCountAvg,
                   round(a.personCount,1) personCount,
                   27 activityPolicyAvgTarget,
                   round(activityPolicyCount/activityPolicyTarget*100,1) activityPrePolicies,
                   round(jinxiActivityPolicyCount/jinxiactivityPolicyTarget*100,1) ajinxiActivityPrePolicies
            FROM  (select area_name,
                  sum(temp.total_insured-temp.total_surrender) activityPolicyCount,
                  sum(temp.activity1_total_insured - temp.activity1_total_surrender + zhongan_cancer_insured_jun-zhongan_cancer_surrender_jun + guotai_cancer_insured_jun-guotai_cancer_surrender_jun + jingxi_zhongan_hejiahuan_insured_jun-jingxi_zhongan_hejiahuan_surrender_jun) jinxiActivityPolicyCount,
                  max(temp.area_manage_avg_cnt)*27 activityPolicyTarget,
                  max(temp.area_manage_avg_cnt)*21 jinxiactivityPolicyTarget,
                  max(temp.area_manage_avg_cnt) personCount
            from phoenix.temp_safes_stats_new_year_2024 temp where temp.pt = '${lastDay}' and area_name is not null GROUP by area_name) a
            order by round(a.activityPolicyCount/personCount,1) desc
            """;
        List<Map<String, Object>> regionDetailList = msgStatService.selectSafesPgMaps(activity2sql);
        if (regionDetailList == null) {
            regionDetailList = new ArrayList<>()
        }

        def sqlResult = """
            select 
                   sum(a.activityPolicyCount) activityPolicyCount, 
                   round(sum(a.activityPolicyTarget),1) activityPolicyTarget,
                   round(sum(a.activityPolicyCount)/sum(personCount),1) activityPolicyCountAvg,
                   sum(jinxiActivityPolicyCount) jinxiActivityPolicyCount,
                   round(sum(a.jinxiActivityPolicyCount)/sum(personCount),1) jinxiActivityPolicyCountAvg,
                   round(sum(a.personCount),1) personCount,
                   27 activityPolicyAvgTarget
            FROM  (select area_name,
                  sum(temp.total_insured-temp.total_surrender) activityPolicyCount,
                  sum(temp.activity1_total_insured - temp.activity1_total_surrender + zhongan_cancer_insured_jun-zhongan_cancer_surrender_jun + guotai_cancer_insured_jun-guotai_cancer_surrender_jun + jingxi_zhongan_hejiahuan_insured_jun-jingxi_zhongan_hejiahuan_surrender_jun) jinxiActivityPolicyCount,
                  max(temp.area_manage_avg_cnt)*27 activityPolicyTarget,
                  max(temp.area_manage_avg_cnt) personCount
            from phoenix.temp_safes_stats_new_year_2024 temp where temp.pt = '${lastDay}' and area_name is not null GROUP by area_name) a
            """;
        Map<String, Object> result = msgStatService.selectSafesPgMap(sqlResult);
        if (result == null){
            result = new HashMap<>();
            result.put("activitypolicycount",0);
            result.put("jinxiactivitypolicycount",0);
            result.put("activitypolicytarget",0);
            result.put("activitypolicycountavg",0);
            result.put("jinxiactivitypolicycountavg",0);
            result.put("activitypolicyavgtarget",27);
            result.put("personcount",0);
        }
        result.put("activity1regiondetaillist",activity1RegionDetailList)
        result.put("regiondetaillist",regionDetailList)
        return result;
    }

}


Map<String, Object> getDetailData(Map<String, Object> params) {

    Map<String, Object> result = new HashMap<>();

    result.put("orgPopData", getOrgPopUpData(params));

    return result;
}