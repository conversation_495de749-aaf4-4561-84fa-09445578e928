import java.time.LocalDate
import java.time.YearMonth
import com.cfpamf.ms.insur.operation.base.constant.BaseConstants
import org.apache.commons.lang3.StringUtils

Object execute(String params) {

    Map<String, Object> allParams = jacksonObjectMapper.readValue(params, Map.class);

    return getDetailData(allParams);
}

/**
 * 弹窗机构数据
 * @param params
 * @return
 */
Map<String, Object> getOrgPopUpData(Map<String, Object> params) {
    int currentMonth = YearMonth.now().getMonthValue();
    String lastDay = LocalDate.now().minusDays(1).format(BaseConstants.FMT_DATE);
    String orgName = params.get("orgName");
    String regionName = params.get("regionName");
    String userId = params.get("userId");
    Map<String, Object> mockData = new HashMap<>();
    String sql
    if (params.roleType == 1) {

        sql = """ 
            select a.area_name regionName,
                   a.bch_name orgName, 
                   round(a.personCount,1) personCount,
                   a.activityPolicyCount, 
                   round(a.activityPolicyTarget_21,1) activityPolicyTarget21,
                   round(a.activityPolicyTarget_28,1) activityPolicyTarget28,
                   a.activityPolicyCountAvg,
                   case when a.activityPolicyTarget_21 >0 then round(a.activityPolicyCount/a.activityPolicyTarget_21*100,1) else 0 end completionRate21,
                   case when a.activityPolicyTarget_28 > 0 then round(a.activityPolicyCount/a.activityPolicyTarget_28*100,1) else 0 end completionRate28,
                   21 activityPolicyAvgTarget21,
                   28 activityPolicyAvgTarget28,
                   rank
            FROM  ( select area_name,
                  temp.bch_name,
                  sum(temp.total_insured-temp.total_surrender) activityPolicyCount,
                  max(temp.bch_manage_avg_cnt)*21 activityPolicyTarget_21,
                  max(temp.bch_manage_avg_cnt)*28 activityPolicyTarget_28,
                  max(temp.bch_manage_avg_cnt) personCount,
                  case when max(temp.bch_manage_avg_cnt)>0 then round(sum(temp.total_insured-temp.total_surrender)/max(temp.bch_manage_avg_cnt),1) else 0 end activityPolicyCountAvg,
                  rank() over(order by case when max(temp.bch_manage_avg_cnt)>0 then round(sum(temp.total_insured-temp.total_surrender)/max(temp.bch_manage_avg_cnt),1) else 0 end desc) rank
            from phoenix.temp_safes_stats_new_year_2024 temp 
            where temp.pt = '${lastDay}'
            GROUP by temp.area_name,temp.bch_name) a 
            where a.bch_name='${orgName}'
            """;
        Map<String, Object> result = msgStatService.selectSafesPgMap(sql);
        if (result == null){
            result = new HashMap<>();
            result.put("regionname",regionName);
            result.put("orgname",orgName);
            result.put("personcount",0);
            result.put("activitypolicycount",0);
            result.put("activitypolicytarget21",0);
            result.put("activitypolicytarget28",0);
            result.put("activitypolicycountavg",0);
            result.put("completionrate21",0);
            result.put("completionrate28",0);
            result.put("activitypolicyavgtarget21",21);
            result.put("activitypolicyavgtarget28",28);
            result.put("rank",0);
        }

        //获取与上一名的人均单量差值
        def preDiff = getBchPreDiff(result,lastDay)
        result["prediff"] = preDiff;

        return result
    }
    if(params.roleType == 2){
        sql = """
            select 
                a.area_name regionName,
                round(a.personCount,1) personCount,
                a.activityPolicyCount, 
                round(a.activityPolicyTarget_21,1) activityPolicyTarget21,
                round(a.activityPolicyTarget_28,1) activityPolicyTarget28,
                a.activityPolicyCountAvg,
                round(a.activityPolicyCount/a.activityPolicyTarget_21*100,1) completionRate21,
                round(a.activityPolicyCount/a.activityPolicyTarget_28*100,1) completionRate28,
                21 activityPolicyAvgTarget21,
                28 activityPolicyAvgTarget28,
                rank
            FROM  (
                select 
                    area_name,
                    sum(temp.total_insured-temp.total_surrender) activityPolicyCount,
                    max(temp.area_manage_avg_cnt)*21 activityPolicyTarget_21,
                    max(temp.area_manage_avg_cnt)*28 activityPolicyTarget_28,
                    max(temp.area_manage_avg_cnt) personCount,
                    round((sum(temp.total_insured-temp.total_surrender))/max(temp.area_manage_avg_cnt),1) activityPolicyCountAvg,
                    rank() over(order by round((sum(temp.total_insured-temp.total_surrender))/max(temp.area_manage_avg_cnt),1) desc) rank
                from phoenix.temp_safes_stats_new_year_2024 temp 
                where temp.pt = '${lastDay}' 
                and area_name like '%区域'
                GROUP by area_name
            ) a 
            where a.area_name='${regionName}'
            """;
        Map<String, Object> result = msgStatService.selectSafesPgMap(sql);
        if (result == null){
            result = new HashMap<>();
            result.put("regionname",regionName);
            result.put("orgname",orgName);
            result.put("personcount",0);
            result.put("activitypolicycount",0);
            result.put("activitypolicytarget21",0);
            result.put("activitypolicytarget28",0);
            result.put("activitypolicycountavg",0);
            result.put("completionrate21",0);
            result.put("completionrate28",0);
            result.put("activitypolicyavgtarget21",21);
            result.put("activitypolicyavgtarget28",28);
            result.put("rank",0);
        }

        //获取与上一名的人均单量差值
        def preDiff = getAreaPreDiff(result,lastDay)
        result["prediff"] = preDiff;
        return result;
    }

    if (params.roleType == 3) {
        Map<String, Object> result = new HashMap<>();
        String sqlPerson = """ 
        select temp.recommend_emp_id,
               temp.recommend_emp_name,
               temp.total_insured-temp.total_surrender activityPolicyCount,
               temp.pt,
               temp.district_name districtname,
               (temp.total_insured_jul - temp.total_surrender_jul) personcount01,
               (temp.total_insured_aug - temp.total_surrender_aug) personcount02,
               (temp.total_insured_sept - temp.total_surrender_sept) personcount03,
               (temp.total_insured - temp.total_surrender) personcounttotal
        from phoenix.temp_safes_stats_new_year_2024 temp where temp.pt in ('${lastDay}') and temp.area_name = '${regionName}'
               and temp.bch_name = '${orgName}' and temp.recommend_emp_id = '${userId}' order by temp.pt limit 1
    """;

        Map<String, Object> personCountResult = msgStatService.selectSafesPgMap(sqlPerson);


        sql = """ 
                select a.area_name regionName,
                       a.bch_name orgName, 
                       round(a.personCount,1) personCount,
                       a.activityPolicyCount, 
                       round(a.activityPolicyTarget_21,1) activityPolicyTarget21,
                       round(a.activityPolicyTarget_28,1) activityPolicyTarget28,
                       a.activityPolicyCountAvg,
                       case when a.activityPolicyTarget_21 >0 then round(a.activityPolicyCount/a.activityPolicyTarget_21*100,1) else 0 end completionRate21,
                       case when a.activityPolicyTarget_28 > 0 then round(a.activityPolicyCount/a.activityPolicyTarget_28*100,1) else 0 end completionRate28,
                       21 activityPolicyAvgTarget21,
                       28 activityPolicyAvgTarget28,
                       rank
                FROM  ( select area_name,
                      temp.bch_name,
                      sum(temp.total_insured-temp.total_surrender) activityPolicyCount,
                      max(temp.bch_manage_avg_cnt)*21 activityPolicyTarget_21,
                      max(temp.bch_manage_avg_cnt)*28 activityPolicyTarget_28,
                      max(temp.bch_manage_avg_cnt) personCount,
                      case when max(temp.bch_manage_avg_cnt)>0 then round(sum(temp.total_insured-temp.total_surrender)/max(temp.bch_manage_avg_cnt),1) else 0 end activityPolicyCountAvg,
                      rank() over(order by case when max(temp.bch_manage_avg_cnt)>0 then round(sum(temp.total_insured-temp.total_surrender)/max(temp.bch_manage_avg_cnt),1) else 0 end desc) rank
                from phoenix.temp_safes_stats_new_year_2024 temp 
                where temp.pt = '${lastDay}'
                GROUP by temp.area_name,temp.bch_name) a 
                where a.bch_name='${orgName}'
            """;
        result = msgStatService.selectSafesPgMap(sql);
        if (result == null){
            result = new HashMap<>();
            result.put("regionname",regionName);
            result.put("orgname",orgName);
            result.put("personcount",0);
            result.put("activitypolicycount",0);
            result.put("activitypolicytarget21",0);
            result.put("activitypolicytarget28",0);
            result.put("activitypolicycountavg",0);
            result.put("completionrate21",0);
            result.put("completionrate28",0);
            result.put("activitypolicyavgtarget21",21);
            result.put("activitypolicyavgtarget28",28);
            result.put("rank",0);

        }
        //计算每月数据
        if(personCountResult == null){
            result.put("personcount01",0);
            result.put("personcount02",0);
            result.put("personcount03",0);
            result.put("personcounttotal",0);
            return result;
        }
        result.put("personcount01",personCountResult["personcount01"]);
        result.put("personcount02",personCountResult["personcount02"]);
        result.put("personcount03",personCountResult["personcount03"]);
        result.put("personcounttotal",personCountResult["personcounttotal"]);
        result.put("districtname",personCountResult["districtname"]);
        return result;
    }

    //全国
    if(params.roleType == 4){
        activity2sql = """
            select 
                a.area_name regionName, 
                round(a.personCount,1) personCount,
                round(a.activityPolicyTarget21,1) activityPolicyTarget21,
                round(a.activityPolicyTarget28,1) activityPolicyTarget28,
                a.activityPolicyCount, 
                round(a.activityPolicyCount/a.activityPolicyTarget21*100,1) completionRate21,
                round(a.activityPolicyCount/a.activityPolicyTarget28*100,1) completionRate28,
                a.activityPolicyCountAvg,
                21 activityPolicyAvgTarget21,
                28 activityPolicyAvgTarget28
            FROM  (
                select 
                    area_name,
                    sum(temp.total_insured-temp.total_surrender) activityPolicyCount,
                    max(temp.area_manage_avg_cnt)*21 activityPolicyTarget21,
                    max(temp.area_manage_avg_cnt)*28 activityPolicyTarget28,
                    max(temp.area_manage_avg_cnt) personCount,
                    round((sum(temp.total_insured-temp.total_surrender))/max(temp.area_manage_avg_cnt),1) activityPolicyCountAvg
                from phoenix.temp_safes_stats_new_year_2024 temp 
                where 
                    temp.pt = '${lastDay}' 
                    and area_name like '%区域'
                GROUP by area_name
            ) a
            order by activityPolicyCountAvg desc
            """;
        List<Map<String, Object>> regionDetailList = msgStatService.selectSafesPgMaps(activity2sql);
        if (regionDetailList == null) {
            regionDetailList = new ArrayList<>()
        }

        def sqlResult = """
            select 
                sum(a.activityPolicyCount) activityPolicyCount, 
                round(sum(a.activityPolicyTarget21),1) activityPolicyTarget21,
                round(sum(a.activityPolicyTarget28),1) activityPolicyTarget28,
                round(sum(a.activityPolicyCount)/sum(personCount),1) activityPolicyCountAvg,
                round(sum(a.activityPolicyCount)/sum(a.activityPolicyTarget21)*100,1) completionRate21,
                round(sum(a.activityPolicyCount)/sum(a.activityPolicyTarget28)*100,1) completionRate28,
                round(sum(a.personCount),1) personCount,
                21 activityPolicyAvgTarget21,
                28 activityPolicyAvgTarget28
            FROM  (
                select area_name,
                    sum(temp.total_insured-temp.total_surrender) activityPolicyCount,
                    max(temp.area_manage_avg_cnt)*28 activityPolicyTarget28,
                    max(temp.area_manage_avg_cnt)*21 activityPolicyTarget21,
                    max(temp.area_manage_avg_cnt) personCount
                from phoenix.temp_safes_stats_new_year_2024 temp 
                where 
                    temp.pt = '${lastDay}' 
                    and area_name like '%区域'
                GROUP by area_name
            ) a
            """;
        Map<String, Object> result = msgStatService.selectSafesPgMap(sqlResult);
        if (result == null){
            result = new HashMap<>();
            result.put("personcount",0);
            result.put("activitypolicytarget21",0);
            result.put("activitypolicytarget28",0);
            result.put("activitypolicycount",0);
            result.put("completionrate21",0);
            result.put("completionrate28",0);
            result.put("activitypolicycountavg",0);
            result.put("activitypolicyavgtarget21",21);
            result.put("activitypolicyavgtarget28",28);
            result.put("rank",0);
        }
        result.put("regiondetaillist",regionDetailList)
        return result;
    }

}


Map<String, Object> getDetailData(Map<String, Object> params) {

    Map<String, Object> result = new HashMap<>();

    result.put("orgPopData", getOrgPopUpData(params));

    return result;
}

BigDecimal getBchPreDiff(Map<String, Object> result,String lastDay) {
    if (result == null) {
        return BigDecimal.ZERO;
    }
    //获取与上一排名的人均单量差值
    def rank = result.get("rank");
    if (rank != 1) {
        def sqlPre = """
                select a.area_name regionName,
                   a.bch_name orgName, 
                   round(a.personCount,1) personCount,
                   a.activityPolicyCount, 
                   round(a.activityPolicyTarget_21,1) activityPolicyTarget21,
                   round(a.activityPolicyTarget_28,1) activityPolicyTarget28,
                   a.activityPolicyCountAvg,
                   case when a.activityPolicyTarget_21 >0 then round(a.activityPolicyCount/a.activityPolicyTarget_21*100,1) else 0 end completionRate21,
                   case when a.activityPolicyTarget_28 > 0 then round(a.activityPolicyCount/a.activityPolicyTarget_28*100,1) else 0 end completionRate28,
                   21 activityPolicyAvgTarget_21,
                   28 activityPolicyAvgTarget_28,
                   rank
                FROM  ( 
                    select area_name,
                        temp.bch_name,
                        sum(temp.total_insured-temp.total_surrender) activityPolicyCount,
                        max(temp.bch_manage_avg_cnt)*21 activityPolicyTarget_21,
                        max(temp.bch_manage_avg_cnt)*28 activityPolicyTarget_28,
                        max(temp.bch_manage_avg_cnt) personCount,
                        case when max(temp.bch_manage_avg_cnt)>0 then round(sum(temp.total_insured-temp.total_surrender)/max(temp.bch_manage_avg_cnt),1) else 0 end activityPolicyCountAvg,
                        rank() over(order by case when max(temp.bch_manage_avg_cnt)>0 then round(sum(temp.total_insured-temp.total_surrender)/max(temp.bch_manage_avg_cnt),1) else 0 end desc) rank
                    from phoenix.temp_safes_stats_new_year_2024 temp 
                    where temp.pt = '${lastDay}'
                    GROUP by temp.area_name,temp.bch_name
                ) a 
                where rank < '${rank}' order by rank desc limit 1
                       """
        Map<String, Object> resultPre = msgStatService.selectSafesPgMap(sqlPre);
        if (resultPre != null) {
            return resultPre.get("activitypolicycountavg") - result.get("activitypolicycountavg");
        }
    }
    return BigDecimal.ZERO;
}

BigDecimal getAreaPreDiff(Map<String, Object> result,String lastDay) {
    if (result == null) {
        return BigDecimal.ZERO;
    }
    //获取与上一排名的人均单量差值
    def rank = result.get("rank");
    if (rank != 1) {
        def sqlPre = """
                select 
                    a.area_name regionName,
                    round(a.personCount,1) personCount,
                    a.activityPolicyCount, 
                    round(a.activityPolicyTarget_21,1) activityPolicyTarget21,
                    round(a.activityPolicyTarget_28,1) activityPolicyTarget28,
                    a.activityPolicyCountAvg,
                    round(a.activityPolicyCount/a.activityPolicyTarget_21*100,1) completionRate21,
                    round(a.activityPolicyCount/a.activityPolicyTarget_28*100,1) completionRate28,
                    21 activityPolicyAvgTarget21,
                    28 activityPolicyAvgTarget28,
                    rank
                FROM  (
                    select 
                        area_name,
                        sum(temp.total_insured-temp.total_surrender) activityPolicyCount,
                        max(temp.area_manage_avg_cnt)*21 activityPolicyTarget_21,
                        max(temp.area_manage_avg_cnt)*28 activityPolicyTarget_28,
                        max(temp.area_manage_avg_cnt) personCount,
                        round((sum(temp.total_insured-temp.total_surrender))/max(temp.area_manage_avg_cnt),1) activityPolicyCountAvg,
                        rank() over(order by round((sum(temp.total_insured-temp.total_surrender))/max(temp.area_manage_avg_cnt),1) desc) rank
                    from phoenix.temp_safes_stats_new_year_2024 temp 
                    where temp.pt = '${lastDay}' 
                    and area_name like '%区域'
                    GROUP by area_name
                ) a 
                where rank < '${rank}' order by rank desc limit 1
                       """
        Map<String, Object> resultPre = msgStatService.selectSafesPgMap(sqlPre);
        if (resultPre != null) {
            return resultPre.get("activitypolicycountavg") - result.get("activitypolicycountavg");
        }
    }
    return BigDecimal.ZERO;
}