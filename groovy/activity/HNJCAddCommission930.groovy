package groovy.activity

import com.google.common.collect.Lists
import com.google.common.collect.Maps


List<Map<String, Object>> execute(String params) {
    String sql = """  with cnt as (select  recommend_user_id,sum(case when app_status = '4' then -1 else 1 end) policyCnt
           from phoenix.dwd_insurance_order_commission_short t
             where product_id = 1062
             and t.payment_Time >= '2024-07-01 00:00:00' and t.payment_Time <='2024-10-01 00:00:00'
             and is_loan_flag = 0 and is_valid_loan = 0 
             and (village_activity is null or village_activity ='')
          group by recommend_user_id ) 
        select t.recommend_user_id,fh_order_id,10 as proportion,
                    CONCAT_WS('|',fh_order_id,policy_no,insured_id_number,plan_Id,risk_id,1) AS dataId,
                    CONCAT_WS('|',fh_order_id,policy_no,insured_id_number,plan_Id ,risk_id,1) AS  uuid
                    FROM phoenix."dwd_insurance_order_commission_short" t
        left join cnt on cnt.recommend_user_id = t.recommend_user_id 
        where product_id = 1062 
        and account_time >= '2024-07-01 00:00:00' and account_time < '2024-10-01 00:00:00'
        and app_status = '1'
        and cnt.policyCnt >= 10
        and is_loan_flag = 0 and is_valid_loan = 0 
        and (village_activity is null or village_activity ='')"""
    return msgStatService.selectSafesPgMaps(sql)
}
