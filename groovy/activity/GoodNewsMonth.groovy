import com.alibaba.fastjson.JSON
import com.cfpamf.ms.insur.operation.msg.pojo.dto.OpMessageRuleGroovyDTO
import com.cfpamf.ms.insur.operation.msg.util.Html2ImageUtils
import org.apache.commons.collections4.CollectionUtils


Map execute(OpMessageRuleGroovyDTO rule) {
    def ftlParam = [:]

    def sql = """
select t.*
FROM (
SELECT t2.regionName, t2.organizationName, t2.userName,t1.recommendId,
SUM(t.conversion_amount) AS amounts
    FROM (
		SELECT *
		FROM sm_commission_detail
        WHERE account_time >= DATE_FORMAT(CURDATE(), '%Y-%m-01')
AND account_time < DATE_FORMAT(DATE_ADD(DATE_FORMAT(CURDATE(), '%Y-%m-01'), INTERVAL 1 MONTH), '%Y-%m-01')
		) t 
    LEFT JOIN sm_order t1 ON t.order_id = t1.fhOrderId
    LEFT JOIN auth_user t2 ON t1.recommendId = t2.userId
    WHERE t1.recommendId is not null
    GROUP BY t1.recommendId
    HAVING amounts >= 100000) t
left join op_message_push t1 on t.recommendId=t1.biz_code 
and t1.receiver_Name = '${rule.receiver.receiverName}'
and t1.message_rule_id= '${rule.id}' 
and t1.create_time >= DATE_FORMAT(CURDATE(), '%Y-%m-01')
AND t1.create_time < DATE_FORMAT(DATE_ADD(DATE_FORMAT(CURDATE(), '%Y-%m-01'), INTERVAL 1 MONTH), '%Y-%m-01')
where t1.id is null  and t.regionName != '总部';
    """

    ftlParam["datas"] = msgStatService.selectMaps(sql)

    println ftlParam["datas"]
    if (CollectionUtils.isEmpty(ftlParam["datas"] as Collection<?>)) {
        return null
    }
    def top3=ftlParam["datas"]
    def dingInfos = wecatService.getDingTalkInfo(top3*.recommendId)
    (ftlParam["datas"] as Collection<?>).stream().forEach {
        def we = dingInfos.find { dingInfo -> Objects.equals(dingInfo.wechatUserId, it.recommendId) }
        if (Objects.nonNull(we)){
            it["avatar"] = we.avatar
        }
    }
    def resultMap = [:]  // 使用 Map 保存结果

    ftlParam["datas"].each { data ->
        def userId = data["recommendId"]
        def ftlMap = ["datasJson": JSON.toJSONString(data)]
        println ftlMap
        // 存储 userId 对应的数据行
        resultMap[userId] = Html2ImageUtils.processTemplateInfoString(rule.getMessageTemplate(), ftlMap)
    }
    println resultMap
    return resultMap
}
