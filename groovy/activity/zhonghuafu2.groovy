package groovy.activity

List<Map<String, Object>> execute(String params) {
    String sql = """SELECT
( CASE t2.`pay_period` WHEN 15 THEN 5 ELSE 15 END ) AS proportion,
CONCAT_WS('|',t1.`fhOrderId`,t3.`policy_no`,t3.`insured_id_number`  ,t1.`planId` ,t3.risk_id,1) AS dataId,
CONCAT_WS('|',t1.`fhOrderId`,t3.`policy_no`,t3.`insured_id_number` ,t1.`planId` ,t3.risk_id,1) AS  uuid 
FROM
sm_order t1
LEFT JOIN `sm_order_policy` t2 ON t1.`fhOrderId` = t2.`fh_order_id` 
LEFT JOIN sm_order_risk_duty t3 on t1.`fhOrderId` = t3.`fh_order_id` 
WHERE
`productId` = 270 
AND t1.`create_time` >= '2022-04-13 00:00:00'
AND t1.`create_time` <= '2022-06-01 00:00:00'
AND t2.`pay_type` = '2' AND t2.`pay_period` in( '15','19','20','30') 
AND t2.`policy_state` = 1 
AND t1.`payStatus` = 2
and t3.`risk_id`  in (47,48,50)
AND t3.`app_status` = 1 """
    return msgStatService.selectMaps(sql)
}
