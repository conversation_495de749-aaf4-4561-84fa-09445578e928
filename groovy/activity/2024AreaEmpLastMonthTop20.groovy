import com.alibaba.fastjson.JSON
import com.cfpamf.ms.insur.operation.base.constant.BaseConstants
import com.cfpamf.ms.insur.operation.msg.pojo.dto.OpMessageRuleGroovyDTO
import com.cfpamf.ms.insur.operation.msg.util.Html2ImageUtils
import org.apache.commons.collections4.CollectionUtils

import java.time.LocalDate
import java.time.format.DateTimeFormatter


Map execute(OpMessageRuleGroovyDTO rule) {
    log.info("==========================start1================================")
    def pt = LocalDate.now().minusDays(1L).format(BaseConstants.FMT_DATE)
    def areaCodeListString = rule.areaCodeListString
    log.info("==========================start================================"+areaCodeListString)
    def resultMap = [:]  // 使用 Map 保存结果
    //防止重跑
    def bizCode = pt
    def queryResultSafes = getPushResult(pt,rule);
    if (!CollectionUtils.isEmpty(queryResultSafes)) {
        return resultMap
    }
    //防止重跑
    def queryResult = getCurTop20(20,pt,areaCodeListString);
    def paramsMap = [:]  // 使用 Map 保存结果
    def date = LocalDate.now().minusDays(1L).format(DateTimeFormatter.ofPattern("yyyy-MM-dd"))
    paramsMap["datestring"] = date
    def isFirstWeek = true
    queryResult.forEach {
        def recommendEmpId = it["recommend_emp_id"]
        it["isFirstWeek"] = isFirstWeek
        if (CollectionUtils.isEmpty(queryResultSafes)) {
            def dingInfos = wecatService.getDingTalkInfo([recommendEmpId])
            def we = dingInfos.find { dingInfo -> Objects.equals(dingInfo.wechatUserId, recommendEmpId) }
            if (Objects.nonNull(we)) {
                it["avatar"] = we.avatar
            }
        }
    }
    def dataJSON = JSON.toJSONString(queryResult)
    paramsMap["dataJSON"] = dataJSON
    def htmlString = Html2ImageUtils.processTemplateInfoString(rule.getMessageTemplate(), paramsMap)
    log.info("---------------- {}",JSON.toJSONString(paramsMap))
    log.info("+++++++++++++++++++++++++++");
    log.info("htmlString================" + htmlString);
    log.info("+++++++++++++++++++++++++++");
    resultMap.put(bizCode, htmlString)
    return resultMap

}

def isFirstWeek() {
    String currentDay = LocalDate.now().getDayOfMonth();
    def array = ["1","2","3"];
    log.info("currentDay= {}",currentDay);
    if(array.contains(currentDay)){
        return "true"
    }
    return "false"
}

def getCurTop20(def rankSize, def pt, def areaCodeListString){
    def sql = """
    select tmp.money as wanmoney,tmp.* from (
        select sum(a.assess_convert_insurance_amt) as money,to_char(current_date,'YYYY-MM-DD') as datestring,rank() over(order by sum(a.assess_convert_insurance_amt) desc ) as ranks,sum(a.assess_convert_insurance_amt) as assess_convert_insurance_amt,a.recommend_emp_id,a.recommend_emp_name
        ,a.bch_code,a.bch_name,a.area_code,a.area_name  
        from phoenix.ads_insurance_policy_index_progress_dfp a 
        where 
          a.pt = '${pt}'
          and a.trade_time >= cast(to_char((current_date - interval '1 month'),'yyyy-mm-01') as timestamp)
          and a.trade_time < current_date
          and a.area_code in (${areaCodeListString})
          GROUP BY a.recommend_emp_id,a.recommend_emp_name,a.bch_code,a.bch_name,a.area_code,a.area_name 
    ) tmp where tmp.ranks<=${rankSize};
    """
    log.info("==========================start================================"+sql)
    def queryResult = msgStatService.selectSafesPgMaps(sql);
    return queryResult
}

def getPushResult(def bizCode,OpMessageRuleGroovyDTO rule){
    def sqlSafes = """
    select * from op_message_push where `biz_code` = '${bizCode}'  and `rule_code` = '${rule.ruleCode}' and receiver = '${rule.receiver.receiver}' limit 1
   """
    log.info("==========================sqlSafes================================"+sqlSafes)
    def queryResultSafes = msgStatService.selectMaps(sqlSafes);
    return queryResultSafes
}
