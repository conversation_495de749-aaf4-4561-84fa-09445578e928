import com.alibaba.fastjson.JSON
import com.cfpamf.ms.insur.operation.base.constant.BaseConstants
import com.cfpamf.ms.insur.operation.msg.pojo.dto.OpMessageRuleGroovyDTO
import com.cfpamf.ms.insur.operation.msg.util.Html2ImageUtils
import org.apache.commons.collections4.CollectionUtils

import java.time.LocalDate
import java.time.format.DateTimeFormatter


Map execute(OpMessageRuleGroovyDTO rule) {
    def resultMap = [:]  // 使用 Map 保存结果
    def localDate = LocalDate.now()
    log.info("localDate= {}",localDate.format(BaseConstants.FMT_DATE))
    if(isBreak(localDate)){
        log.info("非1-3号不跑数据")
        return resultMap
    }
    def pt = localDate.minusDays(1L).format(BaseConstants.FMT_DATE)
    log.info("localDate= {}; pt= {}",localDate.format(BaseConstants.FMT_DATE),localDate.minusDays(1L).format(BaseConstants.FMT_DATE))
    DateTimeFormatter yyyyMM = DateTimeFormatter.ofPattern("yyyy-MM")
    DateTimeFormatter yyyyMMdd = DateTimeFormatter.ofPattern("yyyy-MM-dd")
    def monthFirstDay = localDate.format(yyyyMM)+"-01";
    def currentDay = localDate.format(yyyyMMdd);
    def last7Day = localDate.minusDays(7L).format(yyyyMMdd);
    def areaCodeListString = rule.areaCodeListString

    //防止重跑
    def bizCode = pt
    def queryResultSafes = getPushResult(pt,rule);
    if (!CollectionUtils.isEmpty(queryResultSafes)) {
        return resultMap
    }

    def isFirstWeek = isFirstWeek(localDate);
    log.info("isFirstWeek= {}",isFirstWeek)
    def queryResult = getCurTop20(20,pt,areaCodeListString,monthFirstDay,currentDay);
    def recommendIdList = queryResult.collect{x-> return x["bch_code"]}
    def paramsMap = [:]  // 使用 Map 保存结果
    def date = localDate.minusDays(1L).format(DateTimeFormatter.ofPattern("yyyy-MM-dd"))
    paramsMap["datestring"] = date

    def lastWeekQueryResult = getLastTop20(pt,areaCodeListString,recommendIdList,monthFirstDay,last7Day);

    def lastWeekQueryResultMap = lastWeekQueryResult.collectEntries{ it ->
        [(it["bch_code"]): it]
    }
    log.info("lastWeekQueryResultMap= {}",JSON.toJSONString(lastWeekQueryResultMap))
    def lastData = null

    queryResult.forEach {
        it["isFirstWeek"] = isFirstWeek;
        def bchCode = it["bch_code"]
        lastData = lastWeekQueryResultMap[bchCode];
        it = rankHandle(it,lastData);
    }

    def dataJSON = JSON.toJSONString(queryResult)
    paramsMap["dataJSON"] = dataJSON
    def htmlString = Html2ImageUtils.processTemplateInfoString(rule.getMessageTemplate(), paramsMap)
    log.info("htmlString================" + htmlString);
    resultMap.put(bizCode, htmlString)
    return resultMap
}
def isBreak(def localDate){
    int currentDay = localDate.getDayOfMonth();
    def array = [1,2,3];
    if(array.contains(currentDay)){
        return true
    }
    int weekDay = localDate.getDayOfWeek().getValue()
    if(!Objects.equals(5,weekDay)){
        return true
    }
    return false
}

def getCurTop20(def rankSize, def pt, def areaCodeListString, def monthFirstDay,def currentDay){
    def sql = """
    select tmp.money as wanmoney,tmp.* from (
        select sum(a.assess_convert_insurance_amt) as money,to_char(current_date,'YYYY年MM月DD日') as datestr,rank() over(order by sum(a.assess_convert_insurance_amt) desc ) as ranks,sum(a.assess_convert_insurance_amt) as assess_convert_insurance_amt
        ,a.bch_code,a.bch_name,a.area_code,a.area_name  
        from phoenix.ads_insurance_policy_index_progress_dfp a 
        where 
          a.pt = '${pt}'
          and a.trade_time >= '${monthFirstDay}'
          and a.trade_time < '${currentDay}'
          and a.area_code in (${areaCodeListString})
          GROUP BY a.bch_code,a.bch_name,a.area_code,a.area_name 
    ) tmp where tmp.ranks<=${rankSize};
    """
    log.info("==========================start================================"+sql)
    def queryResult = msgStatService.selectSafesPgMaps(sql);
    return queryResult
}

def getPushResult(def bizCode,OpMessageRuleGroovyDTO rule){
    def sqlSafes = """
    select * from op_message_push where `biz_code` = '${bizCode}'  and `rule_code` = '${rule.ruleCode}' and receiver = '${rule.receiver.receiver}' limit 1
   """
    log.info("==========================sqlSafes================================"+sqlSafes)
    def queryResultSafes = msgStatService.selectMaps(sqlSafes);
    return queryResultSafes
}

def getLastTop20(def pt, def areaCodeListString,def recommendIdList,def monthFirstDay,def last7Day){
    def recommendIdStr = buildRecommendIdSql(recommendIdList)
    log.info("recommendIdStr= {}",recommendIdStr)
    def sql = """
    select ROUND((tmp.money)/10000,1) as wanmoney,tmp.* from (
        select sum(a.assess_convert_insurance_amt) as money,to_char(current_date,'YYYY年MM月DD日') as datestr,rank() over(order by sum(a.assess_convert_insurance_amt) desc ) as ranks,sum(a.assess_convert_insurance_amt) as assess_convert_insurance_amt
        ,a.bch_code,a.bch_name,a.area_code,a.area_name  
        from phoenix.ads_insurance_policy_index_progress_dfp a 
        where 
          a.pt = '${pt}'
          and a.trade_time >= '${monthFirstDay}'
          and a.trade_time < '${last7Day}'
          and a.area_code in (${areaCodeListString})
          GROUP BY a.bch_code,a.bch_name,a.area_code,a.area_name 
    ) tmp where tmp.bch_code in (${recommendIdStr})
    """
    log.info("==========================start================================"+sql)
    def queryResult = msgStatService.selectSafesPgMaps(sql);
    return queryResult
}

def isFirstWeek(def localDate) {
    //当前时间-7天
    int currentMonth = localDate.getMonthValue();
    int lastWeekMonth = localDate.minusDays(7L).getMonthValue();
    if(Objects.equals(currentMonth,lastWeekMonth)){
        return false
    }else{
        return true
    }
}

String buildRecommendIdSql(def queryResult){
    if(CollectionUtils.isEmpty(queryResult)){
        return null
    }
    def sb = new StringBuffer("");
    queryResult.forEach{
        sb.append("'"+it+"',")
    }
    return sb.substring(0,sb.length()-1).toString()
}

def rankHandle(def curData, def lastData){
    log.info("curData= {}; lastData= {}",JSON.toJSONString(curData),JSON.toJSONString(lastData))
    //rankStatus  0 不升不降  1 上升  2 下降
    def rankStatus = null
    if(Objects.isNull(lastData) || Objects.isNull(lastData["ranks"])){
        rankStatus = 0
        curData["rankStatus"] = rankStatus
        log.info("rankHandle curData= {}",JSON.toJSONString(curData))
        return curData
    }
    log.info("curData ranks= {} ; lastData ranks= {}",curData["ranks"],lastData["ranks"])
    if(curData["ranks"] > lastData["ranks"]){
        rankStatus = 2
    }else if(curData["ranks"] < lastData["ranks"]){
        rankStatus = 1
    }else{
        rankStatus = 0
    }
    curData["rankStatus"] = rankStatus
    log.info("rankHandle curData= {}",JSON.toJSONString(curData))
    return curData
}