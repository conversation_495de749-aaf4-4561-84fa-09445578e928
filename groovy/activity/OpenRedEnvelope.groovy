package groovy.activity

import com.cfpamf.ms.insur.operation.activity.form.ValidActivityProductRuleForm
import com.google.common.collect.Lists


List<Map<String, Object>> execute(ValidActivityProductRuleForm params) {
    def couponsOrderMessage = params.getCouponsOrderMessage()

    int productId = couponsOrderMessage.getProductId()
    if (productId == 178 || productId == 210 || productId == 167) {
        println("订单属于赠险或者妈咪保贝")
        return Lists.newArrayList()
    }
    String queryProductSql = """ SELECT productAttrCode,productType from sm_product WHERE   id = ${productId};"""
    Map<String, String> productInfo = msgStatService.selectMap(queryProductSql)
    String productAttrCode = productInfo.get("productAttrCode")
    String productType = productInfo.get("productType")
    if (productAttrCode == "person" && productType == "accident") {
        println("订单属于个意险")
        return Lists.newArrayList();
    }
    def orderId = couponsOrderMessage.getFhOrderId()
    String querySql = """SELECT dataId,'USER' as dataType,  1 as proportion ,  CONCAT('ORDER_ID-',fhOrderId) as uuid  from 
(
SELECT t1.fhOrderId  , t1.customerAdminId as dataId, t1.totalAmount as totalAmount
from sm_order t1 
LEFT JOIN  sm_order_insured t2 on t1.fhOrderId = t2.fhOrderId
LEFT JOIN sm_order_item t7 ON t2.fhOrderId = t7.fh_order_id
            AND t2.idNumber = t7.id_number
            AND t2.appStatus = t7.app_status
WHERE t1.payStatus = '2' 
and t1.create_time >='2022-01-01 00:00:00' and  t1.create_time <= '2022-02-28 23:59:59' and  t1.totalAmount >= 300 
and t1.fhOrderId = '${orderId}' 
GROUP BY t1.fhOrderId 
)  a """

    return msgStatService.selectMaps(querySql)

}



