package groovy.activity


import org.apache.commons.lang3.StringUtils

Object execute(String params) {

    Map<String, Object> allParams = jacksonObjectMapper.readValue(params, Map.class);

    //获取个人弹窗的
    if (Objects.equals(String.valueOf(allParams.get("type")), "1")) {
        return getOrgPopUpData(allParams);
    } else if (Objects.equals(String.valueOf(allParams.get("type")), "2")) {
        //获取机构弹窗的
        return getOrgPopUpData(allParams);
    } else if (Objects.equals(String.valueOf(allParams.get("type")), "3")) {
        //获取详情的
        return getDetailData(allParams);
    } else if (Objects.equals(String.valueOf(allParams.get("type")), "4")) {
        //获取机构排名的
        return getOrgDetailTable(allParams);
    }

    return null;
}

/**
 * 弹窗机构数据
 * @param params
 * @return
 */
Map<String, Object> getOrgPopUpData(Map<String, Object> params) {

    String orgName = params.get("orgName");
    String regionName = params.get("regionName");
    String sql
    if (params.roleType == 1) {
        sql = """ 
     SELECT  
    COALESCE(sy_ms_emp_cnt,0) AS "syMsEmpCnt",
    COALESCE(sy_land_customer_cnt,0) as "syLandCustomerCnt",
		cast(COALESCE(ROUND(sy_land_customer_cnt/sy_ms_emp_cnt::NUMERIC, 3),0) as decimal(10, 1)) as "perMonth",
		first_loan_date "firstLoanDate",
		case when EXTRACT(YEAR FROM first_loan_date::date)=EXTRACT(YEAR FROM now()::date)
		then cast(COALESCE(ROUND(sy_land_customer_cnt/sy_ms_emp_cnt::NUMERIC, 3)*(EXTRACT(MONTH FROM now()::date)-EXTRACT(MONTH FROM first_loan_date::date)+1),0) as decimal(10, 1))
		else 
    cast(COALESCE(ROUND(sy_land_customer_cnt/sy_ms_emp_cnt::NUMERIC, 3)*EXTRACT(MONTH FROM now()::date),0) as decimal(10, 1)) end as "perLandCustomerCnt",
		case when EXTRACT(YEAR FROM first_loan_date::date)=EXTRACT(YEAR FROM now()::date)
		then true else false end isNewOrg,
    area_name AS "areaName",bch_name AS "branchName",
    bch_leader_name AS "bchLeaderName"
    FROM insurance.ads_insurance_bch_index_progress_dfp WHERE
    1=1
    """
    } else {
        sql = """ 
    SELECT  
    area_name AS "areaName",
    COALESCE(sy_ms_emp_cnt,0) AS "syMsEmpCnt",
    COALESCE(sy_land_customer_cnt,0) as "syLandCustomerCnt",
		case when area_code = 'Z4618781' then 
		cast(COALESCE(ROUND(sy_land_customer_cnt/sy_ms_emp_cnt::NUMERIC, 3)*(EXTRACT(MONTH FROM now()::date)-3),0) as decimal(10, 1))
		else 
		cast(COALESCE(ROUND(sy_land_customer_cnt/sy_ms_emp_cnt::NUMERIC, 3)*EXTRACT(MONTH FROM now()::date),0) as decimal(10, 1)) end
		as "perLandCustomerCnt"
    FROM insurance.ads_insurance_area_index_progress_dfp WHERE
    1=1
    """
    }
    if (orgName != null && params.roleType == 1) {
        sql = "$sql and bch_name='${orgName}'"
    }
    if (regionName != null && params.roleType == 2) {
        sql = "$sql and area_name='${regionName}'"
    }
    return msgStatService.selectDwMinerMap(sql);
}


Map<String, Object> getDetailData(Map<String, Object> params) {

    Map<String, Object> result = new HashMap<>();

    result.put("orgPopData", getOrgPopUpData(params));

    return result;
}


Object getOrgDetailTable(Map<String, Object> params) {
    return new HashMap();
}

