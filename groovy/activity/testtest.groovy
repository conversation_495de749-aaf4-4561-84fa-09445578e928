package groovy.activity

import com.cfpamf.ms.insur.operation.msg.service.MsgStatService
import org.apache.commons.lang3.StringUtils

List<Map<String, Object>> execute(String params) {

    MsgStatService s = msgStatService;
    String sql = """
with t as (select order_id,
                  t.id,
                  insured_id_number,
                  amount,
                  policy_status,
                  concat_ws('|', order_id, policy_no, insured_id_number, plan_id, '', term_num) data_id,
                  lead(t.amount)
                       over ( partition by SUBSTRING_INDEX(order_id, '_', 1),insured_id_number order by
                           cast(if(instr(t.order_id, '_') = 0, 0,
                                   SUBSTRING(t.order_id, instr(t.order_id, '_') + 1)) as signed))
                                                                                                sub_amt,
                  lead(concat_ws('|', order_id, policy_no, insured_id_number, plan_id, '', term_num)) over w
                                                                                                sub_data_id
           from sm_commission_detail t
                    left join sm_plan t1 on t.plan_id = t1.id
           where t.account_time >= str_to_date('2023-03-01', '%Y-%m-%d')
             and t.account_time < str_to_date('2023-05-01', '%Y-%m-%d')
             AND t1.productId IN (251,180,326,321,219)
               window w as (
                   partition by SUBSTRING_INDEX(order_id, '_', 1),insured_id_number order by
                       cast(if(instr(t.order_id, '_') = 0, 0,
                               SUBSTRING(t.order_id, instr(t.order_id, '_') + 1)) as signed)))
select order_id,
       sum(t.amount)                          all_amt,
       sum(t.sub_amt)                         sub_amt,
       sum((t.amount - ifnull(t.sub_amt, 0))) cal_amt,
       group_concat(data_id)                  data_id,
       group_concat(sub_data_id)              sub_data_id,
       case
           when sum((t.amount - ifnull(t.sub_amt, 0))) < 5000 then 0
           when sum((t.amount - ifnull(t.sub_amt, 0))) >= 5000 and sum((t.amount - ifnull(t.sub_amt, 0))) <= 10000
               then 3
           else 5 end                         proportion
from t
where t.policy_status = '1'
group by order_id"""
    def maps = s.selectMaps(sql)
    def res = []
    maps.forEach {

        def pit = it
        if (StringUtils.isNotEmpty(it.sub_data_id as CharSequence)) {
            def subDataIds = StringUtils.split(it.sub_data_id as String, ",")
            res.addAll(subDataIds.collect { ['proportion': pit.proportion, 'dataId': it, 'uuid': it] })
        }
        if (StringUtils.isNotEmpty(it.data_id as CharSequence)) {
            def addIds = StringUtils.split(it.data_id as String, ",")
            res.addAll(addIds.collect { ['proportion': pit.proportion, 'dataId': it, 'uuid': it] })
        }
    }
    return res
}
