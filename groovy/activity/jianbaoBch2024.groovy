import com.cfpamf.ms.insur.operation.base.constant.BaseConstants
import com.cfpamf.ms.insur.operation.msg.pojo.dto.OpMessageRuleGroovyDTO
import com.google.common.collect.Lists
import groovy.json.JsonOutput

import java.time.LocalDate
import java.time.YearMonth
import java.time.temporal.TemporalAdjusters
import java.time.format.DateTimeFormatter
import java.time.DayOfWeek

String execute(OpMessageRuleGroovyDTO rule) {
    log.info("参数 {}",rule)
    def receivers = rule.getReceivers();
    log.info("接收者信息 {}",receivers[0]["receiver"])

    def orgNameList = receivers.collect{ it.otherParams.orgName };
    log.info("机构信息 {}",orgNameList)

    def pt = LocalDate.now().minusDays(1L).format(BaseConstants.FMT_DATE)
    //7天前
    def before7pt = LocalDate.now().minusDays(7L).format(BaseConstants.FMT_DATE)
    log.info("七天前 {}",before7pt)

    //顶部看板（根据机构分组后的结果集）
    def bchDatas = getTop(pt,orgNameList);
    log.info("bch areaData {} {} {}",bchDatas,orgNameList, pt)
    //7天前的业绩达成数据（根据区域分组后的结果集）
    def bchData7BeforeDatas = getTop(before7pt,orgNameList)
    log.info("bch areaData7Before {} {} {}",bchData7BeforeDatas,orgNameList, pt)
    //标准保费趋势图
    def tendency = getTendency(orgNameList)
    log.info("bch tendency {} {} {}",tendency,orgNameList, pt)
    //标准保费、交叉销售比、留存率版块
    def smAssessConvertInsuranceDatas = getSmAssessConvertInsurance(pt,orgNameList)
    log.info("bch smAssessConvertInsurance {} {} {}",smAssessConvertInsuranceDatas,orgNameList, pt)
    //活动版块
    def activityDatas = getActivityData(pt,orgNameList)
    log.info("bch activity {} {} {}",activityDatas,orgNameList, pt)

    def jsonData = [:]
    // 获取当前日期
    LocalDate today = LocalDate.now()
    // 获取上周一的日期
    LocalDate lastMonday = today.with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY)).minusWeeks(1)
    String formattedLastMonday = lastMonday.format(DateTimeFormatter.ofPattern("MM.dd"))
    // 获取上周日的日期
    LocalDate thisSunday = today.with(TemporalAdjusters.nextOrSame(DayOfWeek.SUNDAY)).minusWeeks(1)
    String formattedLastSunday = thisSunday.format(DateTimeFormatter.ofPattern("MM.dd"))
    receivers.each { receiver ->
        def orgName = receiver.otherParams.orgName
        def bchData = bchDatas[orgName][0]
        log.info("区域信息 {}",bchData)
        def bchData7BeforeData = bchData7BeforeDatas[orgName][0]

        def amt7before = bchData.syassessconvertinsuranceamt-bchData7BeforeData.syassessconvertinsuranceamt;
        def amt7beforeStr = "";
        if (amt7before >= 10000) {
            def number = (amt7before as double) / 10000
            amt7beforeStr = new BigDecimal(number).setScale(2, BigDecimal.ROUND_HALF_UP).toString()+"万元"
        } else {
            amt7beforeStr = amt7before+"元"
        }

        def smassessconvertinsuranceamt = bchData.smassessconvertinsuranceamt;
        def smassessconvertinsuranceamtStr = "";
        if (smassessconvertinsuranceamt >= 10000) {
            smassessconvertinsuranceamtStr = bchData.smassessconvertinsuranceamtw + "万元"
        } else {
            smassessconvertinsuranceamtStr = smassessconvertinsuranceamt+"元"
        }

        def syassessconvertinsuranceamt = bchData.syassessconvertinsuranceamt;
        def syassessconvertinsuranceamtStr = "";
        if (syassessconvertinsuranceamt >= 10000) {
            syassessconvertinsuranceamtStr = bchData.syassessconvertinsuranceamtw + "万元"
        } else {
            syassessconvertinsuranceamtStr = syassessconvertinsuranceamt+"元"
        }
        def jsonString = getComplete(tendency,orgName)

        def smAssessConvertInsuranceData = smAssessConvertInsuranceDatas[orgName][0]
        def activityData = activityDatas[orgName][0]
        def cardParamMap = [
                title: formattedLastMonday+"-"+formattedLastSunday+" "+orgName+"周简报",
                last7DaysInsuranceAmt: amt7beforeStr,
                smAssessConvertInsuranceAmt: smassessconvertinsuranceamtStr,
                smAssessConvertInsuranceAmtCchieveRate: bchData.smassessconvertinsuranceamtcchieverate+"%",
                smAssessConvertInsuranceAmtAvg: bchData.smassessconvertinsuranceamtavg+"",
                syAssessConvertInsuranceAmt: syassessconvertinsuranceamtStr,
                syAssessConvertInsuranceAmtAchieveRate: bchData.syassessconvertinsuranceamtachieverate+"%",
                syAssessConvertInsuranceAmtAvg: bchData.syassessconvertinsuranceamtavg+"",
                chartData: jsonString,
                insuranceAmtMsg: "<font sizeToken=common_footnote_text_style__font_size>当月标准保费达成"+smassessconvertinsuranceamtStr+"(全国排名第"+smAssessConvertInsuranceData.amtrank+")，达成率"+smAssessConvertInsuranceData.smassessconvertinsuranceamtachieverate+"%(全国排名第"+smAssessConvertInsuranceData.rank+")"+smAssessConvertInsuranceData.insuranceamtmsg+"</font>",
                loanInsuranceRateMsg: "<font sizeToken=common_footnote_text_style__font_size>当月交叉销售比"+smAssessConvertInsuranceData.smofflineloaninsurancerate+"，当年交叉销售比"+smAssessConvertInsuranceData.syofflineloaninsurancerate+""+smAssessConvertInsuranceData.insuranceratemsg+"</font>",
                insuranceRetentionRateMsg: "<font sizeToken=common_footnote_text_style__font_size>当月留存率"+smAssessConvertInsuranceData.sminsuranceretentionrate+"%，较上月"+smAssessConvertInsuranceData.retentionratedifference+"；当年留存率"+smAssessConvertInsuranceData.syinsuranceretentionrate+"%，做好用户留存，可持续稳定贡献保费，达成目标更轻松~</font>",
                acvitityTitle:"“鲸耕细作”",
                acvitityMsg: "<font sizeToken=common_footnote_text_style__font_size>【活动一】活动单量"+activityData.jinxiactivitypolicycount+"，人均单量"+activityData.jinxiactivitypolicycountavg+"，排名第"+activityData.amtrank+""+activityData.activity1msg+"【活动二】活动单量"+activityData.activitypolicycount+"，人均单量"+activityData.activitypolicycountavg+"，排名第"+activityData.rank+""+activityData.activity2msg+"</font>"
        ]
        log.info("cardParamMap {}",cardParamMap)
        def json = ["cardParamMap":cardParamMap]
        jsonData."${receiver.receiver}" = json
    }
    def result = new groovy.json.JsonBuilder()
    result {
        cardData {
            cardParamMap {}
        }
        privateData jsonData
    }
    log.info("输出结果 {}",result)
    return result.toString()
}


/**
 * 获取当前年份的每个月的月末日期
 *
 * @return 当前年份的每个月的月末日期列表
 */
String getEndDatesOfMonthsInCurrentYearAndFmt() {
    String pt = LocalDate.now().minusDays(1L).format(BaseConstants.FMT_DATE)
    //  相对日期
    LocalDate yesterday = LocalDate.parse(pt, BaseConstants.FMT_DATE);

    List<String> pts = Lists.newArrayListWithCapacity(12);
    pts.add(BaseConstants.FMT_DATE.format(yesterday));
    // 获取截止到昨天的所有月末日期   从今年的1月1日开始
    LocalDate start = LocalDate.of(yesterday.getYear(), 1, 1);

    while (start.isBefore(yesterday)) {
        YearMonth yearMonth = YearMonth.from(start);
        LocalDate endOfMonth = yearMonth.atEndOfMonth();
        if (endOfMonth.isBefore(yesterday) || endOfMonth.equals(yesterday)) {
            pts.add(BaseConstants.FMT_DATE.format(endOfMonth));
        }
        // 移动到下一个月份
        start = start.plusMonths(1);
    }
    return "('" + pts.join("', '") + "')";
}

//根据pt获取顶部看板：标准保费，完成率等
Map<String,Map<String, Object>> getTop(String pt,List<String> orgNameList){
    def inOrgNameClause = "('" + orgNameList.join("', '") + "')"
    def sql = """
    select  TO_CHAR(DATE_TRUNC('week', CURRENT_DATE - INTERVAL '1 week')::timestamp, 'MM.DD') AS startDate,
        TO_CHAR((now()::date - INTERVAL '1 day' * extract(DOW FROM now()))::timestamp, 'MM.DD') AS endDate,
        aiampd.area_name areaName,
        aiampd.bch_name bchName,
        ROUND(sm_assess_convert_insurance_amt::numeric,2) smAssessConvertInsuranceAmt,
        ROUND(sm_assess_convert_insurance_amt/10000::numeric,2) smAssessConvertInsuranceAmtW,
        ROUND((sm_assess_convert_insurance_amt_achieve_rate*100)::numeric,1) smAssessConvertInsuranceAmtCchieveRate,
        case when sm_assess_convert_insurance_amt_avg >= 10000 
        then CONCAT(ROUND((sm_assess_convert_insurance_amt_avg/10000)::numeric,2), '万元')
        else  CONCAT(ROUND(sm_assess_convert_insurance_amt_avg::numeric,2), '元') end smAssessConvertInsuranceAmtAvg,
        ROUND(sy_assess_convert_insurance_amt::numeric,2) syAssessConvertInsuranceAmt,
        ROUND(sy_assess_convert_insurance_amt/10000::numeric,2) syAssessConvertInsuranceAmtW,
        ROUND((sy_assess_convert_insurance_amt_achieve_rate*100)::numeric,1) syAssessConvertInsuranceAmtAchieveRate,
        case when sy_assess_convert_insurance_amt_avg >= 10000 
        then CONCAT(ROUND((sy_assess_convert_insurance_amt_avg/10000)::numeric,2), '万元')
        else  CONCAT(ROUND(sy_assess_convert_insurance_amt_avg::numeric,2), '元') end syAssessConvertInsuranceAmtAvg
    from report.ads_insurance_bch_marketing_progress_dfp aiampd 
    where pt = '${pt}' and bch_name in ${inOrgNameClause}
    """
    def result = msgStatService.selectSafesPgMaps(sql)
    log.info("区域简报顶部结果集 {}", JsonOutput.toJson(result))

    // 检查结果集是否为空
    if (result.isEmpty()) {
        // 初始化相同结构的对象列表
        def emptyObjects = []
        orgNameList.each { orgName ->
            def emptyObject = [
                    "bchname": orgName,
                    "smassessconvertinsuranceamt": 0,
                    "smassessconvertinsuranceamtw": 0,
                    "smassessconvertinsuranceamtcchieverate": 0,
                    "smassessconvertinsuranceamtavg": 0 ,
                    "syassessconvertinsuranceamt": 0,
                    "syassessconvertinsuranceamtw": 0,
                    "syassessconvertinsuranceamtachieverate": 0,
                    "syassessconvertinsuranceamtavg": 0 ,
            ]
            emptyObjects.add(emptyObject)
        }
        return emptyObjects.groupBy { it.bchname }
    }
    orgNameList.each { orgName ->
        //因为是批量查询所有区域，可能存在某个分支没值的情况，需要补充
        if (!result.any { map -> map.containsValue(orgName)}) {
            def emptyObject = [
                    "bchname": orgName,
                    "smassessconvertinsuranceamt": 0,
                    "smassessconvertinsuranceamtw": 0,
                    "smassessconvertinsuranceamtcchieverate": 0,
                    "smassessconvertinsuranceamtavg": 0 ,
                    "syassessconvertinsuranceamt": 0,
                    "syassessconvertinsuranceamtw": 0,
                    "syassessconvertinsuranceamtachieverate": 0,
                    "syassessconvertinsuranceamtavg": 0 ,
            ]
            result.add(emptyObject)
        }
    }
    def grouped = result.groupBy { it.bchname }
    return grouped
}

//根据pt获取顶部看板：标准保费，完成率等
Map<String,List<Map<String, Object>>> getTendency(List<String> orgNameList){
    def inOrgNameClause = "('" + orgNameList.join("', '") + "')"
    def pts = getEndDatesOfMonthsInCurrentYearAndFmt();
    def sql = """
    SELECT bch_name bchName,
        TO_CHAR(TO_DATE(pt, 'YYYYMMDD'), 'MM月') x,
        '标准保费（元）' as type,
        MAX(sm_assess_convert_insurance_amt) AS y 
        FROM report.ads_insurance_bch_marketing_progress_dfp
        where pt in ${pts}
          and  bch_name in ${inOrgNameClause}
        GROUP BY bch_name,
                 pt
        order by pt
    """
    def result = msgStatService.selectSafesPgMaps(sql)

    // 检查结果集是否为空
    if (result.isEmpty()) {
        // 初始化相同结构的对象列表
        def emptyObjects = []
        orgNameList.each { orgName ->
            def emptyObject = [
                    "bchname": orgName,
                    "x": "",
                    "type": "标准保费（元）",
                    "y": 0
            ]
            emptyObjects.add(emptyObject)
        }
        return emptyObjects.groupBy { it.bchname }.collectEntries { groupName, groupList ->
            [(groupName): groupList.collect { it.findAll { key, value -> key != 'bchname' } }]
        };
    }

    orgNameList.each { orgName ->
        //因为是批量查询所有区域，可能存在某个分支没值的情况，需要补充
        if (!result.any { map -> map.containsValue(orgName)}) {
            def emptyObject = [
                    "bchname": orgName,
                    "x": "",
                    "type": "标准保费（元）",
                    "y": 0
            ]
            result.add(emptyObject)
        }
    }
    return result.groupBy { it.bchname }.collectEntries { groupName, groupList ->
        [(groupName): groupList.collect { it.findAll { key, value -> key != 'bchname' } }]
    };
}

//根据pt获取标准保费、交叉销售比、留存率等信息
Map<String,Map<String, Object>> getSmAssessConvertInsurance(String pt,List<String> orgNameList){
    def inBchNameClause = "('" + orgNameList.join("', '") + "')"
    def sql = """
    select 
        areaName
        ,bchName
        ,smAssessConvertInsuranceAmt
        ,smAssessConvertInsuranceAmtAchieveRate smAssessConvertInsuranceAmtAchieveRate
        ,rank
        ,amtRank
        ,timeProgress
        ,pt
        ,case when smAssessConvertInsuranceAmtAchieveRate = timeProgress then '，符合预期，继续加油哦~'
        when smAssessConvertInsuranceAmtAchieveRate < timeProgress then '，落后序时进度' || difference || '%，继续努力，加油赶超哦~'
        else '，超过时间进度' || difference || '%，继续加油哦~' end as insuranceAmtMsg
        ,sm_offline_loan_insurance_rate smOfflineLoanInsuranceRate
        ,sy_offline_loan_insurance_rate syOfflineLoanInsuranceRate
        ,addAmt
        ,addTarget
        ,case when sm_offline_loan_insurance_rate < 85 then '，当月提升到全国平均水平85，当月标准保费至少可增加' || case when addAmt>=10000 then CONCAT(ROUND((addAmt/10000)::numeric,2), '万元') else CONCAT(ROUND(addAmt::numeric,2), '元') end ||  case when addTarget = 0.0 then '' else '，月完成率预计可提升' || addTarget || '%' end
        else '，达到标准值，继续保持~' end insuranceRateMsg
        ,sm_insurance_retention_rate smInsuranceRetentionRate
        ,sy_insurance_retention_rate syInsuranceRetentionRate
        ,lastMonthSmInsuranceRetentionRate lastMonthSmInsuranceRetentionRate
        ,ROUND((sm_insurance_retention_rate - lastMonthSmInsuranceRetentionRate)::numeric,1) || '%' as retentionRateDifference
    from (
    select  
        aiampd.area_name areaName,
        aiampd.bch_name bchName,
        sm_assess_convert_insurance_amt smAssessConvertInsuranceAmt,
        COALESCE(ROUND(sm_assess_convert_insurance_amt_achieve_rate*100::numeric,1), 0) smAssessConvertInsuranceAmtAchieveRate,
        a.rank,
        a.amt_rank amtRank,
        ROUND(((EXTRACT(DAY FROM CURRENT_DATE)/EXTRACT(DAY FROM DATE_TRUNC('MONTH', CURRENT_DATE) + INTERVAL '1 MONTH - 1 DAY'))*100)::numeric,1) timeProgress,
        aiampd.pt,
        abs(ROUND(((EXTRACT(DAY FROM CURRENT_DATE)/EXTRACT(DAY FROM DATE_TRUNC('MONTH', CURRENT_DATE) + INTERVAL '1 MONTH - 1 DAY'))*100)::numeric,1) 
        - ROUND((sm_assess_convert_insurance_amt_achieve_rate*100)::numeric,1)) as difference
        ,COALESCE(ROUND(sm_offline_loan_insurance_rate::numeric,1), 0) sm_offline_loan_insurance_rate
        ,COALESCE(ROUND(sy_offline_loan_insurance_rate::numeric,1), 0) sy_offline_loan_insurance_rate
        ,COALESCE(sm_assess_convert_insurance_amt_target, 0) sm_assess_convert_insurance_amt_target
        ,case when sm_offline_loan_insurance_rate=0 or sm_insurance_amt=0.0 or sm_assess_convert_insurance_amt=0.0 then 0.0
        else ROUND((sm_assess_convert_insurance_amt*10000/sm_offline_loan_insurance_rate*(85-sm_offline_loan_insurance_rate)/10000*sm_assess_convert_insurance_amt/sm_insurance_amt)::numeric,2) end addAmt
        ,case when sm_offline_loan_insurance_rate=0 or sm_insurance_amt=0.0 or sm_assess_convert_insurance_amt=0.0 or sm_assess_convert_insurance_amt_target = 0 then 0.0
        else ROUND((sm_assess_convert_insurance_amt*10000/sm_offline_loan_insurance_rate*(85-sm_offline_loan_insurance_rate)/10000*sm_assess_convert_insurance_amt/sm_insurance_amt/sm_assess_convert_insurance_amt_target*100)::numeric,1) end addTarget
        ,ROUND(aiampd.sm_insurance_retention_rate*100::numeric,1) sm_insurance_retention_rate
        ,ROUND(sy_insurance_retention_rate*100::numeric,1) sy_insurance_retention_rate
        ,case when last_month_data.sm_insurance_retention_rate is null then 0 else ROUND(last_month_data.sm_insurance_retention_rate*100::numeric,1) end lastMonthSmInsuranceRetentionRate
    from report.ads_insurance_bch_marketing_progress_dfp aiampd 
    left join (
        select
            t0.rank,
            t0.amt_rank,
            string_agg(t0.sm_assess_convert_insurance_amt_achieve_rate_rank_name, ',') as
            sm_assess_convert_insurance_amt_achieve_rate_rank_name
                from
                (
                    select
                        row_number() over(
                        partition by aiampd.pt
                        order by
                        aiampd.sm_assess_convert_insurance_amt_achieve_rate desc nulls last
                        ,
                        aiampd.bch_code asc
                        ) as rank,
                        row_number() over(
                        partition by aiampd.pt
                        order by
                        aiampd.sm_assess_convert_insurance_amt desc nulls last
                        ,
                        aiampd.bch_code asc
                        ) as amt_rank,
                        aiampd.bch_name as sm_assess_convert_insurance_amt_achieve_rate_rank_name
                    from
                    report.ads_insurance_bch_marketing_progress_dfp aiampd
                    where
                    aiampd.pt = '${pt}'
                ) as t0
                group by
                t0.rank,t0.amt_rank
                order by
                t0.rank asc,t0.amt_rank asc
            ) a on aiampd.bch_name = a.sm_assess_convert_insurance_amt_achieve_rate_rank_name 
        left join (
            select sm_insurance_retention_rate,bch_name from report.ads_insurance_bch_marketing_progress_dfp 
            where pt = TO_CHAR(DATE_TRUNC('month', CURRENT_DATE) - INTERVAL '1 day', 'YYYYMMDD')
        ) last_month_data on last_month_data.bch_name = aiampd.bch_name
        where aiampd.pt = '${pt}' and aiampd.bch_name in ${inBchNameClause}
    ) t
    """
    def result = msgStatService.selectSafesPgMaps(sql)
    log.info("看板获取交叉销售比、留存率结果集 {}",JsonOutput.toJson(result))

    // 检查结果集是否为空
    if (result.isEmpty()) {
        // 初始化相同结构的对象列表
        def emptyObjects = []
        orgNameList.each { orgName ->
            def emptyObject = [
                    "bchname": orgName,
                    "smassessconvertinsuranceamt": 0,
                    "smassessconvertinsuranceamtachieverate": 0,
                    "rank": 0,
                    "amtrank": 0 ,
                    "timeprogress": 0,
                    "pt": 0,
                    "insuranceamtmsg": "" ,
                    "smofflineloaninsurancerate" : 0,
                    "syofflineloaninsurancerate" : 0,
                    "addamt": 0,
                    "addtarget": 0,
                    "insuranceratemsg": "",
                    "sminsuranceretentionrate": 0,
                    "syinsuranceretentionrate": 0,
                    "lastmonthsminsuranceretentionrate": 0,
                    "retentionratedifference": 0,
            ]
            emptyObjects.add(emptyObject)
        }
        return emptyObjects.groupBy { it.bchname }
    }

    orgNameList.each { orgName ->
        //因为是批量查询所有区域，可能存在某个分支没值的情况，需要补充
        if (!result.any { map -> map.containsValue(orgName)}) {
            def emptyObject = [
                    "bchname": orgName,
                    "smassessconvertinsuranceamt": 0,
                    "smassessconvertinsuranceamtachieverate": 0,
                    "rank": 0,
                    "amtrank": 0 ,
                    "timeprogress": 0,
                    "pt": 0,
                    "insuranceamtmsg": "" ,
                    "smofflineloaninsurancerate" : 0,
                    "syofflineloaninsurancerate" : 0,
                    "addamt": 0,
                    "addtarget": 0,
                    "insuranceratemsg": "",
                    "sminsuranceretentionrate": 0,
                    "syinsuranceretentionrate": 0,
                    "lastmonthsminsuranceretentionrate": 0,
                    "retentionratedifference": 0,
            ]
            result.add(emptyObject)
        }
    }
    return result.groupBy { it.bchname }
}

//根据pt获取标准保费、交叉销售比、留存率等信息
Map<String,Map<String, Object>> getActivityData(String pt,List<String> orgNameList){
    def inBchNameClause = "('" + orgNameList.join("', '") + "')"
    def sql = """
        select a.bch_name bchName,
           cast(a.activityPolicyCount as int) activityPolicyCount, 
           a.activityPolicyTarget,
           round(a.activityPolicyCount/personCount,1) activityPolicyCountAvg,
           cast(jinxiActivityPolicyCount as int) jinxiActivityPolicyCount,
           round(a.jinxiActivityPolicyCount/personCount,1) jinxiActivityPolicyCountAvg,
           a.personCount,
           27 activityPolicyAvgTarget
           ,b.rank
           ,b.amt_rank amtRank
           ,CASE WHEN round(a.jinxiActivityPolicyCount/personCount,1)>=21 THEN '，请继续保持'
            ELSE '，人均差值' || 21-round(a.jinxiActivityPolicyCount/personCount,1) || '单，完成活动目标预计可增加保费' || case when (21*personCount-a.jinxiActivityPolicyCount)*300 >=10000 then CONCAT(ROUND(((21*personCount-a.jinxiActivityPolicyCount)*300/10000)::numeric,2), '万元')
        else  CONCAT(ROUND(((21*personCount-a.jinxiActivityPolicyCount)*300)::numeric,2), '元') end || '，需要加油哦~'
            END activity1Msg
            ,CASE WHEN round(a.activityPolicyCount/personCount,1)>=27 THEN '，请继续保持'
            ELSE '，人均差值' || 27-round(a.activityPolicyCount/personCount,1) || '单，完成活动目标预计可增加保费' || case when (27*personCount-a.activityPolicyCount)*300 >=10000 then CONCAT(ROUND(((27*personCount-a.activityPolicyCount)*300/10000)::numeric,2), '万元')
        else  CONCAT(ROUND(((27*personCount-a.activityPolicyCount)*300)::numeric,2), '元') end || '，需要加油哦~'
            END activity2Msg
        from (
            select bch_name,
                sum(temp.total_insured-temp.total_surrender) activityPolicyCount,
                sum(temp.jingxi_sanxing_quanjiafu_insured-temp.jingxi_sanxing_quanjiafu_surrender) jinxiActivityPolicyCount,
                max(temp.bch_manage_avg_cnt*27) activityPolicyTarget,
                max(temp.bch_manage_avg_cnt) personCount
            from phoenix.temp_safes_stats_new_year_2024 temp where temp.pt = '${pt}' GROUP by temp.bch_name
        ) a 
        left join (
            select
                activityPolicyCount/personCount activity1
                ,jinxiActivityPolicyCount/personCount activity2,
                row_number() over(
                    order by
                    activityPolicyCount/personCount desc nulls last
                    ,
                    bch_name asc
                ) as rank,
                row_number() over(
                    order by
                    jinxiActivityPolicyCount/personCount desc nulls last
                    ,
                    bch_name asc
                ) as amt_rank,
                bch_name
            from (
                select bch_name,
                    sum(temp.total_insured-temp.total_surrender) activityPolicyCount,
                    sum(temp.jingxi_sanxing_quanjiafu_insured-temp.jingxi_sanxing_quanjiafu_surrender) jinxiActivityPolicyCount,
                    max(temp.bch_manage_avg_cnt*27) activityPolicyTarget,
                    max(temp.bch_manage_avg_cnt) personCount
                from phoenix.temp_safes_stats_new_year_2024 temp where temp.pt = '${pt}' GROUP by temp.bch_name
            ) a
        ) b on a.bch_name = b.bch_name
        where a.bch_name in ${inBchNameClause}
    """
    def result = msgStatService.selectSafesPgMaps(sql)
    // 检查结果集是否为空
    if (result.isEmpty()) {
        // 初始化相同结构的对象列表
        def emptyObjects = []
        orgNameList.each { orgName ->
            def emptyObject = [
                    "bchname": orgName,
                    "activitypolicycount": 0,
                    "activitypolicytarget": 0,
                    "activitypolicycountavg": 0,
                    "jinxiactivitypolicycount": 0,
                    "jinxiactivitypolicycountavg": 0,
                    "personcount": 0,
                    "activitypolicyavgtarget": 27,
                    "rank": 0,
                    "amtrank": 0 ,
                    "activity1msg": "",
                    "activity2msg":""
            ]
            emptyObjects.add(emptyObject)
        }
        return emptyObjects.groupBy { it.bchname }
    }

    orgNameList.each { orgName ->
        //因为是批量查询所有区域，可能存在某个分支没值的情况，需要补充
        if (!result.any { map -> map.containsValue(orgName)}) {
            def emptyObject = [
                    "bchname": orgName,
                    "activitypolicycount": 0,
                    "activitypolicytarget": 0,
                    "activitypolicycountavg": 0,
                    "jinxiactivitypolicycount": 0,
                    "jinxiactivitypolicycountavg": 0,
                    "personcount": 0,
                    "activitypolicyavgtarget": 27,
                    "rank": 0,
                    "amtrank": 0 ,
                    "activity1msg": "",
                    "activity2msg":""
            ]
            result.add(emptyObject)
        }
    }
    return result.groupBy { it.bchname }
}

List getDefaultTendency(){
    def list = []

    // 获取当前月份
    def currentDate = Calendar.getInstance()
    def currentMonth = currentDate.get(Calendar.MONTH) + 1 // 加 1 是因为月份从 0 开始计数

    // 生成属性对象的列表
    for (int month = 1; month <= currentMonth; month++) {
        def monthName = String.format("%02d", month)

        def propertyObject = [
                "x": "${monthName}"+"月",
                "type": "标准保费（元）",
                "y": 0.0
        ]
        list.add(propertyObject)
    }
    log.info("list {}",list)
    return list
}

String getComplete(LinkedHashMap tenency,String orgName) {
    log.info("tenency {}",tenency)
    def defaultList = getDefaultTendency();
    def tendencys = []
    for (int i = 0; i < defaultList.size(); i++) {
        def defaultObject = defaultList[i]
        def defaultMonth = defaultObject["x"]

        // 在另一个LinkedHashMap对象中查找匹配的月份
        def matchingObject = tenency[orgName].find { it.x == defaultMonth }
        log.info("matchingObject {}",tenency[orgName])

        // 如果找到匹配的月份，则填充对应的y值
        if (matchingObject != null) {
            defaultObject["y"] = matchingObject["y"]
        }
        tendencys.add(defaultObject)
    }

    def jsonMap = [
            data: tendencys,
            type: "lineChart",
            config: [
                    xAxisConfig: [tickCount: 3, type: "cat"],
                    yAxisConfig: [min: 0, tickCount: 5]
            ]
    ]
    return JsonOutput.toJson(jsonMap)
}