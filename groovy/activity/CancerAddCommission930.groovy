package groovy.activity

import com.google.common.collect.Lists
import com.google.common.collect.Maps


List<Map<String, Object>> execute(String params) {
    String sql = """  SELECT fh_order_id
            ,13.72 AS proportion,
            CONCAT_WS('|',fh_order_id,policy_no,insured_id_number,plan_Id,risk_id,1) AS dataId,
            CONCAT_WS('|',fh_order_id,policy_no,insured_id_number,plan_Id ,risk_id,1) AS  uuid
            FROM phoenix."dwd_insurance_order_commission_short" t
            where product_id in (1167,1181,1312) 
            and is_loan_flag = 0 and is_valid_loan = 0 
            and (village_activity is null or village_activity ='')
            and account_time >= '2024-07-01 00:00:00' and account_time < '2024-10-01 00:00:00'
            and app_status = '1'"""
    return msgStatService.selectSafesPgMaps(sql)
}
