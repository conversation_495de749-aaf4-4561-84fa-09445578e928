import com.alibaba.fastjson.JSON
import com.cfpamf.ms.insur.operation.msg.pojo.dto.OpMessageRuleGroovyDTO
import com.cfpamf.ms.insur.operation.msg.util.Html2ImageUtils
import org.apache.commons.collections4.CollectionUtils

import java.time.LocalDate
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter
import java.time.format.TextStyle


String execute(OpMessageRuleGroovyDTO rule) {
    log.info("时间："+LocalDate.now().minusDays(1))
    def monthName = LocalDate.now().minusDays(1)
            .getMonth()
            .getDisplayName(TextStyle.FULL, Locale.SIMPLIFIED_CHINESE)
    def month = LocalDate.now().minusDays(1).getMonth().value
    def time = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:dd").format(LocalDate.now().atStartOfDay())
    def sql = """select * from (
                            select 
                                emp_id,
                                emp_name,
                                bch_code,
                                bch_name,
                                area_code,
                                area_name,
                                case when sm_norm_insurance_amt >= 10000 then CONCAT(round((sm_norm_insurance_amt/10000)::numeric,2),'万')
                                else CONCAT(ROUND(sm_norm_insurance_amt::numeric,2), '') end revenueAmt,
                                round(sm_norm_insurance_amt,2) sm_norm_insurance_amt,
                                RANK() OVER ( ORDER BY round(sm_norm_insurance_amt,2) DESC nulls last) rank
                            from wine.ads_lifesrv_emp_profit_static_dfp 
                            where is_work = 1 and sm_norm_insurance_amt is not null
                            order by sm_norm_insurance_amt desc
                        ) t
                        where t.rank <= 10
                    """
    def queryResult = msgStatService.selectDwMinerMaps(sql);
    log.info("个人月度保险营收排行榜：{}", JSON.toJSONString(queryResult))
    if (CollectionUtils.isEmpty(queryResult)) {
        log.warn("wine.ads_lifesrv_emp_profit_static_dfp没数据")
        return
    }
    def ftlMap = ["dataJson": queryResult]

    ftlMap.put("month",month);
    ftlMap.put("monthName",monthName);
    ftlMap.put("time",time);
    log.info("push params {}", ftlMap)
    return Html2ImageUtils.processTemplateInfoString(rule.getMessageTemplate(), ftlMap)
}
