package groovy.activity

import com.google.common.collect.Lists
import groovy.transform.Field

@Field List MERGE_ORG_LIST = Arrays.asList("凉城", "丰镇", "镶黄旗", "化德", "东乌旗", "西乌旗", "多伦", "蓝旗", "白旗", "太仆寺", "苏尼特左旗", "二连浩特", "阿巴嘎旗", "锡林浩特");

List<Map<String, Object>> execute(String params) {

    Map<String, Long> sumMap = summaryAchievement();
    log.warn("统计数据查询：{}", sumMap)
    String sql = """ 
        select 
            t.conversion_amount as premium,
            t.order_id as orderId,
            t.policy_no as policyNo, 
            t.insured_id_number as idCard,
            t.plan_id as planId,
            t.risk_id as riskId,
            t.term_num as termNum,
            t.policy_status as policyStatus,
            t.commission_user_id as userId,
            u.regionName,
            u.organizationName,
            u.orgCode 
        from sm_commission_detail t 
        left join sm_plan p on t.plan_id=p.id
        left join auth_user u on t.commission_user_id=u.userId
        where p.productId=494
        and orgCode is not null
        and regionName!='总部'  
        and t.account_time >= str_to_date('2022-06-01', '%Y-%m-%d')
        and t.account_time < str_to_date('2022-07-01', '%Y-%m-%d') 
        and SUBSTRING_INDEX(t.order_id,'_',1) 
        in(
            select  `fhOrderId` 
                    from sm_order b 
                    left join sm_commission_detail c on b.fhOrderId=c.order_id
                    where 	b.productId = 494
                    and 	b.`payStatus` = 2
                    and 	case when b.paymentTime=null then c.create_time else b.paymentTime end >= str_to_date('2022-06-01', '%Y-%m-%d')
                    and 	case when b.paymentTime=null then c.create_time else b.paymentTime end < str_to_date('2022-07-01', '%Y-%m-%d')
                    and 	INSTR (b.fhOrderId ,'_') =0
        )
            """
    List<Map<String, Object>> itemMap = msgStatService.selectMaps(sql)
    log.warn("客户经理业绩数据查询结果：{}", itemMap)
    List<Map<String, Object>> result = Lists.newArrayList()
    for (Map<String, Object> map : itemMap) {
        String orgName = map.get("organizationName")
        String regionName = map.get("regionName")
        String key = orgName + "-" + regionName
        log.warn("所属区域：{}", regionName)
        if (MERGE_ORG_LIST.contains(orgName)) {
            log.warn("合并机构被过滤：{}", orgName)
            continue
        }
        Integer prop = (Integer) sumMap.get(key)
        log.warn("获取佣金信息：{},{}", key, prop)
        if (prop != null) {

            String orderId = (String) map.get("orderId")
            String policyNo = (String) map.get("policyNo")
            String idCard = (String) map.get("idCard")
            String planId = (String) map.get("planId")
            String riskId = (String) map.get("riskId")
            String termNum = (String) map.get("termNum")
            String userId = (String) map.get("userId")
            String policyStatus = (String) map.get("policyStatus")
            String dataId = orderId + '|' + policyNo + '|' + idCard + '|' + planId + '|' + riskId + '|' + termNum + '|' + policyStatus
            Map<String, Object> resMap = new HashMap()
            resMap.put("dataId", dataId)
            resMap.put("uuid", dataId)
            resMap.put("proportion", prop)
            result.add(resMap)
        }
        String orderId = (String) map.get("orderId")
    }
    return result
}

// 按分支机构维度统计业绩
private Map<String, Long> summaryAchievement() {
    String sql = '''
        	select 
	sum(premium) as premium,
	a.regionName,
	a.organizationName,
	o.staff
 from (
					select 
						case when t.conversion_amount is null and t.policy_status=4 then t.amount*-1  
						when t.conversion_amount is null and t.policy_status=1 then t.amount
						else t.conversion_amount end as premium,
						t.commission_user_id,
						u.regionName,
						u.organizationName,
						u.orgCode,
						t.policy_status
					from sm_commission_detail t 
					left join sm_plan p on t.plan_id=p.id
					left join auth_user u on t.commission_user_id=u.userId
					where p.productId=494
					and orgCode is not null
					and regionName!='总部' 
					and t.account_time >= str_to_date('2022-06-01', '%Y-%m-%d')
        			and t.account_time < str_to_date('2022-07-01', '%Y-%m-%d') 
					and SUBSTRING_INDEX(t.order_id,'_',1) 
					in(
						select  `fhOrderId` 
								from sm_order b 
								left join sm_commission_detail c on b.fhOrderId=c.order_id
								where 	b.productId = 494
								and 	b.`payStatus` = 2
								and 	case when b.paymentTime=null then c.create_time else b.paymentTime end >= str_to_date('2022-06-01', '%Y-%m-%d')
								and 	case when b.paymentTime=null then c.create_time else b.paymentTime end < str_to_date('2022-07-01', '%Y-%m-%d')
								and 	INSTR (b.fhOrderId ,'_') =0
					)
) a left join system_organization_staff o 
on a.organizationName=o.org_name and a.regionName=o.region_name and o.year=2022 and o.month=6
group by a.regionName,a.organizationName
        '''
    List<Map> list = msgStatService.selectMaps(sql)
    log.info("分支机构业绩数据:{}", list)
    Map<String, Long> awardMap = new HashMap()
    for (Map<String, Object> map : list) {
        Long premium = (Long) map.get("premium")
        Integer staff = (Long) map.get("staff")
        String regionName = (String) map.get("regionName")
        String orgName = (String) map.get("organizationName")
        String key = orgName + "-" + regionName
        double avg = 0
        if (staff != null && staff > 0) {
            avg = premium * 1.0 / staff
        }
        if (avg >= 500) {
            awardMap.put(key, 5)
        }
    }
    log.info("分支机构加佣奖励:{}", awardMap)
    return awardMap;
}




