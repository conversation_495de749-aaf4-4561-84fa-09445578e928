import com.alibaba.fastjson.JSON
import com.cfpamf.ms.insur.operation.base.constant.BaseConstants
import com.cfpamf.ms.insur.operation.msg.pojo.dto.OpMessageRuleGroovyDTO
import com.cfpamf.ms.insur.operation.msg.util.Html2ImageUtils
import org.apache.commons.collections4.CollectionUtils

import java.time.LocalDate


Map execute(OpMessageRuleGroovyDTO rule) {
    log.info("==========================start1================================")
    def pt = LocalDate.now().minusDays(1L).format(BaseConstants.FMT_DATE)
    def areaCodeListString = rule.areaCodeListString
    log.info("==========================start================================"+areaCodeListString)
    def resultMap = [:]  // 使用 Map 保存结果
    def sql = """
    select ROUND((tmp.money)/10000,1) as wanmoney,tmp.* from (
        select sum(a.assess_convert_insurance_amt) as money,to_char((current_date - interval '1 day'),'YYYY年MM月DD日') as datestr,rank() over(order by sum(a.assess_convert_insurance_amt) desc ) as ranks,sum(a.assess_convert_insurance_amt) as assess_convert_insurance_amt,a.recommend_emp_id,a.recommend_emp_name
        ,a.bch_code,a.bch_name,a.area_code,a.area_name  
        from phoenix.ads_insurance_policy_index_progress_dfp a 
        where 
          a.pt = '${pt}'
          and a.trade_time >= current_date - interval '1 day'
          and a.trade_time < current_date
          and a.area_code in (${areaCodeListString})
          GROUP BY a.recommend_emp_id,a.recommend_emp_name,a.bch_code,a.bch_name,a.area_code,a.area_name 
    ) tmp where tmp.ranks=1;
    """
    log.info("==========================start================================"+sql)
    def queryResult = msgStatService.selectSafesPgMaps(sql);
    queryResult.forEach {
        log.info("test================"+JSON.toJSONString(it));
        def recommendEmpId = it["recommend_emp_id"]
        def bizCode = it["datestr"] + "-" + it["recommend_emp_id"]
        def sqlSafes = """
                            select * from op_message_push where `biz_code` = '${bizCode}'  and `rule_code` = '${rule.ruleCode}' and receiver = '${rule.receiver.receiver}' limit 1
                           """
        def queryResultSafes = msgStatService.selectMaps(sqlSafes);
        if (CollectionUtils.isEmpty(queryResultSafes)) {
            def dingInfos = wecatService.getDingTalkInfo([recommendEmpId])
            def we = dingInfos.find { dingInfo -> Objects.equals(dingInfo.wechatUserId, recommendEmpId) }
            if (Objects.nonNull(we)) {
                it["avatar"] = we.avatar
            }
            def htmlString = Html2ImageUtils.processTemplateInfoString(rule.getMessageTemplate(), it)
            log.info("test================" + htmlString);
            resultMap.put(bizCode, htmlString)
        }
    }
    return resultMap
}
