import com.cfpamf.ms.insur.operation.msg.pojo.dto.OpMessageRuleGroovyDTO
import com.cfpamf.ms.insur.operation.msg.service.MsgStatService
import com.cfpamf.ms.insur.operation.msg.util.Html2ImageUtils
import org.springframework.util.CollectionUtils


String execute(OpMessageRuleGroovyDTO rule) {
    MsgStatService service = msgStatService;

    def userParam = rule.getReceiver();
    def allParams = [:]
    def reward = """
SELECT  
    cast(COALESCE(ROUND(sy_land_customer_cnt/sy_ms_emp_cnt::NUMERIC, 1),0)*EXTRACT(MONTH FROM now()::date) as decimal(10, 1)) as "perLandCustomerCnt",
    cast(COALESCE(ROUND(sy_land_customer_cnt/sy_ms_emp_cnt::NUMERIC, 1),0)*EXTRACT(MONTH FROM now()::date) as decimal(10, 1))-150 "difference",
    area_name AS "areaName"
    FROM insurance.ads_insurance_area_index_progress_dfp
    where area_name is not null
order by COALESCE(ROUND(sy_land_customer_cnt/sy_ms_emp_cnt::NUMERIC, 1),0)*EXTRACT(MONTH FROM now()::date) desc
"""
    allParams.datas = service.selectDwMinerMaps(reward)
    if (CollectionUtils.isEmpty(allParams.datas)) {
        log.error("ads_insurance_area_index_progress_dfp 没查到数据")
        return null
    }
    log.info("push params {} {}", allParams.datas)
    return Html2ImageUtils.processTemplateInfoString(rule.getMessageTemplate(), allParams)
}
