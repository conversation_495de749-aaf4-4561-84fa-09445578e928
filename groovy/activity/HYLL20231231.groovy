package groovy.activity

import com.google.common.collect.Lists
import com.google.common.collect.Maps


List<Map<String, Object>> execute(String params) {
    String sql = """  SELECT
        1 AS proportion,
 t1.`fhOrderId`,
CONCAT_WS('|',t1.`fhOrderId`,t4.`policy_no`,t4.`insured_id_number`  ,t1.`planId` ,t4.risk_id,t4.term_num) AS dataId,
CONCAT_WS('|',t1.`fhOrderId`,t4.`policy_no`,t4.`insured_id_number` ,t1.`planId` ,t4.risk_id,t4.term_num) AS  uuid
FROM
        sm_order t1
        LEFT JOIN `sm_order_policy` t2 ON t1.`fhOrderId` = t2.`fh_order_id` 
LEFT JOIN sm_order_risk_duty t3 on t1.`fhOrderId` = t3.`fh_order_id` 
left join sm_commission_detail_item t4 on t3.fh_order_id=t4.order_id and t3.app_status = t4.policy_status  and t4.`commission_type` =2
WHERE
`productId` = 1033 
AND t4.`account_time` >= '2023-11-01 00:00:00' 
AND t4.`account_time` <= '2023-12-31 23:59:59'
and (t2.pay_type = 1 or (t2.`pay_type` = '2' AND t2.`pay_period` =1))
AND t2.`policy_state` = 1 
AND t1.`payStatus` = 2
AND t3.`app_status` = 1
and t4.plan_id = 2304
and t4.term_num = 1"""
    return msgStatService.selectMaps(sql)
}
