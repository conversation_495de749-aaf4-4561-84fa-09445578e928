package groovy.activity

List<Map<String, Object>> execute(String params) {
    String sql = """
with tmp as (
        SELECT
t1.`fhOrderId`,t3.`policy_no`,t3.`insured_id_number` ,t1.`planId` ,t3.risk_id,t4.term_num
FROM
sm_order t1
LEFT JOIN `sm_order_policy` t2 ON t1.`fhOrderId` = t2.`fh_order_id` 
LEFT JOIN sm_order_risk_duty t3 on t1.`fhOrderId` = t3.`fh_order_id` 
left join sm_commission_detail_item t4 on t3.fh_order_id=t4.order_id and t3.app_status = t4.policy_status  and t4.`commission_type` =2
WHERE
`productId` = 691 
AND t4.`account_time` >= '2023-04-01 00:00:00' 
AND t4.`account_time` < '2023-07-01 00:00:00'
and (t2.`pay_type` = '2' AND t2.`pay_period` in('5','10','15','20','30'))
and (t2.valid_unit = 'L' or (t2.valid_unit = 'A' and t2.valid_period ='70'))
AND t2.`policy_state` = 1 
AND t1.`payStatus` = 2
AND t3.`app_status` = 1
and t4.term_num = 1
        )
        SELECT
        ( CASE  
 WHEN t2.valid_unit = 'A' and t2.valid_period ='70' and t2.`pay_type`= 2 and t2.`pay_period` = '5' THEN 2
 WHEN t2.valid_unit = 'A' and t2.valid_period ='70' and t2.`pay_type`= 2 and t2.`pay_period` in ('10','15') THEN 3
 WHEN t2.valid_unit = 'L' and t2.`pay_type`= 2 and t2.`pay_period` in ('5','10','15','20','30') THEN 5
 WHEN t2.valid_unit = 'A' and t2.valid_period ='70' and t2.`pay_type`= 2 and t2.`pay_period` in ('20','30') THEN 6
 END ) AS proportion,
 t1.`fhOrderId`,
CONCAT_WS('|',t1.`fhOrderId`,t4.`policy_no`,t4.`insured_id_number`  ,t1.`planId` ,t4.risk_id,t4.term_num) AS dataId,
CONCAT_WS('|',t1.`fhOrderId`,t4.`policy_no`,t4.`insured_id_number` ,t1.`planId` ,t4.risk_id,t4.term_num) AS  uuid
FROM
        sm_order t1
        LEFT JOIN `sm_order_policy` t2 ON t1.`fhOrderId` = t2.`fh_order_id` 
        LEFT JOIN sm_order_renewal_term_risk t3 on t1.`fhOrderId` = t3.`order_id` 
        left join sys_risk t6 on t6.risk_code = t3.risk_code
        left join sm_commission_detail_item t4 on t3.order_id=t4.order_id and t4.`commission_type` =2 and t3.term_num = t4.term_num
        WHERE
`productId` = 691 
and t1.fhorderId in (select fhorderId from tmp)
and (t2.`pay_type` = '2' AND t2.`pay_period` in('5','10','15','20','30'))
and (t2.valid_unit = 'L' or (t2.valid_unit = 'A' and t2.valid_period ='70'))
AND t2.`policy_state` = 1 
AND t1.`payStatus` = 2
AND t3.`app_status` = 1
        and t3.term_num = 2
and t4.term_num = 2
"""
    return msgStatService.selectMaps(sql)
}
