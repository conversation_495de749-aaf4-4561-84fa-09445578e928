package groovy.activity

import com.google.common.collect.Lists
import com.google.common.collect.Maps


List<Map<String, Object>> execute(String params) {
    String sql = """  SELECT
10 AS proportion,
CONCAT_WS('|',t1.`fhOrderId`,t4.`policy_no`,t4.`insured_id_number`  ,t1.`planId` ,t4.risk_id,1) AS dataId,
CONCAT_WS('|',t1.`fhOrderId`,t4.`policy_no`,t4.`insured_id_number` ,t1.`planId` ,t4.risk_id,1) AS  uuid
FROM
sm_order t1
LEFT JOIN `sm_order_policy` t2 ON t1.`fhOrderId` = t2.`fh_order_id` 
left join sm_commission_detail_item t4 on t1.fhOrderId=t4.order_id  and t4.`commission_type` =2
WHERE
`productId` = 785 
AND t4.`account_time` >= '2023-10-01 00:00:00' 
AND t4.`account_time` <= '2023-12-31 23:59:59'
AND t1.`payStatus` = 2
AND t4.`policy_status` = 1
and t4.term_num = 1"""
    return msgStatService.selectMaps(sql)
}
