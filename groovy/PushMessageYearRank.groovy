import com.cfpamf.ms.insur.operation.msg.pojo.po.OpMessageRule
import com.cfpamf.ms.insur.operation.msg.service.OrderService
import com.cfpamf.ms.insur.operation.msg.util.Html2ImageUtils

import java.time.LocalDate

String execute(OpMessageRule rule) {
    OrderService orderService = orderService;
    LocalDate rptDate = LocalDate.now().minusDays(1L)
    def emp = msgStatService.selectDwMaps("""
 select area_name        region_name,
               bch_name         org_name,
               emp_name         user_name,
               insured_amt_year amts
        from rpt.insurance_order_emp_stat_dfp
        where rpt_date = '${rptDate}'
        order by insured_amt_year desc,
                 insured_cnt_year
            desc
        limit 10
""")
    def params = [areaDatas: orderService.listRankArea(rptDate, 3), userDatas: emp, orgDatas: orderService.listRankBch(rptDate, 10), rptDate: rptDate.toString()]
    return Html2ImageUtils.processTemplateInfoString(rule.getMessageTemplate(), params)
}
