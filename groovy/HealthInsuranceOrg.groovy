import com.cfpamf.ms.insur.operation.msg.pojo.po.OpMessageRule
import com.cfpamf.ms.insur.operation.msg.util.Html2ImageUtils

import java.time.LocalDate

String execute(OpMessageRule rule) {
    Map<String, Object> allParams = jacksonObjectMapper.readValue(rule.getParams(), Map.class)
    String rank = """  
    select t.regionName,
        t.organizationName,
        sum(t.amount) amt,
        if(${allParams.standard} - sum(t.amount) > 0, ${allParams.standard} - sum(t.amount), 0) exp
from (

SELECT u.regionName,u.organizationName,so.fhOrderId,so.productId,so.recommendId ,so.totalAmount,f.pay_years ,
     case when f.pay_years =1 then so.totalAmount *0.1
          when f.pay_years =5 then   so.totalAmount * 0.5
          when f.pay_years =10 or f.pay_years = 15 then so.totalAmount *0.8
          else  so.totalAmount end as amount
FROM sm_order so
       left join  sm_order_insured soi on so.fhOrderId  = soi.fhOrderId
       left join  sm_order_extend_fx f on so.fhOrderId  = f.fh_order_id
       left join auth_user u on u.userid = so.recommendId and u.enabled_flag =0

where  so.paymentTime>='${allParams.startDate}'
  and so.paymentTime <'${allParams.endDate}' 
  and so.payStatus =2 
  and soi.appStatus=1
  and so.productId in  (${allParams.productIds.join(',')})
  ) t 
  group by ${allParams.field}
  order by amt desc 
  limit ${allParams.maxRows};
"""
    System.err.println(rank)
    System.err.println('rank')
    def maps = msgStatService.selectMaps(rank)
    allParams['datas'] = maps
    allParams.put("rptBeginDate",  allParams.get("startDate").toString().substring(5))
    allParams.put("rptEndDate",  LocalDate.now().toString().substring(5))
    return Html2ImageUtils.processTemplateInfoString(rule.getMessageTemplate(), allParams)
}
