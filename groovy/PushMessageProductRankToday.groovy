import com.cfpamf.ms.insur.operation.msg.pojo.po.OpMessageRule
import com.cfpamf.ms.insur.operation.msg.pojo.query.StataRankQuery
import com.cfpamf.ms.insur.operation.msg.pojo.vo.StatRankVO
import com.cfpamf.ms.insur.operation.msg.util.Html2ImageUtils

import java.time.LocalDate
import java.time.LocalTime

String execute(OpMessageRule rule) {
    StataRankQuery params = jacksonObjectMapper.readValue(rule.getParams(), StataRankQuery.class)
    def now = LocalTime.now()
    if (params.getStartDate() == null) {
        def start = LocalDate.now()
        params.setStartDate(start)
        params.setEndDate(start.plusDays(1L))
    }
    Map<String, Object> allParams = jacksonObjectMapper.readValue(rule.getParams(), Map.class)
    List<StatRankVO> rankVos = msgStatService.selectRankByProduct(params)
    allParams.put("datas", rankVos)
    allParams.put("rptDate", now.toString().substring(0,5))
    return Html2ImageUtils.processTemplateInfoString(rule.getMessageTemplate(), allParams)
}
