package groovy.activity

import com.google.common.collect.Lists
import com.google.common.collect.Maps
import com.cfpamf.ms.insur.operation.activity.util.ActivitySqlUtils
import com.cfpamf.ms.insur.operation.activity.dto.ActivityConstRuleParam



List<Map<String, Object>> execute(ActivityConstRuleParam param) {
    def activity = param.constActivity()
    log.info("参数："+param)
    def offset = param.offset()
    def size = param.size()
    String sql = """  
        with renewal as (
            select max(id) id,new_policy_no from policy_renewal_base_info
            group by new_policy_no
        )
        select policy_no,5 as proportion,
                    CONCAT_WS('|',policy_no,endorsement_no,insured_code,sell_product_id,risk_id,term_num,policy_status) AS dataId,
                    CONCAT_WS('|',policy_no,endorsement_no,insured_code,sell_product_id ,risk_id,term_num,policy_status,${activity.id}) AS  uuid,
                    add_amount as amount
                    FROM dwa_ep_policy_product_info t
        left join renewal t1 on t.policy_no = t1.new_policy_no
        where t1.id is not null
        and order_time >= '${activity.startTime}' and order_time < '${activity.endTime}'
        and `risk_code` in (select `risk_code` from `whale_add_commisson_risk_configuration` where `activity_name` = '员工保障福利大放送')
        limit ${offset}, ${size} 
    """
    return msgStatService.selectMaps(sql)
}
