

```sql
--  2021 08 26

create table op_message_push_pool
(
    id              bigint unsigned auto_increment
        primary key,
    message_rule_id bigint unsigned                     null comment '规则id',
    context_id      varchar(64)                         not null comment 'mq消息id',
    exec_time       datetime                            null comment '执行时间',
    state           tinyint(1)                          not null comment '状态 1-初始化 2-已发送 3-发送失败',
    remark          varchar(500)                        not null comment '备注',
    create_time     timestamp default CURRENT_TIMESTAMP not null,
    update_time     timestamp default CURRENT_TIMESTAMP not null,
    enabled_flag    tinyint   default 0                 null
)
    comment '消息推送记录-暂存池' engine = InnoDB;


alter table op_message_push
    add context_id varchar(64) null comment '消息队列id' after message_rule_id;

alter table op_message_push
    add receiver_name varchar(50) null comment '接受者名称' after receiver;

alter table op_message_push
    add message_cache varchar(500) null comment '消息内容缓存' after receiver;


```
