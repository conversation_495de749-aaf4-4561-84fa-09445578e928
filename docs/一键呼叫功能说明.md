# 一键呼叫功能说明

## 功能概述

一键呼叫功能允许客户经理在系统中直接发起对客户的电话呼叫，系统会自动记录呼叫信息，获取录音，并与续保跟进记录进行匹配。

## 功能特性

1. **一键发起呼叫**：通过保单号和客户手机号快速发起呼叫
2. **自动记录管理**：自动保存呼叫记录，包括呼叫状态、时长等信息
3. **录音获取**：定时任务自动获取呼叫录音地址
4. **跟进记录匹配**：自动匹配或创建续保跟进记录

## 技术架构

### 核心组件

1. **CallController**：一键呼叫控制器
2. **CallService**：呼叫业务服务
3. **CallRecordSyncService**：呼叫记录同步服务
4. **CallFollowMatchService**：跟进记录匹配服务
5. **CallServiceFacade**：第三方呼叫服务Feign客户端

### 数据库表

- **call_record**：呼叫记录表，存储所有呼叫相关信息

### 定时任务

1. **呼叫记录同步**：每5分钟执行一次，同步呼叫状态和录音信息
2. **跟进记录匹配**：每10分钟执行一次，匹配或创建跟进记录

## API接口

### 1. 一键呼叫

**接口地址**：`POST /v1/call/oneClick`

**请求参数**：
```json
{
  "policyNo": "保单号",
  "customerPhone": "客户手机号",
  "clientId": "客户编号（可选）",
  "clientName": "客户名称（可选）",
  "remark": "备注（可选）"
}
```

**响应参数**：
```json
{
  "callRecordId": "呼叫记录ID",
  "callSid": "呼叫SID",
  "callStatus": "呼叫状态",
  "message": "消息",
  "success": "是否成功"
}
```

### 2. 查询呼叫记录

**接口地址**：`GET /v1/call/records?policyNo={保单号}`

**响应参数**：呼叫记录列表

### 3. 手动同步呼叫记录

**接口地址**：`POST /v1/call/sync/records`

### 4. 手动匹配跟进记录

**接口地址**：`POST /v1/call/sync/follow`

## 业务流程

### 呼叫流程

1. 用户点击"一键呼叫"按钮
2. 前端传递保单号和客户手机号
3. 系统获取当前登录用户信息（客户经理工号和手机号）
4. 创建呼叫记录并保存到数据库
5. 调用第三方呼叫服务发起呼叫
6. 更新呼叫记录状态
7. 返回呼叫结果给前端

### 录音获取流程

1. 定时任务查询未获取录音的呼叫记录
2. 调用第三方服务查询呼叫详情
3. 如果有录音地址，直接更新；否则调用录音查询接口
4. 更新呼叫记录的录音信息

### 跟进记录匹配流程

1. 定时任务查询已获取录音但未匹配跟进记录的呼叫记录
2. 查找该保单在呼叫时间之后的跟进记录
3. 如果找到，则进行匹配；如果没找到，则创建新的跟进记录
4. 新创建的跟进记录意向默认设置为"考虑"
5. 更新呼叫记录的匹配状态

## 配置说明

### 应用配置

在 `application-dev.yml` 中添加第三方呼叫服务配置：

```yaml
# 第三方呼叫服务配置
call-service:
  url: http://call-service-url
```

### 数据库配置

执行 `sql/call_record.sql` 脚本创建呼叫记录表。

## 状态说明

### 呼叫状态（call_status）

- 0：初始化
- 1：呼叫中
- 2：呼叫成功
- 3：呼叫失败

### 录音状态（record_status）

- 0：未获取
- 1：已获取
- 2：获取失败

### 跟进匹配状态（follow_match_status）

- 0：未匹配
- 1：已匹配

## 注意事项

1. 确保第三方呼叫服务地址配置正确
2. 定时任务默认开启，如需关闭可在配置中设置
3. 呼叫记录会永久保存，建议定期清理历史数据
4. 录音获取可能存在延迟，系统会持续重试
5. 跟进记录匹配基于保单号和时间，确保数据准确性

## 错误处理

系统会记录所有异常信息到日志中，包括：

- 呼叫失败原因
- 录音获取失败信息
- 跟进记录匹配异常

可通过日志文件排查问题。
