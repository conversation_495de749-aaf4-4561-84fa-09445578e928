# 一键呼叫功能测试用例

## 测试环境准备

### 1. 数据库准备
```sql
-- 执行呼叫记录表创建脚本
source sql/call_record.sql;

-- 验证表结构
DESC call_record;
```

### 2. 配置文件检查
```yaml
# application-dev.yml
call-service:
  url: http://call-service-url  # 确保第三方呼叫服务地址正确
```

### 3. 用户权限准备
- 确保测试用户有呼叫权限
- 准备测试用户的工号和手机号
- 准备测试保单号和客户手机号

## 功能测试用例

### 用例1：一键呼叫成功场景

**测试目标**：验证一键呼叫功能正常流程

**前置条件**：
- 用户已登录系统
- 第三方呼叫服务正常
- 保单号和客户手机号有效

**测试步骤**：
1. 调用一键呼叫接口
2. 检查返回结果
3. 验证数据库记录

**测试数据**：
```json
{
  "policyNo": "P202501220001",
  "customerPhone": "13800138000",
  "clientId": "C001",
  "clientName": "张三",
  "clientType": "1552",
  "remark": "测试一键呼叫功能"
}
```

**预期结果**：
- 接口返回success=true
- 返回callRecordId和callSid
- 数据库中插入呼叫记录
- 呼叫状态为1（呼叫中）

### 用例2：参数校验测试

**测试目标**：验证接口参数校验功能

**测试场景**：
1. 保单号为空
2. 客户手机号为空
3. 客户手机号格式错误

**测试数据1**（保单号为空）：
```json
{
  "policyNo": "",
  "customerPhone": "13800138000"
}
```

**预期结果1**：
- 返回参数校验错误
- 提示"保单号不能为空"

**测试数据2**（手机号格式错误）：
```json
{
  "policyNo": "P202501220001",
  "customerPhone": "1380013800"
}
```

**预期结果2**：
- 返回参数校验错误
- 提示"客户手机号格式不正确"

### 用例3：呼叫记录查询测试

**测试目标**：验证呼叫记录查询功能

**前置条件**：
- 数据库中存在测试呼叫记录

**测试步骤**：
1. 调用查询接口
2. 验证返回数据格式
3. 检查状态描述字段

**测试数据**：
```
GET /v1/call/records?policyNo=P202501220001
```

**预期结果**：
- 返回呼叫记录列表
- 包含状态描述字段
- 时间格式正确

### 用例4：定时任务测试

**测试目标**：验证录音获取和跟进匹配定时任务

**测试场景1**：录音获取
1. 手动触发录音同步
2. 检查录音状态更新
3. 验证录音地址获取

**测试场景2**：跟进记录匹配
1. 手动触发跟进匹配
2. 检查匹配状态更新
3. 验证跟进记录创建

**测试接口**：
```
POST /v1/call/sync/records
POST /v1/call/sync/follow
```

## 异常测试用例

### 用例5：第三方服务异常

**测试目标**：验证第三方服务不可用时的处理

**测试步骤**：
1. 配置错误的第三方服务地址
2. 调用一键呼叫接口
3. 检查异常处理

**预期结果**：
- 接口返回失败信息
- 呼叫记录状态为3（呼叫失败）
- 记录失败原因

### 用例6：用户信息获取失败

**测试目标**：验证无法获取用户信息时的处理

**测试步骤**：
1. 使用无效的授权token
2. 调用一键呼叫接口
3. 检查异常处理

**预期结果**：
- 返回用户信息获取失败错误
- 不创建呼叫记录

## 性能测试用例

### 用例7：并发呼叫测试

**测试目标**：验证系统并发处理能力

**测试步骤**：
1. 同时发起多个呼叫请求
2. 检查响应时间
3. 验证数据一致性

**测试指标**：
- 响应时间 < 3秒
- 并发处理无数据冲突
- 系统稳定运行

### 用例8：大数据量测试

**测试目标**：验证大量呼叫记录的处理能力

**测试步骤**：
1. 创建大量测试数据
2. 执行查询操作
3. 运行定时任务

**测试指标**：
- 查询响应时间合理
- 定时任务执行正常
- 数据库性能稳定

## 测试数据清理

### 清理脚本
```sql
-- 清理测试数据
DELETE FROM call_record WHERE policy_no LIKE 'TEST%';
DELETE FROM call_record WHERE remark LIKE '%测试%';

-- 重置自增ID（可选）
ALTER TABLE call_record AUTO_INCREMENT = 1;
```

## 测试报告模板

### 测试结果记录表

| 用例编号 | 用例名称 | 测试结果 | 实际结果 | 备注 |
|---------|---------|---------|---------|------|
| TC001   | 一键呼叫成功 | PASS/FAIL | 描述实际结果 | 问题说明 |
| TC002   | 参数校验 | PASS/FAIL | 描述实际结果 | 问题说明 |
| TC003   | 记录查询 | PASS/FAIL | 描述实际结果 | 问题说明 |
| ...     | ...     | ...     | ...     | ...  |

### 问题记录

| 问题编号 | 问题描述 | 严重程度 | 状态 | 解决方案 |
|---------|---------|---------|------|---------|
| BUG001  | 问题描述 | 高/中/低 | 开放/已修复 | 解决方案描述 |

### 测试总结

- **测试覆盖率**：功能覆盖率、代码覆盖率
- **缺陷统计**：总缺陷数、严重缺陷数、修复率
- **性能指标**：响应时间、并发能力、稳定性
- **建议改进**：功能优化建议、性能优化建议
