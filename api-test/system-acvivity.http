### 新增
POST {{host}}/back/operation/system/activity/programme
Content-Type: application/json

{
  "activeFlag": false,
  "activityObject": "STAFF_MANAGER",
  "startTime": "2022-06-01 00:00:00",
  "endTime": "2022-06-30 00:00:00",
  "content": "<p>4234234</p>",
  "title": "13123",
  "openCount": 0,
  "type": "ADD_COMMISSION",
  "regionList": [
    "ALL"
  ],
  "productList": [],
  "imageUrl": "https://safesfiles-test.oss-cn-beijing.aliyuncs.com/activity-pic/ZHNX07960/xj_202206231516.png",
  "recommendedProductList": [],
  "conflictRule": "OPTIMAL",
  "elsActivityNumber": "",
  "elsGrantPassword": "",
  "systemActivityRefList": [],
  "systemActivityProductList": [
    {
      "planId": [
        1168
      ],
      "productIds": [
        580
      ],
      "quantity": "23",
      "rewardType": "ADD_COMMISSION",
      "triggerType": "JOB",
      "systemActivityProductRuleList": [
        {
          "compareSymbol": "IMMEDIATELY_PRESENT",
          "ruleCode": "group_insurance_sprint_630"
        }
      ]
    }
  ]
}


### 修改
PUT {{host}}/system/activity/programme/104
Content-Type: application/json

{
  "id": 103,
  "activityCode": "00103",
  "title": "1312322",
  "type": "ADD_COMMISSION",
  "activityObject": "STAFF_MANAGER",
  "backgroundImageUrl": null,
  "regionList": [
    "ALL"
  ],
  "regions": "[\"ALL\"]",
  "startTime": "2022-06-01 00:00:00",
  "endTime": "2022-06-30 00:00:00",
  "lastConfigUser": null,
  "updateTime": "2022-06-23 15:16:37",
  "activeFlag": 0,
  "activeState": "UNPUBLISHED",
  "content": "<p>4234234</p>",
  "imageUrl": "https://safesfiles-test.oss-cn-beijing.aliyuncs.com/activity-pic/ZHNX07960/xj_202206231516.png",
  "conflictRule": "OPTIMAL",
  "productList": null,
  "products": "[]",
  "recommendedProductList": null,
  "elsActivityNumber": null,
  "elsGrantPassword": "",
  "systemActivityRefList": [],
  "systemActivityProductList": [
    {
      "planId": [
        1168
      ],
      "productId": 580,
      "quantity": 23,
      "rewardType": "ADD_COMMISSION",
      "triggerType": "JOB",
      "systemActivityProductRuleList": [
        {
          "compareSymbol": "IMMEDIATELY_PRESENT",
          "ruleCode": "group_insurance_sprint_630",
          "params": null
        }
      ]
    }
  ],
  "openCount": 0
}


### 手动配置-新增
POST {{host}}/back/operation/system/activity/programme
Content-Type: application/json

{
  "activityCode": "00114",
  "title": "出单即送模式测试5000",
  "type": "ADD_COMMISSION",
  "activityObject": "STAFF_MANAGER",
  "configType": "CONST",
  "backgroundImageUrl": null,
  "regionList": [
    "ALL"
  ],
  "regions": "[\"ALL\"]",
  "startTime": "2022-04-01 00:00:00",
  "endTime": "2022-08-01 00:00:00",
  "lastConfigUser": null,
  "updateTime": "2022-07-06 13:45:25",
  "activeFlag": 0,
  "activeState": "UNPUBLISHED",
  "content": "<p>33333</p>",
  "imageUrl": "https://safesfiles-test.oss-cn-beijing.aliyuncs.com/activity-pic/ZHNX33656/logo_202207061343.png",
  "conflictRule": "OPTIMAL",
  "productList": null,
  "products": "[]",
  "recommendedProductList": null,
  "elsActivityNumber": null,
  "elsGrantPassword": "",
  "systemActivityRefList": [],
  "systemActivityProductList": null,
  "openCount": 0,
  "constRule": {
    "constRules": [
      {
        "enabledFlag": 0,
        "productLongInsurance": 0,
        "productIds": [],
        "planOrRiskIds": [],
        "ruleType": "SCALE",
        "jointType": "OR",
        "rewardType": "RATIO",
        "quantity": 2,
        "params": {
          "compareSymbol": ">",
          "statRange": "ALL",
          "value": "300000"
        }
      }
    ]
  }
}


### 查询手动配置
GET {{host}}/back/operation/system/activity/programme/111?id=111
Content-Type: application/json


### 查询列表
POST {{host}}/back/operation/system/activity/programme/search
Content-Type: application/json

{
  "startTime": null,
  "endTime": null,
  "activityName": "",
  "productName": "",
  "activeFlag": null,
  "pageNo": 1,
  "pageSize": 10
}


### 手动配置-修改
PUT {{host}}/back/operation/system/activity/programme/114
Content-Type: application/json

{
  "id": 114,
  "activityCode": "00114",
  "title": "加佣2222222",
  "type": "ADD_COMMISSION",
  "activityObject": "STAFF_MANAGER",
  "configType": "CONST",
  "backgroundImageUrl": null,
  "regionList": [
    "ALL"
  ],
  "regions": "[\"ALL\"]",
  "startTime": "2022-07-06 00:00:00",
  "endTime": "2022-07-07 00:00:00",
  "lastConfigUser": null,
  "updateTime": "2022-07-06 13:45:25",
  "activeFlag": 0,
  "activeState": "UNPUBLISHED",
  "content": "<p>33333</p>",
  "imageUrl": "https://safesfiles-test.oss-cn-beijing.aliyuncs.com/activity-pic/ZHNX33656/logo_202207061343.png",
  "conflictRule": "OPTIMAL",
  "productList": null,
  "products": "[]",
  "recommendedProductList": null,
  "elsActivityNumber": null,
  "elsGrantPassword": "",
  "systemActivityRefList": [],
  "systemActivityProductList": null,
  "openCount": 0,
  "constRule": {
    "constRules": [
      {
        "id": 28,
        "enabledFlag": 0,
        "saId": 114,
        "productLongInsurance": 0,
        "productIds": [
          565
        ],
        "planOrRiskIds": [
          103,
          104
        ],
        "ruleType": "ORDER_PREMIUM",
        "jointType": "OR",
        "rewardType": "RATIO",
        "quantity": 2,
        "params": {
          "compareSymbol": ">",
          "statRange": null,
          "value": "2222"
        }
      },
      {
        "id": 29,
        "enabledFlag": 0,
        "saId": 114,
        "productLongInsurance": 1,
        "productIds": [
          576,
          578
        ],
        "planOrRiskIds": [],
        "ruleType": "SCALE",
        "jointType": "OR",
        "rewardType": "RATIO",
        "quantity": 3,
        "params": {
          "compareSymbol": ">",
          "statRange": "PERSON",
          "value": "333"
        }
      },
      {
        "productIds": [
          559
        ],
        "planOrRiskIds": [],
        "params": {
          "compareSymbol": ">",
          "statRange": "PERSON",
          "value": "5555"
        },
        "rewardType": "RATIO",
        "quantity": "5",
        "ruleType": "SUM",
        "productLongInsurance": 1,
        "jointType": "OR"
      }
    ]
  },
  "commissionRule": [
    "ORDER_PREMIUM",
    "SCALE",
    "SUM"
  ]
}



### 查询活动数量
POST {{host}}/back/operation/system/activity/programme/searchProductActivityNum
Content-Type: application/json

{
  "startTime": "2022-04-06 00:00:00",
  "endTime": "2022-08-06 00:00:00",
  "sceneForms": [
    {
      "productIds": [
        587
      ],
      "ruleType": "ORDER_PREMIUM"
    },
    {
      "productIds": [
      ],
      "ruleType": "SUM"
    }
  ]
}
