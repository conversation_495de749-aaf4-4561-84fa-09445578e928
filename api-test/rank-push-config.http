### 配置
POST https://admin.xiaowhale.com/bapi/api/insurance/operation//msg/config
Authorization: {{token}}
Content-Type: application/json


{
  "rule": {
    "ruleCode": "RankBchCust",
    "ruleName": "分支风云榜-服务榜",
    "startDate": "2023-10-12",
    "endDate": "2023-12-31",
    "cronRule": "0 0 9 5 * ?",
    "params": "{}",
    "messageType": "image",
    "messageTemplate": "<!DOCTYPE html>\n<html lang=\"en\">\n\n<head>\n  <meta charset=\"UTF-8\">\n  <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n  <title>分支风云榜-服务榜</title>\n  <style>\n    * {\n      margin: 0;\n      padding: 0;\n    }\n\n    body {\n      font-family: Arial, sans-serif;\n      margin: 0;\n      padding: 0;\n    }\n\n    .copywriting {\n      padding: 20px;\n      font-size: 28px;\n      background-color: #fff;\n    }\n\n    .poster {\n      position: relative;\n      max-width: 750px;\n      margin: 0 auto;\n      background-color: #af1e1c;\n      overflow: hidden;\n    }\n\n    .poster img {\n      width: 100%;\n      height: auto;\n    }\n\n    .poster-month {\n      position: absolute;\n      top: 80px;\n      right: 120px;\n      width: 72px;\n      text-align: center;\n      font-size: 60px;\n      color: #f6d993;\n    }\n\n    .poster-info,\n    .poster-bottom {\n      text-align: center;\n      color: #f6d993;\n      font-size: 28px;\n    }\n\n    .poster-info {\n      width: 100%;\n    }\n\n    .poster-bottom {\n      margin: 10px 0 30px;\n    }\n    .poster-center{\n      margin: 20px 40px 10px;\n    }\n\n    .poster-organization {\n      margin: -20px 8px;\n      padding: 6px 10px;\n      background: linear-gradient(to bottom,#dd433e,#b31616,#dd433e) ;\n      border: 16px solid #7a1610;\n    }\n\n    .poster-organization table {\n      width: 100%;\n      border-collapse: collapse;\n      color: #fff2d2;\n    }\n\n    .poster-organization table thead {\n      border-bottom: 2px solid #f6d993;\n    }\n    .poster-organization table thead th{\n      padding: 6px 0;\n      font-size: 30px;\n    }\n\n    .poster-organization table tbody td {\n      padding: 2px 0 0;\n      font-size: 28px;\n      text-align: center;\n    }\n   \n    .high-light {\n      color: #dd3737;\n      font-weight: bolder;\n    }\n\n    .title-bold {\n      color: green;\n      font-weight: bolder;\n    }\n\n    .ml-6 {\n      margin-left: 12px;\n    }\n\n    .ml-20 {\n      margin-left: 40px;\n    }\n\n    .bold {\n      font-weight: bolder;\n    }\n\n    .mt-5 {\n      margin-top: 10px;\n    }\n\n    .emoji-img {\n      width: 44px;\n      height: 44px;\n      margin-right: 3px;\n      vertical-align: sub;\n    }\n \n  </style>\n</head>\n\n<body>\n  <div class=\"poster\">\n    <div class=\"poster-header\">\n      <img src=\"https://safesfiles.oss-cn-beijing.aliyuncs.com/safes/ZHNX33656/月分支风云榜服务榜_202309221430.png\" alt=\"\">\n    </div>\n    <div class=\"poster-month\" id=\"month-2\"></div>\n    <div class=\"poster-info\">\n      <span>统计口径：当月小程序注册客户数/个</span>\n      <span class=\"ml-20\">统计时间：<span id=\"month-3\"></span>月</span>\n    </div>\n\n    <div class=\"poster-center\">\n      <img src=\"https://safesfiles.oss-cn-beijing.aliyuncs.com/safes/ZHNX33656/横幅_202309191932.png\" alt=\"\">\n      <div class=\"poster-organization\" id=\"organization-rank\">\n        <table border=\"0\">\n          <thead>\n            <tr>\n              <th>序号</th>\n              <th>区域</th>\n              <th>分支</th>\n              <th>当月小程序客户认证数</th>\n            </tr>\n          </thead>\n          <tbody id=\"table-tbody\">\n  \n          </tbody>\n        </table>\n      </div>\n      <img src=\"https://safesfiles.oss-cn-beijing.aliyuncs.com/safes/ZHNX33656/横幅_202309191932.png\" alt=\"\">\n\n    </div>\n    \n    <div class=\"poster-bottom\">\n      <span>再/接/再/厉</span>\n      <span class=\"ml-20\">再/创/佳/绩</span>\n    </div>\n  </div>\n\n  <div class=\"copywriting\">\n    <div class=\"copywriting-header ml-6\">\n      <p class=\"bold\"><img src=\"https://safesfiles.oss-cn-beijing.aliyuncs.com/safes/ZHNX33656/庆祝_202309192056.png\"\n          class=\"emoji-img\">统一思想、明确思路、大步前行！</p>\n      <p><img src=\"https://safesfiles.oss-cn-beijing.aliyuncs.com/safes/ZHNX33656/火_202309192057.png\"\n          class=\"emoji-img\"><span class=\"high-light\">B类业务<span class=\"title-bold\"><span id=\"month-1\"></span>月分支风云榜（服务榜）</span></span>新鲜出炉!\n      </p>\n     \n        <img src=\"https://safesfiles.oss-cn-beijing.aliyuncs.com/safes/ZHNX33656/努力_202309192033.png\" class=\"emoji-img\">\n          目前排名第一的是<span class=\"high-light\" id=\"first-orgname\"></span>，当月注册客户数已达<span class=\"title-bold\"><span id=\"max-area-value\"></span>人</span>。</p>\n      <div class=\"copywriting-content mt-5\">\n        其中<span class=\"bold\">【<span id=\"ranked-area\"></span>】</span>区域各显神通，\n        共<span id=\"ranked-number\"></span>入围榜单。\n        <span class=\"title-bold\" id=\"max-area-name\"></span>分支更是高达<span class=\"high-light\" id=\"max-area-ratio\"></span>的霸榜率！详情请查看分支荣誉榜单~\n      </div>\n    </div>\n  </div>\n</body>\n\n<script>\n\nconst data = ${datasJson}\n\nconst { datas,maxAreaName,yearMonth,areaNames,maxAreaPre } = data\n\n\n  const doms = {\n    areaDom:document.getElementById('table-tbody'),\n    firstOrgDom : document.querySelector('#first-orgname'),\n    rankedAreaDom : document.querySelector('#ranked-area'),\n    maxAreaRatioDom:document.getElementById('max-area-ratio'),\n    maxAreaValueDom:document.getElementById('max-area-value'),\n    maxAreaDom:document.getElementById('max-area-name'),\n    rankedNumberDom:document.getElementById('ranked-number'),\n    month1Dom:document.getElementById('month-1'),\n    month2Dom:document.getElementById('month-2'),\n    month3Dom:document.getElementById('month-3'),\n  }\n\n<#noparse>\n  // 位列第一的分支名称\n  const firstOrgList = (datas || []).filter(v => v.rank_num === 1)\n  const firstOrgName = firstOrgList.map(v => v.area_name + '-' +v.bch_name).join(\"、\");\n  doms.firstOrgDom.innerHTML = firstOrgName\n\n  // 第一分支标保\n  doms.maxAreaValueDom.innerHTML = firstOrgList[0].rank_value\n\n  // 上榜区域\n  const areaNameList = (areaNames || []).map(v=> v.slice(0,-2))\n  doms.rankedAreaDom.innerHTML = areaNameList.join('、')\n\n  // 上榜区域数量\n  doms.rankedNumberDom.innerHTML = datas.length + '家分支'\n\n  // 最大霸榜率\n  const maxAreaValue = maxAreaPre ? parseInt((maxAreaPre / datas?.length) * 100 ) : 0\n  doms.maxAreaRatioDom.innerHTML = maxAreaValue + '%'\n  doms.maxAreaDom.innerHTML = maxAreaName\n\n  // 月份\n  doms.month1Dom.innerHTML = yearMonth.monthValue\n  doms.month2Dom.innerHTML = yearMonth.monthValue\n  doms.month3Dom.innerHTML = yearMonth.monthValue\n\n  // 填充列表模块\n  doms.areaDom.innerHTML = datas && datas.map((item,index) => {\n    return `\n        <tr>\n          <td>${index+1}</td>\n          <td>${item.area_name}</td>\n          <td>${item.bch_name}</td>\n          <td>${item.rank_value}</td>\n        </tr>\n    `\n  }).join(\"\")\n\n</#noparse>\n</script>\n\n</html>",
    "bindComponent": "PushMessageOrgProductParams"
  },
  "autoPush": false,
  "receivers": [
    {
      "id": null,
      "receiverType": "chat",
      "receiverName": "重庆区域保险服务群",
      "receiver": "chat5d2c0325195b7f5195151f72f2add464",
      "params": "{\"imgParam\": {\"width\": 750, \"screenshot\": {\"fullPage\": true, \"omitBackground\": true}}}",
      "auditState": 1
    },
    {
      "id": null,
      "receiverType": "chat",
      "receiverName": "江苏区域保险服务群",
      "receiver": "chatb8a5df1d93f4db660dc565af387680e6",
      "params": "{\"imgParam\": {\"width\": 750, \"screenshot\": {\"fullPage\": true, \"omitBackground\": true}}}",
      "auditState": 1
    },
    {
      "id": null,
      "receiverType": "chat",
      "receiverName": "湖北区域保险服务群",
      "receiver": "chat08b478b62206cc36c8bf35fdb2e845e3",
      "params": "{\"imgParam\": {\"width\": 750, \"screenshot\": {\"fullPage\": true, \"omitBackground\": true}}}",
      "auditState": 1
    },
    {
      "id": null,
      "receiverType": "chat",
      "receiverName": "江西区域保险服务群",
      "receiver": "chat551c141f3925b31157a1357263243687",
      "params": "{\"imgParam\": {\"width\": 750, \"screenshot\": {\"fullPage\": true, \"omitBackground\": true}}}",
      "auditState": 1
    },
    {
      "id": null,
      "receiverType": "chat",
      "receiverName": "辽宁区域保险服务群",
      "receiver": "chatf1de47622d4b1662eb313be4dab088e7",
      "params": "{\"imgParam\": {\"width\": 750, \"screenshot\": {\"fullPage\": true, \"omitBackground\": true}}}",
      "auditState": 1
    },
    {
      "id": null,
      "receiverType": "chat",
      "receiverName": "广东区域保险服务群",
      "receiver": "chat08fb36f271def37f6c83d1d8c0da58ef",
      "params": "{\"imgParam\": {\"width\": 750, \"screenshot\": {\"fullPage\": true, \"omitBackground\": true}}}",
      "auditState": 1
    },
    {
      "id": null,
      "receiverType": "chat",
      "receiverName": "河南区域保险服务群",
      "receiver": "chatace73068e310ec13a56ac33ac21fcee7",
      "params": "{\"imgParam\": {\"width\": 750, \"screenshot\": {\"fullPage\": true, \"omitBackground\": true}}}",
      "auditState": 1
    },
    {
      "id": null,
      "receiverType": "chat",
      "receiverName": "海南区域保险业务服务群",
      "receiver": "chat7a86058aeb4d7ccc95f732935c5dcf56",
      "params": "{\"imgParam\": {\"width\": 750, \"screenshot\": {\"fullPage\": true, \"omitBackground\": true}}}",
      "auditState": 1
    },
    {
      "id": null,
      "receiverType": "chat",
      "receiverName": "内蒙区域保险服务群",
      "receiver": "chat558e81ed80b6b5060baa85babbf88379",
      "params": "{\"imgParam\": {\"width\": 750, \"screenshot\": {\"fullPage\": true, \"omitBackground\": true}}}",
      "auditState": 1
    },
    {
      "id": null,
      "receiverType": "chat",
      "receiverName": "山东区域保险服务群",
      "receiver": "chat15070ee012102910a9120f891f2fa7d8",
      "params": "{\"imgParam\": {\"width\": 750, \"screenshot\": {\"fullPage\": true, \"omitBackground\": true}}}",
      "auditState": 1
    },
    {
      "id": null,
      "receiverType": "chat",
      "receiverName": "山西区域保险服务群",
      "receiver": "chat6223c412df1b400279e77db670a884b2",
      "params": "{\"imgParam\": {\"width\": 750, \"screenshot\": {\"fullPage\": true, \"omitBackground\": true}}}",
      "auditState": 1
    },
    {
      "id": null,
      "receiverType": "chat",
      "receiverName": "云南区域保险服务群",
      "receiver": "chat0dd11c3907dad2064f9207c8da0118f3",
      "params": "{\"imgParam\": {\"width\": 750, \"screenshot\": {\"fullPage\": true, \"omitBackground\": true}}}",
      "auditState": 1
    },
    {
      "id": null,
      "receiverType": "chat",
      "receiverName": "四川区域保险服务群",
      "receiver": "chat8a5440a5c3838393e9358f177b0ce8bf",
      "params": "{\"imgParam\": {\"width\": 750, \"screenshot\": {\"fullPage\": true, \"omitBackground\": true}}}",
      "auditState": 1
    },
    {
      "id": null,
      "receiverType": "chat",
      "receiverName": "河北区域保险服务二群",
      "receiver": "chatbbb4589233f5b834469a670e03cf82ad",
      "params": "{\"imgParam\": {\"width\": 750, \"screenshot\": {\"fullPage\": true, \"omitBackground\": true}}}",
      "auditState": 1
    },
    {
      "id": null,
      "receiverType": "chat",
      "receiverName": "河北区域保险服务一群",
      "receiver": "chat315f52572569117ee548413f39142c5f",
      "params": "{\"imgParam\": {\"width\": 750, \"screenshot\": {\"fullPage\": true, \"omitBackground\": true}}}",
      "auditState": 1
    },
    {
      "id": null,
      "receiverType": "chat",
      "receiverName": "甘肃区域保险服务群",
      "receiver": "chat7cd985de20d2318f9e074cc056e84a38",
      "params": "{\"imgParam\": {\"width\": 750, \"screenshot\": {\"fullPage\": true, \"omitBackground\": true}}}",
      "auditState": 1
    },
    {
      "id": null,
      "receiverType": "chat",
      "receiverName": "湖南保险服务群",
      "receiver": "chat2cd1489ab6ac6ea98e060bc37f048d47",
      "params": "{\"imgParam\": {\"width\": 750, \"screenshot\": {\"fullPage\": true, \"omitBackground\": true}}}",
      "auditState": 1
    }
  ]
}


### 个人风云榜-月度评选榜单
POST https://admin.xiaowhale.com/bapi/api/insurance/operation//msg/config
Authorization: {{token}}
Content-Type: application/json

{
  "rule": {
    "id": 52,
    "enabledFlag": 0,
    "ruleCode": "RankEmpAmt",
    "ruleName": "个人风云榜-月度评选榜单",
    "messageType": "image",
    "messageTemplate": "<!DOCTYPE html>\n<html lang=\"en\">\n\n<head>\n  <meta charset=\"UTF-8\">\n  <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n  <title>个人风云榜</title>\n  <style>\n    * {\n      margin: 0;\n      padding: 0;\n    }\n\n    body {\n      font-family: Arial, sans-serif;\n      margin: 0;\n      padding: 0;\n    }\n\n    .copywriting {\n      padding: 20px;\n      font-size: 28px;\n      background-color: #fff;\n    }\n\n    .poster {\n      position: relative;\n      max-width: 750px;\n      margin: 0 auto;\n      background: url('https://safesfiles.oss-cn-beijing.aliyuncs.com/safes/ZHNX33656/月个人风云榜背景_202309211150.png') no-repeat;\n      background-size: 100% 100%;\n      overflow: hidden;\n    }\n\n    .poster-header img {\n      width: 100%;\n      height: auto;\n    }\n\n    .poster-month {\n      position: absolute;\n      top: 112px;\n      right: 106px;\n      width: 80px;\n      text-align: center;\n      font-size: 60px;\n      font-family: math;\n      color: #f6d993;\n    }\n\n    .poster-info,\n    .poster-bottom {\n      color: #f6d993;\n      font-size: 30px;\n    }\n\n    .poster-info {\n      padding: 0 40px 0;\n      text-align: right;\n    }\n\n    .poster-bottom {\n      margin: 10px 0 30px;\n      text-align: center;\n    }\n\n    .poster-organization {\n      margin: 10px 40px;\n      background-color: #9f1515;\n      border-radius: 16px;\n      overflow: hidden;\n    }\n\n    .poster-organization table {\n      width: 100%;\n      border-collapse: collapse;\n      color: #eeeeeef0;\n    }\n\n    .poster-organization table thead th{\n      padding: 6px;\n      font-size: 30px;\n      background: linear-gradient(to bottom,#f2d9b1,#e0b369,#ead0a4); /* 表头背景色 */\n      color: #af1e1c;\n    }\n    table tbody tr{\n      border-bottom: 2px solid #af1e1c;\n    }\n\n    .poster-organization table tbody td {\n      padding: 6px 0;\n      font-size: 26px;\n      text-align: center;\n    }\n   \n    .high-light {\n      color: #dd3737;\n      font-weight: bolder;\n    }\n\n    .title-bold {\n      color: green;\n      font-weight: bolder;\n    }\n\n    .ml-20 {\n      margin-left: 40px;\n    }\n\n    .bold {\n      font-weight: bolder;\n    }\n\n    .emoji-img {\n      width: 44px;\n      height: 44px;\n      vertical-align: sub;\n    }\n    footer{\n      padding: 0 20px 10px;\n      background-color: #fff;\n      font-size:28px;\n    }\n\n    .poster-top{\n      display: flex;\n      justify-content: center;\n    }\n    .poster-top-item{\n      display: flex;\n      flex-direction: column;\n      align-items: center;\n      color: #e0d1b8;\n      font-size: 26px;\n    }\n    .poster-top-item:not(:nth-last-child(1)){\n      margin-right: 24px;\n    }\n    .poster-top-item:nth-child(1){\n      order: 2;\n    }\n    .poster-top-item:nth-child(2){\n      order: 1;\n      margin-top: 30px;\n    }\n    .poster-top-item:nth-child(3){\n      order: 3;\n      margin-top: 50px;\n    }\n    .poster-top-item__avatar{\n      display: flex;\n      justify-content: center;\n      align-items: center;\n      margin-bottom: -30px;\n      width: 176px;\n      height: 200px;\n      background: url('https://safesfiles.oss-cn-beijing.aliyuncs.com/safes/ZHNX33656/金_202309211845.png') no-repeat;\n      background-size: contain;\n    }\n    .avatar{\n      width: 132px;\n      height: 132px;\n      margin-top: 24px;\n      border-radius: 50%;\n    }\n    .poster-top-item__name{\n      position: relative;\n      padding: 0 18px;\n      background: linear-gradient(to right,#dfb267,#eaddc6,#dfb267);\n      color: #af1e1c;\n      font-size: 30px;\n      font-weight: bold;\n      border-radius: 32px;\n    }\n    .rank-img{\n      position: absolute;\n      top: -24px;\n      left: 20px;\n      width: 50px;\n      height: auto;\n    }\n\n  </style>\n</head>\n\n<body>\n  <div class=\"poster\">\n    <div class=\"poster-header\">\n      <img src=\"https://safesfiles.oss-cn-beijing.aliyuncs.com/safes/ZHNX33656/月个人风云榜_202309211143.png\" alt=\"\">\n    </div>\n    <div class=\"poster-month\" id=\"month-2\"></div>\n    <div class=\"poster-info\">\n      <span class=\"ml-20\">统计时间：<span id=\"month-3\"></span>月</span>\n    </div>\n    <div class=\"poster-top\" id=\"top-three\">\n \n    </div>\n\n    <div class=\"poster-organization\" id=\"organization-rank\">\n      <table border=\"0\">\n        <thead>\n          <tr>\n            <th>排名</th>\n            <th>区域</th>\n            <th>分支</th>\n            <th>姓名</th>\n            <th>标准业绩</th>\n          </tr>\n        </thead>\n        <tbody id=\"table-tbody\">\n\n        </tbody>\n      </table>\n    </div>\n    \n    <div class=\"poster-bottom\">\n      <span>再/接/再/厉</span>\n      <span class=\"ml-20\">再/创/佳/绩</span>\n    </div>\n  </div>\n\n  <div class=\"copywriting\">\n    <div class=\"copywriting-header\">\n      <p>\n        <img src=\"https://safesfiles.oss-cn-beijing.aliyuncs.com/safes/ZHNX33656/奋斗_202309211228.png\" class=\"emoji-img\">\n        <span class=\"bold\">超越自我，追求卓越！</span>\n      </p>\n      <p>\n        <img src=\"https://safesfiles.oss-cn-beijing.aliyuncs.com/safes/ZHNX33656/火_202309192057.png\" class=\"emoji-img\">\n        <span class=\"high-light\">B类业务<span class=\"title-bold\"><span id=\"month-1\"></span>月个人风云榜</span></span>新鲜出炉!\n      </p>\n      <div id=\"copywriting-rank\">\n       \n      </div>\n    </div>\n  </div>\n\n  <footer>\n    <img src=\"https://safesfiles.oss-cn-beijing.aliyuncs.com/safes/ZHNX33656/鼓掌_202309192054.png\" class=\"emoji-img\">\n    <img src=\"https://safesfiles.oss-cn-beijing.aliyuncs.com/safes/ZHNX33656/鼓掌_202309192054.png\" class=\"emoji-img\">\n    让我们一起，用最热烈的掌声，恭喜以上荣誉个人！\n  </footer>\n</body>\n\n<script>\n  const doms = {\n    areaDom : document.getElementById('table-tbody'),\n    month1Dom:document.getElementById('month-1'),\n    month2Dom:document.getElementById('month-2'),\n    month3Dom:document.getElementById('month-3'),\n    copywritingDom:document.getElementById('copywriting-rank'),\n    topThreeDom : document.getElementById('top-three')\n  }\n    const data = ${datasJson}\n  const { datas,maxAreaName,yearMonth,areaNames,maxAreaPre } = data\n\n    <#noparse>\n  // 月份\n  doms.month1Dom.innerHTML = yearMonth.monthValue\n  doms.month2Dom.innerHTML = yearMonth.monthValue\n  doms.month3Dom.innerHTML = yearMonth.monthValue\n\n\n  // 填充区域文案部分\n  doms.copywritingDom.innerHTML  = datas && datas.map(item=>{\n    const areaName = `${item.area_name}-${item.emp_name}`\n    const dianzanImg = `<img src=\"https://safesfiles.oss-cn-beijing.aliyuncs.com/safes/ZHNX33656/点赞_202309191527.png\" class=\"emoji-img\">`\n    // 排名第一区域\n    if(item.rank_num === 1){\n      // 是否多次上榜\n      return `\n      <p>\n          <span class=\"high-light\">${areaName}</span>当月标保突破<span class=\"title-bold\">${item.rank_value}万</span>，\n          位列榜首！${dianzanImg}\n        </p>\n      `\n    }else if([2,3].includes(item.rank_num)){  \n      // 2,3 名多次上榜\n      const message = item.rankTimes > 1 ? `不甘示弱，同时<span class=\"bold\">当年第${item.rankTimes}次上榜！</span>` : `干劲十足，<span class=\"bold\">首次入榜</span>位列第${item.rank_num}！`\n      return `<p><span class=\"high-light\">${areaName}</span>${message}${dianzanImg}</p>`\n    }\n  }).join(\"\")\n\n  // 头像展示前三个，并列第三的话取默认前面的\n  const topThreeList = datas && datas.slice(0,3)\n  doms.topThreeDom.innerHTML = topThreeList && topThreeList.map((item,index)=>{\n    // 设置默认头像\n    const avatarImg = item.avatar || 'https://safesfiles.oss-cn-beijing.aliyuncs.com/safes/ZHNX33656/默认头像_202309211954.png'\n    const rankImg = {\n      1:'https://safesfiles.oss-cn-beijing.aliyuncs.com/safes/ZHNX33656/NO1_202309211853.png',\n      2:'https://safesfiles.oss-cn-beijing.aliyuncs.com/safes/ZHNX33656/NO2_202309211958.png',\n      3:'https://safesfiles.oss-cn-beijing.aliyuncs.com/safes/ZHNX33656/NO3_202309211958.png',\n    }\n    // 排名头像背景\n    const rankBg = {\n      1: 'https://safesfiles.oss-cn-beijing.aliyuncs.com/safes/ZHNX33656/金_202309211845.png',\n      2:'https://safesfiles.oss-cn-beijing.aliyuncs.com/safes/ZHNX33656/银_202309211955.png',\n      3:'https://safesfiles.oss-cn-beijing.aliyuncs.com/safes/ZHNX33656/铜_202309211957.png',\n    }\n    const name = item.bch_name ? `${item.bch_name}-${item.emp_name}` : item.emp_name\n    const rank = item.rank_num\n    return `\n    <div class=\"poster-top-item\">\n        <div class=\"poster-top-item__avatar\" style=\"background-image: url(${rankBg[rank]})\">\n          <img src=\"${avatarImg}\" alt=\"\" class=\"avatar\">\n        </div>\n        <div class=\"poster-top-item__name\">\n          <img src=\"${rankImg[rank]}\" alt=\"No1\" class=\"rank-img\">\n          <span>${name}</span>\n        </div>\n        <p>标准保费</p>\n        <span>${item.rank_value}万</span>\n      </div>\n    `\n  }).join(\"\")\n\n\n  // 填充列表模块\n  doms.areaDom.innerHTML = datas && datas.map((item,index) => {\n    return `\n        <tr>\n          <td>${formatText(item.rank_num)}</td>\n          <td>${formatText(item.area_name)}</td>\n          <td>${formatText(item.bch_name)}</td>\n          <td>${formatText(item.emp_name)}</td>\n          <td>${formatText(item.rank_value)}万</td>\n        </tr>\n    `\n  }).join(\"\")\n\n  function formatText(text){\n    return text == null ? '-' : text\n  }\n\n    </#noparse>\n</script>\n\n</html>",
    "startDate": "2023-10-12",
    "endDate": "2023-12-31",
    "cronRule": "0 0 10 5 * ?",
    "params": "{}",
    "state": 1,
    "remark": null,
    "createBy": "ZHNX09760",
    "updateBy": "ZHNX09760",
    "bindComponent": "PushMessageOrgProductParams"
  },
  "autoPush": false,
  "receivers":[
    {
      "id": null,
      "receiverType": "chat",
      "receiverName": "重庆区域保险服务群",
      "receiver": "chat5d2c0325195b7f5195151f72f2add464",
      "params": "{\"imgParam\": {\"width\": 750, \"screenshot\": {\"fullPage\": true, \"omitBackground\": true}}}",
      "auditState": 1
    },
    {
      "id": null,
      "receiverType": "chat",
      "receiverName": "江苏区域保险服务群",
      "receiver": "chatb8a5df1d93f4db660dc565af387680e6",
      "params": "{\"imgParam\": {\"width\": 750, \"screenshot\": {\"fullPage\": true, \"omitBackground\": true}}}",
      "auditState": 1
    },
    {
      "id": null,
      "receiverType": "chat",
      "receiverName": "湖北区域保险服务群",
      "receiver": "chat08b478b62206cc36c8bf35fdb2e845e3",
      "params": "{\"imgParam\": {\"width\": 750, \"screenshot\": {\"fullPage\": true, \"omitBackground\": true}}}",
      "auditState": 1
    },
    {
      "id": null,
      "receiverType": "chat",
      "receiverName": "江西区域保险服务群",
      "receiver": "chat551c141f3925b31157a1357263243687",
      "params": "{\"imgParam\": {\"width\": 750, \"screenshot\": {\"fullPage\": true, \"omitBackground\": true}}}",
      "auditState": 1
    },
    {
      "id": null,
      "receiverType": "chat",
      "receiverName": "辽宁区域保险服务群",
      "receiver": "chatf1de47622d4b1662eb313be4dab088e7",
      "params": "{\"imgParam\": {\"width\": 750, \"screenshot\": {\"fullPage\": true, \"omitBackground\": true}}}",
      "auditState": 1
    },
    {
      "id": null,
      "receiverType": "chat",
      "receiverName": "广东区域保险服务群",
      "receiver": "chat08fb36f271def37f6c83d1d8c0da58ef",
      "params": "{\"imgParam\": {\"width\": 750, \"screenshot\": {\"fullPage\": true, \"omitBackground\": true}}}",
      "auditState": 1
    },
    {
      "id": null,
      "receiverType": "chat",
      "receiverName": "河南区域保险服务群",
      "receiver": "chatace73068e310ec13a56ac33ac21fcee7",
      "params": "{\"imgParam\": {\"width\": 750, \"screenshot\": {\"fullPage\": true, \"omitBackground\": true}}}",
      "auditState": 1
    },
    {
      "id": null,
      "receiverType": "chat",
      "receiverName": "海南区域保险业务服务群",
      "receiver": "chat7a86058aeb4d7ccc95f732935c5dcf56",
      "params": "{\"imgParam\": {\"width\": 750, \"screenshot\": {\"fullPage\": true, \"omitBackground\": true}}}",
      "auditState": 1
    },
    {
      "id": null,
      "receiverType": "chat",
      "receiverName": "内蒙区域保险服务群",
      "receiver": "chat558e81ed80b6b5060baa85babbf88379",
      "params": "{\"imgParam\": {\"width\": 750, \"screenshot\": {\"fullPage\": true, \"omitBackground\": true}}}",
      "auditState": 1
    },
    {
      "id": null,
      "receiverType": "chat",
      "receiverName": "山东区域保险服务群",
      "receiver": "chat15070ee012102910a9120f891f2fa7d8",
      "params": "{\"imgParam\": {\"width\": 750, \"screenshot\": {\"fullPage\": true, \"omitBackground\": true}}}",
      "auditState": 1
    },
    {
      "id": null,
      "receiverType": "chat",
      "receiverName": "山西区域保险服务群",
      "receiver": "chat6223c412df1b400279e77db670a884b2",
      "params": "{\"imgParam\": {\"width\": 750, \"screenshot\": {\"fullPage\": true, \"omitBackground\": true}}}",
      "auditState": 1
    },
    {
      "id": null,
      "receiverType": "chat",
      "receiverName": "云南区域保险服务群",
      "receiver": "chat0dd11c3907dad2064f9207c8da0118f3",
      "params": "{\"imgParam\": {\"width\": 750, \"screenshot\": {\"fullPage\": true, \"omitBackground\": true}}}",
      "auditState": 1
    },
    {
      "id": null,
      "receiverType": "chat",
      "receiverName": "四川区域保险服务群",
      "receiver": "chat8a5440a5c3838393e9358f177b0ce8bf",
      "params": "{\"imgParam\": {\"width\": 750, \"screenshot\": {\"fullPage\": true, \"omitBackground\": true}}}",
      "auditState": 1
    },
    {
      "id": null,
      "receiverType": "chat",
      "receiverName": "河北区域保险服务二群",
      "receiver": "chatbbb4589233f5b834469a670e03cf82ad",
      "params": "{\"imgParam\": {\"width\": 750, \"screenshot\": {\"fullPage\": true, \"omitBackground\": true}}}",
      "auditState": 1
    },
    {
      "id": null,
      "receiverType": "chat",
      "receiverName": "河北区域保险服务一群",
      "receiver": "chat315f52572569117ee548413f39142c5f",
      "params": "{\"imgParam\": {\"width\": 750, \"screenshot\": {\"fullPage\": true, \"omitBackground\": true}}}",
      "auditState": 1
    },
    {
      "id": null,
      "receiverType": "chat",
      "receiverName": "甘肃区域保险服务群",
      "receiver": "chat7cd985de20d2318f9e074cc056e84a38",
      "params": "{\"imgParam\": {\"width\": 750, \"screenshot\": {\"fullPage\": true, \"omitBackground\": true}}}",
      "auditState": 1
    },
    {
      "id": null,
      "receiverType": "chat",
      "receiverName": "湖南保险服务群",
      "receiver": "chat2cd1489ab6ac6ea98e060bc37f048d47",
      "params": "{\"imgParam\": {\"width\": 750, \"screenshot\": {\"fullPage\": true, \"omitBackground\": true}}}",
      "auditState": 1
    }
  ]
}


### 分支风云榜-服务榜
POST https://admin.xiaowhale.com/bapi/api/insurance/operation//msg/config
Authorization: {{token}}
Content-Type: application/json

{
  "rule": {
    "id": 46,
    "enabledFlag": 0,
    "ruleCode": "RankBchCust",
    "ruleName": "分支风云榜-服务榜",
    "messageType": "image",
    "messageTemplate": "<!DOCTYPE html>\n<html lang=\"en\">\n\n<head>\n  <meta charset=\"UTF-8\">\n  <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n  <title>分支风云榜-服务榜</title>\n  <style>\n    * {\n      margin: 0;\n      padding: 0;\n    }\n\n    body {\n      font-family: Arial, sans-serif;\n      margin: 0;\n      padding: 0;\n    }\n\n    .copywriting {\n      padding: 20px;\n      font-size: 28px;\n      background-color: #fff;\n    }\n\n    .poster {\n      position: relative;\n      max-width: 750px;\n      margin: 0 auto;\n      background-color: #af1e1c;\n      overflow: hidden;\n    }\n\n    .poster img {\n      width: 100%;\n      height: auto;\n    }\n\n    .poster-month {\n      position: absolute;\n      top: 80px;\n      right: 120px;\n      width: 72px;\n      text-align: center;\n      font-size: 60px;\n      color: #f6d993;\n    }\n\n    .poster-info,\n    .poster-bottom {\n      text-align: center;\n      color: #f6d993;\n      font-size: 28px;\n    }\n\n    .poster-info {\n      width: 100%;\n    }\n\n    .poster-bottom {\n      margin: 10px 0 30px;\n    }\n    .poster-center{\n      margin: 20px 40px 10px;\n    }\n\n    .poster-organization {\n      margin: -20px 8px;\n      padding: 6px 10px;\n      background: linear-gradient(to bottom,#dd433e,#b31616,#dd433e) ;\n      border: 16px solid #7a1610;\n    }\n\n    .poster-organization table {\n      width: 100%;\n      border-collapse: collapse;\n      color: #fff2d2;\n    }\n\n    .poster-organization table thead {\n      border-bottom: 2px solid #f6d993;\n    }\n    .poster-organization table thead th{\n      padding: 6px 0;\n      font-size: 30px;\n    }\n\n    .poster-organization table tbody td {\n      padding: 2px 0 0;\n      font-size: 28px;\n      text-align: center;\n    }\n   \n    .high-light {\n      color: #dd3737;\n      font-weight: bolder;\n    }\n\n    .title-bold {\n      color: green;\n      font-weight: bolder;\n    }\n\n    .ml-6 {\n      margin-left: 12px;\n    }\n\n    .ml-20 {\n      margin-left: 40px;\n    }\n\n    .bold {\n      font-weight: bolder;\n    }\n\n    .mt-5 {\n      margin-top: 10px;\n    }\n\n    .emoji-img {\n      width: 44px;\n      height: 44px;\n      margin-right: 3px;\n      vertical-align: sub;\n    }\n \n  </style>\n</head>\n\n<body>\n  <div class=\"poster\">\n    <div class=\"poster-header\">\n      <img src=\"https://safesfiles.oss-cn-beijing.aliyuncs.com/safes/ZHNX33656/月分支风云榜服务榜_202309221430.png\" alt=\"\">\n    </div>\n    <div class=\"poster-month\" id=\"month-2\"></div>\n    <div class=\"poster-info\">\n      <span>统计口径：当月小程序注册客户数/个</span>\n      <span class=\"ml-20\">统计时间：<span id=\"month-3\"></span>月</span>\n    </div>\n\n    <div class=\"poster-center\">\n      <img src=\"https://safesfiles.oss-cn-beijing.aliyuncs.com/safes/ZHNX33656/横幅_202309191932.png\" alt=\"\">\n      <div class=\"poster-organization\" id=\"organization-rank\">\n        <table border=\"0\">\n          <thead>\n            <tr>\n              <th>序号</th>\n              <th>区域</th>\n              <th>分支</th>\n              <th>注册客户数</th>\n            </tr>\n          </thead>\n          <tbody id=\"table-tbody\">\n  \n          </tbody>\n        </table>\n      </div>\n      <img src=\"https://safesfiles.oss-cn-beijing.aliyuncs.com/safes/ZHNX33656/横幅_202309191932.png\" alt=\"\">\n\n    </div>\n    \n    <div class=\"poster-bottom\">\n      <span>再/接/再/厉</span>\n      <span class=\"ml-20\">再/创/佳/绩</span>\n    </div>\n  </div>\n\n  <div class=\"copywriting\">\n    <div class=\"copywriting-header ml-6\">\n      <p class=\"bold\"><img src=\"https://safesfiles.oss-cn-beijing.aliyuncs.com/safes/ZHNX33656/庆祝_202309192056.png\"\n          class=\"emoji-img\">统一思想、明确思路、大步前行！</p>\n      <p><img src=\"https://safesfiles.oss-cn-beijing.aliyuncs.com/safes/ZHNX33656/火_202309192057.png\"\n          class=\"emoji-img\"><span class=\"high-light\">B类业务<span class=\"title-bold\"><span id=\"month-1\"></span>月分支风云榜（服务榜）</span></span>新鲜出炉!\n      </p>\n     \n        <img src=\"https://safesfiles.oss-cn-beijing.aliyuncs.com/safes/ZHNX33656/努力_202309192033.png\" class=\"emoji-img\">\n          目前排名第一的是<span class=\"high-light\" id=\"first-orgname\"></span>，当月注册客户数已达<span class=\"title-bold\"><span id=\"max-area-value\"></span>人</span>。</p>\n      <div class=\"copywriting-content mt-5\">\n        其中<span class=\"bold\">【<span id=\"ranked-area\"></span>】</span>区域各显神通，\n        共<span id=\"ranked-number\"></span>入围榜单。\n        <span class=\"title-bold\" id=\"max-area-name\"></span>分支更是高达<span class=\"high-light\" id=\"max-area-ratio\"></span>的霸榜率！详情请查看分支荣誉榜单~\n      </div>\n    </div>\n  </div>\n</body>\n\n<script>\n\nconst data = ${datasJson}\n\nconst { datas,maxAreaName,yearMonth,areaNames,maxAreaPre } = data\n\n\n  const doms = {\n    areaDom:document.getElementById('table-tbody'),\n    firstOrgDom : document.querySelector('#first-orgname'),\n    rankedAreaDom : document.querySelector('#ranked-area'),\n    maxAreaRatioDom:document.getElementById('max-area-ratio'),\n    maxAreaValueDom:document.getElementById('max-area-value'),\n    maxAreaDom:document.getElementById('max-area-name'),\n    rankedNumberDom:document.getElementById('ranked-number'),\n    month1Dom:document.getElementById('month-1'),\n    month2Dom:document.getElementById('month-2'),\n    month3Dom:document.getElementById('month-3'),\n  }\n\n<#noparse>\n  // 位列第一的分支名称\n  const firstOrgList = (datas || []).filter(v => v.rank_num === 1)\n  const firstOrgName = firstOrgList.map(v => v.area_name + '-' +v.bch_name).join(\"、\");\n  doms.firstOrgDom.innerHTML = firstOrgName\n\n  // 第一分支标保\n  doms.maxAreaValueDom.innerHTML = firstOrgList[0].rank_value\n\n  // 上榜区域\n  const areaNameList = (areaNames || []).map(v=> v.slice(0,-2))\n  doms.rankedAreaDom.innerHTML = areaNameList.join('、')\n\n  // 上榜区域数量\n  doms.rankedNumberDom.innerHTML = datas.length + '家分支'\n\n  // 最大霸榜率\n  const maxAreaValue = maxAreaPre ? parseInt((maxAreaPre / datas?.length) * 100 ) : 0\n  doms.maxAreaRatioDom.innerHTML = maxAreaValue + '%'\n  doms.maxAreaDom.innerHTML = maxAreaName\n\n  // 月份\n  doms.month1Dom.innerHTML = yearMonth.monthValue\n  doms.month2Dom.innerHTML = yearMonth.monthValue\n  doms.month3Dom.innerHTML = yearMonth.monthValue\n\n  // 填充列表模块\n  doms.areaDom.innerHTML = datas && datas.map((item,index) => {\n    return `\n        <tr>\n          <td>${index+1}</td>\n          <td>${item.area_name}</td>\n          <td>${item.bch_name}</td>\n          <td>${item.rank_value}</td>\n        </tr>\n    `\n  }).join(\"\")\n\n</#noparse>\n</script>\n\n</html>",
    "startDate": "2023-10-12",
    "endDate": "2023-12-31",
    "cronRule": "0 0 9 5 * ?",
    "params": "{}",
    "state": 1,
    "remark": null,
    "createBy": "ZHNX09760",
    "updateBy": "ZHNX09760",
    "bindComponent": "PushMessageOrgProductParams"
  },
  "autoPush": false,
  "receivers": [
    {
      "id": null,
      "receiverType": "chat",
      "receiverName": "重庆区域保险服务群",
      "receiver": "chat5d2c0325195b7f5195151f72f2add464",
      "params": "{\"imgParam\": {\"width\": 750, \"screenshot\": {\"fullPage\": true, \"omitBackground\": true}}}",
      "auditState": 1
    },
    {
      "id": null,
      "receiverType": "chat",
      "receiverName": "江苏区域保险服务群",
      "receiver": "chatb8a5df1d93f4db660dc565af387680e6",
      "params": "{\"imgParam\": {\"width\": 750, \"screenshot\": {\"fullPage\": true, \"omitBackground\": true}}}",
      "auditState": 1
    },
    {
      "id": null,
      "receiverType": "chat",
      "receiverName": "湖北区域保险服务群",
      "receiver": "chat08b478b62206cc36c8bf35fdb2e845e3",
      "params": "{\"imgParam\": {\"width\": 750, \"screenshot\": {\"fullPage\": true, \"omitBackground\": true}}}",
      "auditState": 1
    },
    {
      "id": null,
      "receiverType": "chat",
      "receiverName": "江西区域保险服务群",
      "receiver": "chat551c141f3925b31157a1357263243687",
      "params": "{\"imgParam\": {\"width\": 750, \"screenshot\": {\"fullPage\": true, \"omitBackground\": true}}}",
      "auditState": 1
    },
    {
      "id": null,
      "receiverType": "chat",
      "receiverName": "辽宁区域保险服务群",
      "receiver": "chatf1de47622d4b1662eb313be4dab088e7",
      "params": "{\"imgParam\": {\"width\": 750, \"screenshot\": {\"fullPage\": true, \"omitBackground\": true}}}",
      "auditState": 1
    },
    {
      "id": null,
      "receiverType": "chat",
      "receiverName": "广东区域保险服务群",
      "receiver": "chat08fb36f271def37f6c83d1d8c0da58ef",
      "params": "{\"imgParam\": {\"width\": 750, \"screenshot\": {\"fullPage\": true, \"omitBackground\": true}}}",
      "auditState": 1
    },
    {
      "id": null,
      "receiverType": "chat",
      "receiverName": "河南区域保险服务群",
      "receiver": "chatace73068e310ec13a56ac33ac21fcee7",
      "params": "{\"imgParam\": {\"width\": 750, \"screenshot\": {\"fullPage\": true, \"omitBackground\": true}}}",
      "auditState": 1
    },
    {
      "id": null,
      "receiverType": "chat",
      "receiverName": "海南区域保险业务服务群",
      "receiver": "chat7a86058aeb4d7ccc95f732935c5dcf56",
      "params": "{\"imgParam\": {\"width\": 750, \"screenshot\": {\"fullPage\": true, \"omitBackground\": true}}}",
      "auditState": 1
    },
    {
      "id": null,
      "receiverType": "chat",
      "receiverName": "内蒙区域保险服务群",
      "receiver": "chat558e81ed80b6b5060baa85babbf88379",
      "params": "{\"imgParam\": {\"width\": 750, \"screenshot\": {\"fullPage\": true, \"omitBackground\": true}}}",
      "auditState": 1
    },
    {
      "id": null,
      "receiverType": "chat",
      "receiverName": "山东区域保险服务群",
      "receiver": "chat15070ee012102910a9120f891f2fa7d8",
      "params": "{\"imgParam\": {\"width\": 750, \"screenshot\": {\"fullPage\": true, \"omitBackground\": true}}}",
      "auditState": 1
    },
    {
      "id": null,
      "receiverType": "chat",
      "receiverName": "山西区域保险服务群",
      "receiver": "chat6223c412df1b400279e77db670a884b2",
      "params": "{\"imgParam\": {\"width\": 750, \"screenshot\": {\"fullPage\": true, \"omitBackground\": true}}}",
      "auditState": 1
    },
    {
      "id": null,
      "receiverType": "chat",
      "receiverName": "云南区域保险服务群",
      "receiver": "chat0dd11c3907dad2064f9207c8da0118f3",
      "params": "{\"imgParam\": {\"width\": 750, \"screenshot\": {\"fullPage\": true, \"omitBackground\": true}}}",
      "auditState": 1
    },
    {
      "id": null,
      "receiverType": "chat",
      "receiverName": "四川区域保险服务群",
      "receiver": "chat8a5440a5c3838393e9358f177b0ce8bf",
      "params": "{\"imgParam\": {\"width\": 750, \"screenshot\": {\"fullPage\": true, \"omitBackground\": true}}}",
      "auditState": 1
    },
    {
      "id": null,
      "receiverType": "chat",
      "receiverName": "河北区域保险服务二群",
      "receiver": "chatbbb4589233f5b834469a670e03cf82ad",
      "params": "{\"imgParam\": {\"width\": 750, \"screenshot\": {\"fullPage\": true, \"omitBackground\": true}}}",
      "auditState": 1
    },
    {
      "id": null,
      "receiverType": "chat",
      "receiverName": "河北区域保险服务一群",
      "receiver": "chat315f52572569117ee548413f39142c5f",
      "params": "{\"imgParam\": {\"width\": 750, \"screenshot\": {\"fullPage\": true, \"omitBackground\": true}}}",
      "auditState": 1
    },
    {
      "id": null,
      "receiverType": "chat",
      "receiverName": "甘肃区域保险服务群",
      "receiver": "chat7cd985de20d2318f9e074cc056e84a38",
      "params": "{\"imgParam\": {\"width\": 750, \"screenshot\": {\"fullPage\": true, \"omitBackground\": true}}}",
      "auditState": 1
    },
    {
      "id": null,
      "receiverType": "chat",
      "receiverName": "湖南保险服务群",
      "receiver": "chat2cd1489ab6ac6ea98e060bc37f048d47",
      "params": "{\"imgParam\": {\"width\": 750, \"screenshot\": {\"fullPage\": true, \"omitBackground\": true}}}",
      "auditState": 1
    }
  ]
}


### 分支风云榜-业绩榜
POST https://admin.xiaowhale.com/bapi/api/insurance/operation//msg/config
Authorization: {{token}}
Content-Type: application/json

{
  "rule": {
    "id": 49,
    "enabledFlag": 0,
    "ruleCode": "RankBchAmt",
    "ruleName": "分支风云榜-业绩榜",
    "messageType": "image",
    "messageTemplate": "<!DOCTYPE html>\n<html lang=\"en\">\n\n<head>\n  <meta charset=\"UTF-8\">\n  <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n  <title>分支风云榜-业绩榜</title>\n  <style>\n    * {\n      margin: 0;\n      padding: 0;\n    }\n\n    body {\n      font-family: Arial, sans-serif;\n      margin: 0;\n      padding: 0;\n    }\n\n    .copywriting {\n      max-width: 750px;\n      margin: 0 auto;\n      padding: 10px;\n      font-size: 26px;\n      background-color: #fff;\n    }\n\n    .poster {\n      position: relative;\n      max-width: 750px;\n      margin: 0 auto;\n      background-color: #af1e1c;\n      overflow: hidden;\n    }\n\n    .poster img {\n      width: 100%;\n      height: auto;\n    }\n\n    .poster-month {\n      position: absolute;\n      top: 82px;\n      right: 117px;\n      width: 62px;\n      text-align: center;\n      font-size: 62px;\n      font-family: math;\n      color: #f6d993;\n    }\n\n    .poster-info,\n    .poster-bottom {\n      color: #f6d993;\n      font-size: 28px;\n    }\n\n    .poster-info {\n      display: flex;\n      justify-content: space-between;\n      padding: 0 40px;\n    }\n\n    .poster-bottom {\n      text-align: center;\n      margin: 10px 0 30px;\n    }\n    .poster-center{\n      margin: 20px 40px 10px;\n    }\n\n    .poster-organization {\n      margin: -20px 8px;\n      padding: 6px 10px;\n      background: linear-gradient(to bottom,#dd433e,#b31616,#dd433e) ;\n      border: 16px solid #7a1610;\n    }\n\n    .poster-organization table {\n      width: 100%;\n      border-collapse: collapse;\n      color: #fff2d2;\n    }\n\n    .poster-organization table thead {\n      border-bottom: 1px solid #f6d993;\n    }\n    .poster-organization table thead th{\n      padding: 6px 0;\n      font-size: 24px;\n    }\n\n    .poster-organization table tbody td {\n      padding: 1px 0 0;\n      font-size: 24px;\n      text-align: center;\n    }\n   \n    .high-light {\n      color: #dd3737;\n      font-weight: bolder;\n    }\n\n    .title-bold {\n      color: green;\n      font-weight: bolder;\n    }\n\n    .ml-6 {\n      margin-left: 12px;\n    }\n\n    .ml-20 {\n      margin-left: 40px;\n    }\n\n    .bold {\n      font-weight: bolder;\n    }\n\n    .mt-5 {\n      margin-top: 10px;\n    }\n\n    .emoji-img {\n      width: 44px;\n      height: 44px;\n      margin-right: 3px;\n      vertical-align: sub;\n    }\n \n  </style>\n</head>\n\n<body>\n  <div class=\"poster\">\n    <div class=\"poster-header\">\n      <img src=\"https://safesfiles.oss-cn-beijing.aliyuncs.com/safes/ZHNX33656/月分支风云榜-业绩榜头_202309191844.png\" alt=\"\">\n    </div>\n    <div class=\"poster-month\" id=\"month-2\"></div>\n    <div class=\"poster-info\">\n      <span>统计口径：当月人均标准业绩/元</span>\n      <span class=\"ml-20\">统计时间：<span id=\"month-3\"></span>月</span>\n    </div>\n\n    <div class=\"poster-center\">\n      <img src=\"https://safesfiles.oss-cn-beijing.aliyuncs.com/safes/ZHNX33656/横幅_202309191932.png\" alt=\"\">\n      <div class=\"poster-organization\" id=\"organization-rank\">\n        <table border=\"0\">\n          <thead>\n            <tr>\n              <th>序号</th>\n              <th>区域</th>\n              <th>分支</th>\n              <th>人均标准业绩</th>\n            </tr>\n          </thead>\n          <tbody id=\"table-tbody\">\n  \n          </tbody>\n        </table>\n      </div>\n      <img src=\"https://safesfiles.oss-cn-beijing.aliyuncs.com/safes/ZHNX33656/横幅_202309191932.png\" alt=\"\">\n\n    </div>\n    \n    <div class=\"poster-bottom\">\n      <span>再/接/再/厉</span>\n      <span class=\"ml-20\">再/创/佳/绩</span>\n    </div>\n  </div>\n\n  <div class=\"copywriting\">\n    <div class=\"copywriting-header ml-6\">\n      <p class=\"bold\"><img src=\"https://safesfiles.oss-cn-beijing.aliyuncs.com/safes/ZHNX33656/庆祝_202309192056.png\"\n          class=\"emoji-img\">统一思想、明确思路、大步前行！</p>\n      <p><img src=\"https://safesfiles.oss-cn-beijing.aliyuncs.com/safes/ZHNX33656/火_202309192057.png\"\n          class=\"emoji-img\"><span class=\"high-light\">B类业务<span class=\"title-bold\"><span id=\"month-1\"></span>月分支风云榜（业绩榜）</span></span>新鲜出炉!</p>\n  \n      <p><img src=\"https://safesfiles.oss-cn-beijing.aliyuncs.com/safes/ZHNX33656/努力_202309192033.png\"\n          class=\"emoji-img\">目前排名第一的是 <span class=\"high-light\" id=\"first-orgname\"></span>，人均标保已达<span class=\"title-bold\"><span id=\"max-area-value\"></span>元</span></p>\n      <div class=\"copywriting-content mt-5\">\n        其中<span class=\"bold\">【<span id=\"ranked-area\"></span>】区域</span>\n        各显神通，共<span id=\"ranked-number\"></span>入围榜单。<span class=\"title-bold\" id=\"max-area-name\"></span>分支更是高达\n        <span class=\"high-light\" id=\"max-area-ratio\"></span>的霸榜率！详情请查看分支荣誉榜单~\n      </div>\n    </div>\n  </div>\n</body>\n\n<script>\n\n  const data = ${datasJson}\n  const { datas,areaNames,maxAreaPre,yearMonth,maxAreaName }= data\n  const tableData = datas\n\n  const doms = {\n    areaDom:document.getElementById('table-tbody'),\n    firstOrgDom : document.querySelector('#first-orgname'),\n    rankedAreaDom : document.querySelector('#ranked-area'),\n    maxAreaRatioDom:document.getElementById('max-area-ratio'),\n    maxAreaValueDom:document.getElementById('max-area-value'),\n    rankedNumberDom:document.getElementById('ranked-number'),\n    month1Dom:document.getElementById('month-1'),\n    month2Dom:document.getElementById('month-2'),\n    month3Dom:document.getElementById('month-3'),\n    maxAreaNameDom:document.getElementById('max-area-name'),\n  }\n\n<#noparse>\n  // 位列第一的分支名称\n  const firstOrgList = (tableData || []).filter(v => v.rank_num === 1)\n  const firstOrgName = firstOrgList.map(v => v.area_name + '-' + v.bch_name).join(\"、\");\n  doms.firstOrgDom.innerHTML = firstOrgName\n\n  // 第一分支标保\n  doms.maxAreaValueDom.innerHTML = firstOrgList[0].rank_value\n\n  // 上榜区域\n  const areaNameList = (areaNames || []).map(v=> v.slice(0,-2))\n  doms.rankedAreaDom.innerHTML = areaNameList.join('、')\n\n  // 上榜区域数量\n  doms.rankedNumberDom.innerHTML = tableData.length + '家分支'\n\n  // 最大霸榜率\n  const maxAreaValue = maxAreaPre ? parseInt((maxAreaPre / tableData?.length) * 100 ) : 0\n  doms.maxAreaRatioDom.innerHTML = maxAreaValue + '%'\n\n  doms.maxAreaNameDom.innerHTML = maxAreaName\n\n  // 月份\n  doms.month1Dom.innerHTML = yearMonth.monthValue\n  doms.month2Dom.innerHTML = yearMonth.monthValue\n  doms.month3Dom.innerHTML = yearMonth.monthValue\n\n  // 填充列表模块\n  doms.areaDom.innerHTML = tableData && tableData.map((item,index) => {\n    return `\n        <tr>\n          <td>${index+1}</td>\n          <td>${item.area_name}</td>\n          <td>${item.bch_name}</td>\n          <td>${item.rank_value}</td>\n        </tr>\n    `\n  }).join(\"\")\n  \n  </#noparse>\n</script>\n\n</html>",
    "startDate": "2023-10-01",
    "endDate": "2023-12-31",
    "cronRule": "0 0 9 5 * ?",
    "params": "{}",
    "state": 1,
    "remark": null,
    "createBy": "ZHNX09760",
    "updateBy": "ZHNX09760",
    "bindComponent": "PushMessageOrgProductParams"
  },
  "autoPush": false,
  "receivers": [
    {
      "id": null,
      "receiverType": "chat",
      "receiverName": "重庆区域保险服务群",
      "receiver": "chat5d2c0325195b7f5195151f72f2add464",
      "params": "{\"imgParam\": {\"width\": 750, \"screenshot\": {\"fullPage\": true, \"omitBackground\": true}}}",
      "auditState": 1
    },
    {
      "id": null,
      "receiverType": "chat",
      "receiverName": "江苏区域保险服务群",
      "receiver": "chatb8a5df1d93f4db660dc565af387680e6",
      "params": "{\"imgParam\": {\"width\": 750, \"screenshot\": {\"fullPage\": true, \"omitBackground\": true}}}",
      "auditState": 1
    },
    {
      "id": null,
      "receiverType": "chat",
      "receiverName": "湖北区域保险服务群",
      "receiver": "chat08b478b62206cc36c8bf35fdb2e845e3",
      "params": "{\"imgParam\": {\"width\": 750, \"screenshot\": {\"fullPage\": true, \"omitBackground\": true}}}",
      "auditState": 1
    },
    {
      "id": null,
      "receiverType": "chat",
      "receiverName": "江西区域保险服务群",
      "receiver": "chat551c141f3925b31157a1357263243687",
      "params": "{\"imgParam\": {\"width\": 750, \"screenshot\": {\"fullPage\": true, \"omitBackground\": true}}}",
      "auditState": 1
    },
    {
      "id": null,
      "receiverType": "chat",
      "receiverName": "辽宁区域保险服务群",
      "receiver": "chatf1de47622d4b1662eb313be4dab088e7",
      "params": "{\"imgParam\": {\"width\": 750, \"screenshot\": {\"fullPage\": true, \"omitBackground\": true}}}",
      "auditState": 1
    },
    {
      "id": null,
      "receiverType": "chat",
      "receiverName": "广东区域保险服务群",
      "receiver": "chat08fb36f271def37f6c83d1d8c0da58ef",
      "params": "{\"imgParam\": {\"width\": 750, \"screenshot\": {\"fullPage\": true, \"omitBackground\": true}}}",
      "auditState": 1
    },
    {
      "id": null,
      "receiverType": "chat",
      "receiverName": "河南区域保险服务群",
      "receiver": "chatace73068e310ec13a56ac33ac21fcee7",
      "params": "{\"imgParam\": {\"width\": 750, \"screenshot\": {\"fullPage\": true, \"omitBackground\": true}}}",
      "auditState": 1
    },
    {
      "id": null,
      "receiverType": "chat",
      "receiverName": "海南区域保险业务服务群",
      "receiver": "chat7a86058aeb4d7ccc95f732935c5dcf56",
      "params": "{\"imgParam\": {\"width\": 750, \"screenshot\": {\"fullPage\": true, \"omitBackground\": true}}}",
      "auditState": 1
    },
    {
      "id": null,
      "receiverType": "chat",
      "receiverName": "内蒙区域保险服务群",
      "receiver": "chat558e81ed80b6b5060baa85babbf88379",
      "params": "{\"imgParam\": {\"width\": 750, \"screenshot\": {\"fullPage\": true, \"omitBackground\": true}}}",
      "auditState": 1
    },
    {
      "id": null,
      "receiverType": "chat",
      "receiverName": "山东区域保险服务群",
      "receiver": "chat15070ee012102910a9120f891f2fa7d8",
      "params": "{\"imgParam\": {\"width\": 750, \"screenshot\": {\"fullPage\": true, \"omitBackground\": true}}}",
      "auditState": 1
    },
    {
      "id": null,
      "receiverType": "chat",
      "receiverName": "山西区域保险服务群",
      "receiver": "chat6223c412df1b400279e77db670a884b2",
      "params": "{\"imgParam\": {\"width\": 750, \"screenshot\": {\"fullPage\": true, \"omitBackground\": true}}}",
      "auditState": 1
    },
    {
      "id": null,
      "receiverType": "chat",
      "receiverName": "云南区域保险服务群",
      "receiver": "chat0dd11c3907dad2064f9207c8da0118f3",
      "params": "{\"imgParam\": {\"width\": 750, \"screenshot\": {\"fullPage\": true, \"omitBackground\": true}}}",
      "auditState": 1
    },
    {
      "id": null,
      "receiverType": "chat",
      "receiverName": "四川区域保险服务群",
      "receiver": "chat8a5440a5c3838393e9358f177b0ce8bf",
      "params": "{\"imgParam\": {\"width\": 750, \"screenshot\": {\"fullPage\": true, \"omitBackground\": true}}}",
      "auditState": 1
    },
    {
      "id": null,
      "receiverType": "chat",
      "receiverName": "河北区域保险服务二群",
      "receiver": "chatbbb4589233f5b834469a670e03cf82ad",
      "params": "{\"imgParam\": {\"width\": 750, \"screenshot\": {\"fullPage\": true, \"omitBackground\": true}}}",
      "auditState": 1
    },
    {
      "id": null,
      "receiverType": "chat",
      "receiverName": "河北区域保险服务一群",
      "receiver": "chat315f52572569117ee548413f39142c5f",
      "params": "{\"imgParam\": {\"width\": 750, \"screenshot\": {\"fullPage\": true, \"omitBackground\": true}}}",
      "auditState": 1
    },
    {
      "id": null,
      "receiverType": "chat",
      "receiverName": "甘肃区域保险服务群",
      "receiver": "chat7cd985de20d2318f9e074cc056e84a38",
      "params": "{\"imgParam\": {\"width\": 750, \"screenshot\": {\"fullPage\": true, \"omitBackground\": true}}}",
      "auditState": 1
    },
    {
      "id": null,
      "receiverType": "chat",
      "receiverName": "湖南保险服务群",
      "receiver": "chat2cd1489ab6ac6ea98e060bc37f048d47",
      "params": "{\"imgParam\": {\"width\": 750, \"screenshot\": {\"fullPage\": true, \"omitBackground\": true}}}",
      "auditState": 1
    }
  ]
}


### RankBchAmtPre 分支风云榜-业绩榜-月度预获榜单
POST https://admin.xiaowhale.com/bapi/api/insurance/operation//msg/config
Authorization: {{token}}
Content-Type: application/json

{
  "rule": {
    "id": 59,
    "enabledFlag": 0,
    "ruleCode": "RankBchAmtPre",
    "ruleName": "分支风云榜-业绩榜-月度预获榜单",
    "messageType": "image",
    "messageTemplate": "<!DOCTYPE html>\n<html lang=\"en\">\n\n<head>\n  <meta charset=\"UTF-8\">\n  <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n  <title>分支风云榜-业绩预获榜</title>\n  <style>\n    * {\n      margin: 0;\n      padding: 0;\n    }\n\n    body {\n      font-family: Arial, sans-serif;\n      margin: 0;\n      padding: 0;\n    }\n\n    .copywriting {\n      padding: 20px;\n      font-size: 28px;\n      background-color: #fff;\n    }\n\n    .poster {\n      position: relative;\n      max-width: 750px;\n      margin: 0 auto;\n      background-color: #af1e1c;\n      overflow: hidden;\n    }\n\n    .poster img {\n      width: 100%;\n      height: auto;\n    }\n\n    .poster-month {\n      position: absolute;\n      top: 82px;\n      right: 124px;\n      width: 54px;\n      text-align: center;\n      font-size: 54px;\n      color: #f6d993;\n    }\n\n    .poster-info,\n    .poster-bottom {\n      text-align: center;\n      color: #e2c06d;\n      font-size: 28px;\n    }\n\n    .poster-info {\n      width: 100%;\n    }\n\n    .poster-bottom {\n      margin: 10px 0 30px;\n    }\n    .poster-center{\n      margin: 20px 40px 10px;\n    }\n\n    .poster-organization {\n      margin: 10px 40px;\n      background-color: #9f1515;\n      border-radius: 16px;\n      overflow: hidden;\n    }\n\n    .poster-organization table {\n      width: 100%;\n      border-collapse: collapse;\n      color: #eeeeeef0;\n    }\n\n    \n    .poster-organization table thead th{\n      padding: 6px 0;\n      background: linear-gradient(to bottom,#ead0a4,#dfb267,#ead0a4); /* 表头背景色 */\n      color: #af1e1c;\n      font-size: 26px;\n    }\n    table tbody tr{\n      border-bottom: 1px solid #eeeeee42;\n    }\n\n    .poster-organization table tbody td {\n      padding: 6px 0;\n      font-size: 26px;\n      text-align: center;\n    }\n   \n    .high-light {\n      color: #dd3737;\n      font-weight: bolder;\n    }\n\n    .title-bold {\n      color: green;\n      font-weight: bolder;\n    }\n\n    .ml-6 {\n      margin-left: 12px;\n    }\n\n    .ml-20 {\n      margin-left: 40px;\n    }\n\n    .bold {\n      font-weight: bolder;\n    }\n\n    .mt-5 {\n      margin-top: 10px;\n    }\n\n    .emoji-img {\n      width: 44px;\n      height: 44px;\n      margin-right: 2px;\n      vertical-align: sub;\n    }\n \n  </style>\n</head>\n\n<body>\n  <div class=\"poster\">\n    <div class=\"poster-header\">\n      <img src=\"https://safesfiles.oss-cn-beijing.aliyuncs.com/safes/ZHNX33656/分支风云榜业绩榜预获_202309251610.png\" alt=\"\">\n    </div>\n    <div class=\"poster-month\" id=\"month-2\"></div>\n    <div class=\"poster-info\">\n      <span>统计口径：当月人均标准业绩/元</span>\n      <span class=\"ml-20\">统计时间：<span id=\"month-3\"></span>月</span>\n    </div>\n\n    <div class=\"poster-organization\" id=\"organization-rank\">\n      <table border=\"0\">\n        <thead>\n          <tr>\n            <th>序号</th>\n            <th>区域</th>\n            <th>分支</th>\n            <th>人均标准业绩</th>\n          </tr>\n        </thead>\n        <tbody id=\"table-tbody\">\n\n        </tbody>\n      </table>\n    </div>\n    \n    <div class=\"poster-bottom\">\n      <span>再/接/再/厉</span>\n      <span class=\"ml-20\">再/创/佳/绩</span>\n    </div>\n  </div>\n\n  <div class=\"copywriting\">\n    <div class=\"copywriting-header\">\n      <p>\n        <img src=\"https://safesfiles.oss-cn-beijing.aliyuncs.com/safes/ZHNX33656/火_202309192057.png\" class=\"emoji-img\">\n        <span class=\"high-light\">B类业务<span class=\"title-bold\"><span id=\"month-1\"></span>月分支风云榜（业绩榜）</span>-预获榜单</span>新鲜出炉!\n      </p>\n      <p>\n        <img src=\"https://safesfiles.oss-cn-beijing.aliyuncs.com/safes/ZHNX33656/火_202309192057.png\" class=\"emoji-img\">\n        B类业务火热进行中\n        <img src=\"https://safesfiles.oss-cn-beijing.aliyuncs.com/safes/ZHNX33656/努力_202309192033.png\" class=\"emoji-img\">\n        目前排名第一的是<span class=\"high-light\" id=\"first-area\"></span> ，人均标保已达 <span class=\"title-bold\"><span id=\"first-area-value\"></span>元</span>。\n      </p>\n      <p>\n        <img src=\"https://safesfiles.oss-cn-beijing.aliyuncs.com/safes/ZHNX33656/庆祝_202309192056.png\" class=\"emoji-img\">\n        后面的伙伴们也都奋力冲刺！其中 <span class=\"title-bold\" id=\"max-area\"></span>分支更是高达<span class=\"high-light\"><span id=\"max-area-ratio\"></span>%</span>的霸榜率！详情请查看分支预获榜单~\n      </p>\n      <p class=\"bold\">\n        <img src=\"https://safesfiles.oss-cn-beijing.aliyuncs.com/safes/ZHNX33656/火_202309192057.png\" class=\"emoji-img\">\n          让我们抓住机遇，一起全力冲刺吧！2023有您更精彩！\n      </p>\n    </div>\n  </div>\n</body>\n\n<script>\n  const doms = {\n    areaDom : document.getElementById('table-tbody'),\n    firstAreaDom : document.getElementById('first-area'),\n    firstAreaValueDom : document.getElementById('first-area-value'),\n    maxAreaRatioDom:document.getElementById('max-area-ratio'),\n    maxAreaValueDom:document.getElementById('max-area'),\n    month1Dom:document.getElementById('month-1'),\n    month2Dom:document.getElementById('month-2'),\n    month3Dom:document.getElementById('month-3'),\n  }\n\n  const data = ${datasJson}\n\n  const { datas,maxAreaName,yearMonth,areaNames,maxAreaPre } = data\n\n<#noparse>\n  // 位列第一的分支名称\n  const firstOrgList = (datas || []).filter(v => v.rank_num === 1)\n  const firstOrgName = firstOrgList.map(v => v.area_name + '-' +v.bch_name).join(\"、\");\n  doms.firstAreaDom.innerHTML = firstOrgName\n\n  // 第一分支标保\n  doms.firstAreaValueDom.innerHTML = firstOrgList[0].rank_value\n\n\n  // 最大霸榜率\n  const maxAreaValue = maxAreaPre ? parseInt((maxAreaPre / datas?.length) * 100 ) : 0\n  doms.maxAreaRatioDom.innerHTML = maxAreaValue\n  doms.maxAreaValueDom.innerHTML = maxAreaName\n\n  // 月份\n  doms.month1Dom.innerHTML = yearMonth.monthValue\n  doms.month2Dom.innerHTML = yearMonth.monthValue\n  doms.month3Dom.innerHTML = yearMonth.monthValue\n\n  // 填充列表模块\n  doms.areaDom.innerHTML = datas && datas.map((item,index) => {\n    return `\n        <tr>\n          <td>${index+1}</td>\n          <td>${item.area_name}</td>\n          <td>${item.bch_name}</td>\n          <td>${item.rank_value}</td>\n        </tr>\n    `\n  }).join(\"\")\n\n  </#noparse>\n</script>\n\n</html>",
    "startDate": "2023-10-17",
    "endDate": "2023-12-31",
    "cronRule": "30 9 * * 5",
    "params": "{}",
    "state": 1,
    "remark": null,
    "createBy": "ZHNX09760",
    "updateBy": "ZHNX09760",
    "bindComponent": "PushMessageOrgProductParams"
  },
  "autoPush": false,
  "receivers": [
    {
      "id": 502,
      "enabledFlag": 0,
      "messageRuleId": 59,
      "receiverType": "chat",
      "receiver": "chat5b4882782a8d235074b23901dae9ed87",
      "receiverName": "数据推送测试",
      "params": "{\"imgParam\": {\"width\": 750, \"screenshot\": {\"fullPage\": true, \"omitBackground\": true}}}",
      "auditState": 1
    }
  ]
}



###  分支风云榜-服务榜-月度预获榜单
POST https://admin.xiaowhale.com/bapi/api/insurance/operation//msg/config
Authorization: {{token}}
Content-Type: application/json

{
  "rule": {
    "id": 58,
    "enabledFlag": 0,
    "ruleCode": "RankBchCustPre",
    "ruleName": "分支风云榜-服务榜-月度预获榜单",
    "messageType": "image",
    "messageTemplate": "<!DOCTYPE html>\n<html lang=\"en\">\n\n<head>\n  <meta charset=\"UTF-8\">\n  <meta name=\"viewport\" content=\"width=device-width, initial-scale=5.0\">\n  <title>分支风云榜-业绩预获榜</title>\n  <style>\n    * {\n      margin: 0;\n      padding: 0;\n    }\n\n    body {\n      font-family: Arial, sans-serif;\n      margin: 0;\n      padding: 0;\n    }\n\n    .copywriting {\n      padding: 20px;\n      font-size: 28px;\n      background-color: #fff;\n    }\n\n    .poster {\n      position: relative;\n      max-width: 750px;\n      margin: 0 auto;\n      background-color: #af1e1c;\n      overflow: hidden;\n    }\n\n    .poster img {\n      width: 100%;\n      height: auto;\n    }\n\n    .poster-month {\n      position: absolute;\n      top: 80px;\n      right: 116px;\n      width: 72px;\n      text-align: center;\n      font-size: 60px;\n      font-family: math;\n      color: #f6d993;\n    }\n    .poster-statistics-time{\n      position: absolute;\n      top: 396px;\n      right: 100px;\n      font-size: 30px;\n      font-family: math;\n      color: #f6d993;\n    }\n\n    .poster-info,\n    .poster-bottom {\n      text-align: center;\n      color: #f6d993;\n      font-size: 28px;\n    }\n\n    .poster-info {\n      width: 100%;\n      margin-bottom: 16px;\n    }\n\n    .poster-bottom {\n      margin: 10px 0 30px;\n    }\n    .poster-center{\n      margin: 20px 40px 10px;\n    }\n\n    .poster-organization {\n      margin: 10px 40px;\n      background-color: #9f1515;\n      border-radius: 16px;\n      overflow: hidden;\n    }\n\n    .poster-organization table {\n      width: 100%;\n      border-collapse: collapse;\n      color: #eeeeeef0;\n    }\n\n\n    .poster-organization table thead th{\n      padding: 6px 0;\n      font-size: 30px;\n      background: linear-gradient(to bottom,#ead0a4,#dfb267,#ead0a4); /* 表头背景色 */\n      color: #af1e1c;\n    }\n    table tbody tr{\n      border-bottom: 1px solid #eeeeee42;\n    }\n\n    .poster-organization table tbody td {\n      padding: 6px 0;\n      font-size: 26px;\n      text-align: center;\n    }\n\n    .high-light {\n      color: #dd3737;\n      font-weight: bolder;\n    }\n\n    .title-bold {\n      color: green;\n      font-weight: bolder;\n    }\n\n    .ml-6 {\n      margin-left: 12px;\n    }\n\n    .ml-15 {\n      margin-left: 30px;\n    }\n\n    .bold {\n      font-weight: bolder;\n    }\n\n    .mt-5 {\n      margin-top: 10px;\n    }\n\n    .emoji-img {\n      width: 44px;\n      height: 44px;\n      margin-right: 2px;\n      vertical-align: sub;\n    }\n\n  </style>\n</head>\n\n<body>\n  <div class=\"poster\">\n    <div class=\"poster-header\">\n      <img src=\"https://safesfiles.oss-cn-beijing.aliyuncs.com/safes/ZHNX33656/分支风云榜服务榜预获_202309251617.png\" alt=\"\">\n    </div>\n    <div class=\"poster-month\" id=\"month-2\"></div>\n    <div class=\"poster-info\">\n      <span>统计口径：当月小程序注册客户数/个</span>\n      <span class=\"ml-15\">统计时间：<span id=\"month-3\"></span>月</span>\n    </div>\n\n    <div class=\"poster-organization\" id=\"organization-rank\">\n      <table border=\"0\">\n        <thead>\n          <tr>\n            <th>序号</th>\n            <th>区域</th>\n            <th>分支</th>\n            <th>当月小程序客户认证数</th>\n          </tr>\n        </thead>\n        <tbody id=\"table-tbody\">\n\n        </tbody>\n      </table>\n    </div>\n\n    <div class=\"poster-bottom\">\n      <span>再/接/再/厉</span>\n      <span class=\"ml-15\">再/创/佳/绩</span>\n    </div>\n  </div>\n\n  <div class=\"copywriting\">\n    <div class=\"copywriting-header\">\n      <p>\n        <img src=\"https://safesfiles.oss-cn-beijing.aliyuncs.com/safes/ZHNX33656/火_202309192057.png\" class=\"emoji-img\">\n        <span class=\"high-light\">B类业务<span class=\"title-bold\"><span id=\"month-1\"></span>月分支风云榜（服务榜）</span></span>新鲜出炉!\n      </p>\n      <p>\n        <img src=\"https://safesfiles.oss-cn-beijing.aliyuncs.com/safes/ZHNX33656/火_202309192057.png\" class=\"emoji-img\">\n        B类业务火热进行中\n        <img src=\"https://safesfiles.oss-cn-beijing.aliyuncs.com/safes/ZHNX33656/努力_202309192033.png\" class=\"emoji-img\">\n        目前排名第一的是<span class=\"high-light\" id=\"first-area\"></span> ，\n        当月注册客户数已达 <span class=\"title-bold\"><span id=\"first-area-value\"></span>人</span>。\n      </p>\n      <p>\n        <img src=\"https://safesfiles.oss-cn-beijing.aliyuncs.com/safes/ZHNX33656/庆祝_202309192056.png\" class=\"emoji-img\">\n        后面的伙伴们也都奋力冲刺！\n        <!-- 其中<span class=\"bold\">【<span id=\"ranked-area\"></span>】区域</span>\n        各显神通，共<span class=\"bold\" id=\"ranked-number\"></span>入围榜单。 -->\n        其中<span class=\"title-bold\" id=\"max-area\"></span>分支更是高达\n        <span class=\"high-light\" id=\"max-area-ratio\"></span>的霸榜率！详情请查看分支荣誉榜单~\n      </p>\n      <p class=\"bold\">\n        <img src=\"https://safesfiles.oss-cn-beijing.aliyuncs.com/safes/ZHNX33656/火_202309192057.png\" class=\"emoji-img\">\n          让我们抓住机遇，一起全力冲刺吧！2023有您更精彩！\n      </p>\n    </div>\n  </div>\n</body>\n\n<script>\n  const doms = {\n    areaDom : document.getElementById('table-tbody'),\n    firstAreaDom : document.getElementById('first-area'),\n    firstAreaValueDom : document.getElementById('first-area-value'),\n    maxAreaRatioDom:document.getElementById('max-area-ratio'),\n    maxAreaValueDom:document.getElementById('max-area'),\n    month1Dom:document.getElementById('month-1'),\n    month2Dom:document.getElementById('month-2'),\n    month3Dom:document.getElementById('month-3'),\n    // rankedNumberDom:document.getElementById('ranked-number'),\n    // rankedAreaDom : document.querySelector('#ranked-area'),\n  }\n\n  const data = ${datasJson}\n\n  const { datas,maxAreaName,yearMonth,areaNames,maxAreaPre } = data\n\n<#noparse>\n  // 位列第一的分支名称\n  const firstOrgList = (datas || []).filter(v => v.rank_num === 1)\n  const firstOrgName = firstOrgList.map(v => v.area_name + '-' +v.bch_name).join(\"、\");\n  doms.firstAreaDom.innerHTML = firstOrgName\n\n  // 第一分支标保\n  doms.firstAreaValueDom.innerHTML = firstOrgList[0].rank_value\n\n  // 上榜区域\n//   const areaNameList = (areaNames || []).map(v=> v.slice(0,-2))\n//   doms.rankedAreaDom.innerHTML = areaNameList.join('、')\n\n  // 上榜区域数量\n//   doms.rankedNumberDom.innerHTML = datas.length + '家分支'\n\n  // 最大霸榜率\n  const maxAreaValue = maxAreaPre ? parseInt((maxAreaPre / datas?.length) * 100 ) : 0\n  doms.maxAreaRatioDom.innerHTML = maxAreaValue + '%'\n  doms.maxAreaValueDom.innerHTML = maxAreaName\n\n  // 月份\n  doms.month1Dom.innerHTML = yearMonth.monthValue\n  doms.month2Dom.innerHTML = yearMonth.monthValue\n  doms.month3Dom.innerHTML = yearMonth.monthValue\n\n  // 填充列表模块\n  doms.areaDom.innerHTML = datas && datas.map((item,index) => {\n    return `\n        <tr>\n          <td>${index+1}</td>\n          <td>${item.area_name}</td>\n          <td>${item.bch_name}</td>\n          <td>${item.rank_value}</td>\n        </tr>\n    `\n  }).join(\"\")\n\n  </#noparse>\n</script>\n\n</html>\n",
    "startDate": "2023-10-17",
    "endDate": "2023-12-31",
    "cronRule": "30 9 * * 5",
    "params": "{}",
    "state": 1,
    "remark": null,
    "createBy": "ZHNX09760",
    "updateBy": "ZHNX09760",
    "bindComponent": "PushMessageOrgProductParams"
  },
  "autoPush": false,
  "receivers": [
    {
      "id": 501,
      "enabledFlag": 0,
      "messageRuleId": 58,
      "receiverType": "chat",
      "receiver": "chat5b4882782a8d235074b23901dae9ed87",
      "receiverName": "数据推送测试",
      "params": "{\"imgParam\": {\"width\": 750, \"screenshot\": {\"fullPage\": true, \"omitBackground\": true}}}",
      "auditState": 1
    }
  ]
}

### 骑士俱乐部-圆桌骑士
POST https://admin.xiaowhale.com/bapi/api/insurance/operation//msg/config
Authorization: {{token}}
Content-Type: application/json

{
  "rule": {
    "id": 56,
    "enabledFlag": 0,
    "ruleCode": "RankBchClubAmt",
    "ruleName": "骑士俱乐部-圆桌骑士",
    "messageType": "image",
    "messageTemplate": "<!DOCTYPE html>\n<html lang=\"en\">\n\n<head>\n  <meta charset=\"UTF-8\">\n  <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n  <title>圆桌骑士</title>\n  <style>\n    * {\n      margin: 0;\n      padding: 0;\n    }\n\n    body {\n      font-family: Arial, sans-serif;\n      margin: 0;\n      padding: 0;\n    }\n\n    .copywriting {\n      padding: 20px;\n      font-size: 28px;\n      background-color: #fff;\n    }\n\n    .poster {\n      position: relative;\n      max-width: 750px;\n      margin: 0 auto;\n      background-color: #af1e1c;\n      overflow: hidden;\n    }\n\n    .poster img {\n      width: 100%;\n      height: auto;\n    }\n\n    .poster-month {\n      position: absolute;\n      top: 110px;\n      right: 96px;\n      width: 80px;\n      text-align: center;\n      font-size: 64px;\n      font-family: math;\n      color: #f6d993;\n    }\n    .poster-statistics-time{\n      position: absolute;\n      top: 458px;\n      right: 80px;\n      width: 40px;\n      text-align: center;\n      font-size: 30px;\n      font-family: math;\n      color: #f6d993;\n    }\n\t\t.poster-info,\n    .poster-bottom {\n      color: #f6d993;\n      font-size: 30px;\n    }\n\t\t.poster-info {\n\t\t\tpadding: 0 40px;\n\t\t\tfont-size: 26px;\n    }\n\n    .poster-bottom {\n      margin: 10px 0 30px;\n      text-align: center;\n    }\n    .poster-center{\n      margin: 20px 40px 10px;\n    }\n\n    .poster-organization {\n      margin: 20px 40px 10px;\n      background-color: #9f1515;\n      border-radius: 16px;\n      overflow: hidden;\n    }\n\n    .poster-organization table {\n      width: 100%;\n      border-collapse: collapse;\n      color: #eeeeeef0;\n    }\n\n    \n    .poster-organization table thead th{\n      padding: 6px 0;\n      font-size: 30px;\n      background: linear-gradient(to bottom,#ead0a4,#dfb267,#ead0a4); /* 表头背景色 */\n      color: #af1e1c;\n    }\n    table tbody tr{\n      border-bottom: 1px solid #eeeeee42;\n    }\n\n    .poster-organization table tbody td {\n      padding: 6px 0;\n      font-size: 26px;\n      text-align: center;\n    }\n   \n    .high-light {\n      color: #dd3737;\n      font-weight: bolder;\n    }\n\n    .title-bold {\n      color: green;\n      font-weight: bolder;\n    }\n\n    .ml-20 {\n      margin-left: 40px;\n    }\n\n    .bold {\n      font-weight: bolder;\n    }\n\n    .emoji-img {\n      width: 44px;\n      height: 44px;\n      vertical-align: sub;\n    }\n\n  </style>\n</head>\n\n<body>\n  <div class=\"poster\">\n    <div class=\"poster-header\">\n      <img src=\"https://safesfiles.oss-cn-beijing.aliyuncs.com/safes/ZHNX33656/圆桌骑士预获_202309211233.png\" alt=\"圆桌骑士\">\n    </div>\n\t\t<div class=\"poster-info\">\n\t\t\t<span>统计时间： 1月1日-<span id=\"month\"></span>月</span><span id=\"day\"></span>\n\t\t\t<div>评选标准：年度标准业绩排名前30名可入围</div>\n    </div>\n\n    <div class=\"poster-organization\" id=\"organization-rank\">\n      <table border=\"0\">\n        <thead>\n          <tr>\n            <th>序号</th>\n            <th>区域</th>\n            <th>分支</th>\n            <th>姓名</th>\n            <th>标准业绩</th>\n          </tr>\n        </thead>\n        <tbody id=\"table-tbody\">\n\n        </tbody>\n      </table>\n    </div>\n    \n    <div class=\"poster-bottom\">\n      <span>再/接/再/厉</span>\n      <span class=\"ml-20\">再/创/佳/绩</span>\n    </div>\n  </div>\n\n\t<div class=\"copywriting\">\n    <div class=\"copywriting-header\">\n\t\t\t<p>\n        <img src=\"https://safesfiles.oss-cn-beijing.aliyuncs.com/safes/ZHNX33656/庆祝_202309192056.png\" class=\"emoji-img\">\n        <span class=\"high-light\">B类业务<span id=\"year1\"></span>年度\n\t\t\t\t<span class=\"title-bold\">【圆桌骑士】</span>预获榜单</span>新鲜出炉啦~\n      </p>\n      <p>\n\t\t\t\t<img src=\"https://safesfiles.oss-cn-beijing.aliyuncs.com/safes/ZHNX33656/鼓掌_202309192054.png\" class=\"emoji-img\">\n\t\t\t\t<img src=\"https://safesfiles.oss-cn-beijing.aliyuncs.com/safes/ZHNX33656/鼓掌_202309192054.png\" class=\"emoji-img\">\n        让我们用热烈的掌声恭喜以上预获的伙伴们~\n      </p>\n      <p>\n\t\t\t\t<img src=\"https://safesfiles.oss-cn-beijing.aliyuncs.com/safes/ZHNX33656/奋斗_202309211228.png\" class=\"emoji-img\">\n        <span class=\"bold\">“一份耕耘，一分收获”</span>你们的努力值得肯定！\n      </p>\n      <p>\n\t\t\t\t<img src=\"https://safesfiles.oss-cn-beijing.aliyuncs.com/safes/ZHNX33656/奋斗_202309211228.png\" class=\"emoji-img\">\n        <img src=\"https://safesfiles.oss-cn-beijing.aliyuncs.com/safes/ZHNX33656/火_202309192057.png\" class=\"emoji-img\">\n\t\t\t\t<span id=\"year2\" class=\"bold\"></span>祝全体伙伴<span class=\"bold\">业绩蒸蒸日上，铸就更辉煌的荣耀！</span>期待与更多伙伴相见！\n      </p>\n    </div>\n  </div>\n</body>\n\n<script>\n  const doms = {\n    areaDom : document.getElementById('table-tbody'),\n    monthDom:document.getElementById('month'),\n    year1Dom:document.getElementById('year1'),\n    year2Dom:document.getElementById('year2'),\n    copywritingDom:document.getElementById('copywriting-rank'),\n\t\tdayDom:document.getElementById('day')\n  }\n\n  const data = ${datasJson}\n\n  const { datas,maxAreaName,yearMonth,areaNames,maxAreaPre,pt } = data\n\n<#noparse>\n  // 月份 、年份\n  doms.monthDom.innerHTML = yearMonth.monthValue\n  doms.year1Dom.innerHTML = yearMonth.year\n  doms.year2Dom.innerHTML = yearMonth.year\n\tconst day = pt ? pt.slice(-2) : ''\n\tdoms.dayDom.innerHTML = day + '日' || ''\n\n  // 填充列表模块\n  doms.areaDom.innerHTML = datas && datas.map((item,index) => {\n    return `\n        <tr>\n          <td>${index+1}</td>\n          <td>${formatText(item.area_name)}</td>\n          <td>${formatText(item.bch_name)}</td>\n          <td>${formatText(item.emp_name)}</td>\n          <td>${formatText(item.rank_value)}万</td>\n        </tr>\n    `\n  }).join(\"\")\n\n  function formatText(text){\n    return text == null ? '-' : text\n  }\n\n</#noparse>\n</script>\n\n</html>",
    "startDate": "2023-10-17",
    "endDate": "2023-12-31",
    "cronRule": "0 0 16 15,28 * ?",
    "params": "{}",
    "state": 1,
    "remark": null,
    "createBy": "ZHNX09760",
    "updateBy": "ZHNX09760",
    "bindComponent": "PushMessageOrgProductParams"
  },
  "autoPush": false,
  "receivers": [
    {
      "id": 499,
      "enabledFlag": 0,
      "messageRuleId": 56,
      "receiverType": "chat",
      "receiver": "chat5b4882782a8d235074b23901dae9ed87",
      "receiverName": "数据推送测试",
      "params": "{\"imgParam\": {\"width\": 750, \"screenshot\": {\"fullPage\": true, \"omitBackground\": true}}}",
      "auditState": 1
    }
  ]
}

### 俱乐部-黄金骑士
POST https://admin.xiaowhale.com/bapi/api/insurance/operation//msg/config
Authorization: {{token}}
Content-Type: application/json

{
  "rule": {
    "id": 55,
    "enabledFlag": 0,
    "ruleCode": "RankBchClubGoldAmt",
    "ruleName": "俱乐部-黄金骑士",
    "messageType": "image",
    "messageTemplate": "<!DOCTYPE html>\n<html lang=\"en\">\n\n<head>\n  <meta charset=\"UTF-8\">\n  <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n  <title>黄金骑士</title>\n  <style>\n    * {\n      margin: 0;\n      padding: 0;\n    }\n\n    body {\n      font-family: Arial, sans-serif;\n      margin: 0;\n      padding: 0;\n    }\n\n    .copywriting {\n      padding: 20px;\n      font-size: 28px;\n      background-color: #fff;\n    }\n\n    .poster {\n      position: relative;\n      max-width: 750px;\n      margin: 0 auto;\n      background-color: #af1e1c;\n      overflow: hidden;\n    }\n\n    .poster img {\n      width: 100%;\n      height: auto;\n    }\n\n    .poster-month {\n      position: absolute;\n      top: 110px;\n      right: 96px;\n      width: 80px;\n      text-align: center;\n      font-size: 64px;\n      font-family: math;\n      color: #f6d993;\n    }\n    .poster-statistics-time{\n      position: absolute;\n      top: 458px;\n      right: 80px;\n      width: 40px;\n      text-align: center;\n      font-size: 30px;\n      font-family: math;\n      color: #f6d993;\n    }\n\t\t.poster-info,\n    .poster-bottom {\n      color: #f6d993;\n      font-size: 30px;\n    }\n\t\t.poster-info {\n\t\t\tpadding: 0 40px;\n\t\t\tfont-size: 26px;\n    }\n\n    .poster-bottom {\n      margin: 10px 0 30px;\n      text-align: center;\n    }\n    .poster-center{\n      margin: 20px 40px 10px;\n    }\n\n    .poster-organization {\n      margin: 20px 40px 10px;\n      background-color: #9f1515;\n      border-radius: 16px;\n      overflow: hidden;\n    }\n\n    .poster-organization table {\n      width: 100%;\n      border-collapse: collapse;\n      color: #eeeeeef0;\n    }\n\n    \n    .poster-organization table thead th{\n      padding: 6px 0;\n      font-size: 30px;\n      background: linear-gradient(to bottom,#ead0a4,#dfb267,#ead0a4); /* 表头背景色 */\n      color: #af1e1c;\n    }\n    table tbody tr{\n      border-bottom: 1px solid #eeeeee42;\n    }\n\n    .poster-organization table tbody td {\n      padding: 6px 0;\n      font-size: 26px;\n      text-align: center;\n    }\n   \n    .high-light {\n      color: #dd3737;\n      font-weight: bolder;\n    }\n\n    .title-bold {\n      color: green;\n      font-weight: bolder;\n    }\n\n    .ml-20 {\n      margin-left: 40px;\n    }\n\n    .bold {\n      font-weight: bolder;\n    }\n\n    .emoji-img {\n      width: 44px;\n      height: 44px;\n      vertical-align: sub;\n    }\n\n  </style>\n</head>\n\n<body>\n  <div class=\"poster\">\n    <div class=\"poster-header\">\n      <img src=\"https://safesfiles.oss-cn-beijing.aliyuncs.com/safes/ZHNX33656/黄金骑士预获_202309211359.png\" alt=\"黄金骑士\">\n    </div>\n\t\t<div class=\"poster-info\">\n      <span>统计时间： 1月1日-<span id=\"month\"></span>月</span><span id=\"day\"></span>\n\t\t\t<div>评选标准：年度标准业绩≥12万可入围</div>\n    </div>\n\n    <div class=\"poster-organization\" id=\"organization-rank\">\n      <table border=\"0\">\n        <thead>\n          <tr>\n            <th>序号</th>\n            <th>区域</th>\n            <th>分支</th>\n            <th>姓名</th>\n            <th>标准业绩</th>\n          </tr>\n        </thead>\n        <tbody id=\"table-tbody\">\n\n        </tbody>\n      </table>\n    </div>\n    \n    <div class=\"poster-bottom\">\n      <span>再/接/再/厉</span>\n      <span class=\"ml-20\">再/创/佳/绩</span>\n    </div>\n  </div>\n\n\t<div class=\"copywriting\">\n    <div class=\"copywriting-header\">\n\t\t\t<p>\n        <img src=\"https://safesfiles.oss-cn-beijing.aliyuncs.com/safes/ZHNX33656/庆祝_202309192056.png\" class=\"emoji-img\">\n        <span class=\"high-light\">B类业务<span id=\"year\"></span>年<span class=\"title-bold\">【黄金骑士】</span></span><span class=\"high-light\">预获榜单</span>新鲜出炉啦!\n      </p>\n\t\t\t<p>\n\t\t\t\t<img src=\"https://safesfiles.oss-cn-beijing.aliyuncs.com/safes/ZHNX33656/鼓掌_202309192054.png\" class=\"emoji-img\">\n\t\t\t\t<img src=\"https://safesfiles.oss-cn-beijing.aliyuncs.com/safes/ZHNX33656/鼓掌_202309192054.png\" class=\"emoji-img\">\n\t\t\t\t让我们把最热烈的掌声送给以上预获的伙伴们~\n\t\t\t</p>\n\t\t\t<p>\n\t\t\t\t<img src=\"https://safesfiles.oss-cn-beijing.aliyuncs.com/safes/ZHNX33656/奋斗_202309211228.png\" class=\"emoji-img\">\n\t\t\t\t<span class=\"bold\">“一份耕耘，一分收获”</span>你们的努力值得肯定！\n\t\t\t</p>\n      <p class=\"bold\">\n        <img src=\"https://safesfiles.oss-cn-beijing.aliyuncs.com/safes/ZHNX33656/奋斗_202309211228.png\" class=\"emoji-img\">\n\t\t\t\t士气定乾坤，<span id=\"year1\" class=\"high-light\"></span>铸辉煌！\n      </p>\n\t\t\t\n    </div>\n  </div>\n\n</body>\n\n<script>\n  const doms = {\n    areaDom : document.getElementById('table-tbody'),\n    monthDom:document.getElementById('month'),\n    year1Dom:document.getElementById('year1'),\n    copywritingDom:document.getElementById('copywriting-rank'),\n\t\tyearDom:document.getElementById('year'),\n\t\tdayDom:document.getElementById('day')\n  }\n\n  const data = ${datasJson}\n\n  const { datas,maxAreaName,yearMonth,areaNames,maxAreaPre,pt } = data\n\n<#noparse>\n  // 月份 、年份\n  doms.monthDom.innerHTML = yearMonth.monthValue\n  doms.year1Dom.innerHTML = yearMonth.year\n\tdoms.yearDom.innerHTML = yearMonth.year\n\tconst day = pt ? pt.slice(-2) : ''\n\tdoms.dayDom.innerHTML = day ? day + '日' : ''\n\n  // 填充列表模块\n  doms.areaDom.innerHTML = datas && datas.map((item,index) => {\n    return `\n        <tr>\n          <td>${index+1}</td>\n          <td>${formatText(item.area_name)}</td>\n          <td>${formatText(item.bch_name)}</td>\n          <td>${formatText(item.emp_name)}</td>\n          <td>${formatText(item.rank_value)}万</td>\n        </tr>\n    `\n  }).join(\"\")\n\n  function formatText(text){\n\t\tif(text == null || text === ''){\n\t\t\treturn '-'\n\t\t}\n\t\treturn text\n  }\n\n</#noparse>\n\n</script>\n\n</html>",
    "startDate": "2023-10-17",
    "endDate": "2023-12-31",
    "cronRule": "0 0 16 15,28 * ?",
    "params": "{}",
    "state": 1,
    "remark": null,
    "createBy": "ZHNX09760",
    "updateBy": "ZHNX09760",
    "bindComponent": "PushMessageOrgProductParams"
  },
  "autoPush": false,
  "receivers": [
    {
      "id": 497,
      "enabledFlag": 0,
      "messageRuleId": 55,
      "receiverType": "chat",
      "receiver": "chat5b4882782a8d235074b23901dae9ed87",
      "receiverName": "数据推送测试",
      "params": "{\"imgParam\": {\"width\": 750, \"screenshot\": {\"fullPage\": true, \"omitBackground\": true}}}",
      "auditState": 1
    }
  ]
}

### 俱乐部-百万俱乐部
POST https://admin.xiaowhale.com/bapi/api/insurance/operation//msg/config
Authorization: {{token}}
Content-Type: application/json

{
  "rule": {
    "id": 54,
    "enabledFlag": 0,
    "ruleCode": "RankBchClubMillion",
    "ruleName": "俱乐部-百万俱乐部",
    "messageType": "image",
    "messageTemplate": "<!DOCTYPE html>\n<html lang=\"en\">\n\n<head>\n  <meta charset=\"UTF-8\">\n  <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n  <title>百万俱乐部</title>\n  <style>\n    * {\n      margin: 0;\n      padding: 0;\n    }\n\n    body {\n      font-family: Arial, sans-serif;\n      margin: 0;\n      padding: 0;\n    }\n\n    .copywriting {\n      padding: 20px;\n      font-size: 28px;\n      background-color: #fff;\n    }\n\n    .poster {\n      position: relative;\n      max-width: 750px;\n      margin: 0 auto;\n      background-color: #af1e1c;\n      overflow: hidden;\n    }\n\n    .poster img {\n      width: 100%;\n      height: auto;\n    }\n\n    .poster-month {\n      position: absolute;\n      top: 110px;\n      right: 96px;\n      width: 80px;\n      text-align: center;\n      font-size: 64px;\n      font-family: math;\n      color: #f6d993;\n    }\n    .poster-statistics-time{\n      position: absolute;\n      top: 458px;\n      right: 80px;\n      width: 40px;\n      text-align: center;\n      font-size: 30px;\n      font-family: math;\n      color: #f6d993;\n    }\n\t\t.poster-info,\n    .poster-bottom {\n      color: #f6d993;\n      font-size: 30px;\n    }\n\t\t.poster-info {\n\t\t\tpadding: 0 40px;\n\t\t\tfont-size: 26px;\n    }\n\n    .poster-bottom {\n      margin: 10px 0 30px;\n      text-align: center;\n    }\n    .poster-center{\n      margin: 20px 40px 10px;\n    }\n\n    .poster-organization {\n      margin: 20px 40px 10px;\n      background-color: #9f1515;\n      border-radius: 16px;\n      overflow: hidden;\n    }\n\n    .poster-organization table {\n      width: 100%;\n      border-collapse: collapse;\n      color: #eeeeeef0;\n    }\n\n    \n    .poster-organization table thead th{\n      padding: 6px 0;\n      font-size: 30px;\n      background: linear-gradient(to bottom,#ead0a4,#dfb267,#ead0a4); /* 表头背景色 */\n      color: #af1e1c;\n    }\n    table tbody tr{\n      border-bottom: 1px solid #eeeeee42;\n    }\n\n    .poster-organization table tbody td {\n      padding: 6px 0;\n      font-size: 26px;\n      text-align: center;\n    }\n   \n    .high-light {\n      color: #dd3737;\n      font-weight: bolder;\n    }\n\n    .title-bold {\n      color: green;\n      font-weight: bolder;\n    }\n\n    .ml-20 {\n      margin-left: 40px;\n    }\n\n    .bold {\n      font-weight: bolder;\n    }\n\n    .emoji-img {\n      width: 44px;\n      height: 44px;\n      vertical-align: sub;\n    }\n\n  </style>\n</head>\n\n<body>\n  <div class=\"poster\">\n    <div class=\"poster-header\">\n      <img src=\"https://safesfiles.oss-cn-beijing.aliyuncs.com/safes/ZHNX33656/百万俱乐部预获_202309211410.png\" alt=\"百万俱乐部\">\n    </div>\n\t\t<div class=\"poster-info\">\n\t\t\t<span>统计时间： 1月1日-<span id=\"month\"></span>月</span><span id=\"day\"></span>\n\t\t\t<div>评选标准：年度标准业绩≥100万的分支可入围</div>\n    </div>\n\n    <div class=\"poster-organization\" id=\"organization-rank\">\n      <table border=\"0\">\n        <thead>\n          <tr>\n            <th>序号</th>\n            <th>区域</th>\n            <th>分支</th>\n            <th>标准业绩</th>\n          </tr>\n        </thead>\n        <tbody id=\"table-tbody\">\n\n        </tbody>\n      </table>\n    </div>\n    \n    <div class=\"poster-bottom\">\n      <span>再/接/再/厉</span>\n      <span class=\"ml-20\">再/创/佳/绩</span>\n    </div>\n  </div>\n\n\t<div class=\"copywriting\">\n    <div class=\"copywriting-header\">\n\t\t\t<p>\n        <img src=\"https://safesfiles.oss-cn-beijing.aliyuncs.com/safes/ZHNX33656/庆祝_202309192056.png\" class=\"emoji-img\">\n        <span class=\"high-light\">B类业务<span id=\"year1\"></span>年<span class=\"title-bold\">【百万俱乐部】</span>预获榜单</span>出炉啦~\n      </p>\n      <p>\n\t\t\t\t<img src=\"https://safesfiles.oss-cn-beijing.aliyuncs.com/safes/ZHNX33656/鼓掌_202309192054.png\" class=\"emoji-img\">\n        首先恭喜已入围的分支~ 入围<span class=\"title-bold\">【百万俱乐部】</span>享有<span class=\"high-light\">【总裁签发证书/专属奖杯】</span>、\n\t\t\t\t<span class=\"high-light\">【营销物资】</span>及<span class=\"high-light\">全员【小鲸定制版冲锋衣】</span>，丰厚奖励拿不停，尽请期待！\n      </p>\n\t\t\t<p>\n\t\t\t\t<img src=\"https://safesfiles.oss-cn-beijing.aliyuncs.com/safes/ZHNX33656/鼓掌_202309192054.png\" class=\"emoji-img\">\n        同时恭喜预获的分支，距离奖励达成越来越近，预祝达成！\n      </p>\n    </div>\n  </div>\n</body>\n\n<script>\n  const doms = {\n    areaDom : document.getElementById('table-tbody'),\n    monthDom:document.getElementById('month'),\n    year1Dom:document.getElementById('year1'),\n    copywritingDom:document.getElementById('copywriting-rank'),\n\t\tdayDom:document.getElementById('day')\n  }\n\n  const data = ${datasJson}\n\n  const { datas,maxAreaName,yearMonth,areaNames,maxAreaPre,pt } = data\n<#noparse>\n  // 月份 、年份\n  doms.monthDom.innerHTML = yearMonth.monthValue\n  doms.year1Dom.innerHTML = yearMonth.year\n\tconst day = pt ? pt.slice(-2) : ''\n\tdoms.dayDom.innerHTML = day + '日' || ''\n\n  // 填充列表模块\n  doms.areaDom.innerHTML = datas && datas.map((item,index) => {\n    return `\n        <tr>\n          <td>${index+1}</td>\n          <td>${formatText(item.area_name)}</td>\n          <td>${formatText(item.bch_name)}</td>\n          <td>${formatText(item.rank_value)}万</td>\n        </tr>\n    `\n  }).join(\"\")\n\n  function formatText(text){\n    return text == null ? '-' : text\n  }\n\n</#noparse>\n\n</script>\n\n</html>",
    "startDate": "2023-10-17",
    "endDate": "2023-12-31",
    "cronRule": "0 0 17 15,28 * ?",
    "params": "{}",
    "state": 1,
    "remark": null,
    "createBy": "ZHNX09760",
    "updateBy": "ZHNX09760",
    "bindComponent": "PushMessageOrgProductParams"
  },
  "autoPush": false,
  "receivers": [
    {
      "id": 498,
      "enabledFlag": 0,
      "messageRuleId": 54,
      "receiverType": "chat",
      "receiver": "chat5b4882782a8d235074b23901dae9ed87",
      "receiverName": "数据推送测试",
      "params": "{\"imgParam\": {\"width\": 750, \"screenshot\": {\"fullPage\": true, \"omitBackground\": true}}}",
      "auditState": 1
    }
  ]
}

###俱乐部-新秀骑士
POST https://admin.xiaowhale.com/bapi/api/insurance/operation//msg/config
Authorization: {{token}}
Content-Type: application/json

{
  "rule": {
    "id": 53,
    "enabledFlag": 0,
    "ruleCode": "RankBchClubNewAmt",
    "ruleName": "俱乐部-新秀骑士",
    "messageType": "image",
    "messageTemplate": "<!DOCTYPE html>\n<html lang=\"en\">\n\n<head>\n  <meta charset=\"UTF-8\">\n  <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n  <title>新秀骑士</title>\n  <style>\n    * {\n      margin: 0;\n      padding: 0;\n    }\n\n    body {\n      font-family: Arial, sans-serif;\n      margin: 0;\n      padding: 0;\n    }\n\n    .copywriting {\n      padding: 20px;\n      font-size: 28px;\n      background-color: #fff;\n    }\n\n    .poster {\n      position: relative;\n      max-width: 750px;\n      margin: 0 auto;\n      background-color: #af1e1c;\n      overflow: hidden;\n    }\n\n    .poster img {\n      width: 100%;\n      height: auto;\n    }\n\t\t.poster-info,\n    .poster-bottom {\n      color: #f6d993;\n      font-size: 28px;\n    }\n\t\t.poster-info {\n\t\t\tpadding: 0 20px;\n\t\t\tfont-size: 26px;\n    }\n\n    .poster-bottom {\n      margin: 10px 0 30px;\n      text-align: center;\n    }\n    .poster-center{\n      margin: 20px 40px 10px;\n    }\n\n    .poster-organization {\n      margin: 20px 40px 10px;\n      background-color: #9f1515;\n      border-radius: 16px;\n      overflow: hidden;\n    }\n\n    .poster-organization table {\n      width: 100%;\n      border-collapse: collapse;\n      color: #eeeeeef0;\n    }\n\n    \n    .poster-organization table thead th{\n      padding: 6px 0;\n      font-size: 28px;\n      background: linear-gradient(to bottom,#ead0a4,#dfb267,#ead0a4); /* 表头背景色 */\n      color: #af1e1c;\n    }\n    table tbody tr{\n      border-bottom: .5px solid #eeeeee42;\n    }\n\n    .poster-organization table tbody td {\n      padding: 6px 0;\n      font-size: 26px;\n      text-align: center;\n    }\n   \n    .high-light {\n      color: #dd3737;\n      font-weight: bolder;\n    }\n\n    .title-bold {\n      color: green;\n      font-weight: bolder;\n    }\n\n    .ml-20 {\n      margin-left: 40px;\n    }\n\n    .bold {\n      font-weight: bolder;\n    }\n\n    .emoji-img {\n      width: 44px;\n      height: 44px;\n      vertical-align: sub;\n    }\n\n  </style>\n</head>\n\n<body>\n  <div class=\"poster\">\n    <div class=\"poster-header\">\n      <img src=\"https://safesfiles.oss-cn-beijing.aliyuncs.com/safes/ZHNX33656/新秀骑士预获_202309211349.png\" alt=\"新秀骑士\">\n    </div>\n\t\t<div class=\"poster-info\">\n\t\t\t<span>统计时间： 1月1日-<span id=\"month\"></span>月</span><span id=\"day\"></span>\n\t\t\t<div>评选标准：年度标准业绩≥10万且排名前5名可入围</div>\n    </div>\n\n    <div class=\"poster-organization\" id=\"organization-rank\">\n      <table border=\"0\">\n        <thead>\n          <tr>\n            <th>序号</th>\n            <th>区域</th>\n            <th>分支</th>\n            <th>姓名</th>\n            <th>标准业绩</th>\n          </tr>\n        </thead>\n        <tbody id=\"table-tbody\">\n\n        </tbody>\n      </table>\n    </div>\n    \n    <div class=\"poster-bottom\">\n      <span>再/接/再/厉</span>\n      <span class=\"ml-20\">再/创/佳/绩</span>\n    </div>\n  </div>\n\n\t<div class=\"copywriting\">\n    <div class=\"copywriting-header\">\n\t\t\t<p>\n        <img src=\"https://safesfiles.oss-cn-beijing.aliyuncs.com/safes/ZHNX33656/庆祝_202309192056.png\" class=\"emoji-img\">\n        <span class=\"high-light\">B类业务<span id=\"year1\"></span>年<span class=\"title-bold\">【新秀骑士】</span>预获榜单</span>新鲜出炉啦~\n      </p>\n      <p>\n\t\t\t\t<img src=\"https://safesfiles.oss-cn-beijing.aliyuncs.com/safes/ZHNX33656/鼓掌_202309192054.png\" class=\"emoji-img\">\n        让我们把最热烈的掌声送给以上预获的伙伴们~\n      </p>\n\t\t\t<p>\n\t\t\t\t<img src=\"https://safesfiles.oss-cn-beijing.aliyuncs.com/safes/ZHNX33656/奋斗_202309211228.png\" class=\"emoji-img\">\n        <span class=\"bold\">“一份耕耘，一分收获”</span>你们的努力值得肯定！\n      </p>\n      <p>\n        <img src=\"https://safesfiles.oss-cn-beijing.aliyuncs.com/safes/ZHNX33656/火_202309192057.png\" class=\"emoji-img\">\n\t\t\t\t<span id=\"year2\" class=\"bold\"></span>，让我们一起<span class=\"bold\">万紫千红百事顺，朝气蓬勃事正成！</span>\n      </p>\n    </div>\n  </div>\n</body>\n\n<script>\n  const doms = {\n    areaDom : document.getElementById('table-tbody'),\n    monthDom:document.getElementById('month'),\n    year1Dom:document.getElementById('year1'),\n    year2Dom:document.getElementById('year2'),\n    copywritingDom:document.getElementById('copywriting-rank'),\n\t\tdayDom:document.getElementById('day')\n  }\n\n  const data = ${datasJson}\n\n  const { datas,maxAreaName,yearMonth,areaNames,maxAreaPre,pt } = data\n\n<#noparse>\n  // 月份 、年份\n  doms.monthDom.innerHTML = yearMonth.monthValue\n  doms.year1Dom.innerHTML = yearMonth.year\n  doms.year2Dom.innerHTML = yearMonth.year\n\tconst day = pt ? pt.slice(-2) : ''\n\tdoms.dayDom.innerHTML = day + '日' || ''\n\n  // 填充列表模块\n  doms.areaDom.innerHTML = datas && datas.map((item,index) => {\n    return `\n        <tr>\n          <td>${index+1}</td>\n          <td>${formatText(item.area_name)}</td>\n          <td>${formatText(item.bch_name)}</td>\n          <td>${formatText(item.emp_name)}</td>\n          <td>${formatText(item.rank_value)}万</td>\n        </tr>\n    `\n  }).join(\"\")\n\n  function formatText(text){\n    return text == null ? '-' : text\n  }\n</#noparse>\n\n</script>\n\n</html>",
    "startDate": "2023-10-01",
    "endDate": "2023-12-31",
    "cronRule": "0 0 16 15,28 * *",
    "params": "{}",
    "state": 1,
    "remark": null,
    "createBy": "ZHNX09760",
    "updateBy": "ZHNX09760",
    "bindComponent": "PushMessageOrgProductParams"
  },
  "autoPush": false,
  "receivers": [
    {
      "id": 495,
      "enabledFlag": 0,
      "messageRuleId": 53,
      "receiverType": "chat",
      "receiver": "chat5b4882782a8d235074b23901dae9ed87",
      "receiverName": "数据推送测试",
      "params": "{\"imgParam\": {\"width\": 750, \"screenshot\": {\"fullPage\": true, \"omitBackground\": true}}}",
      "auditState": 1
    }
  ]
}