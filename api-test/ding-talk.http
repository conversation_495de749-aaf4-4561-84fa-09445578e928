### 获取 token
POST https://api.dingtalk.com/v1.0/oauth2/accessToken
Content-Type:application/json

{
  "appKey": "dingebjxuactrt6zmvee",
  "appSecret": "0bZbkzAzCGIcrKOW3V5M-oJW9AHqsOmjH2C4vCCG7Gm-lbqY2a3oV2snLt1tTchv"
}

> {%
    client.test("修改token", () => response.status >= 200 && response.status <= 300)
    client.global.set("ding-token", response.body.accessToken)
%}

### 机器人发送
POST https://api.dingtalk.com/v1.0/robot/groupMessages/send
Content-Type:application/json
x-acs-dingtalk-access-token: {{ding-token}}

{
  "msgParam": "{\"text\":\"### B类业务8月风云榜荣誉榜单-分支战报来袭\\n\\n[火][向右]让我们一<span style='color: #53AC57'>起来看看分支风采吧</span>~\\n\\n[向右]分支风云榜-业绩榜：内蒙区域-乌兰浩特分支】以当月人均标保26601元，拿下业绩榜第一名！\\n\\n其中【河北、广东、甘肃、辽宁、内蒙、山东、山西、海南、云南】区域各显神通，共30家分支入围业绩榜单。河北区域的分支达50%的霸榜率！[赞]\\n![img](http://safesfiles.oss-cn-beijing.aliyuncs.com/puppeteer/html-img/1696755877188.png)\",\"title\":\"B类业务8月风云榜荣誉榜单-分支战报来袭\"}",
  "msgKey": "sampleMarkdown",
  "robotCode": "dingebjxuactrt6zmvee",
  "openConversationId": "cid4bKfVtHo7b6XYXsuc3YwlQ=="
}



### 企业机器人自定义普通发送
POST https://oapi.dingtalk.com/chat/send?access_token=ACCESS_TOKEN

###
#{
#    "result": {
#        "chatId": "chata72294f8b4dd1d570338a7149facfaf9",
#        "title": "IGrowth",
#        "openConversationId": "cid4bKfVtHo7b6XYXsuc3YwlQ=="
#    },
#    "method": "chooseChat",
#    "success": true
#}


### 推送群消息
POST {{host}}/dingTalk/message/chat/markdown
Content-Type: application/json

{
  "chatId": "chat5b4882782a8d235074b23901dae9ed87",
  "title": "测试文案",
  "markdown": "###测试文案艾斯德斯分手的风格是大哥\n\n43253443"
}