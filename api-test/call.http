### 一键呼叫接口测试

POST {{host}}/v1/call/oneClick
Content-Type: application/json
Authorization: {{authorization}}

{
  "policyNo": "P202501220001",
  "customerPhone": "13800138000",
  "clientId": "C001",
  "clientName": "张三",
  "remark": "测试一键呼叫功能"
}

### 查询呼叫记录（根据保单号）
GET {{host}}/v1/call/records?policyNo=P202501220001
Authorization: {{authorization}}

### 手动触发呼叫记录同步
POST {{host}}/v1/call/sync/records
Authorization: {{authorization}}

### 手动触发跟进记录匹配
POST {{host}}/v1/call/sync/follow
Authorization: {{authorization}}
