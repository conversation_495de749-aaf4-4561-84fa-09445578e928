[{"id": null, "receiverType": "chat", "receiverName": "重庆区域保险服务群", "receiver": "chat5d2c0325195b7f5195151f72f2add464", "params": "{\"imgParam\": {\"width\": 750, \"screenshot\": {\"fullPage\": true, \"omitBackground\": true}}}", "auditState": 1}, {"id": null, "receiverType": "chat", "receiverName": "江苏区域保险服务群", "receiver": "chatb8a5df1d93f4db660dc565af387680e6", "params": "{\"imgParam\": {\"width\": 750, \"screenshot\": {\"fullPage\": true, \"omitBackground\": true}}}", "auditState": 1}, {"id": null, "receiverType": "chat", "receiverName": "湖北区域保险服务群", "receiver": "chat08b478b62206cc36c8bf35fdb2e845e3", "params": "{\"imgParam\": {\"width\": 750, \"screenshot\": {\"fullPage\": true, \"omitBackground\": true}}}", "auditState": 1}, {"id": null, "receiverType": "chat", "receiverName": "江西区域保险服务群", "receiver": "chat551c141f3925b31157a1357263243687", "params": "{\"imgParam\": {\"width\": 750, \"screenshot\": {\"fullPage\": true, \"omitBackground\": true}}}", "auditState": 1}, {"id": null, "receiverType": "chat", "receiverName": "辽宁区域保险服务群", "receiver": "chatf1de47622d4b1662eb313be4dab088e7", "params": "{\"imgParam\": {\"width\": 750, \"screenshot\": {\"fullPage\": true, \"omitBackground\": true}}}", "auditState": 1}, {"id": null, "receiverType": "chat", "receiverName": "广东区域保险服务群", "receiver": "chat08fb36f271def37f6c83d1d8c0da58ef", "params": "{\"imgParam\": {\"width\": 750, \"screenshot\": {\"fullPage\": true, \"omitBackground\": true}}}", "auditState": 1}, {"id": null, "receiverType": "chat", "receiverName": "河南区域保险服务群", "receiver": "chatace73068e310ec13a56ac33ac21fcee7", "params": "{\"imgParam\": {\"width\": 750, \"screenshot\": {\"fullPage\": true, \"omitBackground\": true}}}", "auditState": 1}, {"id": null, "receiverType": "chat", "receiverName": "海南区域保险业务服务群", "receiver": "chat7a86058aeb4d7ccc95f732935c5dcf56", "params": "{\"imgParam\": {\"width\": 750, \"screenshot\": {\"fullPage\": true, \"omitBackground\": true}}}", "auditState": 1}, {"id": null, "receiverType": "chat", "receiverName": "内蒙区域保险服务群", "receiver": "chat558e81ed80b6b5060baa85babbf88379", "params": "{\"imgParam\": {\"width\": 750, \"screenshot\": {\"fullPage\": true, \"omitBackground\": true}}}", "auditState": 1}, {"id": null, "receiverType": "chat", "receiverName": "山东区域保险服务群", "receiver": "chat15070ee012102910a9120f891f2fa7d8", "params": "{\"imgParam\": {\"width\": 750, \"screenshot\": {\"fullPage\": true, \"omitBackground\": true}}}", "auditState": 1}, {"id": null, "receiverType": "chat", "receiverName": "山西区域保险服务群", "receiver": "chat6223c412df1b400279e77db670a884b2", "params": "{\"imgParam\": {\"width\": 750, \"screenshot\": {\"fullPage\": true, \"omitBackground\": true}}}", "auditState": 1}, {"id": null, "receiverType": "chat", "receiverName": "云南区域保险服务群", "receiver": "chat0dd11c3907dad2064f9207c8da0118f3", "params": "{\"imgParam\": {\"width\": 750, \"screenshot\": {\"fullPage\": true, \"omitBackground\": true}}}", "auditState": 1}, {"id": null, "receiverType": "chat", "receiverName": "四川区域保险服务群", "receiver": "chat8a5440a5c3838393e9358f177b0ce8bf", "params": "{\"imgParam\": {\"width\": 750, \"screenshot\": {\"fullPage\": true, \"omitBackground\": true}}}", "auditState": 1}, {"id": null, "receiverType": "chat", "receiverName": "河北区域保险服务二群", "receiver": "chatbbb4589233f5b834469a670e03cf82ad", "params": "{\"imgParam\": {\"width\": 750, \"screenshot\": {\"fullPage\": true, \"omitBackground\": true}}}", "auditState": 1}, {"id": null, "receiverType": "chat", "receiverName": "河北区域保险服务一群", "receiver": "chat315f52572569117ee548413f39142c5f", "params": "{\"imgParam\": {\"width\": 750, \"screenshot\": {\"fullPage\": true, \"omitBackground\": true}}}", "auditState": 1}, {"id": null, "receiverType": "chat", "receiverName": "甘肃区域保险服务群", "receiver": "chat7cd985de20d2318f9e074cc056e84a38", "params": "{\"imgParam\": {\"width\": 750, \"screenshot\": {\"fullPage\": true, \"omitBackground\": true}}}", "auditState": 1}, {"id": null, "receiverType": "chat", "receiverName": "湖南保险服务群", "receiver": "chat2cd1489ab6ac6ea98e060bc37f048d47", "params": "{\"imgParam\": {\"width\": 750, \"screenshot\": {\"fullPage\": true, \"omitBackground\": true}}}", "auditState": 1}]