
### Match opponents for PK

POST  {{host}}/pk-indicators/match-opponents
Content-Type: application/json

{
  "type": "3",
  "branchCode": "GDQX",
  "userCode": "userCode",
  "indicatorsType": "AMOUNT",
  "indicatorsMaxValue": null,
  "indicatorsMinValue": null,
  "startDate": "2023-07-01",
  "endDate": "2023-07-31",
  "pageNum": 1,
  "pageSize": 10
}

###

### Query historical indicators for PK

### 员工纬度历史战绩查询
POST {{host}}/pk-indicators/history-indicators
Content-Type: application/json


{
  "type": "3",
  "branchCode": "GDQX",
  "codeList": [
    "GSJT0027",
    "HBFC0006"
  ],
  "indicatorTypeList": [
    "RECEIPT_PERFORMANCE_AVG",
    "AMOUNT",
    "REG_CUST",
    "COMPANY_YEAR"
  ],
  "indicatorsType": "AMOUNT",
  "startDate": "2023-07-01",
  "endDate": "2023-07-31"
}


### 分支纬度历史战绩查询
POST {{host}}/pk-indicators/history-indicators
Content-Type: application/json


{
  "type": "2",
  "codeList": [
    "GDQX",
    "SXHZ"
  ],
  "indicatorTypeList": [
    "RECEIPT_PERFORMANCE_AVG",
    "AMOUNT",
    "REG_CUST",
    "ORG_MONTH"
  ],
  "indicatorsType": "AMOUNT",
  "startDate": "2023-07-01",
  "endDate": "2023-07-31"
}

###

### Query real-time indicators for PK

POST {{host}}/pk-indicators/real-time-indicators
Content-Type: application/json

{
  "type": "3",
  "branchCode": "GDQX",
  "codeList": [
    "GSJT0027",
    "HBFC0006"
  ],
  "indicatorTypeList": [
    "RECEIPT_PERFORMANCE_AVG",
    "AMOUNT"
  ],
  "indicatorsType": "AMOUNT",
  "startDate": "2023-07-01",
  "endDate": "2023-07-31"
}

###
POST {{host}}/pk-indicators/real-time-indicators
Content-Type: application/json

{
  "type": "3",
  "codeList": [
    "LNHS0003"
  ],
  "indicatorTypeList": [
    "AMOUNT"
  ],
  "startDate": "2023-07-01",
  "endDate": "2023-07-31"
}


### quyu
POST {{host}}/pk-indicators/history-indicators
Content-Type: application/json

{
  "type": "1",
  "codeList": [
    "CNHN"
  ],
  "indicatorTypeList": [
    "AMOUNT_AVG"
  ],
  "startDate": "2023-07-01",
  "endDate": "2023-07-31"
}


###
POST {{host}}/pk-indicators/real-time-indicators
Content-Type: application/json

{
  "type": "1",
  "codeList": [
    "CNHN"
  ],
  "indicatorTypeList": [
    "43"
  ],
  "startDate": "2023-05-01",
  "endDate": "2023-07-31"
}


### 匹配客户经理

POST  {{host}}/pk-indicators/match-opponents
Content-Type: application/json

{
  "blackUserCodeList": [],
  "branchCode": "LNBP",
  "endDate": "2023-08-31",
  "indicatorsMaxValue": 9999999,
  "indicatorsMinValue": 1.5,
  "indicatorsType": "41",
  "pageNum": 1,
  "pageSize": 20,
  "startDate": "2023-06-01",
  "type": "2",
  "userCode": "LNBP0008"
}




### quyu
POST {{host}}/pk-indicators/history-indicators
Content-Type: application/json

{
  "type": "2",
  "codeList": [
    "SDGX"
  ],
  "indicatorTypeList": [
    "42"
  ],
  "startDate": "2023-08-01",
  "endDate": "2023-08-31"
}



### s
POST {{host}}/pk-indicators/real-time-indicators
Content-Type: application/json

{
  "codeList": [
    "ZHNX31300"
  ],
  "endDate": "2023-08-31",
  "indicatorTypeList": [
    "44"
  ],
  "startDate": "2023-08-01",
  "type": "3"
}


###

# curl -X POST 'http://llm-devops-service.llm/v1/completion-messages'
#--header 'Authorization: Bearer {api_key}'
#--header 'Content-Type: application/json'
#--data-raw '{
#    "inputs": {},
#    "response_mode": "streaming",
#    "user": "abc-123"
#}'
POST http://llm-devops-service.llm/v1/completion-messages
Authorization: Bearer {api_key}
Content-Type: application/json

{
  "inputs": {},
  "response_mode": "streaming",
  "user": "abc-123"
}

###

