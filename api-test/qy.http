### 保存问卷
POST {{host}}/pub/qy/pager/save
Content-Type: application/json

{
  "pagerId": 1,
  "qyQuestions": [
    {
      "id": 1,
      "enabledFlag": 0,
      "pagerId": 1,
      "questionGroup": "问题一",
      "questionType": 1,
      "question": "您了解过保险吗？",
      "params": {
        "preOptionId": [],
        "required": true
      },
      "options": [
        {
          "id": 1,
          "enabledFlag": 0,
          "questionId": 1,
          "optionValue": "了解",
          "params": {}
        },
        {
          "id": 2,
          "enabledFlag": 0,
          "questionId": 1,
          "optionValue": "不了解",
          "params": {}
        }
      ]
    },
    {
      "id": 2,
      "enabledFlag": 0,
      "pagerId": 1,
      "questionGroup": "问题二",
      "questionType": 1,
      "question": "您认为保险有用吗？",
      "params": {
        "preOptionId": [],
        "required": true
      },
      "options": [
        {
          "id": 3,
          "enabledFlag": 0,
          "questionId": 2,
          "optionValue": "有用",
          "params": {}
        },
        {
          "id": 4,
          "enabledFlag": 0,
          "questionId": 2,
          "optionValue": "没用",
          "params": {}
        }
      ]
    },
    {
      "id": 3,
      "enabledFlag": 0,
      "pagerId": 1,
      "questionGroup": "问题三",
      "questionType": 1,
      "question": "您是否有储蓄的习惯？",
      "params": {
        "preOptionId": [],
        "required": true
      },
      "options": [
        {
          "id": 5,
          "enabledFlag": 0,
          "questionId": 3,
          "optionValue": "有",
          "params": {}
        },
        {
          "id": 6,
          "enabledFlag": 0,
          "questionId": 3,
          "optionValue": "没有",
          "params": {}
        }
      ]
    },
    {
      "id": 4,
      "enabledFlag": 0,
      "pagerId": 1,
      "questionGroup": "问题四",
      "questionType": 1,
      "question": "您买过保险吗？",
      "params": {
        "preOptionId": [],
        "required": true
      },
      "options": [
        {
          "id": 7,
          "enabledFlag": 0,
          "questionId": 4,
          "optionValue": "买过",
          "params": {}
        },
        {
          "id": 8,
          "enabledFlag": 0,
          "questionId": 4,
          "optionValue": "没买过",
          "params": {}
        }
      ]
    },
    {
      "id": 5,
      "enabledFlag": 0,
      "pagerId": 1,
      "questionGroup": "问题四",
      "questionType": 1,
      "question": "您是通过哪一种方式购买的？",
      "params": {
        "preOptionId": [
          7
        ],
        "required": true
      },
      "options": [
        {
          "id": 9,
          "enabledFlag": 0,
          "questionId": 5,
          "optionValue": "熟人",
          "params": {}
        },
        {
          "id": 10,
          "enabledFlag": 0,
          "questionId": 5,
          "optionValue": "网上",
          "params": {}
        }
      ]
    },
    {
      "id": 6,
      "enabledFlag": 0,
      "pagerId": 1,
      "questionGroup": "问题四",
      "questionType": 1,
      "question": "您了解您买过的保险产品具体保哪些责任？不保哪些责任吗？",
      "params": {
        "preOptionId": [
          7
        ],
        "required": true
      },
      "options": [
        {
          "id": 11,
          "enabledFlag": 0,
          "questionId": 6,
          "optionValue": "不太清楚",
          "params": {}
        },
        {
          "id": 12,
          "enabledFlag": 0,
          "questionId": 6,
          "optionValue": "非常清楚",
          "params": {}
        }
      ]
    },
    {
      "id": 7,
      "enabledFlag": 0,
      "pagerId": 1,
      "questionGroup": "问题四",
      "questionType": 1,
      "question": "您购买的保险理赔过吗？",
      "params": {
        "preOptionId": [
          7
        ],
        "required": true
      },
      "options": [
        {
          "id": 13,
          "enabledFlag": 0,
          "questionId": 7,
          "optionValue": "没理赔过",
          "params": {}
        },
        {
          "id": 14,
          "enabledFlag": 0,
          "questionId": 7,
          "optionValue": "理赔过",
          "params": {}
        }
      ]
    },
    {
      "id": 8,
      "enabledFlag": 0,
      "pagerId": 1,
      "questionGroup": "问题四",
      "questionType": 2,
      "question": "没买保险的原因主要是？",
      "params": {
        "preOptionId": [
          8
        ],
        "required": true
      },
      "options": [
        {
          "id": 15,
          "enabledFlag": 0,
          "questionId": 8,
          "optionValue": "保费贵、负担不起",
          "params": {}
        },
        {
          "id": 16,
          "enabledFlag": 0,
          "questionId": 8,
          "optionValue": "担心受骗，买了不赔，白花钱",
          "params": {}
        },
        {
          "id": 17,
          "enabledFlag": 0,
          "questionId": 8,
          "optionValue": "买了保险没人管",
          "params": {}
        }
      ]
    },
    {
      "id": 9,
      "enabledFlag": 0,
      "pagerId": 1,
      "questionGroup": "问题四",
      "questionType": 1,
      "question": "您认为自己以后会买保险吗？",
      "params": {
        "preOptionId": [
          8
        ],
        "required": true
      },
      "options": [
        {
          "id": 18,
          "enabledFlag": 0,
          "questionId": 9,
          "optionValue": "会",
          "params": {}
        },
        {
          "id": 19,
          "enabledFlag": 0,
          "questionId": 9,
          "optionValue": "不会",
          "params": {}
        }
      ]
    },
    {
      "id": 10,
      "enabledFlag": 0,
      "pagerId": 1,
      "questionGroup": "问题四",
      "questionType": 2,
      "question": "您希望买到什么样的保险产品？",
      "params": {
        "preOptionId": [
          8
        ],
        "required": true
      },
      "options": [
        {
          "id": 20,
          "enabledFlag": 0,
          "questionId": 10,
          "optionValue": "生病能报销",
          "params": {}
        },
        {
          "id": 21,
          "enabledFlag": 0,
          "questionId": 10,
          "optionValue": "以防万一给家人留钱",
          "params": {}
        },
        {
          "id": 22,
          "enabledFlag": 0,
          "questionId": 10,
          "optionValue": "养老存钱的",
          "params": {}
        }
      ]
    },
    {
      "id": 11,
      "enabledFlag": 0,
      "pagerId": 1,
      "questionGroup": "问题五",
      "questionType": 1,
      "question": "您希望有人帮您梳理您的保险保障有哪些？哪些能赔？哪些赔不了？并且能有专业的人为您免费进行相关咨询和后续理赔服务吗？",
      "params": {
        "preOptionId": [],
        "required": true
      },
      "options": [
        {
          "id": 23,
          "enabledFlag": 0,
          "questionId": 11,
          "optionValue": "不希望",
          "params": {
            "code": "showIns",
            "desc": "如果您后续想要了解保险，可以关注小鲸向海小程序"
          }
        },
        {
          "id": 24,
          "enabledFlag": 0,
          "questionId": 11,
          "optionValue": "希望",
          "params": {
            "code": "showIns",
            "desc": "如果您希望有人给您提供专业的保险服务，并给您有效的帮助，请关注小鲸向海小程序"
          }
        }
      ]
    }
  ]
}

### 查询问卷
GET {{host}}/pub/qy/pager/detail?pagerId=1
Content-Type: application/json


### 保存问卷答案

POST {{host}}/pub/qy/pager/answer?pagerId=1
Content-Type: application/json

{
  "answerDetail": [
    {
      "answerDetail": "",
      "optionIds": [
        2
      ],
      "questionId": 1
    },
    {
      "answerDetail": "",
      "optionIds": [
        3
      ],
      "questionId": 2
    },
    {
      "answerDetail": "",
      "optionIds": [
        5
      ],
      "questionId": 3
    },
    {
      "answerDetail": "",
      "optionIds": [
        8
      ],
      "questionId": 4
    },
    {
      "answerDetail": "",
      "optionIds": [
        17,
        15,
        16
      ],
      "questionId": 8
    },
    {
      "answerDetail": "",
      "optionIds": [
        19
      ],
      "questionId": 9
    },
    {
      "answerDetail": "",
      "optionIds": [
        22,
        20,
        21
      ],
      "questionId": 10
    },
    {
      "answerDetail": "",
      "optionIds": [
        24
      ],
      "questionId": 11
    }
  ],
  "answerUserType": 1,
  "answerState": 1,
  "pagerId": 1,
  "userId": "ZHNX33656",
  "externalUserid": "wmNGxBDgAAcbKj19goLyStsGtclWZfEA"
}



### 查询问卷答案

GET {{host}}/pub/qy/pager/answer?pagerId=1&externalUserid=wmNGxBDgAAcbKj19goLyStsGtclWZfEA
Content-Type: application/json


