-- 呼叫记录表
CREATE TABLE `call_record` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `policy_no` varchar(50) NOT NULL COMMENT '保单号',
  `customer_phone` varchar(20) NOT NULL COMMENT '客户手机号',
  `employee_id` varchar(50) NOT NULL COMMENT '客户经理工号',
  `employee_name` varchar(50) DEFAULT NULL COMMENT '客户经理姓名',
  `employee_phone` varchar(20) NOT NULL COMMENT '客户经理手机号',
  `client_id` varchar(50) DEFAULT NULL COMMENT '客户编号',
  `client_name` varchar(100) DEFAULT NULL COMMENT '客户名称',
  `call_sid` varchar(100) DEFAULT NULL COMMENT '呼叫SID',
  `call_status` tinyint NOT NULL DEFAULT '0' COMMENT '呼叫状态：0-初始化，1-呼叫中，2-呼叫成功，3-呼叫失败',
  `begin_call_time` datetime DEFAULT NULL COMMENT '呼叫开始时间',
  `end_call_time` datetime DEFAULT NULL COMMENT '呼叫结束时间',
  `duration` int DEFAULT NULL COMMENT '通话时长（秒）',
  `record_url` varchar(500) DEFAULT NULL COMMENT '录音地址',
  `record_duration` varchar(20) DEFAULT NULL COMMENT '录音时长',
  `record_status` tinyint NOT NULL DEFAULT '0' COMMENT '录音获取状态：0-未获取，1-已获取，2-获取失败',
  `record_time` datetime DEFAULT NULL COMMENT '录音获取时间',
  `follow_match_status` tinyint NOT NULL DEFAULT '0' COMMENT '跟进记录匹配状态：0-未匹配，1-已匹配',
  `follow_record_id` bigint DEFAULT NULL COMMENT '匹配的跟进记录ID',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `fail_reason` varchar(500) DEFAULT NULL COMMENT '呼叫失败原因',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `enabled_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识：0-正常，1-删除',
  PRIMARY KEY (`id`),
  KEY `idx_policy_no` (`policy_no`),
  KEY `idx_call_sid` (`call_sid`),
  KEY `idx_employee_id` (`employee_id`),
  KEY `idx_record_status` (`record_status`),
  KEY `idx_follow_match_status` (`follow_match_status`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='呼叫记录表';
