alter table phoenix_emp_todo
    add batch_no varchar(20) default '' not null after id;

drop table customer_loan;
create table safes.customer_loan
(
    id                         bigint auto_increment primary key ,
    customer_id                bigint                              null comment '客户id',
    id_number                  varchar(50)                         not null comment '证件号码',
    customer_name              varchar(50)                         not null comment '客户名称',
    cust_industry              varchar(30)                         not null comment '贷款行业',
    license_num                varchar(50)                         not null comment '统一社会信用代码',
    business_loan              tinyint                             not null comment '是否经营贷',
    `2023_settle_max_loan_amt` decimal(18, 2)                      not null comment '23年结清最大放款金额',
    cust_new_annual_surplus    decimal(18, 2)                      not null comment '23年最新的年盈余',
    customer_admin             varchar(50)                         not null comment '管控客户经理',
    conversion_time            datetime                            null comment '转化时间',
    conversion_state           tinyint   default 0                 not null comment '转化状态',
    conversion_emp             varchar(50)                         null comment '转化员工',
    two_plan                   varchar(10)                         not null comment '二类计划',
    industry_sort              int                                 not null comment '行业排序 一类+二类 20 仅二类30 仅一类40 ',
    tag_sort                   int                                 not null comment '标签排序',
    amt_sort                   decimal(18, 2)                      not null comment '金额排序',
    todo_state                 int                                 not null comment '是否生成待办',
    current_loan               tinyint                             null comment '是否在贷',
    update_time                timestamp default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP,
    enabled_flag               tinyint   default 0                 null,
    create_time                timestamp default CURRENT_TIMESTAMP not null
)
    comment '异业客户-信贷客户';

create unique index  udx_customer_loan_id_number on customer_loan (id_number);

CREATE TABLE `customer_loan_follow`
(
    `id` bigint unsigned NOT NULL AUTO_INCREMENT,
    `customer_id` int NOT NULL COMMENT '客户主键',
    `id_number` varchar(30) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '证件号',
    `service_mode` varchar(30) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '服务方式 wechat:微信,phone:电话,offline_visit:线下拜访,lose_contact:联系不上',
    `intention` varchar(30) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '意向 high:高意向,median:中意向, low:低意向,none:无意向,lose_contact:联系不上',
    `reason` varchar(10) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '不续原因 1:客户未续贷,2:已在其他处投保,3:产品没优势,4:客户认为保险没有用,5:保费涨价了,6:其他',
    `reason_remark` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '其他-说明',
    `remark` varchar(5000) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '备注',
    `next_time` datetime DEFAULT NULL COMMENT '下次跟进时间',
    `newest` tinyint DEFAULT '1' COMMENT '是否最新记录 0：否 1：是',
    `follow_people` varchar(50) COLLATE utf8mb4_general_ci NOT NULL COMMENT '跟进人',
    `follow_people_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '跟进人',
    `follow_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '跟进时间',
    `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `enabled_flag` tinyint NOT NULL DEFAULT '0',
    PRIMARY KEY (`id`)
) COMMENT='异业客户跟进记录表';
CREATE TABLE `customer_loan_policy`
(
    `id` bigint NOT NULL AUTO_INCREMENT,
    `id_number` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '证件号码',
    `customer_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '客户名称',
    `policy_no` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '保单号',
    `fh_order_id` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '订单号',
    `policy_state` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '保单状态',
    `cancel_flag` tinyint DEFAULT NULL COMMENT '是否退保 0：是 1：否',
    `cancel_time` datetime DEFAULT NULL COMMENT '退保时间',
    `amount` decimal(10,2) DEFAULT NULL COMMENT '保费',
    `total_amount` decimal(10,2) DEFAULT NULL COMMENT '订单总保费',
    `insur` tinyint DEFAULT NULL COMMENT '是否被保人 0:是 1:否',
    `customer_admin` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '管控人',
    `convert_time` datetime DEFAULT NULL COMMENT '转化时间',
    `convert_emp` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '转化员工',
    `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `enabled_flag` tinyint DEFAULT '0',
    `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `udx_customer_policy_fh_order_id` (`fh_order_id`,`id_number`,`policy_no`,`policy_state`),
    KEY `customer_interruption_policy_id_number` (`id_number`)
) COMMENT='转化客户保单信息表';
