CREATE TABLE IF NOT EXISTS dwa_safes_phoenix_renew_area
(
    area_name               STRING COMMENT '区域',
    -- 续期/续保
    tdo                     int COMMENT '续保/续期 待办数',
    tdo_done                int COMMENT '续保/续期待办完成数',
    tdo_timeout             int COMMENT '续保/续期待办超时数',
    tdo_policy              int COMMENT '续保/续期 保单数',
    tdo_amt                 decimal(20, 4) COMMENT '续保/续期 保费',
    tdo_follow              INT COMMENT '续保/续期 跟进数',
    tdo_follow_policy       INT COMMENT '续保/续期 跟进转化保单数',
    tdo_follow_amt          decimal(20, 4) COMMENT '续保/续期 跟进转换保费',
    -- 续保（短险）
    tdo_short               int COMMENT '续保待办数',
    tdo_short_done          int COMMENT '续保待办完成数',
    tdo_short_timeout       int COMMENT '续保待办超时数',
    tdo_short_policy        int COMMENT '续保保单数',
    tdo_short_amt           decimal(20, 4) COMMENT '续保保费',
    tdo_short_follow        INT COMMENT '续保跟进数',
    tdo_short_follow_policy INT COMMENT '续保跟进转化保单数',
    tdo_short_follow_amt    decimal(20, 4) COMMENT '续保跟进转换保费',
    -- 续期（短险）
    tdo_long                int COMMENT '续期待办数',
    tdo_long_done           int COMMENT '续期待办完成数',
    tdo_long_timeout        int COMMENT '续期待办超时数',
    tdo_long_policy         int COMMENT '续期保单数',
    tdo_long_amt            decimal(20, 4) COMMENT '续期保费',
    tdo_long_follow         INT COMMENT '续期跟进数',
    tdo_long_follow_policy  INT COMMENT '续期跟进转化保单数',
    tdo_long_follow_amt     decimal(20, 4) COMMENT '续期跟进转换保费'
)
    PARTITIONED BY (pt STRING)
    STORED AS ALIORC LIFECYCLE 380;


SELECT t1.area_name
     , t1.district_name
     , t1.bch_name
     , t1.bch_code
     , tdo_long + tdo_short                             tdo
     , tdo_long_done + tdo_short_done                   tdo_done
     , tdo_long_timeout + tdo_short_timeout             tdo_timeout
     , tdo_long_policy + tdo_short_policy               tdo_policy
     , tdo_long_amt + tdo_short_amt                     tdo_amt
     , tdo_long_follow + tdo_short_follow               tdo_follow
     , tdo_long_follow_policy + tdo_short_follow_policy tdo_follow_policy
     , tdo_long_follow_amt + tdo_long_follow_amt        tdo_follow_amt

     , tdo_long
     , tdo_long_renew_policy
     , tdo_long_renew_amt
     , tdo_long_follow
     , tdo_long_follow_policy
     , tdo_long_follow_amt
     , pt
     , job_number2
     , tdo_short
     , tdo_short_policy
     , tdo_short_amt
     , tdo_short_follow
     , tdo_short_follow_policy
     , tdo_short_follow_amt
     , pt2
     , area_name
     , bch_name
     , bch_code
     , userid
from t_renew_long t1
         left join t_renew_short t2
                   on t1.bch_code = t2.bch_code
         left join t_user t3 on t1.bch_code = t3.userid
;